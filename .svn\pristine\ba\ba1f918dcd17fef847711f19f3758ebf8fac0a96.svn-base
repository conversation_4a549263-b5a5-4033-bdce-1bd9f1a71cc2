.inputTextareafamily {
    max-width: 742px;
    border-radius: 10px;
    font-weight: 300;
    font-size: 12px;
    
}

.inputTextareafamily:enabled:focus {
    border: 2px solid #179d52 !important;
    box-shadow: none;
}

.inputTextareafamily:enabled:hover {
    border: 2px solid #179d52 !important;
    box-shadow: none;
}

.inputTextareafamily::placeholder {
    font-weight: 300;
    font-size: 12px;
    line-height: 14px;
    color: #909090;
}

.selectedButton {
    background-color: #007bff;
    /* Change the color as per your design */
    color: white;
    /* Change text color when selected */
}

.customButton {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    height: 46px;
    width: 100px;
    max-width: 100%;
    min-height: 46px;
    background-color: #FFA500;
    margin-top: 28px;
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 700;
    text-align: center;
    padding: 13px 10px;
    border: none;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    /* transition: background-color 0.3s ease; */
}