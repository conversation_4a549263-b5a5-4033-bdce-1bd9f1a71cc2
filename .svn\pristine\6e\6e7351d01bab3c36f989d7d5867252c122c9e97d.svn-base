import React, { useState } from "react";
import TodoCheck from "../../../../assets/images/Icons/list-check.png";
import TodoCheckMobile from "../../../../assets/images/Icons/note-todo-mobile.png";
import TodoDeclined from "../../../../assets/images/Icons/list-declined.png";
import c from "../../../../helper/juggleStreetConstants";
import { Jobs } from "../types";
import useIsMobile from "../../../../hooks/useIsMobile";
import { FaCheck, FaRegEye } from "react-icons/fa6";
import { SlLocationPin } from "react-icons/sl";
import { IframeBridge } from "../../../../services/IframeBridge";
import { RootState } from "../../../../store";
import { useSelector } from "react-redux";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { useNavigate } from "react-router-dom";

import { styleText } from "util";

function calculatePercentage(part: number, total: number): number {
  if (total === 0) {
    return 0;
  }
  return (part / total) * 100;
}

const ProgressBar: React.FC<{
  unProgressColor: string;
  progressColor: string;
  percentage: number;
}> = ({ percentage, progressColor, unProgressColor }) => {
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div className="w-full flex gap-2 align-items-center">
      <div
        className="flex-grow-1 relative overflow-hidden"
        style={{
          minHeight: "7.19px",
          maxHeight: "7.19px",
          backgroundColor: unProgressColor,
          borderRadius: "20px",
        }}
      >
        <div
          className="h-full absolute top-0 left-0"
          style={{
            backgroundColor: progressColor,
            width: `${percentage}%`,
            borderRadius: "20px",
          }}
        />
      </div>
      <p
        className="m-0 p-0"
        style={{
          fontWeight: "500",
          fontSize: "14px",
          color: progressColor,
        }}
      >
        {percentage.toFixed(0)}%
      </p>
    </div>
  ) : (
    <div className="w-full flex gap-2 align-items-center">
      <div
        className="flex-grow-1 relative overflow-hidden"
        style={{
          minHeight: "7.19px",
          maxHeight: "7.19px",
          backgroundColor: unProgressColor,
          borderRadius: "20px",
        }}
      >
        <div
          className="h-full absolute top-0 left-0"
          style={{
            backgroundColor: progressColor,
            width: `${percentage}%`,
            borderRadius: "20px",
          }}
        />
      </div>
      <div
        style={{
          backgroundColor: "#ffffff",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          borderRadius: "50%",
          padding: "3px",
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "12px",
            color: progressColor,
          }}
        >
          {percentage.toFixed(0)}%
        </p>
      </div>
    </div>
  );
};

type Props = {
  job: Jobs;
};

const JobAnalytics = ({ job }: Props) => {
  const { isMobile } = useIsMobile();
  if (!job) {
    return null;
  }

  if (job.managedBy === 20) {
    return null;
  }

  if (job.jobStatus === c.jobStatus.AWARDED) {
    return null;
  }
  const totalInvited = job?.applicantsTotal || 0;
  const totalApplied = job?.applicantsApplied || 0;

  const totalViewed = job?.applicantsViewed || 0;
  const totalDeclined = (job.applicantsNotAvailable + job.applicantsNotInterested) || 0;
  const totalApplicants = job.applicants?.length;
  let totalCalendarClash = 0;
  let totalNotEnoughNotice = 0;
  let totalPriceTooLow = 0;
  let totalOther = 0;
  let totalTooFarAway = 0;
  let totalShiftsTooShort = 0;
  const navigate = useNavigate();

  const appliedPercentage = calculatePercentage(
    job.applicantsApplied,
    totalApplicants
  );
  const declinedPercentage = calculatePercentage(
    job.applicantsNotAvailable + job.applicantsNotInterested,
    totalApplicants
  );
  const { inIframe } = useSelector(
    (state: RootState) => state.applicationState
  );
  const client = Number(utils.getCookie(CookiesConstant.clientType));


  job.applicants?.forEach((a) => {
    switch (a.unavailableReason) {
      case c.providerUnavailableReason.CALENDAR_CLASH:
        totalCalendarClash += 1;
        break;
      case c.providerUnavailableReason.NOT_ENOUGH_NOTICE:
        totalNotEnoughNotice += 1;
        break;
      case c.providerUnavailableReason.PRICE_TOO_LOW:
        totalPriceTooLow += 1;
        break;
      case c.providerUnavailableReason.OTHER:
        totalOther += 1;
        break;
      case c.providerUnavailableReason.TOO_FAR_AWAY:
        totalTooFarAway += 1;
        break;
      case c.providerUnavailableReason.SHIFTS_TOO_SHORTS:
        totalShiftsTooShort += 1;
        break;
    }
  });

  let totalCalendarClashPercentage = calculatePercentage(
    totalCalendarClash,
    totalApplicants
  );
  let totalNotEnoughNoticePercentage = calculatePercentage(
    totalNotEnoughNotice,
    totalApplicants
  );
  let totalPriceTooLowPercentage = calculatePercentage(
    totalPriceTooLow,
    totalApplicants
  );
  let totalOtherPercentage = calculatePercentage(totalOther, totalApplicants);
  let totalTooFarAwayPercentage = calculatePercentage(
    totalTooFarAway,
    totalApplicants
  );
  let totalShiftsTooShortPercentage = calculatePercentage(
    totalShiftsTooShort,
    totalApplicants
  );
  return !isMobile ? (
    <div
      className="h-min mt-4 flex flex-column justify-content-start"
      style={{
        width: "952px",
        paddingInline: "30px 20px",
        paddingBlock: "20px",
        border: "1px solid #DFDFDF",
        borderRadius: "20px",
      }}
    >
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "22px",
          color: "#585858",
        }}
      >
        Your job analytics
      </h1>

      <div className="grid grid-nogutter mt-2">
        <div className="col-4 flex flex-column gap-2">
          <div className="flex gap-2 align-items-center">
            <img
              src={TodoCheck}
              alt="todo check"
              width="13.35px"
              height="14.4px"
            />
            <h2
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Responses
            </h2>
          </div>
          <div className="flex justify-content-between pr-2">
            <p
              className="m-0 p-0 "
              style={{
                fontWeight: "600",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              Applied : {totalApplied}
            </p>
            <span
              className="m-0 p-0 flex"
              style={{
                fontWeight: "600",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              Declined :&nbsp;<p className="p-0 m-0" style={{ color: '#FF6359' }}>{totalDeclined}</p>
            </span>
            <p
              className="m-0 p-0 "
              style={{
                fontWeight: "600",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              Viewed : {totalViewed}
            </p>
          </div>
          <div
            className="flex-grow-1 flex flex-column pr-3"
            style={{
              borderTop: "1px solid #DFDFDF",
              borderRight: "1px solid #DFDFDF",
            }}
          >
            <p
              className="m-0 p-0 mt-3"
              style={{
                fontWeight: "500",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              Applied
            </p>
            <ProgressBar
              percentage={appliedPercentage}
              progressColor="#179D52"
              unProgressColor="#DFDFDF"
            />
            <p
              className="m-0 p-0 mt-3"
              style={{
                fontWeight: "500",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              Declined
            </p>
            <ProgressBar
              percentage={declinedPercentage}
              progressColor="#FF6359"
              unProgressColor="#DFDFDF"
            />
          </div>
        </div>
        <div className="col-8 flex flex-column gap-2">
          <div className="flex gap-2 align-items-center">
            <img
              src={TodoDeclined}
              alt="todo check"
              width="11.7px"
              height="14.4px"
            />
            <h2
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Reasons for Declines
            </h2>
          </div>
          <div className="flex justify-content-between pr-2" style={{ marginTop: '18px' }}></div>
          <div
            className="flex-grow-1 flex flex-column pl-3"
            style={{
              borderTop: "1px solid #DFDFDF",
            }}
          >
            <div className="flex-grow-1 grid grid-nogutter">
              <div className="col-6 pr-2 flex flex-column">
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Calendar clash
                </p>
                <ProgressBar
                  percentage={totalCalendarClashPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />{" "}
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Not enough notice
                </p>
                <ProgressBar
                  percentage={totalNotEnoughNoticePercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Price too low
                </p>
                <ProgressBar
                  percentage={totalPriceTooLowPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
              </div>
              <div className="col-6 pl-2 flex flex-column">
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Other
                </p>
                <ProgressBar
                  percentage={totalOtherPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />{" "}
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Too far away
                </p>
                <ProgressBar
                  percentage={totalTooFarAwayPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  Shifts too short
                </p>
                <ProgressBar
                  percentage={totalShiftsTooShortPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div
      className="h-min mt-3 flex flex-column justify-content-start"
      style={{
        width: "100%",

        borderRadius: "20px",
      }}
    >
      <div className="grid grid-nogutter mt-2 flex flex-column">
        <div className=" flex flex-column gap-2">
          <div className="flex gap-2 align-items-center">
            <img
              src={TodoCheckMobile}
              alt="todo check"
              width="13.35px"
              height="14.4px"
            />
            <h2
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Responses
            </h2>
          </div>
          {job.applicants.some(
            (a) =>
              a.applicationStatus !== c.jobApplicationStatus.APPLIED &&
              a.applicationStatus !== c.jobApplicationStatus.NOT_AVAILABLE
          ) && (
              <div className="flex-grow-1 flex justify-content-between pr-2 mb-2">
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "600",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Applied : {totalApplied}
                </p>
                <span
                  className="m-0 p-0 flex"
                  style={{
                    fontWeight: "600",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Declined :&nbsp;
                  <p className="p-0 m-0" style={{ color: "#FF6359" }}>
                    {totalDeclined}
                  </p>
                </span>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "600",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Viewed : {totalViewed}
                </p>
              </div>
            )}
        </div>
        <div
          className="flex-grow-1 flex flex-column pr-3"
          style={{
            borderTop: "1px solid #DFDFDF",
            borderRight: "1px solid #DFDFDF",
          }}
        >
          <p
            className="m-0 p-0 mt-3"
            style={{
              fontWeight: "500",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            Applied
          </p>
          <ProgressBar
            percentage={appliedPercentage}
            progressColor="#179D52"
            unProgressColor="#DFDFDF"
          />
          <p
            className="m-0 p-0 mt-3"
            style={{
              fontWeight: "500",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            Declined
          </p>
          <ProgressBar
            percentage={declinedPercentage}
            progressColor="#585858"
            unProgressColor="#DFDFDF"
          />
        </div>
        <div className="flex flex-column gap-2 mt-3">
          <div className="flex gap-2 align-items-center">
            <img
              src={TodoCheckMobile}
              alt="todo check"
              width="11.7px"
              height="14.4px"
            />
            <h2
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Reasons for Declines
            </h2>
          </div>
          <div
            className="flex-grow-1 flex flex-column "
            style={{
              borderTop: "1px solid #DFDFDF",
            }}
          >
            <div className="flex-grow-1 grid grid-nogutter flex-column mt-2">
              <div className=" flex flex-column">

                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Calendar clash
                </p>
                <ProgressBar
                  percentage={totalCalendarClashPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />{" "}


                <p
                  className="m-0 p-0 "
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Not enough notice
                </p>
                <ProgressBar
                  percentage={totalNotEnoughNoticePercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
                <p
                  className="m-0 p-0 "
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Price too low
                </p>
                <ProgressBar
                  percentage={totalPriceTooLowPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
              </div>
              <div className=" flex flex-column">
                <p
                  className="m-0 p-0 "
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Other
                </p>
                <ProgressBar
                  percentage={totalOtherPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />{" "}
                <p
                  className="m-0 p-0 "
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Too far away
                </p>
                <ProgressBar
                  percentage={totalTooFarAwayPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
                <p
                  className="m-0 p-0 "
                  style={{
                    fontWeight: "700",
                    fontSize: "12px",
                    color: "#585858",
                  }}
                >
                  Shifts too short
                </p>
                <ProgressBar
                  percentage={totalShiftsTooShortPercentage}
                  progressColor="#585858"
                  unProgressColor="#DFDFDF"
                />
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default JobAnalytics;
