import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";

import { PrimeReactProvider } from "primereact/api";
import { Provider } from "react-redux";
import { store } from "./store/index.ts";
import "./index.css";
import "primeflex/primeflex.css";
import "primereact/resources/themes/lara-light-cyan/theme.css";
import { AppInsightsContext } from "@microsoft/applicationinsights-react-js";
import { reactPlugin } from "./services/AppInsights.ts";
import { HashRouter } from "react-router-dom";
import IFrameInitializer from "./containers/Common/IFrameInitializer.tsx";
import AdminInitializer from "./containers/Common/AdminInitializer.tsx";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <AppInsightsContext.Provider value={reactPlugin}>
      <AdminInitializer>
        <Provider store={store}>
          <PrimeReactProvider>
            <IFrameInitializer>
              <HashRouter>
                <App />
              </HashRouter>
            </IFrameInitializer>
          </PrimeReactProvider>
        </Provider>
      </AdminInitializer>
    </AppInsightsContext.Provider>
  </React.StrictMode>
);
