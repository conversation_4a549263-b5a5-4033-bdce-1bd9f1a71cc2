import React, { useState } from 'react';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import '../TimesheetScreen.css';
import { TabView, TabPanel } from 'primereact/tabview';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';
import c from '../../../../helper/juggleStreetConstants';

const Payments: React.FC = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isHelper = clientType === c.clientType.UNSPECIFIED;

  const headers = {
    firstTab: isParent ? "Invoice Approval" : "Pending Approval",
    secondTab: isParent ? "Make Payment" : "Awaiting Payment",
    thirdTab: isParent ? "Payment History" : "Payment Receipts",
  };

  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          {headers.firstTab}
        </div>
      }>
        <div className="simple-tab-panel-content">
          {isParent && <p>Parent: Content for Invoice Approval.</p>}
          {isHelper && <p>Helper: Content for Pending Approval.</p>}
        </div>
      </TabPanel>
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          {headers.secondTab}
        </div>
      }>
        <div className="simple-tab-panel-content">
          {isParent && <p>Parent: Content for Make Payment.</p>}
          {isHelper && <p>Helper: Content for Awaiting Payment.</p>}
        </div>
      </TabPanel>
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          {headers.thirdTab}
        </div>
      }>
        <div className="simple-tab-panel-content">
          {isParent && <p>Parent: Content for Payment History.</p>}
          {isHelper && <p>Helper: Content for Payment Receipts.</p>}
        </div>
      </TabPanel>
    </TabView>
  );
};

export default Payments;