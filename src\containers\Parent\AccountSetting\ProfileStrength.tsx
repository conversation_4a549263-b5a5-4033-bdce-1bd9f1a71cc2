import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { useEffect, useState } from "react";
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { ProgressBar } from "primereact/progressbar";
import { updateAccountAndSettingsActiveTab } from "../../../store/slices/applicationSlice";
import { updateActiveTabIndex } from "../../../store/slices/accountSettingSlice";


interface RankItem {
    rankingCategory: string;
    entriesCount: number;
    pointsPerEntry: number;
    totalPoints: number;
    label: string;
    userManageable: boolean;
    visible: boolean;
}



const ProfileStrength = ({ onBack }) => {
    const dispatch = useDispatch<AppDispatch>();
    const session = useSelector((state: RootState) => state.sessionInfo.data);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo) as { data: { rankingItems: RankItem[] } };
    const [rankingItems, setrankingItems] = useState<RankItem[]>([]);
    const [completenessPercentage, setCompletenessPercentage] = useState(0);
    const [selectedItem, setSelectedItem] = useState<string | null>(null);


    useEffect(() => {
        const rankingItems = session["rankingItems"] || [];
        setrankingItems(rankingItems);
    }, [sessionInfo.data.rankingItems]);

    useEffect(() => {
        if (session) {
            setCompletenessPercentage(session['completenessPercentage'] || 0);
        }
    }, [session]);

    const pendingProfileTotalPointsCalc = (): number =>
        session["rankingItems"].reduce(
            (total, item) => (item.userManageable ? total + item.pointsPerEntry : total),
            0
        );

    const pendingProfilePoints = (): number =>
        session["rankingItems"].reduce(
            (total, item) => (item.entriesCount > 0 && item.userManageable ? total + item.totalPoints : total),
            0
        );

    const completenessPercentageCalc = Math.round(
        (pendingProfilePoints() / pendingProfileTotalPointsCalc()) * 100
    );

    const userManageablevisibleItems = rankingItems.filter(item => item.userManageable === true && item.visible === true);
    const handleItemClick = (item) => {
        if (item.rankingCategory === 'profile.public') {
            dispatch(updateActiveTabIndex(14));
            dispatch(updateAccountAndSettingsActiveTab(14));
        } else if (item.rankingCategory === 'profile.video') {
            dispatch(updateActiveTabIndex(10));
            dispatch(updateAccountAndSettingsActiveTab(10));
        } else if (item.rankingCategory === 'profile.wwcc') {
            dispatch(updateActiveTabIndex(19));
            dispatch(updateAccountAndSettingsActiveTab(19));
        } else if (item.rankingCategory === 'profile.vouches') {
            dispatch(updateActiveTabIndex(17));
            dispatch(updateAccountAndSettingsActiveTab(17));
        } else if (item.rankingCategory === 'profile.online-payments') {
            dispatch(updateActiveTabIndex(25));
            dispatch(updateAccountAndSettingsActiveTab(25));
        }
    };
    const getDescriptionText = (rankingCategory: string) => {
        switch (rankingCategory) {
            case 'profile.photo':
                return 'Upload a recent portrait photo';
            case 'profile.active':
                return 'Job preferences & validated checks need to be completed before families can see your profile.';
            case 'profile.video':
                return 'Upload a short video, this is the best way to introduce yourself to new families. Helpers with a video receive more job invitations.';
            case 'response.1hour':
                return 'Receive maximum points by responding within 1 Hour to all parent job invitations and messages.';
            case 'response.1day':
                return 'Respond quicker to parents to receive more job invitations. ALWAYS respond to job invitations and in-app messages.';
            case 'response.slow':
                return 'Response Times slower than 1 Day are below parent expectations. Respond quicker to all parent job invitations and in-app messages.';
            case 'profile.wwcc':
                return 'Commonly requested by parents.';
            case 'profile.vouches':
                return 'Add referees who can provide a personal reference about you. Very important for all new Juggle St helpers.';
            case 'profile.online-payments':
                return 'Give families the flexibility to pay you in cash or via bank transfer. Simply add your bank details. Juggle St does NOT take a fee or commission.';
            case 'profile.public':
                return 'Allow parents to view your profile on Juggle Street’s public website. No personal details are shared on public website.';
            case 'profile.references':
                return 'Add referees who can provide a personal reference about you. Very important for all new Juggle St helpers.';
            default:
                return '';
        }
    };
    const entryPercentage = (rank: RankItem): string => {
        if (rank.entriesCount > 0) return '';
        const totalPoints = pendingProfileTotalPointsCalc();
        if (totalPoints === 0) return '0%';

        const perc = Math.round((rank.pointsPerEntry / totalPoints) * 100);
        return `${perc > 0 ? '+' : ''}${perc}%`;
    };

    return (
        <div className={styles.utilcontainerhelper} style={{ color: '#585858' }}>

            <header className={styles.utilheader}>
                <h1 className="p-0 m-0">Profile Strength</h1>
            </header>

            <p className={styles.profiletext}>Profile Strength</p>
            <div className="profile-strength-container">
                <ProgressBar
                    value={session && session["loading"] ? 100 : completenessPercentageCalc}
                    className={styles.profilestrengthprogressbar}
                />
            </div>
            <div>
                {session && (
                    <div>
                        <div
                            className="completed"
                            style={{ width: `${completenessPercentageCalc}%` }}
                        ></div>
                        <div style={{ fontWeight: 100 }}>Your Profile Strength is {completenessPercentageCalc}%</div>
                        <div className="mt-2">
                            <label className={styles.profiletext}>Improve Your Profile Strength</label>
                        </div>
                        <div className="mt-2">
                            {userManageablevisibleItems.map((item, index) => {
                                const itemPercentage = (item.pointsPerEntry / item.totalPoints) * 100;
                                const isChecked = itemPercentage === 100;

                                return (
                                    <div key={index} className="radio-item mt-2 cursor-pointer">
                                        <div className="flex">
                                            <input
                                                type="checkbox"
                                                id={`radio-${index}`}
                                                name="profile-strength"
                                                value={item.label}
                                                className={`${styles.customCheckbox}`}
                                                checked={selectedItem === item.label || isChecked}
                                                onClick={() => handleItemClick(item)}
                                                style={{ fontSize: '18px', borderRadius: '1rem' }}
                                            />
                                            <label className="font-semibold cursor-pointer" htmlFor={`radio-${index}`}>{item.label}</label>
                                            <span style={{ color: '#179D52', marginLeft: '0.5rem' }}>{entryPercentage(item)}</span>
                                        </div>
                                        <div className="description-text ml-4" style={{ fontWeight: 100 }}>
                                            {isChecked ? "Completed!" : getDescriptionText(item.rankingCategory)}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
export default ProfileStrength;
