import React, { useState, useEffect } from 'react';
import AwaitingConfirmationCard from './AwaitingConfirmationCard';
import { TimesheetDetails } from '../../../../hooks/useTimesheetDetails';
import Service from '../../../../services/services';
import useLoader from '../../../../hooks/LoaderHook';
import c from '../../../../helper/juggleStreetConstants';
import CookiesConstant from '../../../../helper/cookiesConst';
import utils from '../../../../components/utils/util';

interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

interface TimesheetRow {
  start: string;
  finish: string;
  hours: number;
  rate: number;
  total: number;
  isOriginal?: boolean;
  editVersion?: number;
  id?: number;
}

interface TimesheetDetailsPopupProps {
  selectedEntry: TimesheetEntry | null;
  timesheetDetails: TimesheetDetails | null;
  timesheetRows?: TimesheetRow[]; // Array of timesheet entries
  onClose: () => void;
  onApprovalSuccess?: () => void;
}

// Utility functions for time conversion and formatting
const convertTo24Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [time, modifier] = timeStr.split(" ");
  if (!time || !modifier) return timeStr; // fallback
  let [hours, minutes] = time.split(":").map(Number);

  if (modifier.toUpperCase() === "PM" && hours < 12) {
    hours += 12;
  }
  if (modifier.toUpperCase() === "AM" && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

const convertTo12Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [hours, minutes] = timeStr.split(":").map(Number);

  if (isNaN(hours) || isNaN(minutes)) return timeStr;

  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours % 12 || 12;

  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};

const formatTimeForDisplay = (timeStr: string): string => {
  if (!timeStr) return '';

  // If already in AM/PM format, return as is
  if (timeStr.includes('AM') || timeStr.includes('PM') || timeStr.includes('am') || timeStr.includes('pm')) {
    return timeStr;
  }

  // If in 24-hour format, convert to 12-hour
  return convertTo12Hour(timeStr);
};

const calculateHours = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const start = new Date(`1970-01-01T${startTime}`);
  const end = new Date(`1970-01-01T${endTime}`);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.warn("Invalid time format", { startTime, endTime });
    return 0;
  }

  const diffMs = end.getTime() - start.getTime();
  const hours = diffMs / (1000 * 60 * 60);
  return parseFloat(hours.toFixed(2));
};

const calculateTotalPrice = (hours: number, rate: number): number => {
  if (isNaN(hours) || isNaN(rate)) return 0;
  return parseFloat((hours * rate).toFixed(2));
};

const TimesheetDetailsPopup: React.FC<TimesheetDetailsPopupProps> = ({
  selectedEntry,
  timesheetDetails,
  timesheetRows = [],
  onClose,
  onApprovalSuccess
}) => {
  const { enableLoader, disableLoader } = useLoader();

  if (!selectedEntry || !timesheetDetails) return null;

  // Helper function to get initial formatted times
  const getInitialFormattedTime = (timeStr: string) => {
    return formatTimeForDisplay(timeStr || '');
  };

  // Function to process timesheet rows and convert times to AM/PM format
  const processTimesheetRows = (rows: TimesheetRow[]): TimesheetRow[] => {
    return rows.map((row, index) => ({
      ...row,
      start: getInitialFormattedTime(row.start),
      finish: getInitialFormattedTime(row.finish),
      isOriginal: false,
      editVersion: 0,
      id: row.id || index
    }));
  };

  // Create initial timesheet rows - either from props or from timesheetDetails
  const getInitialTimesheetRows = (): TimesheetRow[] => {
    if (timesheetRows && timesheetRows.length > 0) {
      // Use provided timesheet rows array
      return processTimesheetRows(timesheetRows);
    } else {
      // Fallback to single row from timesheetDetails (backward compatibility)
      const startTime = getInitialFormattedTime(timesheetDetails.jobStartTime || '');
      const endTime = getInitialFormattedTime(timesheetDetails.jobEndTime || '');
      const rate = Number(timesheetDetails.price) || 0;
      const startTime24 = convertTo24Hour(startTime);
      const endTime24 = convertTo24Hour(endTime);
      const hours = calculateHours(startTime24, endTime24);
      const total = calculateTotalPrice(hours, rate);

      return [{
        start: startTime,
        finish: endTime,
        hours: hours,
        rate: rate,
        total: total,
        isOriginal: false,
        editVersion: 0,
        id: 1
      }];
    }
  };

  // State for managing multiple timesheet rows
  const [currentTimesheetRows, setCurrentTimesheetRows] = useState<TimesheetRow[]>(() =>
    getInitialTimesheetRows()
  );

  // Update state when timesheetDetails or timesheetRows change
  useEffect(() => {
    if (timesheetDetails || (timesheetRows && timesheetRows.length > 0)) {
      const newRows = getInitialTimesheetRows();
      setCurrentTimesheetRows(newRows);

      console.log('TimesheetDetailsPopup - Updated timesheet rows:', {
        timesheetRowsCount: timesheetRows?.length || 0,
        processedRowsCount: newRows.length,
        rows: newRows
      });
    }
  }, [timesheetDetails, timesheetRows]);

  // Handle time changes from AwaitingConfirmationCard
  const handleTimesheetRowsChange = (updatedRows: TimesheetRow[]) => {
    if (updatedRows && updatedRows.length > 0) {
      console.log('TimesheetDetailsPopup - Timesheet rows changed:', updatedRows);
      setCurrentTimesheetRows(updatedRows);
    }
  };

  // Calculate total amount from all rows
  const calculateTotalAmount = (rows: TimesheetRow[]): number => {
    return rows
      .filter(row => !row.isOriginal)
      .reduce((sum, row) => sum + (row.total || 0), 0);
  };

  const totalAmount = calculateTotalAmount(currentTimesheetRows);

  console.log('TimesheetDetailsPopup - Current state:', {
    timesheetRowsCount: timesheetRows?.length || 0,
    currentRowsCount: currentTimesheetRows.length,
    totalAmount: totalAmount,
    rows: currentTimesheetRows
  });

  // const handleApprove = async () => {
  //   if (!timesheetDetails) return;

  //   enableLoader();

  //   // Calculate totals from all current timesheet rows
  //   const totalHours = currentTimesheetRows
  //     .filter(row => !row.isOriginal)
  //     .reduce((sum, row) => sum + (row.hours || 0), 0);

  //   const baseRate = Number(timesheetDetails.price) || 0;
  //   const jobTypeLabelToIdMap: Record<string, number> = {
  //     "Unspecified": c.jobType.UNSPECIFIED,
  //     "One Of Job": c.jobType.BABYSITTING,
  //     "Recurring Job": c.jobType.NANNYING,
  //     "Before School Care": c.jobType.BEFORE_SCHOOL_CARE,
  //     "After School Care": c.jobType.AFTER_SCHOOL_CARE,
  //     "Before & After School Care": c.jobType.BEFORE_AFTER_SCHOOL_CARE,
  //     "Au Pair": c.jobType.AU_PAIR,
  //     "Home Tutoring": c.jobType.HOME_TUTORING,
  //     "Primary School Tutoring": c.jobType.PRIMARY_SCHOOL_TUTORING,
  //     "High School Tutoring": c.jobType.HIGH_SCHOOL_TUTORING,
  //     "Odd Job": c.jobType.ONE_OFF_ODD_JOB,
  //   };

  //   const ODD_AND_ONE_OFF_JOBS = new Set<number>([
  //     c.jobType.ONE_OFF_ODD_JOB,
  //     c.jobType.BABYSITTING,
  //     ...Object.values(c.oddJobType),
  //   ]);

  //   const JOB_TYPES_WITH_ROWS = new Set<number>([
  //     c.jobType.NANNYING,
  //     c.jobType.BEFORE_SCHOOL_CARE,
  //     c.jobType.AFTER_SCHOOL_CARE,
  //     c.jobType.BEFORE_AFTER_SCHOOL_CARE,
  //     c.jobType.HOME_TUTORING,
  //     c.jobType.PRIMARY_SCHOOL_TUTORING,
  //     c.jobType.HIGH_SCHOOL_TUTORING,
  //   ]);

  //   const rawJobType = timesheetDetails.jobType;
  //   const currentJobType: number =
  //     typeof rawJobType === "number" ? rawJobType : jobTypeLabelToIdMap[rawJobType] ?? 0;

  //   console.log("Raw jobType:", rawJobType, typeof rawJobType);
  //   console.log("Final jobType as number:", currentJobType);

  //   const basePayload: any = {
  //     timesheetId: timesheetDetails.timesheetId,
  //     JobId: timesheetDetails.jobId,
  //     ApplicantId: timesheetDetails.applicantId,
  //     NewEstimatedJobHours: totalHours,
  //     NewEstimatedJobValue: totalAmount,
  //     NewPrice: baseRate,
  //     userId: timesheetDetails.userId,
  //   };

  //   if (ODD_AND_ONE_OFF_JOBS.has(currentJobType)) {
  //     basePayload.NewJobStartTime = timesheetDetails.jobStartTime;
  //     basePayload.NewJobEndTime = timesheetDetails.jobEndTime;
  //   }

  //   if (JOB_TYPES_WITH_ROWS.has(currentJobType)) {
  //     basePayload.timesheetRows = currentTimesheetRows.filter(row => !row.isOriginal);
  //   }

  //   const payload = { ...basePayload };

  //   console.log("Final Approval Payload:", payload);

  //   try {
  //     await new Promise<void>((resolve, reject) => {
  //       Service.postUpdateHistory(
  //         (response: any) => {
  //           console.log("Timesheet approved:", response);
  //           resolve();
  //         },
  //         (error: any) => {
  //           console.error("Error approving timesheet:", error);
  //           reject(error);
  //         },
  //         payload
  //       );
  //     });
  //     onApprovalSuccess?.();
  //     onClose();
  //   } catch (error) {
  //     console.error('Approval failed:', error);
  //   } finally {
  //     disableLoader();
  //   }
  // };
const handleApprove = async () => {
    // Basic check to ensure we have the necessary data
    if (!timesheetDetails || !timesheetDetails.timesheetId) {
        console.error("Missing timesheet details or timesheet ID");
        return;
    }

    enableLoader();

    // --- START: MODIFIED API CALL LOGIC ---

    // 1. Determine the client type to select the correct API endpoint
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const showApprove = clientType === c.clientType.INDIVIDUAL || clientType === c.clientType.BUSINESS;

    // 2. Select the correct service function based on the client type
    const apiCallFunction = showApprove
      ? Service.postApproveTimeSheet
      : Service.postConfirmTimeSheet;

    // 3. Create the simple payload with ONLY the timeSheetId as requested
    const finalPayload = {
      timeSheetId: timesheetDetails.timesheetId
    };

    console.log(`Preparing to call: ${apiCallFunction.name}`);
    console.log("Final Submission Payload:", finalPayload);

    const successLogMessage = showApprove
      ? "Timesheet approved:"
      : "Timesheet confirmed:";

    try {
      await new Promise<void>((resolve, reject) => {
        // 4. Call the selected function with the simple payload
        apiCallFunction(
          (response: any) => {
            console.log(successLogMessage, response);
            resolve();
          },
          (error: any) => {
            console.error("Error submitting timesheet:", error);
            reject(error);
          },
          finalPayload
        );
      });
      
      onApprovalSuccess?.();
      onClose();
    } catch (error) {
      console.error('Submission failed:', error);
    } finally {
      disableLoader();
    }
};
  return (
    <div className="overlay-popup">
      <div className="slide-up-card">
        <AwaitingConfirmationCard
          profileName={`${timesheetDetails.firstName} ${timesheetDetails.lastName}`}
          profileImage={timesheetDetails.originalImageUrl}
          jobType={timesheetDetails.jobType}
          jobDate={timesheetDetails.jobDate}
          jobAddress={timesheetDetails.formattedAddress}
          baseRate={Number(timesheetDetails.price) || 0}
          extraHoursRate={timesheetDetails.overtimeRate}
          initialTimesheetRows={currentTimesheetRows} // Pass all timesheet rows
          onSubmit={handleApprove}
          onGoBack={onClose}
          onTimesheetRowsChange={handleTimesheetRowsChange}
        />
      </div>
    </div>
  );
};

export default TimesheetDetailsPopup;
