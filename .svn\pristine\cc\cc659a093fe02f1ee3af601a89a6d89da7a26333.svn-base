.profile-card {
  display: flex;
  width: 540px;
  height: 190px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0.1, 0.1, 0.1, 0.1);
  padding: 4px;
}

.profile-image {
  width: 174px;
  height: 190px;
  overflow: hidden;
}

.profile-image img {
  width: 174px;
  height: 181px;
  object-fit: cover;
  border-radius: 20px;
}

.profile-info {
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  padding-left: 18px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.headerH2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #585858;
}

.rating {
  font-size: 12px;
  color: #1e1e1e;
  font-weight: 300;
  height: 18px;
}

.star {
  color: #ffa500;
  height: 20px;
  width: 20px;
}

.favorite-button {
  background: none;
  border: none;
  font-size: 21px;
  color: #33363f;
  cursor: pointer;
}

.profile-details {
  font-size: 14px;
  color: #585858;
  margin-bottom: 10px;
  font-weight: 300;
}

.Jobs-complete {
  font-size: 14px;
  color: #585858;
  font-weight: 300;
}

.profile-details > div {
  margin-bottom: 5px;
}

.super-helper {
  /* background-color: #fff9e6; */
  text-decoration: underline;
  color: #585858;

  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  /* font-weight: bold; */
  margin-bottom: 10px;
}

/* @media (max-width: 600px) {
    .profile-card {
        width: 100%;
        height: auto;
        flex-direction: column;
    }

    .profile-image {
        width: 100%;
        height: 200px;
    }

    .profile-info {
        padding: 15px;
    }
} */

.cmn-border {
  border-radius: 20px;
  padding: 6px;
  box-shadow: 0 0 3.5px 0 rgba(0, 0, 0, 0.25);
  padding-left: 20px;
  padding-right: 20px;
}

.helper-connect-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.super-helper {
  margin-right: 10px;
}

.profile-card-container {
  display: flex;
  flex-direction: column;
  /* min-height: 404px; */
  /* max-height: 800px; */
  overflow-y: auto;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  right: 0;
  height:50%;
  padding-left: 60px;
  transition: all 0.5s ease;
}

.container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-right: 38px;
  /* overflow-x:hidden;
    overflow-y:auto; */
  /* transition : all 0.8s ease; */
}

.profile-card-container::-webkit-scrollbar {
  width: 10px;
}

.profile-card-container::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}

.profile-card-container::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.buttons-container {
  display: flex;
  justify-content: space-between;
  /* gap: 11px; */
  /* margin-bottom: 10px; */
  /* Space between buttons and the profile cards */
}

.jobs {
  width: max-content;
  height: 47px;
  border-radius: 10px;
  gap: 16px;
  font-weight: 500;
  color: #585858;
  font-size: 16px;
  padding-inline: 15px;
}


.job-option {
  padding: 3px 16px;
  border-radius: 10px;
  /* transition:0.3s ease; */
}

.job-option p {
  margin: 0;
  display: flex;
  align-items: center;
}

.job-option:hover {
  border: 1px solid rgba(255, 165, 0, 1);
  box-shadow: 0 0 3.5px 0 rgba(0, 0, 0, 0.25);
}

.job-option.active {
  background-color: orange;
  color: white !important;
  border-radius: 10px;
  font-weight: 700;
}

.home-filter-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 25px;
  border: 1px solid rgba(88, 88, 88, 0.3);
  border-radius: 20px;
  height: 35px;
  background-color: transparent;
  font-size: 14px;
  color: #585868;
  text-wrap: nowrap;
}
.home-filter-btn:hover {
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.25);
}
.home-all-filter-btn {
  display: flex;
  align-items: center;
}
.home-all-filter-btn:hover {
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.25);
}
.home-close-icon {
  position: absolute;
  top: 5px;
  right: 15px;
  background: white;
  border: none;
  border-radius: 50%;
  font-size: 1.9rem;
  color: #585858;
  cursor: pointer;
  /* height: 16px;
    width: 16px; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.home-apply-btn {
  height: 28px;
  width: 78px;
  font-size: 12px;
  font-weight: 700;
  background-color: #ffa500;
  color: #ffffff;
  border: none;
  border-radius: 20px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

/* Style for unchecked checkbox */
.custom-checkbox {
  width: 20px;
  height: 20px;
  appearance: none;
  background-color: white;
  border: 2px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}

/* Style for checked checkbox */
.custom-checkbox:checked {
  background-color: green;
  border-color: green;
}

/* Adding the tick mark when the checkbox is checked */
.custom-checkbox:checked::after {
  content: "✔";
  color: white;
  font-size: 13px;
  position: absolute;
  top: -1px;
  left: 3px;
}

/* Responsive layout adjustments */
@media (max-width: 1200px) {
  .profile-card {
    width: 100%;
    flex-direction: row;
  }

  .home-filter-btn {
    padding: 0px 20px;
  }
}

@media (max-width: 768px) {
  /* Stack profile card and filter options vertically */
  .profile-card {
    flex-direction: column;
    width: 100%;
    height: auto;
  }

  .profile-image {
    width: 100%;
    height: 180px;
  }

  .profile-info {
    padding: 10px;
  }

  /* Adjust button size and spacing for smaller screens */
  .home-filter-btn {
    padding: 0px 15px;
    font-size: 12px;
    height: 30px;
  }

  .home-apply-btn {
    width: 70px;
    height: 24px;
    font-size: 11px;
  }

  .home-close-icon {
    font-size: 1.6rem;
    top: 8px;
    right: 10px;
  }

  /* Container adjustments */
  .container {
    gap: 15px;
    margin-right: 20px;
  }

  .helper-connect-container {
    flex-direction: column;
    gap: 5px;
  }
}

@media (max-width: 600px) {
  /* Further reduce button and card sizes on extra small screens */
  .profile-card {
    width: 100%;
    flex-direction: column;
  }

  .profile-info {
    padding: 8px;
  }

  .profile-card-container {
    padding-left: 20px;
  }

  /* Smaller text and icons */
  h2 {
    font-size: 20px;
  }

  .rating,
  .profile-details,
  .Jobs-complete {
    font-size: 12px;
  }

  .super-helper {
    font-size: 12px;
    padding: 4px 8px;
  }

  /* Overlay buttons and filter adjustments */
  .home-filter-btn {
    padding: 0px 8px;
    font-size: 12px;
  }

  .home-apply-btn {
    font-size: 10px;
    width: 60px;
    height: 22px;
  }

  .home-close-icon {
    font-size: 1.5rem;
    right: 10px;
  }
}
