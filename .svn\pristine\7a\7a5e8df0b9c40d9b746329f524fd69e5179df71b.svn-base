import React, { useEffect, useMemo, useState } from 'react';
import styles from '../../styles/award-shifts-to.module.css';
import { Divider } from 'primereact/divider';
import clock from '../../../../assets/images/Icons/clockstart.png';
import clocked from '../../../../assets/images/Icons/clockend.png';

import ConfirmAwardDialog from './ConfirmAwardDialog';
import c from '../../../../helper/juggleStreetConstants';
import { Jobs, WeeklySchedule } from '../types';
import useLoader from '../../../../hooks/LoaderHook';
import Service from '../../../../services/services';
import { useNavigate } from 'react-router-dom';
import useIsMobile from '../../../../hooks/useIsMobile';
import { Dialog } from 'primereact/dialog';

interface Shift {
    dayOfWeek: number;
    day: string;
    startTime: string;
    endTime: string;
    shiftId: number;
    price: number;
    awardedAplicantId: number | null;
    applicants: Array<{
        applicantId: number;
        availabilityId: number;
        publicName: string;
        imageSrc: string;
        status: number;
    }>;
}

interface DialogProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    shifts: Shift[];
    timeSlots;
    selectedAplicant: number;
    onShiftSelect: (shiftId: string) => void;
    onSelectAll: () => void;
    job: Jobs;
}

const AwardShiftsTo: React.FC<DialogProps> = ({
    isOpen,
    onClose,
    title,
    timeSlots,
    shifts,
    job,
    selectedAplicant,
}) => {
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
    const [selectShifts, setSelectedShift] = useState<number[]>([]);
    const { disableLoader, enableLoader } = useLoader();
    const { isMobile } = useIsMobile();
    const navigate = useNavigate();
    const getAplicantImage = (id: number | null) => {
        if (!id) {
            return null;
        }
        for (const shift of shifts) {
            for (const app of shift.applicants) {
                if (app.applicantId === id) {
                    return app.imageSrc;
                }
            }
        }
        return null;
    };

    const awardebleShift = useMemo(() => {
        const results: Array<{
            day: string;
            dayOfWeek: number;
            startTime: string;
            endTime: string;
            imageUrl: string;
            awardedAplicantImage: string | null;
            shiftId: number;
            publicName: string;
        }> = [];
        if (!selectedAplicant) {
            return results;
        }
        for (const shift of shifts) {
            const aplicant = shift.applicants.find((a) => a.applicantId === selectedAplicant);
            if (!aplicant) {
                return results;
            }
            if (aplicant.status === c.applicantAvailabilityStatus.AVAILABLE) {
                results.push({
                    day: shift.day,
                    dayOfWeek: shift.dayOfWeek,
                    endTime: shift.endTime,
                    startTime: shift.startTime,
                    imageUrl: aplicant.imageSrc,
                    shiftId: shift.shiftId,
                    publicName: aplicant.publicName,
                    awardedAplicantImage: getAplicantImage(shift.awardedAplicantId),
                });
            }
        }
        return results;
    }, [timeSlots]);
    if (!isOpen) return null;

    const handleSelectAll = () => {
        if (awardebleShift.length === selectShifts.length) {
            setSelectedShift([]);
            return;
        }
        setSelectedShift(awardebleShift.map((s) => s.shiftId));
    };

    const handleShiftSelect = (shiftId: number) => {
        if (selectShifts.includes(shiftId)) {
            setSelectedShift((prev) => prev.filter((s) => s !== shiftId));
        } else {
            setSelectedShift((prev) => {
                return [...prev, shiftId];
            });
        }
    };

    useEffect(() => {}, [selectShifts]);

    const handleAwardShifts = () => {
        setIsConfirmDialogOpen(true);
    };
    const entries = job.weeklySchedule?.weeklyScheduleEntries || [];

    const filledShifts = entries.filter((entry) => entry.applicantId !== null).length;
    const openShifts = entries.filter((entry) => entry.applicantId === null).length;

    // const handleConfirm = () => {
    //   const getIds = () => {
    //     return (job.weeklySchedule as WeeklySchedule).weeklyScheduleEntries
    //       .filter((e) => e.applicantId !== null)
    //       .map((e) => e.applicantId);
    //   };

    //   const payload = {
    //     ...job,
    //     // awardedApplicantIds: [applicantId],
    //     awardedApplicantIds: [...getIds(), selectedAplicant],
    //     weeklySchedule: {
    //       ...job.weeklySchedule,
    //       weeklyScheduleEntries: (
    //         job.weeklySchedule as WeeklySchedule
    //       ).weeklyScheduleEntries.map((e) => ({
    //         ...e,
    //         applicantId: selectShifts.includes(e.id)
    //           ? selectedAplicant
    //           : e.applicantId,
    //       })),
    //     },
    //     awardNewApplicant: true,
    //   };

    //   enableLoader();
    //   Service.jobClientAwardJobs(
    //     async (response: Jobs) => {

    //       // setAwardJob(response);
    //       navigate(
    //         `/awardJobConfirmation/${encodeURIComponent(awardebleShift[0].publicName)}/` +
    //         `${encodeURIComponent(awardebleShift[0].imageUrl)}/` +
    //         `${encodeURIComponent(job.id)}?filledShifts=${filledShifts}&openShifts=${openShifts}`
    //       );
    //       // await refresh();

    //       disableLoader();
    //     },
    //     (error: any) => {
    //       disableLoader();
    //       console.error("Error awarding job:", error);
    //     },

    //     job.id,
    //     payload
    //   );
    //   setIsConfirmDialogOpen(false);
    // };
    const [errorDialogOpen, setErrorDialogOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    const handleConfirm = () => {
        const getIds = () => {
            return (job.weeklySchedule as WeeklySchedule).weeklyScheduleEntries
                .filter((e) => e.applicantId !== null)
                .map((e) => e.applicantId);
        };

        // Update weekly schedule entries with selected shifts
        const updatedWeeklyScheduleEntries = (
            job.weeklySchedule as WeeklySchedule
        ).weeklyScheduleEntries.map((e) => ({
            ...e,
            applicantId: selectShifts.includes(e.id) ? selectedAplicant : e.applicantId,
        }));

        // Calculate updated filled and open shifts
        const updatedFilledShifts = updatedWeeklyScheduleEntries.filter(
            (entry) => entry.applicantId !== null
        ).length;
        const updatedOpenShifts = updatedWeeklyScheduleEntries.filter(
            (entry) => entry.applicantId === null
        ).length;

        // Check if all shifts have applicantId assigned
        const allShiftsAssigned = updatedOpenShifts === 0;

        const daysOfWeek = Array(7)
            .fill(0)
            .map((_, i) => ({
                dayOfWeek: i,
                isRequired: false, // or set true if you have logic for required days
            }));

        // If you want to set isRequired true for days present in weeklyScheduleEntries:
        const requiredDays = new Set(
            job.weeklySchedule?.weeklyScheduleEntries.map((entry) => entry.dayOfWeek)
        );
        const daysOfWeekWithRequired = Array(7)
            .fill(0)
            .map((_, i) => ({
                dayOfWeek: i,
                isRequired: requiredDays.has(i),
            }));

        const jobClientAwardJobspayload = {
            ...job,
            awardedApplicantIds: [...getIds(), selectedAplicant],
            weeklySchedule: {
                ...job.weeklySchedule,
                weeklyScheduleEntries: updatedWeeklyScheduleEntries,
            },
            awardNewApplicant: true,
        };

        const awardRecurringJobPayload = {
            jobStartTime: job.jobStartTime,
            jobEndTime: job.jobEndTime,
            paymentType: job.paymentType,
            helperPaymentMethod: job.helperPaymentMethod,
            jobDeliveryMethod: job.jobDeliveryMethod,
            willPayOvertime: job.willPayOvertime,
            daysOfWeek: daysOfWeekWithRequired,
            weeklySchedule: {
                scheduleName: job.weeklySchedule?.scheduleName,
                isActive: job.weeklySchedule?.isActive,
                weeklyScheduleEntries: updatedWeeklyScheduleEntries,
            },
            jobSettings: job.jobSettings,
            userPaymentType: job.userPaymentType,
            actionType: job.actionType,
            isUpsellEnabled: null,
            jobDate: job.jobDate,
            addressId: job.addressId,
            longitude: job.longitude,
            latitude: job.latitude,
            managedBy: job.managedBy,
            relatedJobId: job.id,
            specialInstructions: job.specialInstructions,
            overtimeRate: job.overtimeRate,
            cardSelected: null,
            applicants: [...getIds(), selectedAplicant].map((id) => ({ applicantId: id })),
            applicantFilters: job.applicantFilters,
            deviceType: job.deviceType,
            jobDateDay: job.jobDateDay,
            jobDateMonth: job.jobDateMonth,
            jobDateYear: job.jobDateYear,
            jobType: job.jobType,
            price: job.price,
            awardNewApplicant: true,
        };

    
        enableLoader();

        // Call appropriate service based on whether all shifts are assigned
        if (allShiftsAssigned) {
            // All shifts have applicantId, use jobClientAwardJobs
            Service.jobClientAwardJobs(
                async (response: Jobs) => {
                    navigate(
                        `/awardJobConfirmation/${encodeURIComponent(
                            awardebleShift[0].publicName
                        )}/${encodeURIComponent(awardebleShift[0].imageUrl)}/${encodeURIComponent(
                            job.id
                        )}?filledShifts=${updatedFilledShifts}&openShifts=${updatedOpenShifts}`
                    );
                    disableLoader();
                },
                (error: any) => {
                    disableLoader();
                    setErrorMessage(error?.message || 'An error occurred while awarding the job.');
                    setErrorDialogOpen(true);
                    console.error('Error awarding job:', error);
                },
                job.id,
                jobClientAwardJobspayload
            );
        } else {
            // Some shifts still have applicantId as null, use AwardRecurringJob
            Service.AwardRecurringJob(
                awardRecurringJobPayload,
                async (response: Jobs) => {
                    navigate(
                        `/awardJobConfirmation/${encodeURIComponent(
                            awardebleShift[0].publicName
                        )}/${encodeURIComponent(awardebleShift[0].imageUrl)}/${encodeURIComponent(
                            job.id
                        )}?filledShifts=${updatedFilledShifts}&openShifts=${updatedOpenShifts}`
                    );
                    disableLoader();
                },
                (error: any) => {
                    disableLoader();
                    setErrorMessage(error?.message || 'An error occurred while awarding the job.');
                    setErrorDialogOpen(true);
                    console.error('Error awarding job:', error);
                }
            );
        }

        setIsConfirmDialogOpen(false);
    };
    const handleCancel = () => {
        setIsConfirmDialogOpen(false);
    };

    function formatTimeTo12Hour(time: string): string {
        const [hours, minutes] = time.split(':').map(Number); // Split the time and convert to numbers
        const period = hours >= 12 ? 'pm' : 'am'; // Determine AM/PM
        const formattedHours = hours % 12 || 12; // Convert to 12-hour format (handle 0 as 12)
        return `${formattedHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    }
    return !isMobile ? (
        <div className={styles.overlay}>
            <Dialog
                header="Error"
                visible={errorDialogOpen}
                style={{ width: '450px' }}
                modal
                draggable={false}
                onHide={() => setErrorDialogOpen(false)}
                footer={
                    <button
                        style={{
                            backgroundColor: '#ffa500',
                            color: 'white',
                            padding: '0.75rem 1.5rem',
                            borderRadius: '10px',
                            border: 'none',
                            fontSize: '16px',
                            fontWeight: '700',
                            cursor: 'pointer',
                        }}
                        onClick={() => setErrorDialogOpen(false)}
                    >
                        OK
                    </button>
                }
            >
                <div style={{ color: '#585858', fontWeight: 500 }}>{errorMessage}</div>
            </Dialog>
            <div className={`${styles.dialog} `} style={{ height: '585px' }}>
                <div className={`${styles.header} p-2`}>
                    <h2 className={styles.title}>{title}</h2>
                    <button className={styles.closeButton} onClick={onClose}>
                        ×
                    </button>
                </div>

                <div className="p-3">
                    <div className={`${styles.sectionHeader} pl-3 pr-3 mb-2`}>
                        <h3 className={`${styles.sectionTitle} p-0 m-0`}>Shift date and time</h3>
                        <h3 className={`${styles.sectionTitle} p-0 m-0`}>Select Shift</h3>
                    </div>
                    <Divider />
                    <div
                        className={`${styles.shiftsList} flex justify-content-between overflow-x-auto `}
                        style={{ height: 'auto', maxHeight: '350px' }}
                    >
                        {awardebleShift.map((shift, index) => (
                            <div key={index} className={styles.shiftItem}>
                                <div className={styles.shiftInfo}>
                                    <span className={styles.dayLabel}>{shift.day}</span>
                                    <div className={styles.timeContainer}>
                                        <div
                                            className="flex justify-content-center align-items-center "
                                            style={{
                                                border: '1px solid #DFDFDF',
                                                borderRadius: '10px',
                                                width: '103px',
                                                height: '41px',
                                            }}
                                        >
                                            <img
                                                src={clock}
                                                alt="file check"
                                                width="13.5px"
                                                height="13.5px"
                                                className="text-gray-500"
                                            />
                                            <span
                                                className={`${styles.timeBox} p-2`}
                                                style={{ fontSize: '14px' }}
                                            >
                                                {formatTimeTo12Hour(shift.startTime)}
                                            </span>
                                        </div>

                                        <span
                                            className="p-2"
                                            style={{ fontSize: '12px', color: '#585858' }}
                                        >
                                            to
                                        </span>
                                        <div
                                            className="flex justify-content-center align-items-center"
                                            style={{
                                                border: '1px solid #DFDFDF',
                                                borderRadius: '10px',
                                                width: '103px',
                                                height: '41px',
                                            }}
                                        >
                                            <img
                                                src={clocked}
                                                alt="file check"
                                                width="14.34px"
                                                height="13.5px"
                                                className="text-gray-500"
                                            />
                                            <span
                                                className={`${styles.timeBox} p-2`}
                                                style={{ fontSize: '14px' }}
                                            >
                                                {formatTimeTo12Hour(shift.endTime)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="" style={{ width: '43px', height: '47pxpx' }}>
                                    <div
                                        className={`${styles.imageContainer} `}
                                        // Removed onClick handler from image container
                                    >
                                        <img
                                            src={shift.awardedAplicantImage ?? shift.imageUrl}
                                            alt={`Shift ${shift.day}`}
                                            className={styles.shiftImage}
                                        />
                                    </div>
                                    {(selectShifts.includes(shift.shiftId) ||
                                        shift.awardedAplicantImage !== null) && (
                                        <span className={styles.Imgcheckmark}>✓</span>
                                    )}
                                </div>

                                <div
                                    style={{
                                        height: '50px',
                                        width: '2px',
                                        backgroundColor: '#DFDFDF',
                                    }}
                                ></div>

                                <button
                                    onClick={() => handleShiftSelect(shift.shiftId)}
                                    className={`${styles.selectButton}   ${
                                        selectShifts.includes(shift.shiftId) ||
                                        shift.awardedAplicantImage !== null
                                            ? styles.selected
                                            : ''
                                    } `}
                                    disabled={shift.awardedAplicantImage !== null}
                                    style={{
                                        cursor:
                                            shift.awardedAplicantImage !== null
                                                ? 'disable'
                                                : 'pointer',
                                    }}
                                >
                                    {(selectShifts.includes(shift.shiftId) ||
                                        shift.awardedAplicantImage !== null) && (
                                        <span className={styles.checkmark}>✓</span>
                                    )}
                                    <span className={`${styles.circle} `}></span> &nbsp;&nbsp;
                                    <span style={{ fontSize: '14px' }}>
                                        {shift.awardedAplicantImage !== null
                                            ? 'Awarded'
                                            : 'Select Shift'}
                                    </span>
                                </button>
                            </div>
                        ))}
                    </div>
                    <Divider />

                    <div className={styles.footer}>
                        {awardebleShift.length === selectShifts.length ? (
                            <span
                                className="pl-3 pr-3 flex  align-items-center"
                                style={{
                                    fontWeight: 'bold',
                                    textDecoration: 'underline',
                                    cursor: 'pointer',
                                    color: '#FFA500',
                                    fontSize: '14px',
                                }}
                                onClick={handleSelectAll}
                            >
                                Unselect All
                            </span>
                        ) : (
                            <button
                                onClick={handleSelectAll}
                                className={`${styles.selectAllButton} pl-3 pr-3`}
                            >
                                Select all shifts
                            </button>
                        )}
                        <button
                            className={`${styles.awardButton} ${
                                selectShifts.length > 0 ? styles.enabledButton : ''
                            } cursor-pointer`}
                            disabled={selectShifts.length === 0}
                            onClick={handleAwardShifts}
                        >
                            Award Shifts
                        </button>
                    </div>
                    <ConfirmAwardDialog
                        isOpen={isConfirmDialogOpen}
                        title="Confirm Award"
                        message="Would you like to confirm the selected shifts to"
                        onOpenChange={(open) => setIsConfirmDialogOpen(open)}
                        onConfirm={handleConfirm} // Handle confirm action
                        onCancel={handleCancel} // Handle cancel action
                        name={awardebleShift[0]?.publicName}
                        image={awardebleShift[0]?.imageUrl}
                        noText="Go back"
                        yesText="Yes"
                    />
                </div>
            </div>
        </div>
    ) : (
        <div className={styles.overlayMobile}>
          <Dialog
                header="Error"
                visible={errorDialogOpen}
                style={{ width: '90%', }}
                modal
                draggable={false}
                onHide={() => setErrorDialogOpen(false)}
                footer={
                    <button
                        style={{
                            backgroundColor: '#ffa500',
                            color: 'white',
                            padding: '0.75rem 1.5rem',
                            borderRadius: '10px',
                            border: 'none',
                            fontSize: '16px',
                            fontWeight: '700',
                            cursor: 'pointer',
                        }}
                        onClick={() => setErrorDialogOpen(false)}
                    >
                        OK
                    </button>
                }
            >
                <div style={{ color: '#585858', fontWeight: 500 }}>{errorMessage}</div>
            </Dialog>
            <div className={`${styles.dialogMobile} `}>
                <div className={`${styles.headerMobile}`}>
                    <h2 className={styles.titleMobile}>{title}</h2>
                    <button className={styles.closeButtonMobile} onClick={onClose}>
                        ×
                    </button>
                </div>

                <div className="p-3">
                    <div
                        className={`${styles.shiftsListMobile} flex justify-content-between overflow-x-auto `}
                        style={{ height: 'auto', maxHeight: '320px' }}
                    >
                        {awardebleShift.map((shift, index) => (
                            <div
                                key={index}
                                className={`${styles.shiftItemMobile} ${
                                    selectShifts.includes(shift.shiftId) ||
                                    shift.awardedAplicantImage
                                        ? styles.selectedShiftBorder
                                        : ''
                                }`}
                            >
                                <div className={styles.shiftInfo}>
                                    <div>
                                        <div
                                            className={`${styles.imageContainerMobile} ${
                                                selectShifts.includes(shift.shiftId) ||
                                                shift.awardedAplicantImage
                                                    ? styles.selectedImageBorder
                                                    : ''
                                            } `}
                                            // Removed onClick handler from image container
                                        >
                                            <img
                                                src={shift.awardedAplicantImage ?? shift.imageUrl}
                                                alt={`Shift ${shift.day}`}
                                                className={styles.shiftImageMobile}
                                            />
                                        </div>
                                    </div>
                                    <div className="flex flex-column">
                                        <span className={styles.dayLabelMobile}>{shift.day}</span>
                                        <div className={styles.timeContainer}>
                                            <div className="flex justify-content-center align-items-center ">
                                                <span
                                                    className={`${styles.timeBox}`}
                                                    style={{
                                                        fontSize: '12px',
                                                        fontWeight: '700',
                                                        color: '#179D52',
                                                        textDecoration: 'underline',
                                                    }}
                                                >
                                                    {formatTimeTo12Hour(shift.startTime)}
                                                </span>
                                            </div>

                                            <span
                                                className="px-1"
                                                style={{
                                                    fontSize: '12px',
                                                    fontWeight: '700',
                                                    color: '#179D52',
                                                    textDecoration: 'underline',
                                                }}
                                            >
                                                to
                                            </span>
                                            <div className="flex justify-content-center align-items-center">
                                                <span
                                                    className={`${styles.timeBox} pr-4 `}
                                                    style={{
                                                        fontSize: '12px',
                                                        fontWeight: '700',
                                                        color: '#179D52',
                                                        textDecoration: 'underline',
                                                    }}
                                                >
                                                    {formatTimeTo12Hour(shift.endTime)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button
                                    onClick={() => handleShiftSelect(shift.shiftId)}
                                    className={`${
                                        selectShifts.includes(shift.shiftId) ||
                                        shift.awardedAplicantImage !== null
                                            ? styles.selectedMobile
                                            : styles.selectButtonMobile
                                    }`}
                                    disabled={shift.awardedAplicantImage !== null}
                                    style={{
                                        backgroundColor:
                                            selectShifts.includes(shift.shiftId) ||
                                            shift.awardedAplicantImage
                                                ? '#179D52'
                                                : '#DFDFDF',
                                        color:
                                            selectShifts.includes(shift.shiftId) ||
                                            shift.awardedAplicantImage
                                                ? '#fff'
                                                : '#585858',
                                        cursor:
                                            shift.awardedAplicantImage !== null
                                                ? 'not-allowed'
                                                : 'pointer',
                                    }}
                                >
                                    &nbsp;&nbsp;
                                    <span style={{ fontSize: '12px', fontWeight: '700' }}>
                                        {shift.awardedAplicantImage !== null
                                            ? 'Awarded'
                                            : selectShifts.includes(shift.shiftId)
                                            ? 'Shift Selected >'
                                            : 'Select Shift >'}
                                    </span>
                                </button>
                            </div>
                        ))}
                    </div>
                    <Divider />

                    <div className={styles.footerMobile}>
                        {awardebleShift.length === selectShifts.length ? (
                            <span
                                className="pl-3 pr-3 flex  align-items-center"
                                style={{
                                    color: '#585858',
                                    textDecoration: 'underline',
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    backgroundColor: 'transparent',
                                    border: '1px solid #000000',
                                    borderRadius: '20px',
                                }}
                                onClick={handleSelectAll}
                            >
                                Unselect all
                            </span>
                        ) : (
                            <button
                                onClick={handleSelectAll}
                                className={`pl-3 pr-3`}
                                style={{
                                    color: '#585858',
                                    textDecoration: 'underline',
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    backgroundColor: 'transparent',
                                    border: '1px solid #000000',
                                    borderRadius: '20px',
                                }}
                            >
                                Select all shifts
                            </button>
                        )}
                        <button
                            className={`${styles.awardButtonMobile} ${
                                selectShifts.length > 0 ? styles.enabledButtonMobile : ''
                            } cursor-pointer`}
                            disabled={selectShifts.length === 0}
                            onClick={handleAwardShifts}
                        >
                            Award Shifts
                        </button>
                    </div>
                    <ConfirmAwardDialog
                        isOpen={isConfirmDialogOpen}
                        title="Confirm Award"
                        message="Would you like to confirm the selected shifts to"
                        onOpenChange={(open) => setIsConfirmDialogOpen(open)}
                        onConfirm={handleConfirm} // Handle confirm action
                        onCancel={handleCancel} // Handle cancel action
                        name={awardebleShift[0]?.publicName}
                        image={awardebleShift[0]?.imageUrl}
                        noText="Go back"
                        yesText="Yes"
                    />
                </div>
            </div>
        </div>
    );
};

export default AwardShiftsTo;
