import { useEffect, useState } from 'react';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import '../../../components/utils/util.css';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import CustomButton from '../../../commonComponents/CustomButton';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import {
    decrementProfileActivationStep,
    incrementProfileActivationStep,
} from '../../../store/slices/applicationSlice';
import { IoCheckmarkDoneOutline } from 'react-icons/io5';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';
interface OddJobsComponentProps {
    onComplete?: () => void;
}
const OddJobs: React.FC<OddJobsComponentProps> = ({ onComplete }) => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    // const session = useSelector((state: RootState) => state.sessionInfo.data);
    const dispatch = useDispatch<AppDispatch>();
    const { disableLoader, enableLoader } = useLoader();
    const {isMobile}=useIsMobile();
    const jobs = [
        {
            label: 'Laundry',
            description: 'Washing, drying, folding & putting clothes away, ironing etc.',
        },
        {
            label: 'Errand running',
            description: 'Shopping, picking up dry cleaning, mailing packages etc.',
        },
        {
            label: 'Outdoor chores',
            description: 'Sweeping & tidying, car washing, taking the bins in & out etc.',
        },
        {
            label: 'Help for the elderly',
            description: 'Odd jobs & errands for elderly parents and neighbours',
        },
        { label: 'Other Odd Jobs', description: 'Admin, tech support, and other household tasks' },
    ];

    const [checkedState, setCheckedState] = useState<boolean[]>(new Array(jobs.length).fill(false));

    useEffect(() => {
        if (sessionInfo.data['oddJobPreferences']) {
            setCheckedState([
                sessionInfo.data['oddJobPreferences'].laundry,
                sessionInfo.data['oddJobPreferences'].errands,
                sessionInfo.data['oddJobPreferences'].outdoorChores,
                sessionInfo.data['oddJobPreferences'].elderlyHelp,
                sessionInfo.data['oddJobPreferences'].otherOddJobs,
            ]);
        }
    }, [sessionInfo]);

    const handleCheckboxChange = (index: number) => {
        const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
        setCheckedState(updatedCheckedState);
    };

    const handleNext = () => {
        const payload = {
            ...sessionInfo.data,
            oddJobPreferences: {
                laundry: checkedState[0],
                errands: checkedState[1],
                outdoorChores: checkedState[2],
                elderlyHelp: checkedState[3],
                otherOddJobs: checkedState[4],
            },
        };
        enableLoader();
        dispatch(updateSessionInfo({ payload: payload })).finally(() => {
            disableLoader();
            dispatch(incrementProfileActivationStep());
            onComplete();
        });
    };

    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };

    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ProfileCompletenessHeader
                title="OddJobs"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={()=>dispatch(decrementProfileActivationStep())}
            />
            <div  style={{paddingInline:isMobile && "15px"}}className={`flex`}>

                <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0" style={{ fontSize: '16px', color: '#585858' }}>
                    <span><IoCheckmarkDoneOutline style={{ color: '#585858', fontSize: '18px' }} /></span>Select the Odd Jobs you would be willing to do in your neighbourhood.
                </h1>
            </div>
            <div   style={{paddingInline:isMobile && "15px", marginBottom:isMobile && "70px"}} className="flex flex-column justify-content-center">
                {jobs.map((job, index) => (
                    <label key={job.label} className="flex items-center gap-2 p-1 cursor-pointer">
                        <input
                            type="checkbox"
                            className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                            checked={checkedState[index]}
                            onChange={() => handleCheckboxChange(index)}
                            style={{ fontSize: '18px' }}
                        />
                        <div className="flex flex-column items-center">
                            <div className="txt-clr" style={{ fontSize: '16px' }}>
                                {job.label}
                            </div>
                            <div className="txt-clr" style={{ fontSize: '16px' }}>
                                {job.description}
                            </div>
                        </div>
                    </label>
                ))}
            </div>
            <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <>
                            <i className="pi pi-angle-left"></i>
                            Previous
                        </>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: 'transparent',
                        color: '#585858',
                        width: '156px',
                        height: '39px',
                        fontSize: '14px',
                        fontWeight: '500',
                        margin:isMobile && "5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />

                <CustomButton
                    className={styles.hoverClass}
                    data-skip={checkedState ? 'false' : 'true'}
                    onClick={checkedState.some((state) => state) ? handleNext : handleSkip}
                    label={
                        <>
                            {checkedState.some((state) => state) ? 'Next' : 'Skip'}
                            <i
                                className={`pi pi-angle-${checkedState.some((state) => state) ? 'right' : 'right'
                                    }`}
                                style={{ marginLeft: '8px' }}
                            ></i>
                        </>
                    }
                    style={
                        checkedState.some((state) => state)
                            ? {
                                backgroundColor: '#FFA500',
                                color: '#fff',
                                width: '156px',
                                height: '39px',
                                fontWeight: '800',
                                fontSize: '14px',
                                borderRadius: '8px',
                                border: '2px solid transparent',
                                boxShadow: '0px 4px 12px #00000',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                               margin : isMobile && "10px"
                            }
                            : {
                                backgroundColor: 'transparent',
                                color: '#585858',
                                width: '156px',
                                height: '39px',
                                fontWeight: '400',
                                fontSize: '14px',
                                borderRadius: '10px',
                                border: '1px solid #F0F4F7',
                               margin : isMobile && "10px"
                            }
                    }
                />
            </footer>
        </div>
    );
};

export default OddJobs;
