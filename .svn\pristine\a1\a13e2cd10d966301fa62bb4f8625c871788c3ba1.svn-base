import { Divider } from "primereact/divider";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { Next, RadioButton } from "./Buttons";
import { LuPackageCheck, LuSmilePlus } from "react-icons/lu";
import { HiOutlineBookOpen } from "react-icons/hi2";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import useLoader from "../../../../hooks/LoaderHook";
import CustomDialog from "../../CustomDialog";
import customDialogstyles from "../../../Parent/styles/MyAddresses.module.css";
import { AutoComplete } from "primereact/autocomplete";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import Service from "../../../../services/services";
import Auth from "../../../../services/authService";
import { updateUser } from "../../../../store/tunks/sessionInfoTunk";
import styles from "../Styles/JobType.module.css";
import { IoClose } from "react-icons/io5";
import { useNavigate, useSearchParams } from "react-router-dom";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";

import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import { IframeBridge } from "../../../../services/IframeBridge";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

// Types
type Address = {
  addressLabel: string;
  addressLine1: string;
  addressLine2: any;
  postCode: string;
  suburb: string;
  state: string;
  isPrimaryAddress: boolean;
  formattedSuburb: string;
  howLongHaveYouLivedInThisAddress: any;
  latitude: number;
  longitude: number;
  country: string;
  formattedAddress: string;
  id: number;
};

// utils

const generateAddressLabel = (address: Address) => {
  let formatedAddress = "";
  if (address?.addressLabel && address?.addressLabel.trim().length > 0) {
    formatedAddress += address.addressLabel.trim() + " - ";
  }
  if (address?.addressLine1 && address?.addressLine1.trim().length > 0) {
    formatedAddress += address.addressLine1.trim() + ", ";
  }
  if (address?.suburb && address?.suburb.trim().length > 0) {
    formatedAddress += address.suburb.trim();
  }
  return formatedAddress;
};

const isValid = (value: { [key: string]: any }) => value?.jobType && value?.addressId && (value.jobType !== 256 || !!value?.jobSubType);

type JobTypeSettings = {
  activeValues: number[];
  subActiveValues: number[];
};

const isMatchingJobType = (settings: JobTypeSettings, jobType?: number, jobSubType?: number) => {
  return (
    (jobType !== undefined && settings.activeValues.includes(jobType)) || (jobSubType !== undefined && settings.subActiveValues.includes(jobSubType))
  );
};

const GET_CHILDCARE_JOB_TYPE = () => ({
  name: "Childcare",
  activeIcon: <LuSmilePlus style={{ minWidth: "26px", minHeight: "26px" }} color="#179D52" />,
  inactiveIcon: <LuSmilePlus style={{ minWidth: "26px", minHeight: "26px" }} color="#585858" />,
  activeValues: [1, 2, 4, 8, 12],
  subActiveValues: [],
  subType: [
    {
      name: "One-Off Job",
      value: 1,
      description: null,
      subType: null,
      activeValues: [1],
    },
    {
      name: "Recurring Job",
      value: 2,
      description: "Full time and part time jobs with repeating schedule",
      activeValues: [2, 4, 8, 12],
      subType: [
        { name: "Daytime Nanny", value: 2, description: null, subType: null },
        {
          name: "Before School Care",
          value: 4,
          description: null,
          subType: null,
        },
        {
          name: "After School Care",
          value: 8,
          description: null,
          subType: null,
        },
        {
          name: "Before & After School Care",
          value: 12,
          description: null,
          subType: null,
        },
      ],
    },
  ],
});

const GET_TUTORING_JOB_TYPE = () => ({
  name: "Tutoring",
  activeIcon: <HiOutlineBookOpen style={{ minWidth: "26px", minHeight: "26px" }} color="#179D52" />,
  inactiveIcon: <HiOutlineBookOpen style={{ minWidth: "26px", minHeight: "26px" }} color="#585858" />,
  activeValues: [64, 128],
  subActiveValues: [],
  subType: [
    {
      name: "Primary School Tutoring",
      value: 64,
      description: null,
      subType: null,
    },
    {
      name: "High School Tutoring",
      value: 128,
      description: null,
      subType: null,
    },
  ],
});

const GET_ODD_JOB_JOB_TYPE = () => ({
  name: "Odd Jobs",
  activeIcon: <LuPackageCheck style={{ minWidth: "26px", minHeight: "26px" }} color="#179D52" />,
  inactiveIcon: <LuPackageCheck style={{ minWidth: "26px", minHeight: "26px" }} color="#585858" />,
  activeValues: [256],
  subActiveValues: [1, 2, 4, 8, 16],
  subType: [
    {
      name: "Laundry",
      value: 256,
      description: "Washing, drying, folding & Washing, drying, folding & putting clothes away, ironing etc.",
      subType: null,
      subValue: 1,
    },
    {
      name: "Errand running",
      value: null,
      description: "Shopping, picking up dry cleaning mailing packages etc",
      subType: null,
      subValue: 2,
    },
    {
      name: "Outdoor chores ",
      value: null,
      description: "Sweeping & tidying, car washing, taking the bins in & out etc",
      subType: null,
      subValue: 4,
    },
    {
      name: "Help for elderly",
      value: null,
      description: "Odd jobs & errands for elderly parents and neighbors ",
      subType: null,
      subValue: 8,
    },
    {
      name: "Other",
      value: null,
      description: "Admin, tech support, and other household tasks",
      subType: null,
      subValue: 16,
    },
  ],
});

function JobType() {
  const { isMobile } = useIsMobile();

  return isMobile ? <JobTypeMobile /> : <JobTypeWeb />;
}

export default JobType;

interface JobTypeContextProps {
  data: { [key: string]: any };
  metadata: { [key: string]: any };
  setData: React.Dispatch<React.SetStateAction<{}>>;
  setMetadata: React.Dispatch<React.SetStateAction<{}>>;
  sessionInfo: { [key: string]: any };
  onNext: (formData: { [key: string]: any }) => void;
}

const JobTypeContext = createContext<JobTypeContextProps>({
  data: {},
  metadata: {},
  setData: () => {},
  setMetadata: () => {},
  sessionInfo: {},
  onNext: () => {},
});

const useJobTypeContext = () => {
  const context = useContext(JobTypeContext);
  return { context };
};

const useJobTypeHook = () => {
  const sessionInfoState = useSelector((state: RootState) => state.sessionInfo);
  const { enableLoader, disableLoader } = useLoader();
  const { payload, setpayload, next, prev } = useJobManager();
  const clientType = utils.getCookie(CookiesConstant.clientType);
  // const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  const navigate = useNavigate();
  useEffect(() => {
    if (sessionInfoState.loading) {
      enableLoader();
      return;
    } else {
      disableLoader();
    }
  }, [sessionInfoState.loading, sessionInfoState.data]);
  const clientCategory = sessionInfoState?.data?.["client"]?.["clientCategory"];
  const canShowJobOptions =
    sessionInfoState.data?.["isJobManagerAccount"] ||
    (clientCategory === c.clientCategory.FAMILY && !sessionInfoState.data["paymentInfo"]?.["isCorporateMembership"]) ||
    (clientCategory === c.clientCategory.FAMILY &&
      sessionInfoState.data["paymentInfo"]?.["isCorporateMembership"] &&
      (sessionInfoState.data["permissions"]?.["canPostUnlimitedPrimarySchoolTutoringJobs"] ||
        sessionInfoState.data["permissions"]?.["canPostUnlimitedHighSchoolTutoringJobs"]));
  const onNext = (formData: { [key: string]: any }) => {
    enableLoader();
    const selectedAddress = sessionInfoState.data["addresses"].find((a) => a.id === formData.addressId) as Address;
    const filteredApplicantFilters = (payload.applicantFilters ?? []).filter(
      (filter) => !["jobTypes", "jobSubTypes", "address"].includes(filter.field)
    );
    setpayload({
      ...payload,
      jobType: formData.jobType,
      ...(formData.jobType === 256 && {
        jobSubType: formData.jobSubType,
      }),
      addressId: formData.addressId,
      addressLabel:selectedAddress.addressLabel,
      latitude: selectedAddress?.latitude,
      longitude: selectedAddress?.longitude,
      applicantFilters: [
        ...filteredApplicantFilters,
        {
          field: "address",
          operator: "eq",
          value: [selectedAddress?.longitude, selectedAddress?.latitude],
        },
        {
          field: "jobTypes",
          operator: "eq",
          value: [formData.jobType],
        },
        ...(formData.jobType === 256
          ? [
              {
                field: "jobSubTypes",
                operator: "eq",
                value: [formData.jobSubType],
              },
            ]
          : []),
      ],
    });
    disableLoader();
    next("job-posting");
  };
  const handleGoBack = () => {
    sessionStorage.removeItem("jobManagement");
    if (clientType === 1) {
      navigate("/parent-home");
    } else if (clientType === 2) {
      navigate("/business-home");
    } else {
      navigate("/");
    }
  };
  return { sessionInfo: sessionInfoState.data, onNext, payload, prev, handleGoBack, clientType, canShowJobOptions };
};

// Web version of the JobType component

const JobTypeWeb = () => {
  const [initial, setInitial] = useState(true);
  const [data, setData] = useState({});
  const [metadata, setMetadata] = useState({});
  const { sessionInfo, onNext, payload } = useJobTypeHook();

  useEffect(() => {
    if (sessionInfo) {
      setMetadata((prev) => ({ ...prev, country: sessionInfo?.["country"] }));
      if (data["addressId"]) {
        setData((prev) => ({
          ...prev,
          addressId: sessionInfo?.["addresses"].at(-1).id,
        }));
      }
    }
  }, [sessionInfo]);

  useEffect(() => {
    if (!payload || !initial) return;
    setData(payload);
    setInitial(true);
  }, [payload, initial]);

  return (
    <JobTypeContext.Provider
      value={{
        data,
        sessionInfo,
        setData,
        metadata,
        setMetadata,
        onNext,
      }}
    >
      <JobTypeWebContent />
    </JobTypeContext.Provider>
  );
};

const JobTypeWebContent = () => {
  const [searchParams] = useSearchParams();
  const { context } = useJobTypeContext();
  const { handleGoBack } = useJobTypeHook();

  return (
    <div className="w-full h-full overflow-hidden overflow-y-auto relative">
      <div className="w-full h-full flex flex-column align-items-center pt-3">
        {}
        <div className="flex-grow-1 w-full flex flex-column align-items-center" style={{ maxWidth: "70%" }}>
          {!searchParams.has("jobaction") && (
            <>
              <h1
                className="m-0 p-0"
                style={{
                  fontSize: "30px",
                  fontWeight: "700",
                  color: "#585858",
                }}
              >
                What kind of job are you posting?
              </h1>
              <div className="w-full my-4 flex gap-5">
                <div
                  className="flex flex-column gap-3 my-2"
                  style={{
                    transition: "all 0.3s ease-in-out",
                    minWidth: context.data?.jobType >= 0 ? "40%" : "100%",
                    maxWidth: context.data?.jobType >= 0 ? "40%" : "100%",
                  }}
                >
                  <JobTypeList />
                </div>
                {context.data?.jobType >= 0 && (
                  <div className="flex flex-column flex-grow-1">
                    <JobTypeSubsection />
                  </div>
                )}
              </div>
            </>
          )}
          {!!context.data?.jobType && !searchParams.has("jobaction") && <Divider />}
          {!!context.data?.jobType && (
            <>
              <h1
                className="m-0 p-0 mt-2"
                style={{
                  fontSize: "30px",
                  fontWeight: "700",
                  color: "#585858",
                }}
              >
                Job Address
              </h1>
              <div className="w-full my-4 flex flex-column gap-3">
                <AddressSection />
                <AlternaviteAddressSection />
              </div>
            </>
          )}
        </div>
        <div className="h-min w-full py-3  bottom-0 bg-white flex justify-content-center" style={{ borderTop: "1px solid #DFDFDF" }}>
          <div className="h-min w-full flex justify-content-between" style={{ maxWidth: "70%" }}>
            <button
              onClick={handleGoBack}
              style={{
                background: "none",
                border: "none",
                padding: 0,
                margin: 0,
                color: "#585858", // Blue text (you can change this)
                // textDecoration: 'underline',
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: 500,
              }}
            >
              {`<`} Go Back
            </button>
            <Next
              className=""
              disabled={!isValid(context.data)}
              onClick={(e) => {
                e.preventDefault();
                context.onNext(context.data);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Mobile version of the JobType component

const JobTypeMobile = () => {
  const [initial, setInitial] = useState(true);
  const [data, setData] = useState({});
  const [metadata, setMetadata] = useState({});
  const { sessionInfo, onNext, payload } = useJobTypeHook();
  const { disableLoader } = useLoader();

  useEffect(() => {
    const id = requestAnimationFrame(() => {
      IframeBridge.sendToParent({
        type: "canProcced",
        data: null,
      });
      disableLoader();
    });
    return () => cancelAnimationFrame(id);
  }, []);

  useEffect(() => {
    if (sessionInfo) {
      setMetadata((prev) => ({ ...prev, country: sessionInfo?.["country"] }));
      if (data["addressId"]) {
        setData((prev) => ({
          ...prev,
          addressId: sessionInfo?.["addresses"].at(-1).id,
        }));
      }
    }
  }, [sessionInfo]);

  useEffect(() => {
    if (!payload || !initial) return;

    setData(payload);

    setMetadata((prev) => ({
      ...prev,
      subJobTypeSelected: !!payload.jobType ? true : false,
    }));

    setInitial(true);
  }, [payload, initial]);

  return (
    <JobTypeContext.Provider
      value={{
        data,
        sessionInfo,
        setData,
        metadata,
        setMetadata,
        onNext,
      }}
    >
      <JobTypeMobileContent />
    </JobTypeContext.Provider>
  );
};

const JobTypeMobileContent = () => {
  const { context } = useJobTypeContext();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { inIframe } = useSelector((state: RootState) => state.applicationState);



  const showAddressSection = () => {
    return GET_CHILDCARE_JOB_TYPE().subType[1].activeValues.includes(context.data?.jobType)
      ? context.metadata?.subJobTypeSelected
      : !!context.data?.jobType;
  };

  return (
    <div className="w-full h-full overflow-hidden overflow-y-auto relative">
      <div className="w-full h-full flex flex-column align-items-center pt-3">
        <div className="flex-grow-1 w-full flex flex-column align-items-center" style={{ maxWidth: "90%" }}>
          {!searchParams.has("jobaction") && (
            <>
              <h1
                className="m-0 p-0"
                style={{
                  fontSize: "22px",
                  fontWeight: "700",
                  color: "#585858",
                }}
              >
                What kind of job are you posting?
              </h1>
              <div className="w-full h-min flex flex-column gap-2 mt-3">
                <JobTypeListMobile />
              </div>
              <div className="w-full h-min flex flex-column gap-2 mt-3">
                <JobTypeListSubType />
              </div>
            </>
          )}

          {showAddressSection() && (
            <>
              <h1
                className="m-0 p-0 align-self-start"
                style={{
                  fontSize: "22px",
                  fontWeight: "700",
                  color: "#179D52",
                }}
              >
                Job Address
              </h1>
              <div className="w-full h-min flex flex-column gap-2 my-3">
                <AddressSectionMobile />
              </div>
            </>
          )}
        </div>
        <div className="h-min w-full py-3 sticky bottom-0 bg-white flex justify-content-center" style={{ borderTop: "1px solid #DFDFDF" }}>
          <div className="h-min w-full " style={{ maxWidth: "270px" }}>
            <BackButtonPortal id="back-button-portal">
              <div
                onClick={() => {
                  sessionStorage.removeItem("jobManagement");
                  IframeBridge.sendToParent({
                    type: "goBack-postjob",
                  });
                  if (!inIframe) {
                    navigate("/");
                  }
                }}
              >
                <img
                  src={SideArrow}
                  alt="cross"
                  width={13}
                  height={20}
                  className="cursor-pointer"
                  style={{ display: "block", objectFit: "contain" }}
                />
              </div>
            </BackButtonPortal>
            <Next
              className="w-full"
              disabled={
                !isValid(context.data) ||
                (GET_CHILDCARE_JOB_TYPE().subType[1].activeValues.includes(context.data?.jobType) ? !context.metadata?.subJobTypeSelected : false)
              }
              onClick={(e) => {
                e.preventDefault();
                context.onNext(context.data);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Common components for both web and mobile versions

const ToggleButton = ({
  name,
  activeValues,
  icon1,
  icon2,
  onClick,
  differentType,
}: {
  name: string;
  icon1: React.ReactNode;
  icon2: React.ReactNode;
  activeValues: number[];
  onClick?: () => void;
  differentType?: boolean;
}) => {
  const { isMobile } = useIsMobile();
  const { context } = useJobTypeContext();

  const isActive = () =>
    differentType
      ? context.metadata?.subJobTypeSelected && (activeValues.includes(context.data?.jobType) || activeValues.includes(context.data?.jobSubType))
      : activeValues.includes(context.data?.jobType);

  return (
    <div
      className={`flex align-items-center cursor-pointer relative ${isMobile ? "flex-column justify-content-center" : ""}`}
      style={{
        border: isActive() ? (isMobile ? "2px solid #179D52" : "3px solid #179D52") : "1px solid #DFDFDF",
        boxShadow: isMobile ? "" : "0 4px 4px 0 #00000040",
        height: differentType ? "50px" : isMobile ? "98px" : "80px",
        borderRadius: "10px",
      }}
      onClick={(e) => {
        e.preventDefault();
        if (onClick) {
          onClick();
        }
      }}
    >
      <div className={`flex w-full justify-content-center align-items-center gap-2 ${isMobile ? "flex-column" : ""}`}>
        {!differentType && (
          <div
            className={`flex justify-content-center align-items-center`}
            style={{
              width: isMobile ? "48px" : "30px",
              height: isMobile ? "48px" : "30px",
              borderRadius: "999px",
              border: isMobile ? (isActive() ? "2px solid #179D52" : "1px solid #dfdfdf") : "none",
              marginLeft: isMobile ? "" : "30px",
              marginTop: isMobile ? "5px" : "",
            }}
          >
            {isActive() ? icon1 : icon2}
          </div>
        )}
        <p
          className="m-0 p-0 mx-auto"
          style={{
            fontSize: isMobile ? (isActive() ? "20px" : "18px") : "22px",
            fontWeight: isMobile ? (isActive() ? "700" : "500") : "600",
            color: isActive() ? "#179D52" : "#585858",
          }}
        >
          {name}
        </p>
      </div>
    </div>
  );
};

const JobTypeList = () => {
  const { context } = useJobTypeContext();
  const { clientType, canShowJobOptions } = useJobTypeHook();
  const childcareSettings = GET_CHILDCARE_JOB_TYPE();
  const tutoringSettings = GET_TUTORING_JOB_TYPE();
  const oddJobSettings = GET_ODD_JOB_JOB_TYPE();
  return (
    <>
      <ToggleButton
        name="Childcare"
        activeValues={childcareSettings.activeValues}
        icon1={childcareSettings.activeIcon}
        icon2={childcareSettings.inactiveIcon}
        onClick={() => context.setData((prev) => ({ ...prev, jobType: 1, jobSubType: 0 }))}
      />
      {context.sessionInfo?.client.clientType !== c.clientType.BUSINESS && (
        <ToggleButton
          name="Tutoring"
          activeValues={tutoringSettings.activeValues}
          icon1={tutoringSettings.activeIcon}
          icon2={tutoringSettings.inactiveIcon}
          onClick={() =>
            context.setData((prev) => ({
              ...prev,
              jobType: 64,
              jobSubType: 0,
            }))
          }
        />
      )}
      <ToggleButton
        name="Odd Jobs"
        activeValues={oddJobSettings.activeValues}
        icon1={oddJobSettings.activeIcon}
        icon2={oddJobSettings.inactiveIcon}
        onClick={() =>
          context.setData((prev) => ({
            ...prev,
            jobType: 256,
            jobSubType: oddJobSettings.subActiveValues[0],
          }))
        }
      />
      {(clientType === "2" || (clientType === "1" && !canShowJobOptions)) && (
        <div
          className="flex"
          style={{
            width: "100%",
            color: "#777777",
            fontSize: "14px",
            fontWeight: "400",
            marginTop: "10px",
          }}
        >
          <p>* Please contact Customer Service if you are trying to post a Primary School or a High School Tutoring Job.</p>
        </div>
      )}
    </>
  );
};

const JobTypeSubsection = () => {
  const { context } = useJobTypeContext();
  const childcareSettings = GET_CHILDCARE_JOB_TYPE();
  const tutoringSettings = GET_TUTORING_JOB_TYPE();
  const oddJobSettings = GET_ODD_JOB_JOB_TYPE();

  if (isMatchingJobType(childcareSettings, context.data?.jobType, context.data?.jobSubType)) {
    return <JobTypeSubsectionChildcare settings={childcareSettings} />;
  }

  if (isMatchingJobType(tutoringSettings, context.data?.jobType, context.data?.jobSubType)) {
    return <JobTypeSubsectionTutoring settings={tutoringSettings} />;
  }

  if (isMatchingJobType(oddJobSettings, context.data?.jobType, context.data?.jobSubType)) {
    return <JobTypeSubsectionOddJobs settings={oddJobSettings} />;
  }

  return null;
};

const JobTypeSubsectionChildcare = ({ settings }: { settings: ReturnType<typeof GET_CHILDCARE_JOB_TYPE> }) => {
  const { context } = useJobTypeContext();
  return (
    <div className="grid mt-4">
      {settings.subType.map((item, index) => (
        <div key={index} className={index === 0 ? "col-4" : "col-8"}>
          <div
            className="ml-2 flex align-items-center gap-2 cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              context.setData((prev) => ({
                ...prev,
                jobType: item.value,
                jobSubType: 0,
              }));
            }}
          >
            <RadioButton selected={item.activeValues.includes(context.data?.jobType)} />
            <p
              className="m-0 p-0"
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#585858",
              }}
            >
              {item.name}
            </p>
          </div>
          {item.description && (
            <p
              className="p-0 text-sm f"
              style={{
                fontSize: "14px",
                fontWeight: "300",
                color: "#585858",
                margin: 0,
                marginLeft: "4ch",
              }}
            >
              {item.description}
            </p>
          )}
        </div>
      ))}
      <Divider className="col-12" />
      <div className="col-4" />
      {settings.subType[1].activeValues.includes(context.data?.jobType) && (
        <div className="col-8 gap-2 flex flex-column">
          {settings.subType[1].subType.map((item, index) => {
            return (
              <div
                className="ml-2 flex align-items-center gap-2 cursor-pointer"
                key={index}
                onClick={(e) => {
                  e.preventDefault();
                  context.setData((prev) => ({
                    ...prev,
                    jobType: item.value,
                    jobSubType: 0,
                  }));
                }}
              >
                <RadioButton selected={item.value === context.data?.jobType} />
                <p
                  className="m-0 p-0"
                  style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    color: "#585858",
                  }}
                >
                  {item.name}
                </p>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const JobTypeSubsectionTutoring = ({ settings }: { settings: ReturnType<typeof GET_TUTORING_JOB_TYPE> }) => {
  const { isMobile } = useIsMobile();
  const { context } = useJobTypeContext();
  return (
    <div
      className={`flex gap-2 ${isMobile ? "justify-content-between w-full " : ""}`}
      style={{
        marginTop: isMobile ? "" : "130px",
      }}
    >
      {settings.subType.map((item, index) => (
        <div
          key={index}
          className="ml-2 flex align-items-center gap-2 cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            context.setMetadata((prev) => ({
              ...prev,
              subJobTypeSelected: false,
            }));
            context.setData((prev) => ({
              ...prev,
              jobType: item.value,
              jobSubType: 0,
            }));
          }}
        >
          <RadioButton selected={item.value === context.data?.jobType} />
          <p
            className="m-0 p-0"
            style={{
              fontSize: "16px",
              fontWeight: "600",
              color: "#585858",
            }}
          >
            {item.name}
          </p>
        </div>
      ))}
    </div>
  );
};

const JobTypeSubsectionOddJobs = ({ settings }: { settings: ReturnType<typeof GET_ODD_JOB_JOB_TYPE> }) => {
  const { isMobile } = useIsMobile();
  const { context } = useJobTypeContext();
  return (
    <div
      className="grid"
      style={{
        marginTop: isMobile ? "" : "220px",
      }}
    >
      {settings.subType.map((item, index) => (
        <div key={index} className={`${isMobile ? "col-12" : "col-6"} flex flex-column`}>
          <div
            key={index}
            className="ml-2 flex align-items-center gap-2 cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              context.setMetadata((prev) => ({
                ...prev,
                subJobTypeSelected: false,
              }));
              context.setData((prev) => ({
                ...prev,
                jobSubType: item.subValue,
              }));
            }}
          >
            <RadioButton selected={item.subValue === context.data?.jobSubType} />
            <p
              className="m-0 p-0"
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#585858",
              }}
            >
              {item.name}
            </p>
          </div>
          {item.description && (
            <p
              className="p-0"
              style={{
                fontSize: "14px",
                fontWeight: "300",
                color: "#585858",
                margin: 0,
                marginLeft: "4ch",
              }}
            >
              {item.description}
            </p>
          )}
        </div>
      ))}
    </div>
  );
};

const AddressSection = () => {
  const { context } = useJobTypeContext();
  const addresses = (context.sessionInfo?.addresses || []) as Address[];

  const selected = (address: Address) => context.data?.addressId === address.id;

  return addresses.map((address, index) => {
    return (
      <div
        key={index}
        className="flex gap-2 align-items-center cursor-pointer"
        onClick={(e) => {
          e.preventDefault();
          context.setData((prev) => ({ ...prev, addressId: address.id }));
        }}
      >
        <RadioButton selected={selected(address)} />
        <p
          className="m-0 p-0"
          style={{
            fontSize: "16px",
            fontWeight: selected(address) ? "600" : "400",
            color: "#585858",
          }}
        >
          {generateAddressLabel(address)}
        </p>
      </div>
    );
  });
};

const AlternaviteAddressSection = () => {
  const { context } = useJobTypeContext();
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  return (
    <>
      <AddNewAddressPopup />
      {(clientType === "1" || (clientType === "2" && sessionInfo.data["client"]["clientCategory"] === 3)) && (
        <div
          className="flex gap-2 align-items-center cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            context.setMetadata((prev) => ({
              ...prev,
              showAddAddressDialog: true,
            }));
          }}
        >
          <RadioButton selected={false} />
          <p
            className="m-0 p-0"
            style={{
              fontSize: "16px",
              fontWeight: "400",
              color: "#585858",
            }}
          >
            Alternate Address
          </p>
        </div>
      )}
    </>
  );
};

const AddNewAddressPopup = () => {
  const { isMobile } = useIsMobile();
  const [addressSuggestions, setAddressSuggestions] = useState([]);
  const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<any[]>([]);

  const { enableLoader, disableLoader } = useLoader();

  const { context } = useJobTypeContext();
  const dispatch = useDispatch<AppDispatch>();

  const handleDialogClose = () => {
    context.setMetadata((prev) => ({ ...prev, showAddAddressDialog: false }));
    context.setMetadata((prev) => ({ ...prev, addressLine1: "" }));
    context.setMetadata((prev) => ({ ...prev, addressLine2: "" }));
    context.setMetadata((prev) => ({
      ...prev,
      country: context.sessionInfo?.["country"] || "au",
    }));
    context.setMetadata((prev) => ({ ...prev, nickname: "" }));
    context.setMetadata((prev) => ({ ...prev, postCode: "" }));
    context.setMetadata((prev) => ({ ...prev, state: "" }));
    context.setMetadata((prev) => ({ ...prev, suburb: "" }));
    context.setMetadata((prev) => ({ ...prev, nzAuto: true }));
    context.setMetadata((prev) => ({ ...prev, nzAddress: "" }));
  };

  const onSuggestionsFetchRequested = async (value) => {
    if (value.length < 6) {
      setAddressSuggestions([]);
      return;
    }

    try {
      Service.addressSearch(
        value,
        (res) => {
          setAddressSuggestions(res);
        },
        (err) => {}
      );
    } catch (error) {}
  };

  const searchAddress = (event: { query: string }) => {
    Auth.suburbSearch(
      event.query,
      (res) => {
        const suggestions = res.map((item) => ({
          label: `${item.name}, ${(item.state as string).toUpperCase()}`,
          value: item,
        }));
        setAutoCompleteSuggestions(suggestions);
      },
      () => {}
    );
  };

  const isFormComplete = () => {
    return (
      context.metadata?.addressLine1 && context.metadata?.suburb && context.metadata?.postCode && context.metadata?.country && context.metadata?.state
    );
  };

  const handleAddressNext = () => {
    if (isFormComplete()) {
      const updatedAddresses = context.sessionInfo?.["addresses"].map((address) => {
        if (address.id === context.metadata?.selectedAddressId) {
          // If this is the address being edited, update its fields
          return {
            ...address,
            addressLabel: context.metadata?.nickname,
            addressLine1: context.metadata?.addressLine1,
            addressLine2: context.metadata?.addressLine2,
            country: context.metadata?.country,
            postCode: context.metadata?.postCode,
            state: context.metadata?.state,
            suburb: context.metadata?.suburb,
          };
        }
        return address; // Leave other addresses unchanged
      });

      const payload = {
        ...(context.sessionInfo as object), // Retain existing session info data
        addresses:
          context.metadata?.selectedAddressId !== undefined
            ? updatedAddresses // If editing, use the updated addresses
            : [
                ...context.sessionInfo?.["addresses"],
                {
                  addressLabel: context.metadata?.nickname,
                  addressLine1: context.metadata?.addressLine1,
                  addressLine2: context.metadata?.addressLine2,
                  autoCompleteSuggestions: [],
                  autoCompleteValue: "",
                  country: context.metadata?.country,
                  isPrimaryAddress: false,
                  postCode: context.metadata?.postCode,
                  showDetailedAddress: false,
                  state: context.metadata?.state,
                  suburb: context.metadata?.suburb,
                },
              ], // If adding a new address, append it to the list
      };

      enableLoader();
      dispatch(updateUser({ payload })).finally(() => {
        context.setMetadata((prev) => ({ ...prev, addressLine1: "" }));
        context.setMetadata((prev) => ({ ...prev, addressLine2: "" }));
        context.setMetadata((prev) => ({
          ...prev,
          country: context.sessionInfo?.["country"] || "au",
        }));
        context.setMetadata((prev) => ({ ...prev, nickname: "" }));
        context.setMetadata((prev) => ({ ...prev, postCode: false }));
        context.setMetadata((prev) => ({ ...prev, state: "" }));
        context.setMetadata((prev) => ({ ...prev, suburb: "" }));
        context.setMetadata((prev) => ({ ...prev, nzAuto: true }));
        context.setMetadata((prev) => ({ ...prev, nzAddress: "" }));
        context.setMetadata((prev) => ({
          ...prev,
          showAddAddressDialog: false,
        }));
        disableLoader();
      });
    }
  };

  return (
    <CustomDialog
      className={isMobile ? styles.dialog : ""}
      visible={context.metadata?.showAddAddressDialog}
      onHide={handleDialogClose}
      closeClicked={handleDialogClose}
      profileCompletion={0}
    >
      <div className={`relative ${customDialogstyles.addAddressContainer} ${isMobile ? styles.addAddressContainer : ""}`}>
        <header>
          <div className="flex justify-content-between h-min align-items-center">
            <h2 className={`${customDialogstyles.addAddressHeader} ${isMobile ? styles.addAddressHeader : ""} m-0 p-0`}>{"Add Address"}</h2>
            {isMobile && (
              <IoClose
                size={40}
                color="#585858"
                onClick={(e) => {
                  e.preventDefault();
                  handleDialogClose();
                }}
              />
            )}
          </div>
          <Divider className={customDialogstyles.addaddressDivider} />
        </header>

        <div className={`${customDialogstyles.flexContainer} ${isMobile ? styles.flexContainer : ""}`}>
          <div className={customDialogstyles.addAddressDiv}>
            <div className={customDialogstyles.dropdownContainerCountry}>
              <p className={customDialogstyles.addressHead}>Country</p>
              <Dropdown
                disabled
                id="Country"
                name="Country"
                options={[
                  { label: "Australia", value: "au" },
                  { label: "New Zealand", value: "nz" },
                ]}
                placeholder="Select Country"
                value={context.metadata?.country}
                onChange={(e) => {
                  e.preventDefault();
                  context.setMetadata((prev) => ({
                    ...prev,
                    country: context.sessionInfo?.["country"] || "au",
                  }));
                  context.setMetadata((prev) => ({
                    ...prev,
                    addressLine1: "",
                  }));
                  context.setMetadata((prev) => ({
                    ...prev,
                    addressLine2: "",
                  }));
                  context.setMetadata((prev) => ({ ...prev, nickname: "" }));
                  context.setMetadata((prev) => ({ ...prev, postCode: false }));
                  context.setMetadata((prev) => ({ ...prev, state: "" }));
                  context.setMetadata((prev) => ({ ...prev, suburb: "" }));
                }} // Update country
                className="input-placeholder"
              />
            </div>

            {context.metadata?.country === "nz" && context.metadata?.nzAuto ? (
              <div className={customDialogstyles.nzAddressContainer}>
                <p className={customDialogstyles.addressHead}>Address</p>

                <div className="input-container" style={{ marginTop: "11px", width: "100%" }}>
                  <AutoComplete
                    value={context.metadata?.nzAddress} // Bind input value to the state
                    suggestions={addressSuggestions} // Pass suggestions to AutoComplete
                    completeMethod={(e) => onSuggestionsFetchRequested(e.query)}
                    className={customDialogstyles.nzAddressDropdown}
                    placeholder="Start typing your address"
                    onChange={(e) => {
                      const inputValue = e.value;

                      // Restrict numerical input
                      if (/^\d+$/.test(inputValue)) {
                        return; // Ignore numerical input
                      }

                      context.setMetadata((prev) => ({
                        ...prev,
                        nzAddress: inputValue,
                      }));
                      // Update state with valid input
                    }}
                    onSelect={(e) =>
                      context.setMetadata((prev) => ({
                        ...prev,
                        address: e.value,
                      }))
                    }
                    style={{ width: "100%" }}
                  />
                </div>

                <button
                  className={customDialogstyles.switchToBtn}
                  onClick={() => {
                    context.setMetadata((prev) => ({ ...prev, nzAuto: false }));
                  }}
                >
                  Type Address Manually
                </button>
              </div>
            ) : (
              <>
                <div className={customDialogstyles.addAddressDiv}>
                  <p className={customDialogstyles.addressHead}>Address</p>
                  <div className="input-container" style={{ marginTop: "18px", width: "100%" }}>
                    <InputText
                      id="addressLine1"
                      name="addressLine1"
                      placeholder=""
                      style={{ width: "100%" }}
                      className="input-placeholder"
                      value={context.metadata?.addressLine1}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        const filteredValue = inputValue.trimStart();
                        context.setMetadata((prev) => ({
                          ...prev,
                          addressLine1: filteredValue,
                        }));
                      }}
                    />
                    <label htmlFor="addressLine1" className={`label-name ${context.metadata?.addressLine1 ? "label-float" : ""}`}>
                      Address Line 1
                    </label>
                  </div>
                  <div className="input-container" style={{ marginTop: "25px", width: "100%" }}>
                    <InputText
                      id="addressLine2"
                      name="addressLine2"
                      style={{ width: "100%" }}
                      placeholder=""
                      className="input-placeholder"
                      value={context.metadata?.addressLine2}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        const filteredValue = inputValue.trimStart();
                        context.setMetadata((prev) => ({
                          ...prev,
                          addressLine2: filteredValue,
                        }));
                      }}
                    />
                    <label htmlFor="addressLine2" className={`label-name ${context.metadata?.addressLine2 ? "label-float" : ""}`}>
                      Address Line 2
                    </label>
                  </div>
                </div>

                <div className={`${customDialogstyles.addAddressSecond} flex flex-column md:flex-row`}>
                  <div
                    className="input-container"
                    style={{
                      marginTop: "18px",
                      maxWidth: "259px",
                      height: "56px",
                    }}
                  >
                    <p className={customDialogstyles.addressHead}>Suburb</p>
                    <AutoComplete
                      id="Suburb"
                      name="Suburb"
                      placeholder=""
                      className={`${customDialogstyles.autoComplete} input-placeholder`}
                      suggestions={autoCompleteSuggestions}
                      completeMethod={searchAddress}
                      value={context.metadata?.suburb}
                      field="label"
                      onChange={(e) => {
                        const inputValue = e.value;
                        const filteredValue = typeof inputValue === "string" ? inputValue.trimStart() : inputValue.label;

                        // Restrict numerical input
                        if (/^\d+$/.test(filteredValue)) {
                          return; // Ignore numerical input
                        }
                        context.setMetadata((prev) => ({
                          ...prev,
                          suburb: filteredValue,
                        }));
                      }}
                      onSelect={(e) => {
                        const selectedSuburb = e.value.value;

                        context.setMetadata((prev) => ({
                          ...prev,
                          suburb: selectedSuburb.name,
                        }));
                        context.setMetadata((prev) => ({
                          ...prev,
                          state: selectedSuburb.state.toLowerCase(),
                        }));
                        context.setMetadata((prev) => ({
                          ...prev,
                          postCode: selectedSuburb.postCode,
                        }));
                      }}
                    />
                  </div>
                  {context.metadata?.country !== "nz" ? (
                    <div className={customDialogstyles.dropdownContainer}>
                      <p className={customDialogstyles.addressHead}>State</p>

                      <Dropdown
                        id="State"
                        name="State"
                        options={[
                          { label: "NSW", value: "nsw" },
                          { label: "ACT", value: "act" },
                          { label: "VIC", value: "vic" },
                          { label: "SA", value: "sa" },
                          { label: "QLD", value: "qld" },
                          { label: "WA", value: "wa" },
                        ]}
                        placeholder=""
                        className="input-placeholder"
                        value={context.metadata?.state}
                        onChange={(e) =>
                          context.setMetadata((prev) => ({
                            ...prev,
                            state: e.target.value,
                          }))
                        } // Update state
                      />
                    </div>
                  ) : (
                    <div className={customDialogstyles.dropdownContainer}>
                      <p className={customDialogstyles.addressHead}>City</p>
                      <InputText
                        id="city"
                        name="city"
                        placeholder=""
                        className="input-placeholder"
                        value={context.metadata?.state}
                        onChange={(e) =>
                          context.setMetadata((prev) => ({
                            ...prev,
                            state: e.target.value.replace(/\s+/g, ""),
                          }))
                        } // Remove spaces before updating state
                      />
                    </div>
                  )}

                  <div
                    className="input-container"
                    style={{
                      marginTop: !isMobile ? "18px" : "0px",
                      maxWidth: "134px",
                      height: "56px",
                    }}
                  >
                    <p className={customDialogstyles.addressHead}>Post code</p>
                    <InputText
                      id="postCode"
                      name="postCode"
                      placeholder=""
                      className="input-placeholder"
                      value={context.metadata?.postCode === null ? "" : context.metadata?.postCode}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        const filteredValue = inputValue.replace(/\D/g, ""); // Allow only digits, remove non-numeric characters
                        context.setMetadata((prev) => ({
                          ...prev,
                          postCode: filteredValue,
                        }));
                      }}
                    />
                  </div>
                </div>

                <div className={customDialogstyles.addAddressDiv}>
                  <div
                    className={`input-container ${customDialogstyles.nicknameCont}`}
                    style={{ marginTop: !isMobile ? "18px" : "60px", width: "100%" }}
                  >
                    <p className={customDialogstyles.addressHead}>Nickname</p>
                    <InputText
                      style={{ width: "100%" }}
                      id="Nickname"
                      name="Nickname"
                      placeholder="For example, Home, Melb Work, Holiday House"
                      className="input-placeholder"
                      value={context.metadata?.nickname}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        const filteredValue = inputValue.trimStart();
                        context.setMetadata((prev) => ({
                          ...prev,
                          nickname: filteredValue,
                        }));
                      }}
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* <Divider className={customDialogstyles.addressDividerSecond} /> */}
        <footer className={isMobile ? styles.saveAddress : customDialogstyles.dialogFooter}>
          <button
            onClick={handleAddressNext} // Submit the form
            className={customDialogstyles.saveAddress}
            disabled={!isFormComplete()}
            // onClick={handleDialogClose}
          >
            Save
          </button>
        </footer>
      </div>
    </CustomDialog>
  );
};

// mobile Components

const JobTypeListMobile = () => {
  const { context } = useJobTypeContext();
  const childcareSettings = GET_CHILDCARE_JOB_TYPE();
  const tutoringSettings = GET_TUTORING_JOB_TYPE();
  const { clientType, canShowJobOptions } = useJobTypeHook();
  const oddJobSettings = GET_ODD_JOB_JOB_TYPE();
  return (
    <>
      {(!context.data?.jobType || childcareSettings.activeValues.includes(context.data?.jobType)) && (
        <ToggleButton
          name="Childcare"
          activeValues={childcareSettings.activeValues}
          icon1={childcareSettings.activeIcon}
          icon2={childcareSettings.inactiveIcon}
          onClick={() =>
            context.setData((prev) => {
              context.setMetadata((prev) => ({
                ...prev,
                subJobTypeSelected: false,
              }));
              if (childcareSettings.activeValues.includes(prev["jobType"])) {
                return { ...prev, jobType: null, jobSubType: 0 };
              }
              return { ...prev, jobType: 1, jobSubType: 0 };
            })
          }
        />
      )}

      {(!context.data?.jobType || tutoringSettings.activeValues.includes(context.data?.jobType)) &&
        context.sessionInfo?.client.clientType !== c.clientType.BUSINESS && (
          <ToggleButton
            name="Tutoring"
            activeValues={tutoringSettings.activeValues}
            icon1={tutoringSettings.activeIcon}
            icon2={tutoringSettings.inactiveIcon}
            onClick={() =>
              context.setData((prev) => {
                context.setMetadata((prev) => ({
                  ...prev,
                  subJobTypeSelected: false,
                }));
                if (tutoringSettings.activeValues.includes(prev["jobType"])) {
                  return { ...prev, jobType: null, jobSubType: 0 };
                }
                return { ...prev, jobType: 64, jobSubType: 0 };
              })
            }
          />
        )}
      {(!context.data?.jobType || oddJobSettings.activeValues.includes(context.data?.jobType)) && (
        <ToggleButton
          name="Odd Jobs"
          activeValues={oddJobSettings.activeValues}
          icon1={oddJobSettings.activeIcon}
          icon2={oddJobSettings.inactiveIcon}
          onClick={() =>
            context.setData((prev) => {
              context.setMetadata((prev) => ({
                ...prev,
                subJobTypeSelected: false,
              }));
              if (oddJobSettings.activeValues.includes(prev["jobType"])) {
                return { ...prev, jobType: null, jobSubType: 0 };
              }
              return {
                ...prev,
                jobType: 256,
                jobSubType: oddJobSettings.subActiveValues[0],
              };
            })
          }
        />
      )}
      {(clientType === "2" || (clientType === "1" && !canShowJobOptions)) && (
        <div
          className="flex"
          style={{
            width: "100%",
            color: "#777777",
            fontSize: "14px",
            fontWeight: "400",
            marginTop: "10px",
          }}
        >
          <p>* Please contact Customer Service if you are trying to post a Primary School or a High School Tutoring Job.</p>
        </div>
      )}
    </>
  );
};

const JobTypeListSubType = () => {
  const { context } = useJobTypeContext();
  const childcareSettings = GET_CHILDCARE_JOB_TYPE();
  const tutoringSettings = GET_TUTORING_JOB_TYPE();
  const oddJobSettings = GET_ODD_JOB_JOB_TYPE();

  if (!context.data?.jobType) return null;
  return (
    <>
      {childcareSettings.activeValues.includes(context.data?.jobType) && (
        <div className="w-full h-min flex flex-column">
          <div className="w-full h-min flex justify-content-center">
            {childcareSettings.subType.map((item, index) => (
              <div key={index} className="flex flex-column gap-1 w-6 px-2">
                <div
                  key={index}
                  className="flex gap-2 align-items-center"
                  onClick={(e) => {
                    e.preventDefault();
                    context.setMetadata((prev) => ({
                      ...prev,
                      subJobTypeSelected: false,
                    }));
                    context.setData((prev) => ({
                      ...prev,
                      jobType: item.value,
                      jobSubType: 0,
                    }));
                  }}
                >
                  <RadioButton selected={item.activeValues.includes(context.data?.jobType)} />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontSize: "16px",
                      fontWeight: "600",
                      color: "#585858",
                    }}
                  >
                    {item.name}
                  </p>
                </div>
                {item.description && (
                  <p
                    className="p-0"
                    style={{
                      fontSize: "14px",
                      fontWeight: "300",
                      color: "#585858",
                      margin: "0px",
                      marginLeft: "3ch",
                    }}
                  >
                    {item.description}
                  </p>
                )}
              </div>
            ))}
          </div>
          {childcareSettings.subType[1].activeValues.includes(context.data?.jobType) && (
            <>
              <Divider className="my-2" />
              <div className="flex flex-column gap-2">
                {childcareSettings?.subType?.[1]?.subType?.map((subItem, index) => {
                  const isSelected = context.metadata?.subJobTypeSelected;
                  const shouldRender = !isSelected || subItem.value === context.data?.jobType;

                  if (!shouldRender) return null;

                  return (
                    <ToggleButton
                      key={index}
                      activeValues={[subItem.value]}
                      icon1={null}
                      icon2={null}
                      name={subItem.name}
                      onClick={() => {
                        context.setData((prev) => ({
                          ...prev,
                          jobType: subItem.value,
                          jobSubType: 0,
                        }));

                        context.setMetadata((prev) => ({
                          ...prev,
                          subJobTypeSelected: true,
                        }));
                      }}
                      differentType
                    />
                  );
                })}
              </div>
              {context.metadata?.subJobTypeSelected && (
                <p
                  className="m-0 my-2 p-0 text-center"
                  style={{
                    fontSize: "14px",
                    fontWeight: "400",
                    color: "#585858",
                    textDecoration: "underline",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    context.setMetadata((prev) => ({
                      ...prev,
                      subJobTypeSelected: false,
                    }));
                  }}
                >
                  Undo
                </p>
              )}
            </>
          )}
          <Divider className="my-3" />
        </div>
      )}

      {tutoringSettings.activeValues.includes(context.data?.jobType) && (
        <div className="w-full h-min flex flex-column">
          <JobTypeSubsectionTutoring settings={tutoringSettings} />
          <Divider className="my-3" />
        </div>
      )}
      {oddJobSettings.activeValues.includes(context.data?.jobType) && (
        <div className="w-full h-min flex flex-column">
          <JobTypeSubsectionOddJobs settings={oddJobSettings} />
          <Divider className="my-3" />
        </div>
      )}
    </>
  );
};

const AddressSectionMobileDropdownTemplate = ({ option }: { option: Address }) => {
  return (
    <div className="flex gap-2 align-items-center">
      <RadioButton selected={false} />
      <p
        className="m-0 p-0"
        style={{
          color: "#585858",
          textWrap:"wrap",
        }}
      >
        {generateAddressLabel(option)}
      </p>
    </div>
  );
};

const AddressSectionMobileDropdownSelected = ({ option }: { option: Address | null }) => {
  if (!option) {
    return <p className="m-0 p-0">Select address</p>;
  }
  return (
    <div className="flex gap-2 align-items-center">
      <p className="m-0 p-0">{generateAddressLabel(option)}</p>
    </div>
  );
};

const AddressSectionMobile = () => {
  const { context } = useJobTypeContext();
  const addresses = (context.sessionInfo?.addresses || []) as Address[];

  return (
    <>
      <AddNewAddressPopup />
      <Dropdown
        className={styles.dropdown}
        value={addresses.find((a) => a.id === context.data?.addressId) || null}
        options={
          context.sessionInfo?.addresses
            ? [
                ...context.sessionInfo.addresses,
                ...(context.sessionInfo?.client.clientCategory !== c.clientCategory.BUSINESS_SINGLE_SITE
                  ? [{ id: -1, suburb: "Alternate Address" }]
                  : []),
              ]
            : []
        }
        valueTemplate={(option) => <AddressSectionMobileDropdownSelected option={option} />}
        itemTemplate={(option) => <AddressSectionMobileDropdownTemplate option={option} />}
        placeholder="Select address"
        data-hasvalue={addresses.find((a) => a.id === context.data?.addressId) ? true : false}
        onChange={(e) => {
          e.preventDefault();
          const value = e.value as Address;
          if (value.id === -1) {
            context.setMetadata((prev) => ({ ...prev, showAddAddressDialog: true }));
            return;
          }
          context.setData((prev) => ({
            ...prev,
            addressId: value.id,
          }));
        }}
        pt={{
          input: {
            className: styles.dinput,
          },
          panel: {
            className: styles.dpanel,
          },
          item: {
            style: {
              background: "white",
            },
          },
        }}
      />
    </>
  );
};
