.familyMembershipContainer {
  width: 100%;
  padding-bottom: 30px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  flex-direction: column;
  user-select: none;
}

.gradientContainer {
  position: absolute;
  top: 0;
  width: 100%;
  height: 207px;
  background: linear-gradient(
    90deg,
    rgba(255, 165, 0, 0.1),
    rgba(55, 169, 80, 0.1)
  );
}
.gradientClipper {
  top: 80px;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: white;
  height: 500px;
  border-radius: 50% 50% 0 0;
  transform: scaleX(1.2);
}

.fmMainContent {
  z-index: 1;
  margin-top: 2%;
  background-color: white;
  border-radius: 20px;
  width: 80%;
  height: min-content;
}

.fmMainContent.upgrade {
  width: 90%;
}

@media (min-width: 400px) {
  .fmMainContent:not(.upgrade) {
    width: 75%;
  }
}
@media (min-width: 540px) {
  .fmMainContent:not(.upgrade) {
    width: 70%;
  }
}
@media (min-width: 768px) {
  .fmMainContent:not(.upgrade) {
    width: 70%;
  }
}
@media (min-width: 820px) {
  .fmMainContent:not(.upgrade) {
    width: 60%;
  }
}
@media (min-width: 1024px) {
  .fmMainContent:not(.upgrade) {
    width: 60%;
  }
}
@media (min-width: 1280px) {
  .fmMainContent:not(.upgrade) {
    width: 50%;
  }
}
