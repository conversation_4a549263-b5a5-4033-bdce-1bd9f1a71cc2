import { ProgressBar } from "primereact/progressbar";
import styles from "../../Helper/styles/introduction.module.css";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { FaFlag } from "react-icons/fa6";
import { FormEvent, useEffect, useRef, useState } from "react";
import RoundCheckImg from "../../../assets/images/Icons/round-check.png";
import useLoader from "../../../hooks/LoaderHook";
import Service from "../../../services/services";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
// import Childcare from "./Childcare";
// import OddJobs from "./OddJobs";
// import Tutoring from "./Tutoring";
// import AuPair from "./AuPair";
import c from "../../../helper/juggleStreetConstants";
import useIsMobile from "../../../hooks/useIsMobile";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";

const Job = ({
  title,
  subTitle,
  value,
  selectedValues,
  setSelectedValues,
  status,
}: {
  title: string;
  subTitle: string;
  value: number;
  selectedValues: number[];
  status: string;
  setSelectedValues: (values: number[]) => void;
}) => {
  const checkboxRef = useRef<HTMLInputElement>(null);

  const handleCheckboxChange = () => {
    if (selectedValues.includes(value)) {
      setSelectedValues(selectedValues.filter((v) => v !== value));
    } else {
      setSelectedValues([...selectedValues, value]);
    }
  };

  const getStatusColor = (status: string) => {
    if (status === 'Complete') return '#179D52';
    if (status === 'Incomplete') return 'red';
    return 'black';
  };

  return (
    <div
      className={`flex align-items-center gap-4 cursor-pointer ${selectedValues.includes(value) ? "selected" : ""
        }`}
      onClick={(e) => {
        e.preventDefault();
        if (checkboxRef.current) {
          checkboxRef.current.click();
        }
      }}
    >

      <input
        ref={checkboxRef}
        type="checkbox"
        name="jobType"
        value={value}
        className="hidden"
        checked={selectedValues.includes(value)}
        onChange={handleCheckboxChange}
      />
      <div
        className="flex justify-content-center align-items-center"
        style={{
          minWidth: "16px",
          maxWidth: "16px",
          minHeight: "16px",
          maxHeight: "16px",
          borderRadius: "50%",
          border: !selectedValues.includes(value)
            ? "1px solid #DFDFDF"
            : undefined,
        }}
      >
        {selectedValues.includes(value) && (
          <img src={RoundCheckImg} width="100%" height="100%" />
        )}
      </div>
      <div>
        <div className="flex flex-wrap gap-3">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            {title}

          </p>
          <p className="m-0 p-0">
            {status && (
              <span style={{ color: getStatusColor(status), width: '14%' }}>{status}</span>
            )}
          </p>
        </div>


        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "14px",
            color: "#585858",
          }}
        >
          {subTitle}
        </p>
      </div>
    </div>
  );
};

const JobList = ({ onChange }: { onChange: (values: number[]) => void }) => {
  const [selectedValues, setSelectedValues] = useState<number[]>([]);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const interestedInChildcareJobs = sessionInfo.data['interestedInChildcareJobs'];
  const interestedInOddJobs = sessionInfo.data['interestedInOddJobs'];
  const interestedInTutoringJobs = sessionInfo.data['interestedInTutoringJobs'];
  const interestedInAuPairJobs = sessionInfo.data['interestedInAuPairJobs'];
  const interestedInJobTypes = sessionInfo.data['interestedInJobTypes'];

  const completedChildCare = () => {
    const completed = (interestedInJobTypes & c.jobType.BABYSITTING
      || interestedInJobTypes & c.jobType.NANNYING
      || interestedInJobTypes & c.jobType.BEFORE_SCHOOL_CARE
      || interestedInJobTypes & c.jobType.AFTER_SCHOOL_CARE) > 0;

    return completed;
  };

  const completedOddJobs = () => {
    return false;
  };

  const completedTutoring = () => {
    const completed = (interestedInJobTypes & c.jobType.PRIMARY_SCHOOL_TUTORING
      || interestedInJobTypes & c.jobType.HIGH_SCHOOL_TUTORING) > 0;
    const requiresVideo = !sessionInfo.data?.['provider'].interestedInOnlineTutoring || sessionInfo.data?.['medias'].length > 0;

    return completed && requiresVideo;
  };

  const completedAuPair = () => {
    const completed = (interestedInJobTypes & c.jobType.AU_PAIR) > 0;
    let nonJsRefs = 0;
    sessionInfo.data?.['vouches'].forEach((v) => {
      if (v.referenceType !== c.vouchReferenceType.juggleStreet) {
        nonJsRefs++;
      }
    });

    return completed && nonJsRefs >= 1;
  };

  const getStatus = (status: boolean | null, jobType: number, selected: boolean) => {
    if (status === null) return '';
    switch (jobType) {
      case 0:
        return completedChildCare() ? 'Complete' : (selected ? 'Incomplete' : '');
      case 1:
        return completedOddJobs() ? 'Complete' : (selected ? 'Incomplete' : '');
      case 2:
        return completedTutoring() ? 'Complete' : (selected ? 'Incomplete' : '');
      case 3:
        return completedAuPair() ? 'Complete' : (selected ? 'Incomplete' : '');
      default:
        return status ? 'Complete' : (selected ? 'Incomplete' : '');
    }
  };

  const jobs = [
    {
      title: "Childcare",
      subTitle: "Babysitting, nannying, before & after school",
      value: 0,
      status: getStatus(interestedInChildcareJobs, 0, selectedValues.includes(0)),
    },
    {
      title: "Odd Jobs",
      subTitle: "Housework, outdoor chores, running errands & casual help for the elderly",
      value: 1,
      status: getStatus(interestedInOddJobs, 1, selectedValues.includes(1)),
    },
    {
      title: "Tutoring",
      subTitle: "In-home and online private lessons for Primary and High School students",
      value: 2,
      status: getStatus(interestedInTutoringJobs, 2, selectedValues.includes(2)),
    },
    {
      title: "Au Pair",
      subTitle: "Live-in with family – provide childcare for accommodation, food & small allowance",
      value: 3,
      status: getStatus(interestedInAuPairJobs, 3, selectedValues.includes(3)),
    },
  ];

  useEffect(() => {
    // Initialize selectedValues based on sessionInfo
    const initialSelectedValues = [];
    if (interestedInChildcareJobs) initialSelectedValues.push(0);
    if (interestedInOddJobs) initialSelectedValues.push(1);
    if (interestedInTutoringJobs) initialSelectedValues.push(2);
    if (interestedInAuPairJobs) initialSelectedValues.push(3);
    setSelectedValues(initialSelectedValues);
  }, [interestedInChildcareJobs, interestedInOddJobs, interestedInTutoringJobs, interestedInAuPairJobs]);


  useEffect(() => {
    onChange(selectedValues);
  }, [selectedValues, onChange]);


  return (
    <div className="flex flex-column gap-2">
      {jobs.map((job, index) => (
        <Job
          key={index}
          title={job.title}
          subTitle={job.subTitle}
          value={job.value}
          selectedValues={selectedValues}
          setSelectedValues={setSelectedValues}
          status={job.status}
        />
      ))}
    </div>
  );
};

const Form = () => {
  const [isValid, setIsValid] = useState<boolean>(false);
  const [jobTypes, setJobTypes] = useState<number[]>([]);
  const [intro, setIntro] = useState<string>("");
  const minCharLimit = 100;
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [showChildcare, setShowChildcare] = useState<boolean>(false);
  const [showOddJobs, setShowOddJobs] = useState<boolean>(false);
  const [showTutoring, setShowTutoring] = useState<boolean>(false);
  const [showAuPair, setShowAuPair] = useState<boolean>(false);
  const { enableLoader, disableLoader } = useLoader();
  const session = useSelector((state: RootState) => state.sessionInfo.data);
  const dispatch = useDispatch<AppDispatch>();
  const {isMobile}=useIsMobile()
  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  function handelSubmit(event: FormEvent<HTMLFormElement>): void {
    event.preventDefault();
    if (!isValid) {
      dispatch(incrementProfileActivationStep());
    }
    enableLoader();
    const payload = {
      ...session,
      aboutMe: intro,
      interestedInChildcareJobs: jobTypes.includes(0),
      interestedInOddJobs: jobTypes.includes(1),
      interestedInTutoringJobs: jobTypes.includes(2),
      interestedInAuPairJobs: jobTypes.includes(3),
    };
    Service.updateSessionInfo(
      payload,
      (newSession) => {
        dispatch(createNewSessionInfo(newSession));
        disableLoader();
        dispatch(incrementProfileActivationStep());
        setShowChildcare(jobTypes.includes(0));
        setShowOddJobs(jobTypes.includes(1));
        setShowTutoring(jobTypes.includes(2));
        setShowAuPair(jobTypes.includes(3));
      },
      (error) => {
        disableLoader();
        
      }
    );
  }

  useEffect(() => {
    if (sessionInfo.data && sessionInfo.data['aboutMe']) {
      setIntro(sessionInfo.data['aboutMe']);
    }
  }, [sessionInfo]);

  const handleReset = (event: FormEvent<HTMLFormElement>): void => {
    event.preventDefault();
    dispatch(decrementProfileActivationStep());
  };

  return (
    <>
      {!showChildcare && !showOddJobs && !showTutoring && !showAuPair && (

        <form
          action="none"
          className="flex flex-column gap-4 pb-6"
          onSubmit={handelSubmit}
          onReset={handleReset}
        >
          <JobList
            onChange={(values) => {
              setJobTypes(values);
              setIsValid(values.length > 0 && intro?.length >= minCharLimit);
            }}
          />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Introduce Yourself
          </p>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: "14px",
              color: "#585858",
              textWrap: "wrap",
            }}
          >
            Tell your neighbours a bit about yourself, but leave out your name and
            age as this will already be shown. This introduction will be displayed
            on your profile card.
          </p>
          <textarea
            name="introduction"
            className={styles.introductionTextArea}
            value={intro}
            placeholder="EXAMPLE -  Hi mums and dad’s, I grew up in a big family with 2 younger brothers and a younger sister, so I have many years of experience looking after children! I know lots of people in the neighbourhood and look forward to meeting you."
            onChange={(e) => {
              e.preventDefault();
              const value = e.target.value;
              setIntro(value);

              setIsValid(jobTypes?.length > 0 && value?.length >= minCharLimit);
            }}
          />
          <p
            style={{
              fontSize: "14px",
              color: intro?.length < minCharLimit ? "red" : "green",
              fontWeight: "400",
            }}
          >
            {intro?.length < minCharLimit &&
              `${minCharLimit - intro?.length} characters remaining`}
          </p>
               {!isMobile ? (
                    <div className="flex justify-content-between">
                    <button
                      className="cursor-pointer"
                      type="reset"
                      style={{
                        appearance: "none",
                        border: "none",
                        backgroundColor: "transparent",
                        fontWeight: "500",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {"<  Previous"}
                    </button>
                    <button
                      className="cursor-pointer transition-all transition-duration-200 transition-ease-in-out"
                      type="submit"
                      style={{
                        appearance: "none",
                        backgroundColor: isValid ? "#FFA500" : "transparent",
                        fontWeight: isValid ? "800" : "500",
                        fontSize: "14px",
                        color: isValid ? "#FFFFFF" : "#585858",
                        border: "1px solid #F0F4F7",
                        borderRadius: "10px",
                        padding: "8px 50px",
                        boxShadow: isValid && "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                      }}
                      onClick={!isValid ? handleSkip : undefined}
                    >
                      {isValid ? "Next >" : "Skip >"}
                    </button>
                  </div>
        ):(
          <div className={`flex justify-content-between  ${isMobile ? `${styles.introbuttonDiv}` : ""}` }>
          <button
            className="cursor-pointer"
            type="reset"
            style={{
              appearance: "none",
              border: "none",
              backgroundColor: "transparent",
              fontWeight: "500",
              fontSize: "14px",
              color: "#585858",
              margin:"10px"
            }}
          >
            {"<  Previous"}
          </button>
          <button
            className="cursor-pointer transition-all transition-duration-200 transition-ease-in-out"
            type="submit"
            style={{
              appearance: "none",
              backgroundColor: isValid ? "#FFA500" : "transparent",
              fontWeight: isValid ? "800" : "500",
              fontSize: "14px",
              color: isValid ? "#FFFFFF" : "#585858",
              border: "1px solid #F0F4F7",
              borderRadius: "10px",
              padding: "8px 50px",
              boxShadow: isValid && "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                margin:"10px"
            }}
            onClick={!isValid ? handleSkip : undefined}
          >
            {isValid ? "Next >" : "Skip >"}
          </button>
        </div>
        )}
        </form>
      )}
      {/* {showChildcare && <Childcare onComplete={() => setShowChildcare(false)} />}
      {showOddJobs && <OddJobs onComplete={() => setShowOddJobs(false)} />}
      {showTutoring && <Tutoring onComplete={() => setShowTutoring(false)} />}
      {showAuPair && <AuPair onComplete={() => setShowAuPair(false)} />} */}
    </>
  );
};

const Introduction = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const {isMobile}=useIsMobile();
  const applicationSate=useSelector<RootState>((state)=>state.applicationState.profileActivationCurrentStep);
  const dispatch = useDispatch<AppDispatch>();
  return (
    <>
     {isMobile && (
       <ProfileCompletenessHeader
       title="Introduction"
       profileCompleteness={sessionInfo.data['profileCompleteness']}
       loading={sessionInfo.loading}
       onBackClick={() => {
   
        if (applicationSate === 1) {
          window.location.href='/';
        } else {
          dispatch(decrementProfileActivationStep());
        }
      }}
  />
     )}
        <div  style={{paddingTop:isMobile && "10px" , paddingInline:isMobile && "15px",width:isMobile && "100%", maxWidth:isMobile && "100%"}} className={styles.introductionContainer}>
      {!isMobile && (
              <header className={styles.introductionHeader}>
              <h1 className="p-0 m-0">Introduction</h1>
              <ProgressBar
                value={
                  sessionInfo.loading ? 100 : sessionInfo.data["profileCompleteness"]
                }
                className={styles.introductionProgressBar}
              />
              <p
                style={{
                  fontFamily: "Poppins",
                  fontWeight: 500,
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                Your profile is{" "}
                <span
                  style={{
                    color: "#179D52",
                    fontSize: "18px",
                    fontWeight: "700",
                  }}
                >
                  {sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]}
                  % complete.
                </span>
              </p>
            </header>
      )}
      <div className="flex flex-column gap-3">
        <div className="flex gap-1 align-items-center">
          <FaFlag color="#585858" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Select the jobs you would like to apply for
          </p>
        </div>
        <Form />
      </div>
    </div>
    </>
  );
};

export default Introduction;
