.profileCardContainer {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: #ffffff;
  padding-inline: 20px;
  transition: all 0.5s ease;
}
.headerGradient {
  background: linear-gradient(90deg, #37a950, #ffa500);
  height: 55px;
  position: fixed;
  top: 0;
  z-index: 1;
}

.juggleLogo {
  object-fit: contain;
  width: 166px;
  height: 25px;

  cursor: pointer;
}
.jobmobile {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 100%;
  height: max-content;
  padding-block: 5px;
  border-radius: 10px;
  font-weight: 500;
  color: #585858;
  font-size: 12px;
  gap: 4px;
  flex-wrap: wrap;
}

.businessTag {
  background-color: rgb(23, 157, 82);
  border-radius: 0.5rem;
  color: white;
  margin-left: 0.3rem;
  margin-top: 0.1rem;
  border: none;
  padding-inline: 0.3rem;
  font-weight: 400;
}
.businessTagMobile {
  background-color: rgb(23, 157, 82);
  border-radius: 0.5rem;
  color: white;
  margin-left: 2.3rem;
  margin-top: 0.1rem;
  border: none;
  padding-inline: 10px;
  font-weight: 300;
}
.navigationBar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: #ffffff;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
}

.navTabs {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.navItem {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 10px;
  transition: all 0.3s ease;
}

.navItem:hover {
  background-color: #f5f5f5;
}

.activeIndicator {
  height: 2px;
  width: 33.33%; /* Adjust based on the number of tabs */
  background-color: #007bff;
  transition: transform 0.3s ease;
}
.tabContainer {
  display: flex;
  justify-content: space-around;
  min-height: 65px;
  background: #227C3C;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-top: 55px;
}
.horizontalnavitemcontainer {
}
.tabItem {
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}
.horizontalNavItem {
  display: flex;
  align-items: center;
  gap: 3px;
  cursor: pointer;
  flex-direction: column;
}
.navLabel{
  font-size: 14px;
  font-weight: 500;
}
.activeLine {
  width: 100%;
  height: 4px;
  background-color: #fff;
  position: absolute;
  bottom: -10px;
  left: 0;
  border-radius: 2px;
}

.horizontalnavitemcontainer {
  position: relative; /* Needed for active line to position correctly */
}
/* parent-home.module.css */
.stylehelpercontainer {
  width: 100%;
  height: 100vh; /* Full viewport height */
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
  background-color: #179D52; /* Green background color */
  display: flex;
  flex-direction: column;
}

.header {
  width: 100%;
  padding: 15px 0;
  background-color: #f5f5f5;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 30px;
}

.curveContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  flex-grow: 1; /* Takes up remaining space */
  align-items: center;
  flex-direction: column;
  gap: 20px;
}

.curveDiv {
  width: 100%;
  max-width: 954px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 90px;
  height: 100%;

}

.curveDivFirst {
  width: 80%;
  padding: 8px;
  max-width: 954px;
  background-color: #fff;
  border-radius: 90px;
 min-height: 618px;
  border: 3px solid #179D52;
}
.continueButton{
  background-color: #FFA500;
  color: #fff;
  border: none;
  padding: 10px 70px;
  font-weight: 700;
  border-radius: 10px;
  cursor: pointer;
}
.continueButton:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}
.smallDiv {
  display: flex ;
      justify-content: center;
}
.idprogressbar {
  width: 100%;
  max-width: 844px;
  height: 0.4375rem;
  margin-top: 1rem;
}

.idprogressbar>div {
  background-color: #179d52 !important;
}

.idprogressbar>div>div {
  display: none;
}
.smallDiv h3 {
  color: #444;
  margin-top: 0;
}

.smallDiv p {
  color: #666;
}

@media (max-width: 880px) {
  .jobsSmall {
      flex-direction: column;
      height: max-content;
      gap: 0;
      align-items: start !important;
  }
}
@media (max-width: 880px) {
  .containerSmall {
    margin-top: 201px !important;
  }
}
