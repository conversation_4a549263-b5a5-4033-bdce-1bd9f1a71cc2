import { Divider } from "primereact/divider";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { PayloadTemplate, useJobManager } from "../../provider/JobManagerProvider";
import { Dialog } from "primereact/dialog";
import ProviderProfilePopup from "../../../../Parent/ProviderProfile/ProviderProfilePopup";
import { FaChevronRight } from "react-icons/fa6";
import { IoStarSharp } from "react-icons/io5";
import { SlLocationPin } from "react-icons/sl";
import Service from "../../../../../services/services";
import resetFiltersgreyImg from "../../../../../assets/images/Icons/reset-grey.png";
import { BiSearchAlt } from "react-icons/bi";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../store";
import { UserSearchResponse } from "../../../../../hooks/SearchGeoSearchHook";
import { SearchFilters } from "../../../../../store/types";
import useLoader from "../../../../../hooks/LoaderHook";
import styles from "../../../styles/candidate-selection.module.css";
import utils from "../../../../../components/utils/util";
import selectcandidates from "../../../../../assets/images/Icons/select-candidates.png";
import userCheckProfile from "../../../../../assets/images/Icons/users-profiles-check.png";
import environment from "../../../../../helper/environment";
import LocationImg from "../../../../../assets/images/Icons/location.png";
import menuIcon from "../../../../../assets/images/Icons/Filter_alt.png";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import resetFiltersImg from "../../../../../assets/images/Icons/reset-filters.png";
import c from "../../../../../helper/juggleStreetConstants";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import { GoBack, Next } from "../Buttons";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";
import { convertArrayToFilters, FilterProvider, useFilterContext } from "../../../../../hooks/FilterManagerHook";
import { PartialFilters as PayloadFilters } from "../../../../../hooks/FilterManagerHook";
import AllFilters from "../../../AllFilters";

const initialFilter: { [key in "childCare" | "childcareRecuring" | "oddJobs" | "tutoring"]: PayloadFilters } = {
  childCare: {
    neighbourhood: null,
    ageGroups: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  childcareRecuring: {
    neighbourhood: null,
    ageGroups: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  oddJobs: {
    neighbourhood: null,
    age: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  tutoring: {
    neighbourhood: null,
    age: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: 0,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 2,
    responseTime: null,
    activity: null,
    tutoringCategory: [1, 2, 3, 4],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
};

const extractOverrideFilters = (payload: Partial<PayloadTemplate>) => {
  return convertArrayToFilters(payload.applicantFilters || []);
};

function getInitialFilters(payload: Partial<PayloadTemplate>) {
  return payload.jobType === 256
    ? initialFilter.oddJobs
    : [64, 128].includes(payload.jobType!)
      ? initialFilter.tutoring
      : payload.jobType === 1
        ? initialFilter.childcareRecuring
        : initialFilter.childCare;
}

interface Candidate {
  id: string;
  publicName: string;
  jobsCompleted: number;
  imageSrc: string;
  suburb: string;
  state: string;
  metadata?: {
    isSuperProvider?: boolean;
    [key: string]: any;
  };
}

type JobCategory = "Childcare" | "Tutoring" | "Odd Jobs";
type Filter = {
  field: string;
  value: any;
  operator: string;
};

function CandidateSelection() {
  const { isMobile } = useIsMobile();
  return <FilterProvider>{isMobile ? <CandidateSelectionMobile /> : <CandidateSelectionWeb />}</FilterProvider>;
}

export default CandidateSelection;

const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const filterDta = (): JobCategory => {
    if (payload.jobType === 256) {
      return "Odd Jobs"; // Return valid JobCategory value
    } else if ([64, 128].includes(payload.jobType)) {
      return "Tutoring"; // Return valid JobCategory value
    } else {
      return "Childcare"; // Return valid JobCategory value
    }
  };
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo.data);
  const { isMobile } = useIsMobile();
  const [selectedCandidates, setSelectedCandidates] = useState<Candidate[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const { defaultFilters } = useSelector((state: RootState) => state.applicationState);
  const [connections, setConnections] = useState<any[]>([]);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [showAllFilters, setShowAllFilters] = useState<boolean>(false);
  const [useSearchResponse, setUseSearchResponse] = useState<UserSearchResponse>(null);
  const [selectedJob, setSelectedJob] = useState<JobCategory>(filterDta());
  const { disableLoader, enableLoader } = useLoader();
  const [currentFilters, setCurrentFilters] = useState<SearchFilters>(defaultFilters);
  const isDisabled = selectedCandidates.length === 0;
  const resetFiltersRef = useRef(null);
  const [searchResult, setSearchResult] = useState<UserSearchResponse>(null);
  const [dropdownEnabled, setDropdownEnabled] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const handleImageClick = (candidate) => {
    setSelectedCandidate(candidate);
    setShowPopup(true);
  };

  const filteredCandidates = useMemo(() => {
    const candidateMap = new Map(); // Create a map to store unique candidates by ID
    // Add all selected candidates to the map first
    selectedCandidates.forEach((candidate) => {
      candidateMap.set(candidate.id, candidate);
    });
    // Add remaining connections that aren't already selected
    connections.forEach((candidate) => {
      if (!candidateMap.has(candidate.id)) {
        candidateMap.set(candidate.id, candidate);
      }
    });
    // Convert map values back to an array
    return Array.from(candidateMap.values());
  }, [selectedCandidates, connections]);

  const [totalCandidates, setTotalCandidates] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [filtersApplied, setFiltersApplied] = useState(false);

  const { updateFilters, resetFilters, update } = useFilterContext({
    initialFilters: getInitialFilters(payload),
    overrideWith: extractOverrideFilters(payload),
    onUpdate: (f, a) => {
      setShowAllFilters(false);
      enableLoader();
      const finalPayload = {
        filters: f,
        pageIndex: currentPage,
        pageSize,
        includeUsers: [],
      };
      Service.getConnections(
        (res) => {
          if (a === "LoadMore") {
            if (currentPage === 1) {
              setConnections(res.results || []);
            } else {
              setConnections((prevConnections) => [...prevConnections, ...(res.results || [])]);
            }
          } else {
            setConnections(res.results || []);
          }

          setUseSearchResponse(res);
          setShowAllFilters(false);
          setTotalCandidates(res.total || 0);
          setHasMoreData(res.results.length >= pageSize);
          if (a === "Update") {
            setFiltersApplied(true);
          }
          setTotalCandidates(res.total || 0);
          const updatedSelectedCandidates = selectedCandidates.filter((selectedCandidate) =>
            res.results.some((candidate) => Number(candidate.id) === Number(selectedCandidate.id))
          );
          setSelectedCandidates(updatedSelectedCandidates);
          disableLoader();
        },
        (error) => {
          console.log(error);
          disableLoader();
        },
        finalPayload
      );
    },
  });

  useEffect(() => {
    if (currentPage && currentPage !== 1) {
      update();
    }
  }, [currentPage]);

  useEffect(() => {
    // Check if in edit mode (actionType === 3) and connections are loaded
    if (payload.actionType  && connections.length > 0) {
      const preSelectedCandidates = getPreSelectedCandidates(connections, payload?.applicants);

      // If pre-selected candidates are found, update the state
      if (preSelectedCandidates.length > 0) {
        setSelectedCandidates(preSelectedCandidates);
      }
    }
  }, [payload.actionType, payload, connections]);

  useEffect(() => {
    if (payload?.["selectedCandidates"]) {
      setSelectedCandidates(payload?.["selectedCandidates"]);
    }
  }, [payload]);
 

  const jobSpecificFilters: Record<JobCategory, Partial<Filter>[]> = {
    Childcare: [
      {
        field: "jobTypes",
        value: Number(payload.jobType) === 12 ? [4, 8] : [Number(payload.jobType)],
        operator: "eq",
      },
      { field: "tutoringCategory", value: [], operator: "eq" },
      // { field: 'jobDeliveryMethod', value: null, operator: 'eq' },
      { field: "year12GraduationYear", value: null, operator: "eq" },
      { field: "name", value: null, operator: "eq" },
      {
        field: "address",
        value: [payload.longitude, payload.latitude],
        operator: "eq",
      },
      {
        field: "hourlyRate",
        value: payload.price,
        operator: "eq",
      },
      {
        field: "otherSkills",
        value: [],
        operator: "eq",
      },
      {
        field: "otherSkills",
        value: [],
        operator: "eq",
      },
      {
        field: "responseTime",
        value: null,
        operator: "eq",
      },
    ],
    "Odd Jobs": [
      { field: "jobTypes", value: [payload.jobType], operator: "eq" },
      {
        field: "jobSubTypes",
        value: [payload.jobSubType],
        operator: "eq",
      },
      { field: "tutoringCategory", value: [], operator: "eq" },
      // { field: 'jobDeliveryMethod', value: null, operator: 'eq' },
      { field: "year12GraduationYear", value: null, operator: "eq" },
      { field: "name", value: null, operator: "eq" },
      {
        field: "address",
        value: [payload.longitude, payload.latitude],
        operator: "eq",
      },
      {
        field: "hourlyRate",
        value: payload.price,
        operator: "eq",
      },
      {
        field: "responseTime",
        value: null,
        operator: "eq",
      },
    ],
    Tutoring: [
      { field: "jobTypes", value: [payload.jobType], operator: "eq" },
      {
        field: "distance",
        value: 2, // Provide a fallback value
        operator: "eq",
      },
      { field: "tutoringCategory", value: [1, 2, 3, 4], operator: "eq" },
      { field: "year12GraduationYear", value: null, operator: "eq" },
      { field: "jobDeliveryMethod", value: Number(payload.jobDeliveryMethod), operator: "eq" },
      { field: "name", value: null, operator: "eq" },
      {
        field: "address",
        value: [payload.longitude, payload.latitude],
        operator: "eq",
      },
      {
        field: "hourlyRate",
        value: payload.price,
        operator: "eq",
      },
      {
        field: "responseTime",
        value: null,
        operator: "eq",
      },
    ],
  };

  const getPreSelectedCandidates = (connections: Candidate[], applicants?: { applicantId: number }[]): Candidate[] => {
    if (!applicants || applicants.length === 0) return [];

    const preSelectedCandidateIds = applicants.map((app) => app.applicantId);
    return connections.filter((candidate) => preSelectedCandidateIds.includes(Number(candidate.id)));
  };

  const handleLoadMore = () => {
    setCurrentPage((prevPage) => prevPage + 1);
  };

  const handleSelect = (candidate: Candidate) => {
    // Check if the candidate is already selected
    if (!selectedCandidates.some((c) => c.id === candidate.id)) {
      // Look for full candidate data in connections array
      const existingCandidate = connections.find((c) => String(c.id) === String(candidate.id));
      const completeCandidate = existingCandidate || candidate;
      setSelectedCandidates([...selectedCandidates, completeCandidate]);
      setSearchTerm("");
      setSearchResult(null);
      setDropdownEnabled(false);
    }
  };
  const handleUnselect = (candidateId: string) => {
    setSelectedCandidates((prevSelected) => prevSelected.filter((candidate) => candidate.id !== candidateId));
  };
  const handleSelectTop10 = () => {
    const topCandidates = payload.jobType !== 1 ? connections.slice(0, 25) : connections.slice(0, 10);
    const filteredCandidates = topCandidates.filter((candidate) => !selectedCandidates.find((c) => c.id === candidate.id));
    setSelectedCandidates([...selectedCandidates, ...filteredCandidates]);
  };

  const handleUnselectAll = () => {
    setSelectedCandidates([]);
  };

  const handleNext = () => {
    setpayload({
      ...payload,
      selectedCandidates,
      applicants: selectedCandidates.map((val) => ({
        applicantId: Number(val.id),
      })),
    });
    if (sessionInfo?.["paymentInfo"]?.paymentType === c.userPaymentType.FREE) {
      next("subscription");
      return;
    }
    if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
      next("job-summary");
    } else {
      next("review-post");
    }
  };

  const handleGoBack = () => {
    enableLoader();
    setpayload({
      ...payload,
      selectedCandidates,
    });
    if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
      prev("pricing-payments-step2");
    } else {
      prev("jobpricing-step3");
    }
    disableLoader();
  };

  const handleGoBackMobile = () => {
    enableLoader();
    setpayload({
      ...payload,
      selectedCandidates,
    });
    if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
      prev("section4-mobile");
    } else {
      prev("overtime-section-mobile");
    }
    disableLoader();
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setSelectedCandidate(null);
  };
  const handleApplyFilters = (filters: PayloadTemplate["applicantFilters"]) => {
    const validKeys = Object.keys(getInitialFilters(payload));
    // Filter out any filters whose `field` is not in the initial keys
    const filtered = filters.filter((f) => validKeys.includes(f.field));
    // Keep the original values as-is and convert
    const updated = convertArrayToFilters(filtered);

    updateFilters(updated);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      ...defaultFilters,
      filters: defaultFilters.filters.map((filter) => {
        switch (filter.field) {
          case "jobTypes":
            return { ...filter, value: [payload.jobType] };
          case "jobSubTypes":
            return { ...filter, value: [payload.jobSubType] };
          // Add more specific cases if needed
          default:
            return filter;
        }
      }),
    };

    setCurrentFilters(resetFilters);
    setFiltersApplied(false); // No filters applied after reset
    Service.getConnections(
      (response: any) => {
        setConnections(response.results || []);
        setUseSearchResponse(response);
      },
      (error: any) => {
        console.error("Failed to fetch connections after resetting filters:", error);
      },
      resetFilters
    );
  };

  const getMobileButtonClass = () => {
    return selectedCandidates.length > 0
      ? `${styles.unselectTopHelperBtnMobile} cursor-pointer`
      : `${styles.selectTopHelperBtnMobile} cursor-pointer`;
  };
  const getMobileButtonText = () => {
    if (selectedCandidates.length > 0) {
      return <>&nbsp; Unselect All</>;
    }
    return (
      <>
        <img alt="Select" src={selectcandidates} style={{ width: "11px", height: "12px", color: "#FFFFFF" }} />
        &nbsp; {payload.jobType !== 1 ? "Select Top 25" : "Select Top 10"} {payload.jobType === 64 || payload.jobType === 128 ? "Tutors" : "Helpers"}
      </>
    );
  };

  const handleMobileTopButton = () => {
    if (selectedCandidates.length > 0) {
      handleUnselectAll();
    } else {
      handleSelectTop10();
    }
  };

  const debounceSearch = utils.debounce(async (searchText) => {
    if (searchText.trim().length < 2) {
      setSearchResult(null);
      setDropdownEnabled(false);
      return;
    }
    const payload = {
      pageindex: 1,
      pageSize: 10,
      sortBy: "experience",
      filters: [
        { field: "publicName", value: searchText, operator: "contains" },
        { field: "jobDeliveryMethod", value: 1, operator: "eq" },
      ],
    };

    const { success, data } = await new Promise<{
      success: boolean;
      data: UserSearchResponse | null;
    }>((res, _) => {
      Service.getConnections(
        (data) => {
          res({ data: data, success: true });
        },
        () => {
          res({ data: null, success: false });
        },
        payload
      );
    });

    if (success && data && data.results.length > 0) {
      const filteredResults = data.results.filter((candidate) => !selectedCandidates.some((c) => String(c.id) === String(candidate.id)));
      const connectionIds = new Set(connections.map((c) => c.id));
      const newConnections = [...connections];
      filteredResults.forEach((result) => {
        if (!connectionIds.has(result.id)) {
          newConnections.push(result);
        }
      });
      setConnections(newConnections);
      setSearchResult({ ...data, results: filteredResults });
      setDropdownEnabled(true);
    } else {
      setSearchResult(null);
      setDropdownEnabled(false);
    }
  }, 300);
  return {
    filterDta,
    handleSelect,
    handleUnselect,
    handleSelectTop10,
    handleUnselectAll,
    handleGoBack,
    handleNext,
    payload,
    next,
    prev,
    setpayload,
    resetFilters,
    isMobile,
    handleImageClick,
    handleApplyFilters,
    handleResetFilters,
    handleMobileTopButton,
    handleClosePopup,
    selectedCandidates,
    setSelectedCandidates,
    connections,
    setConnections,
    showAllFilters,
    setShowAllFilters,
    selectedJob,
    setSelectedJob,
    currentFilters,
    handleLoadMore,
    setCurrentFilters,
    isDisabled,
    resetFiltersRef,
    showPopup,
    setShowPopup,
    selectedCandidate,
    setSelectedCandidate,
    searchTerm,
    setSearchTerm,
    searchResult,
    setSearchResult,
    dropdownEnabled,
    setDropdownEnabled,
    filteredCandidates,
    hoveredCard,
    setHoveredCard,
    totalCandidates,
    setTotalCandidates,
    filtersApplied,
    setFiltersApplied,
    currentPage,
    setCurrentPage,
    pageSize,
    hasMoreData,
    setHasMoreData,
    jobSpecificFilters,
    getPreSelectedCandidates,
    getMobileButtonClass,
    getMobileButtonText,
    useSearchResponse,
    setUseSearchResponse,
    debounceSearch,
    enableLoader,
    disableLoader,
    handleGoBackMobile,
  };
};

const CandidateSelectionWeb = () => {
  const {
    handleSelect,
    handleUnselect,
    handleSelectTop10,
    handleUnselectAll,
    payload,
    handleGoBack,
    handleNext,
    handleImageClick,
    handleApplyFilters,
    handleClosePopup,
    selectedCandidates,
    showAllFilters,
    setShowAllFilters,
    resetFilters,
    selectedJob,
    isDisabled,
    resetFiltersRef,
    showPopup,
    selectedCandidate,
    searchTerm,
    handleLoadMore,
    setSearchTerm,
    searchResult,
    dropdownEnabled,
    filteredCandidates,
    hoveredCard,
    setHoveredCard,
    totalCandidates,
    filtersApplied,
    setFiltersApplied,
    useSearchResponse,
    debounceSearch,
  } = useJobTypeHook();

  return (
    <div>
      <div className="pl-6 h-full pr-6 pb-2 max-w-7xl mx-auto overflow-hidden overflow-y-auto absolute">
        <AllFilters
          availableHelpers={useSearchResponse}
          mode="post-job"
          enable={showAllFilters}
          disableDistance={payload.jobDeliveryMethod === "2"}
          onClose={(payload, actionType) => {
            if (payload && payload.filters) {
              if (actionType !== "reset") {
                handleApplyFilters(payload.filters);
              }
            }
            setShowAllFilters(false);
          }}
          selectedJobCategory={selectedJob}
          onResetRef={(func) => {
            resetFiltersRef.current = func;
          }}
        />
        <div className="flex justify-content-between flex-wrap pt-5">
          <GoBack onClick={handleGoBack} />
          <Next disabled={isDisabled} onClick={handleNext} />
        </div>
        <div className="mb-5 ml-2">
          <h1 className={styles.headerH1}>
            <span className={styles.spanSelect}>{"Select Candidates"}</span>
            <span className={styles.spanFor}>for your job</span>
          </h1>
          <Divider className="mb-3" />
          <div className="flex flex-wrap gap-4 align-items-center ">
            <button className={`${styles.selectTopHelperBtn} cursor-pointer`} onClick={handleSelectTop10}>
              <img alt="Doller" src={userCheckProfile} style={{ width: "18px", height: "18px", color: "#FFFFFF" }} />
              &nbsp; {payload.jobType !== 1 ? "Select Top 25" : "Select Top 10"}{" "}
              {payload.jobType === 64 || payload.jobType === 128 ? "Candidates" : "Candidates"}
            </button>
            <p className="cursor-pointer" style={{ fontSize: "16px", color: "#585858" }} onClick={handleUnselectAll}>
              <u>Unselect All</u>
            </p>
            <div className="relative align-items-center flex ">
              <BiSearchAlt className="absolute ml-2 top-1/1 transform -translate-y-1/2 text-gray-600 " />
              <input
                type="text"
                placeholder="Search by Name"
                className={`${styles.searchInput} pl-10 pr-4 py-2`}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  debounceSearch(e.target.value);
                }}
                style={{ width: "300px" }}
              />
              {searchResult && dropdownEnabled && searchResult.results.length > 0 && (
                <div
                  className="absolute w-full overflow-y-auto"
                  style={{
                    top: "100%",
                    left: "0.45px",
                    transform: "translateY(10px)",
                    zIndex: "5",
                    backgroundColor: "#ffffff",
                    borderRadius: "5px",
                    border: "1px solid #bbbbbb",
                    maxHeight: "15rem",
                    transition: "all .3s ease-in-out",
                  }}
                >
                  {searchResult.results.map((val, index) => (
                    <div
                      key={index}
                      className="grid nested-grid grid-nogutter align-items-center select-none hover:bg-gray-100 cursor-pointer"
                      style={{
                        padding: "5px",
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        handleSelect({
                          id: String(val.id),
                          publicName: val.publicName,
                          jobsCompleted: val.jobsCompleted,
                          imageSrc: val.imageSrc,
                          suburb: val.suburb,
                          state: val.state,
                          metadata: val.metadata || {},
                        });
                      }}
                    >
                      <div className="col-2 flex justify-content-center mb-2">
                        <img
                          style={{
                            height: "50px",
                            width: "50px",
                            borderRadius: "50%",
                          }}
                          src={`${environment.getStorageURL(window.location.hostname)}/images/${val.imageSrc}`}
                          alt="helper Image"
                        />
                      </div>
                      <div className="col-10 mb-2">
                        <div className="grid grid-nogutter ml-2">
                          <div className="col-12 pl-1">
                            <p className="m-0 p-0 font-semibold" style={{ fontSize: "18px", color: "#585858" }}>
                              {val.publicName}
                            </p>
                          </div>
                          <div className="col-1 w-min flex justify-content-center align-items-center"></div>
                          <div className="col-11 flex align-items-center gap-1">
                            <img src={LocationImg} alt="location" width="10.52px" height="12px" />
                            <p className="m-0 p-0" style={{ fontSize: "16px", color: "#585858" }}>
                              {`${val.suburb} - Helper`}
                            </p>
                          </div>
                        </div>
                      </div>
                      <Divider className="col-12" />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* <button className="text-gray-600 border px-4 py-2 rounded-lg flex align-items-center gap-2"> */}
            <button
              className={`${styles.searchAllFilters} py-2 pl-10 pr-4 gap-1 cursor-pointer`}
              style={{ backgroundColor: "transparent" }}
              onClick={() => setShowAllFilters(true)}
            >
              <img alt="Doller" src={menuIcon} style={{ width: "20px", color: "#FFFFFF" }} />
              All Filters
            </button>
            {filtersApplied && (
              <button
                className={`${styles.searchAllFilters} py-2 pl-10 pr-3 gap-1 cursor-pointer font-medium`}
                style={{
                  backgroundColor: "rgba(255, 99, 89, 0.3)",
                  border: "1px solid rgba(255, 99, 89, 1)",
                  color: "rgba(255, 99, 89, 1)",
                  fontSize: "12px",
                }}
                // onClick={handleResetFilters}
                onClick={() => {
                  resetFilters();
                  setFiltersApplied(false);
                  if (resetFiltersRef.current) {
                    resetFiltersRef.current();
                  }
                }}
              >
                <img alt="ResetFilters" src={resetFiltersImg} style={{ width: "20px", color: "#FFFFFF" }} />
                &nbsp; Reset filters
              </button>
            )}
          </div>
          <div className="mt-1">
            <h1 className="p-0 m-0 font-semibold" style={{ color: "#585858", fontSize: "12px" }}>
              * We recommend inviting at least 30 helpers to ensure you get a good shortlist of applicants to choose from.{" "}
              {/* {currentPayload.jobType === 64 || currentPayload.jobType === 128
                            ? 'Tutor'
                            : 'Helper'} */}
            </h1>
          </div>
        </div>
        <div className="pl-2">
          <div className={`${styles.helpersMeetCriteria} bg-white rounded-lg pb-4 shadow`}>
            <div className="flex pl-5">
              <h2 className={`${styles.headerH2} pl-4 text-2xl font-semibold text-gray-700 p-0 m-0 pt-2`}>
                <div className="gap-4 flex align-items-center">
                  <div style={{ color: "#FFA500", fontSize: "30px", fontWeight: "700" }}>
                    {totalCandidates}
                    <span style={{ fontSize: "18px", fontWeight: "500", color: "#585858" }}> Candidates meet your criteria</span>
                  </div>
                  <div style={{ color: selectedCandidates.length === 0 ? "#585858" : "#179D52", fontSize: "30px", fontWeight: "700" }}>
                    {selectedCandidates.length}
                    <span style={{ fontSize: "18px", fontWeight: "500", color: "#585858" }}> Candidates Selected</span>
                  </div>
                </div>
              </h2>
            </div>
            <div className="flex flex-wrap p-4 gap-1">
              {filteredCandidates.length === 0 ? (
                <div className="flex justify-content-center text-gray-500 mt-4">No candidates match the selection criteria</div>
              ) : (
                filteredCandidates.map((candidate) => {
                  const isSelected = selectedCandidates.some((c) => c.id === candidate.id);
                  const isHovered = hoveredCard === candidate.id;
                  return (
                    <div
                      key={candidate.id}
                      className={`${styles.criteriaCard} relative ${isSelected ? styles.criteriaCardSelected : ""} ${isHovered && isSelected ? styles.criteriaCardHovered : ""
                        }`}
                      onMouseEnter={() => setHoveredCard(candidate.id)}
                      onMouseLeave={() => setHoveredCard(null)}
                    >
                      <div className="flex align-items-center ml-1">
                        <img
                          src={`${environment.getStorageURL(window.location.hostname)}/images/${candidate.imageSrc}`}
                          alt={candidate.publicName}
                          className={`${styles.profileImage} cursor-pointer`}
                          onClick={() => handleImageClick(candidate)}
                        />
                        {candidate.metadata?.isSuperProvider && (
                          <div className="absolute w-4 flex justify-content-start pl-" style={{ bottom: "6px" }}>
                            <div
                              className="text-white font-bold"
                              style={{
                                fontSize: "0.4em",
                                backgroundColor: "#444444",
                                padding: "3px 10px",
                                paddingInline: "9px",
                                borderRadius: "30px",
                                userSelect: "none",
                              }}
                            >
                              Super Helper
                            </div>
                          </div>
                        )}
                        <div className="ml-1" style={{ width: "72%" }}>
                          <h3 className={styles.name} style={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
                            {candidate.publicName}
                          </h3>
                          <div className={styles.location} style={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
                            <SlLocationPin color="#37A950" fontSize={"12px"} />
                            {candidate.suburb}
                          </div>
                          <div className={styles.jobs} style={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
                            <IoStarSharp color="#FFA500" fontSize={"12px"} />
                            {candidate.jobsCompleted} Jobs completed
                          </div>
                        </div>
                      </div>

                      <div className="absolute top-1 right-1 " style={{ marginLeft: "70%" }}>
                        {isSelected ? (
                          <>
                            {!isHovered ? (
                              <div className={styles.selectedCheck}>
                                <span>✓</span>
                              </div>
                            ) : (
                              <button
                                onClick={() => handleUnselect(candidate.id)}
                                className={`${styles.selectButton} bg-gray-600 text-white transition-colors`}
                              >
                                Unselect
                              </button>
                            )}
                          </>
                        ) : (
                          <button
                            onClick={() => handleSelect(candidate)}
                            className={`${styles.selectButton} bg-[#FFA500] text-white hover:bg-[#F59E0B] transition-colors`}
                          >
                            <span className="mt-8"></span> Select <FaChevronRight style={{ fontSize: "8px" }} />
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
            {selectedCandidate && (
              <Dialog visible={showPopup} style={{ width: "auto" }} onHide={handleClosePopup} draggable={false}>
                <ProviderProfilePopup candidateId={selectedCandidate.id} requestId={0} />
              </Dialog>
            )}
          </div>
        </div>
        <div className="flex justify-content-center mt-4">
          <button
            className="px-4 py-2 bg-white border-none cursor-pointer underline text-lg font-semibold"
            onClick={() => {
              handleLoadMore();
            }}
            style={{ color: "#585858" }}
          >
            See More
          </button>
        </div>
        <div>
          <div className="flex justify-content-between flex-wrap pb-5 static bottom-0 bg-white" style={{ width: "100%", padding: "0 1rem" }}>
            <Divider className="mt-1 mb-4 w-full" />
            <GoBack onClick={handleGoBack} />
            <Next disabled={isDisabled} onClick={handleNext} />
          </div>
        </div>
      </div>
    </div>
  );
};

const CandidateSelectionMobile = () => {
  const {
    handleSelect,
    handleUnselect,
    handleNext,
    payload,
    handleImageClick,
    handleApplyFilters,
    handleMobileTopButton,
    selectedCandidates,
    connections,
    showAllFilters,
    setShowAllFilters,
    resetFilters,
    handleLoadMore,
    selectedJob,
    isDisabled,
    resetFiltersRef,
    searchTerm,
    setSearchTerm,
    searchResult,
    dropdownEnabled,
    filteredCandidates,
    totalCandidates,
    filtersApplied,
    setFiltersApplied,
    getMobileButtonClass,
    getMobileButtonText,
    useSearchResponse,
    debounceSearch,
    handleGoBackMobile,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        height: "100%",
        width: "100%",
        backgroundColor: "#fff",
        flexDirection: "column",
        position: "relative",
        overflow: "auto",
      }}
    >
      <div className={styles.candidatesearchMobile}>
        <AllFilters
          availableHelpers={useSearchResponse}
          mode="post-job"
          enable={showAllFilters}
          disableDistance={payload.jobDeliveryMethod === "2"}
          onClose={(payload, actionType) => {
            if (payload && payload.filters) {
              if (actionType !== "reset") {
                handleApplyFilters(payload.filters);
              }
            }
            setShowAllFilters(false);
          }}
          selectedJobCategory={selectedJob}
          onResetRef={(func) => {
            resetFiltersRef.current = func;
          }}
        />
        <div className="mb-1" style={{ paddingTop: "10px", width: "100%" }}>
          <h1 className={styles.headerH1Mobile}>
            <span>{payload.jobType === 64 || payload.jobType === 128 ? "Select Tutors" : "Select Candidates"}</span>
            <span> for your job</span>
          </h1>
          <div className="flex flex-wrap gap-3 align-items-center mb-2">
            <button className={getMobileButtonClass()} onClick={handleMobileTopButton}>
              {getMobileButtonText()}
            </button>
            <button
              className={`${styles.searchAllFiltersMobile} py-2 pl-10 pr-4 gap-1 cursor-pointer`}
              style={{ backgroundColor: "transparent" }}
              onClick={() => setShowAllFilters(true)}
            >
              <img alt="Doller" src={menuIcon} style={{ width: "20px", color: "#FFFFFF" }} />
              All Filters
            </button>
            <div className="relative align-items-center flex ">
              <BiSearchAlt className="absolute ml-2 top-1/1 transform -translate-y-1/2 text-gray-600 " />
              <input
                type="text"
                placeholder="Search by Name"
                className={`${styles.searchInputMobile} pl-10 pr-4 py-2`}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  debounceSearch(e.target.value);
                }}
                style={{ width: "300px" }}
              />
              {searchResult && dropdownEnabled && searchResult.results.length > 0 && (
                <div
                  className="absolute w-full overflow-y-auto"
                  style={{
                    top: "100%",
                    left: "0.45px",
                    transform: "translateY(10px)",
                    zIndex: "5",
                    backgroundColor: "#ffffff",
                    borderRadius: "5px",
                    border: "1px solid #bbbbbb",
                    maxHeight: "15rem",
                    transition: "all .3s ease-in-out",
                  }}
                >
                  {searchResult.results.map((val, index) => (
                    <div
                      key={index}
                      className="grid nested-grid grid-nogutter align-items-center select-none hover:bg-gray-100 cursor-pointer"
                      style={{
                        padding: "5px",
                      }}
                      onClick={(e) => {
                        e.preventDefault();

                        handleSelect({
                          id: String(val.id),
                          publicName: val.publicName,
                          jobsCompleted: val.jobsCompleted,
                          imageSrc: val.imageSrc,
                          suburb: val.suburb,
                          state: val.state,
                        });
                      }}
                    >
                      <div className="col-2 flex justify-content-center mb-2 ">
                        <img
                          style={{
                            height: "50px",
                            width: "50px",
                            borderRadius: "50%",
                          }}
                          src={`${environment.getStorageURL(window.location.hostname)}/images/${val.imageSrc}`}
                          alt="helper Image"
                        />
                      </div>
                      <div className="col-10 mb-2">
                        <div className="grid grid-nogutter ml-2">
                          <div className="col-12 pl-1">
                            <p className="m-0 p-0 font-semibold" style={{ fontSize: "18px", color: "#585858" }}>
                              {val.publicName}
                            </p>
                          </div>
                          <div className="col-1 w-min flex justify-content-center align-items-center"></div>
                          <div className="col-11 flex align-items-center gap-1">
                            <img src={LocationImg} alt="location" width="10.52px" height="12px" />
                            <p className="m-0 p-0" style={{ fontSize: "16px", color: "#585858" }}>
                              {`${val.suburb} - Helper`}
                            </p>
                          </div>
                        </div>
                      </div>
                      <Divider className="col-12" />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {filtersApplied && (
              <button
                className={`${styles.searchAllFilters} py-2 pl-10 pr-3 gap-1 cursor-pointer font-medium`}
                style={{
                  backgroundColor: "transparent",
                  border: "1px solid #585858",
                  color: "#585858",
                  fontSize: "12px",
                  width: "80%",
                  display: "flex",
                  justifyContent: "center",
                }}
                onClick={() => {
                  resetFilters();
                  setFiltersApplied(false);
                  if (resetFiltersRef.current) {
                    resetFiltersRef.current();
                  }
                }}
              >
                <img alt="ResetFilters" src={resetFiltersgreyImg} style={{ width: "20px", color: "#FFFFFF" }} />
                &nbsp; Reset filters
              </button>
            )}
          </div>
          <div className=" flex flex-wrap">
            <h1 className="p-0 m-0 font-semibold" style={{ color: "#585858", fontSize: "12px" }}>
              * We recommend inviting at least {payload.jobType !== 1 ? "25" : "10"} helpers to ensure you get a good shortlist of applicants to
              choose from.{" "}
            </h1>
          </div>
        </div>

        <div className={`${styles.helpersMeetCriteriaMobile}`}>
          <div className="flex flex-wrap gap-1">
            {connections.length > 0 ? (
              filteredCandidates.map((candidate) => {
                const isSelected = selectedCandidates.some((c) => c.id === candidate.id);
                return (
                  <div
                    key={candidate.id}
                    className={`${styles.criteriaCardMobile} p-3 px-2 mb-1 flex align-items-center justify-between hover:shadow-md transition-shadow`}
                    style={{
                      border: isSelected ? "2px solid #179D52" : "1px solid #585858",
                    }}
                  >
                    <div className="flex align-items-center">
                      <div className="relative">
                        <img
                          src={`${environment.getStorageURL(window.location.hostname)}/images/${candidate.imageSrc}`}
                          alt={candidate.publicName}
                          className={`${styles.profileImage}`}
                          onClick={() => handleImageClick(candidate)}
                        />
                        {candidate.metadata?.isSuperProvider && (
                          <div className="w-4 flex justify-content-start pl-" style={{ bottom: "6px" }}>
                            <div
                              className="text-white font-bold"
                              style={{
                                fontSize: "0.4em",
                                backgroundColor: "#444444",
                                padding: "3px 10px",
                                paddingInline: "9px",
                                borderRadius: "30px",
                                userSelect: "none",
                                textWrap: "nowrap",
                                position: "absolute",
                                bottom: "0px",
                              }}
                            >
                              Super Helper
                            </div>
                          </div>
                        )}
                      </div>
                      <div className={`${styles.infoMobile}`}>
                        <h3 className={styles.nameMobile}>{candidate.publicName}</h3>
                        <div className={styles.locationMobile}>
                          <SlLocationPin color="#37A950" fontSize="16px" />
                          {candidate.suburb}
                        </div>
                        <div className={styles.jobsMobile}>
                          <IoStarSharp color="#FFA500" fontSize="16px" />
                          {candidate.jobsCompleted} Jobs completed
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => (isSelected ? handleUnselect(candidate.id) : handleSelect(candidate))}
                      className={isSelected ? styles.unSelecttButtonMobile : styles.selectButtonMobile}
                    >
                      {isSelected ? (
                        "Selected ✓"
                      ) : (
                        <>
                          Select <FaChevronRight style={{ height: "10px", width: "10px", fontSize: "12px", fontWeight: "600" }} />
                        </>
                      )}
                    </button>
                  </div>
                );
              })
            ) : (
              <div className="flex justify-content-center">
                <p style={{ fontSize: "16px", color: "#585858" }}>No connections available.</p>
              </div>
            )}
          </div>
          <div className="flex justify-content-center mt-4 mx-auto">
            <button
              className="px-4 py-2 bg-white border-none cursor-pointer underline text-lg font-semibold"
              onClick={() => {
                handleLoadMore();
              }}
              style={{ color: "#585858" }}
            >
              See More
            </button>
          </div>
        </div>
      </div>

      <div style={{ width: "100%", display: "flex", flexDirection: "column" }}>
        <div className={styles.countsDiv}>
          <div className={styles.unselectDiv}>
            <span style={{ color: "#585858", fontSize: "20px", fontWeight: "700" }}>{totalCandidates}&nbsp;</span>
            <span style={{ fontSize: "12px", fontWeight: "500", color: "#585858" }}>
              {payload.jobType === 64 || payload.jobType === 128 ? "Tutors meet your criteria" : "Helpers meet your criteria"}
            </span>
          </div>
          <div className={styles.selectDiv}>
            <span style={{ color: "#fff", fontSize: "20px", fontWeight: "700" }}>{selectedCandidates.length}&nbsp;</span>
            <span
              style={{
                fontSize: "12px",
                fontWeight: "500",
                color: payload.jobType === 64 || payload.jobType === 128 ? "#585858" : "#fff", // Example different color for "Helpers selected"
              }}
            >
              {payload.jobType === 64 || payload.jobType === 128 ? "Tutors selected" : "Helpers selected"}
            </span>
          </div>
        </div>

        <div style={{ width: "100%" }}>
          <BackButtonPortal id="back-button-portal">
            <div onClick={handleGoBackMobile}>
              <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
            </div>
          </BackButtonPortal>

          <CustomFooterButton label="Next" onClick={handleNext} isDisabled={isDisabled} />
        </div>
      </div>
    </div>
  );
};
