.addCardDialog {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  border-radius: 15px;
}

.dialogContent {
  padding: 0;
  background: white;
  border-radius: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeBtn {
  font-size: 24px;
  color: #666;
  cursor: pointer;
  transition: color 0.2s;
}

.closeBtn:hover {
  color: #333;
}

.formContainer {
  padding: 25px;
}

.inputGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.stripeInput {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 15px;
  background: white;
  transition: border-color 0.2s;
}

.stripeInput:focus-within {
  border-color: #179D52;
  box-shadow: 0 0 0 2px rgba(23, 157, 82, 0.1);
}

.rowInputs {
  display: flex;
  gap: 15px;
}

.rowInputs .inputGroup {
  flex: 1;
}

.errorMessage {
  background-color: #ffebee;
  color: #c62828;
  padding: 12px 15px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 20px;
  border-left: 4px solid #c62828;
}

.buttonContainer {
  margin-top: 30px;
}

.addButton {
  background-color: #179D52 !important;
  border: none !important;
  color: white !important;
  width: 100%;
  padding: 14px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 16px;
  transition: background-color 0.2s;
}

.addButton:hover:not(:disabled) {
  background-color: #148a47 !important;
}

.addButton:disabled {
  background-color: #ccc !important;
  cursor: not-allowed;
}

/* Custom Input Styles */
.customInputContainer {
  margin-bottom: 15px;
}

.customInput {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 16px;
  color: #333;
  background: white;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.customInput:focus {
  outline: none;
  border-color: #179D52;
  box-shadow: 0 0 0 2px rgba(23, 157, 82, 0.1);
}

.customInput::placeholder {
  color: #999;
  font-size: 16px;
}

.asterisk {
  color: #c62828;
  font-weight: bold;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .addCardDialog {
    max-width: 100%;
    margin: 0;
  }
  
  .header {
    padding: 15px 20px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .formContainer {
    padding: 20px;
  }
  
  .rowInputs {
    flex-direction: column;
    gap: 20px;
  }
}
