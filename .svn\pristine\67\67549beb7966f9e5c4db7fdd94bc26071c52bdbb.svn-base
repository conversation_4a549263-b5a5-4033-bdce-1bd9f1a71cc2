import { Divider } from "primereact/divider";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import styles from "../../../Common/styles/post-job-step-2.module.css";
import { useRef, useState } from "react";
import c from "../../../../helper/juggleStreetConstants";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import { GoBack, Next } from "./Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";
function JobPosting() {
  const { isMobile } = useIsMobile();
  return isMobile ? <JobPostingMobile /> : <JobPostingWeb />;
}

export default JobPosting;
const useJobTypeHook = () => {
  const { next, payload, prev, setpayload } = useJobManager();
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const [selectedButton, setSelectedButton] = useState<string | null>(
    payload.managedBy === c.managedBy.USER ? "diy" : payload.managedBy === c.managedBy.SYSTEM ? "assist" : null
  );

  const handleButtonClick = (buttonName: string) => {
    if (selectedButton === buttonName) {
      setSelectedButton(null);
    } else {
      setSelectedButton(buttonName);
    }
  };

  return {
    buttonContainerRef,
    handleButtonClick,
    selectedButton,
    next,
    payload,
    prev,
    setpayload,
  };
};

const JobPostingWeb = () => {
  const { buttonContainerRef, handleButtonClick, selectedButton, next, payload, prev, setpayload } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        width: "100%",
        height: "100%",
        flexDirection: "column",
      }}
    >
      <div className={styles.containerPostJob}>
        <header className={styles.headerPost}>
          <h1 className={styles.headerPostJob}>Candidate Selection & Job Posting</h1>
          <p className={styles.instructionPostJob}>
            Before defining & describing your job please choose your <br />
            candidate selection & job posting process
          </p>
        </header>

        <main className={styles.mainContentPostJobs}>
          <div className={styles.buttonContainer} ref={buttonContainerRef}>
            <button
              className={`${styles.buttonPostJob} ${selectedButton === "diy" ? styles.selectedButton : ""}`}
              onClick={() => handleButtonClick("diy")}
            >
              <h1 className={`${styles.buttonPostJobHead} ${selectedButton === "diy" ? styles.selectedText : ""}`}>Do it Yourself</h1>
              <p className={styles.instructionPostJobText}>
                DIY - create job, use filters to narrow your search and select helpers to invite to your job post.
              </p>
            </button>
            <button
              className={`${styles.buttonPostJob} ${selectedButton === "assist" ? styles.selectedButton : ""}`}
              onClick={() => handleButtonClick("assist")}
            >
              <h1 className={`${styles.buttonPostJobHead} ${selectedButton === "assist" ? styles.selectedText : ""}`}>Juggle Assist</h1>
              <p className={styles.instructionPostJobText}>
                Streamlined Selection - Juggle Assist algorithm matches helpers to your requirements and creates a Shortlist.
              </p>
            </button>
          </div>
        </main>
        <br />
      </div>
      <footer className={`${styles.footerPostJob} relative`}>
        <br />
        <Divider className={styles.dividerPosJob} />
        <div className="flex flex-column justify-content-center align-items-center">
          <div style={{ maxWidth: "70%" }} className="w-full flex justify-content-between mt-2 sticky bottom-0 py-3 ">
            <GoBack
              onClick={() => {
                setpayload({
                  ...payload,
                  managedBy: selectedButton === "diy" ? c.managedBy.USER : selectedButton === "assist" ? c.managedBy.SYSTEM : null,
                });
                prev("job-type");
              }}
            />
            <Next
              disabled={selectedButton === null}
              onClick={() => {
                setpayload({
                  ...payload,
                  managedBy: selectedButton === "diy" ? c.managedBy.USER : selectedButton === "assist" ? c.managedBy.SYSTEM : null,
                });

                // Conditional navigation based on jobType
                if ([1, 256].includes(payload.jobType)) {
                  next("job-details");
                } else if ([2, 4, 8, 12].includes(payload.jobType)) {
                  next("day-and-schedule");
                } else if ([64, 128].includes(payload.jobType)) {
                  next("typeand-years");
                }
              }}
            />
          </div>
        </div>
      </footer>
    </div>
  );
};

const JobPostingMobile = () => {
  const { buttonContainerRef, handleButtonClick, selectedButton, next, payload, prev, setpayload } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        width: "100%",
        height: "100%",
      }}
    >
      <div className={styles.containerPostJobMobile}>
        <header style={{ paddingInline: "20px", width: "100%" }} className={styles.headerPostMobile}>
          <h1 className={styles.headerPostJobMobile}>Candidate Selection & Job Posting</h1>
          <p className={styles.instructionPostJobMobile}>
            Before defining & describing your job please choose your candidate selection & job posting process
          </p>
        </header>

        <main className={styles.mainContentPostJobsMobile}>
          <div style={{ paddingInline: "20px" }} className={styles.buttonContainer} ref={buttonContainerRef}>
            <button
              className={`${styles.buttonPostJobMobile} ${selectedButton === "diy" ? styles.selectedButtonMobile : ""}`}
              onClick={() => handleButtonClick("diy")}
            >
              <h1 className={`${styles.buttonPostJobHead} ${selectedButton === "diy" ? styles.selectedText : ""}`}>Do it Yourself</h1>
              <p className={styles.instructionPostJobText}>
                DIY -create job, use filters to narrow your search and select helpers to invite to your job post.
              </p>
            </button>
            <button
              className={`${styles.buttonPostJobMobile} ${selectedButton === "assist" ? styles.selectedButtonMobile : ""}`}
              onClick={() => handleButtonClick("assist")}
            >
              <h1 className={`${styles.buttonPostJobHead} ${selectedButton === "assist" ? styles.selectedText : ""}`}>Juggle Assist</h1>
              <p className={styles.instructionPostJobText}>
                Streamlined Selection - Juggle Assist algorithm matches helpers to your requirements and creates a Shortlist
              </p>
            </button>
          </div>
        </main>
        <br />

        <BackButtonPortal id="back-button-portal">
          <div
            onClick={() => {
              setpayload({
                ...payload,
                managedBy: selectedButton === "diy" ? c.managedBy.USER : selectedButton === "assist" ? c.managedBy.SYSTEM : null,
              });
              prev("job-type");
            }}
          >
            <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
          </div>
        </BackButtonPortal>

        <CustomFooterButton
          label="Next"
          isDisabled={selectedButton === null} // Disable button if no selection is made
          onClick={() => {
            
            setpayload({
              ...payload,
              managedBy: selectedButton === "diy" ? c.managedBy.USER : selectedButton === "assist" ? c.managedBy.SYSTEM : null,
            });
            if ([1, 256].includes(payload.jobType)) {
              next("job-details");
            } else if ([2, 4, 8, 12].includes(payload.jobType)) {
              next("day-and-schedule");
            } else if ([64, 128].includes(payload.jobType)) {
              next("typeand-years");
            }
          }}
        />
      </div>
    </div>
  );
};
