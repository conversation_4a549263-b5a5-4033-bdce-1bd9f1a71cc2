import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from '../styles/my-family.module.css';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import useLoader from '../../../hooks/LoaderHook';
import OutlineButton from '../../../commonComponents/OutlineButton';
import { InputTextarea } from 'primereact/inputtextarea';
import c from '../../../helper/juggleStreetConstants';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import useIsMobile from '../../../hooks/useIsMobile';
const Tutoring = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [tutorRadioState, settutorRadioState] = useState('no');
  const [tutoringError, setTutoringError] = useState<boolean>(false);
  const [interestedInFaceToFaceTutoring, setInterestedInFaceToFaceTutoring] = useState<boolean>(false);
  const [interestedInOnlineTutoring, setInterestedInOnlineTutoring] = useState<boolean>(false);
  const [primaryHighSchoolError, setPrimaryHighSchoolError] = useState<boolean>(false);
  const [primarySchool, setPrimarySchool] = useState<boolean>(false);
  const [highSchool, setHighSchool] = useState<boolean>(false);
  const [secondarySchool, setSecondarySchool] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [_, setQualificationCheckedState] = useState<boolean[]>([]);
  const [radioState, setRadioState] = useState<string>('');
  const [categoryError, setCategoryError] = useState<boolean>(false);
  const [subjectGroups, setSubjectGroups] = useState(sessionInfo.data["provider"]["primarySchoolSubjects"]);
  const [highSchoolSubjects, setHighSchoolSubjects] = useState(sessionInfo.data["provider"]["highSchoolSubjects"]);
  const [subjectError, setSubjectError] = useState<boolean>(false);
  const [highSchoolSubjectError, sethighSchoolSubjectError] = useState<boolean>(false);
  const [ageGroupError, setAgeGroupError] = useState<boolean>(false);
  const [highSchoolAgeGroupError, sethighSchoolAgeGroupError] = useState<boolean>(false);
  const [highSchoolYearError, sethighSchoolYearError] = useState<boolean>(false);
  const [canTeachstudyskillsError, setcanTeachstudyskillsError] = useState<boolean>(false);
  const {isMobile}=useIsMobile();
  const [exampreparationError, setexampreparationError] = useState<boolean>(false);
  const [ageGroups, setAgeGroups] = useState(sessionInfo.data["provider"]["primarySchoolYears"]);
  const [ageGroupsHighSchool, setAgeGroupsHighSchool] = useState(sessionInfo.data["provider"]["highSchoolYears"]);
  const [myExperience, setMyExperience] = useState<string>('');
  // const [canTeachStudySkills, setCanTeachStudySkills] = useState<boolean | null>(sessionInfo.data["provider"]["canTeachStudySkills"] || null);
  const [canTeachStudySkills, setCanTeachStudySkills] = useState<boolean | null>(
    sessionInfo.data["provider"]["canTeachStudySkills"] ?? null
  );
  // const [canTeachExamPreparation, setCanTeachExamPreparation] = useState<boolean | null>(sessionInfo.data["provider"]["canTeachExamPreparation"] || null);
  const [canTeachExamPreparation, setCanTeachExamPreparation] = useState<boolean | null>(
    sessionInfo.data["provider"]["canTeachExamPreparation"] ?? null
  );
  const [graduationYear, setGraduationYear] = useState<number | null>(sessionInfo.data["provider"]["year12GraduationYear"] || null);
  const [selectedJobTypes, setSelectedJobTypes] = useState<number[]>([]);
  const [checkedState, setCheckedState] = useState<boolean[]>(new Array(c.childcareJobTypes.length).fill(false));
  const { enableLoader, disableLoader } = useLoader();
  const minCharLimit = 100;
  const tutoringLabels = [
    {
      label: "In-home face-to-face Tutoring",
      value: "interestedInFaceToFaceTutoring",
      state: interestedInFaceToFaceTutoring,
      setState: setInterestedInFaceToFaceTutoring
    },
    {
      label: "Online Video Tutoring",
      value: "interestedInOnlineTutoring",
      state: interestedInOnlineTutoring,
      setState: setInterestedInOnlineTutoring
    }
  ];
  const primaryHighSchoolLabels = [
    {
      label: "Primary School",
      value: "primarySchool",
      state: primarySchool,
      setState: setPrimarySchool,
      jobType: c.jobType.PRIMARY_SCHOOL_TUTORING
    },
    ...(sessionInfo.data['country'] === 'au' ? [{
      label: "High School",
      value: "highSchool",
      state: highSchool,
      setState: setHighSchool,
      jobType: c.jobType.HIGH_SCHOOL_TUTORING
    }] : [{
      label: "Secondary School",
      value: "highSchool",
      state: secondarySchool,
      setState: setSecondarySchool,
      jobType: c.jobType.HIGH_SCHOOL_TUTORING
    }])
  ];
  const tutoringSkills = [
    {
      label: "Newbie",
      description: "No tutoring experience",
      value: 1
    },
    {
      label: "Apprentice",
      description: "Some previous tutoring experience, mainly unpaid for family & friends",
      value: 2
    },
    {
      label: "Experienced",
      description: "Received payment for tutoring multiple times",
      description2: "Studying to be a teacher",
      value: 3
    },
    {
      label: "Professional",
      description: "Established tutor with deep academic understanding",
      description2: "Qualified teacher",
      description3: "Certified tutor",
      value: 4

    },
  ]
  const handleCategoryChange = (value: number) => {
    setSelectedCategory(value);
    setCategoryError(false);
  };
  const handleTutoringCheckboxChange = (setState: React.Dispatch<React.SetStateAction<boolean>>, value: boolean) => {
    setState(value);
    setTutoringError(false);
  };
  const primarySchoolTutoring = () => {
    return (sessionInfo.data['interestedInJobTypes'] & c.jobType.PRIMARY_SCHOOL_TUTORING) === c.jobType.PRIMARY_SCHOOL_TUTORING;
  };
  const highSchoolTutoring = () => {
    return (sessionInfo.data['interestedInJobTypes'] & c.jobType.HIGH_SCHOOL_TUTORING) === c.jobType.HIGH_SCHOOL_TUTORING;
  };
  useEffect(() => {
    setPrimarySchool(primarySchoolTutoring());
    setHighSchool(highSchoolTutoring());
    setSelectedJobTypes(() => {
      const selected = [];
      if (primarySchoolTutoring()) selected.push(c.jobType.PRIMARY_SCHOOL_TUTORING);
      if (highSchoolTutoring()) selected.push(c.jobType.HIGH_SCHOOL_TUTORING);
      return selected;
    });
  }, []);


  useEffect(() => {
    if (sessionInfo.data["provider"]) {
      setInterestedInFaceToFaceTutoring(sessionInfo.data["provider"].interestedInFaceToFaceTutoring ?? null);
      setInterestedInOnlineTutoring(sessionInfo.data["provider"].interestedInOnlineTutoring ?? null);
      // setPrimarySchool(sessionInfo.data["provider"].primarySchool || false);
      // setHighSchool(sessionInfo.data["provider"].highSchool || false);
      setSecondarySchool(sessionInfo.data["provider"].secondarySchool || false);
      setSelectedCategory(sessionInfo.data["provider"].tutoringCategory || null);
      setRadioState(sessionInfo.data["provider"].tutoringQualifications.some(q => q.selected) ? 'yes' : 'no');
      setQualificationCheckedState(
        sessionInfo.data["provider"].tutoringQualifications.map((qualification) => qualification.selected || false)
      );
      // const initialSelectedJobTypes: number[] = [];
      // if (sessionInfo.data["provider"].primarySchool) initialSelectedJobTypes.push(c.jobType.PRIMARY_SCHOOL_TUTORING);
      // if (sessionInfo.data["provider"].highSchool) initialSelectedJobTypes.push(c.jobType.HIGH_SCHOOL_TUTORING);
      // setSelectedJobTypes(initialSelectedJobTypes);
      setMyExperience(sessionInfo.data["provider"].myExperience3 || '');
      setCanTeachStudySkills(sessionInfo.data["provider"].canTeachStudySkills ?? null);
      setCanTeachExamPreparation(sessionInfo.data["provider"].canTeachExamPreparation ?? null);
    }
  }, [sessionInfo]);
  useEffect(() => {
    if (sessionInfo.data['interestedInJobTypes']) {
      // Load previous selections using bit operations
      const previousSelections = c.childcareJobTypes.map(jobType =>
        !!(sessionInfo.data['interestedInJobTypes'] & jobType.value)
      );
      setCheckedState(previousSelections);
    }
  }, [sessionInfo.data]);
  useEffect(() => {
    setSubjectGroups(subjectGroups.map(group => ({
      ...group,
      isOpen: group.text === "Academic" ? true : group.isOpen
    })));
  }, []);
  useEffect(() => {
    setHighSchoolSubjects(highSchoolSubjects.map(group => ({
      ...group,
      isOpen: group.text === "Academic",
      children: group.children.map(subject => ({
        ...subject,
        isOpen: group.text === "Academic" && subject.text === "English",
        children: subject.children || []
      }))
    })));
  }, []);
  useEffect(() => {
    if (sessionInfo.data['interestedInJobTypes']) {
      const previousSelections = c.childcareJobTypes.map(jobType =>
        !!(sessionInfo.data['interestedInJobTypes'] & jobType.value)
      ).reduce((acc, selected, index) => {
        if (selected) acc.push(c.childcareJobTypes[index].value);
        return acc;
      }, [] as number[]);
      setSelectedJobTypes(prev => [...prev, ...previousSelections]);
    }
  }, [sessionInfo.data['interestedInJobTypes']]);

  const calculateInterestedInJobTypes = () => {
    return selectedJobTypes.reduce((sum, jobType) => sum | jobType, 0);
  };
  const handlePrimaryHighSchoolCheckboxChange = (setState: React.Dispatch<React.SetStateAction<boolean>>, value: boolean, jobType: number) => {
    setState(value);
    setPrimaryHighSchoolError(false);

    setSelectedJobTypes(prevSelectedJobTypes => {
      if (value) {
        return [...prevSelectedJobTypes, jobType];
      } else {
        return prevSelectedJobTypes.filter(type => type !== jobType);
      }
    });
  };
  const handleSave = () => {
    let hasError = false;
    const interestedInJobTypes = calculateInterestedInJobTypes();
    const selectedAgeGroups = ageGroups.some(group => group.selected);
    const selectedSubjects = subjectGroups.some(group =>
      group.children.some(subject => subject.selected)
    );
    const selectedhighSchoolAgeGroups = ageGroupsHighSchool.some(group => group.selected);
    // const selectedhighSchoolSubjects = highSchoolSubjects.some(group =>
    //   group.children.some(subject => subject.selected)
    // );

    const selectedhighSchoolSubjects = highSchoolSubjects.some(group =>
      group.children?.some(child =>
        child.children?.some(subject => subject.selected) || child.selected // Handle cases where there are no children
      ) || group.selected // Check the group itself if no children are present
    );

    if (!interestedInFaceToFaceTutoring && !interestedInOnlineTutoring) {
      setTutoringError(true);
      hasError = true;
    }

    if (!primarySchool && !highSchool && !secondarySchool) {
      setPrimaryHighSchoolError(true);
      hasError = true;
    }
    if (primarySchool) {
      if (selectedCategory === null) {
        setCategoryError(true);
        hasError = true;
      }
      if (!selectedAgeGroups) {
        setAgeGroupError(true);
        hasError = true;
      }
      if (!selectedSubjects) {
        setSubjectError(true);
        hasError = true;
      }
    }
    if (highSchool) {
      if (selectedCategory === null) {
        setCategoryError(true);
        hasError = true;
      }
      if (!selectedhighSchoolSubjects) {
        sethighSchoolSubjectError(true);
        hasError = true;
      } else {
        sethighSchoolSubjectError(false);
      }
      if (!selectedhighSchoolAgeGroups) {
        sethighSchoolAgeGroupError(true);
        hasError = true;
      }
      if (canTeachStudySkills === null) {
        setcanTeachstudyskillsError(true);
        hasError = true;
      } else {
        setcanTeachstudyskillsError(false);
      }
      if (canTeachExamPreparation === null) {
        setexampreparationError(true);
        hasError = true;
      } else {
        setexampreparationError(false);
      }
      if (!graduationYear) {
        sethighSchoolYearError(true);
        hasError = true;
      } else {
        sethighSchoolYearError(false);
      }
    }
    if (myExperience.length < minCharLimit) {
      hasError = true;
    }
    if (hasError) {
      return;
    }
    enableLoader();
    const payload = {
      ...sessionInfo.data,
      provider: {
        ...sessionInfo.data["provider"],
        interestedInFaceToFaceTutoring,
        interestedInOnlineTutoring,
        tutoringCategory: selectedCategory,
        primarySchoolYears: ageGroups,
        primarySchoolSubjects: subjectGroups,
        myExperience3: myExperience,
        highSchoolYears: ageGroupsHighSchool,
        canTeachStudySkills: canTeachStudySkills,
        canTeachExamPreparation: canTeachExamPreparation,
        year12GraduationYear: graduationYear,
        highSchoolSubjects: highSchoolSubjects,
      },
      interestedInJobTypes: interestedInJobTypes,
    };
    dispatch(updateSessionInfo({ payload })).finally(() => {
      disableLoader();
    });
  };
  useEffect(() => {
    if (sessionInfo?.data['interestedInTutoringJobs']) {
      settutorRadioState("yes");
    }
  }, [sessionInfo]);
  const handleTutorRadioChange = (value: string) => {
    settutorRadioState(value);
  };
  const toggleSubject = (groupIndex: number, subjectIndex: number) => {
    const updatedSubjectGroups = subjectGroups.map((group, i) => ({
      ...group,
      children: group.children.map((subject, j) => ({
        ...subject,
        selected: i === groupIndex && j === subjectIndex ? !subject.selected : subject.selected,
      })),
    }));
    setSubjectGroups(updatedSubjectGroups);
    setSubjectError(false);
  };
  const toggleAgeGroup = (index: number) => {
    const updatedAgeGroups = ageGroups.map((group, i) => ({
      ...group,
      selected: i === index ? !group.selected : group.selected,
    }));
    setAgeGroups(updatedAgeGroups);
    setAgeGroupError(false);
  };
  const togglehighSchoolAgeGroup = (index: number) => {
    const updatedhighSchoolAgeGroups = ageGroupsHighSchool.map((group, i) => ({
      ...group,
      selected: i === index ? !group.selected : group.selected,
    }));
    setAgeGroupsHighSchool(updatedhighSchoolAgeGroups);
    sethighSchoolAgeGroupError(false);
  };
  const toggleGroup = (groupIndex: number) => {
    setSubjectGroups(subjectGroups.map((group, i) => ({
      ...group,
      isOpen: i === groupIndex ? !group.isOpen : group.isOpen,
    })));
  };
  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMyExperience(event.target.value);

  };
  const handleRadioChange = (setter: React.Dispatch<React.SetStateAction<boolean | null>>, value: boolean) => {
    setter(value);
    sessionInfo.data["provider"]["canTeachStudySkills"] = value;
    sessionInfo.data["provider"]["canTeachExamPreparation"] = value;
  };

  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = 1960; year <= currentYear; year++) {
      years.push(year);
    }
    return years;
  };
  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setGraduationYear(parseInt(e.target.value, 10));
  };
  useEffect(() => {
    setHighSchoolSubjects(highSchoolSubjects.map(group => ({
      ...group,
      isOpen: group.text === "Academic",
      children: group.children.map(subject => ({
        ...subject,
        isOpen: group.text === "Academic" && subject.text === "English",
        children: subject.children || []
      }))
    })));
  }, []);
  const highSchooltoggleGroup = (groupIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => ({
      ...group,
      // Close all other groups when opening a new one
      isOpen: i === groupIndex ? !group.isOpen : false,
      children: group.children.map(subject => ({
        ...subject,
        // Close all subjects when closing a group
        isOpen: i === groupIndex ? subject.isOpen : false
      }))
    })));
  };
  const highSchooltoggleSubSubject = (groupIndex: number, subjectIndex: number, subSubjectIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => {
      if (group.text === "Academic" && i === groupIndex) {
        return {
          ...group,
          children: group.children.map((subject, j) => {
            if (j === subjectIndex) {
              return {
                ...subject,
                children: subject.children.map((subSubject, k) => ({
                  ...subSubject,
                  selected: k === subSubjectIndex ? !subSubject.selected : subSubject.selected,
                })),
              };
            }
            return subject;
          }),
        };
      }
      return group;
    }));
    sethighSchoolSubjectError(false);
  };
  const highSchooltoggleSubject = (groupIndex: number, subjectIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => {
      if (group.text === "Academic") {
        // For Academic group, handle opening/closing of subjects
        if (i === groupIndex) {
          return {
            ...group,
            children: group.children.map((subject, j) => ({
              ...subject,
              isOpen: j === subjectIndex ? !subject.isOpen : false
            }))
          };
        }
        return group;
      } else {
        // For non-Academic groups, handle checkbox selection
        if (i === groupIndex) {
          return {
            ...group,
            children: group.children.map((subject, j) => ({
              ...subject,
              selected: j === subjectIndex ? !subject.selected : subject.selected
            }))
          };
        }
        return group;
      }
    }));
    sethighSchoolSubjectError(false);
  };
  const highSchoolrenderSubjects = (group, groupIndex) => {
    if (group.text === "Academic") {
      return group.children.map((subject, subjectIndex) => (
        <div key={subject.name} className="border-l-2 border-gray-200 pl-4">
          <div
            className="flex items-center gap-2 mb-1 cursor-pointer"
            onClick={() => highSchooltoggleSubject(groupIndex, subjectIndex)}
          >
            <span
              className="transition-transform duration-200"
              style={{
                transform: subject.isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                color: '#179D52'
              }}
            >
              ▼
            </span>
            <span className="txt-clr font-semibold">{subject.text}</span>
          </div>
          {subject.isOpen && (
            <div className="space-y-2 ml-4">
              {subject.children.map((subSubject, subSubjectIndex) => (
                <label
                  key={subSubject.name}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={subSubject.selected || false}
                    onChange={() => highSchooltoggleSubSubject(groupIndex, subjectIndex, subSubjectIndex)}
                    className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                  />
                  <span className="txt-clr font-medium">{subSubject.text}</span>
                </label>
              ))}
            </div>
          )}
        </div>
      ));
    } else {
      // For non-Academic groups, render simple checkboxes
      return group.children.map((subject, subjectIndex) => (
        <label
          key={subject.name}
          className="flex items-center gap-2 cursor-pointer ml-4"
        >
          <input
            type="checkbox"
            checked={subject.selected || false}
            onChange={() => highSchooltoggleSubject(groupIndex, subjectIndex)}
            className={`${styles.customCheckbox} txt-clr cursor-pointer`}
          />
          <span className="txt-clr font-medium">{subject.text}</span>
        </label>
      ));
    }
  };
  const isAnyCheckboxSelected = tutoringLabels.some((item) => item.state);
  return (
    <div className='flex flex-column p-4' style={{color:'#585858'}}>
      <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
        <header className={styles.utilheader}>
          <h1 style={{fontSize:isMobile && "24px"}}  className="p-0 m-0">Tutoring</h1>
        </header>
        <CustomButton
          label="Save"
          className={myfamilystyles.customButton}
          style={{ margin: '0', width: '150px' }}
          onClick={handleSave}
        />
      </div>
      <div>
        <h1
          className="m-0 p-1 txt-clr font-medium line-height-1"
          style={{ fontSize: '16px', color: '#179d52' }}
        >
          Would you like to work as a Tutor?
        </h1>
      </div>
      <div>
        <div className="flex justify-content-start items-center mt-3 gap-2">
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="language-radio"
              value="yes"
              checked={tutorRadioState === 'yes'}
              onChange={() => handleTutorRadioChange('yes')}
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="language-radio"
              value="no"
              checked={tutorRadioState === 'no'}
              onChange={() => handleTutorRadioChange('no')}
              className="cursor-pointer"
            />
            No
          </label>
        </div>
      </div>
      <div>
        {tutorRadioState === 'yes' && (
          <>
            <h1
              className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
              style={{ fontSize: '16px', color: tutoringError ? 'red' : '#179d52' }}
            >
              Select one or both types of Tutoring
            </h1>
            <div className='flex flex-wrap justify-center mt-2'>
              {
                tutoringLabels.map((item, index) => (
                  <label key={index} className=" flex  gap-2 p-1 cursor-pointer">
                    <input
                      type="checkbox"
                      className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                      style={{ fontSize: '18px' }}
                      checked={item.state}
                      onChange={(e) => handleTutoringCheckboxChange(item.setState, e.target.checked)}
                    />
                    <span className="text-base">
                      {item.label}
                    </span>

                  </label>
                ))
              }
            </div>
            {isAnyCheckboxSelected && (
              <>
                <h1
                  className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                  style={{ fontSize: '16px', color: primaryHighSchoolError ? 'red' : '#179d52' }}
                >
                  Select the jobs you would like to apply for
                </h1>
                <div className='flex flex-wrap justify-center mt-2'>
                  {
                    primaryHighSchoolLabels.map((item, index) => (
                      <label key={index} className=" flex  gap-2 p-1 cursor-pointer">
                        <input
                          type="checkbox"
                          className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                          style={{ fontSize: '18px' }}
                          checked={item.state}
                          onChange={(e) => handlePrimaryHighSchoolCheckboxChange(item.setState, e.target.checked, item.jobType)}
                        />
                        <span className="text-base">
                          {item.label}
                        </span>

                      </label>
                    ))
                  }
                </div>
                <h1
                  className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                  style={{ fontSize: '16px', color: categoryError ? 'red' : '#179d52' }}
                >
                  Select the category that best describes your tutoring skill-set
                </h1>
                <div className='flex flex-wrap  flex-column justify-center mt-2'>
                  {
                    tutoringSkills.map((item, index) => (
                      <div key={index} className=" gap-2 p-1 cursor-pointer">
                        <label className="flex items-center gap-2">
                          <input
                            type="radio"
                            name="tutoringCategory"
                            style={{ fontSize: '18px' }}
                            checked={selectedCategory === item.value}
                            onChange={() => handleCategoryChange(item.value)}
                            className='cursor-pointer'
                          />
                          <span className="text-base cursor-pointer">
                            {item.label}
                          </span>
                        </label>
                        <ul className="text-base list-disc p-0 m-0 p-1 pl-6">
                          <li className='' style={{ fontSize: '14px' }}>{item.description}</li>
                          {item.description2 && <li style={{ fontSize: '14px' }}>{item.description2}</li>}
                          {item.description3 && <li style={{ fontSize: '14px' }}>{item.description3}</li>}
                        </ul>
                      </div>
                    ))
                  }
                </div>
              </>
            )}
          </>
        )}
      </div>
      {primarySchool && tutorRadioState !== 'no' && (
        <>
          <div className='flex align-items-center justify-content-center'>
            <h1
              className="p-0 m-0 txt-clr font-bold line-height-1"
              style={{ fontSize: "24px" }}
            >
              Primary School
            </h1>
          </div>
          <div className=" p-4">
            <div className="mb-6">
              <h1 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                style={{ fontSize: '16px', color: ageGroupError ? 'red' : '#179d52' }}
              >Select age groups you work with</h1>
              <div className="flex flex-wrap gap-2">
                {ageGroups.map((primarySchoolYears, index) => (
                  <OutlineButton
                    key={primarySchoolYears.name}
                    onClick={() => toggleAgeGroup(index)}
                    style={{
                      fontSize: "14px",
                      border: primarySchoolYears.selected ? "2px solid #179D52" : "none",
                      paddingBlock: "15px",
                      fontWeight: primarySchoolYears.selected ? "700" : "500",
                      backgroundColor: primarySchoolYears.selected ? "#F0F4F7" : "#FFFFFF",
                    }}
                  >
                    {primarySchoolYears.text}
                  </OutlineButton>
                ))}
              </div>
            </div>
            <div>
              <h1
                className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                style={{ fontSize: '16px', color: subjectError ? 'red' : '#179d52' }}
              >
                Primary School Subjects
              </h1>
              <div className="space-y-4">
                {subjectGroups.map((group, groupIndex) => (
                  <div key={group.name} className="border-l-2 border-gray-200 pl-4">
                    <div
                      className="flex items-center gap-2 mb-1 cursor-pointer"
                      onClick={() => toggleGroup(groupIndex)}
                    >
                      <span className="transition-transform duration-200"
                        style={{ transform: group.isOpen ? 'rotate(180deg)' : 'rotate(0deg)', color: '#179D52' }}>
                        ▼
                      </span>
                      <span className="txt-clr font-semibold">{group.text}</span>
                    </div>
                    {group.isOpen && (
                      <div className="space-y-2 ml-4">
                        {group.children.map((subject, subjectIndex) => (
                          <label
                            key={subject.name}
                            className="flex items-center gap-2 cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={subject.selected}
                              onChange={() => toggleSubject(groupIndex, subjectIndex)}
                              className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                            />
                            <span className="txt-clr font-medium">{subject.text}</span>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className='flex align-items-center justify-content-center'>
            <h1
              className="p-0 m-0 txt-clr font-bold line-height-1"
              style={{ fontSize: "24px" }}
            >
              Tutoring Experience
            </h1>
          </div>
          <h1
            className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
            style={{ fontSize: '16px', color: myExperience.length < minCharLimit ? 'red' : '#179d52' }}
          >
            Describe your Tutoring experience
          </h1>
          <div className="txt-clr mt-1"  >
            <InputTextarea
              autoResize
              value={myExperience}
              required
              onChange={handleTextareaChange}
              rows={3}
              cols={30}
              className={styles.inputTextareafamily}
              placeholder='How long have you been tutoring? What subjects are you passionate about? What academic qualifications do you have?
              What formal teaching experience do you have? Etc.'
            />
            <p
              style={{
                fontSize: '14px',
                color: myExperience.length < minCharLimit ? 'red' : 'green',
                fontWeight: '400',
              }}
            >
              {myExperience.length < minCharLimit &&
                `${minCharLimit - myExperience.length} characters remaining`}
            </p>
          </div>
        </>)}
      {highSchool && tutorRadioState !== 'no' && (
        <>
          <div className='flex align-items-center justify-content-center'>
            <h1
              className="p-0 m-0 txt-clr font-bold line-height-1"
              style={{ fontSize: "24px" }}
            >
              High School
            </h1>
          </div>
          <div className="">
            <div className="mb-5">
              <h3 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                style={{ fontSize: "16px", color: highSchoolAgeGroupError ? 'red' : '#179d52' }}>Select age groups you work with</h3>
              <div className="flex flex-wrap gap-2">
                {ageGroupsHighSchool.map((primarySchoolYears, index) => (
                  <OutlineButton
                    key={primarySchoolYears.name}
                    onClick={() => togglehighSchoolAgeGroup(index)}
                    style={{
                      fontSize: "14px",
                      border: primarySchoolYears.selected ? "2px solid #179D52" : "none",
                      paddingBlock: "15px",
                      fontWeight: primarySchoolYears.selected ? "700" : "500",
                      backgroundColor: primarySchoolYears.selected ? "#F0F4F7" : "#FFFFFF",
                    }}
                  >

                    {primarySchoolYears.text}
                  </OutlineButton>
                ))}
              </div>
            </div>

            <h1
              // className="p-0 m-0 txt-clr font-medium line-height-1"
              className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
              style={{ fontSize: "16px", color: canTeachstudyskillsError ? 'red' : '#179d52' }}
            >
              Do you teach Study Skills
            </h1>
            <div className="flex justify-content-start items-center mt-1 gap-2">
              <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                <input
                  type="radio"
                  name="teachStudySkills"
                  value="yes"
                  checked={canTeachStudySkills === true}
                  onChange={() => handleRadioChange(setCanTeachStudySkills, true)}
                  className="cursor-pointer"
                />
                Yes
              </label>
              <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                <input
                  type="radio"
                  name="teachStudySkills"
                  value="no"
                  checked={canTeachStudySkills === false}
                  onChange={() => handleRadioChange(setCanTeachStudySkills, false)}
                  className="cursor-pointer"
                />
                No
              </label>
            </div>
            <h1
              className="p-0 m-0 mt-3 txt-clr font-medium line-height-1"
              style={{ fontSize: "16px", color: exampreparationError ? 'red' : '#179d52' }}
            >
              Do you teach Exam Preparation
            </h1>
            <div className="flex justify-content-start items-center mt-1 gap-2">
              <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                <input
                  type="radio"
                  name="teachExamPreparation"
                  value="yes"
                  checked={canTeachExamPreparation === true}
                  onChange={() => handleRadioChange(setCanTeachExamPreparation, true)}
                  className="cursor-pointer"
                />
                Yes
              </label>
              <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                <input
                  type="radio"
                  name="teachExamPreparation"
                  value="no"
                  checked={canTeachExamPreparation === false}
                  onChange={() => handleRadioChange(setCanTeachExamPreparation, false)}
                  className="cursor-pointer"
                />
                No
              </label>
            </div>
            <h1
              className="p-0 m-0 mt-3 txt-clr font-medium line-height-1"
              style={{ fontSize: "16px", color: highSchoolYearError ? 'red' : '#179d52' }}
            >
              {`What year did you complete (or will complete) ${sessionInfo.data['country'] === 'au' ? 'Year 12' : 'Year 13'
                }?`}
            </h1>
            <div className="relative mt-2">
              <select
                value={graduationYear || ''}
                onChange={handleYearChange}
                className={`appearance-none p-2 border border-gray-300 rounded cursor-pointer ${window.innerWidth < 1440 ? 'w-3' : 'w-2'}`}
              >
                <option value="" disabled>Select Year</option>
                {generateYears().map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 flex items-center px-2" style={{ top: '6px', left: '13%', color: '#179D52' }}>
                ▼
              </div>
            </div>
          </div>

          <h1 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
            style={{ fontSize: '16px', color: highSchoolSubjectError ? 'red' : '#179d52' }}>
            High School Subjects
          </h1>
          <div className="space-y-4 mt-4">
            {highSchoolSubjects.map((group, groupIndex) => (
              <div key={group.name} className="border-l-2 border-gray-200 pl-4">
                <div
                  className="flex items-center gap-2 mb-1 cursor-pointer"
                  onClick={() => highSchooltoggleGroup(groupIndex)}
                >
                  <span
                    className="transition-transform duration-200"
                    style={{
                      transform: group.isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                      color: '#179D52'
                    }}
                  >
                    ▼
                  </span>
                  <span className="txt-clr font-semibold">{group.text}</span>
                </div>
                {group.isOpen && highSchoolrenderSubjects(group, groupIndex)}
              </div>
            ))}
          </div>

          <div className='flex align-items-center justify-content-center'>
            <h1
              className="p-0 m-0 txt-clr font-bold line-height-1"
              style={{ fontSize: "24px" }}
            >
              Tutoring Experience
            </h1>
          </div>
          <h1
            className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
            style={{ fontSize: '16px', color: myExperience.length < minCharLimit ? 'red' : '#179d52' }}
          >
            Describe your Tutoring experience
          </h1>
          <div className="txt-clr mt-1 pl-3"  >
            <InputTextarea
              autoResize
              value={myExperience}
              required
              onChange={handleTextareaChange}
              rows={3}
              cols={30}
              className={styles.inputTextareafamily}
              placeholder='How long have you been tutoring? What subjects are you passionate about? What academic qualifications do you have?
             What formal teaching experience do you have? Etc.'
            />
            <p
              style={{
                fontSize: '14px',
                color: myExperience.length < minCharLimit ? 'red' : 'green',
                fontWeight: '400',
              }}
            >
              {myExperience.length < minCharLimit &&
                `${minCharLimit - myExperience.length} characters remaining`}
            </p>
          </div>
        </>)}
    </div>
  )
}

export default Tutoring