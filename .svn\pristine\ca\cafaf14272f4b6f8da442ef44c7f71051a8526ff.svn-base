import React, { useState } from 'react';
import PaymentSummaryHelper from '../PaymentSummaryHelper';
import PaymentApprovalSummary from '../PaymentApprovalSummary';
import PaymentSuccess from './PaymentSuccess';

function TimeSheet() {
  const timesheetData = [
    {
      status: "Awaiting Your Confirmation",
      type: "One Off Job",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Craig S",
    },
    {
      status: "Pending Review",
      type: "Contract Job",
      date: "30th of May, 2025",
      location: "12 Ocean Drive, North Sydney",
      userName: "Sarah M",
    },
    {
      status: "Approved",
      type: "Freelance Job",
      date: "22nd of April, 2025",
      location: "15 Park Lane, Melbourne",
      userName: "John D",
    },
  ];
    const [isSummaryOpen, setIsSummaryOpen] = useState(false);

  const defaultProps = {
    payerName: 'Craig S',
    payerAddress: '9 Christie Street, South Brisbane',
    paymentAmount: '112.50',
    jobType: 'One-Off Job',
    jobDate: '16th of Jan, 2025',
    parentName: '<PERSON>',
    helperName: '<PERSON><PERSON>',
    hoursWorked: '4.5',
    hourlyRate: '25',
    platformFee: '5.30',
    jobTotal: '106.87',
  };

  const handleReview = (userName) => {
    alert(`Review Timesheet for ${userName} clicked!`);
  };

  return (
    <div className='h-full'>
      {/* {timesheetData.map((entry, index) => (
        <TimeSheetCard
          key={index}
          status={entry?.status}
          type={entry?.type}
          date={entry?.date}
          location={entry?.location}
          userName={entry?.userName}
          onReview={() => handleReview(entry?.userName)}
        />
      ))} */}
      {/* <InvoiceApproved/> */}

      <PaymentSuccess
  amount="127.50"
  invoiceNo="102938"
  status="Complete"
  date="17.01.2024"
  time="1:24pm"
  invoiceString='Download Invoice'
  paymentType="PayTo"
  onDownload={() => console.log('Download PDF')}
  onDone={() => console.log('Go to home or close')}
/>
<div>
      <button
       
        onClick={() => setIsSummaryOpen(true)}
      >
        View Payment Summary
      </button>

      {/* <PaymentSummaryHelper
        isOpen={isSummaryOpen}
        onClose={() => setIsSummaryOpen(false)}
        {...defaultProps}
      /> */}
      {/* <PaymentApprovalSummary
     isOpen={isSummaryOpen}
    onClose={() => setIsSummaryOpen(false)}
    invoiceNo="INV-2025-001"
    dateIssued="[DD/MM/YYYY]"
    reference="N/A"
    totalAmount="115.50"
    platformFee="5.00"
    gst="10.50"
    paymentType="Credit Card"
    payerEmail="<EMAIL>"
    dueDateStatus="Due Now"
    jobType='One-Off Job'
    jobDate='16th of Jan, 2025'
/> */}

    </div>

    </div>
  );
}

export default TimeSheet;