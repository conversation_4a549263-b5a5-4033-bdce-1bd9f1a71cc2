import React from 'react';
import '../commonStyle/outline-button.css';

interface OutlineButtonProps extends React.HTMLProps<HTMLDivElement> {
    noShadow?: boolean;
    children: React.ReactNode;
}

function OutlineButton({ noShadow = false, children, ...props }: OutlineButtonProps) {
    return (
        <div
            {...props}
            className={`outline-button ${!noShadow ? 'outline-button-with-shadow' : ''}`}
        >
            {children}
        </div>
    );
}

export default OutlineButton;
