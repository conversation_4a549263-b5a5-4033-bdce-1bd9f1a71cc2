import React from "react";
import { FaCircleExclamation, FaLock } from "react-icons/fa6";
import { MdBusinessCenter } from "react-icons/md";
import SidePannel from "./SidePannel";
import { useNavigate } from "react-router-dom";
import HomeHeaderHelper from "./HomeHeaderHelper";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import c from "../../../helper/juggleStreetConstants";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../../Common/HorizontalNavigationMobile";

interface EssentialLinkProps {
  title: string;
  link: string;
  linkAction: string;
  isFamily?: boolean;
}

const EssentialLink: React.FC<EssentialLinkProps> = ({
  title,
  link,
  linkAction,
}) => {
  const navigate = useNavigate();
  const handleNavigation = () => {
    if (linkAction === "replace") {
      navigate(link, { replace: true });
    } else {
      navigate(link);
    }
  };

  const getIcon = () => {
    if (title === "About Us") {
      return (
        <MdBusinessCenter
          style={{ marginRight: "20px", fontSize: "1.5em", color: "green" }}
        />
      );
    }
    if (title == "Terms & Conditions") {
      return (
        <FaCircleExclamation
          style={{ marginRight: "20px", fontSize: "1.5em", color: "green" }}
        />
      );
    }
    if (title == "Privacy") {
      return (
        <FaLock
          style={{ marginRight: "20px", fontSize: "1.5em", color: "green" }}
        />
      );
    }
    return null;
  };

  return (
    <div
      onClick={handleNavigation}
      style={{
        cursor: "pointer",
        display: "flex",
        alignItems: "center",
        padding: "10px",
      }}
    >
      {getIcon()}
      <span>{title}</span>
    </div>
  );
};
const LearnMore = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { isMobile } = useIsMobile();
  const navigate = useNavigate();
  const activeIndex =
    sessionInfo?.data?.["accountStatus"] === c.accountStatus?.APPROVED ? 7 : 3;
  return (
    <div>
      {!isMobile && <HomeHeaderHelper />}
      <HorizontalNavigation
        onBackClick={() => {
          navigate("/helper-home");
        }}
        title="Learn More"
      />
      <SidePannel activeindex={activeIndex} />
      <div
        style={{
          width: !isMobile ? "calc(100% - 280px)" : "100%",
          height: "100%",
          position: "absolute",
          right: 0,
          padding: "1rem",
          paddingTop: isMobile && "75px",
        }}
      >
        <EssentialLink
          title="About Us"
          link="/helper-home/public/about-us?"
          linkAction="replace"
        />
        <EssentialLink
          title="Terms & Conditions"
          link="/helper-home/public/terms-and-conditions?"
          linkAction="replace"
        />
        <EssentialLink
          title="Privacy"
          link="/helper-home/public/privacy?"
          linkAction="replace"
        />
      </div>
    </div>
  );
};
export default LearnMore;
