.utilcontainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  padding: 40px;
  /* width: 325px; */
}

.utilheader h1 {
  width: auto;
  /* height: 48px; */
  font-weight: 700;
  font-size: 32px;
  color: #585858;
  line-height: 48px;
}
.utilprogressbar {
  width: auto;
  max-width: 100%;
  height: 7px;
  /* margin-top: 1rem; */
  margin-bottom: 1rem;
}

.utilprogressbar > div {
  background-color: #179d52 !important;
}
.utilprogressbar > div > div {
  display: none;
}
.utilButton {
  background-color: #ffa500;
  border: none;
  border-radius: 10px;
  color: #ffffff;
  height: 40px;
  width: 150px;
}
.customCheckbox {
  appearance: none;
  width: 19px;
  height: 20px;
  border: 1px solid #585858;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
  display: inline-block;
  vertical-align: middle;
  min-height: 20px;
  min-width: 19px;
  max-width: 19px;
  max-height: 20px;
}

.customCheckbox:checked {
  background-color: #179d52;
  border-color: #179d52;
  position: relative;
}

.customCheckbox:checked::after {
  content: "✔";
  position: absolute;
  color: white;
  font-size: 12px;
  left: 3px;
  top: -1;
}

.utilfooterContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 55px;
  width: 100%;
}
.utilcontainerhelper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 40px;
  /* width: 325px; */
}
.editBtn {
  width: 75px;
  min-height: 34px;
  font-size: 12px;
  display: flex;
  margin-left: auto;
  height: 34px;
  font-weight: 800;
  line-height: 18px;
  color: #ffffff;
  border-radius: 10px;
  align-items: center;
  background-color: #ffa500;
  border: none;
  padding: 8px 14px 8px 10px;
  gap: 8px;
  cursor: pointer;
}
.removeBtn {
  width: 74px;
  height: 34px;
  background-color: #ffd0cd;
  border-radius: 10px;
  border: 1px solid #ff6359;
  color: #ff6359;
  cursor: pointer;
}
.wwccDialogContent {
  padding: 40px;
  background-color: #fff;
  border-radius: 33px;
}

/* Close button styles */
.wwccCloseBtn {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  text-align: right;
}

.wwccCloseBtn:hover {
  color: #333;
}
.CloseBtn {
  display: flex;
  justify-content: end;
  position: absolute;
  right: 3%;
  width: 30px;
  height: 32px;
  color: #585858;
  border-radius: 50%;
  top: 2%;
  cursor: pointer;
}
.calendarDiv {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-block: 0px;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}
.wwccHeader {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #585858;
}
.wwccSaveButton {
  width: 156px;
  height: 39px;
  background-color: #ffa500;
  font-weight: 800;
  font-size: 14px;
  color: #ffffff;
  box-shadow: 0px 4px 4px 0px #00000040;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}
.wwccSaveButton:disabled {
  cursor: not-allowed;
  background-color: #f0f4f7;
  color: #585858;
  font-size: 14px;
  font-weight: 400;
  box-shadow: none;
}
ng-deep .p-card .p-card-content {
  padding: 0;
}
.profiletext {
  color: #585858;
  font-weight: 700;
}
.inputTextareafamily {
  width: 100%;
  height: 100%;
  border: none;
  border-color: unset;
  box-shadow: none;
  border-radius: 10px;
  font-weight: 300;
  font-size: 12px;
}
@media (max-width: 1450px) {
  .profile-strength-container {
    display: block;
  }

  .profile-strength-container .progress-bar {
    display: block;
  }
}
.profilestrengthprogressbar {
  width: auto;
  max-width: 100%;
  height: 7px;
}

.profilestrengthprogressbar > div {
  background-color: #179d52 !important;
}
.profilestrengthprogressbar > div > div {
  display: none;
}
.p-dialog-content {
  overflow-y: unset !important;
  flex-grow: 1 !important;
}

.referenceType {
  width: 100% !important;
  border: none !important;
}
.referenceType > div {
  background-color: #f0f4f7 !important;
}
.referenceType > span {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  justify-content: center !important;
}
.utilheaderMobile {
  width: 100%;
  position: fixed;
  top: 0;
  font-weight: 700;
  font-size: 22px !important;
  display: flex;
  justify-content: center;
  color: #585858;
  line-height: 48px;
}
.utilfooterContainerMobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-top: 55px; */
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  height: min-content;
  box-shadow: 0 0 8px #00000040;
  background-color: #fff;
}
