import { Dispatch, MutableRefObject, SetStateAction, useEffect, useState } from 'react';
import SidePannel from './SidePannel';
import HomeHeaderHelper from './HomeHeaderHelper';
import useScrollListener from '../../../hooks/useScrollListener';
import { User<PERSON><PERSON>ult, UserSearchResponse, useSearchHook } from '../../../hooks/SearchGeoSearchHook';
import ClientCard from './ClientCard';
import environment from '../../../helper/environment';
import { SlLocationPin } from 'react-icons/sl';
import { IoStarSharp } from 'react-icons/io5';
import useLoader from '../../../hooks/LoaderHook';
import useProviderFilter from '../../../hooks/useProviderFilter';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../commonComponents/CustomButton';
import Service from '../../../services/services';
import styles from '../../../containers/Common/styles/connectionRequest.module.css';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';
import useIsMobile from '../../../hooks/useIsMobile';
import utils from '../../../components/utils/util';
import CookiesConstant from '../../../helper/cookiesConst';
const HelperCardContainer = ({ user, onRefresh }: { user: UserResult; onRefresh: () => void }) => {
    return (
        <div className='h-full p-2'>
            <ClientCard
                helperName={user.publicName}
                imgSrc={`${environment.getStorageURL(window.location.hostname)}/images/${
                    user.imageSrc
                }`}
                items={[
                    {
                        icon: <SlLocationPin color='#37A950' fontSize={'18px'} />,
                        description: (
                            <span>
                                {user.suburb} - {user.distanceInKm}Km
                            </span>
                        ),
                    },
                    {
                        icon: <IoStarSharp color='#FFA500' fontSize={'18px'} />,
                        description: (
                            <span className='font-light'>
                                {user.ratingsAvg.toFixed(1)} ({user.ratingsCount} ratings)
                            </span>
                        ),
                    },
                ]}
                refresh={onRefresh}
                {...user}
            />
        </div>
    );
};

const HelperCardList = ({
    userSearchResponse,
    onRefresh,
}: {
    userSearchResponse: UserSearchResponse;
    onRefresh: () => void;
}) => {
    const {isMobile}=useIsMobile()
    return !isMobile ? (
        <div className='grid grid-nogutter z-1'>
        {userSearchResponse?.results?.map((user) => (
            <div key={user.id} className={`col-12 lg:col-6 ${styles['xl:col-4']}`}>
                <HelperCardContainer user={user} onRefresh={onRefresh} />
            </div>
        ))}
    </div>
    ):(
        <div className='grid grid-nogutter z-1'>
        {userSearchResponse?.results?.map((user) => (
            <div key={user.id} className={`col-6 lg:col-6 ${styles['xl:col-2']}`}>
                <HelperCardContainer user={user} onRefresh={onRefresh} />
            </div>
        ))}
    </div>
    )
};

const HelperMain = ({
    listener,
    filters,
    setUseSearchResponse,
    useSearchResponse,
}: {
    listener: MutableRefObject<HTMLDivElement>;
    filters: any;
    useSearchResponse: UserSearchResponse;
    setUseSearchResponse: Dispatch<SetStateAction<UserSearchResponse>>;
}) => {
    const { isMobile } = useIsMobile();
    const { enableLoader, disableLoader } = useLoader();
    const { updateFilters, refreshSearchResult } = useSearchHook((data) => {
        setUseSearchResponse((prev) => {
            if (!data || !data.results) {
                disableLoader();
                return prev;
            }
            if (data.results.length === 0) {
                disableLoader();
                return prev;
            }
            const prevResultsMap = new Map((prev?.results || []).map((item) => [item.id, item]));
            let resultsChanged = false;
            const newResults = data.results.map((newItem) => {
                const existingItem = prevResultsMap.get(newItem.id);
                if (!existingItem) {
                    resultsChanged = true;
                    return newItem;
                }
                if (JSON.stringify(existingItem) !== JSON.stringify(newItem)) {
                    resultsChanged = true;
                    return newItem;
                }
                return existingItem;
            });

            if (!resultsChanged) {
                disableLoader();
                return prev;
            }
            const finalResults = [...(prev?.results || [])];
            newResults.forEach((newItem) => {
                const existingIndex = finalResults.findIndex((item) => item.id === newItem.id);
                if (existingIndex === -1) {
                    finalResults.push(newItem);
                } else {
                    finalResults[existingIndex] = newItem;
                }
            });
            return {
                ...prev,
                results: finalResults,
                total: data?.total || prev?.total || 0,
            };
        });
        disableLoader();
    });
    useProviderFilter({
        defaultFilters: filters,
        onFilterChange: (filters) => {
            updateFilters((prev) => filters);
        },
        enableLoader,
        setSearchResponse: setUseSearchResponse,
    });
    return (
        <div
            ref={listener}
            className={`w-full h-full flex flex-column ${
                !isMobile && 'pl-6 pr-3'
            } pb-3 relative overflow-y-auto overflow-x-hidden`}
            style={{
                transition: 'all 0.3s ease-in-out',
                backgroundColor: '#FFFFFF',
            }}
        >
            <HelperCardList
                userSearchResponse={useSearchResponse}
                onRefresh={() => {
                    enableLoader();
                    setUseSearchResponse(null);
                    setTimeout(() => {
                        refreshSearchResult();
                    }, 1000);
                }}
            />
        </div>
    );
};

const ConnectionRequest = () => {
    const [useSearchResponse, setUseSearchResponse] = useState<UserSearchResponse | null>(null);
    const [connectionRadioState, setConnectionRadioState] = useState<'recent' | 'all'>('all');
    const updatedNeighbourhood = connectionRadioState === 'recent' ? 6 : 3;
    const [filters, setFilters] = useState({
        pageIndex: 1,
        pageSize: 50,
        sortBy: 'experience',
        filters: [
            { field: 'distance', operator: 'eq', value: -1 },
            { field: 'neighbourhood', operator: 'eq', value: updatedNeighbourhood },
            { field: 'jobDeliveryMethod', operator: 'eq', value: 2 },
        ],
    });
    const { listener, position } = useScrollListener();
    const { disableLoader, enableLoader } = useLoader();
    const navigate = useNavigate();
    const { isMobile } = useIsMobile();
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const handleConnectionRadioChange = async (value: 'all' | 'recent') => {
        setConnectionRadioState(value);
        const newNeighbourhood = value === 'recent' ? 6 : 3;
        const updatedFilters = {
            ...filters,
            filters: filters.filters.map((filter) =>
                filter.field === 'neighbourhood' ? { ...filter, value: newNeighbourhood } : filter
            ),
        };
        const payload = { ...updatedFilters };
        enableLoader();
        const { success, data } = await new Promise<{
            success: boolean;
            data: UserSearchResponse | null;
        }>((res, _) => {
            Service.getConnections(
                (data) => {
                    res({ data: data, success: true });
                },
                () => {
                    res({ data: null, success: false });
                },
                payload
            );
        });
        if (success && data) {
            
            setUseSearchResponse(data);
            disableLoader();
        } else {
            console.error('Failed to fetch connections');
            disableLoader();
        }
    };

    useEffect(() => {
        handleConnectionRadioChange('all');
    }, []);
    const navigateToHome = () => {
        navigate('/helper-home');
    };
    return (
        <div>
            <HomeHeaderHelper />
            <SidePannel activeindex={1} />
            <HorizontalNavigation
                title='Connection Request'
                onBackClick={() => {
                    navigate('/helper-home');
                }}
            />
            <div
                style={{
                    width: !isMobile && 'calc(100% - 200px)',
                    height: '100%',
                    position: 'absolute',
                    right: 0,
                    left: isMobile && 0,
                    padding: !isMobile && '3rem',
                    color: '#585858',
                    marginTop: isMobile && '70px',
                }}
            >
                <div>
                    <div
                        className={`flex justify-content-start items-center ${
                            !isMobile ? 'mt-3 ml-6' : 'mx-2'
                        } gap-2`}
                    >
                        <label className='flex justify-content-start items-center txt-clr cursor-pointer'>
                            <input
                                type='radio'
                                name='recent-radio'
                                value='recent'
                                checked={connectionRadioState === 'recent'}
                                onChange={() => handleConnectionRadioChange('recent')}
                                className='cursor-pointer'
                            />
                            Recent
                        </label>
                        <label className='flex justify-content-start items-center txt-clr cursor-pointer'>
                            <input
                                type='radio'
                                name='all-radio'
                                value='all'
                                checked={connectionRadioState === 'all'}
                                onChange={() => handleConnectionRadioChange('all')}
                                className='cursor-pointer'
                            />
                            All
                        </label>
                    </div>
                </div>
                {useSearchResponse?.total === 0 && (
                    <div className={!isMobile ? 'ml-6 q-mx-sm q-ym-sm' : 'mx-2'}>
                        You don't have invitations that match this category.
                        <br />
                        <CustomButton
                            label={'Connect with families near you'}
                            style={{ margin: '0', width: '245px' }}
                            onClick={navigateToHome}
                        />
                    </div>
                )}
                <HelperMain
                    listener={listener}
                    filters={filters}
                    setUseSearchResponse={setUseSearchResponse}
                    useSearchResponse={useSearchResponse}
                />
            </div>
        </div>
    );
};
export default ConnectionRequest;
