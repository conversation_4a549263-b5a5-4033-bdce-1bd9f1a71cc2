// AccountLayout.tsx
import React from "react";
import styles from "../../Parent/styles/account-layout.module.css";
// import MyFamily from './MyFamily';
// import MyChildren from './MyChildren';
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
// import MyAddresses from './MyAddresses';
// import FamilyMembership from './FamilyMembership';
import AboutMe from "./AccountHolder";
import AccountSidePanelBuisness from "../AccountSidePanelBuisness";
import MyBusiness from "./MyBusiness";
import FamilyMembershipBusiness from "./FamilyMembershipBusiness";
import Payments from "../../Parent/AccountSetting/Payments";
import GeneralSettings from "../../Common/GeneralSettings";
import MyAddresses from "../../Parent/AccountSetting/MyAddresses";
import useIsMobile from "../../../hooks/useIsMobile";

interface AccountLayoutProps {
  visible: boolean;
}

const AccountLayoutBuisness: React.FC<AccountLayoutProps> = ({ visible }) => {
  const [isSidePanelVisible, setSidePanelVisible] = React.useState(true);
  const { isMobile } = useIsMobile();
  const { accountAndSettingsActiveTabBuisness } = useSelector(
    (state: RootState) => state.applicationState
  );

  if (!visible) return null;

  const toggleSidePanel = () => {
    setSidePanelVisible((prev) => !prev);
  };

  function renderPanels(panelId: number) {
    switch (panelId) {
      case 0:
        return <AboutMe />;
      case 1:
        return <MyBusiness />;
      case 5:
        return <Payments />;
      case 7:
        return <GeneralSettings />;
      // case 3:
      //     return <MyAddresses />;
      case 6:
        return <FamilyMembershipBusiness />;

      case 2:
        return <MyAddresses />;

      default:
        return null;
    }
  }

  return (
    <div className={styles.overlay}>
      {/* <button className={styles.toggleButton} onClick={toggleSidePanel}>
                <i className="pi pi-bars" />
            </button> */}
      {!isMobile ? (
        <div className={styles.header}>
          <h2 className={styles.headerTitle}>Account & Settings</h2>
          {/* <button className={styles.closeButton} onClick={onHide}>
                 Close
             </button> */}
        </div>
      ) : (
        <div className={styles.headerMobile}>
          <h2 className={styles.headerTitleMobile}>Account & Settings</h2>
          {/* <button className={styles.closeButton} onClick={onHide}>
                    Close
                </button> */}
        </div>
      )}

      {!isMobile ? (
        <div className={styles.dialogContainer}>
          {isSidePanelVisible && (
            <div className={styles.leftPane}>
              <AccountSidePanelBuisness />
            </div>
          )}

          <div className={styles.rightPane}>
            <div className={styles.content}>
              {renderPanels(accountAndSettingsActiveTabBuisness)}
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.dialogContainerMobile}>
          {isSidePanelVisible && (
            <div className={styles.leftPane}>
              <AccountSidePanelBuisness />
            </div>
          )}

          <div className={styles.rightPaneMobile}>
            <div className={styles.content}>
              {renderPanels(accountAndSettingsActiveTabBuisness)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountLayoutBuisness;
