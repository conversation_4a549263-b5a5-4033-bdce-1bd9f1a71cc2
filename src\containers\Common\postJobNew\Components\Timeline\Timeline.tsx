import { useSelector } from "react-redux";
import c from "../../../../../helper/juggleStreetConstants";
import { JobManagementScreens, PayloadTemplate, useJobManager } from "../../provider/JobManagerProvider";
import { RootState } from "../../../../../store";
import React, { useEffect, useMemo, useState } from "react";
import { FaCheck } from "react-icons/fa6";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { ProgressBar } from "primereact/progressbar";
import utils from "../../../../../components/utils/util";
import CookiesConstant from "../../../../../helper/cookiesConst";
import { useNavigate } from "react-router-dom";
import { IframeBridge } from "../../../../../services/IframeBridge";

type TimelineObject = { [label: string]: JobManagementScreens[] };

interface TimelineStepProps {
  step: string;
  index: number;
  active: number;
}

const getPosition = (activeIndex: number, currentIndex: number) => {
  if (activeIndex === currentIndex) {
    return "same" as const;
  } else if (activeIndex > currentIndex) {
    return "previous" as const;
  } else {
    return "next" as const;
  }
};

const TimelineStep: React.FC<TimelineStepProps> = ({ step, active, index }) => {
  const position = getPosition(active, index);
  return (
    <div className="flex gap-2 align-items-center">
      <div
        className="relative flex justify-content-center align-items-center"
        style={{
          left: "-10px",
          width: "18px",
          height: "18px",
          borderRadius: "999px",
          backgroundColor: position === "next" ? "white" : "#FFA500",
          boxShadow: "0 4px 4px 0 #00000040",
        }}
      >
        {position === "previous" && <FaCheck color="white" size={"70%"} />}
      </div>
      <p
        className="m-0 p-0"
        style={{
          fontSize: position === "same" ? "22px" : "16px",
          fontWeight: position === "previous" ? "600" : position === "same" ? "700" : "300",
          color: "#FFFFFF",
          textDecoration: position === "same" ? "underline" : "",
        }}
      >
        {step}
      </p>
    </div>
  );
};

function getJobType(payload: Partial<PayloadTemplate>) {
  if (payload?.jobType === 256) return "odd-job" as const;
  if ([2, 4, 8, 12].includes(payload?.jobType)) return "recurring" as const;
  if ([64, 128].includes(payload?.jobType)) return "tutoring" as const;
  return "one-off";
}

function generateTimelineData(sessionInfo: any, payload: Partial<PayloadTemplate>): TimelineObject[] {
  const oneOff_oddJob: TimelineObject[] = [
    { "Job Details": ["job-details"] },
    { "Job Description": ["job-description-mobile"] },
    { "Job Pricing": ["jobpricing-step1", "jobpricing-step2", "jobpricing-step3", "overtime-section-mobile"] },
    { "Select Candidates": ["candidate-selection"] },
    { "Select Matching": ["candidate-matching"] },
    { Payment: ["subscription"] },
    { "Review & Post": ["job-summary", "review-post"] },
  ];

  const recurring_job: TimelineObject[] = [
    { "Days & Schedule": ["day-and-schedule"] },
    { "Job Pricing": ["pricing-payments-step1", "pricing-payments-step2"] },
    { "Job Description": ["section4-mobile"] },
    { "Select Candidates": ["candidate-selection"] },
    { "Select Matching": ["candidate-matching"] },
    { Payment: ["subscription"] },
    { "Review & Post": ["job-summary", "review-post"] },
  ];

  const tutoring_job: TimelineObject[] = [
    { "Type & Years": ["typeand-years"] },
    { "Select Subjects": ["tutoring-subjects"] },
    { "Days & Schedule": ["day-and-schedule"] },
    { "Job Pricing": ["pricing-payments-step1", "pricing-payments-step2"] },
    { "Job Description": ["section4-mobile"] },
    { "Select Candidates": ["candidate-selection"] },
    { "Select Matching": ["candidate-matching"] },
    { Payment: ["subscription"] },
    { "Review & Post": ["job-summary", "review-post"] },
  ];

  const jobType = getJobType(payload);
  const isPaidUser = sessionInfo?.paymentInfo?.paymentType !== c.userPaymentType.FREE;
  const managedByUser = payload?.managedBy !== c.managedBy.SYSTEM;

  let timeline: TimelineObject[] = [];

  switch (jobType) {
    case "odd-job":
    case "one-off":
      timeline = oneOff_oddJob;
      break;
    case "recurring":
      timeline = recurring_job;
      break;
    case "tutoring":
      timeline = tutoring_job;
      break;
  }

  if (managedByUser) {
    timeline = timeline.map((step) => {
      if (step["Candidate Matching"]) {
        return { "Candidate Selection": step["Candidate Matching"] };
      }
      return step;
    });
  }

  if (isPaidUser) {
    timeline = timeline.filter((step) => !("Payment" in step));
  }

  return timeline;
}

function getTimelineStepIndexFromHash(hashPath: string, timeline: TimelineObject[]): number {
  const normalizedHash = hashPath.toLowerCase();

  for (let i = 0; i < timeline.length; i++) {
    const label = Object.keys(timeline[i])[0];
    const normalizedLabel = label.toLowerCase().replace(/\s|&/g, "-");

    const values = timeline[i][label];

    const matchByValue = values.some((val) => normalizedHash.includes(val.toLowerCase()));
    const matchByLabel = normalizedHash.includes(normalizedLabel);

    if (matchByLabel || matchByValue) {
      return i;
    }
  }

  return -1;
}
function getCompletionPercentage(length: number, activeIndex: number): number {
  if (length <= 1) return 0;
  const percent = (activeIndex / (length - 1)) * 100;
  return Math.round(percent);
}

// function Timeline() {
//   const { isMobile } = useIsMobile();
//   const sessionInfo = useSelector((state: RootState) => state.sessionInfo.data);
//   const { payload } = useJobManager();
//   const hashPath = window.location.hash;

//   const timeline = useMemo(() => generateTimelineData(sessionInfo, payload), [sessionInfo, payload]);

//   const isHidden = useMemo(() => ["job-type", "job-posting"].some((p) => hashPath.includes(p)), [hashPath]);

//   const activeIndex = useMemo(() => getTimelineStepIndexFromHash(hashPath, timeline), [hashPath]);

//   if (isHidden) {
//     return <div className="w-full h-full justify-content-center"></div>;
//   }

//   return isMobile ? <TimelineMobile activeIndex={activeIndex} timeline={timeline} /> : <TimelineWeb activeIndex={activeIndex} timeline={timeline} />;
// }

function Timeline() {
  const { isMobile } = useIsMobile();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo.data);
  const { payload } = useJobManager();
  const hashPath = window.location.hash;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(false);
  }, [payload, sessionInfo]);

  const timeline = useMemo(() => {
    if (!payload || !sessionInfo) return [];
    return generateTimelineData(sessionInfo, payload);
  }, [sessionInfo, payload]);

  const isHidden = useMemo(() => ["job-type", "job-posting"].some((p) => hashPath.includes(p)), [hashPath]);

  const activeIndex = useMemo(() => {
    if (!timeline.length) return 0;
    return getTimelineStepIndexFromHash(hashPath, timeline);
  }, [hashPath, timeline]);

  if (isLoading) {
    return (
      <div className="w-full h-full flex justify-content-center align-items-center">
        <p></p>
      </div>
    );
  }

  if (isHidden) {
    return <div className="w-full h-full justify-content-center"></div>;
  }

  return isMobile ? (
    <TimelineMobile activeIndex={activeIndex} timeline={timeline} />
  ) : (
    <TimelineWeb activeIndex={activeIndex} timeline={timeline} />
  );
}

export default Timeline;

interface TimelineWebProps {
  activeIndex: number;
  timeline: TimelineObject[];
}

function TimelineWeb({ activeIndex, timeline }: TimelineWebProps) {
  return (
    <div className="w-full h-full">
      <div
        className="h-full relative flex flex-column justify-content-around"
        style={{
          marginLeft: "20%",
          borderLeft: "2px solid #F5F3F3",
        }}
      >
        {timeline.map((stepObj, index) => {
          const label = Object.keys(stepObj)[0];
          return <TimelineStep key={index} index={index} active={activeIndex} step={label} />;
        })}
      </div>
    </div>
  );
}
function TimelineMobile({ activeIndex, timeline }: TimelineWebProps) {
  const navigate = useNavigate();
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const handleGoBack = () => {
    sessionStorage.removeItem("jobManagement");
    IframeBridge.sendToParent({
      type: "goBack-postjob",
    });
    if (!inIframe) {
      navigate("/");
    }
  };
  return (
    <div className="w-full h-full flex justify-content-center align-items-center gap-3">
      <div
        id="back-button-portal"
        style={{
          minWidth: "30px",
          minHeight: "30px",
          maxWidth: "30px",
          maxHeight: "30px",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          zIndex: 1,
        }}
      />
      <div className="w-full flex align-items-center">
        <div className="flex flex-column w-full">
          <p
            className="m-0 p-0 text-center"
            style={{
              fontSize: "22px",
              fontWeight: "800",
              color: "#FFFFFF",
            }}
          >
            {Object.keys(timeline?.[activeIndex] ?? {})[0] ?? ""}
          </p>
          <ProgressBar
            className="mt-2"
            style={{
              height: "7px",
              padding: "1px 2px",
            }}
            value={getCompletionPercentage(timeline.length, activeIndex) + 20}
            displayValueTemplate={() => null}
            pt={{
              value: {
                style: {
                  height: "5px",
                  backgroundColor: "#FFA500",
                  borderRadius: "20px",
                  maxWidth: "98.5%",
                },
              },
            }}
          />
        </div>
      </div>
      <div
        onClick={handleGoBack}
        style={{
          minWidth: "30px",
          minHeight: "30px",
          maxWidth: "30px",
          maxHeight: "30px",
          borderRadius: "50%",
          backgroundColor: "#fff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          zIndex: 1,
          right: "18px",
        }}
      >
        <p className="pi pi-times text-green-700"></p>
      </div>
    </div>
  );
}

{
  /**

<div
        style={{
          width: "60%",
        }}
      >
        <p
          className="m-0 p-0 text-center"
          style={{
            fontSize: "22px",
            fontWeight: "800",
            color: "#FFFFFF",
          }}
        >
          {Object.keys(timeline[activeIndex])[0]}
        </p>
        <ProgressBar
          style={{
            height: "7px",
            padding: "1px 2px",
          }}
          value={getCompletionPercentage(timeline.length, activeIndex)}
          displayValueTemplate={() => null}
          pt={{
            value: {
              style: {
                height: "5px",
                backgroundColor: "#FFA500",
                borderRadius: "20px",
              },
            },
          }}
        />
      </div>
      <div
        id="jugglestreet-portal"
        className="w-full"
        style={{
          marginBlock: "20px 10px",
        }}
      />

   */
}
