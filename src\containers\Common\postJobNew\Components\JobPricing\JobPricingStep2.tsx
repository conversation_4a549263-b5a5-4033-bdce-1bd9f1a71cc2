import { Divider } from "primereact/divider";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../store";
import { utils } from "../../../../../components/utils/util";
import CookiesConstant from "../../../../../helper/cookiesConst";
import c from "../../../../../helper/juggleStreetConstants";

import styles from "../../../styles/job-pricing.module.css";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import { GoBack, Next } from "../Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";
function JobPricingStep2() {
  const { isMobile } = useIsMobile();
  return isMobile ? <JobPricingStep2Mobile /> : <JobPricingStep2Web />;
}
export default JobPricingStep2;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const [jobTypeSelection, setJobTypeSelection] = useState<number>(payload.paymentType ? payload.paymentType : -1); // State for "Fixed Price or Hourly Rate"
  const [paymentMethodSelection, setPaymentMethodSelection] = useState<number>(
    payload.helperPaymentMethod !== undefined
      ? payload.helperPaymentMethod - 1 // Adjust for 0-based index
      : -1
  );
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  function onlyBankTransfer() {
    return Number(utils.getCookie(CookiesConstant.clientType)) !== c.clientType.INDIVIDUAL && !sessionInfo?.data?.["isJobManagerAccount"];
  }

  useEffect(() => {
    if (onlyBankTransfer() && paymentMethodSelection === -1) {
      setPaymentMethodSelection(0); // Set to "Bank Transfer"
    }
  }, [sessionInfo, paymentMethodSelection]);

  useEffect(() => {
    setpayload({
      ...payload,
      paymentType: jobTypeSelection !== -1 ? jobTypeSelection : payload.paymentType,
      helperPaymentMethod:
        paymentMethodSelection !== -1
          ? paymentMethodSelection + 1 // Adjust for 1-based index
          : payload.helperPaymentMethod,
    });
  }, [jobTypeSelection, paymentMethodSelection]);
  const [jobType, setJobType] = useState<number>(payload.paymentType ? payload.paymentType : -1);
  const [paymentMethod, setPaymentMethod] = useState<number>(payload.helperPaymentMethod ? payload.helperPaymentMethod - 1 : -1);

  const jobTypes = [
    ["Hourly Rate", 2],
    ["Fixed Price", 1],
  ];
  const paymentMethods = ["Cash Payment", "Bank Transfer"];

  // const handleNext = () => {
  //     if (jobType === -1) return;

  //     const updatedPayload = {
  //         ...payload,
  //         paymentType: jobType,
  //         helperPaymentMethod: paymentMethod + 1,
  //     };
  //     next("jobpricing-step3");
  // };

  const handlePrev = () => {
    const updatedPayload = {
      ...payload,
      paymentType: jobType === 0 ? 2 : 1,
      helperPaymentMethod: paymentMethod + 1,
    };
    // prevClicked(updatedPayload);
  };
  return {
    jobTypeSelection,
    setJobTypeSelection,
    paymentMethodSelection,
    setPaymentMethodSelection,
    sessionInfo,
    onlyBankTransfer,
    next,
    prev,
    setpayload,
    payload,
    jobTypes,
    paymentMethods,
    jobType,
    setJobType,
    paymentMethod,
    setPaymentMethod,
  };
};
const JobPricingStep2Web = () => {
  const {
    jobTypeSelection,
    setJobTypeSelection,
    paymentMethodSelection,
    setPaymentMethodSelection,
    sessionInfo,
    next,
    prev,
    setpayload,
    payload,
    onlyBankTransfer,
  } = useJobTypeHook();
  return (
    <div className="h-full w-full flex flex-column justify-content-center align-items-start overflow-hiden overflow-y-auto relative">
      <div
        className="flex align-items-center flex-column"
        style={{
          height: "100%",
          width: "80%",
          marginTop: "12%",
        }}
      >
        <div
          className="w-full flex flex-column align-items-center lg:flex-row lg:gap-0 lg:align-items-start"
          style={{
            gap: "80px",
          }}
        >
          <div
            className="h-min flex justify-content-center"
            style={{
              width: "54%",
            }}
          >
            <p
              className="m-0 p-0 ml-auto mr-6"
              style={{
                width: "70%",
                fontWeight: 700,
                fontSize: "30px",
                color: "#585858",
                textWrap: "wrap",
              }}
            >
              Is this job Fixed Price or Hourly Rate?
            </p>
          </div>
          <div className="flex-grow-1 h-min flex align-items-center flex-column gap-2 w-6">
            {[
              ["Hourly Rate", 2],
              ["Fixed Price", 1],
            ].map((item, index) => (
              <div
                className=""
                key={index}
                style={{
                  userSelect: "none",
                  cursor: "pointer",
                  display: "flex",
                  justifyContent: "start",
                  alignItems: "center",
                  border: jobTypeSelection === item[1] ? "3px solid #179D52" : "1px solid #DFDFDF",
                  height: "70px",
                  maxWidth: "363px",
                  width: "90%",
                  borderRadius: "10px",
                  padding: "0 20px",
                  gap: "20px",
                  fontWeight: 700,
                  fontSize: "24px",
                  color: jobTypeSelection === item[1] ? "#179D52" : "#585858",
                }}
                onClick={() => {
                
                  setJobTypeSelection(item[1] as number);
                }}
              >
                <div
                  className="flex justify-content-center align-items-center"
                  style={{
                    height: "18px",
                    width: "18px",
                    borderRadius: "50%",
                    padding: "1px",
                    border: `1px solid ${jobTypeSelection === index ? "#179d52" : "#DFDFDF"}`,
                  }}
                >
                  {jobTypeSelection === item[1] && (
                    <div
                      style={{
                        height: "100%",
                        width: "100%",
                        borderRadius: "50%",
                        backgroundColor: "#179D52",
                      }}
                    />
                  )}
                </div>
                <p className="m-0 p-0">{item[0]}</p>
              </div>
            ))}
          </div>
        </div>
        {/* <div className="w-full mt-auto" /> */}
        <Divider className="mt-7 mb-4 w-10 " />
      </div>

      {jobTypeSelection !== -1 && (
        <div className="h-full w-full flex  align-items-start ">
          <div
            className="flex align-items-center flex-column"
            style={{
              height: "100%",
              width: "80%",
              // paddingTop: '20px',
            }}
          >
            <div
              className="w-full flex  flex-column align-items-center lg:flex-row lg:gap-0 lg:align-items-start"
              style={{
                gap: "80px",
              }}
            >
              <div
                className="h-min flex justify-content-center"
                style={{
                  width: "54%",
                }}
              >
                <p
                  className="m-0 p-0 ml-auto mr-6"
                  style={{
                    width: "70%",
                    fontWeight: 700,
                    fontSize: "30px",
                    color: "#585858",
                    textWrap: "wrap",
                  }}
                >
                  How will you pay your Helper for this job?
                </p>
              </div>
              <div className="flex-grow-1 h-min flex align-items-center flex-column gap-2 w-6">
                {(onlyBankTransfer() ? ["Bank Transfer"] : ["Cash Payment", "Bank Transfer"]).map((item, index) => (
                  <div
                    className=""
                    key={index}
                    style={{
                      userSelect: "none",
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "start",
                      alignItems: "center",
                      border: paymentMethodSelection === index ? "3px solid #179D52" : "1px solid #DFDFDF",
                      height: "70px",
                      maxWidth: "363px",
                      width: "90%",
                      borderRadius: "10px",
                      padding: "0 20px",
                      gap: "20px",
                      fontWeight: 700,
                      fontSize: "24px",
                      color: paymentMethodSelection === index ? "#179D52" : "#585858",
                    }}
                    onClick={() => {
                      setPaymentMethodSelection(index);
                    }}
                  >
                    <div
                      className="flex justify-content-center align-items-center"
                      style={{
                        height: "18px",
                        width: "18px",
                        borderRadius: "50%",
                        padding: "1px",
                        border: `1px solid ${paymentMethodSelection === index ? "#179d52" : "#DFDFDF"}`,
                      }}
                    >
                      {paymentMethodSelection === index && (
                        <div
                          style={{
                            height: "100%",
                            width: "100%",
                            borderRadius: "50%",
                            backgroundColor: "#179D52",
                          }}
                        />
                      )}
                    </div>
                    <p className="m-0 p-0">{item}</p>
                  </div>
                ))}
                {paymentMethodSelection === 1 && (
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 400,
                      fontSize: "14px",
                      color: "#585858",
                      maxWidth: "317px",
                      width: "100%",
                    }}
                  >
                    When you award this job you will be provided with the helper’s bank details.
                  </p>
                )}
                {onlyBankTransfer() && (
                  <span
                    className="m-0 p-0"
                    style={{
                      maxWidth: "363px",
                      fontWeight: 400,
                      fontSize: "14px",
                      color: "#585858",
                      width: "90%",
                    }}
                  >
                    <p className="m-0 p-0">Direct payment via bank transfer.</p>
                    {/* <p className='m-0 p-0'>
                                        When you award this job, you will be provided with the
                                        candidate's bank details.
                                    </p> */}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      <div
        className=" flex flex-column sticky justify-content-center align-items-center bottom-0 bg-white"
        style={{
          width: "100%",
        }}
      >
        <Divider className="m-0" />

        <div className="flex justify-content-between align-content-center py-3" style={{ width: "80%" }}>
          <GoBack
            onClick={() => {
              setpayload({
                ...payload,
                helperPaymentMethod: onlyBankTransfer() ? c.helperPaymentMethod.bankTransfer : paymentMethodSelection + 1,
              });
              prev("jobpricing-step1");
            }}
          />
          <Next
            disabled={jobTypeSelection === -1 || paymentMethodSelection === -1}
            onClick={() => {
              setpayload({
                ...payload,
                helperPaymentMethod: onlyBankTransfer() ? c.helperPaymentMethod.bankTransfer : paymentMethodSelection + 1,
              });
              next("jobpricing-step3");
            }}
          />
        </div>
      </div>
    </div>
  );
};
const JobPricingStep2Mobile = () => {
  const {
    jobType,
    jobTypes,
    paymentMethods,
    jobTypeSelection,
    setJobType,
    next,
    prev,
    setpayload,
    payload,
    paymentMethod,
    setPaymentMethod,
    paymentMethodSelection,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
        height: "100%",
        position: "relative",
      }}
    >
      <div className={styles.pricingContainer}>
        <div className={styles.pricingContent}>
          {/* Job Type Section */}
          <div>
            <div className={styles.pricingQuestion}>
              <p className={styles.pricingQuestionText}>Will you be paying an Hourly Rate or a Fixed Price?</p>
            </div>
            <div className={styles.pricingOptions}>
              {jobTypes.map((item, index) => (
                <div
                  key={index}
                  className={styles.pricingOption}
                  style={{
                    border: jobType === item[1] ? "2px solid #179D52" : "2px solid #DFDFDF",
                    color: jobType === item[1] ? "#179D52" : "#585858",
                    backgroundColor: jobType === item[1] ? "var(--Selected-button-input, #179D5233)" : "",
                  }}
                  onClick={() => {
                    setJobType(item[1] as number);
                    if (item[1] === 1) {
                      setpayload({
                        ...payload,
                        paymentType: item[1] as number,
                        willPayOvertime: false,
                        overtimeRate: null,
                      });
                    }
                  }}
                >
                  <p className={styles.pricingOptionText}>{item[0]}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Payment Method Section */}
          {jobType !== -1 && (
            <div>
              <div className={styles.pricingQuestion}>
                <p className={styles.pricingQuestionText}>How will you pay your Helper for this job?</p>
              </div>
              <div className={styles.pricingOptions}>
                {paymentMethods.map((item, index) => (
                  <div
                    key={index}
                    className={styles.pricingOption}
                    style={{
                      border: paymentMethod === index ? "2px solid #179D52" : "2px solid #DFDFDF",
                      color: paymentMethod === index ? "#179D52" : "#585858",
                      backgroundColor: paymentMethod === index ? "var(--Selected-button-input, #179D5233)" : "",
                    }}
                    onClick={() => setPaymentMethod(index)}
                  >
                    <p className={styles.pricingOptionText}>{item}</p>
                  </div>
                ))}
                {paymentMethod === 1 && (
                  <p className={styles.pricingBankNote}>When you award this job you will be provided with the helper's bank details.</p>
                )}
              </div>
            </div>
          )}
        </div>

        <BackButtonPortal id="back-button-portal">
          <div
            onClick={() => {
              setpayload({
                ...payload,
                paymentType: jobType,
                helperPaymentMethod: paymentMethod + 1,
              });
              prev("jobpricing-step1"); // Navigate to the previous step
            }}
          >
            <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
          </div>
        </BackButtonPortal>
      </div>

      {/* <CustomFooterButton label="Go Back" onClick={handlePrev} /> */}
      <CustomFooterButton
        label="Next"
        isDisabled={jobType === -1 || paymentMethod === -1}
        onClick={() => {
          setpayload({
            ...payload,
            paymentType: jobType,
            helperPaymentMethod: paymentMethod + 1,
          });
          next("jobpricing-step3"); // Navigate to the next step
        }}
      />
    </div>
  );
};
