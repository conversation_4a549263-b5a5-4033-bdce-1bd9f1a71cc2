import { createSlice } from '@reduxjs/toolkit';
import { AccountSettingStateType } from '../types';

const initialSetting: AccountSettingStateType = {
    activeTabIndex: 0,
};

const accountSettingSlice = createSlice({
    name: 'Account Settings',
    initialState: initialSetting,
    reducers: {
        updateActiveTabIndex: (state, action) => {
            state.activeTabIndex = action.payload;
        },
    },
});

export const { updateActiveTabIndex } = accountSettingSlice.actions;

export default accountSettingSlice.reducer;
