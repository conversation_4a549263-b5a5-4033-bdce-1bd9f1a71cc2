.responsibilitiescontainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    justify-content: space-between;
    padding: 40px;
    /* width: 325px; */
}

.responsibilitiesheader h1{
    width: auto;
    height: 48px;
    font-weight: 700 ;
    font-size: 32px;
    color: #585858;
    line-height: 48px;   
}
.responsibilitiesprogressbar{
    width: auto; 
    max-width: 100%; 
    height: 7px; 
    margin-top: 1rem;
}

.responsibilitiesprogressbar > div {
    background-color: #179d52 !important; 
}
.responsibilitiesprogressbar > div > div {
    display: none; 
}
.responsibilitiesButton{
    background-color: #FFA500;
    border: none;
    border-radius: 10px;
    color: #FFFFFF;
    height: 40px;
    width: 150px;
}
.customCheckbox {
    appearance: none;
    width: 20px;
    min-width: 20px;
    min-height: 20px;
    height: 20px;
    border: 1px solid #585858;
    border-radius: 4px;
    cursor: pointer;
    outline: none;
  }
  
  .customCheckbox:checked {
    background-color: #179D52;
    border-color: #179D52;
    position: relative;
  }
  
  .customCheckbox:checked::after {
    content: '✔';
    position: absolute;
    color: white;
    font-size: 14px;
    left: 3px;
    top: 0;
  }
  .responsibilitiesButtonMobileDiv{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    height: min-content;
    box-shadow: 0 0 8px #00000040;
    background-color: #fff;
  }
  .responsibilitiesButtonMobile{
    background-color: #FFA500;
    border: none;
    border-radius: 10px;
    color: #FFFFFF;
    height: 40px;
    width: 150px;
}