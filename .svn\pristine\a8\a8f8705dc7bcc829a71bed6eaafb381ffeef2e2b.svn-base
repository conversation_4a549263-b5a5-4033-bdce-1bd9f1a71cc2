import { useState, useEffect, useRef } from "react";
import "../Common/styles/custom-homepage-card.css";

import { AppDispatch, RootState } from "../../store";
import { useDispatch, useSelector } from "react-redux";
import ProfileCompleteCongoLoader from "../Common/ProfileCompleteCongoLoader";
import CustomHomepageCard from "../Common/CustomHomepageCard";
import juggleLogo from "../../assets/images/juggle_white.png";
import WelcomeComponent from "../Common/WelcomeComponent";
import HelpdeskManager from "../../commonComponents/HelpdeskManager";
import { LuPackageCheck, LuSmilePlus } from "react-icons/lu";
import {
  UserSearchResponse,
  useSearchHook,
} from "../../hooks/SearchGeoSearchHook";
import useLoader from "../../hooks/LoaderHook";
import { FiMap } from "react-icons/fi";
import { OverlayPanel } from "primereact/overlaypanel";
import { Divider } from "primereact/divider";
import AllFilters from "../Common/AllFilters";
import menuIcon from "../../assets/images/Icons/Filter_alt.png";
import JuggleMaps from "../Parent/JuggleMaps";
import HomeHeaderBuisness from "./HomeHeaderBuisness";
import LeftHandUserPanel from "../Common/LeftHandUserPanel";
import styles from "../Common/styles/parent-home.module.css";
import {
  JobCategory,
  JobsCompletedFilter,
  JobTypeFilters,
  ReviewFilter,
  useFilterHook,
} from "../../hooks/useFilterHook";
import useIsMobile from "../../hooks/useIsMobile";
import { useNavigate } from "react-router-dom";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import {
  ConfirmationPopupGreen,
  useConfirmationPopup,
} from "../Common/ConfirmationPopup";
import { updateProfileActivationEnabled } from "../../store/slices/applicationSlice";
import { RxUpdate } from "react-icons/rx";
import ShimmerHelperCard from "../Common/ShimmerHelperCard";
import { IframeBridge } from "../../services/IframeBridge";

const BusinessHome = () => {
  const [profileCardDivWidth, setProfileCardDivWidth] = useState<number>(0);
  const [profileCardCol, setProfileCardCol] = useState<
    "col-12" | "col-6" | "col-4" | "col" | "col-3"
  >("col");
  const [showProfileCompletenessDialog, setShowProfileCompletenessDialog] =
    useState(true);
  const { enableLoader, disableLoader } = useLoader();
  const [useSearchResponse, setUseSearchResponse] =
    useState<UserSearchResponse>(null);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { sideBarIsOpened: sidebarIsOpen, shouldShowProfileActivation } =
    useSelector((state: RootState) => state.applicationState);
  const { isMobile } = useIsMobile();
  // const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth <= 768);
  const [numberOfRows, setNumberOfRows] = useState<number>(0);
  const profileCardDivRef = useRef<HTMLDivElement>(null);
  const [showBackToMapView, setShowBackToMapView] = useState<boolean>(false);
  const op1 = useRef<OverlayPanel>(null);
  const op2 = useRef<OverlayPanel>(null);
  const op3 = useRef<OverlayPanel>(null);
  const isFirstRender = useRef(true);
  const [showAllFilters, setShowAllFilters] = useState<boolean>(false);
  const { defaultFilters } = useSelector(
    (state: RootState) => state.applicationState
  );
    const { inIframe } = useSelector((state: RootState) => state.applicationState);

  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [filtersApplied, setFiltersApplied] = useState<boolean>(false);
  const { updateFilters, refreshSearchResult } = useSearchHook((data) => {
    setUseSearchResponse((prev) => {
      if (!data || !data.results) {
        disableLoader();
        return prev;
      }

      if (data.results.length === 0) {
        disableLoader();
        return prev;
      }

      const prevResultsMap = new Map(
        (prev?.results || []).map((item) => [item.id, item])
      );
      let resultsChanged = false;

      const newResults = data.results.map((newItem) => {
        const existingItem = prevResultsMap.get(newItem.id);

        if (!existingItem) {
          resultsChanged = true;
          return newItem;
        }

        if (JSON.stringify(existingItem) !== JSON.stringify(newItem)) {
          resultsChanged = true;
          return newItem;
        }

        return existingItem;
      });

      if (!resultsChanged) {
        disableLoader();
        return prev;
      }

      const finalResults = [...(prev?.results || [])];
      newResults.forEach((newItem) => {
        const existingIndex = finalResults.findIndex(
          (item) => item.id === newItem.id
        );
        if (existingIndex === -1) {
          finalResults.push(newItem);
        } else {
          finalResults[existingIndex] = newItem;
        }
      });

      return {
        ...prev,
        results: finalResults,
        total: data?.total || prev?.total || 0,
      };
    });
    disableLoader();
  });

  const {
    selectedJob,
    jobTypeFilters,
    reviewFilter,
    jobsCompletedFilter,
    isApplyButtonDisabled,
    getJobTypeOptions,
    handleJobSelect,
    handleJobTypeChange,
    handleReviewFilterChange,
    handleJobsCompletedFilterChange,
    handleApplyJobTypeFilters,
  } = useFilterHook({
    defaultFilters,
    onFilterChange: (filters) =>
      updateFilters((prev) => ({
        ...prev,
        pageIndex: 1,
        filters: prev.filters.map((data) => {
          const newFilter = filters[data.field];
          return newFilter || data;
        }),
      })),
    enableLoader,
    setSearchResponse: setUseSearchResponse,
  });

  const getSelectedFilterValues = (
    category: JobCategory,
    filters: JobTypeFilters
  ) => {
    const options = getJobTypeOptions(category);
    return Object.entries(filters)
      .filter(([_, value]) => value)
      .map(([key]) => {
        const option = options.find((opt) => opt.key === key);
        return option ? option.value : null;
      })
      .filter((value) => value !== null);
  };
  useEffect(() => {
    if (isFirstRender.current) {
      const initialFilters = getInitialJobTypeFilters("Childcare");
      const initialValues = getSelectedFilterValues(
        "Childcare",
        initialFilters
      );

      updateFilters((prev) => ({
        ...prev,
        pageIndex: 1,
        filters: prev.filters.map((data) => {
          if (data.field === "jobTypes") {
            return {
              field: "jobTypes",
              operator: "eq",
              value: initialValues,
            };
          }
          return data;
        }),
      }));

      isFirstRender.current = false;
    }
  }, []);
  function getInitialJobTypeFilters(category: JobCategory): JobTypeFilters {
    switch (category) {
      case "Childcare":
        return {
          babysitting: true,
          nannying: true,
          beforeSchool: true,
          afterSchool: true,
        };

      case "Odd Jobs":
        return {
          laundry: true,
          errands: true,
          outdoorChores: true,
          elderlyHelp: true,
          otherOddJobs: true,
        };
    }
  }

  const handlePagination = () => {
    enableLoader();
    updateFilters((prev) => {
      return {
        ...prev,
        pageIndex: prev.pageIndex + 1,
      };
    });
  };
  useEffect(() => {
    setUseSearchResponse(null);
    updateFilters(() => defaultFilters);
  }, [window.location]);

  const reviews = [
    { value: "All", label: "All" },
    { value: "Over-4-stars", label: "Over 4 stars" },
    { value: "Over-4-stars-with-reviews", label: "Over 4 stars with reviews" },
  ];

  const JobsCompleted = [
    { value: "All", label: "All" },
    { value: "1-or-more-jobs", label: "1 or more-jobs" },
    { value: "5-or-more-jobs", label: "5 or more jobs" },
  ];

  const handleBackToMapView = () => {
    if (profileCardDivRef.current && showBackToMapView) {
      profileCardDivRef.current.style.height = "50%";
      profileCardDivRef.current.scrollTo({ top: 0, behavior: "smooth" });
      setShowBackToMapView(false);
    }
  };

  const closeJobType = () => {
    if (op1.current) {
      op1.current.hide();
    }
  };

  const closeReviews = () => {
    if (op2.current) {
      op2.current.hide();
    }
  };

  const closeJobsCompleted = () => {
    if (op3.current) {
      op3.current.hide();
    }
  };

  useEffect(() => {
    if (!profileCardDivRef.current) return;
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        if (entry.target === profileCardDivRef.current) {
          setProfileCardDivWidth(entry.contentRect.width);
        }
      }
    });
    resizeObserver.observe(profileCardDivRef.current);
    return () => {
      if (profileCardDivRef.current) {
        resizeObserver.unobserve(profileCardDivRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (profileCardDivWidth <= 850) {
      setProfileCardCol("col-12");
    } else if (profileCardDivWidth <= 950) {
      setProfileCardCol("col-6");
    } else if (profileCardDivWidth <= 1300) {
      setProfileCardCol("col-6");
    } else if (profileCardDivWidth >= 1500) {
      setProfileCardCol("col-3");
    } else {
      setProfileCardCol("col-4");
    }
  }, [profileCardDivWidth]);

  useEffect(() => {
    if (!profileCardDivRef.current) return;

    // Function to update width
    const updateWidth = () => {
      const newWidth = profileCardDivRef.current.offsetWidth;
      setProfileCardDivWidth(newWidth);
    };

    // Set initial width
    updateWidth();

    // Add window resize listener for real-time updates
    window.addEventListener("resize", updateWidth);

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener("resize", updateWidth);
    };
  }, []); // Empty dependency array to set up listener once on mount
  useEffect(() => {
    if (isMobile) {
      if (profileCardDivWidth <= 850) {
        setProfileCardCol("col-6");
      } else if (profileCardDivWidth <= 950) {
        setProfileCardCol("col-6");
      } else if (profileCardDivWidth <= 1300) {
        setProfileCardCol("col-4");
      } else if (profileCardDivWidth <= 1324) {
        setProfileCardCol("col-4");
      }
      else if (profileCardDivWidth <= 1500) {
        setProfileCardCol("col-3");
      }
      else {
        setProfileCardCol("col-4");
      }

    }
  }, [profileCardDivWidth, isMobile]);

  // useEffect(() => {
  //     const handleResize = () => {
  //         setIsMobile(window.innerWidth <= 768);
  //     };
  //     window.addEventListener('resize', handleResize);
  //     return () => window.removeEventListener('resize', handleResize);
  // }, []);

  useEffect(() => {
    if (profileCardDivRef.current) {
      const profileCards =
        profileCardDivRef.current.querySelectorAll<HTMLElement>(
          ".profile-card2"
        );
      const rowPositions = new Set<number>();
      profileCards.forEach((card) => {
        rowPositions.add(card.offsetTop);
      });
      setNumberOfRows(rowPositions.size);
    }
  }, [useSearchResponse, isMobile]);

  const [isAbove1024, setIsAbove1024] = useState(window.innerWidth > 1024); // New state

  useEffect(() => {
    const handleResize = () => setIsAbove1024(window.innerWidth > 1024);
    window.addEventListener("resize", handleResize);
    handleResize(); // Set initial value
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const handleScroll = () => {
    if (profileCardDivRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        profileCardDivRef.current;
      if (scrollTop + clientHeight >= scrollHeight - 2000) {
        setShowBackToMapView(true);
        profileCardDivRef.current.style.height = "80vh";
      }
    }
  };

  useEffect(() => {
    const container = profileCardDivRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, [numberOfRows, isMobile, useSearchResponse]);

  const renderHeader = () => (
    <header
      className={`w-full flex align-items-center justify-content-center  overflow-hidden ${styles.headerGradient}`}
    >
      <img className={styles.juggleLogo} src={juggleLogo} alt="juggle logo" />
      <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
    </header>
  );
  const HorizontalNavigation = {
    Item: (
      index: number,
      icon: React.ReactNode,
      label: string,
      onClick: () => void
    ) => {
      const isPostJob = label === "Profiles"; // Always active for Post Job
      return (
        <div className={styles.horizontalnavitemcontainer}>
          <div
            className={`${styles.horizontalNavItem} ${isPostJob ? styles.activeTab : ""
              }`}
            onClick={onClick}
          >
            {icon}
            <span className={styles.navLabel}>{label}</span>
          </div>
          {/* Active line below only for Post Job */}
          {isPostJob && <div className={styles.activeLine}></div>}
        </div>
      );
    },
  };
  const resetFiltersRef = useRef(null);
  return !isMobile ? (
    <div className="" style={{ height: "100vh", width: "100vw" }}>
      <HomeHeaderBuisness />
      <JuggleMaps
        height={`calc(100% - ${showBackToMapView ? "80vh" : "50%"})`}
        width="calc(100% - 250px)"
        position="absolute"
        right={0}
      />
      {shouldShowProfileActivation &&
        sessionInfo.data["profileCompleteness"] >= 100 && (
          <ProfileCompleteCongoLoader />
        )}
      <AllFilters
        enable={showAllFilters}
        availableHelpers={useSearchResponse}
        selectedJobCategory={selectedJob}
        onClose={(payload) => {
          setShowAllFilters(false);
          if (payload === null) return;
          enableLoader();
          setUseSearchResponse(null);
          updateFilters(() => payload);
          setFiltersApplied(true);
        }}
        onResetRef={(resetFn) => {
          resetFiltersRef.current = resetFn;
        }}
      />
      {showBackToMapView && (
        <div
          className="flex cursor-pointer absolute "
          onClick={handleBackToMapView}
          style={{
            left: "60%",
            transform: "translateX(-50%)",
            bottom: "82vh",
            zIndex: "900",
          }}
        >
          <div
            className="flex align-items-center pl-3 pr-3"
            style={{
              border: "2px solid rgba(88, 88, 88, 0.3)",
              borderRadius: "20px",
              height: "44px",
              backgroundColor: "#585858",
            }}
          >
            <FiMap style={{ color: "#FFFFFF" }} />
            &nbsp;&nbsp;
            <p
              className="font-bold"
              style={{ color: "#FFFFFF", fontSize: "12px" }}
            >
              Back to map view
            </p>
          </div>
        </div>
      )}
      <div
        className="profile-card-container "
        style={{
          width: sidebarIsOpen ? "calc(100% - 246px)" : "calc(100% - 83px)",
          minHeight: "auto",
          overflowY: "auto",
        }}
        ref={profileCardDivRef}
      >
        <div
          className="buttons-container flex justify-content-between flex-column"
          style={{
            position: "fixed",
            backgroundColor: "#FFFFFF",
            width: "calc(100% - 336px)",
            paddingBlock: "10px",
            zIndex: "20",
            right: "40px",
            borderBottom: "2px solid rgba(88,88,88,0.07)",
          }}
        >
          <div
            className="flex jobs align-items-center cursor-pointer relative"
            style={{
              textWrap: "nowrap",
            }}
          >
            <div className="flex justify-content-evenly mt-2 gap-2 pl-3 pr-2">
              <div className="flex">
                {getJobTypeOptions(selectedJob).map(({ key, label }) => (
                  <div
                    key={key}
                    className="mb-2 flex justify-content-start align-items-center"
                  >
                    {/* Checkbox for other job types */}
                    <input
                      type="checkbox"
                      id={key}
                      checked={(jobTypeFilters as any)[key]}
                      onChange={() => {
                        handleJobTypeChange(key);
                      }}
                      className="custom-checkbox"
                    />
                    <label
                      htmlFor={key}
                      className="m-1 cursor-pointer"
                      style={{ fontSize: "12px" }}
                    >
                      {label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex cursor-pointer mt-1 gap-3">
              <div
                className="home-all-filter-btn pl-2 pr-2"
                style={{
                  border: "2px solid rgba(88, 88, 88, 0.3)",
                  borderRadius: "15px",
                  height: "43px",
                }}
                onClick={() => {
                  setShowAllFilters(true);
                }}
              >
                <img
                  alt="Doller"
                  src={menuIcon}
                  style={{ width: "20px", color: "#FFFFFF" }}
                />
                &nbsp;
                <p
                  className="font-medium p-0 m-0"
                  style={{
                    color: "#585858",
                    textWrap: "nowrap",
                    fontSize: "16px",
                  }}
                >
                  All Filters
                </p>
              </div>

              {filtersApplied && (
                <div
                  className="home-all-filter-btn pl-2 pr-2"
                  style={{
                    border: "2px solid rgba(88, 88, 88, 0.3)",
                    borderRadius: "15px",
                    height: "43px",
                  }}
                  onClick={() => {
                    enableLoader();
                    // Call the resetAllFilters function from the child component
                    if (resetFiltersRef.current) {
                      resetFiltersRef.current();
                    }
                    if (selectedJob === "Childcare") {
                      enableLoader();

                      // Reset filters for Childcare
                      handleJobSelect("Childcare");
                      updateFilters(() => defaultFilters);

                      setUseSearchResponse(null); // Clear search results
                      setFiltersApplied(false); // Mark filters as cleared

                      setTimeout(() => {
                        disableLoader();
                      }, 1000);

                      return;
                    }
                    // Also update parent state
                    setUseSearchResponse(null);
                    updateFilters(() => defaultFilters);
                    setFiltersApplied(false);
                    setTimeout(() => {
                      disableLoader();
                    }, 1000);
                  }}
                >
                  <img
                    alt="Doller"
                    src={menuIcon}
                    style={{ width: "20px", color: "#FFFFFF" }}
                  />
                  &nbsp;
                  <p
                    className="font-medium p-0 m-0"
                    style={{
                      color: "#585858",
                      textWrap: "nowrap",
                      fontSize: "16px",
                    }}
                  >
                    Clear Filters
                  </p>
                </div>
              )}
            </div>

          </div>
          <div
            className=""
            style={{ backgroundColor: "#FFFFFF", paddingInline: "30px" }}
          >
            <p
              style={{
                color: "#787777",
                textWrap: "nowrap",
                fontSize: "18px",
                fontWeight: "400",
              }}
            >
              <span>
                <b
                  style={{
                    height: "100px",
                    fontSize: "24px",
                    color: "#585858",
                    fontWeight: "700",
                  }}
                >
                  {useSearchResponse !== null
                    ? useSearchResponse.results.length
                    : 0}
                  {" of "}
                  {useSearchResponse !== null ? useSearchResponse.total : 0}{" "}
                </b>
              </span>
              Candidates Near Me
            </p>
          </div>
        </div>

        <div className="container" style={{ backgroundColor: "#FFFFFF", marginTop: "169px" }}>
          <div className="grid flex-grow-1">
            {useSearchResponse !== null &&
              useSearchResponse.results.map((profile, index) => (
                <div key={index} className={`${profileCardCol} profile-card2`}>
                  <CustomHomepageCard
                    {...profile}
                    refresh={() => {
                      enableLoader();
                      setUseSearchResponse(null);
                      setTimeout(() => {
                        refreshSearchResult();
                      }, 1000);
                    }}
                  />
                </div>
              ))}
          </div>
          <div className="flex justify-content-center w-full mb-4 mt-3">
            <div
              className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
              onClick={handlePagination}
              style={{
                border: "1px solid #000000",
                borderRadius: "5px",
                fontSize: "14px",
                color: "#585858",
                height: "42px",
              }}
            >
              See More Helpers
            </div>
          </div>
        </div>
      </div>
      {!sessionInfo.loading &&
        Number(sessionInfo.data["profileCompleteness"]) < 100 && (
          <WelcomeComponent
            dialogBoxState={showProfileCompletenessDialog}
            reverseWhen={65}
            profileCompletion={Number(sessionInfo.data["profileCompleteness"])}
            userName={sessionInfo.data["firstName"]}
            closeClicked={() => setShowProfileCompletenessDialog(false)}
            onExploreClicked={() => setShowProfileCompletenessDialog(false)}
          />
        )}
      <LeftHandUserPanel activeindex={0} />
      <HelpdeskManager />
    </div>
  ) : (
    <>
      {renderHeader()}
      <div className="" style={{
        height: "100vh", width: isAbove1024 && sidebarIsOpen ? "calc(100vw - 288px)" : "100vw",
        marginLeft: isAbove1024 && sidebarIsOpen ? "288px" : "0",
        position: isAbove1024 ? "relative" : undefined,
      }}>
        <HomeHeaderBuisness />
        <ConfirmationPopupGreen confirmationProps={confirmationProps} />
        <div>
          {/* <JuggleMaps
        height={`calc(100% - ${showBackToMapView ? '80vh' : '350px'})`}
        width='calc(100% - 255px)'
        position='absolute'
        top={0}
        right={0}
    /> */}

          <div className={styles.tabContainer}>
            {[
              {
                item: ({ index, itemStyles }) =>
                  HorizontalNavigation.Item(
                    index,
                    <i
                      style={{ fontSize: "20px" }}
                      className={`pi pi-users ${itemStyles.navIcon}`}
                    ></i>,
                    "Profiles",
                    () => {
                      const path =
                        clientType === 2 ? "/business-home" : "/parent-home";
                      navigate(path);
                    }
                  ),
              },
              // Conditionally render "My Jobs" if jobPlace > 0, otherwise render "Post Job"
              sessionInfo.data?.["client"]?.["jobsPlaced"] > 0
                ? {
                  item: ({ index, itemStyles }) =>
                    HorizontalNavigation.Item(
                      index,
                      <i className={`pi pi-list ${itemStyles.navIcon}`}></i>,
                      "My Jobs",
                      () => {
                        const path =
                          clientType === 2
                            ? "/business-home/manage-jobs?jobId=-1&activeTab=0"
                            : "/parent-home/manage-jobs?jobId=-1&activeTab=0";
                        navigate(path);
                      }
                    ),
                }
                : {
                  item: ({ index, itemStyles }) =>
                    HorizontalNavigation.Item(
                      index,
                      <i className={`pi pi-plus ${itemStyles.navIcon}`}></i>,
                      "Post Job",
                      () => {
                        if (sessionInfo.data["profileCompleteness"] <= 99) {
                          showConfirmationPopup(
                            "Complete",
                            `Your account must be 100% complete before proceeding.`,
                            "Complete",
                            <RxUpdate style={{ fontSize: "20px" }} />,
                            () => {
                              dispatch(updateProfileActivationEnabled(true));
                            }
                          );
                          return; // Prevent navigation
                        }
                        const path =
                          clientType === 2
                            ? "/business-home/post-job"
                            : "/parent-home/post-job";
                        navigate(path);
                      }
                    ),
                },
              // {
              //   item: ({ index, itemStyles }) =>
              //     HorizontalNavigation.Item(
              //       index,
              //       <i className={`pi pi-comments ${itemStyles.navIcon}`}></i>,
              //       "Chat",
              //       () => {
              //         if (sessionInfo.data["profileCompleteness"] <= 99) {
              //           showConfirmationPopup(
              //             "Complete",
              //             `Your account must be 100% complete before proceeding.`,
              //             "Complete",
              //             <RxUpdate style={{ fontSize: "20px" }} />,
              //             () => {
              //               dispatch(updateProfileActivationEnabled(true));
              //             }
              //           );
              //           return; // Prevent navigation
              //         }

              //         const path =
              //           clientType === 2
              //             ? "/business-home/inAppChat"
              //             : "/parent-home/inAppChat";
              //         navigate(path);
              //       }
              //     ),
              // },
              {
                item: ({ index, itemStyles }) =>
                  HorizontalNavigation.Item(
                    index,
                    <i className={`pi pi-file ${itemStyles.navIcon}`}></i>,
                    "Payments",
                    () => {
                      // if (sessionInfo.data["profileCompleteness"] <= 99) {
                      //   showConfirmationPopup(
                      //     "Complete",
                      //     `Your account must be 100% complete before proceeding.`,
                      //     "Complete",
                      //     <RxUpdate style={{ fontSize: "20px" }} />,
                      //     () => {
                      //       dispatch(updateProfileActivationEnabled(true));
                      //     }
                      //   );
                      //   return; // Prevent navigation
                      // }

                      // const path =
                      //   clientType === 2
                      //     ? "/business-home/timesheet/helper-confirm"
                      //     : "/parent-home/timesheet/helper-confirm";
                      // navigate(path);
                      if (!inIframe) {
                  
                 const path = clientType === 2 ? "/business-home/timesheet/helper-confirm" : "/parent-home/timesheet/helper-confirm";
                  navigate(path);
                }
                if (clientType === 0) {
                  IframeBridge.sendToParent({
                    type: "helperInAppPaymentsNavigate",
                  });
                } else {
                  IframeBridge.sendToParent({
                    type: "inAppPaymentsNavigate",
                  });
                }
                    }
                  ),
              },
            ].map((tab, index) => (
              <div key={index} className={styles.tabItem}>
                {tab.item({ index, itemStyles: styles })}
              </div>
            ))}
          </div>
          {shouldShowProfileActivation &&
            sessionInfo.data["profileCompleteness"] >= 100 && (
              <ProfileCompleteCongoLoader />
            )}
          <AllFilters
            enable={showAllFilters}
            availableHelpers={useSearchResponse}
            selectedJobCategory={selectedJob}
            onClose={(payload) => {
              setShowAllFilters(false);
              if (payload === null) return;
              enableLoader();
              setUseSearchResponse(null);
              updateFilters(() => payload);
              setFiltersApplied(true);
            }}
            onResetRef={(resetFn) => {
              resetFiltersRef.current = resetFn;
            }}
          />
          {/* {showBackToMapView && (
        <div
            className='flex cursor-pointer absolute '
            onClick={handleBackToMapView}
            style={{
                left: '60%',
                transform: 'translateX(-50%)',
                bottom: '82vh',
                zIndex: '900',
            }}
        >
            <div
                className='flex align-items-center pl-3 pr-3'
                style={{
                    border: '2px solid rgba(88, 88, 88, 0.3)',
                    borderRadius: '20px',
                    height: '44px',
                    backgroundColor: '#585858',
                }}
            >
                <FiMap style={{ color: '#FFFFFF' }} />
                &nbsp;&nbsp;
                <p className='font-bold' style={{ color: '#FFFFFF', fontSize: '12px' }}>
                    Back to map view
                </p>
            </div>
        </div>
    )} */}
          <div className={styles.profileCardContainer} ref={profileCardDivRef}>
            <div
              className="buttons-container flex justify-content-between"
              style={{
                backgroundColor: "#FFFFFF",
                flexDirection: "column",
                paddingBlock: "10px",
                borderBottom: "2px solid rgba(88,88,88,0.07)",
              }}
            >
              <div
                className={`${styles.jobmobile}`}
                style={{
                  boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                  textWrap: "nowrap",
                }}
              >
                <div
                  className={`job-option ${selectedJob === "Childcare" ? "active" : ""
                    }`}
                  onClick={() => handleJobSelect("Childcare")}
                >
                  <p>
                    <LuSmilePlus /> &nbsp;Childcare
                  </p>
                </div>
                <div
                  className={`job-option ${selectedJob === "Tutoring" ? "active" : ""
                    }`}
                  onClick={() => handleJobSelect("Tutoring")}
                >
                  {/* <p>
                                <HiOutlineBookOpen /> &nbsp;Tutoring
                            </p> */}
                </div>
                <div
                  className={`job-option ${selectedJob === "Odd Jobs" ? "active" : ""
                    }`}
                  onClick={() => handleJobSelect("Odd Jobs")}
                >
                  <p>
                    <LuPackageCheck /> &nbsp;Odd Jobs
                  </p>
                </div>
              </div>
              <div
                style={{ flexDirection: "column", marginTop: "8px", gap: "5px" }}
                className="flex justify-content-between align-items-baseline"
              >
                <div className="flex justify-content-evenly mt-2 gap-2 pr-2">
                  <button
                    className="home-filter-btn cursor-pointer"
                    onClick={(e) => op1.current.toggle(e)}
                  >
                    {" "}
                    Job Type &nbsp;<span className="pi pi-angle-down"></span>{" "}
                  </button>

                  <OverlayPanel
                    ref={op1}
                    className="overflow-hidden mt-2"
                    style={{
                      width: "223px",
                      height: "auto",
                      borderRadius: "20px",
                    }}
                  >
                    <div>
                      <div>
                        <button
                          className="home-close-icon"
                          onClick={closeJobType}
                        >
                          &times;
                        </button>
                      </div>
                      {getJobTypeOptions(selectedJob).map(({ key, label }) => (
                        <div
                          key={key}
                          className="mb-2 flex justify-content-start align-items-center"
                        >
                          {/* <input
                                                    type='radio'
                                                    id={key}
                                                    name='tutoring-type'
                                                    checked={
                                                        (
                                                            jobTypeFilters as TutoringJobTypeFilter
                                                        ).selectedOption === key
                                                    }
                                                    onChange={() => handleJobTypeChange(key)}
                                                    className='custom-radio'
                                                /> */}

                          <input
                            type="checkbox"
                            id={key}
                            checked={(jobTypeFilters as any)[key]}
                            onChange={() => handleJobTypeChange(key)}
                            className="custom-checkbox"
                          />

                          <label
                            htmlFor={key}
                            className="ml-2 cursor-pointer"
                            style={{ fontSize: "12px" }}
                          >
                            {label}
                          </label>
                        </div>
                      ))}

                      <Divider />
                      <div
                        className="mt-2 mb-2"
                        style={{
                          float: "right",
                          width: "78px",
                          height: "28px",
                        }}
                      >
                        <button
                          className={`home-apply-btn ${isApplyButtonDisabled ? "disabled" : "enabled"
                            }`}
                          onClick={() => {
                            handleApplyJobTypeFilters();
                            closeJobType();
                          }}
                          disabled={isApplyButtonDisabled}
                          style={{
                            backgroundColor: isApplyButtonDisabled
                              ? "rgba(255,165,0,0.5)"
                              : "rgba(255,165,0,1)",
                            fontWeight: isApplyButtonDisabled ? 400 : 700,
                          }}
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  </OverlayPanel>
                  <button
                    className="home-filter-btn cursor-pointer"
                    onClick={(e) => op2.current.toggle(e)}
                  >
                    {" "}
                    Reviews &nbsp;<span className="pi pi-angle-down"></span>{" "}
                  </button>
                  <OverlayPanel
                    ref={op2}
                    className="overflow-hidden mt-2"
                    style={{
                      width: "223px",
                      height: "auto",
                      borderRadius: "20px",
                    }}
                  >
                    <div>
                      <div>
                        <button
                          className="home-close-icon"
                          onClick={closeReviews}
                        >
                          &times;
                        </button>
                      </div>
                      {reviews.map((option) => (
                        <div
                          key={option.value}
                          className="mb-2 flex justify-content-start align-items-center"
                        >
                          <input
                            type="radio"
                            id={option.value}
                            name="reviews"
                            value={option.value}
                            checked={reviewFilter === option.value}
                            onChange={() => {
                              handleReviewFilterChange(
                                option.value as ReviewFilter
                              );
                              closeReviews();
                            }}
                            className="cursor-pointer"
                            style={{ display: "none" }}
                          />
                          <div
                            className="flex justify-content-center align-items-center cursor-pointer"
                            style={{
                              height: "18px",
                              width: "18px",
                              borderRadius: "50%",
                              padding: "1px",
                              border: `1px solid ${reviewFilter === option.value
                                ? "#179d52"
                                : "#DFDFDF"
                                }`,
                            }}
                            onClick={() => {
                              handleReviewFilterChange(
                                option.value as ReviewFilter
                              );
                              closeReviews();
                            }}
                          >
                            {reviewFilter === option.value && (
                              <div
                                style={{
                                  height: "100%",
                                  width: "100%",
                                  borderRadius: "50%",
                                  backgroundColor: "#179D52",
                                }}
                              />
                            )}
                          </div>
                          <label
                            htmlFor={option.value}
                            className="ml-2 cursor-pointer"
                            style={{ fontSize: "12px" }}
                          >
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </OverlayPanel>
                  <button
                    className="home-filter-btn cursor-pointer"
                    onClick={(e) => op3.current.toggle(e)}
                  >
                    {" "}
                    Jobs Completed &nbsp;
                    <span className="pi pi-angle-down"></span>{" "}
                  </button>
                  <OverlayPanel
                    ref={op3}
                    className="overflow-hidden mt-2"
                    style={{
                      width: "223px",
                      height: "auto",
                      borderRadius: "20px",
                    }}
                  >
                    <div>
                      <div>
                        <button
                          className="home-close-icon"
                          onClick={closeJobsCompleted}
                        >
                          &times;
                        </button>
                      </div>
                      {JobsCompleted.map((option) => (
                        <div
                          key={option.value}
                          className="mb-2 flex justify-content-start align-items-center "
                        >
                          <input
                            type="radio"
                            id={option.value}
                            name="Jobscompleted"
                            value={option.value}
                            checked={jobsCompletedFilter === option.value}
                            onChange={() => {
                              handleJobsCompletedFilterChange(
                                option.value as JobsCompletedFilter
                              );
                              closeJobsCompleted();
                            }}
                            className="cursor-pointer"
                            style={{ display: "none" }}
                          />
                          <div
                            className="flex justify-content-center align-items-center cursor-pointer"
                            style={{
                              height: "18px",
                              width: "18px",
                              borderRadius: "50%",
                              padding: "1px",
                              border: `1px solid ${jobsCompletedFilter === option.value
                                ? "#179d52"
                                : "#DFDFDF"
                                }`,
                            }}
                            onClick={() => {
                              handleJobsCompletedFilterChange(
                                option.value as JobsCompletedFilter
                              );
                              closeJobsCompleted();
                            }}
                          >
                            {jobsCompletedFilter === option.value && (
                              <div
                                style={{
                                  height: "100%",
                                  width: "100%",
                                  borderRadius: "50%",
                                  backgroundColor: "#179D52",
                                }}
                              />
                            )}
                          </div>
                          <label
                            htmlFor={option.value}
                            className="ml-2 cursor-pointer"
                            style={{ fontSize: "12px" }}
                          >
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </OverlayPanel>
                </div>
                <div className="flex cursor-pointer mt-1 gap-3">
                  {/* All Filters Button */}
                  <div
                    className="home-all-filter-btn pl-2 pr-2"
                    style={{
                      border: "1px solid rgba(88, 88, 88, 0.3)",
                      borderRadius: "15px",
                      height: "30px",
                    }}
                    onClick={() => {
                      setShowAllFilters(true);
                    }}
                  >
                    <img
                      alt="Doller"
                      src={menuIcon}
                      style={{ width: "20px", color: "#FFFFFF" }}
                    />
                    &nbsp;
                    <p
                      className="font-medium"
                      style={{
                        color: "#585858",
                        textWrap: "nowrap",
                        fontSize: "12px",
                      }}
                    >
                      All Filters
                    </p>
                  </div>

                  {/* Clear Filters Button (Only visible when filtersApplied is true) */}
                  {filtersApplied && (
                    <div
                      className="home-all-filter-btn pl-2 pr-2"
                      style={{
                        border: "1px solid rgba(88, 88, 88, 0.3)",
                        borderRadius: "15px",
                        height: "30px",
                      }}
                      onClick={() => {
                        enableLoader();
                        if (resetFiltersRef.current) {
                          resetFiltersRef.current(); // Reset child filters
                        }
                        setUseSearchResponse(null); // Reset search response
                        updateFilters(() => defaultFilters); // Update filters to default
                        setFiltersApplied(false); // Mark filters as cleared
                        setTimeout(() => {
                          disableLoader();
                        }, 1000);
                      }}
                    >
                      <img
                        alt="Doller"
                        src={menuIcon}
                        style={{ width: "20px", color: "#FFFFFF" }}
                      />
                      &nbsp;
                      <p
                        className="font-medium"
                        style={{
                          color: "#585858",
                          textWrap: "nowrap",
                          fontSize: "12px",
                        }}
                      >
                        Clear Filters
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={`${!isMobile && "xl:mt-7"}`} style={{ backgroundColor: "#FFFFFF" }}>
              <p
                style={{
                  color: "#787777",
                  textWrap: "nowrap",
                  fontSize: "10px",
                  fontWeight: "400",
                }}
              >
                <span>
                  <b
                    style={{
                      height: "100px",
                      fontSize: "16px",
                      color: "#585858",
                      fontWeight: "700",
                    }}
                  >
                    {useSearchResponse !== null
                      ? useSearchResponse.results.length
                      : 0}
                    {" of "}
                    {useSearchResponse !== null
                      ? useSearchResponse.total
                      : 0}{" "}
                  </b>
                </span>
                {selectedJob === "Tutoring"
                  ? "Candidates Near Me"
                  : "Candidates Near Me"}
              </p>
            </div>
            <div
              className="container "
              style={{ backgroundColor: "#FFFFFF", margin: "0px" }}
            >
              <div className="grid flex-grow-1">
                {useSearchResponse !== null ? (
                  useSearchResponse.results.map((profile, index) => (
                    <div
                      key={index}
                      className={`${profileCardCol} profile-card2`}
                    >
                      <CustomHomepageCard
                        {...profile}
                        refresh={refreshSearchResult}
                      />
                    </div>
                  ))) : (
                  Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className={`${profileCardCol} profile-card2`}>
                      <ShimmerHelperCard />
                    </div>
                  ))

                )}
              </div>
              <div className="flex justify-content-center w-full mb-4 mt-3">
                <div
                  className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
                  onClick={handlePagination}
                  style={{
                    border: "1px solid #000000",
                    borderRadius: "5px",
                    fontSize: "14px",
                    color: "#585858",
                    height: "42px",
                  }}
                >
                  See More Helpers
                </div>
              </div>
            </div>
          </div>
          {/* {applicationState.interestInHomeAgedCareResponse && (
                <AgedCarePopup
                    visible={applicationState.interestInHomeAgedCareResponse}
                    onHide={hidePopup}
                />
            )}
            {conditions && (
                <EmployeeBenefits
                    isDialogVisible={applicationState.hasRequestedEmployeeBenefits}
                    onHide={hidePopupEmployee}
                />
            )} */}

          {!sessionInfo.loading &&
            Number(sessionInfo.data["profileCompleteness"]) < 100 && (
              <WelcomeComponent
                dialogBoxState={showProfileCompletenessDialog}
                reverseWhen={65}
                profileCompletion={Number(
                  sessionInfo.data["profileCompleteness"]
                )}
                userName={sessionInfo.data["firstName"]}
                closeClicked={() => setShowProfileCompletenessDialog(false)}
                onExploreClicked={() => setShowProfileCompletenessDialog(false)}
              />
            )}

          { }
          <LeftHandUserPanel activeindex={0} />
          <HelpdeskManager />
        </div>
      </div>
    </>
  );
};

export default BusinessHome;
