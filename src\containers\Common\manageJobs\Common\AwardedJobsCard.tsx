import React, { useEffect, useRef, useState } from 'react';
import home from '../../../../assets/images/Icons/home.png';
import calendar from '../../../../assets/images/Icons/manage_job.png';
import clock from '../../../../assets/images/Icons/clockstart.png';
import childcare from '../../../../assets/images/Icons/my_child.png';
import styles from '../../../Common/styles/job-card.module.css';
import { Divider } from 'primereact/divider';
import userProfile from '../../../../assets/images/Icons/user-profile.png';
import useIsMobile from '../../../../hooks/useIsMobile';
import c from '../../../../helper/juggleStreetConstants';
interface JobCardProps {
    date?: {
        day: string;
        date: string;
        month: string;
    };
    title?: number;
    jobOption?: string;
    jobType?: string;
    timeSlot?: string;
    location?: string;
    status?: string;
    expiry?: number;
    name?: string;
    image?: string;
    images?: Array<{ id: number; image: string }>;
    names?: Array<{ id: number; name: string }>;
    managedBy?: number;
    onClick?: () => void;
}

const getJobType = (jobCode: number): string => {
    if (jobCode === 256) {
        return 'Odd Job';
    } else if (jobCode === 64 || jobCode === 128) {
        return 'Tutoring';
    } else {
        return 'Childcare';
    }
};

const getJobTypeMobile = (jobCode: number): string => {
    const jobTypeMap: { [key: number]: string } = {
        0: 'Unspecified',
        1: 'Babysitting',
        2: 'Nannying',
        4: 'Before School Care',
        8: 'After School Care',
        12: 'Before & After School Care',
        16: 'Au Pair',
        32: 'Home Tutoring',
        64: 'Primary School Tutoring',
        128: 'High School Tutoring',
        256: 'One-Off Odd Job',
    };

    return jobTypeMap[jobCode] || 'Unknown';
};
// const defaultProps: JobCardProps = {
//   date: {
//     day: "FRI",
//     date: "24th",
//   },
//   // title: "Childcare",
//   jobOption: "Do it yourself",
//   jobType: "One off job",
//   timeSlot: "6:00pm - 9:00pm",
//   location: "9 Christie Street, South Brisbane",
//   status: "Awarded ",
//   name: "Ciara",
//   image: "",
// };

const AwardedJobsCard: React.FC<JobCardProps> = (props) => {
    const { isMobile } = useIsMobile();
    const {
        date,
        title,
        jobOption,
        jobType,
        timeSlot,
        location,
        status,
        name,
        image,
        images,
        expiry,
        names,
        managedBy,
        onClick,
    } = { ...props };
    const getOrdinalSuffix = (number) => {
        if (!number) return '';
        const lastDigit = number % 10;
        const lastTwoDigits = number % 100;

        if (lastTwoDigits >= 11 && lastTwoDigits <= 13) return 'th';
        switch (lastDigit) {
            case 1:
                return 'st';
            case 2:
                return 'nd';
            case 3:
                return 'rd';
            default:
                return 'th';
        }
    };
    return !isMobile ? (
        <div className='mb-5' style={{ width: '100%', maxWidth: '651px' }}>
            {/* Date Column */}
            <div className='flex items-stretch'>
                {/* Main Card */}
                <div className='flex flex-column w-full'>
                    <div
                        className='flex-1 w-full'
                        style={{
                            borderRadius: '10px',
                            border: '3px solid #179D52',
                            width: '100%',
                            maxWidth: '651px',
                            margin: '0 auto'
                        }}
                    >
                        <div className='pl-3 pr-3'>
                            {/* Header Section - Made responsive with flex-wrap */}
                            <div className='pr-2 pt-2 flex flex-wrap justify-content-between align-items-center gap-2'>
                                <div className='flex align-items-center gap-2 flex-wrap'>
                                    <p
                                        className='p-0 m-0 font-bold'
                                        style={{ fontSize: 'clamp(20px, 4vw, 30px)', color: '#585858' }}
                                    >
                                        {getJobType(title)}
                                    </p>
                                    <span style={{ fontSize: 'clamp(20px, 4vw, 30px)', color: "#585858" }}>-</span>
                                    <div
                                        className='flex align-items-center justify-content-start gap-2'
                                        style={{ minWidth: '100px' }}
                                    >
                                        <span className='font-bold' style={{ fontSize: 'clamp(20px, 4vw, 30px)', color: '#585858' }}>
                                            {date?.day}&nbsp;
                                        </span>
                                        <span
                                            className='font-semibold'
                                            style={{ fontSize: 'clamp(20px, 4vw, 30px)', color: '#585858', position: 'relative' }}
                                        >
                                            {date?.date} 
                                            <sup
                                                style={{
                                                    fontSize: 'clamp(12px, 2vw, 14px)',
                                                    position: 'absolute',
                                                    top: '-5px',
                                                    right: '-15px',
                                                }}
                                            >
                                                {getOrdinalSuffix(date?.date)}
                                            </sup>
                                        </span>
                                              <span className='font-bold ' style={{ fontSize: 'clamp(24px, 4vw, 30px)', color: '#585858' , marginLeft:"13px" }}>
                                             {date?.month}
                                        </span>
                                    </div>
                                </div>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-3 py-2`}
                                >
                                    <span className={styles.jobType}>{jobOption}</span>
                                </div>
                            </div>

                            {/* Job Details - Made responsive with proper wrapping */}
                            <div className='flex flex-wrap gap-2 mt-2'>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                >
                                    <img
                                        alt='Calendar'
                                        src={calendar}
                                        style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                    />
                                    <span className={styles.jobType}>{jobType}</span>
                                </div>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                >
                                    <img
                                        alt='Clock'
                                        src={clock}
                                        style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                    />
                                    <span className={styles.jobType}>{timeSlot}</span>
                                </div>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                >
                                    <img
                                        alt='Home'
                                        src={home}
                                        style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                    />
                                    <span className={styles.jobType}>{location}</span>
                                </div>
                            </div>
                            <Divider className='mt-3' />

                            {/* Status Section - Made responsive with column layout on small screens */}
                            <div className='pr-2 py-3 border-t'>
                                <div className='flex flex-wrap justify-content-between align-items-center gap-3'>
                                    <div className='space-y-1'>
                                        {managedBy === 20 && (
                                            <div className='flex align-items-center gap-2 flex-wrap'>
                                                <span className={`${styles.status}`}>Status:</span>
                                                <span className={`${styles.status} font-bold`}>
                                                    <button
                                                        className='font-bold'
                                                        style={{
                                                            backgroundColor: '#179D52',
                                                            border: 'none',
                                                            borderRadius: '10px',
                                                            width: 'clamp(90px, 20vw, 114px)',
                                                            height: 'clamp(36px, 6vw, 42px)',
                                                            color: '#FFFFFF',
                                                            fontSize: 'clamp(10px, 2vw, 12px)',
                                                        }}
                                                    >
                                                        {status}&nbsp; ✓
                                                    </button>
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    {jobType === 'One-off Job' ? (
                                        <div className='flex flex-wrap justify-content-center align-items-center gap-2'>
                                            <span className={`${styles.status}`}>
                                                Helper Awarded:
                                            </span>
                                            <span
                                                className={`${styles.status} font-bold ml-2`}
                                                style={{
                                                    fontSize: 'clamp(14px, 3vw, 16px)',
                                                    textDecoration: 'underline',
                                                }}
                                            >
                                                {name}
                                            </span>
                                            {image && (
                                                <div style={{ position: 'relative', marginLeft: '10px' }}>
                                                    <img
                                                        style={{
                                                            height: 'clamp(40px, 10vw, 53px)',
                                                            width: 'clamp(40px, 10vw, 53px)',
                                                            borderRadius: '50%',
                                                        }}
                                                        src={image}
                                                        alt='Helper Image'
                                                    />
                                                    <div
                                                        style={{
                                                            position: 'absolute',
                                                            top: '0',
                                                            right: '0',
                                                            height: 'clamp(12px, 3vw, 16px)',
                                                            width: 'clamp(13px, 3vw, 17px)',
                                                            borderRadius: '50%',
                                                            backgroundColor: 'green',
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        }}
                                                    >
                                                        <span
                                                            style={{
                                                                color: 'white',
                                                                fontSize: 'clamp(8px, 2vw, 10px)',
                                                                fontWeight: 'bold',
                                                            }}
                                                        >
                                                            ✔
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className='flex flex-wrap justify-content-center align-items-center gap-2'>
                                            <span className={`${styles.status}`}>
                                                Helper Awarded:
                                            </span>
                                            {names.map((n, index) => (
                                                <span
                                                    key={index}
                                                    className={`${styles.status} font-bold ml-2`}
                                                    style={{
                                                        fontSize: 'clamp(14px, 3vw, 16px)',
                                                        textDecoration: 'underline',
                                                    }}
                                                >
                                                    {n.name}
                                                </span>
                                            ))}

                                            {images.map((i, index) => (
                                                <div
                                                    key={index}
                                                    style={{
                                                        position: 'relative',
                                                        marginLeft: '10px',
                                                    }}
                                                >
                                                    <img
                                                        style={{
                                                            height: 'clamp(40px, 10vw, 53px)',
                                                            width: 'clamp(40px, 10vw, 53px)',
                                                            borderRadius: '50%',
                                                        }}
                                                        src={i.image}
                                                        alt='Helper Image'
                                                    />
                                                    <div
                                                        style={{
                                                            position: 'absolute',
                                                            top: '0',
                                                            right: '0',
                                                            height: 'clamp(12px, 3vw, 16px)',
                                                            width: 'clamp(13px, 3vw, 17px)',
                                                            borderRadius: '50%',
                                                            backgroundColor: 'green',
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        }}
                                                    >
                                                        <span
                                                            style={{
                                                                color: 'white',
                                                                fontSize: 'clamp(8px, 2vw, 10px)',
                                                                fontWeight: 'bold',
                                                            }}
                                                        >
                                                            ✔
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className='flex justify-content-center w-full'>
                            <button
                                onClick={onClick}
                                className='py-2 text-white font-bold mt-2 cursor-pointer'
                                style={{
                                    backgroundColor: '#FFA500',
                                    textDecoration: 'underline',
                                    fontSize: 'clamp(16px, 3vw, 20px)',
                                    border: 'none',
                                    borderBottomLeftRadius: '6px',
                                    borderBottomRightRadius: '6px',
                                    width: "100%"
                                }}
                            >
                                Job Details
                            </button>
                        </div>
                    </div>

                    {/* View Job Button - Made responsive */}

                </div>
            </div>
        </div>
    ) : (
        <div className='h-min mb-3 w-full'>
            {/* Date Column  */}
            <div className='flex items-stretch'>
                {/* Main Card  */}
                <div className='flex flex-column w-full'>
                    <div
                        className='flex-1 '
                        onClick={onClick}
                        style={{
                            borderRadius: '30px',
                            border: '2px solid #179D52',
                            width: '100%',
                        }}

                    >
                        <div>
                            <div className='flex flex-row justify-content-between align-items-center px-3 pt-3 pb-2'>
                                <div
                                    className='flex flex-row align-items-center'
                                    style={{ width: '100px' }}
                                >
                                    <span
                                        className='font-semibold'
                                        style={{ fontSize: '18px', color: '#585858' }}
                                    >
                                        {date?.day}
                                        {','}
                                    </span>
                                    <span
                                        className='font-medium ml-1'
                                        style={{ fontSize: '18px', color: '#585858' }}
                                    >
                                        {date?.date}
                                    </span>
                                    <span
                                        className='font-semibold  ml-1'
                                        style={{
                                            fontSize: '18px',
                                            color: '#585858',
                                            lineHeight: '20px',
                                        }}
                                    >
                                        {date?.month}
                                    </span>
                                </div>
                                <div className='flex align-items-center gap-2'>
                                    <span className={`${styles.statusMobile}`}>Expiry:</span>
                                    <span
                                        className={`${styles.statusMobile} font-bold`}
                                        style={{ textDecoration: 'underline' }}
                                    >
                                        {expiry} days
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Divider />
                        <div>
                            {/* Header Section  */}
                            <div className=' px-3 flex justify-content-between align-items-center pl-2 '>
                                <div className='flex align-items-center gap-2 pt-3 '>
                                    <span className='' role='img' aria-label='childcare'>
                                        <img
                                            alt='Clock'
                                            src={childcare}
                                            style={{
                                                width: '17px',
                                                height: '17px',
                                                color: '#585858',
                                            }}
                                            className='font-light'
                                        />
                                    </span>
                                    <p
                                        className='p-0 m-0 font-bold'
                                        style={{ fontSize: '14px', color: '#585858' }}
                                    >
                                        {getJobTypeMobile(title)}
                                    </p>
                                </div>
                                {/* <div
                  className={`flex align-items-center gap-2 px-3 py-1`}
                >
                  <span className={styles.jobTypeMobile}>{jobOption}</span>
                </div> */}
                            </div>

                            {/* Job Details  */}
                            <div className=' px-2 flex flex-wrap gap-0 flex-column'>
                                <div className={`flex align-items-center gap-2 px-2 py-1`}>
                                    <img
                                        alt='Calendar'
                                        src={calendar}
                                        style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                                    />
                                    <span className={styles.jobTypeMobile}>{jobType}</span>
                                </div>
                                <div className={` flex align-items-center gap-2 px-2 py-1`}>
                                    <img
                                        alt='Clock'
                                        src={clock}
                                        style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                                    />
                                    <span className={styles.jobTypeMobile}>{timeSlot}</span>
                                </div>
                                <div className={`flex align-items-center gap-2 px-2 py-1`}>
                                    <img
                                        alt='Home'
                                        src={home}
                                        style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                                    />
                                    <span className={styles.jobTypeMobile}>{location}</span>
                                </div>
                                {jobType === 'One-off Job' ? (
                                    <div
                                        className='flex px-2 align-items-center'
                                        style={{ position: 'relative' }}
                                    >
                                        <div className='flex flex-row justify-content-center align-items-center gap-2'>
                                            <img
                                                style={{
                                                    width: '18px',
                                                    height: '14px',
                                                    color: '#FFFFFF',
                                                }}
                                                src={userProfile}
                                                alt='userprofile'
                                            />
                                            <span
                                                style={{
                                                    fontWeight: '700',
                                                    textDecoration: 'underline',
                                                    textWrap: "nowrap"
                                                }}
                                                className={`${styles.statusMobile}`}
                                            >
                                                Helper Awarded:
                                            </span>
                                        </div>
                                        <span
                                            className={`${styles.statusMobile} font-bold ml-2`}
                                            style={{
                                                fontSize: '14px',
                                                textDecoration: 'underline',
                                                textWrap: "nowrap"
                                            }}
                                        >
                                            {name}
                                        </span>
                                        {image ? (
                                            <div
                                                style={{ position: 'relative', marginLeft: '6px' }}
                                            >
                                                {/* Image */}
                                                <img
                                                    style={{
                                                        height: '33px',
                                                        width: '33px',
                                                        borderRadius: '50%',
                                                        border: '1px solid #179D52',
                                                    }}
                                                    src={image}
                                                    alt='Helper Image'
                                                />
                                                {/* Green Circular Tick */}
                                            </div>
                                        ) : null}
                                    </div>
                                ) : (
                                    <div
                                        className='flex px-2   align-items-center'
                                        style={{ position: 'relative' }}
                                    >
                                        <div className='flex flex-row justify-content-center align-items-center gap-2'>
                                            <img
                                                style={{
                                                    width: '18px',
                                                    height: '14px',
                                                    color: '#FFFFFF',
                                                }}
                                                src={userProfile}
                                                alt='userprofile'
                                            />
                                            <span
                                                style={{ fontWeight: 'bold' }}
                                                className={`${styles.statusMobile}`}
                                            >
                                                Helper Awarded:
                                            </span>
                                        </div>
                                        {names.map((n, index) => (
                                            <span
                                                key={index}
                                                className={`${styles.statusMobile} font-bold ml-2`}
                                                style={{
                                                    fontSize: '14px',
                                                    textDecoration: 'underline',
                                                }}
                                            >
                                                {n.name}
                                            </span>
                                        ))}

                                        {images.map((i, index) => (
                                            <div
                                                key={index}
                                                style={{ position: 'relative', marginLeft: '6px' }}
                                            >
                                                {/* Image */}
                                                <img
                                                    style={{
                                                        height: '33px',
                                                        width: '33px',
                                                        borderRadius: '50%',
                                                        border: '1px solid #179D52',
                                                    }}
                                                    src={i.image}
                                                    alt='Helper Image'
                                                />
                                                {/* Green Circular Tick */}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                            <Divider className='mt-3' />

                            {/* Status Section  */}
                            <div className='px-2 py-3 border-t flex  justify-content-between'>
                                <div className='flex justify-content-between align-items-center'>
                                    <div className='space-y-1'>
                                        <div className='flex align-items-center gap-2 px-2'>
                                            <span className={`${styles.statusMobile}`}>
                                                Status:
                                            </span>
                                            <span
                                                style={{
                                                    fontSize: '14px',
                                                    fontWeight: '700',
                                                    color: '#179D52',
                                                    textDecoration: 'underline',
                                                }}
                                            >
                                                {status}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className='flex justify-content-center  py-2 px-2 text-white font-bold cursor-pointer'>
                                    <button
                                        onClick={onClick}
                                        className=' py-2 px-2 text-white font-bold cursor-pointer'
                                        style={{
                                            backgroundColor: '#FFA500',
                                            fontSize: '12px',
                                            border: 'none',
                                            borderRadius: '20px',
                                        }}
                                    >
                                        View Job
                                    </button>
                                </div>
                            </div>

                            {/* View Job Button  */}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AwardedJobsCard;
