import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
 
function JobPrice() {
  const { isMobile } = useIsMobile()
  return isMobile ? <JobPriceMobile /> : <JobPriceWeb />;
}
 
export default JobPrice;
const useJobTypeHook = () => {
  const { } = useJobManager()
  return {
 
  }
}
 
const JobPriceWeb = () => {
  const { } = useJobTypeHook()
  return <div>isWeb</div>;
}
const JobPriceMobile = () => {
  const { } = useJobTypeHook()
  return <div>isMobile</div>;
}