# Testing Multiple Timesheet Rows

## 🧪 **How to Test the Implementation**

### **1. Test the Current Implementation**

1. **Navigate to the route**: `/parent-home/timesheet/awaiting-confirmation`
2. **Click "Review Timesheet"** on any timesheet card
3. **Verify the popup shows**:
   - Header: "Review Timesheet"
   - Multiple rows in the table:
     - Row 1: 9:00 AM - 12:00 PM (3 hours, $25/hr, $75)
     - Row 2: 1:00 PM - 5:00 PM (4 hours, $25/hr, $100)  
     - Row 3: 6:00 PM - 9:00 PM (3 hours, $30/hr, $90)
   - Total: 10 hours, $265

### **2. Test Edit Functionality**

1. **In the popup, click "Edit Timesheet"**
2. **Verify EditTimesheet component receives**:
   - Complete array of all 3 timesheet rows
   - Each row with proper AM/PM time format
   - Ability to edit all rows simultaneously

### **3. Console Verification**

Check browser console for these logs:
```
Fetching timesheet rows for ID: [timesheetId]
Multiple timesheet rows set: [array of 3 objects]
TimesheetDetailsPopup - Current state: {timesheetRowsCount: 3, ...}
```

## 🔧 **Customizing the Data**

### **Replace Sample Data with Your Actual Data**

In `TimeSheet.tsx`, find the `fetchTimesheetRows` function and replace the sample data:

```typescript
// Replace this sample data:
const sampleRows = [
  {
    start: "09:00",
    finish: "12:00", 
    hours: 3,
    rate: 25,
    total: 75,
    id: 1
  },
  // ... more rows
];

// With your actual data:
const actualRows = [
  {
    start: yourData.startTime1,
    finish: yourData.endTime1,
    hours: yourData.hours1,
    rate: yourData.rate1,
    total: yourData.total1,
    id: yourData.id1
  },
  // ... your actual timesheet entries
];
```

### **If You Have an API for Multiple Rows**

Replace the sample data section with an API call:

```typescript
const fetchTimesheetRows = async (timesheetId: number) => {
  try {
    Service.getTimesheetRows( // Your API method
      (response: any) => {
        console.log('API response:', response);
        const rows = Array.isArray(response) ? response : response?.data || [];
        setTimesheetRows(rows);
      },
      (error: any) => {
        console.error('Error:', error);
        setTimesheetRows([]);
      },
      timesheetId
    );
  } catch (error) {
    console.error('Error fetching timesheet rows:', error);
    setTimesheetRows([]);
  }
};
```

## 📊 **Expected Data Structure**

Your timesheet rows should follow this structure:

```typescript
interface TimesheetRow {
  start: string;        // "09:00" or "9:00 AM"
  finish: string;       // "17:00" or "5:00 PM"  
  hours: number;        // 8
  rate: number;         // 25
  total: number;        // 200
  id?: number;          // 1
  isOriginal?: boolean; // false
  editVersion?: number; // 0
}
```

## 🎯 **Verification Checklist**

- [ ] **Multiple rows display** in the popup table
- [ ] **AM/PM time format** for all start/finish times
- [ ] **Correct calculations** for hours and totals
- [ ] **Grand total** calculated from all rows
- [ ] **Edit button** opens EditTimesheet with complete array
- [ ] **Close button** works properly
- [ ] **Console logs** show correct data flow

## 🐛 **Troubleshooting**

### **If Only One Row Shows:**
- Check if `timesheetRows` prop is being passed to TimesheetDetailsPopup
- Verify `fetchTimesheetRows()` is being called in `handleReview()`
- Check console for any errors in data fetching

### **If Times Don't Show in AM/PM:**
- Verify the time conversion functions are working
- Check that input times are in correct format ("09:00" or "9:00 AM")

### **If EditTimesheet Doesn't Get Array:**
- Verify `onTimesheetRowsChange` prop is passed correctly
- Check that `currentTimesheetRows` state is populated

### **If Calculations Are Wrong:**
- Check the `calculateHoursFromTimes` function in AwaitingConfirmationCard
- Verify rate values are numbers, not strings

## 🚀 **Success Indicators**

When working correctly, you should see:

1. **Popup opens** with "Review Timesheet" header
2. **Table shows multiple rows** (3 in the sample)
3. **Times in AM/PM format** (9:00 AM, not 09:00)
4. **Correct calculations** for each row
5. **Total amount** at bottom
6. **Edit functionality** works with all rows
7. **Console logs** show proper data flow

The implementation is ready for your actual timesheet data! 🎉
