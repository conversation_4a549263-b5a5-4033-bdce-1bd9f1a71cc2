import React, { useState } from "react";
import childrenIcon from "../../../../assets/images/Icons/my_child.png";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { IoHomeOutline } from "react-icons/io5";
import { FaArrowLeft } from "react-icons/fa";
import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
import EditTimesheet from "./EditTimesheet";
import Dollar from "../../../../assets/images/Icons/Dollar1.png";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";

interface TimesheetRow {
  start: any;
  finish: any;
  hours: number;
  rate: any;
  total: number;
  originalId?: string;
  isOriginal?: boolean;
  editVersion?: number;
}

interface AwaitingConfirmationCardProps {
  timesheetId?: number;
  jobId?: number;
  applicantId?: number;
  userId?: number;
  profileName: string;
  profileImage: string;
  jobType: string;
  jobDate: string;
  jobAddress: string;
  baseRate: number;
  extraHoursRate: number;
  initialTimesheetRows: TimesheetRow[];
  statusText?: string;
  onSubmit?: () => void;
  onGoBack?: () => void;
}

const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
  profileName,
  profileImage,
  jobType,
  jobDate,
  jobAddress,
  baseRate,
  extraHoursRate,
  initialTimesheetRows,
  statusText = "Awaiting Your Confirmation",
  onSubmit,
  onGoBack,
}) => {
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isBusiness = clientType === c.clientType.BUSINESS;
  const showApprove = isParent || isBusiness;

  const [submitted, setSubmitted] = useState(false);
  const [showEditPage, setShowEditPage] = useState(false);
  const [editVersionCounter, setEditVersionCounter] = useState(1);

  const [timesheetRows, setTimesheetRows] = useState<TimesheetRow[]>(
    initialTimesheetRows.map((row) => ({
      ...row,
      isOriginal: false,
      editVersion: 0,
    }))
  );

  const totalAmount = timesheetRows
    .filter((row) => !row.isOriginal)
    .reduce((sum, row) => sum + row.total, 0);

  const sortedRows = [...timesheetRows].sort((a, b) => {
    if (a.isOriginal && !b.isOriginal) return -1;
    if (!a.isOriginal && b.isOriginal) return 1;
    return 0;
  });

  const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
    const currentActiveRows = timesheetRows.filter((row) => !row.isOriginal);
    const previousOriginalRows = timesheetRows.filter((row) => row.isOriginal);
    const newRows: TimesheetRow[] = [];

    updatedShifts.forEach((updatedShift, index) => {
      const currentRow = currentActiveRows[index];
      if (currentRow) {
        const wasChanged =
          currentRow.start !== updatedShift.start ||
          currentRow.finish !== updatedShift.finish;
        if (wasChanged) {
          const clonedOriginal = { ...currentRow };
          newRows.push({ ...clonedOriginal, isOriginal: true, editVersion: editVersionCounter - 1 });
          newRows.push({
            start: updatedShift.start,
            finish: updatedShift.finish,
            hours: 0,
            rate: 0,
            total: 0,
            isOriginal: false,
            editVersion: editVersionCounter,
          });
        } else {
          newRows.push({ ...currentRow, editVersion: editVersionCounter });
        }
      } else {
        newRows.push({
          start: updatedShift.start,
          finish: updatedShift.finish,
          hours: 0,
          rate: 0,
          total: 0,
          isOriginal: false,
          editVersion: editVersionCounter,
        });
      }
    });

    setTimesheetRows([...previousOriginalRows, ...newRows]);
    setEditVersionCounter((prev) => prev + 1);
    setShowEditPage(false);
  };

  const handleSubmit = () => {
    setSubmitted(true);
    onSubmit?.();
  };

  const renderApprovedHeader = () => (
    <div className={styles.approvedHeader}>
      <div className={styles.inlineContainer}>
        <div className={styles.confirmRow}>
          <div className={styles.confirmedIconCircle}>
            <span className={styles.checkIcon}>✓</span>
          </div>
        </div>
        <div className={styles.headerConfirmedTitle}>
          {showApprove ? "Approved" : "Confirmed"}
        </div>
        <div className={styles.confirmedSubtitle}>
          {showApprove
            ? "Thanks for confirming! This Timesheet will now be used to generate an invoice and complete the payment process."
            : "Thanks for confirming your Timesheet"}
        </div>
      </div>
    </div>
  );

  const renderSharedHeader = () => (
    <div className={styles.headerSection}>
      <div>
        <h3 className={styles.title}>Review Timesheet</h3>
        <div className={styles.status}>
          Status:{" "}
          <span className={styles.statusHighlight}>
            {submitted
              ? showApprove
                ? "Approved"
                : "Awaiting Parent's Approval"
              : statusText}
          </span>
        </div>
      </div>
      <div className={styles.profileContainer}>
        <img src={profileImage} alt="Profile" className={styles.avatar} />
        <div className={styles.profileName}>{profileName}</div>
      </div>
    </div>
  );

  const renderJobInfo = () => (
    <div className={styles.info}>
      <div className={styles.infoBlock}>
        <div className={styles.row}>
          <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
          <div>{jobType}</div>
        </div>
        <div className={styles.row}>
          <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
          <div>{jobDate}</div>
        </div>
        <div className={styles.row}>
          <IoHomeOutline className={styles.rowIcon} />
          <div>{jobAddress}</div>
        </div>
      </div>
      <hr className={styles.hrFull} />
      <div className={styles.rateBlock}>
        <img src={Dollar} alt="dollar" className={styles.rowIcon} />
        <div className={styles.rateText}>
          <div>Base Rate: ${baseRate} per hour</div>
          <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div>
        </div>
      </div>
      <hr className={styles.hrFull} />
    </div>
  );

  const renderTable = () => (
    <div className={styles.timesheetContainer}>
      <div className={styles.rowHeader}>
        <div className={styles.column}>Start</div>
        <div className={styles.column}>Finish</div>
        <div className={styles.column}>Hours</div>
        <div className={styles.column}>Rate</div>
        <div className={styles.column}>Total</div>
      </div>
      {sortedRows.map((row, index) => (
        <div
          key={`${index}-${row.editVersion}-${row.isOriginal}`}
          className={`${styles.rowData} ${row.isOriginal ? styles.originalRow : ""}`}
          style={
            row.isOriginal
              ? { color: "#ff6359", textDecoration: "line-through", opacity: 0.7 }
              : {}
          }
        >
          <div className={styles.column}>{row.start}</div>
          <div className={styles.column}>{row.finish}</div>
          <div className={styles.column}>{row.hours}</div>
          <div className={styles.column}>${row.rate}</div>
          <div className={styles.column}>${row.total}</div>
        </div>
      ))}
      <hr className={styles.hr} />
      <div className={styles.totalRow}>
        <div className={styles.column}></div>
        <div className={styles.column}></div>
        <div className={styles.column}></div>
        <div className={styles.column}></div>
        <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
      </div>
      <hr className={styles.hr} />
    </div>
  );

  const renderPendingApprovalSection = () => (
    <div className={styles.pendingApprovalSection}>
      <div className={styles.pendingContent}>
        <div className={styles.pendingHeader}>Pending Craig's Approval</div>
        <div className={styles.pendingText}>Awaiting for Craig S to approve your timesheet</div>
      </div>
      <div className={styles.pendingProfile}>
        <div className={styles.pendingProfileName}>Craig S</div>
        <img src={profileImage} alt="Craig S" className={styles.avatar} />
      </div>
    </div>
  );

  if (showEditPage) {
    const activeShiftsForEdit = timesheetRows
      .filter((row) => !row.isOriginal)
      .map(({ start, finish }) => ({ start, finish }));

    return (
      <EditTimesheet
        day="Monday,"
        date={jobDate}
        profileImage={profileImage}
        profileName={profileName}
        baseRate={baseRate}
        extraRate={extraHoursRate}
        initialShifts={activeShiftsForEdit}
        onClose={() => setShowEditPage(false)}
        onSave={handleSaveShifts}
      />
    );
  }

  return (
    <>
      {submitted && renderApprovedHeader()}

      <div className={`${styles.card} ${submitted ? styles.cardApproved : ""}`}>
        {renderSharedHeader()}
        {renderJobInfo()}
        {renderTable()}

        {submitted ? (
          <>
            <div className={styles.statusBox}>
              <p className={showApprove ? styles.confirmedTitle : styles.pendingTitle}>
                {showApprove ? "Approved Timesheet" : "Timesheet Confirmed"}
              </p>
              <p className={showApprove ? styles.confirmedSubText : styles.pendingSub}>
                {showApprove
                  ? "The hours worked are correct."
                  : "This Timesheet will now be submitted to the parent for approval."}
              </p>
            </div>
            
            {!showApprove && renderPendingApprovalSection()}
            
            <div className={`${styles.section} ${styles.editSection} ${styles.disabledBlock}`}>
              <div className={styles.sectionText}>
                <div className={styles.titlee}>Edit Timesheet</div>
                <div className={styles.subText}>Make an adjustment to the actual hours worked</div>
              </div>
              <button className={styles.editDisabledBtn} disabled>
                Edit Timesheet
              </button>
            </div>
            <button className={styles.nextBtn}>Next</button>
          </>
        ) : (
          <>
            {timesheetRows.some((row) => row.isOriginal) && (
              <div className={styles.adjustedTimesheetBlock}>
                <div className={styles.adjustedHeader}>
                  <span className={styles.adjustedTitle}>Adjusted Timesheet</span>
                  <span className={styles.adjustedIcon}>?</span>
                </div>
                <div className={styles.adjustedDescription}>
                  You have adjusted the hours worked. Please review changes and then{" "}
                  {showApprove ? `"Approve"` : `"Submit"`}.
                </div>
                <hr className={styles.hrFull} />
              </div>
            )}

            <div className={styles.footer}>
              <div className={styles.section}>
                <div className={styles.sectionText}>
                  <div className={styles.titlee}>
                    {showApprove ? "Approve Timesheet" : "Confirm Timesheet"}
                  </div>
                  <div className={styles.subText}>
                    {showApprove
                      ? "The hours are correct. Approve the timesheet for payment processing."
                      : "The Timesheet is an accurate record of the job."}
                  </div>
                </div>
                <button className={styles.submitBtn} onClick={handleSubmit}>
                  {showApprove ? "Approve" : "Submit"}
                </button>
              </div>
              <div className={`${styles.section} ${styles.editSection}`}>
                <div className={styles.sectionText}>
                  <div className={styles.titlee}>Edit Timesheet</div>
                  <div className={styles.subText}>Make adjustments to the Timesheet.</div>
                </div>
                <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>
                  Edit Timesheet
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default AwaitingConfirmationCard;


