import styles from "../Common/styles/horizontal-navigation-mobile.module.css";

import { useSearchParams } from "react-router-dom";

import sideArrow from "../../assets/images/Icons/side_arrow_left.png";
import React from "react";
import useIsMobile from "../../hooks/useIsMobile";

type HorizontalNavigationItem = {
  item: (params: { index: number; activeIndex?: number; maxIndex: number; itemStyles: CSSModuleClasses }) => React.ReactNode;
};

type HorizontalNavigationProps = {
  activeIndex?: number;
  title: string;
  tabs?: HorizontalNavigationItem[];
  onBackClick: () => void;
  style?: React.CSSProperties; // Add style prop
  wrapperStyle?: React.CSSProperties; // Add style prop
};

const HorizontalNavigation = ({ activeIndex, title, onBackClick, tabs, style, wrapperStyle = { position: "fixed" } }: HorizontalNavigationProps) => {
  const { isMobile } = useIsMobile();

  const getActive = () => (!!activeIndex ? activeIndex : 0);

  if (!isMobile) return null;

  return (
    <div
      className="w-full h-min top-0 left-0 flex flex-column"
      style={{
        zIndex: "1000",
        ...wrapperStyle,
        backgroundColor: "#179d52",
      }}
    >
      <div
        className={`w-full flex justify-content-center align-items-center relative ${styles.native}`}
        style={{
          minHeight: "60px",
          backgroundColor: "#179d52",
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            fontSize: "20px",
            color: "#FFFFFF",
            fontWeight: "700",
          }}
        >
          {title}
        </p>
        <button
          className="absolute flex justify-content-center items-align-center cursor-pointer"
          style={{
            top: "50%",
            transform: "translateY(-50%)",
            left: "30px",
            background: "rgba(217, 217, 217, 0.3)",
            borderRadius: "999px",
            border: "none",
            padding: "5px 8px",
          }}
          onClick={(e) => {
            e.preventDefault();
            onBackClick();
          }}
        >
          <img src={sideArrow} alt="sideArrow" width="10px" height="15px" />
        </button>
      </div>

      {!!tabs && (
        <div
          className="flex justify-content-around"
          style={{
            minHeight: "50px",
            background: "#227c3c",
            width: "100%",
            borderBottomLeftRadius: "20px",
            borderBottomRightRadius: "20px",
            ...style, // Apply dynamic styles
          }}
        >
          {tabs.map((tab, index) => (
            <React.Fragment key={index}>
              {tab.item({
                index,
                activeIndex: getActive(),
                maxIndex: tabs.length,
                itemStyles: styles,
              })}
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  );
};

const Item = (index: number, activeIndex: number, icon: React.ReactNode, text: string, onClick: () => void) => {
  return (
    <button
      className={`${styles.navItem} ${activeIndex === index ? styles.active : ""}`}
      onClick={(e) => {
        e.preventDefault();
        onClick();
      }}
      style={{
        flex: "1 1 0", // Allow shrinking and growing equally
        overflow: "hidden", // Prevent overflow
      }}
    >
      {icon}
      <span className={styles.navText}>{text}</span>
      {activeIndex === index && <div className={styles.activeLine}></div>}
    </button>
  );
};

HorizontalNavigation.Item = Item;

export default HorizontalNavigation;
