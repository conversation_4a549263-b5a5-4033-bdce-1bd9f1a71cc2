/* Container styles */
.wwccContainer {
  padding: 40px;
  background-color: #f9f9f9;
  border-radius: 8px;
  max-width: 600px;
  margin: auto;
}

/* Header styles */
.wwccHeader {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #585858;
}
.wwccHeaderSecond {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #585858;
  }

/* Button container styles */
.wwccButtonContainer {
  text-align: center;
  margin-bottom: 20px;
}

.wwccAddButton {
  padding: 10px 20px;
  background-color: #f0f4f7;
  color: #585858;
  border: 2px solid #ffa500;
  border-radius: 10px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 7px;
}

/* Dialog content styles */
.wwccDialogContent {
  padding: 40px;
  background-color: #fff;
  border-radius: 33px;
}

/* Close button styles */
.wwccCloseBtn {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  text-align: right;
}

.wwccCloseBtn:hover {
  color: #333;
}

/* Input container styles */
.wwccSettingsInputContainer {
  margin-bottom: 20px; /* Space between input fields */
  display: flex;
  flex-direction: column;
  width: 100%; /* Ensures full width in container */
}
.wwccSettingsInputContainerSec {
  margin-bottom: 20px; /* Space between input fields */
  display: flex;
  flex-direction: column;
  width: 100%; /* Ensures full width in container */
}

/* Label styles */
.wwccLabelName {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  font-size: 16px;
  color: #999;
  transition: all 0.2s;
}

.wwccLabelFloat {
  top: -10px;
  left: 10px;
  font-size: 12px;
  color: #333;
}

/* Input field styles */
.input-placeholder {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}
.wwccprogressbar {
  width: 487px;
  max-width: 100%;
  height: 7px;
  margin-top: 1rem;
}

.wwccprogressbar > div {
  background-color: #179d52 !important;
}
.wwccprogressbar > div > div {
  display: none;
}
.CloseBtn {
  display: flex;
  justify-content: end;
  position: absolute;
  right: 3%;
  width: 30px;
  height: 32px;
  color: #585858;
  border-radius: 50%;
  top: 2%;
  cursor: pointer;
}
.calendarDiv{
  display: flex;
  align-items: center;
  gap: 10px;
  margin-block: 0px;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}
.wwccSaveButton{
  width: 156px;
  height: 39px;
  background-color: #ffa500;
  font-weight: 800;
  font-size: 14px;
  color: #FFFFFF;
  box-shadow: 0px 4px 4px 0px #00000040;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}
.editBtn {
  width: 135px;
  min-height: 34px;
  font-size: 12px;
  display: flex;
  height: 34px;
  font-weight: 800;
  line-height: 18px;
  color: #ffffff;
  border-radius: 10px;
  align-items: center;
  background-color: #ffa500;
  border: none;
  padding: 8px 14px 8px 10px;
  gap: 8px;
  cursor: pointer;
  justify-content: center;
}
.buttonContainer{
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.removeBtn{
  width: 74px;
  height: 34px;
  background-color: #ffd0cd;
  border-radius: 10px;
  border: 1px solid #FF6359;
  color: #FF6359;
  cursor: pointer;
}
.utilfooterContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 55px;
  width: 100%;
}
.wwccSaveButton:disabled {
  cursor: not-allowed;
  background-color: #F0F4F7;
  color: #585858;
  font-size: 14px;
  font-weight: 400;
  box-shadow: none;
}
.wwccContainerMobile {
  width: 100%;
}
.utilfooterContainerMobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-top: 55px; */
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  height: min-content;
  box-shadow: 0 0 8px #00000040;
  background-color: #fff;
}
.wwccCaleder > span{
  width: 100% !important;
}