
import React, { useRef, useState } from "react";
import styles from "../../../../containers/Common/styles/editTimesheet.module.css";
import { FaArrowLeft } from "react-icons/fa";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { RiArrowDropDownLine } from "react-icons/ri";
import { FiPlus } from "react-icons/fi";
import { FaAngleUp } from "react-icons/fa";

interface Shift {
    start: string;
    finish: string;
}

interface EditTimesheetProps {
    date: string;
    profileImage: string;
    profileName: string;
    baseRate: number;
    extraRate: number;
    initialShifts: Shift[];
    onClose: () => void;
    onSave?: (shifts: Shift[]) => void;
}

const EditTimesheet: React.FC<EditTimesheetProps> = ({
    date,
    profileImage,
    profileName,
    baseRate,
    extraRate,
    initialShifts,
    onClose,
    onSave,
}) => {
    const initialShiftsRef = useRef<Shift[]>(JSON.parse(JSON.stringify(initialShifts)));

    const [shifts, setShifts] = useState<Shift[]>(initialShifts);
    const [isChanged, setIsChanged] = useState(false);
    const [openDropdown, setOpenDropdown] = useState<{ shiftIndex: number, field: 'start' | 'finish' } | null>(null);

    const handleTimeChange = (index: number, key: "start" | "finish", value: string) => {
        const updated = [...shifts];
        updated[index][key] = value;
        setShifts(updated);
        setIsChanged(true);
        setOpenDropdown(null);
    };

    const handleAddShift = () => {
        const newShift1 = { start: "", finish: "" };
        const newShift2 = { start: "", finish: "" };

        const updatedShifts = [...shifts, newShift1, newShift2];
        setShifts(updatedShifts);

        const clone1 = { ...newShift1 };
        const clone2 = { ...newShift2 };
        initialShiftsRef.current = [...initialShiftsRef.current, clone1, clone2];

        setIsChanged(true);
    };


    const handleSave = () => {
        if (onSave) {
            onSave(shifts);
        }
        setIsChanged(false);
    };

    const handleCancel = () => {
        const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
        setShifts(clonedInitial);
        setIsChanged(false);
        setOpenDropdown(null);
    };



    const toggleDropdown = (shiftIndex: number, field: 'start' | 'finish') => {
        if (openDropdown?.shiftIndex === shiftIndex && openDropdown?.field === field) {
            setOpenDropdown(null);
        } else {
            setOpenDropdown({ shiftIndex, field });
        }
    };

    const timeOptions = [
        "6:00am", "6:15am", "6:30am", "6:45am", "7:00am", "7:15am", "7:30am", "7:45am",
        "8:00am", "8:15am", "8:30am", "8:45am", "9:00am", "9:15am", "9:30am", "9:45am",
        "10:00am", "10:15am", "10:30am", "10:45am", "11:00am", "11:15am", "11:30am", "11:45am",
        "12:00pm", "12:15pm", "12:30pm", "12:45pm", "1:00pm", "1:15pm", "1:30pm", "1:45pm",
        "2:00pm", "2:15pm", "2:30pm", "2:45pm", "3:00pm", "3:15pm", "3:30pm", "3:45pm",
        "4:00pm", "4:15pm", "4:30pm", "4:45pm", "5:00pm", "5:15pm", "5:30pm", "5:45pm",
        "6:00pm", "6:15pm", "6:30pm", "6:45pm", "7:00pm", "7:15pm", "7:30pm", "7:45pm",
        "8:00pm", "8:15pm", "8:30pm", "8:45pm", "9:00pm"
    ];

    return (
        <div className={styles.container}>
            <div className={styles.headerWrapper}>
                <button className={styles.backBtn} onClick={onClose}>
                    <span className={styles.arrowCircle}>
                        <span className={styles.arrow}><FaArrowLeft /></span>
                    </span>
                    Go back
                </button>
            </div>

            <div className={styles.header}>
                <div style={{
                    fontWeight: 700, fontSize: '22px',
                }}>Edit Timesheet</div>
                <div className={styles.subHeading}>
                    <div className={styles.row}>
                        <img src={manageJobsIcon} className={styles.rowIcon} alt="icon" />
                        <div>{date}</div>
                    </div>
                    <div className={styles.profileSection}>
                        <img src={profileImage} alt="Profile" className={styles.profileImg} />
                        <span className={styles.profileName}>{profileName}</span>
                    </div>
                </div>
            </div>
            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />

            <div className={styles.rateInfo}>
                <div>$ Base Rate: ${baseRate} per hour</div>
                <div>Extra Hours Rate: ${extraRate} per hour</div>
            </div>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />

            {shifts.map((shift, index) => (
                <div key={index} className={styles.shiftBlock}>
                    <div className={styles.shiftLabel}>
                        {index % 2 === 0 ? "Before School" : "After School"}
                    </div>

                    <div className={styles.shiftRow}>
                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} ${openDropdown?.shiftIndex === index && openDropdown?.field === 'start'
                                    ? styles.active
                                    : ''
                                    }`}
                            >
                                <span
                                    className={`${styles.label} ${shift.start !== (initialShiftsRef.current[index]?.start || "")
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                >
                                    Start
                                </span>

                                <div className={styles.divider} />
                                <span
                                    className={`${styles.time} ${shift.start !== initialShiftsRef.current[index]?.start
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.start || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.start !== initialShiftsRef.current[index]?.start ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'start')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span style={{ fontWeight: 400, fontSize: '25px' }}>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' ? (
                                            <FaAngleUp />
                                        ) : (
                                            <RiArrowDropDownLine />
                                        )}
                                    </span>
                                </div>

                            </div>

                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.start === time ? styles.dropdownItemSelected : ''}`}
                                            onClick={() => handleTimeChange(index, "start", time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} ${openDropdown?.shiftIndex === index && openDropdown?.field === 'finish'
                                    ? styles.active
                                    : ''
                                    }`}
                            >
                                <span
                                    className={`${styles.label} ${shift.finish !== (initialShiftsRef.current[index]?.finish || "") ? styles.modifiedTime : ''
                                        }`}
                                >
                                    Finish
                                </span>

                                <div className={styles.divider} />

                                <span
                                    className={`${styles.time} ${shift.finish !== initialShiftsRef.current[index]?.finish ? styles.modifiedTime : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.finish || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.finish !== initialShiftsRef.current[index]?.finish ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'finish')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span style={{ fontWeight: 400, fontSize: '25px' }}>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' ? (
                                            <FaAngleUp style={{ fontSize: '24px',marginTop:'2px'}} />
                                        ) : (
                                            <RiArrowDropDownLine style={{ fontSize: '28px'}} />
                                        )}
                                    </span>
                                </div>
                            </div>

                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.finish === time ? styles.dropdownItemSelected : ''
                                                }`}
                                            onClick={() => handleTimeChange(index, 'finish', time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            ))}

            <button className={styles.addShiftBtn} onClick={handleAddShift}>
                <span className={styles.plusCircle}><FiPlus /></span>Add another shift
            </button>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%', marginTop: '150px' }} />
            <div className={styles.footerButtons}>
                <button
                    className={styles.revertBtn}
                    onClick={() => {
                        if (isChanged) {
                            handleCancel();
                        } else {
                            const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
                            setShifts(clonedInitial);
                            setOpenDropdown(null);
                        }
                    }}

                >
                    {isChanged ? "Cancel Changes" : "Revert Changes"}
                </button>

                <button
                    className={`${styles.saveBtn} ${isChanged ? styles.saveBtnEnabled : ''}`}
                    disabled={!isChanged}
                    onClick={handleSave}
                >
                    Save Changes
                </button>
            </div>
        </div>
    );
};

export default EditTimesheet;