import React, { useEffect, useRef, useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { IoClose } from 'react-icons/io5';
import styles from '../../styles/awarded-details-popup.module.css'; // Assuming you have styles defined in this file.
import { Divider } from 'primereact/divider';
import BackArrow from '../../../../assets/images/Icons/back-icon.png';
import { Button } from 'primereact/button';
import useIsMobile from '../../../../hooks/useIsMobile';
interface AwardedDetailsPopupProps {
    visible: boolean;
    onHide: () => void;
    name?: string;
    helperimg?: string;
    title: string;
    subTitle?: string;
    isPhone?: boolean;
    data: Array<{ text?: string; boldText: string; fontSize: string }>;
}

const AwardedDetailsPopup: React.FC<AwardedDetailsPopupProps> = ({
    visible,
    onHide,
    title,
    subTitle,
    data,
    name,
    helperimg,
    isPhone,
}) => {
    const [copyStatus, setCopyStatus] = useState('');

    const handleCopy = async (text: string) => {
        try {
            // Fallback for older browsers and mobile devices
            if (!navigator.clipboard) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    document.execCommand('copy');
                    textArea.remove();
                    setCopyStatus('Copied!');
                } catch (err) {
                    textArea.remove();
                    setCopyStatus('Failed to copy');
                }
            } else {
                // Modern browsers
                await navigator.clipboard.writeText(text);
                setCopyStatus('Copied!');
            }

            // Clear status after 2 seconds
            setTimeout(() => setCopyStatus(''), 2000);
        } catch (err) {
            setCopyStatus('Failed to copy');
            setTimeout(() => setCopyStatus(''), 2000);
        }
    };

    // Touch event handler for better mobile support
    const handleTouchCopy = (text: string, e: React.TouchEvent) => {
        e.preventDefault(); // Prevent default touch behavior
        handleCopy(text);
    };

    const { isMobile } = useIsMobile();
    return !isMobile ? (
        <Dialog
            content={
                <div className={styles.DialogContent}>
                    <div className={styles.closeBtn} onClick={onHide}>
                        <IoClose />
                    </div>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                        >
                            <h3 className={`${styles.detailsTitle} p-3`}>{title}</h3>
                            {subTitle && <p className={styles.detailsPara}>{subTitle}</p>}
                        </div>
                        <Divider />
                        <div className='flex flex-column align-items-center justify-content-center gap-2 py-2'>
                            {data.map((d, i) => {
                                return (
                                    <div
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                        }}
                                    >
                                        {d.text && (
                                            <p
                                                style={{
                                                    fontSize: d.fontSize,
                                                    fontWeight: '400',
                                                    color: '#585858',
                                                }}
                                                className='m-0 p-0'
                                            >
                                                {d.text}
                                            </p>
                                        )}
                                        <p
                                            style={{
                                                fontSize: d.fontSize,
                                                fontWeight: '700',
                                                color: '#585858',
                                            }}
                                            className='m-0 p-0 ml-2'
                                        >
                                            {d.boldText}
                                        </p>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            }
            visible={visible}
            onHide={onHide}
        />
    ) : (
        <Dialog
            style={{
                margin: 0,
                padding: 0,
                width: '100vw',
                maxWidth: '100%',
                transform: 'none',
                transition: 'none',
            }}
            content={
                <div className={styles.DialogContentMobile}>
                    {/* <div className={styles.closeBtn} onClick={onHide}>
          <IoClose />
        </div> */}
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                width: '100%',
                                justifyContent: 'space-between',
                                paddingInline: '20px',
                                alignItems: 'center',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    gap: '8px',
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    alignItems: 'center',
                                }}
                                onClick={onHide}
                            >
                                <div
                                    style={{
                                        backgroundColor: '#D9D9D94D',
                                        paddingInline: '11px',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        paddingBlock: '8px',
                                        borderRadius: '50%',
                                        width: 'min-content',
                                    }}
                                >
                                    <img
                                        src={BackArrow}
                                        width={'10px'}
                                        height={'9.5px'}
                                        alt='backArrow'
                                    />
                                </div>
                                Go back
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    position: 'relative',
                                    bottom: '22px',
                                    left: '10px',
                                }}
                            >
                                <img
                                    src={helperimg}
                                    width={'79px'}
                                    height={'75px'}
                                    alt='helperimg'
                                    style={{ borderRadius: '50%', border: '2px solid #179D52' }}
                                />
                            </div>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                        >
                            <h1
                                className='mb-0 p-0'
                                style={{ fontSize: '16px', color: '#585858', fontWeight: '300' }}
                            >
                                {name}
                            </h1>
                            <h3 className={`${styles.detailsTitleMobile} p-1`}>{title}</h3>
                            {subTitle && <p className={styles.detailsPara}>{subTitle}</p>}
                        </div>
                        <Divider />
                        <div className='flex flex-column align-items-center justify-content-center gap-2 py-2'>
                            {data.map((d, i) => {
                                return (
                                    <div
                                        key={i}
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                        }}
                                    >
                                        {d.text && (
                                            <p
                                                style={{
                                                    fontSize: d.fontSize,
                                                    fontWeight: '400',
                                                    color: '#585858',
                                                }}
                                                className='m-0 p-0'
                                            >
                                                {d.text}
                                            </p>
                                        )}
                                        <div className='flex flex-column align-items-center'>
                                            <p
                                                style={{
                                                    fontSize: d.fontSize,
                                                    fontWeight: '700',
                                                    color: '#179D52',
                                                }}
                                                className='m-0 p-0 ml-2'
                                            >
                                                {d.boldText}
                                            </p>
                                            {isPhone && (
                                                <>
                                                    <Button
                                                        label='Copy number'
                                                        onClick={(e) =>{ 
                                                        e.preventDefault(); // Add this to prevent any default behavior    
                                                        handleCopy(d.boldText)}}
                                                        onTouchEnd={(e) => {  // Change from onTouchStart to onTouchEnd for better reliability
                                                            e.preventDefault();
                                                            handleCopy(d.boldText);
                                                        }}
                                                        className={styles.copyBtn}
                                                        style={{
                                                            paddingInline: '30px',
                                                            paddingBlock: '7px',
                                                            marginTop: '10px',
                                                        }}
                                                        aria-label='Copy number'
                                                    />
                                                    {copyStatus && (
                                                        <small className='text-gray-500 ml-2'>
                                                            {copyStatus}
                                                        </small>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            }
            visible={visible}
            onHide={onHide}
        />
    );
};

export default AwardedDetailsPopup;
