# Multiple Timesheet Rows Implementation Guide

## 🎯 **What Was Implemented**

Your timesheet popup now supports displaying multiple timesheet rows instead of just one object. Here's what was added:

### ✅ **1. Multiple Rows Display**
- Popup now shows ALL timesheet entries from an array
- Each row displays: Start Time, Finish Time, Hours, Rate, Total
- All times are shown in AM/PM format with proper calculations

### ✅ **2. Complete Array Passed to EditTimesheet**
- When user clicks "Edit Timesheet", the entire array is passed to EditTimesheet component
- Users can edit all entries simultaneously
- Maintains existing AM/PM time display and real-time calculation features

### ✅ **3. Enhanced TimeSheet.tsx**
- Added `timesheetRows` state to manage multiple rows
- Added `fetchTimesheetRows()` function to get multiple timesheet data
- Updated popup to pass complete array

## 🔧 **Code Changes Made**

### **1. TimeSheet.tsx Updates**

#### **Added State for Multiple Rows:**
```typescript
// State for multiple timesheet rows
const [timesheetRows, setTimesheetRows] = useState<any[]>([]);
```

#### **Enhanced handleReview Function:**
```typescript
const handleReview = (entry: TimesheetEntry) => {
  setSelectedEntry(entry);
  fetchTimesheetDetails(entry.id);
  // Also fetch multiple timesheet rows for this entry
  fetchTimesheetRows(entry.id);
};
```

#### **Added fetchTimesheetRows Function:**
```typescript
const fetchTimesheetRows = async (timesheetId: number) => {
  // Option 1: API call (if you have multiple rows endpoint)
  // Option 2: Sample data (current implementation)
  const sampleRows = [
    {
      start: "09:00",      // Morning shift
      finish: "12:00", 
      hours: 3,
      rate: 25,
      total: 75,
      id: 1
    },
    {
      start: "13:00",      // Afternoon shift  
      finish: "17:00",
      hours: 4, 
      rate: 25,
      total: 100,
      id: 2
    },
    {
      start: "18:00",      // Evening shift
      finish: "21:00",
      hours: 3,
      rate: 30,           // Higher evening rate
      total: 90,
      id: 3
    }
  ];
  
  setTimesheetRows(sampleRows);
};
```

#### **Updated TimesheetDetailsPopup Call:**
```typescript
<TimesheetDetailsPopup
  selectedEntry={selectedEntry}
  timesheetDetails={timesheetDetails}
  timesheetRows={timesheetRows} // Pass the multiple timesheet rows
  onClose={closePopup}
  onApprovalSuccess={handleApprovalSuccess}
/>
```

## 🚀 **How It Works Now**

### **User Flow:**
1. **User clicks "Review Timesheet"** → Opens popup with multiple rows
2. **Popup displays all timesheet entries** → Shows complete work schedule
3. **User clicks "Edit Timesheet"** → EditTimesheet receives complete array
4. **User can edit all entries** → Modify times, rates, add/remove rows
5. **Changes are saved** → All modifications applied to the timesheet

### **Data Flow:**
```
TimeSheet.tsx
     ↓
handleReview(entry) → fetchTimesheetDetails() + fetchTimesheetRows()
     ↓
TimesheetDetailsPopup receives:
- timesheetDetails (single object)
- timesheetRows (array of entries)
     ↓
AwaitingConfirmationCard displays multiple rows
     ↓
EditTimesheet receives complete array for editing
```

## 📊 **Example Display**

### **Before (Single Row):**
```
┌─────────────┬─────────────┬───────┬──────┬─────────┐
│ Start Time  │ Finish Time │ Hours │ Rate │ Total   │
├─────────────┼─────────────┼───────┼──────┼─────────┤
│ 9:00 AM     │ 5:00 PM     │ 8.00  │ $25  │ $200.00 │
└─────────────┴─────────────┴───────┴──────┴─────────┘
```

### **After (Multiple Rows):**
```
┌─────────────┬─────────────┬───────┬──────┬─────────┐
│ Start Time  │ Finish Time │ Hours │ Rate │ Total   │
├─────────────┼─────────────┼───────┼──────┼─────────┤
│ 9:00 AM     │ 12:00 PM    │ 3.00  │ $25  │ $75.00  │
│ 1:00 PM     │ 5:00 PM     │ 4.00  │ $25  │ $100.00 │
│ 6:00 PM     │ 9:00 PM     │ 3.00  │ $30  │ $90.00  │
├─────────────┼─────────────┼───────┼──────┼─────────┤
│             │ TOTAL       │ 10.00 │      │ $265.00 │
└─────────────┴─────────────┴───────┴──────┴─────────┘
```

## 🔧 **Integration with Your Data**

### **Option 1: If You Have Multiple Rows API**
Replace the sample data with actual API call:

```typescript
const fetchTimesheetRows = async (timesheetId: number) => {
  Service.getTimesheetRows(
    (response: any) => {
      console.log('Multiple timesheet rows response:', response);
      const rows = Array.isArray(response) ? response : response?.data || [];
      setTimesheetRows(rows);
    },
    (error: any) => {
      console.error('Error fetching timesheet rows:', error);
      setTimesheetRows([]);
    },
    timesheetId
  );
};
```

### **Option 2: If You Have Array in Current API Response**
If your `getTimeSheetDetails` already returns multiple rows:

```typescript
// In useTimesheetDetails.ts, check if response contains array
const fetchTimesheetDetails = async (timesheetId: number) => {
  Service.getTimeSheetDetails(
    (response: any) => {
      // Check if response contains multiple timesheet rows
      if (response.timesheetRows && Array.isArray(response.timesheetRows)) {
        setTimesheetRows(response.timesheetRows);
      }
      // Process single timesheet details as before
      // ...
    },
    // ...
  );
};
```

### **Option 3: Create Multiple Rows from Single Data**
Split single timesheet into multiple periods:

```typescript
const createMultipleRowsFromSingle = (timesheetDetails: TimesheetDetails) => {
  const startTime = timesheetDetails.jobStartTime; // e.g., "09:00"
  const endTime = timesheetDetails.jobEndTime;     // e.g., "17:00"
  const rate = timesheetDetails.price || 25;
  
  // Split into morning and afternoon shifts
  const rows = [
    {
      start: startTime,
      finish: "12:00",
      hours: 3,
      rate: rate,
      total: 3 * rate,
      id: 1
    },
    {
      start: "13:00",
      finish: endTime,
      hours: 4,
      rate: rate,
      total: 4 * rate,
      id: 2
    }
  ];
  
  setTimesheetRows(rows);
};
```

## 🎯 **Benefits**

### **✅ For Users:**
- See complete work schedule in one view
- Edit multiple time periods simultaneously
- Better representation of actual work patterns
- Clear breakdown of different shifts/rates

### **✅ For Developers:**
- Flexible data structure supports various scenarios
- Backward compatible with existing single-row data
- Easy to extend with additional timesheet features
- Clean separation between display and edit functionality

## 📝 **Next Steps**

1. **Replace Sample Data**: Update `fetchTimesheetRows()` with your actual data source
2. **Test Multiple Scenarios**: Verify with different numbers of rows (1, 2, 5+ rows)
3. **Customize Display**: Adjust table styling if needed for multiple rows
4. **Add Validation**: Ensure time periods don't overlap
5. **Enhance EditTimesheet**: Add features like adding/removing rows

## 🚀 **Summary**

Your timesheet popup now:
- ✅ **Displays multiple timesheet rows** from an array
- ✅ **Shows all data** instead of just one object
- ✅ **Passes complete array to EditTimesheet** component
- ✅ **Maintains AM/PM time display** and calculations
- ✅ **Supports both single and multiple row scenarios**

The implementation is ready to use with your actual data! 🎉
