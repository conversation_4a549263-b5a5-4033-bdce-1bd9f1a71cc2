.dialogContent {
  background-color: #fff;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow-y: auto;
}

.employeeHeader {
  font-size: clamp(20px, 4vw, 24px);
  color: #585858;
  margin-top: 0;
  margin-bottom: 3px;
  font-weight: 700;
}

.benefits {
  width: 100% !important;
  max-width: max-content;
  padding-inline: 25px;
}

.benefitsContent {
  padding: clamp(15px, 3vw, 25px) clamp(15px, 3vw, 30px);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.custom-radio-container .p-radiobutton {
  width: 20px; /* Adjust size */
  height: 20px;
  border: 2px solid #28a745; /* Green border color */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.custom-radio-container .p-radiobutton input[type="radio"] {
  opacity: 0;
  position: absolute;
}

.custom-radio-container .p-radiobutton .p-radiobutton-box {
  width: 12px; /* Adjust inner circle size */
  height: 12px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
}

.custom-radio-container .p-radiobutton input[type="radio"]:checked ~ .p-radiobutton-box {
  background-color: #28a745; /* Inner green color */
  box-shadow: 0 0 0 3px #fff;
}


.closBtn {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 25px;
  height: 27px;
  color: #585858;
  cursor: pointer;
  right: 38px;
  top: 8px;
  z-index: 1;
}

.benefitsForm {
  width: 100%;
  margin: 1rem 0;
}

.benefitsTitle {
  font-size: clamp(18px, 3vw, 24px);
  font-weight: bold;
  margin-bottom: 1rem;
}

.benefitsDescription {
  font-size: clamp(14px, 3vw, 16px);
  color: #585858;
  margin-top: 0;
  width: 100%;
  font-weight: 500;
}

.benefitsFormRow {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.benefitsFormRow > div {
  /* flex: 1 1 300px; */
  min-width: 0;
}

.benefitsInput {
  width: 100%;
}

.input-placeholder {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.benefitsRadioGroup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.benefitsRadioGroup > div:first-child {
  font-size: clamp(14px, 3vw, 16px);
}

.benefitsError {
  color: #ff0000;
  margin: 0.5rem 0;
  font-size: clamp(12px, 2.5vw, 14px);
}

.benefitsButtons {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

/* .benefitsSuccess {
  text-align: center;
  padding: clamp(1rem, 3vw, 2rem);
} */

.companySize {
  width: 100% !important;
  border: none !important;

}
.companySize > div {
  background-color: #f0f4f7 !important;
}
.companySize > span {
  border-top-right-radius:0px;
  border-bottom-right-radius: 0px;
}
.benefitsSubmit{
  background-color: #FFA500;
  box-shadow: 0px 4px 4px 0px #00000040;
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  width: 178px;
  height: 45px;
  cursor: pointer;

}
.benefitsLater{
  background-color: transparent;
  box-shadow: 0px 0px 4px 0px #00000040;
  border: none;
  border-radius: 10px;
  color: #585858;
  font-size: 16px;
  font-weight: 700;
  width: 178px;
  height: 36px;
  text-decoration: underline;
  cursor: pointer;
  text-wrap: noWrap;
}
/* Add these styles to your employee-benefits.module.css file */

:global(.p-radiobutton) {
  width: 18px !important;
  height: 18px !important;
}

:global(.p-radiobutton .p-radiobutton-box) {
  width: 18px !important;
  height: 18px !important;
  border: 1px solid #585858 !important;
  background-color: #FFFFFF !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  position: relative !important;
  transition: border-color 0.3s ease, background-color 0.3s ease !important;
}

:global(.p-radiobutton .p-radiobutton-box.p-highlight) {
  border: 1px solid #179D52 !important;
  background-color: #FFFFFF !important;
}

:global(.p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover) {
  border-color: #179D52 !important;
  background-color: #FFFFFF !important;
}

:global(.p-radiobutton .p-radiobutton-box .p-radiobutton-icon) {
  width: 14px !important;
  height: 14px !important;
  background-color: #179D52 !important;
  border-radius: 50% !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  transition: transform 0.2s ease !important;
}

/* Style for the radio button label */
:global(.p-radiobutton-label) {
  margin-left: 8px !important;
  color: #585858 !important;
  cursor: pointer !important;
}

.benefitsRadioGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
}

.benefitsRadioGroup > div:first-child {
  font-size: 16px;
  color: #585858;
}

.benefitsRadioGroup .flex {
  display: flex;
  gap: 24px;
}

.benefitsRadioGroup .flex .flex {
  align-items: center;
  gap: 8px;
}

/* Media Queries for specific screen sizes */
@media screen and (max-width: 768px) {
  .benefitsRadioGroup {
    gap: 0.5rem;
  }
  
  .benefitsFormRow {
    gap: 0.75rem;
  }
  
  .benefitsButtons {
    justify-content: center;
  }
  
  .benefitsButtons button {
    width: 100%;
    max-width: 300px;
  }
}

@media screen and (max-width: 480px) {
  .dialogContent {
    border-radius: 10px;
  }
  
  .benefitsContent {
    padding: 15px;
  }
  
  .benefitsFormRow > div {
    flex: 1 1 100%;
  }
}
.companySizeEmp {
  width: 42% !important;
  border: none !important;

}
.companySizeEmp > div {
  background-color: #f0f4f7 !important;
}
.companySizeEmp > span {
  border-top-right-radius:0px;
  border-bottom-right-radius: 0px;
  justify-content: center !important;
}