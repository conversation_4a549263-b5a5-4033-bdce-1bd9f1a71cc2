import React, { useEffect, useState } from 'react';
import 'primereact/resources/themes/saga-green/theme.css'; // Or your preferred base theme
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import Payments from './Tabs/Payments';
import Chat from './Tabs/Chat';
import TimeSheet from './Tabs/TimeSheet';
import { useLocation, useNavigate } from 'react-router-dom';
import { IframeBridge } from '../../../services/IframeBridge';
import utils from '../../../components/utils/util';
import CookiesConstant from '../../../helper/cookiesConst';
import useLoader from '../../../hooks/LoaderHook';
import { RootState } from '../../../store';
import { useSelector } from 'react-redux';
import timesheetIcon from '../../../assets/images/Icons/elements.png';


const PaymentsLayout: React.FC = () => {
  const [activeBottomNav, setActiveBottomNav] = useState('Timesheet');
  const { enableLoader, disableLoader } = useLoader();
  const navigate = useNavigate();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const bottomNavItems = [
    { key: 'Timesheet', label: 'Timesheet', icon: 'pi pi-file-edit', },
    { key: 'Payments', label: 'Payments', icon: 'pi pi-credit-card' },
    {
      key: 'Chat', label: 'Chat', icon: 'pi pi-comments', onClick: () => {
        IframeBridge.sendToParent({
          type: "navigateConversations",
        });
      }
    },
  ];
  useEffect(() => {
    const id = requestAnimationFrame(() => {
      IframeBridge.sendToParent({
        type: "canProcced",
        data: null,
      });
      disableLoader();
    });
    return () => cancelAnimationFrame(id);
  }, []);

  const renderMainContent = () => {
    switch (activeBottomNav) {
      case 'Timesheet':
        return (
          <TimeSheet />
        );
      case 'Payments':
        return <Payments />;
      case 'Chat':
        return <Chat />;
      default:
        return <TimeSheet
        />
    }
  };
  return (
    <>
      <div className="phone-screen-container">
        {/* Header */}
        <div
          style={{
            backgroundColor: '#179D52',
            color: 'white',
            padding: '15px 20px',
            display: 'flex',
            alignItems: 'center',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            flexShrink: 0,
            position: 'relative',
          }}
        >
          <i
            className="pi pi-angle-left"
            style={{
              position: 'absolute',
              left: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              cursor: 'pointer',
              fontSize: '1.5rem',
            }}
            onClick={() => {
              IframeBridge.sendToParent({
                type: "goBackFromInAppPayments",
              });
              // const inIframe = window.self !== window.top;
              if (!inIframe) {
                const path = clientType === 2 ? "/business-home" : "/parent-home";
                navigate(path);
              }
            }}
          >
          </i>
          <div style={{ flexGrow: 1, textAlign: 'center' }}>
            {activeBottomNav}
          </div>
        </div>

        <div style={{ flexGrow: 1, backgroundColor: '#ffffff', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
          {renderMainContent()}
        </div>
        {/* Bottom Navigation */}
        <div
          style={{
            backgroundColor: '#179D52',
            color: 'white',
            display: 'flex',
            justifyContent: 'space-around',
            padding: '8px 0',
            borderTop: '1px solid rgba(255,255,255,0.2)',
            flexShrink: 0, // Prevent footer from shrinking
          }}
        >
          {bottomNavItems.map((item) => (
            <div
              key={item.key}
              // onClick={() => setActiveBottomNav(item.key)}
              onClick={() => {
                setActiveBottomNav(item.key);
                if (item.onClick) {
                  item.onClick();
                }
              }}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '5px 10px',
                color: activeBottomNav === item.key ? 'white' : 'rgba(255,255,255,0.7)',
                borderBottom: activeBottomNav === item.key ? '3px solid white' : '3px solid transparent',
                transition: 'color 0.2s, border-bottom-color 0.2s',
                flex: 1,
                textAlign: 'center'
              }}
            >
              <i className={item.icon} style={{ fontSize: '1.4rem', marginBottom: '3px' }}

              ></i>
              
              <span style={{ fontSize: '14px', color: '#ffffff', fontWeight: activeBottomNav === item.key ? 'bold' : 'normal' }}>{item.label}</span>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default PaymentsLayout;