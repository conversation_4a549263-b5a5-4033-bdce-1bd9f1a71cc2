import { useState, useCallback } from 'react';
import useLoader from './LoaderHook';

interface UseApiDataOptions<T> {
  initialData?: T[];
  transform?: (item: any) => T;
  onSuccess?: (data: T[]) => void;
  onError?: (error: any) => void;
}

interface UseApiDataReturn<T> {
  data: T[];
  isLoading: boolean;
  error: string | null;
  fetchData: (apiCall: (successCallback: (response: any) => void, errorCallback: (error: any) => void) => void) => Promise<void>;
  refreshData: () => void;
  setData: (data: T[]) => void;
  clearData: () => void;
}

/**
 * Generic hook for fetching and managing API data
 * @param options Configuration options for the hook
 * @returns Object with data, loading state, error state, and methods
 */
export const useApiData = <T = any>(options: UseApiDataOptions<T> = {}): UseApiDataReturn<T> => {
  const { initialData = [], transform, onSuccess, onError } = options;
  
  const [data, setData] = useState<T[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastApiCall, setLastApiCall] = useState<(() => Promise<void>) | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  const fetchData = useCallback(async (
    apiCall: (successCallback: (response: any) => void, errorCallback: (error: any) => void) => void
  ): Promise<void> => {
    setIsLoading(true);
    setError(null);
    enableLoader();

    // Store the API call for refresh functionality
    const apiCallWrapper = async () => {
      return new Promise<void>((resolve, reject) => {
        apiCall(
          (response: any) => {
            try {
              console.log("API Response:", response);
              
              let processedData: T[];
              if (Array.isArray(response)) {
                processedData = transform ? response.map(transform) : response;
              } else if (response?.data && Array.isArray(response.data)) {
                processedData = transform ? response.data.map(transform) : response.data;
              } else {
                console.warn("Expected array response, got:", typeof response);
                processedData = [];
              }

              console.log("Processed data:", processedData);
              setData(processedData);
              onSuccess?.(processedData);
              resolve();
            } catch (err) {
              console.error('Error processing API response:', err);
              const errorMsg = err instanceof Error ? err.message : 'Failed to process response';
              setError(errorMsg);
              onError?.(err);
              reject(err);
            }
          },
          (error: any) => {
            console.error('API call failed:', error);
            const errorMsg = error?.message || 'API call failed';
            setError(errorMsg);
            onError?.(error);
            reject(error);
          }
        );
      });
    };

    setLastApiCall(() => apiCallWrapper);

    try {
      await apiCallWrapper();
    } catch (err) {
      console.error('Fetch data failed:', err);
    } finally {
      setIsLoading(false);
      disableLoader();
    }
  }, [transform, onSuccess, onError, enableLoader, disableLoader]);

  const refreshData = useCallback(async () => {
    if (lastApiCall) {
      await lastApiCall();
    }
  }, [lastApiCall]);

  const clearData = useCallback(() => {
    setData([]);
    setError(null);
  }, []);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    setData,
    clearData
  };
};

export default useApiData;
