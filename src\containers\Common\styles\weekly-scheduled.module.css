.weeklyScheduled {
  display: flex;
  flex-direction: column;
  height: 100%;
  -webkit-user-select: none;
  user-select: none;
  padding-inline: 120px;
  width: 100%;
  padding-top: 30px;
}

.daySelection {
  display: flex;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}
.weeklyScheduledTitle {
  margin: 0px;
}

.buttonRow {
  display: flex;
  gap: 20px;
  margin: 10px 0;
}

.controlButton {
  padding: 8px 16px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.controlButtonOne {
  width: max-content;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  background-color: #2f9acd;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  height: 41px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.controlButtonTwo {
  width: max-content;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  background-color: #8577db;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  height: 41px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.controlButtonThree {
  width: max-content;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  background-color: #1f9eab;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  height: 41px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.selectedDaysContainer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

.dayCard {
  display: flex;
  flex-direction: row;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 8px;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  width: 864px;
  height: 115px;
  padding-left: 30px;
  gap: 15px;
}

.dayCardOutside {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  width: min-content;
  height: 115px;
  gap: 20px;
}

.enabledText {
  font-size: 30px;
  font-weight: 700;
  line-height: 45px;
  color: #179d52;
  width: 88px;
  margin: 0px;
  margin-top: 15px;
}
.disabledText {
  font-size: 30px;
  font-weight: 700;
  line-height: 45px;
  color: #585858;
  width: 88px;
  margin: 0px;
  margin-top: 15px;
}
.formateDate{
  font-size: 14px !important;
  font-weight: 400  !important;
  color: #585858  !important;
  line-height: 21px  !important;
}

.dayCard p {
  margin: 0;
  text-wrap: nowrap;
  font-size: 14px;
  font-weight: 600;
  color: #585858;
  line-height: 21px;
}
.customTime input:enabled:focus {
  box-shadow: none !important;
  border-right: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-left: none !important;
}

.customTimeSec input:enabled:focus {
  box-shadow: none !important;
  border-right: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-left: none !important;
}
.customTime {
  display: flex !important;
  flex-direction: row !important;
  height: 42px !important;
  align-items: unset !important;
  width: 100% !important;
  width: min-content !important;
  margin-top: 10px !important;
  border-radius: 12px !important;
}
.customTime input {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  width: 80px !important;
  height: 42px !important;
  padding: 0px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}
.customTime button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  padding: 20px !important;
  height: 42px !important;
}
.customTimeSec {
  display: flex !important;
  flex-direction: row !important;
  height: 42px !important;
  align-items: unset !important;
  width: min-content !important;
  height: 42px !important;
  margin-top: 10px !important;
}
.customTimeSec input {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  width: 80px !important;
  height: 42px !important;
  padding: 0px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}
.customTimeSec button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 1px solid #dfdfdf !important;
  border-top: 1px solid #dfdfdf !important;
  border-bottom: 1px solid #dfdfdf !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  padding: 20px !important;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  padding: 8px;
}

.cancelButton {
  background-color: transparent;
  border: 1px solid #4e4e4e;
  color: #cdcdcd;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.saveButton {
  background-color: #ff9800;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}
.saveButton:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  opacity: 0.6;
}
.errorMessageTime {
  color: #ffffff;
  text-align: end;
  background-color: rgba(255, 99, 89, 1);
  width: max-content;
  border-radius: 10px;
  padding: 3px;
  padding-inline: 5px;
  position: relative;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.errorMessageTime::before {
  content: "✔";
  margin-right: 5px;
  color: #ffffff;
  font-size: 1em;
}
.removeButton {
  background-color: transparent;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  color: #ff6359;
  border: none;
  text-wrap: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}
.dayContainer.disabled {
  opacity: 0.5;

  cursor: not-allowed;
  color: #dfdfdf;
}

.dayCard h3.disabled {
  font-size: 30px;
  font-weight: 700;
  line-height: 45px;
  color: #dfdfdf;
}
.buttonGroup {
  width: max-content;
  display: flex;
  gap: 17px;
}
.dayByDayButton {
  width: 423px;
  height: 46px;
  border-radius: 5px;
  border: 1px solid #585858;
  background-color: #ffffff;
  color: #585858;
  font-size: 16px;
  font-weight: 700;
  padding: 10px;
  cursor: pointer;
}
.autoFillButton {
  width: 423px;
  height: 46px;
  background-color: #ffa500;
  border: none;
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
}

.greenBorderSec {
  border: 2px solid #179d52; /* Green border for enabled divs */
}
.durationCount {
  font-size: 30px !important;
  font-weight: 600 !important;
  line-height: 45px !important;
  text-align: center !important;
  color: #585858 !important;
}
.footer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: auto;
}

.footerActions {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  padding-bottom: 25px;
}
