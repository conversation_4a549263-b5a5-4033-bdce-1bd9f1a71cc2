import { useState, useEffect } from 'react';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import useIsMobile from '../../../hooks/useIsMobile';

const OddJobs = () => {
    const session = useSelector((state: RootState) => state.sessionInfo.data) as { oddJobPreferences?: { [key: string]: boolean } };
    const dispatch = useDispatch<AppDispatch>();
    const [OddJobsRadioState, setOddJobsRadioState] = useState('no');
    const { disableLoader, enableLoader } = useLoader();
    const {isMobile}=useIsMobile();
    const [oddjobError, setoddjobError] = useState<boolean>(false);
    const jobs = [
        { label: 'Laundry', key: 'laundry', description: 'Washing, drying, folding & putting clothes away, ironing etc.' },
        { label: 'Errand running', key: 'errands', description: 'Shopping, picking up dry cleaning, mailing packages etc.' },
        { label: 'Outdoor chores', key: 'outdoorChores', description: 'Sweeping & tidying, car washing, taking the bins in & out etc.' },
        { label: 'Help for the elderly', key: 'elderlyHelp', description: 'Odd jobs & errands for elderly parents and neighbours' },
        { label: 'Other Odd Jobs', key: 'otherOddJobs', description: 'Admin, tech support, and other household tasks' },
    ];

    const [checkedState, setCheckedState] = useState<boolean[]>(new Array(jobs.length).fill(false));
    useEffect(() => {
        const updatedCheckedState = jobs.map((job) => session.oddJobPreferences?.[job.key] || false);
        setCheckedState(updatedCheckedState);
        if (updatedCheckedState.some((checked) => checked)) {
            setOddJobsRadioState('yes');
        }
    }, [session.oddJobPreferences]);
    const handleCheckboxChange = (index: number) => {
        const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
        setCheckedState(updatedCheckedState);
        if (updatedCheckedState.some((checked) => checked)) {
            setOddJobsRadioState('yes');
        } else {
            setOddJobsRadioState('no');
        }
    };

    const handleOddJobsRadioChange = (value: string) => {
        setOddJobsRadioState(value);
    };

    const handleSave = () => {
        let hasError = false;
        const oddJobPreferences = jobs.reduce((preferences, job, index) => {
            preferences[job.key] = checkedState[index];
            return preferences;
        }, {});
        const isAnyCheckboxSelected = checkedState.some((checked) => checked);
        if (!isAnyCheckboxSelected) {
          setoddjobError(true);
          hasError = true;
        } else {
            setoddjobError(false);
        }
        if (hasError) return;
        enableLoader();
        const payload = {
            ...session,
            oddJobPreferences,
        };

        dispatch(updateSessionInfo({ payload })).finally(() => {
            disableLoader();
        });
    };

    return (
        <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }} className={styles.utilcontainerhelper}>
            <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
                <header className={styles.utilheader}>
                    <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">OddJobs</h1>
                </header>
                <CustomButton
                    label="Save"
                    className={myfamilystyles.customButton}
                    style={{ margin: '0', width: '150px' }}
                    onClick={handleSave}
                />
            </div>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#179d52' }}
                >
                    Would you like to work as Odd Jobs?
                </h1>
            </div>
            <div>
                <div className="flex justify-content-start items-center mt-3 gap-2">
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="childminder-radio"
                            value="yes"
                            checked={OddJobsRadioState === 'yes'}
                            onChange={() => handleOddJobsRadioChange('yes')}
                            className="cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="childminder-radio"
                            value="no"
                            checked={OddJobsRadioState === 'no'}
                            onChange={() => handleOddJobsRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
            </div>
            {OddJobsRadioState === 'yes' && (
                <>
                    <div>
                        <h1 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2" style={{ fontSize: '18px', color: oddjobError ? 'red' : '#179d52' }}>
                            Select the Odd Jobs you would be willing to do in your neighbourhood.
                        </h1>
                    </div>
                    <div className="flex flex-column justify-content-center">
                        {jobs.map((job, index) => (
                            <label key={job.label} className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="checkbox"
                                    className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                    checked={checkedState[index]}
                                    onChange={() => handleCheckboxChange(index)}
                                    style={{ fontSize: '18px' }}
                                />
                                <div className="flex flex-column items-center">
                                    <div className="txt-clr" style={{ fontSize: '16px' }}>
                                        {job.label}
                                    </div>
                                    <div className="txt-clr" style={{ fontSize: '16px' }}>
                                        {job.description}
                                    </div>
                                </div>
                            </label>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
};

export default OddJobs;
