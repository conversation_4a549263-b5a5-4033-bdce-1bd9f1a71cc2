import React, { useEffect, useState } from 'react'
import useLoader from '../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import Service from '../../../services/services';
import { fetchNoRefreshSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import ProfileStrength from './ProfileStrength';
import ArrowLeft from '../../../assets/images/Icons/arrow-left.png';
import useIsMobile from '../../../hooks/useIsMobile';

const PrivacySettings = () => {
    const dispatch = useDispatch<AppDispatch>();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [showProfileStrength, setShowProfileStrength] = useState(false);
    const session = useSelector((state: RootState) => state.sessionInfo.data) as { isPublicProfileAccessible?: boolean };
    const [privacySetting, setPrivacySetting] = useState('null');
    const [isPublicProfileAccessible, setIsPublicProfileAccessible] = useState<boolean | null>(null);
    const { disableLoader, enableLoader } = useLoader();
    const {isMobile}=useIsMobile()
    const handlePrivacyChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setPrivacySetting(value);
        setIsPublicProfileAccessible(value === 'public');
    };

    const privacyOptions = [
        {
            value: "private",
            label: "Private",
            description:
                "Your profile information is only visible to other Juggle Street users.",
        },
        {
            value: "public",
            label: "Public",
            description:
                "Your general profile info and photo are visible to the public. This does NOT include personal details such as phone number, email, and home address.",
        },
    ];
    const sessionAccessibility = session?.isPublicProfileAccessible ?? null;
    useEffect(() => {
        setPrivacySetting(sessionAccessibility ? 'public' : 'private');
    }, [sessionAccessibility]);
    const handleSave = () => {
        enableLoader();
        const payload = {
            isPublicProfileAccessible: isPublicProfileAccessible,
            isPublicProfilePhotoAccessible: isPublicProfileAccessible
        };
        Service.updateProfileVisibility(payload,
            () => {
                dispatch(fetchNoRefreshSessionInfo());
                disableLoader();
            },
            (error) => {
                disableLoader();
                
            });
    };

    return (
        <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }}className={styles.utilcontainerhelper}>
            <div>
                {/* {!showProfileStrength ? (
                    <div
                        className="flex items-center justify-between mb-4"
                        style={{ width: '100%' }}
                    >
                        <div
                            className="flex gap-2 cursor-pointer align-items-center"
                            style={{
                                border: '1px solid #F1F1F1',
                                padding: '10px 25px',
                                borderRadius: '20px',
                            }}
                            onClick={() => setShowProfileStrength(true)}
                        >
                            <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '14px',
                                    color: '#585858',
                                }}
                            >
                                Go Back
                            </p>
                        </div>
                        <div style={{ width: '75px' }}></div>
                    </div>
                ) : (
                    <ProfileStrength onBack={() => setShowProfileStrength(false)} />
                )} */}
            </div>
            <div
                className="flex align-items-center justify-content-between mb-2 mt-1
                     flex-wrap"
            >
                <header className={styles.utilheader}>
                    <h1 style={{fontSize:isMobile && "24px"}}  className="p-0 m-0">Privacy Settings</h1>
                </header>
                <CustomButton
                    label={"Save"}
                    className={`${myfamilystyles.customButton}`}
                    style={{ margin: "0", width: "150px" }}
                    onClick={handleSave}
                ></CustomButton>
            </div>
            <h1
                className="p-0 m-0 txt-clr flex-wrap font-medium line-height-1"
                style={{ fontSize: "16px", color: "#179d52" }}
            >
                Juggle Street Profile
            </h1>
            <div className="mt-3">
                {privacyOptions.map((option) => (
                    <div key={option.value} className="mb-2">
                        <div className="flex items-center">
                            <input
                                type="radio"
                                id={option.value}
                                name="privacy"
                                value={option.value}
                                checked={privacySetting === option.value}
                                onChange={handlePrivacyChange}
                                className="cursor-pointer"
                            />

                            <label
                                htmlFor={option.value}
                                className="cursor-pointer txt-clr font-bold"
                                style={{ fontSize: "16px" }}
                            >
                                {option.label}
                            </label>
                        </div>
                        <p
                            className="p-0 m-0 pl-4 cursor-pointer txt-clr font-medium"
                            style={{ fontSize: "14px" }}
                        >
                            {option.description}
                        </p>
                    </div>
                ))}
                <div>
                    <h1
                        className="p-0 m-0 pl-4 mt-3 txt-clr font-medium"
                        style={{ fontSize: "14px" }}
                    >
                        <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                        having a Public profile will extend your reach
                        and improve your chances of being offered jobs. You will also be
                        able to share your Public profile on social media.{" "}
                    </h1>
                </div>
            </div>
        </div>
    )
}

export default PrivacySettings