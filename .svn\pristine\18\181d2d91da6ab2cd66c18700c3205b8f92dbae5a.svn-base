import { Slider } from 'primereact/slider';
import { InputNumber } from 'primereact/inputnumber';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';

const SuperHelperSettings = () => {
    const session = useSelector((state: RootState) => state.sessionInfo.data);
    const [hasMinHourlyRate, setHasMinHourlyRate] = useState('no');
    const [visibilityLimit, setVisibilityLimit] = useState(session["visibilityLimit"]);
    const [minHourlyRate, setMinHourlyRate] = useState<number | null>(session["provider"]["minHourlyRate"] || null);
    const [locationRadioState, setLocationRadioState] = useState('yes');
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const [hasMinHourlyRateError, sethasMinHourlyRateError] = useState("");
    const [locationError, setLocationError] = useState("");
    // const [suburbStats, setSuburbStats] = useState<any>(null);
    const { disableLoader, enableLoader } = useLoader();
    const dispatch = useDispatch<AppDispatch>();

    //  useEffect(() => {
    //     if (session["country"] === "nz") return;

    //     const fetchSuburbStats = async () => {
    //         const term = session["defaultAddress"]["formattedSuburb"];
    //         const suburbs = await suburbsearch({ term, country: session["country"] });

    //         const matchByPostcode = suburbs.find(
    //             (s: any) => s.state === session["defaultAddress"]["state"] && s.postcode === session["defaultAddress"]["postCode"]
    //         );

    //         setSuburbStats(matchByPostcode || suburbs.find((s: any) => s.state === session["defaultAddress"].["state"] && s.name === session["defaultAddress"].["formattedSuburb"]));
    //     };

    //     fetchSuburbStats();
    // }, [session, suburbSearchService]);
    const handlelocationRadioChange = (value: string) => {
        setLocationRadioState(value);
    };
    const handleMinHourlyRateRadioChange = (value: string) => {
        setHasMinHourlyRate(value);
    };
    const handleSaveLocation = () => {
        showConfirmationPopup(
            "JOB INVITE ALERT!",
            "Default is 10km - if you change this setting you will only receive job invites from employers within this distance. This will limit the number of invites you receive.",
            "Continue",
            null,
            () => {
                if (!visibilityLimit || visibilityLimit == 0) {
                    setLocationError("Please limit the location.");
                    return;
                }
                setLocationError('');
                enableLoader();
                const payload = {
                    ...session,
                    visibilityLimit
                };
                dispatch(updateSessionInfo({ payload })).finally(() => {
                    disableLoader();
                });
            }
        );
    };
    const handleSaveRate = () => {
        showConfirmationPopup(
            "JOB INVITE ALERT!",
            "If you set a minimum rate, you will only receive job invitations at, and above this hourly rate. This will limit the number of job invites you receive.",
            "Continue",
            null,
            () => {
                if (!minHourlyRate || minHourlyRate == 0) {
                    sethasMinHourlyRateError("Please enter minimum hourly rate.");
                    return;
                }
                sethasMinHourlyRateError('');
                enableLoader();
                const payload = {
                    ...session,
                    provider: {
                        ...session["provider"],
                        minHourlyRate: minHourlyRate
                    },
                };
                dispatch(updateSessionInfo({ payload })).finally(() => {
                    disableLoader();
                });
            }
        );
    };
    return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between', padding: '25px', color: '#585858' }}>
            <div className='flex align-items-center justify-content-between mb-2 mt-1
                     flex-wrap'>
                <header className={styles.utilheader}>
                    <h1 className="p-0 m-0">Super Helper Settings</h1>
                </header>
            </div>
            <span className="m-0 p-1 txt-clr font-medium line-height-1 mt-2" style={{ fontSize: '16px', color: '#179d52' }}>Congratulations on becoming a Super Helper</span>
            <p>This award recognizes your ongoing commitment to Juggle Street, the number of jobs you have completed, your responsiveness and your consistently high ratings.
                You now have access to these settings:</p>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#179d52' }}
                >
                    Would you like to limit the location of your jobs?
                </h1>
            </div>
            <div>
                <label className="cursor-pointer">
                    <input
                        type="radio"
                        value="yes"
                        checked={locationRadioState === 'yes'}
                        onChange={() => handlelocationRadioChange('yes')}
                        className="cursor-pointer"
                    />
                    Yes
                </label>
                <label className="cursor-pointer">
                    <input
                        type="radio"
                        value="no"
                        checked={locationRadioState === 'no'}
                        onChange={() => handlelocationRadioChange('no')}
                        className="cursor-pointer"
                    />
                    No
                </label>
            </div>
            {locationRadioState === 'yes' && (
                <>
                    <div className="slider-container mt-3" style={{ position: "relative" }}>
                        <Slider
                            value={visibilityLimit}
                            onChange={(e) => setVisibilityLimit(e.value)}
                            step={2500}
                            min={0}
                            max={10000}
                            className="mt-3"
                        />
                        <div
                            className="slider-marks"
                            style={{
                                position: "absolute",
                                top: "20px",
                                width: "100%",
                                display: "flex",
                                justifyContent: "space-between",
                            }}
                        >
                            <span style={{ fontSize: "12px", color: "#999", position: "absolute", left: "0", top: "-2rem" }}>Home</span>
                            <span style={{ fontSize: "12px", color: "#999", position: "absolute", right: "0", top: "-2rem" }}>Default</span>
                        </div>
                        <div
                            className="slider-marks"
                            style={{
                                position: "absolute",
                                top: "25px",
                                width: "100%",
                                display: "flex",
                                justifyContent: "space-between",
                            }}
                        >
                            {[0, 2500, 5000, 7500, 10000].map((mark) => (
                                <span
                                    key={mark}
                                    style={{
                                        fontSize: "12px",
                                        color: "#999",
                                    }}
                                >
                                    {mark === 0 ? '0km' : mark === 10000 ? '10km' : `${mark / 1000}km`}
                                </span>
                            ))}
                        </div>
                        <div className='mt-3'>
                            {locationError && <small className='p-error'>{locationError}</small>}
                        </div>
                    </div>
                    <div style={{ display: "flex", justifyContent: "center", marginTop: "1.5rem" }}>
                        <CustomButton
                            label={"Save"}
                            className={`${myfamilystyles.customButton}`}
                            style={{ width: "150px" }}
                            onClick={handleSaveLocation}
                        />
                    </div>
                </>
            )}


            <div className='mt-5'>
                <div>
                    <h1
                        className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                        style={{ fontSize: '16px', color: '#179d52' }}
                    >
                        Would you like to set a minimum hourly rate?
                    </h1>
                </div>
                <div>
                    <label className="cursor-pointer">
                        <input
                            type="radio"
                            value="yes"
                            checked={hasMinHourlyRate === 'yes'}
                            onChange={() => handleMinHourlyRateRadioChange('yes')}
                            className="cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="cursor-pointer">
                        <input
                            type="radio"
                            value="no"
                            checked={hasMinHourlyRate === 'no'}
                            onChange={() => handleMinHourlyRateRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
                {hasMinHourlyRate == 'yes' && (
                    <div>
                        <InputNumber
                            value={minHourlyRate}
                            onValueChange={(e) => setMinHourlyRate(e.value)}
                            mode="currency"
                            currency="USD"
                            placeholder='00'
                            className={hasMinHourlyRateError ? 'p-invalid' : ''}
                        /><br></br>
                        {hasMinHourlyRateError && <small className='p-error'>{hasMinHourlyRateError}</small>}
                        <div className='mt-3'>
                            <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                            the average rate in Arncliffe is $35 p/h.
                        </div>
                    </div>
                )}

            </div>
            <CustomButton
                label={"Save"}
                className={`${myfamilystyles.customButton}`}
                style={{ width: "150px", marginTop: "1rem" }}
                onClick={handleSaveRate}
            />
            <div className='mt-3'>
                <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                To maintain Super Helper status, you must complete at least 4 jobs a year and receive five-star reviews at least 80% of the time.
            </div>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
        </div>
    );
};
export default SuperHelperSettings;
