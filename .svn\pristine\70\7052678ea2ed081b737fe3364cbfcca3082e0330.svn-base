.profileActivationContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  padding: 40px;
  padding-inline: 50px;
}

.profileActivationHeader h1 {
  width: 240px;
  height: 48px;
  font-size: 32px;
  font-weight: 700;

  color: #585858;
  line-height: 48px;
}

.profileActivationContent {
  display: flex;
  flex: 1;
  justify-content: space-between;
  gap: 75px;
  padding: 20px 0;
}

.contentLeft {
  width: 381px;
  height: 274px;
  flex: 1;
  padding: 20px;
  background: rgba(241, 241, 241, 0.5);
  border: 1px solid #f0f4f7;
  border-radius: 33px;
}

.contentRight {
  background-color: #ffffff;
  width: 381px;
  height: 274px;
  flex: 1;
  padding: 20px;
}

.steptext {
  font-weight: 400;
  font-size: 16px;
  color: #585858;
  margin-top: -19px;
}

.headerTitle {
  font-weight: 500;
  margin: 0;
  font-size: 24px;
  color: #585858;
  text-wrap: nowrap;
}

.instructionnumber {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #585858;
}

.verificationMessage {
  font-weight: 400;
  width: 307px;
  font-size: 12px;
  color: #585858;
  padding-top: 0.5rem;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 42px;
  width: 100%;
}

.otpinput {
  border: none !important;
  border-radius: 0 !important;
  width: 36px !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  padding: 0px !important;
  text-align: center !important;

  border-color: #585858 !important;
  border-bottom: 2px solid #585858 !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.otpinput:enabled:focus {
  border: none !important;
  border-radius: 0 !important;
  width: 36px !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  padding: 0px !important;
  text-align: center !important;
  border-color: #585858 !important;
  border-bottom: 2px solid #585858 !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.resendLink {
  margin-top: 38px;
  color: #1f9eab;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.codebuttonContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}

.disabledResendLink {
  color: grey;
  /* Grey color for disabled state */
  cursor: not-allowed;
  /* Indicate that it's not clickable */
}

/* SuccessMessage.module.css */

/* Container for success message and icon */
.successMessageContainer {
  display: flex;
  align-items: center;
  gap: 7px;
  line-height: 40px;
}

/* Style for success message text */
.successMessage {
  font-size: 12px;
  font-weight: 400;
  color: #585858;
}

/* Style for success icon */
.successIcon {
  background-color: #179d52;
  color: #ffffff;
  padding: 3px;
  border-radius: 50%;
}

.profileActivationFooter {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.previousBtn {
  padding: 10px 20px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #007bff;
  color: #fff;
}

.skipBtn {
  padding: 10px 20px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #6c757d;
  color: #fff;
}

.idprogressbar {
  width: 100%;
  max-width: 844px;
  height: 0.4375rem;
  margin-top: 1rem;
}

.idprogressbar>div {
  background-color: #179d52 !important;
}

.idprogressbar>div>div {
  display: none;
}

.hoverClass:hover {
  box-shadow: 0 5px 4px 0 rgba(241, 241, 241, 1);
  font-weight: 800 !important;
  text-shadow: 0 5px rgba(241, 241, 241, 1) !important;
}

.profileActivationContainerMobile {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-inline: 15px;
  margin-top: 85px;
  padding-bottom: 40%;
  overflow: auto;
}

.buttonContainerMobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: min-content;
  box-shadow: 0px 0px 8px 0px #00000040;
  background-color: #fff;
}
.customButtonHover:hover {
  font-weight:bold; 
  box-shadow: 0 1px 1px rgba(184, 184, 184, 0.7); 
  background-color: #f0f0f0; 
  cursor: pointer;
}
/* Responsive Design */
@media (max-width: 768px) {
  .profileActivationContent {
    flex-direction: column;
  }

  .contentLeft,
  .contentRight {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .verificationMessage {
    width: 90%;
    /* Adjust width for tablets */
    font-size: 11px;
    /* Slightly reduce font size */
    text-align: center;
    /* Center the text for better readability */
  }
}

/* For screens 480px and below (small mobile devices) */
@media (max-width: 480px) {
  .verificationMessage {
    width: 100%;
    /* Full width on small screens */
    font-size: 10px;
    /* Further reduce font size */
  }
}

@media (max-width: 600px) {
  .headerTitle {
    font-size: 20px;
    /* Slightly reduce font size */
  }
}