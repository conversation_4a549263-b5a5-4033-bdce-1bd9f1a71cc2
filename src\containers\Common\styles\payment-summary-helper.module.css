.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: end;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
}

.overlay.open {
    opacity: 1;
    visibility: visible;
}

.container {
    background: #fff;
    border-radius: 40px;
    width: 100%;
    height: 100%;
    max-height: 80%;;
    overflow-y: auto;
    transform: translateY(100%);
    transition: transform 0.5s ease;
    margin-inline: 8px;
    margin-bottom: 15px;
}

.overlay.open .container {
    transform: translateY(0);
}

.header {
    background: #179D52;
    padding: 16px;

    align-items: center;
    height: 175px;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
}

.Aproovalheader {
    background: #179D52;
    padding: 16px;
    align-items: center;
    height: 98px;
}

.backBtn {
    background: none;
    border: none;
    color: #fff;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    cursor: pointer;
}

.content {
    padding: 16px;
}

.summaryHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    margin-top: -85px;
    background: var(--Background, #F4F4F4);
    border-radius: 20px;
    box-shadow: 0px 4px 4px 0px #00000040;
    padding-inline: 15px;
    padding-block: 20px;

}

.ApprovalsummaryHeader {
    display: flex;
    justify-content: space-between;
    padding-inline: 12px;

}

.summaryHeader h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px;
}

.payerName {
    font-size: 14px;
    font-weight: 700;
    margin: 0;
    color: #585858;
}

.payerAddress {
    font-size: 10px;
    color: #585858;
    font-weight: 400;
    margin: 0;
}

.amountBox {
    background: #fff;
    border-radius: 20px;
    padding: 8px 12px;
    text-align: center;
}

.amountBox span {
    font-size: 12px;
    color: #666;
}

.amount {
    font-size: 26px;
    font-weight: 600;
    color: #585858;
    margin: 0;
}

.jobDetails {
    padding: 12px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.jobDetailRow {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 8px;
}

.jobDetailRow span:first-child {
    color: #666;
}

.jobType {
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    color: #585858;
}

.paymentDetails {
    padding: 12px;

}

.paymentDetails h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 12px;
}

.detailRow,
.detailRowTotal {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 8px;
}

.detailRow span:first-child,
.detailRowTotal span:first-child {
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
}

.detailRowTotal {
    font-weight: 600;
}

.jobTotal {
    color: #585858;
    font-size: 18px;
    font-weight: 800;
}

.infoIcon {
    vertical-align: middle;
}

.footer {
    text-align: center;
    margin-bottom: 34px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.closeBtn {
    width: 100%;
    padding: 12px;
    background: #FFA500;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 20px;
    cursor: pointer;
}