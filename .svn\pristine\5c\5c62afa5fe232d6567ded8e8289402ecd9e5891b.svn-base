/* TimeSheet Card Styles */
.card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s ease;
  overflow: hidden;
  max-width: 400px;
  width: 100%;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header {
  display: flex;
  flex-direction: row;
  gap: 4px;
  margin-bottom: 8px;
}

.status {
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
  background: transparent;
  padding: 0;
  border-radius: 0;
  width: fit-content;
  border: none;
}

.infoRow {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.infoRow img {
  flex-shrink: 0;
  opacity: 0.8;
}

.spantag {
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  line-height: 1.4;
  word-break: break-word;
}

.user {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  min-width: 80px;
  text-align: center;
}

.avatar {
  font-size: 48px;
  color: #6c757d;
  background: #e9ecef;
  border-radius: 50%;
  padding: 4px;
}

.userName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  word-break: break-word;
  line-height: 1.2;
}

.button {
  background: #FFA500;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 32px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 16px 0;
  min-width: 200px;
  width: 90%;
}

.button:hover {
  background: #FF8C00;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(23, 157, 82, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin: 0 16px;
    max-width: none;
  }
  
  .cardContent {
    gap: 10px;
  }
  
  .infoRow {
    gap: 6px;
    margin-bottom: 4px;
  }
  
  .spantag {
    font-size: 13px;
  }
  
  .user {
    min-width: 70px;
    gap: 6px;
  }
  
  .avatar {
    font-size: 40px;
  }
  
  .userName {
    font-size: 13px;
  }
  
  .button {
    padding: 10px 20px;
    font-size: 13px;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .card {
    margin: 0 8px;
  }
  
  .header {
    margin-bottom: 6px;
  }
  
  .status {
    font-size: 13px;
    padding: 3px 6px;
  }
  
  .cardContent {
    gap: 8px;
  }
  
  .infoRow {
    gap: 5px;
    margin-bottom: 3px;
  }
  
  .spantag {
    font-size: 12px;
  }
  
  .user {
    min-width: 60px;
    gap: 4px;
  }
  
  .avatar {
    font-size: 36px;
  }
  
  .userName {
    font-size: 12px;
  }
  
  .button {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 100px;
    margin: 12px 0;
  }
}

/* Status variants */
.status.pending {
  color: #FF8C00;
  background: rgba(255, 140, 0, 0.1);
  border-color: rgba(255, 140, 0, 0.2);
}

.status.approved {
  color: #179D52;
  background: rgba(23, 157, 82, 0.1);
  border-color: rgba(23, 157, 82, 0.2);
}

.status.declined {
  color: #DC3545;
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
}

/* Animation for card entrance */
.card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Divider styling */
.card :global(.p-divider) {
  margin: 0;
  border-color: #f0f0f0;
}

.card :global(.p-divider-horizontal) {
  margin: 16px 0 0 0;
}

.card:focus-within {
  box-shadow: 0 4px 16px rgba(23, 157, 82, 0.15);
}
