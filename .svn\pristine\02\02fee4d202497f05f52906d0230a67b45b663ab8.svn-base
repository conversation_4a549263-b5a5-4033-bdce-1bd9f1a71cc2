import { useNavigate, useSearchParams } from "react-router-dom";
import JobHistoryCard from "../Common/JobHistoryCard";
import NoJobsCard from "../Common/NoJobsCard";
import { ManageJobSectionProps } from "../types";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";
import { Divider } from "primereact/divider";
import styles from '../../styles/job-history-card.module.css';
import { Dialog } from "primereact/dialog";
import { useState } from "react";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";

const JobHistory: React.FC<ManageJobSectionProps> = ({
  jobHistory,
  viewJob,
}) => {
  const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isMobile } = useIsMobile();
  const handleViewJobIdChange = (newJobId: number, index: number) => {
    const updatedParams = new URLSearchParams(searchParams);
    updatedParams.set("rateJobId", String(newJobId));
    updatedParams.set("activeTab", String(index));
    navigate({ search: updatedParams.toString() });
  };
  const [duplicateJob, setDuplicateJob] = useState<boolean>(false);
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };
  const completedJobsCount = jobHistory.filter(
    (job) =>
      job.jobStatus === c.jobStatus.COMPLETED ||
      job.jobStatus === c.jobStatus.FILLED ||
      job.jobStatus === c.jobStatus.FILLED_AND_CANCELLED
  ).length;
  const canceledJobsCount = jobHistory.filter(
    (job) => job.jobStatus === c.jobStatus.CANCELLED
  ).length;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const suffixes = ["st", "nd", "rd", "th"];
    const day = date.getDate();
    const daySuffix =
      day % 10 === 1 && day !== 11
        ? suffixes[0]
        : day % 10 === 2 && day !== 12
          ? suffixes[1]
          : day % 10 === 3 && day !== 13
            ? suffixes[2]
            : suffixes[3];

    // Format the date
    const dayOfWeek = new Intl.DateTimeFormat("en-GB", {
      weekday: "short",
    }).format(date);
    const month = new Intl.DateTimeFormat("en-GB", { month: "long" }).format(
      date
    );

    return `${dayOfWeek} ${day}${daySuffix} of ${month}`;
  };

  const handleDuplicateJob = (jobId: number) => {
    setSelectedJobId(jobId);
    setDuplicateJob(true);
  };

  const confirmDuplicateJob = () => {
    if (selectedJobId !== null) {
      const newParams = new URLSearchParams();
      newParams.set("jobId", String(selectedJobId));
      if (Number(clientType) === 1) {
        navigate({
          pathname: "/parent-home/post-job/duplicate",
          search: newParams.toString(),
        });
      } else {
        navigate({
          pathname: "/business-home/post-job/duplicate",
          search: newParams.toString(),
        });
      }
    }
    setDuplicateJob(false);
    setSelectedJobId(null);
  };
  
  const confirmDuplicateJobWeb = () => {
    if (selectedJobId !== null) {
      const newParams = new URLSearchParams();
      const action = duplicateJob ? "Duplicate" : "Edit";
      newParams.set("jobaction", action); // ← fixed typo here
      if (Number(clientType) === 1) {
        navigate({
          pathname: `/parent-home/job/${selectedJobId}/job-type`,
          search: newParams.toString(),
        });
      } else {
        navigate({
          pathname: `/business-home/job/${selectedJobId}/job-type`,
          search: newParams.toString(),
        });
      }
    }
    setDuplicateJob(false);
    setSelectedJobId(null);
  };
  const avatarUrl = jobHistory.filter(
    (job) => job.awardedImageSrc !== null && job.awardedImageSrc !== undefined)



  return !isMobile ? (
    <>
      <Dialog
        visible={duplicateJob}
        onHide={() => {
          setDuplicateJob(false);
        }}
        style={{
          width: "100vw",
          height: "100vh",
          maxHeight: "none",
          maxWidth: "none",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
        content={
          <div
            className="flex flex-column py-3 px-4"
            style={{
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "20px",
              maxWidth: "610px",
            }}
          >
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "32px",
                color: "#585858",
              }}
            >
              Duplicate Job
            </h1>
            <Divider />
            <p
              className="m-0 p-0 mt-3"
              style={{
                fontWeight: "500",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Use this feature to post a new job based on the current job
              details, all applicable job settings are copied.
            </p>
            <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
              <button
                className="px-4 py-1 cursor-pointer"
                style={{
                  backgroundColor: "#FFFFFF",
                  border: "none",
                  boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                  fontWeight: "500",
                  fontSize: "18px",
                  color: "#585858",
                  textDecoration: "underline",
                  borderRadius: "5px",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  setDuplicateJob(false);
                }}
              >
                No
              </button>
              <button
                className="px-7 py-1 cursor-pointer"
                style={{
                  backgroundColor: "#FFA500",
                  border: "none",
                  boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                  fontWeight: "700",
                  fontSize: "18px",
                  color: "#FFFFFF",
                  textDecoration: "underline",
                  borderRadius: "5px",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  confirmDuplicateJobWeb(); // Call the confirmation function
                }}
              >
                Yes
              </button>
            </div>
          </div>
        }
      />
      <div>
        {jobHistory.length === 0 ? (
          // Show NoJobsCard when no jobs exist
          <div style={{ width: "60%" }} className="mr-4">
            <NoJobsCard
              description={"No job history matches the specified criteria."}
            />
          </div>
        ) : (
          // Show the header and job cards when jobs exist
          <>
            <div
              className="p-3"
              style={{
                border: "1px solid #DFDFDF",

                borderRadius: "10px",
              }}
            >
              {completedJobsCount > 0 && (
                <>
                  <h1
                    className="p-0 m-0 font-bold"
                    style={{ fontSize: "30px", color: "#585858" }}
                  >
                    Completed Jobs{" "}
                    <span style={{ color: "#179D52" }}>
                      ({completedJobsCount})
                    </span>
                  </h1>
                  <div className={styles.jobHistoryRow}>
                    {jobHistory
                      .filter((job) => job.jobStatus === c.jobStatus.COMPLETED)
                      .sort((a, b) => new Date(b.jobDate).getTime() - new Date(a.jobDate).getTime())
                      .map((job, index) => {
                        const ratingProvided =
                          job.jobStatus === c.jobStatus.COMPLETED &&
                          job.isRatedByClient;
                        const awardedApplicants = job.applicants?.filter(
                          (applicant) => applicant.applicationStatus === 6
                        ) || [];

                        const applicantsNumber = awardedApplicants.length;

                        // If exactly 1 awarded applicant, use their details
                        const hasSingleAwardedApplicant = applicantsNumber === 1;
                        const awardedApplicant = hasSingleAwardedApplicant ? awardedApplicants[0] : null;
                        return (
                          <JobHistoryCard
                            key={index}
                            name={
                              hasSingleAwardedApplicant
                                ? `${awardedApplicant.applicantFirstName} ${awardedApplicant.applicantLastInitial || ""}`
                                : job.awardedFirstName && job.awardedLastInitial
                                  ? `${job.awardedFirstName} ${job.awardedLastInitial}`
                                  : "No Name"
                            }
                            jobTitle={
                              job.isTutoringJob === true
                                ? "Tutoring Job"
                                : job.isRecurringJob === true
                                  ? "Recurring Job"
                                  : "One-off Job"
                            }
                            time={
                              job.isRecurringJob || job.isTutoringJob
                                ? `${job.duration} weeks`
                                : job.jobStartTime && job.jobEndTime
                                  ? convertTo12HourFormat(
                                    `${job.jobStartTime}-${job.jobEndTime}`
                                  )
                                  : "No Time Available"
                            }
                            date={
                              job.jobDate
                                ? formatDate(job.jobDate)
                                : "No Date Available"
                            }
                            address={
                              job.formattedAddress
                                ? `${utils.cleanAddress(job.formattedAddress)}`
                                : "No Address Available"
                            }
                            ratingProvided={ratingProvided}
                            ratingCount={job.ratingProvided}
                            jobStatus={job.jobStatus}
                            avatarUrl={
                              hasSingleAwardedApplicant
                                ? awardedApplicant.applicantImageSrc
                                : job.awardedImageSrc || null
                            }
                            onClick={() => handleViewJobIdChange(job.id, 3)}
                            viewJobHistroy={() => viewJob(job.id)}
                            duplicateJob={() => handleDuplicateJob(job.id)}
                            isRecurring={job.isRecurringJob}
                            isTutoring={job.isTutoringJob}
                            applicantsNumber={
                              job.applicants.filter(
                                (applicant) => applicant.applicationStatus === 6
                              ).length
                            } // Updated here
                          />
                        );
                      })}
                  </div>
                  <Divider className="mt-4" />
                </>
              )}
              {canceledJobsCount > 0 && (
                <>
                  <h1
                    className="p-0 m-0 mt-2 font-bold"
                    style={{ fontSize: "30px", color: "#585858" }}
                  >
                    Cancelled Jobs{" "}
                    <span style={{ color: "#FF0000" }}>
                      ({canceledJobsCount})
                    </span>
                  </h1>
                  <div className={styles.jobHistoryRow}>
                    {jobHistory
                      .filter((job) => job.jobStatus === c.jobStatus.CANCELLED)
                      .sort((a, b) => new Date(b.jobDate).getTime() - new Date(a.jobDate).getTime())
                      .map((job, index) => {
                        return (
                          <JobHistoryCard
                            key={index}
                            name={
                              job.awardedFirstName && job.awardedLastInitial
                                ? `${job.awardedFirstName} ${job.awardedLastInitial}`
                                : "No Name"
                            }
                            jobTitle={
                              job.isTutoringJob === true
                                ? "Tutoring Job"
                                : job.isRecurringJob === true
                                  ? "Recurring Job"
                                  : "One-off Job"
                            }
                            time={
                              job.isRecurringJob || job.isTutoringJob
                                ? `${job.duration} weeks`
                                : job.jobStartTime && job.jobEndTime
                                  ? convertTo12HourFormat(
                                    `${job.jobStartTime}-${job.jobEndTime}`
                                  )
                                  : "No Time Available"
                            }
                            date={
                              job.jobDate
                                ? formatDate(job.jobDate)
                                : "No Date Available"
                            }
                            address={
                              job.formattedAddress
                                ? `${utils.cleanAddress(job.formattedAddress)}`
                                : "No Address Available"
                            }
                            ratingProvided={false}
                            jobStatus={job.jobStatus}
                            viewJobHistroy={() => viewJob(job.id)}
                            avatarUrl={job.awardedImageSrc || null}
                            onClick={() => handleViewJobIdChange(job.id, 3)}
                            duplicateJob={() => handleDuplicateJob(job.id)}
                            isRecurring={job.isRecurringJob}
                            isTutoring={job.isTutoringJob}
                          />
                        );
                      })}
                  </div>
                </>
              )}
            </div>
          </>
        )}
      </div>
    </>
  ) : (
    <>
      <Dialog
        visible={duplicateJob}
        onHide={() => {
          setDuplicateJob(false);
        }}
        style={{
          width: "100vw",
          height: "100vh",
          maxHeight: "none",
          maxWidth: "none",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
        content={
          <div
            className="flex flex-column py-3 px-4"
            style={{
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "20px",
              maxWidth: "610px",
            }}
          >
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "32px",
                color: "#585858",
              }}
            >
              Duplicate Job
            </h1>
            <Divider />
            <p
              className="m-0 p-0 mt-3"
              style={{
                fontWeight: "500",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Use this feature to post a new job based on the current job
              details, all applicable job settings are copied.
            </p>
            <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
              <button
                className="px-4 py-1 cursor-pointer"
                style={{
                  backgroundColor: "#FFFFFF",
                  border: "none",
                  boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                  fontWeight: "500",
                  fontSize: "18px",
                  color: "#585858",
                  textDecoration: "underline",
                  borderRadius: "5px",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  setDuplicateJob(false);
                }}
              >
                No
              </button>
              <button
                className="px-7 py-1 cursor-pointer"
                style={{
                  backgroundColor: "#FFA500",
                  border: "none",
                  boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                  fontWeight: "700",
                  fontSize: "18px",
                  color: "#FFFFFF",
                  textDecoration: "underline",
                  borderRadius: "5px",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  confirmDuplicateJob();
                }}
              >
                Yes
              </button>
            </div>
          </div>
        }
      />
      <div>
        {jobHistory.length === 0 ? (
          <div>
            <NoJobsCard
              description={"No job history matches the specified criteria."}
            />
          </div>
        ) : (
          <>
            <div
              className=""
              style={{
                width: "100%",
                borderRadius: "10px",
              }}
            >
              {completedJobsCount > 0 && (
                <>
                  <h1
                    className="p-0 m-0 font-bold"
                    style={{ fontSize: "18px", color: "#585858" }}
                  >
                    Completed Jobs{" "}
                    <span style={{ color: "#179D52", fontSize: "18px" }}>
                      ({completedJobsCount})
                    </span>
                  </h1>


                  {jobHistory
                    .filter((job) => job.jobStatus === c.jobStatus.COMPLETED)
                    .sort((a, b) => new Date(b.jobDate).getTime() - new Date(a.jobDate).getTime())
                    .map((job, index) => {
                      const ratingProvided =
                        job.jobStatus === c.jobStatus.COMPLETED &&
                        job.isRatedByClient;
                      const awardedApplicants = job.applicants?.filter(
                        (applicant) => applicant.applicationStatus === 6
                      ) || [];

                      const applicantsNumber = awardedApplicants.length;

                      // If exactly 1 awarded applicant, use their details
                      const hasSingleAwardedApplicant = applicantsNumber === 1;
                      const awardedApplicant = hasSingleAwardedApplicant ? awardedApplicants[0] : null;
                      return (
                        <JobHistoryCard
                          key={index}
                          name={
                            hasSingleAwardedApplicant
                              ? `${awardedApplicant.applicantFirstName} ${awardedApplicant.applicantLastInitial || ""}`
                              : job.awardedFirstName && job.awardedLastInitial
                                ? `${job.awardedFirstName} ${job.awardedLastInitial}`
                                : "No Name"
                          }
                          jobTitle={
                            job.isTutoringJob === true
                              ? "Tutoring Job"
                              : job.isRecurringJob === true
                                ? "Recurring Job"
                                : "One Off Job"
                          }
                          time={
                            job.isRecurringJob || job.isTutoringJob
                              ? `${job.duration} weeks`
                              : job.jobStartTime && job.jobEndTime
                                ? convertTo12HourFormat(
                                  `${job.jobStartTime}-${job.jobEndTime}`
                                )
                                : "No Time Available"
                          }
                          date={
                            job.jobDate
                              ? formatDate(job.jobDate)
                              : "No Date Available"
                          }
                          address={
                            job.formattedAddress
                              ? `${utils.cleanAddress(job.formattedAddress)}`
                              : "No Address Available"
                          }
                          ratingProvided={ratingProvided}
                          ratingCount={job.ratingProvided}
                          jobStatus={job.jobStatus}
                          avatarUrl={
                            hasSingleAwardedApplicant
                              ? awardedApplicant.applicantImageSrc
                              : job.awardedImageSrc || null
                          }
                          onClick={() => handleViewJobIdChange(job.id, 3)}
                          viewJobHistroy={() => viewJob(job.id)}
                          duplicateJob={() => handleDuplicateJob(job.id)}
                          isRecurring={job.isRecurringJob}
                          isTutoring={job.isTutoringJob}
                          applicantsNumber={
                            job.applicants.filter(
                              (applicant) => applicant.applicationStatus === 6
                            ).length
                          }
                        />
                      );
                    })}
                  <Divider />
                </>
              )}
              {canceledJobsCount > 0 && (
                <>
                  <h1
                    className="p-0 m-0 mt-2 font-bold"
                    style={{ fontSize: "18px", color: "#585858" }}
                  >
                    Cancelled Jobs{" "}
                    <span style={{ color: "#FF0000", fontSize: "18px" }}>
                      ({canceledJobsCount})
                    </span>
                  </h1>
                  {jobHistory
                    .filter((job) => job.jobStatus === c.jobStatus.CANCELLED)
                    .sort((a, b) => new Date(b.jobDate).getTime() - new Date(a.jobDate).getTime())
                    .map((job, index) => {
                      return (
                        <JobHistoryCard
                          key={index}
                          name={
                            job.awardedFirstName && job.awardedLastInitial
                              ? `${job.awardedFirstName} ${job.awardedLastInitial}`
                              : "No Name"
                          }
                          jobTitle={
                            job.isTutoringJob === true
                              ? "Tutoring Job"
                              : job.isRecurringJob === true
                                ? "Recurring Job"
                                : "One Off Job"
                          }
                          time={
                            job.isRecurringJob || job.isTutoringJob
                              ? `${job.duration} weeks`
                              : job.jobStartTime && job.jobEndTime
                                ? convertTo12HourFormat(
                                  `${job.jobStartTime}-${job.jobEndTime}`
                                )
                                : "No Time Available"
                          }
                          date={
                            job.jobDate
                              ? formatDate(job.jobDate)
                              : "No Date Available"
                          }
                          address={
                            job.formattedAddress
                              ? `${utils.cleanAddress(job.formattedAddress)}`
                              : "No Address Available"
                          }
                          ratingProvided={false}
                          jobStatus={job.jobStatus}
                          viewJobHistroy={() => viewJob(job.id)}
                          avatarUrl={job.awardedImageSrc || null}
                          onClick={() => handleViewJobIdChange(job.id, 3)}
                          duplicateJob={() => handleDuplicateJob(job.id)}
                          isRecurring={job.isRecurringJob}
                          isTutoring={job.isTutoringJob}
                        />
                      );
                    })}
                </>
              )}
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default JobHistory;
