import UserPlus from "../../../../assets/images/Icons/user-plus.png";
import UserPlusMobile from "../../../../assets/images/Icons/user-plus-mobile.png";
import EditIcon from "../../../../assets/images/Icons/editIconfiled.png";
import EditIconMobile from "../../../../assets/images/Icons/edit-icon-mobile.png";
import CloseIcon from "../../../../assets/images/Icons/white-close.png";
import SideArrow from "../../../../assets/images/Icons/side-aroow.png";
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
import { useSearchParams } from "react-router-dom";
import useIsMobile from "../../../../hooks/useIsMobile";

interface PopupFunctions {
  show: () => void;
  hide: () => void;
}

const JobFooterPopup = forwardRef<PopupFunctions, { children: React.ReactNode }>(({ children }, ref) => {
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <Dialog
      visible={visible}
      onHide={() => setVisible(false)}
      style={{
        height: "100vh",
        width: "100vw",
        maxWidth: "none",
        maxHeight: "none",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
      content={<>{children}</>}
    />
  ) : (
    <Dialog
      visible={visible}
      onHide={() => setVisible(false)}
      style={{
        height: "100vh",
        width: "100vw",
        maxWidth: "none",
        maxHeight: "none",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
      content={<>{children}</>}
    />
  );
});

const JobFooterEditPopup = forwardRef<PopupFunctions, { onEdit: () => void }>(({ onEdit }, ref) => {
  const popupRef = useRef<PopupFunctions>(null);

  useImperativeHandle(ref, () => ({
    show: () => popupRef.current?.show(),
    hide: () => popupRef.current?.hide(),
  }));
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <JobFooterPopup ref={popupRef}>
      <div
        className="flex flex-column py-3 px-4"
        style={{
          backgroundColor: "#FFFFFF",
          border: "1px solid #F0F4F7",
          borderRadius: "20px",
          maxWidth: "610px",
        }}
      >
        <h1
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "32px",
            color: "#585858",
          }}
        >
          Edit Job
        </h1>
        <Divider />
        <p
          className="m-0 p-0 mt-3"
          style={{
            fontWeight: "500",
            fontSize: "16px",
            color: "#585858",
          }}
        >
          When you edit the job details it will be reposted, and you will lose your shortlist. Candidates who have already applied will be asked to
          re-apply.
        </p>
        <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
          <button
            className="px-4 py-1 cursor-pointer"
            style={{
              backgroundColor: "#FFFFFF",
              border: "none",
              boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
              fontWeight: "500",
              fontSize: "18px",
              color: "#585858",
              textDecoration: "underline",
              borderRadius: "5px",
            }}
            onClick={(e) => {
              e.preventDefault();
              popupRef.current.hide();
            }}
          >
            No
          </button>
          <button
            className="px-7 py-1 cursor-pointer"
            style={{
              backgroundColor: "#FFA500",
              border: "none",
              boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              fontWeight: "700",
              fontSize: "18px",
              color: "#FFFFFF",
              textDecoration: "underline",
              borderRadius: "5px",
            }}
            onClick={(e) => {
              e.preventDefault();
              popupRef.current.hide();
              onEdit();
            }}
          >
            Yes
          </button>
        </div>
      </div>
    </JobFooterPopup>
  ) : (
    <JobFooterPopup ref={popupRef}>
      <div
        className="flex flex-column py-3 px-4"
        style={{
          backgroundColor: "#FFFFFF",
          borderTop: "2px solid #179D52",
          borderLeft: "2px solid #179D52",
          borderRight: "2px solid #179D52",
          borderTopLeftRadius: "30px",
          borderTopRightRadius: "30px",
          width: "100%",
          position: "fixed",
          bottom: "0px",
          left: "0px",
        }}
      >
        <h1
          className="mb-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "24px",
            color: "#585858",
          }}
        >
          Edit Job
        </h1>
        <Divider className="mt-2" />
        <p
          className="m-0 p-0 mt-3"
          style={{
            fontWeight: "500",
            fontSize: "14px",
            color: "#585858",
          }}
        >
          When you edit the job details it will be reposted, and you will lose your shortlist. Candidates who have already applied will be asked to
          re-apply.
        </p>
        <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
          <button
            className="px-4 py-2 cursor-pointer"
            style={{
              backgroundColor: "#FFFFFF",
              border: "none",
              boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
              fontWeight: "500",
              fontSize: "14px",
              color: "#585858",
              textDecoration: "underline",
              borderRadius: "30px",
            }}
            onClick={(e) => {
              e.preventDefault();
              popupRef.current.hide();
            }}
          >
            No
          </button>
          <button
            className="px-7 py-2 cursor-pointer"
            style={{
              backgroundColor: "#FFA500",
              border: "none",
              boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              fontWeight: "700",
              fontSize: "14px",
              color: "#FFFFFF",
              textDecoration: "underline",
              borderRadius: "30px",
            }}
            onClick={(e) => {
              e.preventDefault();
              popupRef.current.hide();
              onEdit();
            }}
          >
            Yes
          </button>
        </div>
      </div>
    </JobFooterPopup>
  );
});

type Props = {
  managedBy: number;
  expiryDate: number;
  onInviteMoreCandidatesClicked?: () => void;
  onEditClicked?: () => void;
  onCancelClicked?: () => void;
};

const JobFooter = ({ expiryDate, managedBy, onInviteMoreCandidatesClicked, onEditClicked, onCancelClicked }: Props) => {
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get("activeTab");
  const editRef = useRef<PopupFunctions>();
  const { isMobile } = useIsMobile();

  if (activeTab === "1")
    return (
      <div
        className="relative h-10rem mt-5 overflow-hidden"
        style={{
          width: "110%",
          left: "-10%",
          backgroundColor: "#179D52",
        }}
      >
        <div
          className="absolute w-full h-full"
          style={{
            top: "-50%",
            left: "50px",
            transform: "rotateZ(-4deg)",
            backgroundColor: "#FFFFFF",
            borderRadius: "0 0 20% 30%",
          }}
        />
      </div>
    );
  return !isMobile ? (
    <>
      <JobFooterEditPopup
        ref={editRef}
        onEdit={() => {
          if (onEditClicked) {
            onEditClicked();
          }
        }}
      />

      <div
        className="w-full h-min flex py-5 mt-5"
        style={{
          backgroundColor: "#179D52",
        }}
      >
        <div className="flex-grow-1 flex flex-column gap-3 px-5">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "30px",
              color: "#FFFFFF",
            }}
          >
            Need more candidates?
          </h1>
          <div
            className="flex gap-2 w-min cursor-pointer"
            style={{
              backgroundColor: "#FFA500",
              padding: "10px 80px",
              borderRadius: "10px",
              textWrap: "nowrap",
              boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
            }}
            onClick={(e) => {
              e.preventDefault();
              if (onInviteMoreCandidatesClicked) {
                onInviteMoreCandidatesClicked();
              }
            }}
          >
            <img src={UserPlus} alt="user plus" width="19.2px" height="19.2px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#FFFFFF",
              }}
            >
              {managedBy === 1 ? "Invite More candidates" : "Edit Criteria"}
            </p>
          </div>
          <div
            className="w-min flex gap-2 align-items-center"
            style={{
              textWrap: "nowrap",
            }}
          >
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "30px",
                color: "#FFFFFF",
              }}
            >
              Job expiry
            </p>
            <div
              style={{
                border: "1px solid #FFFFFF",
                borderRadius: "30px",
                padding: "10px",
              }}
            >
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "15px",
                  color: "#FFFFFF",
                }}
              >
                {expiryDate} days
              </p>
            </div>
          </div>
        </div>
        <div className="flex-grow-1 flex flex-column gap-3 pl-5 pr-7">
          {[
            {
              title: "Edit Job",
              subTitle: "Change the date, pay rate, description",
              icon: <img src={EditIcon} alt="edit" width="18px" height="18px" />,
              onClick: () => {
                editRef.current.show();
              },
            },
            {
              title: "Cancel Job",
              subTitle: "Cancel the job immediately  ",
              icon: <img src={CloseIcon} alt="close" width="18px" height="18px" />,
              onClick: onCancelClicked,
            },
          ].map((e, i) => (
            <div
              key={i}
              className="flex flex-column gap-2 cursor-pointer"
              onClick={(event) => {
                event.preventDefault();
                if (e.onClick) {
                  e.onClick();
                }
              }}
            >
              <div className="flex justify-content-between align-items-center">
                <div className="flex gap-3 align-items-center">
                  {e.icon}
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "600",
                      fontSize: "18px",
                      color: "#FFFFFF",
                    }}
                  >
                    {e.title}
                  </p>
                </div>
                <img src={SideArrow} alt="side arrow" width="7.85px" height="16px" />
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#FFFFFF",
                }}
              >
                {e.subTitle}
              </p>
              <div
                className="w-full"
                style={{
                  height: "1px",
                  backgroundColor: "#FFFFFF",
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </>
  ) : (
    <>
      <JobFooterEditPopup
        ref={editRef}
        onEdit={() => {
          if (onEditClicked) {
            onEditClicked();
          }
        }}
      />

      <div
        className="w-full h-max flex"
        style={{
          position: "fixed",
          bottom: "0",
          left: "0",
          width: "100%",
          display: "flex",
          padding: "10px 0",
          paddingBottom: "0px",
          justifyContent: "space-between",
          alignItems: "center",
          boxSizing: "border-box",
        }}
      >
        {/* Cancel Job Option */}
        <div
          className="flex flex-column gap-2 cursor-pointer"
          onClick={(event) => {
            event.preventDefault();
            onCancelClicked();
          }}
          style={{
            backgroundColor: "#FF6359",
            padding: "10px 0",
            height: "calc(var(--safe-area-bottom) + 51px)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            flex: 1,
            paddingBottom: "var(--safe-area-bottom)",
          }}
        >
          <div className="flex justify-content-between align-items-center">
            <div className="flex align-items-center flex-column">
              <img src={CloseIcon} alt="close" width="18px" height="18px" />
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "600",
                  fontSize: "12px",
                  color: "#FFFFFF",
                  textWrap: "nowrap",
                }}
              >
                Cancel Job
              </p>
            </div>
          </div>
        </div>

        {/* Edit Job Option */}
        <div
          className="cursor-pointer"
          style={{
            backgroundColor: "#BBBBBB",
            padding: "10px 0",
            height: "calc(var(--safe-area-bottom) + 51px)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            flex: 1,
            paddingBottom: "var(--safe-area-bottom)",
          }}
          onClick={(event) => {
            event.preventDefault();
            editRef.current.show();
          }}
        >
          <div className="flex justify-content-between align-items-center">
            <div className="flex align-items-center flex-column">
              <img src={EditIconMobile} alt="edit" width="14px" height="14px" />
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "600",
                  fontSize: "12px",
                  color: "#FFFFFF",
                  textWrap: "nowrap",
                }}
              >
                Edit Job
              </p>
            </div>
          </div>
        </div>

        {/* Invite More Option */}
        <div
          className="flex gap-2 cursor-pointer"
          style={{
            backgroundColor: "#179D52",
            padding: "10px 0",
            height: "calc(var(--safe-area-bottom) + 51px)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            flex: 1,
            paddingBottom: "var(--safe-area-bottom)",
          }}
          onClick={(e) => {
            e.preventDefault();
            if (onInviteMoreCandidatesClicked) {
              onInviteMoreCandidatesClicked();
            }
          }}
        >
          <div className="flex flex-column justify-content-center align-items-center">
            <img src={UserPlusMobile} alt="user plus" width="19.2px" height="19.2px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "600",
                fontSize: "12px",
                color: "#FFFFFF",
                textWrap: "nowrap",
              }}
            >
              Invite More
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobFooter;
