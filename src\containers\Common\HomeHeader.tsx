import React, { useEffect, useRef, useState } from "react";
import { Avatar } from "primereact/avatar";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "../../store";
import { removeSession } from "../../store/slices/sessionInfoSlice";
import {
  updateAccountAndSettingsActiveTab,
  updateChatWindowState,
  updateShowAccountAndSettings,
  updateShowProfileActivation,
} from "../../store/slices/applicationSlice";
import CustomDialog from "./CustomDialog";

import style from "./styles/home-header.module.css";
import menu from "../../assets/images/Icons/menu.png";
import AccountLayout from "../Parent/AccountSetting/AccountLayout";
import aboutMeIcon from "../../assets/images/Icons/about_me.png";
import familyIcon from "../../assets/images/Icons/my_family.png";
import childrenIcon from "../../assets/images/Icons/my_child.png";
import addressIcon from "../../assets/images/Icons/my_addresses.png";
import paymentsIcon from "../../assets/images/Icons/payments.png";
import familyMembershipIcon from "../../assets/images/Icons/family_membership.png";
import settingsIcon from "../../assets/images/Icons/settings.png";
import logoutIcon from "../../assets/images/Icons/logout.png";
import chatIcon from "../../assets/images/Icons/chat.png";
import giftIcon from "../../assets/images/Icons/refer.png";
import defaultGiftIcon from "../../assets/images/Icons/referGrey.png";
import { updateActiveTabIndex } from "../../store/slices/accountSettingSlice";
import useIsMobile from "../../hooks/useIsMobile";
import utils from "../../components/utils/util";

function HomeHeader() {
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const [showMenu, setShowMenu] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { enableAccountAndSettings } = useSelector((state: RootState) => state.applicationState);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { isMobile } = useIsMobile();
  const options = [
    {
      option: "My Account",
      options: [
        {
          icon: <img src={aboutMeIcon} alt="About Me" width="14.4px" height="14.47px" />,
          optionName: "About Me",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(0));
            dispatch(updateAccountAndSettingsActiveTab(0));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
        {
          icon: <img src={familyIcon} alt="My Family" width="14.4px" height="12.92px" />,
          optionName: "My Family",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(1));
            dispatch(updateAccountAndSettingsActiveTab(1));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
        {
          icon: <img src={childrenIcon} alt="My Children" width="16.5px" height="15.82px" />,
          optionName: "My Children",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(2));
            dispatch(updateAccountAndSettingsActiveTab(2));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
        {
          icon: <img src={addressIcon} alt="My Addresses" width="14.32px" height="14.32px" />,
          optionName: "My Addresses",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(3));
            dispatch(updateAccountAndSettingsActiveTab(3));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
      ],
    },
    {
      option: "Billing",
      options: [
        {
          icon: <img src={paymentsIcon} alt="Payments" width="13.5px" height="15.07px" />,
          optionName: "Payments",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(5));
            dispatch(updateAccountAndSettingsActiveTab(5));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
        {
          icon: <img src={familyMembershipIcon} alt="Family Membership" width="9.33px" height="16.07px" />,
          optionName: "Family Membership",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(6));
            dispatch(updateAccountAndSettingsActiveTab(6));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
      ],
    },
    {
      option: "Settings",
      options: [
        {
          icon: <img src={settingsIcon} alt="General Settings" width="16px" height="16.08px" />,
          optionName: "General Settings",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(7));
            dispatch(updateAccountAndSettingsActiveTab(7));
            dispatch(updateShowAccountAndSettings(true));
          },
        },
        {
          icon: <img src={logoutIcon} alt="Log out" width="16px" height="16.08px" />,
          optionName: "Log out",
          func: () => {
            setShowMenu(false);
            try {
              dispatch(removeSession());
              dispatch(updateShowProfileActivation(false));
              utils.obliterateEverything();
            } catch (_e) {
            } finally {
              navigate("/");
            }
          },
        },
      ],
    },
    {
      option: "Support",
      options: [
        {
          icon: <img src={chatIcon} alt="Log out" width="16px" height="16.08px" />,
          optionName: "Contact Customer Service",
          func: () => {
            setShowMenu(false);
            dispatch(updateChatWindowState(true));
          },
        },
      ],
    },
  ];

  function checkValue<T>(val: any, defaultValue: T): T {
    if (val !== null && val !== undefined) {
      return val;
    }
    return defaultValue;
  }

  const firstName = checkValue(sessionInfo?.data?.["firstName"], "");
  const lastName = checkValue(sessionInfo?.data?.["lastName"], "");

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div
      style={{ paddingTop: isMobile && "12px", top: isMobile && "0", width: isMobile && "max-content", right: isMobile && "0" }}
      className={`${
        !isMobile
          ? `fixed z-3 pt-2 md:pt-4 pr-2 md:pr-6 w-full flex justify-content-end pointer-events-none`
          : `fixed z-3  pr-3  w-full flex justify-content-end pointer-events-none`
      }`}
    >
      {!isMobile && (
        <div
          ref={buttonRef}
          className={`${
            !isMobile
              ? "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl shadow-3 gap-1 md:gap-2 align-items-center w-min pointer-events-auto hover:shadow-4"
              : "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl  gap-1 md:gap-2 align-items-center w-min pointer-events-auto"
          }`}
          onClick={() => setShowMenu(!showMenu)}
        >
          {!isMobile && (
            <img
              // className="cursor-pointer"
              src={menu}
              alt="hamburger"
              height={"15px"}
              width={"20.69"}
            />
          )}
          <div
            className="border-circle p-1 md:p-2 flex justify-content-center align-items-center pointer-events-none select-none"
            style={{
              height: "24px",
              width: "24px",
              backgroundColor: "#9F9F9F",
              fontSize: "10px",
              fontWeight: "600",
              color: "white",
              overflow: "hidden",
            }}
          >
            {sessionInfo.data && sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
              <img
                src={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
                alt="Profile"
                className="profilePhoto"
                style={{
                  height: "30.71px",
                  width: "30.65px",
                  borderRadius: "50%",
                  objectFit: "contain",
                }}
              />
            ) : (
              `${firstName[0]}.${lastName[0]}`
            )}
          </div>
        </div>
      )}
      {showMenu && (
        <div
          ref={menuRef}
          className="fixed bg-white shadow-3 p-2 pt-3 pb-4 overflow-y-auto overflow-x-hidden w-64 md:w-80 border-round-2xl pointer-events-auto"
          style={{ top: "70px", maxHeight: "85vh" }}
        >
          <div className="flex flex-column justify-content-center align-items-center">
            <div className="w-full flex justify-content-center align-content-center gap-2 md:gap-3 mb-2 mt-2">
              <Avatar
                className={`${style.imageStyle}`}
                label={`${firstName.charAt(0)}.${lastName.charAt(0)}`}
                image={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
                shape="circle"
                size="large"
                style={{
                  fontSize: "16px",
                  fontWeight: "600",
                  backgroundColor: "#9F9F9F",
                  color: "#FFFFFF",
                }}
              />
              <h3 className={`text-base md:text-lg font-semibold m-0 mt-2 ${style.mainOptions}`}>{`${firstName} ${lastName}`}</h3>
            </div>
            {options.map((option, index) => (
              <div key={index} className="w-full flex flex-column mb-2 pl-3 md:pl-5">
                <h3 className={`text-base md:text-lg font-semibold m-0 mt-2 ${style.mainOptions}`}>{option.option}</h3>
                <div
                  className="pl-2 md:pl-3 mt-1 md:mt-2"
                  style={{
                    borderLeft: "2px solid #F1F1F1",
                  }}
                >
                  {option.options.map((value, optionIndex) => (
                    <div key={optionIndex} className="flex justify-content-start align-items-center gap-2 md:gap-3">
                      <div style={{ width: "15px" }}>{value.icon}</div>
                      <p
                        className={`m-0 w-min cursor-pointer text-sm md:text-base ${style.noTextWrap}`}
                        style={{
                          paddingBlock: "4px",
                          color: "#585858",
                          fontWeight: "500",
                        }}
                        onClick={value.func}
                      >
                        {value.optionName}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            <button
              className={`border-round-xl px-2 md:px-3 py-1 md:py-2 flex justify-content-center align-items-center gap-1 md:gap-2 cursor-pointer text-xs md:text-sm ${style.referbtn}`}
              style={{
                backgroundColor: "transparent",
                color: "#585858",
                fontWeight: "500",
                position: "relative",
              }}
              onClick={() => {
                setShowMenu(false);
                dispatch(updateActiveTabIndex(26));
                dispatch(updateAccountAndSettingsActiveTab(26));
                dispatch(updateShowAccountAndSettings(true));
              }}
            >
              <img src={defaultGiftIcon} alt="Gift Icon" className={`${style.defaultIcon}`} width="14.4px" height="14.48px" />
              <img src={giftIcon} alt="Hover Gift Icon" className={`${style.hoverIcon}`} width="14.4px" height="14.48px" /> Get $10 for each friend
              you refer
            </button>
          </div>
        </div>
      )}

      <CustomDialog
        visible={enableAccountAndSettings}
        onHide={() => {
          dispatch(updateShowAccountAndSettings(false));
        }}
        closeClicked={() => {
          dispatch(updateShowAccountAndSettings(false));
        }}
        profileCompletion={0}
      >
        <AccountLayout visible={enableAccountAndSettings} />
      </CustomDialog>
    </div>
  );
}

export default HomeHeader;
