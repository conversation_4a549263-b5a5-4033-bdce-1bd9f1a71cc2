import  { useEffect, useState } from 'react'
import SidePannel from './SidePannel';
import { useSearchParams } from 'react-router-dom';
import environment from '../../../helper/environment';
import HomeHeaderHelper from './HomeHeaderHelper';
import useLoader from '../../../hooks/LoaderHook';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import c from '../../../helper/juggleStreetConstants';
import useIsMobile from '../../../hooks/useIsMobile';

const HelperPricing = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { disableLoader, enableLoader } = useLoader();
    const [frameSrc, setFrameSrc] = useState<string>('');
    const [searchParams] = useSearchParams();
    const{isMobile}=useIsMobile();
    useEffect(() => {
        enableLoader();
        const link = `https://${environment.getMarketingRoot(window.location.hostname)}/helpers/pricing?hideLayout=1&loggedIn=1`
        setFrameSrc(link);
    }, []);
    const handleIframeLoad = () => {
        
        disableLoader();
      };
      const activeIndex = sessionInfo?.data?.['accountStatus'] === c.accountStatus?.APPROVED ? 5 : 1;
    return (
        <div style={{ width: '100%', height: '100vh' }}>
            <SidePannel activeindex={activeIndex} />
            <HomeHeaderHelper />
            <iframe
                src={frameSrc}
                frameBorder="0"
                style={{ width:!isMobile ? 'calc(100% - 250px)' : "100%", height: '100%', position: 'absolute', right: 0 }}
                title="Page Helper Pricing"
                onLoad={handleIframeLoad}
            />
        </div>
    );
}

export default HelperPricing