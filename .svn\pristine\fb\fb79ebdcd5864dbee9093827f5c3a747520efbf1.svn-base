import { Dialog } from "primereact/dialog";
import { RiCloseLargeFill } from "react-icons/ri";
import { UserSearchResponse } from "../../hooks/SearchGeoSearchHook";
import styles from "./styles/all-filters.module.css";
import { useEffect, useRef, useState } from "react";
import helperImage1 from "../../assets/images/helper_image_1.png";
import helperImage2 from "../../assets/images/helper_image_2.png";
import helperImage3 from "../../assets/images/helper_image_3.png";
import { Divider } from "primereact/divider";
import environment from "../../helper/environment";
import Service from "../../services/services";
import utils from "../../components/utils/util";
import { BiLocationPlus } from "react-icons/bi";
import { Filter, SearchFilters, CandidateSearchFilters } from "../../store/types";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaChevronRight } from "react-icons/fa6";
import { FaTimes } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import useIsMobile from "../../hooks/useIsMobile";
import c from "../../helper/juggleStreetConstants";
import ProviderProfile from "../Parent/ProviderProfile/ProviderProfile";
import CustomDialog from "../../commonComponents/CustomDialog";

export const RadioButton2 = ({ selected }: { selected: boolean }) => {
  return (
    <div
      className="flex justify-content-center align-items-center"
      style={{
        minHeight: "16px",
        height: "16px",
        width: "16px",
        minWidth: "16px",
        borderRadius: "50%",
        border: `1px solid ${selected ? "#179D52" : "#DFDFDF"}`,
        backgroundColor: selected ? "#179D52" : "",
      }}
    >
      {selected && <FaCheck fontSize={"10px"} color="#ffffff" />}
    </div>
  );
};

interface Subject {
  optionId: number;
  text: string;
  children: Subject[] | null;
}

const RenderNodes = ({
  subject,
  selectedOption,
  onSelect,
  depth = 0,
  expandedNodes,
  setExpandedNodes,
}: {
  subject: Subject;
  selectedOption: number | null;
  onSelect: (optionId: number) => void;
  depth?: number;
  expandedNodes: Set<number>;
  setExpandedNodes: React.Dispatch<React.SetStateAction<Set<number>>>;
}) => {
  const hasChildren = subject.children !== null && subject.children.length > 0;
  const isExpanded = expandedNodes.has(subject.optionId);

  const handleClick = () => {
    if (hasChildren) {
      const newExpandedNodes = new Set(expandedNodes);
      if (isExpanded) {
        newExpandedNodes.delete(subject.optionId);
      } else {
        newExpandedNodes.add(subject.optionId);
      }
      setExpandedNodes(newExpandedNodes);
    } else {
      onSelect(subject.optionId);
    }
  };

  return (
    <div className="w-full">
      <div
        className={`
                    flex align-items-center p-2 px-3 cursor-pointer 
                    hover:surface-hover transition-colors
                    ${hasChildren ? "justify-content-between" : "justify-content-start"}
                    ${depth > 0 ? "pl-" + depth * 4 : ""}
                `}
        onClick={handleClick}
      >
        <div className="flex align-items-center gap-3">
          {hasChildren ? (
            <i
              className={`pi pi-chevron-right 
                                transition-transform 
                                ${isExpanded ? "rotate-90" : ""}
                            `}
            />
          ) : (
            <RadioButton2 selected={subject.optionId === selectedOption} />
          )}
          <span className="text-color-secondary font-medium">{subject.text}</span>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="pl-4">
          {subject.children?.map((child) => (
            <RenderNodes
              key={child.optionId}
              subject={child}
              onSelect={onSelect}
              depth={depth + 1}
              selectedOption={selectedOption}
              expandedNodes={expandedNodes}
              setExpandedNodes={setExpandedNodes}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const SpecificSubjectPopup = ({
  tutoringType,
  visible,
  onHide,
}: {
  tutoringType: "primarySchoolSubjects" | "highSchoolSubjects";
  visible: boolean;
  onHide: (selectedSubject: number) => void;
}) => {
  const [defaultSubjects, setDefaultSubjects] = useState<Subject[]>([]);
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());
  const { data, loading } = useSelector((state: RootState) => state.sessionInfo);

  const findNodePath = (subjects: Subject[], targetId: number): number[] => {
    for (const subject of subjects) {
      if (subject.optionId === targetId) {
        return [subject.optionId];
      }

      if (subject.children) {
        const childPath = findNodePath(subject.children, targetId);
        if (childPath.length > 0) {
          return [subject.optionId, ...childPath];
        }
      }
    }
    return [];
  };

  useEffect(() => {
    if (loading) return;

    const mapToRequiredSubjects = (subjects: any[]): Subject[] => {
      return subjects.map((subject) => ({
        optionId: subject["optionId"],
        text: subject["text"],
        children: subject["children"] ? mapToRequiredSubjects(subject["children"]) : null,
      }));
    };

    setDefaultSubjects(mapToRequiredSubjects(data["client"][tutoringType]));
  }, [data, tutoringType, loading]);

  useEffect(() => {
    if (!selectedSubjectId) return;

    const nodePath = findNodePath(defaultSubjects, selectedSubjectId);
    setExpandedNodes(new Set(nodePath));
  }, [selectedSubjectId, defaultSubjects, visible]);

  const handleSubjectSelect = (optionId: number) => {
    setSelectedSubjectId(optionId);
    onHide(optionId);
  };

  const renderHeader = () => (
    <div className="flex align-items-center justify-content-center">
      <h2 className="text-2xl font-bold text-color-secondary m-0">Select Specific Subject</h2>
    </div>
  );

  return (
    <Dialog
      visible={visible}
      onHide={() => onHide(-1)}
      header={renderHeader()}
      style={{ width: "90vw", maxWidth: "496px", height: "80vh" }}
      contentClassName="p-0 flex flex-column border-round-bottom-3xl"
      headerClassName="border-round-top-3xl"
    >
      <Divider />
      <div className="flex-grow-1 w-full select-none p-3 overflow-y-auto">
        {defaultSubjects.map((val) => (
          <RenderNodes
            key={val.optionId}
            subject={val}
            onSelect={handleSubjectSelect}
            selectedOption={selectedSubjectId}
            expandedNodes={expandedNodes}
            setExpandedNodes={setExpandedNodes}
          />
        ))}
      </div>
      <Divider />
      <div className="flex justify-content-end p-3">
        <button
          className="p-2 bg-red-500 text-white border-round-md flex items-center gap-2 hover:bg-red-600 transition-all border-none"
          onClick={(e) => {
            e.preventDefault();
            setSelectedSubjectId(-1);
            onHide(-1);
          }}
        >
          <FaTimes className="text-lg" />
          Cancel
        </button>
      </div>
    </Dialog>
  );
};

const CustomDropdown = ({ options, value, onChange, optionLabel, optionValue, className }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [position, setPosition] = useState("below");

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (option) => {
    onChange(option[optionValue]);
    setIsOpen(false);
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);

    if (isOpen) {
      const { bottom, top } = dropdownRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - bottom;
      const spaceAbove = top;

      setPosition(spaceBelow < 200 && spaceAbove > spaceBelow ? "above" : "below");
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  return (
    <div ref={dropdownRef} className={`${styles.customDropdown} ${className}`}>
      <div className={styles.selectedOption} onClick={toggleDropdown}>
        {value === "-2" ? "Select a Language" : options.filter((lang) => lang.payload === value)[0].text}
      </div>
      {isOpen && (
        <div className={`${styles.dropdownMenu} ${styles[position]}`}>
          {options.map((option) => (
            <div key={option[optionValue]} className={styles.dropdownOption} onClick={() => handleOptionClick(option)}>
              {option[optionLabel]}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface AllFiltersProps {
  enable: boolean;
  availableHelpers: UserSearchResponse;
  selectedJobCategory: "Childcare" | "Tutoring" | "Odd Jobs";
  disableDistance?: boolean;
  mode?: "Full" | "post-job";
  onClose: (
    payload: {
      filters: Filter[];
      pageIndex: number;
      pageSize: number;
      sortBy: "experience";
      jobType?: number;
    },
    actionType?: "submit" | "reset"
  ) => void;
  onResetRef?: (resetFn: () => void) => void;
}

function Filters<T>({
  values,
  type,
  name,
  expanded = false,
  groupName,
  getId,
  getLabel,
  checked,
  onChange,
}: {
  values: T[];
  type: "checkbox" | "radio";
  name: string;
  expanded?: boolean;
  groupName: string;
  getId: (value: T, index: number) => string;
  getLabel: (value: T, index: number) => string;
  checked: (value: T, index: number) => boolean;
  onChange: (value: T, index: number) => void;
}) {
  return values.map((value, index) => {
    const isSelected = checked(value, index);

    return (
      <div
        className={`flex align-items-center gap-1 ${expanded ? "w-6" : ""}`}
        key={index}
        style={{
          marginBottom: expanded ? "5px" : "0px",
          cursor: "pointer",
        }}
        onClick={type === "radio" ? () => onChange(value, index) : undefined} // Only apply for radio
      >
        {type === "radio" ? (
          <div
            style={{
              height: "18px",
              width: "18px",
              borderRadius: "50%",
              padding: "1px",
              border: `1px solid ${isSelected ? "#179D52" : "#DFDFDF"}`,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {isSelected && (
              <div
                style={{
                  height: "100%",
                  width: "100%",
                  borderRadius: "50%",
                  backgroundColor: "#179D52",
                }}
              />
            )}
          </div>
        ) : (
          <input
            className={`${styles.customCheckBox} cursor-pointer`}
            type="checkbox"
            name={`${groupName}-${name}`}
            id={getId(value, index)}
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation(); // Prevents container click event
              onChange(value, index);
            }}
          />
        )}
        <label
          className="cursor-pointer"
          style={{
            fontWeight: "500",
            fontSize: "14px",
            userSelect: "none",
            textWrap: "nowrap",
          }}
          htmlFor={getId(value, index)}
          onClick={(e) => {
            if (type === "checkbox") {
              e.stopPropagation(); // Stop propagation for checkboxes
            }
          }}
        >
          {getLabel(value, index)}
        </label>
      </div>
    );
  });
}

export const defaultFiltersOddJobs: SearchFilters = {
  filters: [
    { field: "neighbourhood", value: 0, operator: "eq" },
    { field: "ageGroups", value: [1, 2, 3, 4], operator: "eq" },
    { field: "jobTypes", value: [], operator: "eq" },
    { field: "experience", value: 0, operator: "eq" },
    { field: "ratings", value: [], operator: "eq" },
    { field: "activity", value: 0, operator: "eq" },
    { field: "distance", value: 1, operator: "eq" },
    { field: "driving", value: [], operator: "eq" },
    { field: "otherSkills", value: [], operator: "eq" },
    { field: "tutoringCategory", value: null, operator: "eq" },
    { field: "auPairCategory", value: null, operator: "eq" },
    { field: "nationality", value: null, operator: "eq" },
    { field: "jobDeliveryMethod", value: null, operator: "eq" },
    { field: "jobSubTypes", value: [1, 2, 4, 8, 16], operator: "eq" },
  ],
  pageIndex: 1,
  pageSize: 50,
  sortBy: "experience",
};
export const FiltersInHomeTutorsPrimarySchool: SearchFilters = {
  filters: [
    { field: "neighbourhood", value: 0, operator: "eq" },
    { field: "ageGroups", value: [1, 2, 3, 4], operator: "eq" },
    { field: "jobTypes", value: [64], operator: "eq" },
    { field: "experience", value: 0, operator: "eq" },
    { field: "ratings", value: [], operator: "eq" },
    { field: "activity", value: 0, operator: "eq" },
    { field: "distance", value: 1, operator: "eq" },
    { field: "driving", value: [], operator: "eq" },
    { field: "otherSkills", value: [], operator: "eq" },
    { field: "tutoringCategory", value: [1, 2, 3, 4], operator: "eq" },
    { field: "auPairCategory", value: null, operator: "eq" },
    { field: "nationality", value: null, operator: "eq" },
    { field: "jobDeliveryMethod", value: 1, operator: "eq" },
    { field: "jobSubTypes", value: [], operator: "eq" },
  ],
  pageIndex: 1,
  pageSize: 50,
  sortBy: "experience",
};
export const FiltersInHomeTutorsHighSchool: SearchFilters = {
  pageIndex: 1,
  pageSize: 50,
  sortBy: "experience",
  filters: [
    { field: "neighbourhood", value: 0, operator: "eq" },
    { field: "ageGroups", value: [1, 2, 3, 4], operator: "eq" },
    { field: "jobTypes", value: [128], operator: "eq" },
    { field: "experience", value: 0, operator: "eq" },
    { field: "ratings", value: [], operator: "eq" },
    { field: "activity", value: 0, operator: "eq" },
    { field: "distance", value: 1, operator: "eq" },
    { field: "driving", value: [], operator: "eq" },
    { field: "otherSkills", value: [], operator: "eq" },
    { field: "tutoringCategory", value: [1, 2, 3, 4], operator: "eq" },
    { field: "auPairCategory", value: null, operator: "eq" },
    { field: "nationality", value: null, operator: "eq" },
    { field: "jobDeliveryMethod", value: 1, operator: "eq" },
    { field: "jobSubTypes", value: [], operator: "eq" },
  ],
};
export const FiltersInOnlineTutorsPrimarySchool: SearchFilters = {
  pageIndex: 1,
  pageSize: 50,
  sortBy: "experience",
  filters: [
    { field: "neighbourhood", value: 0, operator: "eq" },
    { field: "ageGroups", value: [1, 2, 3, 4], operator: "eq" },
    { field: "jobTypes", value: [64], operator: "eq" },
    { field: "experience", value: 0, operator: "eq" },
    { field: "ratings", value: [], operator: "eq" },
    { field: "activity", value: 0, operator: "eq" },
    { field: "distance", value: 2, operator: "eq" },
    { field: "driving", value: [], operator: "eq" },
    { field: "otherSkills", value: [], operator: "eq" },
    { field: "tutoringCategory", value: [1, 2, 3, 4], operator: "eq" },
    { field: "auPairCategory", value: null, operator: "eq" },
    { field: "nationality", value: null, operator: "eq" },
    { field: "jobDeliveryMethod", value: 1, operator: "eq" },
    { field: "jobSubTypes", value: [], operator: "eq" },
  ],
};
export const FiltersInOnlineTutorsHighSchool: SearchFilters = {
  pageIndex: 1,
  pageSize: 50,
  sortBy: "experience",
  filters: [
    { field: "neighbourhood", value: 0, operator: "eq" },
    { field: "ageGroups", value: [1, 2, 3, 4], operator: "eq" },
    { field: "jobTypes", value: [128], operator: "eq" },
    { field: "experience", value: 0, operator: "eq" },
    { field: "ratings", value: [], operator: "eq" },
    { field: "activity", value: 0, operator: "eq" },
    { field: "distance", value: 2, operator: "eq" },
    { field: "driving", value: [], operator: "eq" },
    { field: "otherSkills", value: [], operator: "eq" },
    { field: "tutoringCategory", value: [1, 2, 3, 4], operator: "eq" },
    { field: "auPairCategory", value: null, operator: "eq" },
    { field: "nationality", value: null, operator: "eq" },
    { field: "jobDeliveryMethod", value: 1, operator: "eq" },
    { field: "jobSubTypes", value: [], operator: "eq" },
  ],
};

function AllFilters({ enable, availableHelpers, selectedJobCategory, mode = "Full", onClose, onResetRef, disableDistance = false }: AllFiltersProps) {
  const [searchResult, setSearchResult] = useState<UserSearchResponse>(null);
  const [dropdownEnabled, setDropdownEnabled] = useState(false);
  const [subjectPopup, setSubjectPopup] = useState(false);

  // * Filters
  const [tutoringType, setTutoringType] = useState<number>(disableDistance ? 1 : 0);
  const [distance, setDistance] = useState<number>(disableDistance || selectedJobCategory === "Tutoring" ? 2 : 1);
  const [experience, setExperience] = useState<boolean[]>(new Array(4).fill(true));
  const [teaches, setTeaches] = useState<boolean[]>(new Array(7).fill(true));
  const [subject, setSubject] = useState<number>(0);
  const [specificSubject, setSpecificSubject] = useState<number>(0);
  const [jobCompleted, setJobCompleted] = useState<number>(0);
  const [lastSeen, setLastSeen] = useState<number>(0);
  const [helpersAge, setHelpersAge] = useState<boolean[]>([true, true, true, true]);
  const [search, setSearch] = useState<number>(0);
  const [ratings, setRatings] = useState<number>(0);
  const [other, setOther] = useState<boolean[]>(new Array(7).fill(false));
  const [language, setLanguage] = useState<number>(-2);
  const [graduationYear, setGraduationYear] = useState<number>(c.year12FilterGroups.ALL);

  const [selectedJobType, setSelectedJobType] = useState({
    childcare: [
      { label: "Babysitting", value: true },
      { label: "Nanning", value: true },
      { label: "Before School", value: true },
      { label: "After School", value: true },
    ],
    tutoring: [
      { label: "Primary School", value: true },
      { label: "High School", value: false },
    ],
    oddJobs: [
      { label: "Laundry", value: true },
      { label: "Errands", value: true },
      { label: "Outdoor Chores", value: true },
      { label: "Elderly Help", value: true },
      { label: "Other Odd Jobs", value: true },
    ],
  });

  const { isMobile } = useIsMobile();

  const { defaultFilters, filters } = useSelector((state: RootState) => state.applicationState);
  const navigate = useNavigate();

  useEffect(() => {
    if (onResetRef && typeof onResetRef === "function") {
      onResetRef(resetAllFilters);
    }
  }, [onResetRef]);

  useEffect(() => {
    if (searchResult && searchResult.results.length > 0) {
      setDropdownEnabled(true);
      return;
    }
    setDropdownEnabled(false);
  }, [searchResult]);

  const debounceSearch = utils.debounce(async (searchText) => {
    if (searchText.trim().length < 2) {
      setSearchResult(null);
      setDropdownEnabled(false);
      return;
    }
    const payload = {
      pageindex: 1,
      pageSize: 10,
      sortBy: "experience",
      filters: [
        { field: "publicName", value: searchText, operator: "contains" },
        { field: "jobDeliveryMethod", value: 1, operatro: "eq" },
      ],
    };
    const { success, data } = await new Promise<{
      success: boolean;
      data: UserSearchResponse | null;
    }>((res, _) => {
      Service.getConnections(
        (data) => {
          res({ data: data, success: true });
        },
        () => {
          res({ data: null, success: false });
        },
        payload
      );
    });

    if (success && data && data.results.length > 0) {
      setSearchResult(data);
    } else {
      setSearchResult(null);
    }
  }, 300);
  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);
  const [selectedRequestId, setSelectedRequestId] = useState(null);

  const viewProvider = (helperId, requestId) => {
    if (isMobile) {
      const newParams = new URLSearchParams();
      newParams.set("id", String(helperId));
      newParams.set("requestId", String(requestId));

      setDropdownEnabled(false);
      navigate({
        pathname: "provider-profile",
        search: newParams.toString(),
      });
    } else {
      setSelectedProviderId(helperId);
      setSelectedRequestId(requestId);
      setShowPopup(true);
    }
  };

  const handleCloseProfilePopup = () => {
    setShowPopup(false);
    // setSelectedCandidate(null);
  };

  function getDisplayHelpers() {
    if (availableHelpers && availableHelpers.results.length > 2) {
      return availableHelpers.results.slice(0, 3).map((helper) => `${environment.getStorageURL(window.location.hostname)}/images/${helper.imageSrc}`);
    } else {
      return [helperImage1, helperImage2, helperImage3];
    }
  }

  function getJobTypes() {
    if (selectedJobCategory === "Childcare") {
      return selectedJobType.childcare;
    } else if (selectedJobCategory === "Tutoring") {
      return selectedJobType.tutoring;
    } else {
      return selectedJobType.oddJobs;
    }
  }

  function handleJobTypeChange(category: string, label: string) {
    setSelectedJobType((prevState) => {
      const newState = { ...prevState };

      if (category === "tutoring") {
        newState.tutoring = newState.tutoring.map((job) => ({
          ...job,
          value: job.label === label,
        }));
      } else {
        newState[category] = newState[category].map((job) => ({
          ...job,
          value: job.label === label ? !job.value : job.value,
        }));
      }

      return newState;
    });
  }

  function getLanguages() {
    return [
      { text: "Any", payload: "-1" },
      { text: "Spanish", payload: "58" },
      { text: "French", payload: "46" },
      { text: "German", payload: "47" },
      { text: "Italian", payload: "51" },
      { text: "Dutch", payload: "44" },
      { text: "Cantonese", payload: "42" },
      { text: "Indonesian", payload: "50" },
      { text: "Filipino", payload: "45" },
      { text: "Korean", payload: "52" },
      { text: "Croatian", payload: "43" },
      { text: "Hindi", payload: "49" },
      { text: "Arabic", payload: "41" },
      { text: "Greek", payload: "48" },
      { text: "Macedonian", payload: "53" },
      { text: "Punjabi", payload: "56" },
      { text: "Serbian", payload: "57" },
      { text: "Maltese", payload: "54" },
      { text: "Mandarin", payload: "55" },
      { text: "Japanese", payload: "5000" },
      { text: "Portuguese", payload: "5001" },
      { text: "Te Reo Māori", payload: "5002" },
    ];
  }

  function onApply(event: React.MouseEvent<HTMLButtonElement>) {
    event.preventDefault();
    const fieldHandlers: Record<string, () => any> = {
      neighbourhood: () => {
        if (search === 0) return 0;
        if (search === 1) return 1;
        if (search === 2) return 5;
      },
      experience: () => (jobCompleted === 0 ? null : jobCompleted),
      ageGroups: () => helpersAge.map((val, index) => (val ? index + 1 : null)).filter((index) => index !== null),
      age: () => helpersAge.map((val, index) => (val ? index + 1 : null)).filter((index) => index !== null),
      distance: () => {
        // Set distance to 2 if "Online Tutors" is selected
        if (selectedJobCategory === "Tutoring" && tutoringType === 1) {
          return 2;
        }
        return distance;
      },
      tutoringCategory: () => {
        if (selectedJobCategory === "Tutoring") return [1, 2, 3, 4];
        return null;
      },

      jobTypes: () => {
        var newVal: number[] = [];
        if (selectedJobCategory === "Childcare") {
          selectedJobType.childcare.forEach((val) => {
            if (val.value) {
              if (val.label === "Babysitting" && val.value) newVal.push(1);
              if (val.label === "Nanning" && val.value) newVal.push(2);
              if (val.label === "Before School" && val.value) newVal.push(4);
              if (val.label === "After School" && val.value) newVal.push(8);
            }
          });
        }
        if (selectedJobCategory === "Tutoring") {
          const previousJobType = filters.filters.find((f) => f.field === "jobTypes");
          newVal = (previousJobType?.value as number[]) || [];
        }
        return newVal;
      },
      ratings: () => {
        if (selectedJobCategory === "Tutoring") return [];
        const newVal: number[] = [];
        if (other[0]) newVal.push(1); // "All"
        if (other[2]) newVal.push(2); // "Over 4 stars"
        if (other[3]) newVal.push(3); // "Over 4 stars with reviews"
        return newVal; // Default to "All"
      },
      activity: () => {
        if (lastSeen === 0) return null;
        if (lastSeen === 1) return 4;
        if (lastSeen === 2) return 3;
        if (lastSeen === 3) return 1;
      },
      driving: () => {
        const newVal: number[] = [];
        if (other[1]) newVal.push(1);
        if (other[5]) newVal.push(2);
        return newVal;
      },
      otherSkills: () => {
        if (selectedJobCategory === "Tutoring") {
          const newVal: number[] = [];
          if (other[0]) newVal.push(2); // Profile Video
          if (other[4]) newVal.push(4); // Special Needs
          return newVal;
        }

        const newVal: number[] = [];
        if (other[4]) newVal.push(4); // Special Needs for other categories
        return newVal;
      },
      jobSubTypes: () => {
        const newVal: number[] = [];
        if (selectedJobCategory === "Odd Jobs") {
          selectedJobType.oddJobs.forEach((val) => {
            if (val.value) {
              if (val.label === "Laundry" && val.value) newVal.push(1);
              if (val.label === "Errands" && val.value) newVal.push(2);
              if (val.label === "Outdoor Chores" && val.value) newVal.push(4);
              if (val.label === "Elderly Help" && val.value) newVal.push(8);
              if (val.label === "Other Odd Jobs" && val.value) newVal.push(16);
            }
          });
        }
        return newVal;
      },
      jobDeliveryMethod: () => (selectedJobCategory === "Tutoring" ? tutoringType + 1 : null),
      schoolSubject: () => (specificSubject === 0 ? -1 : specificSubject),
      language: () => (language === -2 ? [] : Number(language)),
      year12GraduationYear: () => (graduationYear === 0 ? null : graduationYear),
    };
    const payload: SearchFilters = {
      ...defaultFilters,
      filters: defaultFilters.filters.map((filter) => {
        const fieldHandler = fieldHandlers[filter.field];

        if (fieldHandler) {
          return {
            field: filter.field,
            operator: "eq",
            value: fieldHandler(),
          };
        }
        return filter;
      }),
    };

    const requiredFields: string[] = Object.keys(fieldHandlers);
    requiredFields.forEach((field) => {
      const existingFilter = payload.filters.find((filter) => filter.field === field);
      if (!existingFilter) {
        payload.filters.push({
          field,
          operator: "eq",
          value: fieldHandlers[field](),
        });
      }
    });
    selectedJobCategory;
    const omitFilters: Record<"Childcare" | "Tutoring" | "Odd Jobs" | "Global", Array<string>> = {
      Global: [],
      Childcare: [],
      Tutoring: [],
      "Odd Jobs": [],
    };

    if (language === -2) {
      omitFilters.Global.push("language");
    }

    const finalPayload = {
      ...payload,
      filters: payload.filters
        .filter((val) => !omitFilters[selectedJobCategory].includes(val.field))
        .filter((val) => !omitFilters.Global.includes(val.field)),
    };

    onClose({ ...defaultFilters, ...finalPayload }, "submit");
    setDropdownEnabled(false);
  }
  function resetAllFilters() {
    // Retrieve the last selected jobDeliveryMethod and jobTypes
    const lastJobDeliveryMethod = filters.filters.find((f) => f.field === "jobDeliveryMethod")?.value;
    const lastJobType = filters.filters.find((f) => f.field === "jobTypes")?.value;
    // Determine the appropriate filters to apply based on the last selected values
    const getFilterByCategory = (): SearchFilters => {
      if (selectedJobCategory === "Odd Jobs") {
        return defaultFiltersOddJobs;
      }
      if (selectedJobCategory === "Tutoring") {
        if (lastJobDeliveryMethod === 1) {
          // In-Home Tutors
          if (lastJobType?.includes(64)) {
            return FiltersInHomeTutorsPrimarySchool;
          }
          if (lastJobType?.includes(128)) {
            return FiltersInHomeTutorsHighSchool;
          }
        }
        if (lastJobDeliveryMethod === 2) {
          // Online Tutors
          if (lastJobType?.includes(64)) {
            return FiltersInOnlineTutorsPrimarySchool;
          }
          if (lastJobType?.includes(128)) {
            return FiltersInOnlineTutorsHighSchool;
          }
        }
      }
      return defaultFilters;
    };
    // Reset the filters and apply the appropriate filter based on the last selected values
    onClose(getFilterByCategory(), "reset");
    setTutoringType(0);
    setDistance(disableDistance ? 2 : 1);
    setExperience(new Array(4).fill(true));
    setTeaches(new Array(7).fill(true));
    setSubject(0);
    setSpecificSubject(0);
    setJobCompleted(0);
    setLastSeen(0);
    setHelpersAge([true, true, true, true]);
    setSearch(0);
    setRatings(0);
    setOther(new Array(7).fill(false));
    setLanguage(-2);
    setGraduationYear(c.year12FilterGroups.ALL);

    // Reset job types
    setSelectedJobType({
      childcare: [
        { label: "Babysitting", value: true },
        { label: "Nanning", value: true },
        { label: "Before School", value: true },
        { label: "After School", value: true },
      ],
      tutoring: [
        { label: "Primary School", value: lastJobType?.includes(64) },
        { label: "High School", value: lastJobType?.includes(128) },
      ],
      oddJobs: [
        { label: "Laundry", value: true },
        { label: "Errands", value: true },
        { label: "Outdoor Chores", value: true },
        { label: "Elderly Help", value: true },
        { label: "Other Odd Jobs", value: true },
      ],
    });

    setDropdownEnabled(false);
    setSearchResult(null);
  }
  return (
    <>
      <CustomDialog
        visible={showPopup}
        style={{
          width: "100%",
          maxWidth: "100%",
          height: "100%",
          maxHeight: "100%",
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          overflowY: "auto",
        }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile candidateId={selectedProviderId} requestId={Number(selectedRequestId)} onClose={handleCloseProfilePopup} />
      </CustomDialog>
      <SpecificSubjectPopup
        visible={subjectPopup}
        onHide={(s) => {
          setSubjectPopup(false);
          setSpecificSubject(s);
          if (s === -1) {
            setSubject(0);
          }
        }}
        tutoringType={selectedJobType.tutoring[0].value ? "primarySchoolSubjects" : "highSchoolSubjects"}
      />
      <Dialog
        visible={enable}
        onHide={() => {
          setDropdownEnabled(false);
          onClose(null);
        }}
        content={
          <div className={`${styles.main} overflow-hidden flex flex-column`}>
            <div className={`w-full flex justify-content-between ${styles.padding}`}>
              <h1 className={`${styles.title} pointer-events-none`}>All Filters</h1>
              <button
                className={`${styles.closeButton} cursor-pointer`}
                onClick={() => {
                  setDropdownEnabled(false);
                  onClose(null);
                }}
              >
                <RiCloseLargeFill />
              </button>
            </div>
            <div
              style={{
                minHeight: "3px",
                width: "100%",
                backgroundColor: "#f1f1f1",
              }}
            />
            <div className={`flex-grow-1 ${styles.scrollView} ${styles.padding} flex flex-column`}>
              {mode === "Full" && (
                <>
                  <h1 className="m-0 p-0 pointer-events-none" style={{ fontSize: "18px", fontWeight: "700" }}>
                    Search by name
                  </h1>
                  <div className={`${styles.searchInput} relative overflow-visible`}>
                    <input
                      type="text"
                      className={`${styles.inputStyle}`}
                      placeholder="Min 2 characters"
                      onChange={(e) => {
                        debounceSearch(e.target.value);
                      }}
                    />
                    {searchResult && dropdownEnabled && searchResult.results.length > 0 && (
                      <div
                        className="absolute w-full overflow-y-auto"
                        style={{
                          left: "0.45px",
                          transform: "translateY(10px)",
                          zIndex: "5",
                          backgroundColor: "#ffffff",
                          borderRadius: "5px",
                          border: "1px solid #bbbbbb",
                          maxHeight: "15rem",
                          transition: "all .3s ease-in-out",
                        }}
                      >
                        {searchResult.results.map((val, index) => (
                          <div
                            key={index}
                            className="grid nested-grid grid-nogutter align-items-center select-none hover:bg-gray-100 cursor-pointer"
                            style={{
                              padding: "5px",
                            }}
                            onClick={(e) => {
                              e.preventDefault();
                              viewProvider(val.id, val.requestId);
                            }}
                          >
                            <div className="col-2 flex justify-content-center mb-2">
                              <img
                                style={{
                                  height: "50px",
                                  width: "50px",
                                  borderRadius: "50%",
                                }}
                                src={`${environment.getStorageURL(window.location.hostname)}/images/${val.imageSrc}`}
                                alt="helper Image"
                              />
                            </div>
                            <div className="col-10 mb-2">
                              <div className="grid grid-nogutter">
                                <div className="col-12 pl-1">
                                  <p className="m-0 p-0">{val.publicName}</p>
                                </div>
                                <div className="col-1 w-min flex justify-content-center align-items-center">
                                  <BiLocationPlus />
                                </div>
                                <div className="col-11 flex">
                                  <p className="m-0 p-0">{`${val.suburb} - Helper`}</p>
                                </div>
                              </div>
                            </div>
                            <Divider className="col-12" />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className={`flex justify-content-start align-items-center`} style={{ marginTop: "10px", padding: "5px 20px" }}>
                    <div className="relative flex">
                      {getDisplayHelpers().map((helper, index) => (
                        <div
                          key={index}
                          style={{
                            position: "relative",
                            left: `-${index * 10}px`,
                            zIndex: index,
                          }}
                        >
                          <img src={helper} alt="helper display" height="33px" width="33px" style={{ borderRadius: "50%" }} />
                        </div>
                      ))}
                    </div>
                    <p className={`p-0 m-0 pb-1 pointer-events-none`} style={{ fontWeight: "300", fontSize: "12px" }}>
                      Search Your favorite helpers by name
                    </p>
                  </div>
                  <Divider className="w-11 align-self-center mt-2" />
                </>
              )}
              {selectedJobCategory === "Tutoring" && mode === "Full" && (
                <>
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Tutoring Type
                  </h1>
                  <div
                    className={!isMobile ? `flex justify-content-start gap-2` : `flex justify-content-start gap-3 flex-column`}
                    style={{ padding: !isMobile && "0 25px", paddingBottom: "15px" }}
                  >
                    <Filters
                      values={["In-Home Tutors", "Online Tutors"]}
                      type="radio"
                      name="tutoringType"
                      groupName="tutoring-type-group"
                      getId={(value, index) => `tutoringType-${value.toString()}-${index}`}
                      expanded
                      getLabel={(value) => value}
                      checked={(_, index) => tutoringType === index}
                      onChange={(_, index) => setTutoringType(index)}
                    />
                  </div>
                  {selectedJobCategory === "Tutoring" && tutoringType !== 1 && (
                    <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  )}
                </>
              )}
              {/* {isOnlineTutoring && ( */}
              {(selectedJobCategory !== "Tutoring" || tutoringType !== 1) && (
                <>
                  {/* <Divider
                    className="align-self-center mt-2"
                    style={{ width: "100%" }}
                  /> */}
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Distance
                  </h1>
                  <div
                    className={!isMobile ? `flex justify-content-start gap-2` : `flex justify-content-start gap-3 flex-column`}
                    style={{ padding: !isMobile && "0 25px", paddingBottom: "15px" }}
                  >
                    <Filters
                      values={[2.5, 5, 10]}
                      type="radio"
                      name="distance"
                      groupName="distance-group"
                      getId={(value, index) => `distance-${value.toString()}-${index}`}
                      getLabel={(value) => `Within ${value}km`}
                      checked={(_, index) => distance === index}
                      onChange={(_, index) => setDistance(index)}
                    />
                  </div>
                </>
              )}
              {mode === "Full" && isMobile && (
                <>
                  <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Job Type
                  </h1>
                  <div className={`flex flex-wrap`} style={{ padding: "0px 25px" }}>
                    <Filters
                      values={getJobTypes()}
                      type={selectedJobCategory === "Tutoring" ? "radio" : "checkbox"}
                      name="jobType"
                      groupName="job-type-group"
                      expanded={true}
                      getId={(value, index) => `job-type-${value.label.trim()}-${index}`}
                      getLabel={(value) => value.label}
                      checked={(value) => value.value}
                      onChange={(value) => {
                        setSubject(0);
                        setSpecificSubject(-1);
                        handleJobTypeChange(selectedJobCategory.toLowerCase(), value.label);
                      }}
                    />
                  </div>
                </>
              )}
              {/* <Divider
                className="align-self-center mt-2"
                style={{ width: "100%" }}
              /> */}
              {selectedJobCategory !== "Tutoring" && isMobile && (
                <>
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Ratings
                  </h1>
                  <div className="flex flex-wrap" style={{ padding: !isMobile && "0px 25px" }}>
                    <Filters
                      values={["All", "Over 4 stars", "Over 4 stars with reviews"]}
                      type="radio"
                      name="ratings"
                      groupName="ratings-group"
                      expanded={true}
                      getId={(value, index) => `ratings-${value.trim()}-${index}`}
                      getLabel={(value) => value}
                      checked={(_, index) => ratings === index}
                      onChange={(_, index) => setRatings(index)}
                    />
                  </div>
                </>
              )}
              {/* )} */}
              {selectedJobCategory === "Tutoring" && (
                <>
                  {selectedJobCategory === "Tutoring" && tutoringType !== 1 && (
                    <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  )}
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Experience
                  </h1>
                  <div className={!isMobile ? "flex flex-wrap" : "flex flex-wrap flex-column gap-3"} style={{ padding: !isMobile && "0px 25px" }}>
                    <Filters
                      values={[
                        ["Newbie", 0],
                        ["Apprentice", 1],
                        ["Experienced", 2],
                        ["Professional", 3],
                      ]}
                      type="checkbox"
                      name="experience"
                      groupName="experience-group"
                      expanded={true}
                      getId={(value, index) => `experience-${(value[0] as string).trim()}-${index}`}
                      getLabel={(value) => value[0] as string}
                      checked={(value) => experience[value[1] as number]}
                      onChange={(value) =>
                        setExperience((prev) => {
                          let newValue = [...prev];
                          newValue[value[1] as number] = !newValue[value[1] as number];
                          return newValue;
                        })
                      }
                    />
                  </div>
                  <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Teaches
                  </h1>
                  <div className={!isMobile ? "flex flex-wrap" : "flex flex-wrap flex-column gap-3"} style={{ padding: !isMobile && "0px 25px" }}>
                    <Filters
                      values={[
                        ["Kindy", 0],
                        ["Year 1", 1],
                        ["Year 2", 2],
                        ["Year 3", 3],
                        ["Year 4", 4],
                        ["Year 5", 5],
                        ["Year 6", 6],
                      ]}
                      type="checkbox"
                      name="teaches"
                      groupName="Teaches-group"
                      expanded={true}
                      getId={(value, index) => `Teaches-${(value[0] as string).trim()}-${index}`}
                      getLabel={(value) => value[0] as string}
                      checked={(value) => teaches[value[1] as number]}
                      onChange={(value) =>
                        setTeaches((prev) => {
                          let newValue = [...prev];
                          newValue[value[1] as number] = !newValue[value[1] as number];
                          return newValue;
                        })
                      }
                    />
                  </div>
                </>
              )}

              {selectedJobCategory === "Tutoring" && mode === "Full" && (
                <>
                  <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Subject
                  </h1>
                  <div
                    className={!isMobile ? `flex justify-content-start gap-2` : `flex justify-content-start gap-3 flex-column`}
                    style={{ padding: !isMobile && "5px 25px" }}
                  >
                    <Filters
                      values={["All Subjects", "Specific Subject"]}
                      type="radio"
                      name="subject"
                      groupName="subject-group"
                      getId={(value, index) => `subject-${value.toString()}-${index}`}
                      getLabel={(value) => value}
                      checked={(_, index) => subject === index}
                      onChange={(_, index) => {
                        setSubject(index);
                        if (index === 1) {
                          setSubjectPopup(true);
                        }
                      }}
                      expanded
                    />
                  </div>
                </>
              )}
              <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
              <h1
                className="pointer-events-none"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  marginBlock: "10px",
                }}
              >
                Jobs Completed
              </h1>
              <div
                className={!isMobile ? `flex justify-content-start gap-3` : `flex justify-content-start gap-3 flex-column`}
                style={{ padding: !isMobile && "0px 25px" }}
              >
                <Filters
                  values={["All", "1 or more jobs", "5 or more jobs"]}
                  type="radio"
                  name="jobCompleted"
                  groupName="job-completed-group"
                  getId={(value, index) => `job-completed-${value.trim()}-${index}`}
                  getLabel={(value) => value}
                  checked={(_, index) => jobCompleted === index}
                  onChange={(_, index) => setJobCompleted(index)}
                />
              </div>
              <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
              <h1
                className="pointer-events-none"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  marginBlock: "10px",
                }}
              >
                Last Seen
              </h1>
              <div className={`flex flex-wrap`} style={{ padding: !isMobile && "0px 25px" }}>
                <Filters
                  values={["All", "1 Day", "7 Day", "30+ Days"]}
                  type="radio"
                  name="lastSeen"
                  groupName="last-seen-group"
                  expanded={true}
                  getId={(value, index) => `last-seen-${value.trim()}-${index}`}
                  getLabel={(value) => value}
                  checked={(_, index) => lastSeen === index}
                  onChange={(_, index) => setLastSeen(index)}
                />
              </div>
              <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
              <h1
                className="pointer-events-none"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  marginBlock: "10px",
                }}
              >
                Helpers Age
              </h1>
              <div className={!isMobile ? "flex flex-wrap" : "flex flex-wrap flex-column gap-3"} style={{ padding: !isMobile && "0px 25px" }}>
                <Filters
                  values={["16 - 17", "18 - 24", "25 - 44", "45+"]}
                  type="checkbox"
                  name="HelpersAge"
                  groupName="Helpers-age-group"
                  expanded={true}
                  getId={(value, index) => `Helpers-age-${value.trim()}-${index}`}
                  getLabel={(value) => value}
                  checked={(_, index) => helpersAge[index]}
                  onChange={(_, index) =>
                    setHelpersAge((prev) => {
                      const newHelpersAge = [...prev];
                      newHelpersAge[index] = !newHelpersAge[index];
                      return newHelpersAge;
                    })
                  }
                />
              </div>

              <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
              <h1
                className="pointer-events-none"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  marginBlock: "10px",
                }}
              >
                Search
              </h1>
              <div className="flex flex-wrap" style={{ padding: !isMobile && "0px 25px" }}>
                <Filters
                  values={["All", "My Favourite", "New Helpers"]}
                  type="radio"
                  name="search"
                  groupName="search-group"
                  expanded={true}
                  getId={(value, index) => `search-${value.trim()}-${index}`}
                  getLabel={(value) => value}
                  checked={(_, index) => search === index}
                  onChange={(_, index) => setSearch(index)}
                />
              </div>
              <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
              <h1
                className="pointer-events-none"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  marginBlock: "10px",
                }}
              >
                Other
              </h1>
              <div className={!isMobile ? "flex flex-wrap" : "flex flex-wrap flex-column gap-3"} style={{ padding: !isMobile && "0px 25px" }}>
                <Filters
                  values={
                    selectedJobCategory === "Childcare"
                      ? [
                        ["Profile Video", 0],
                        ["Driving License", 1],
                        ["4+ Stars", 2],
                        ["Reviews", 3],
                        ["Special Needs", 4],
                        ["Own Car", 5],
                      ]
                      : selectedJobCategory === "Odd Jobs"
                        ? [
                          ["Profile Video", 0],
                          ["Driving License", 1],
                          ["4+ Stars", 2],
                          ["Reviews", 3],
                          ["Own Car", 5],
                        ]
                        : [
                          ["Profile Video", 0],
                          ["Special Needs", 4],
                        ]
                  }
                  type="checkbox"
                  name="other"
                  groupName="other-group"
                  expanded={true}
                  getId={(value, index) => `other-${(value[0] as string).trim()}-${index}`}
                  getLabel={(value) => value[0] as string}
                  checked={(value) => other[value[1] as number]}
                  onChange={(value) =>
                    setOther((prev) => {
                      let newValue = [...prev];
                      newValue[value[1] as number] = !newValue[value[1] as number];
                      return newValue;
                    })
                  }
                />
              </div>
              {selectedJobCategory === "Childcare" && (
                <>
                  <Divider className="align-self-center mt-2" style={{ width: "100%" }} />
                  <h1
                    className="pointer-events-none"
                    style={{
                      fontSize: "18px",
                      fontWeight: "700",
                      marginBlock: "10px",
                    }}
                  >
                    Helper Language
                  </h1>
                  <CustomDropdown
                    className={`${styles.prDropdown} w-10 align-self-center`}
                    value={language.toString()}
                    optionLabel="text"
                    optionValue="payload"
                    options={getLanguages()}
                    onChange={(value) => {
                      setLanguage(value);
                    }}
                  />
                </>
              )}
            </div>
            <div
              style={{
                minHeight: "2px",
                width: "100%",
                backgroundColor: "#f1f1f1",
              }}
            />
            <div className={`${styles.padding} flex justify-content-end gap-3`}>
              <button
                className={`${styles.buttonStyle}`}
                data-clear
                onClick={(e) => {
                  e.preventDefault();
                  resetAllFilters(); // Reset all local state
                }}
              >
                Clear
              </button>
              <button className={`${styles.buttonStyle}`} data-apply onClick={onApply}>
                Apply
              </button>
            </div>
          </div>
        }
      />
    </>
  );
}

export default AllFilters;
