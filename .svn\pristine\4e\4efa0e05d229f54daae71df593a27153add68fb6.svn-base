import React, { useEffect } from 'react';
import { Crisp } from 'crisp-sdk-web';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store';
import { updateChatWindowState } from '../store/slices/applicationSlice';

const HelpdeskManager: React.FC = () => {
    const showChatBox = useSelector<RootState>(
        (state: RootState) => state.applicationState.showChatBox
    );
    const crispChatWindow = useSelector<RootState>(
        (state: RootState) => state.applicationState.crispChatWindow
    );
    const dispatch = useDispatch<AppDispatch>();

    useEffect(() => {
        // Initialize Crisp with your Crisp Website ID
        Crisp.configure('aecb7de2-efae-4561-b2eb-a0ce4201ba2f');

        Crisp.chat.onChatClosed(() => {
            dispatch(updateChatWindowState(false));
        });

        if (crispChatWindow) {
            Crisp.chat.open();
        }

        // Optionally show the chat box on page load
        if (showChatBox) {
            Crisp.chat.show();
        } else {
            Crisp.chat.hide();
        }
    }, [showChatBox, crispChatWindow]);

    return null; // This component does not render anything visible
};

export default HelpdeskManager;
