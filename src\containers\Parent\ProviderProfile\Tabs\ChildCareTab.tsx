import React, { CSSProperties, useState } from "react";
import styles from "../../styles/child-careTab.module.css";
import earth from "../../../../assets/images/Icons/earth.png";
import tickIcon from "../../../../assets/images/Icons/check-green.png";
import tickBlack from "../../../../assets/images/Icons/check-star.png";
import { ExtendedProfileTabProps } from "../types";
import { Divider } from "primereact/divider";
import degree from "../../../../assets/images/Icons/degree.png";
import star from "../../../../assets/images/Icons/star.png";
import rank from "../../../../assets/images/Icons/rank.png";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";
import { PiFirstAidKit } from "react-icons/pi";

interface ChecksProps {
  date1: string;
  date2: string;
}

interface Qualification {
  optionId: number;
  text: string;
  selected: boolean;
  canSelectCategory: boolean;
  childCategory: number;
  children: Qualification[];
}
const LimitedText = ({
  text,
  limit,
  disableLimit,
  style,
  className,
}: {
  text: string;
  limit: number;
  disableLimit: boolean;
  style?: CSSProperties;
  className?: string;
}) => {
  const displayText =
    disableLimit || text.length <= limit ? text : text.slice(0, limit);
  return (
    <span className={className} style={{ ...style }}>
      {displayText}
    </span>
  );
};

const Checks: React.FC<ChecksProps> = ({ date1, date2 }) => {
  const { isMobile } = useIsMobile();
  return (
    <>
    <div className="px-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Checks
      </h1>
      <div
        className={
          !isMobile ? `${"flex gap-2 mt-2 mx-2"}` : `${"flex gap-2 mt-2 "}`
        }
      >
        {!isMobile ? (
          <img src={tickBlack} alt="check" height="23px" width="23px" />
        ) : (
          <img src={tickBlack} alt="check" height="18px" width="18px" />
        )}
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "600",
            fontSize: !isMobile ? "16px" : "14px",
            color: "#585858",
          }}
        >
          Working With Children Check
        </p>
      </div>
      <p
        className="m-0 p-0 mt-2 mb-3"
        style={{
          fontWeight: "700",
          fontSize: "12px",
          color: "#179D52",
        }}
      >
        {`Verified on: ${date1} | Expires on: ${date2}`}
      </p>
     
    </div>
    <Divider />
    </>
  );
};

const FirstAid = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "20px",
          color: "#585858",
        }}
      >
        First Aid Accreditations
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2 align-items-center">
          <PiFirstAidKit />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const ChildcareExtras = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "20px",
          color: "#585858",
        }}
      >
        Childcare Extras
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2 align-items-center">
          <PiFirstAidKit />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const ChildcareQualification: React.FC<{ helper: any }> = ({ helper }) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        marginTop: "10px",
      }}
    >
      {helper.qualifications
        .filter((val) => val.selected === true)
        .map((qualification: Qualification) => (
          <div
            key={qualification.optionId}
            style={{
              fontWeight: "600",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
              display: "flex",
              gap: "10px",
            }}
          >
                     {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
            <span>{qualification.text}</span>
            {qualification.children && qualification.children.length > 0 && (
              <div style={{ marginLeft: "1rem" }}>
                {qualification.children.map((child: Qualification) => (
                  <div
                    key={child.optionId}
                    style={{
                      fontWeight: "600",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    {child.text}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
    </div>
  );
};
const TutoringQualification: React.FC<{ helper: any }> = ({ helper }) => {
  const {isMobile}=useIsMobile()
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        marginTop: "10px",
      }}
    >
      {helper.tutoringQualifications
        .filter((val) => val.selected === true)
        .map((tutoringQualifications: Qualification) => (
          <div
            key={tutoringQualifications.optionId}
            style={{
              fontWeight: "600",
              fontSize: "16px",
              color: "#585858",
              display: "flex",
              gap: "10px",
            }}
          >
             {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
            <span>{tutoringQualifications.text}</span>
            {tutoringQualifications.children &&
              tutoringQualifications.children.length > 0 && (
                <div style={{ marginLeft: "1rem" }}>
                  {tutoringQualifications.children.map(
                    (child: Qualification) => (
                      <div
                        key={child.optionId}
                        style={{
                          fontWeight: "600",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        {child.text}
                      </div>
                    )
                  )}
                </div>
              )}
          </div>
        ))}
    </div>
  );
};
const ReviewAndRatingHead = ({
  rating,
  ratingCount,
  isSuperHelper,
}: {
  rating: number;
  ratingCount: number;
  isSuperHelper: boolean;
}) => {
  const { isMobile } = useIsMobile();
  return (
    <div className={!isMobile ? `${"mb-4"}` : `${"mb-2"}`}>
      <div className="flex justify-content-between align-items-center">
        <div>
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: !isMobile ? "20px" : "16px",
              color: "#585858",
            }}
          >
            Reviews
          </h1>
          <div className="flex gap-1">
            <img src={star} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "300",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
            </p>
          </div>
        </div>
        {isSuperHelper && (
          <div className="flex gap-2 align-items-center">
            <img src={rank} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "18px",
                color: "#585858",
              }}
            >
              Super Helper
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
const ReviewAndRatingList = ({
  ratings,
}: {
  ratings: Array<{
    clientFirstName: string;
    clientLastName: string;
    feedback: string;
    ratingDate: string;
    ratingAvg: number;
    clientImageUrl: string;
  }>;
}) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      className={
        !isMobile
          ? `${"flex flex-column  pt-2 mt-2 mb-4"}`
          : `${"flex flex-column "}`
      }
    >
      {ratings.map((rating, index) => (
        <React.Fragment key={index}>
          <Divider />
          <div className="flex gap-2 my-2">
            <div
              style={{
                height: "38px",
                width: "38px",
                background: "gray",
                borderRadius: "50%",
                overflow: "hidden",
                 minWidth:"38px"
              }}
            >
              <img
                src={rating.clientImageUrl}
                alt="client Image"
                width="100%"
                height="100%"
              />
            </div>
            <div className="flex-grow-1 flex flex-column gap-2">
              <div className="flex">
                <div className="flex-grow-1 flex flex-column">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                  >
                    {new Date(rating.ratingDate).toLocaleDateString("en-GB")}
                  </p>
                </div>
                <div
                  style={{ display: "flex", flexDirection: "row", gap: "5px" }}
                >
                  <img
                    src={star}
                    alt="star"
                    width="19.82px"
                    height="18.62px"
                    style={{ marginTop: "2px" }}
                  />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "600",
                      fontSize: "18px",
                      color: "#585858",
                    }}
                  >
                    {`${rating.ratingAvg.toFixed(1)}`}
                  </p>
                </div>
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                {rating.feedback}
              </p>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

const ChildCareTab: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  const text = helper.providerMyExperience || "";
  const { isMobile } = useIsMobile();
  const nat = c.countriesIso.find(
    (country) =>
      country.alpha2.toLowerCase() === helper?.nationality.toLowerCase() ||
      country.value.toLowerCase() === helper?.nationality.toLowerCase()
  );
  return !isMobile ? (
    <div className={styles.childCareContainer}>
      <div className={styles.childCareBoxOne}>
        <h1 className={styles.childCare}>Childcare experience</h1>
        <div className={styles.childCareExperience}>
          {/* {helper.providerMyExperience} */}
          <div className="py-2 px-3">
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              <span
                className="m-0 p-0 inline-block text-left"
                style={{
                  fontWeight: "400",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                <LimitedText
                  className="m-0 p-0"
                  text={text}
                  limit={displayLimit}
                  disableLimit={textState}
                  style={{
                    fontWeight: "400",
                    fontSize: "16px",
                    color: "#585858",
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                    wordWrap: "break-word",
                    whiteSpace: "normal",
                  }}
                />
                {text.length >= displayLimit && (
                  <span
                    className="cursor-pointer hover:text-gray-300"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                    onClick={() => toggleTextState((prev) => !prev)}
                  >
                    {" "}
                    {textState ? "Show Less." : "Read More..."}
                  </span>
                )}
              </span>
            </p>
          </div>
        </div>
        <div className="flex mt-3">
          <div className="flex-grow-1">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Services
            </h1>
            <div className="flex py-2 px-4">
              <div className="h-min flex-grow-1 flex gap-2">
                {(helper.interestedInJobTypes & c.jobType.BABYSITTING) !==
                  0 && (
                  <div className={styles.row}>
                    <span>One-Off</span>
                    <img src={tickIcon} alt="tick" className={styles.tick} />
                  </div>
                )}

                {(helper.interestedInJobTypes & c.jobType.NANNYING) !== 0 && (
                  <div className={styles.row}>
                    <span>Recurring</span>
                    <img src={tickIcon} alt="tick" className={styles.tick} />
                  </div>
                )}
                {(helper.interestedInJobTypes &
                  c.jobType.BEFORE_SCHOOL_CARE) !==
                  0 && (
                  <div className={styles.row}>
                    <span>Before School Care</span>
                    <img src={tickIcon} alt="tick" className={styles.tick} />
                  </div>
                )}
                {(helper.interestedInJobTypes & c.jobType.AFTER_SCHOOL_CARE) !==
                  0 && (
                  <div className={styles.row}>
                    <span>After School Care</span>
                    <img src={tickIcon} alt="tick" className={styles.tick} />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-column align-items-center">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Nationality
            </h1>
            <div
              className="flex gap-2 justify-content-center align-items-center"
              style={{
                width: "145.28px",
                height: "42.28px",
                borderRadius: "20px",
                backgroundColor: "#F1F1F1",
              }}
            >
              <img src={earth} alt="earth" />
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "12px",
                  color: "#585858",
                }}
              >
                {" "}
                {nat?.label || "Unknown"}
              </p>
            </div>
          </div>
        </div>
      </div>
      {(helper?.firstAid.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <div className={styles.childCareBoxTwo}>
          <FirstAid
            data={helper.firstAid
              .filter((val) => val.selected === true)
              .map((val) => val.text)}
          />
        </div>
      )}
      <div className={styles.childCareBoxTwo}>
        {(helper?.certificates?.length ?? 0) > 0 && (
          <Checks
            date1={new Date(
              helper.certificates[0].verificationDate
            ).toLocaleDateString("en-GB")}
            date2={new Date(
              helper.certificates[0].expiryDate
            ).toLocaleDateString("en-GB")}
          />
        )}

        {helper.qualifications.filter((x) => x.selected === true).length >
          0 && (
          <div className={styles.qualificationContainer}>
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Childcare Qualification
            </h1>
            <div>
              <ChildcareQualification helper={helper} />
            </div>
          </div>
        )}
        {helper.tutoringQualifications.filter((val) => val.selected === true)
          .length > 0 && (
          <>
            <Divider />
            <div className={styles.qualificationContainerTutor}>
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Tutoring Qualification
              </h1>
              <div>
                <TutoringQualification helper={helper} />
              </div>
            </div>
          </>
        )}
      </div>

      {(helper?.extraSkills.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <div className={styles.childCareBoxTwo}>
          <ChildcareExtras
            data={helper.extraSkills
              .filter((val) => val.selected === true)
              .map((val) => val.text)}
          />
        </div>
      )}
         {helper?.hasVouches && (
      <div className={styles.childCareBoxThree}>
        <div className="px-4 pt-2 mt-2 mb-4">
          <div className="flex justify-content-between align-items-center">
            <div className="flex flex-column gap-1">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: !isMobile ? "20px" : "16px",
                  color: "#585858",
                }}
              >
                References
              </h1>
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: !isMobile ? "16px" : "16px",
                  color: "#585858",
                }}
              >
                Available on request. Please contact Customer Service to obtain
                referee details.
              </h1>
            </div>
          </div>
        </div>
      </div>
         )}
      {(helper?.ratingsExtended.length ?? 0) > 0 && (
        <div className={styles.childCareBoxThree}>
          <div className={styles.qualificationContainerTutorSec}>
            <div>
              <>
                <ReviewAndRatingHead
                  rating={helper?.providerRatingsAvg ?? 0}
                  ratingCount={helper?.providerRatingsCount ?? 0}
                  isSuperHelper={helper?.isSuperProvider ?? false}
                />
                {(helper?.ratingsExtended.length ?? 0) > 0 && (
                  <ReviewAndRatingList ratings={helper.ratingsExtended} />
                )}
              </>
            </div>
          </div>
        </div>
      )}
    </div>
  ) : (
    <div className={styles.childCareContainerMobile}>
      <div className={styles.childCareBoxOneMobile}>
        <h1 style={{ fontSize: "16px", color: "#585858" }}>
          Childcare experience
        </h1>
        <div className={styles.childCareExperience}>
          {/* {helper.providerMyExperience} */}
          <div className="">
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              <span
                className="m-0 p-0 inline-block text-left"
                style={{
                  fontWeight: "400",
                  fontSize: "12px",
                  color: "#585858",
                }}
              >
                <LimitedText
                  className="m-0 p-0"
                  text={text}
                  limit={displayLimit}
                  disableLimit={textState}
                  style={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#585858",
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                    wordWrap: "break-word",
                    whiteSpace: "normal",
                  }}
                />
                {text.length >= displayLimit && (
                  <span
                    className="cursor-pointer hover:text-gray-300"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                    onClick={() => toggleTextState((prev) => !prev)}
                  >
                    {" "}
                    {textState ? "Show Less." : "Read More..."}
                  </span>
                )}
              </span>
            </p>
          </div>
        </div>
        <div className="flex mt-3">
          <div className="flex-grow-1">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Services
            </h1>
            <div className="flex py-2">
              <div className="h-min flex-grow-1 flex gap-2 flex-column sm:flex-row">
                <div>
                  {(helper.interestedInJobTypes & c.jobType.BABYSITTING) !==
                    0 && (
                    <div className={styles.row}>
                      <span style={{ fontSize: "12px" }}>One-Off</span>
                      <img src={tickIcon} alt="tick" className={styles.tick} />
                    </div>
                  )}

                  {(helper.interestedInJobTypes & c.jobType.NANNYING) !== 0 && (
                    <div className={styles.row}>
                      <span style={{ fontSize: "12px" }}>Recurring</span>
                      <img src={tickIcon} alt="tick" className={styles.tick} />
                    </div>
                  )}
                </div>
                <div>
                  {(helper.interestedInJobTypes &
                    c.jobType.BEFORE_SCHOOL_CARE) !==
                    0 && (
                    <div className={styles.row}>
                      <span style={{ fontSize: "12px" }}>
                        Before School Care
                      </span>
                      <img src={tickIcon} alt="tick" className={styles.tick} />
                    </div>
                  )}
                  {(helper.interestedInJobTypes &
                    c.jobType.AFTER_SCHOOL_CARE) !==
                    0 && (
                    <div className={styles.row}>
                      <span style={{ fontSize: "12px" }}>
                        After School Care
                      </span>
                      <img src={tickIcon} alt="tick" className={styles.tick} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-column align-items-center">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Nationality
            </h1>
            <div
              className="flex gap-2 justify-content-center align-items-center"
              style={{
                paddingInline: "10px",
                paddingBlock: "10px",
                borderRadius: "20px",
                backgroundColor: "#F1F1F1",
              }}
            >
              <img src={earth} alt="earth" />
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "12px",
                  color: "#585858",
                }}
              >
                {" "}
                {nat?.label || "Unknown"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {(helper?.firstAid.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <div className={styles.childCareBoxTwo}>
          <FirstAid
            data={helper.firstAid
              .filter((val) => val.selected === true)
              .map((val) => val.text)}
          />
        </div>
      )}

      <div className={styles.childCareBoxTwoMobile}>
        {(helper?.certificates?.length ?? 0) > 0 && (
          <Checks
            date1={new Date(
              helper.certificates[0].verificationDate
            ).toLocaleDateString("en-GB")}
            date2={new Date(
              helper.certificates[0].expiryDate
            ).toLocaleDateString("en-GB")}
          />
        )}

        {helper.qualifications.filter((x) => x.selected === true).length >
          0 && (
          <div className={styles.qualificationContainerMobile}>
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Childcare Qualification
            </h1>
            <div>
              <ChildcareQualification helper={helper} />
            </div>
          </div>
        )}
        {helper.tutoringQualifications.filter((val) => val.selected === true)
          .length > 0 && (
          <>
            <Divider />
            <div className={styles.qualificationContainerTutorMobile}>
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Tutoring Qualification
              </h1>
              <div>
                <TutoringQualification helper={helper} />
              </div>
            </div>
          </>
        )}
      </div>

      {(helper?.extraSkills.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <div className={styles.childCareBoxTwo}>
          <ChildcareExtras
            data={helper.extraSkills
              .filter((val) => val.selected === true)
              .map((val) => val.text)}
          />
        </div>
      )}
        {helper?.hasVouches && (
      <div className={styles.childCareBoxThreeMobile}>
        <div className="px-4 pt-2 mt-2 mb-4">
          <div className="flex justify-content-between align-items-center">
            <div className="flex flex-column gap-1">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: !isMobile ? "20px" : "16px",
                  color: "#585858",
                }}
              >
                References
              </h1>
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: !isMobile ? "16px" : "16px",
                  color: "#585858",
                }}
              >
                Available on request. Please contact Customer Service to obtain
                referee details.
              </h1>
            </div>
          </div>
        </div>
      </div>
         )}
      {(helper?.ratingsExtended.length ?? 0) > 0 && (
        <div className={styles.childCareBoxThreeMobile}>
          <div className={styles.qualificationContainerTutorSec}>
            <div>
              <>
                <ReviewAndRatingHead
                  rating={helper?.providerRatingsAvg ?? 0}
                  ratingCount={helper?.providerRatingsCount ?? 0}
                  isSuperHelper={helper?.isSuperProvider ?? false}
                />
                {(helper?.ratingsExtended.length ?? 0) > 0 && (
                  <ReviewAndRatingList ratings={helper.ratingsExtended} />
                )}
              </>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChildCareTab;
