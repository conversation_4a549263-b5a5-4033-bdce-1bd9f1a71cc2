import React, { useState } from "react";
import styles from "../../styles/award-job-confirmation.module.css";
import juggleLogo from "../../.././../assets/images/juggle_white.png";
import rightArrowCircle from "../../../..//assets/images/Icons/arrow-circle.png";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ShiftFilledTick from "../../../../assets/images/shift-filled-tick.png";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import useIsMobile from "../../../../hooks/useIsMobile";
import backArrowCircle from "../../../..//assets/images/Icons/back-icon.png";
import { IframeBridge } from "../../../../services/IframeBridge";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { FaRegCircleQuestion } from "react-icons/fa6";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { Jobs } from "../types";
import c from "../../../../helper/juggleStreetConstants";
import { CancelJobPopup, useCancelJobPopup } from "./CancelJobPopup";
import remove from "../../../../assets/images/Icons/remove.png";
import ViewShifts from "../modals/ViewShifts";
const AwardJobConfirmation: React.FC = ({ }) => {
  const { name, imageUrl, jobId, } = useParams();

  const location = useLocation();
  const params = new URLSearchParams(location.search);

  const filledShifts = parseInt(params.get("filledShifts"), 10);
  const openShifts = parseInt(params.get("openShifts"), 10);
  const navigate = useNavigate();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const { isMobile } = useIsMobile();
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const { disableLoader, enableLoader } = useLoader();
  const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
  const [showViewShifts, setShowViewShifts] = useState(false); // Add state for dialog visibility
  const handleContinueFillShifts = () => {
    // IframeBridge.sendToParent({
    //   type: "navigateToPostJob",
    //   data: {
    //     id: jobId,
    //     action: "fillJob",
    //   },
    // });

    // if (!inIframe) {
    //   const newParam = new URLSearchParams();
    //   newParam.set("jobaction", "fillJob");

    //   if (clientType === 1) {
    //     navigate({
    //       pathname: `/parent-home/job/${jobId}/day-and-schedule`,
    //       search: newParam.toString(),
    //     });
    //   } else if (clientType === 2) {
    //     navigate({
    //       pathname: `/business-home/job/${jobId}/day-and-schedule`,
    //       search: newParam.toString(),
    //     });
    //   }
    // }
    navigate(-1)
  };

  const handleCloseJob = () => {
    showCancelJobPopup(
      "Are you sure?", // Heading
      "If you cancel the remaining shifts all applicants will be notified, and these shifts will be moved into the Job History section. Are you sure you want to continue?", // Message
      "Go Back", // Cancel Text
      "Cancel Remaining Shifts", // Confirm Text
      <img src={remove} alt="confirm" style={{ height: "20px", width: "20px" }} />, // Icon
      () => {
        // On Confirm
        const payload = {
          jobStatus: c.jobStatus.CANCELLED,
        };
        enableLoader();
        Service.jobClientAwardJobs(
          async (response: Jobs) => {
            disableLoader();
            handleManageJobsNavigation(navigate, clientType);
          },
          (error: any) => {
            disableLoader();
            console.error("Error closing job:", error);
          },
          parseInt(jobId),
          payload
        );
      },
      () => {
        // On Cancel (optional: handle dialog cancellation, e.g., log or do nothing)
      }
    );
  };
  const renderHeader = () =>
    !isMobile ? (
      <header
        className={`w-full flex align-items-center justify-content-between border-round-bottom-lg overflow-hidden ${styles.shiftHeaderGradient}`}
      >
        <img className={styles.shiftJuggleLogo} src={juggleLogo} alt="juggle logo" onClick={() => navigate("/parent-home")} />
        <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
      </header>
    ) : (
      <header
        className={`w-full flex align-items-center justify-content-center border-round-bottom-lg overflow-hidden ${styles.shiftHeaderGradientMobile}`}
      >
        <img className={styles.shiftJuggleLogoMobile} src={juggleLogo} alt="juggle logo" onClick={() => navigate("/parent-home")} />
        <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
      </header>
    );

  const handleNavigation = (navigate: (path: string) => void, clientType: number) => {
    IframeBridge.sendToParent({
      type: "goBackFromManageJob",
    });
    if (!inIframe) {
      const path = clientType === 2 ? "/business-home" : "/parent-home";
      navigate(path);
    }
  };
  const handleManageJobsNavigation = (navigate: (path: string) => void, clientType: number) => {
    IframeBridge.sendToParent({
      type: "goBackToManageJob",
      data: {
        activeTab: '0',

      },
    });

    if (!inIframe) {
      const path = clientType === 2 ? "/business-home/manage-jobs?jobId=-1&activeTab=0" : "/parent-home/manage-jobs?jobId=-1&activeTab=0";
      navigate(path);
    }
  };
  return !isMobile ? (
    <div className="min-h-screen w-full flex flex-column">
      <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />

      {renderHeader()}
      <main className={styles.shiftReferMainSection}>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            flexGrow: "1",
            alignItems: "center",
          }}
        >
          <button className={styles.shiftBackBtn} onClick={() => handleNavigation(navigate, clientType)}>
            <img src={rightArrowCircle} alt="rightArrowCircle" width="18px" height="18px" />
            Back to Helpers Near Me
          </button>
          <div className={styles.shiftContainers}>
            <div
              style={{
                maxWidth: "795px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {/* <div>
                <img src={ShiftFilledTick} alt="ShiftFilledTick" width="76px" height="81px" />
              </div> */}
              {/* <h2 className={styles.ShiftHeader}>Shift Filled</h2> */}
              <div className="flex flex-column gap-2">
                <div style={{ fontSize: "14px", fontWeight: "500", color: "#585858", textAlign: "center" }} className="flex align-items-center gap-1">
                  <img src={ShiftFilledTick} alt="ShiftFilledTick" width="36px" height="41px" />
                  <span style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>{name}</span> has been awarded {filledShifts} Shifts <br />
                </div>
                {openShifts > 0 && (
                  <div style={{ fontSize: "14px", fontWeight: "500", color: "#585858", textAlign: "center" }} className="flex align-items-center gap-2 pl-1">
                    <FaRegCircleQuestion fontSize={"30px"} />
                    <span> You still have <span style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>{openShifts} shifts remaining</span></span>
                  </div>
                )}
              </div>
            </div>
            <div>
              <img src={imageUrl} alt="HelperImg" width="207px" height="216px" style={{ borderRadius: "10px", marginTop: "20px" }} />
            </div>
            {/* <button className={styles.returnBtn} onClick={() => handleManageJobsNavigation(navigate, clientType)}>
              Return to Mange Jobs
            </button> */}
            {openShifts > 0 && (
              <button style={{ paddingInline: "61px" }} className={styles.viewShiftMobile} onClick={() => setShowViewShifts(true)}>
                View Shifts
              </button>
            )}
            {openShifts > 0 && (
              <button className={styles.continueFillMobile} onClick={handleContinueFillShifts}>
                Fill Remaining Shifts
              </button>
            )}

            {openShifts > 0 && (
              <button className={styles.closeJobBtn} onClick={handleCloseJob}>
                Cancel Remaining Shifts
              </button>
            )}

          </div>
        </div>
      </main>
      {showViewShifts && jobId && (
        <ViewShifts jobId={jobId} visible={showViewShifts} onClose={() => setShowViewShifts(false)} />
      )}
    </div>
  ) : (
    <div className="min-h-screen w-full flex flex-column">
      <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
      {renderHeader()}
      <main style={{ alignItems: "unset" }} className={styles.shiftReferMainSectionMobile}>

        <div>
          <button className={styles.shiftBackBtnMobile} onClick={() => handleNavigation(navigate, clientType)}>
            <div
              style={{
                background: "#D9D9D94D",
                display: "flex",
                justifyContent: "center",
                borderRadius: "50%",
                alignItems: "center",
                paddingBlock: "5px",
                paddingInline: "5px",
              }}
            >
              <img src={backArrowCircle} alt="rightArrowCircle" width="14px" height="12px" />
            </div>
            Back to Helpers Near Me
          </button>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            flexGrow: "1",
            alignItems: "center",
          }}
        >
          <div className={styles.shiftContainersMobile}>
            <div>
              <img
                src={imageUrl}
                alt="HelperImg"
                width="147px"
                height="146px"
                style={{ borderRadius: "50%", marginTop: "20px", border: "2px solid #179D52" }}
              />
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",

              }}
            >
              {/* <h2 className={styles.ShiftHeaderMobile}>Shift Filled</h2> */}
              <div className="flex flex-column gap-2">
                <div style={{ fontSize: "14px", fontWeight: "500", color: "#585858", textAlign: "center" }} className="flex align-items-center gap-1">
                  <img src={ShiftFilledTick} alt="ShiftFilledTick" width="36px" height="41px" />
                  <span style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>{name}</span> has been awarded {filledShifts} Shifts <br />
                </div>
                {openShifts > 0 && (
                  <div style={{ fontSize: "14px", fontWeight: "500", color: "#585858", textAlign: "center" }} className="flex align-items-center gap-2 pl-1">
                    <FaRegCircleQuestion fontSize={"30px"} />
                    <span> You still have <span style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>{openShifts} shifts remaining</span></span>
                  </div>
                )}
              </div>

            </div>

            {/* <button style={{ paddingInline: "61px" }} className={styles.returnBtnMobile} onClick={() => handleManageJobsNavigation(navigate, clientType)}>
              Return to Mange Jobs
            </button> */}
            {openShifts > 0 && (
              <button style={{ paddingInline: "61px" }} className={styles.viewShiftMobile} onClick={() => setShowViewShifts(true)}>
                View Shifts
              </button>
            )}
            {openShifts > 0 && (
              <button className={styles.continueFillMobile} onClick={handleContinueFillShifts}>
                Fill Remaining Shifts
              </button>
            )}

            {openShifts > 0 && (
              <button className={styles.closeJobBtn} onClick={handleCloseJob}>
                Cancel Remaining Shifts
              </button>
            )}

          </div>
        </div>
      </main>
      {showViewShifts && jobId && (
        <ViewShifts jobId={jobId} visible={showViewShifts} onClose={() => setShowViewShifts(false)} />
      )}
    </div>
  );
};

export default AwardJobConfirmation;
