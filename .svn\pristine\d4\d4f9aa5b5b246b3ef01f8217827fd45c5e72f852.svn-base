// import React, { useState } from "react";
// import childrenIcon from "../../../../assets/images/Icons/my_child.png";
// import familyMembershipIcon from "../../../../assets/images/Icons/family_membership.png";
// import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
// import { IoHomeOutline } from "react-icons/io5";
// import { FaArrowLeft } from "react-icons/fa";
// import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
// import EditTimesheet from "./EditTimesheet";

// interface TimesheetRow {
//     start: string;
//     finish: string;
//     hours: number;
//     rate: number;
//     total: number;
// }

// interface AwaitingConfirmationCardProps {
//     profileName: string;
//     profileImage: string;
//     jobType: string;
//     jobDate: string;
//     jobAddress: string;
//     baseRate: number;
//     extraHoursRate: number;
//     timesheetRows: TimesheetRow[];
//     statusText?: string;
//     onSubmit?: () => void;
//     onGoBack?: () => void;
// }

// const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
//     profileName,
//     profileImage,
//     jobType,
//     jobDate,
//     jobAddress,
//     baseRate,
//     extraHoursRate,
//     timesheetRows,
//     statusText = "Awaiting Your Confirmation",
//     onSubmit,
//     onGoBack,
// }) => {
//     const totalAmount = timesheetRows.reduce((sum, row) => sum + row.total, 0);
//     const [showEditPage, setShowEditPage] = useState(false);

//     if (showEditPage) {
//         return (
//             <EditTimesheet
//                 date="Monday, 16th of January, 2024"
//                 profileImage="https://primefaces.org/cdn/primereact/images/avatar/amyelsner.png"
//                 profileName="Craig S"
//                 baseRate={25}
//                 extraRate={35}
//                 initialShifts={[
//                     { start: "6:00am", finish: "9:00am" },
//                     { start: "3:30pm", finish: "6:30pm" },
//                 ]}
//                 onClose={() => setShowEditPage(false)}
//                 onSave={(updatedShifts) => console.log("Saved shifts:", updatedShifts)}
//             />
//         );

//     }
//     return (
//         <>
//             <div className={styles.headerWrapper}>
//                 <button className={styles.backBtn} onClick={onGoBack}>
//                     <span className={styles.arrowCircle}>
//                         <span className={styles.arrow}><FaArrowLeft /></span>
//                     </span>
//                     Go back
//                 </button>
//             </div>

//             <div className={styles.card}>
//                 <div className={styles.headerSection}>
//                     <div>
//                         <h3 className={styles.title}>Review Timesheet</h3>
//                         <div className={styles.status}>
//                             Status: <span className={styles.statusHighlight}>{statusText}</span>
//                         </div>
//                     </div>
//                     <div className={styles.profileContainer}>
//                         <img
//                             src={profileImage}
//                             alt="Profile"
//                             className={styles.avatar}
//                         />
//                         <div className={styles.profileName}>{profileName}</div>
//                     </div>
//                 </div>

//                 <div className={styles.info}>
//                     <div className={styles.infoBlock}>
//                         <div className={styles.row}>
//                             <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
//                             <div>{jobType}</div>
//                         </div>
//                         <div className={styles.row}>
//                             <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
//                             <div>{jobDate}</div>
//                         </div>
//                         <div className={styles.row}>
//                             <IoHomeOutline className={styles.rowIcon} />
//                             <div>{jobAddress}</div>
//                         </div>
//                     </div>

//                     <hr className={styles.hrFull} />

//                     <div className={styles.rateRow}>
//                         <img
//                             src={familyMembershipIcon}
//                             alt="Family Membership"
//                             width={16}
//                             height={16}
//                             style={{ marginTop: "2px" }}
//                         />
//                         <div>
//                             <div>Base Rate: ${baseRate} per hour</div>
//                             <div>Extra Hours Rate: ${extraHoursRate} per hour</div>
//                         </div>
//                     </div>

//                     <hr className={styles.hrFull} />
//                 </div>

//                 <div className={styles.timesheetContainer}>
//                     <div className={styles.rowHeader}>
//                         <div className={styles.column}>Start</div>
//                         <div className={styles.column}>Finish</div>
//                         <div className={styles.column}>Hours</div>
//                         <div className={styles.column}>Rate</div>
//                         <div className={styles.column}>Total</div>
//                     </div>

//                     {timesheetRows.map((row, index) => (
//                         <div key={index} className={styles.rowData}>
//                             <div className={styles.column}>{row.start}</div>
//                             <div className={styles.column}>{row.finish}</div>
//                             <div className={styles.column}>{row.hours}</div>
//                             <div className={styles.column}>${row.rate}</div>
//                             <div className={styles.column}>${row.total}</div>
//                         </div>
//                     ))}

//                     <hr className={styles.hr} />

//                     <div className={styles.totalRow}>
//                         <div className={styles.column} />
//                         <div className={styles.column} />
//                         <div className={styles.column} />
//                         <div className={styles.column} />
//                         <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
//                     </div>

//                     <hr className={styles.hr} />
//                 </div>

//                 <div className={styles.footer}>
//                     <div className={styles.section}>
//                         <div className={styles.sectionText}>
//                             <strong className={styles.titlee}>Confirm Timesheet</strong>
//                             <div className={styles.subText}>The Timesheet is an accurate record of the job.</div>
//                         </div>
//                         <button className={styles.submitBtn} onClick={onSubmit}>Submit</button>
//                     </div>

//                     <div className={`${styles.section} ${styles.editSection}`}>
//                         <div className={styles.sectionText}>
//                             <strong className={styles.titlee}>Edit Timesheet</strong>
//                             <div className={styles.subText}>Make adjustments to the Timesheet.</div>
//                         </div>
//                         {/* <button className={styles.editBtn} onClick={onEdit}>Edit Timesheet</button> */}
//                         <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>
//                             Edit Timesheet
//                         </button>

//                     </div>
//                 </div>
//             </div>
//         </>
//     );
// };

// export default AwaitingConfirmationCard;


import React, { useState } from "react";
import childrenIcon from "../../../../assets/images/Icons/my_child.png";
import familyMembershipIcon from "../../../../assets/images/Icons/family_membership.png";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { IoHomeOutline } from "react-icons/io5";
import { FaArrowLeft } from "react-icons/fa";
import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
import EditTimesheet from "./EditTimesheet";

interface TimesheetRow {
    start: string;
    finish: string;
    hours: number;
    rate: number;
    total: number;
}

interface AwaitingConfirmationCardProps {
    profileName: string;
    profileImage: string;
    jobType: string;
    jobDate: string;
    jobAddress: string;
    baseRate: number;
    extraHoursRate: number;
    initialTimesheetRows: TimesheetRow[];
    statusText?: string;
    onSubmit?: () => void;
    onGoBack?: () => void;
}

const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
    profileName,
    profileImage,
    jobType,
    jobDate,
    jobAddress,
    baseRate,
    extraHoursRate,
    initialTimesheetRows,
    statusText = "Awaiting Your Confirmation",
    onSubmit,
    onGoBack,
}) => {
    
    const [timesheetRows, setTimesheetRows] = useState<TimesheetRow[]>(initialTimesheetRows);
    const [showEditPage, setShowEditPage] = useState(false);

    const totalAmount = timesheetRows.reduce((sum, row) => sum + row.total, 0);

   const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
    const updatedTimesheetRows = updatedShifts.map((shift, index) => {
        const existingRow = timesheetRows[index];

        return {
            start: shift.start,
            finish: shift.finish,
            hours: existingRow?.hours ?? 0,
            rate: existingRow?.rate ?? 0,
            total: existingRow?.total ?? 0,
        };
    });

    setTimesheetRows(updatedTimesheetRows);
    setShowEditPage(false);
};

    if (showEditPage) {
        
        const initialShiftsForEdit = timesheetRows.map(({ start, finish }) => ({ start, finish }));

        return (
            <EditTimesheet
                date={jobDate}
                profileImage={profileImage}
                profileName={profileName}
                baseRate={baseRate}
                extraRate={extraHoursRate}
                initialShifts={initialShiftsForEdit}
                onClose={() => setShowEditPage(false)}
                onSave={handleSaveShifts}
            />
        );
    }

    return (
        <>
            <div className={styles.headerWrapper}>
                <button className={styles.backBtn} onClick={onGoBack}>
                    <span className={styles.arrowCircle}>
                        <span className={styles.arrow}><FaArrowLeft /></span>
                    </span>
                    Go back
                </button>
            </div>

            <div className={styles.card}>
                <div className={styles.headerSection}>
                    <div>
                        <h3 className={styles.title}>Review Timesheet</h3>
                        <div className={styles.status}>
                            Status: <span className={styles.statusHighlight}>{statusText}</span>
                        </div>
                    </div>
                    <div className={styles.profileContainer}>
                        <img
                            src={profileImage}
                            alt="Profile"
                            className={styles.avatar}
                        />
                        <div className={styles.profileName}>{profileName}</div>
                    </div>
                </div>

                <div className={styles.info}>
                    <div className={styles.infoBlock}>
                        <div className={styles.row}>
                            <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
                            <div>{jobType}</div>
                        </div>
                        <div className={styles.row}>
                            <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
                            <div>{jobDate}</div>
                        </div>
                        <div className={styles.row}>
                            <IoHomeOutline className={styles.rowIcon} />
                            <div>{jobAddress}</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />

                    <div className={styles.rateRow}>
                        <img
                            src={familyMembershipIcon}
                            alt="Family Membership"
                            width={16}
                            height={16}
                            style={{ marginTop: "2px" }}
                        />
                        <div>
                            <div>Base Rate: ${baseRate} per hour</div>
                            <div>Extra Hours Rate: ${extraHoursRate} per hour</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />
                </div>

                <div className={styles.timesheetContainer}>
                    <div className={styles.rowHeader}>
                        <div className={styles.column}>Start</div>
                        <div className={styles.column}>Finish</div>
                        <div className={styles.column}>Hours</div>
                        <div className={styles.column}>Rate</div>
                        <div className={styles.column}>Total</div>
                    </div>

                    {timesheetRows.map((row, index) => (
                        <div key={index} className={styles.rowData}>
                            <div className={styles.column}>{row.start}</div>
                            <div className={styles.column}>{row.finish}</div>
                            <div className={styles.column}>{row.hours}</div>
                            <div className={styles.column}>${row.rate}</div>
                            <div className={styles.column}>${row.total}</div>
                        </div>
                    ))}

                    <hr className={styles.hr} />

                    <div className={styles.totalRow}>
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
                    </div>

                    <hr className={styles.hr} />
                </div>

                <div className={styles.footer}>
                    <div className={styles.section}>
                        <div className={styles.sectionText}>
                            <strong className={styles.titlee}>Confirm Timesheet</strong>
                            <div className={styles.subText}>The Timesheet is an accurate record of the job.</div>
                        </div>
                        <button className={styles.submitBtn} onClick={onSubmit}>Submit</button>
                    </div>

                    <div className={`${styles.section} ${styles.editSection}`}>
                        <div className={styles.sectionText}>
                            <strong className={styles.titlee}>Edit Timesheet</strong>
                            <div className={styles.subText}>Make adjustments to the Timesheet.</div>
                        </div>
                        <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>
                            Edit Timesheet
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default AwaitingConfirmationCard;
