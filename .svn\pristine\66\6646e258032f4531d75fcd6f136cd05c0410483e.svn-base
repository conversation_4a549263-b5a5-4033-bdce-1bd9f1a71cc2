import { useEffect, useState } from "react";
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import { AppDispatch, RootState } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import useLoader from "../../../hooks/LoaderHook";
import c from "../../../helper/juggleStreetConstants";
import CustomButton from "../../../commonComponents/CustomButton";
import { updateSessionInfo } from "../../../store/tunks/sessionInfoTunk";
import myfamilystyles from "../styles/my-family.module.css";
import useIsMobile from "../../../hooks/useIsMobile";

const Citizenship = () => {
  const dispatch = useDispatch<AppDispatch>();
  const session = useSelector((state: RootState) => state.sessionInfo.data) as {
    nationality?: string;
  };

  const [citizenship, setCitizenship] = useState<string>("");
  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const { disableLoader, enableLoader } = useLoader();
  const [countryError, setCountryError] = useState<string>("");
  const [nationality, setNationality] = useState<string>("");
  const {isMobile}=useIsMobile();
  const citizenshipOptions = [
    {
      value: "AU",
      label: "Australian Citizen",
    },
    {
      value: "NZ",
      label: "New Zealand Citizen",
    },
    {
      value: "OTHER",
      label: "Other Nationality",
    },
  ];

 useEffect(() => {
         if (session?.nationality) {
           const nationality = session.nationality.toLowerCase();
       
           if (nationality === "au") {
             setCitizenship("AU");
             setNationality("AU");
           } else if (nationality === "nz") {
             setCitizenship("NZ");
             setNationality("NZ");
           } else {
             setCitizenship("OTHER");
             setSelectedCountry(session.nationality);
             setNationality(session.nationality.toLowerCase());
           }
         } else {
           setCitizenship("OTHER");
           setNationality("OTHER");
         }
       }, [session?.nationality]);

   const handleCitizenshipChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setCitizenship(value);
        setCountryError("");
      
        if (value === "AU") {
          setNationality("au");
          setSelectedCountry("");
        } else if (value === "NZ") {
          setNationality("nz");
          setSelectedCountry("");
        } else {
          setNationality("OTHER");
          setSelectedCountry("");
        }
      };

  const handleCountryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedCountryValue = event.target.value;
    setSelectedCountry(selectedCountryValue);
    setCountryError("");

    if (selectedCountryValue) {
      setNationality(selectedCountryValue.toLowerCase());
    }
  };

  const handleSave = () => {
    // if (citizenship === "OTHER" && !selectedCountry) {
    //   setCountryError("Please select a country.");
    //   return;
    // }
    enableLoader();
    const payload = {
      ...session,
      nationality: nationality,
      country: selectedCountry,
    };

    dispatch(updateSessionInfo({ payload })).finally(() => {
      disableLoader();
    });
  };

  return (
    <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }} className={styles.utilcontainerhelper}>
      <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
        <header className={styles.utilheader}>
          <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Citizenship</h1>
        </header>
        <CustomButton
          label={"Save"}
          className={`${myfamilystyles.customButton}`}
          style={{ margin: "0", width: "150px" }}
          onClick={handleSave}
        ></CustomButton>
      </div>
      <div style={{ maxWidth: "100%", width: "800px" }}>
        <div>
          <h1
            className="m-0 p-1 txt-clr font-medium line-height-1 mt-2" style={{ fontSize: '18px',color:'#179d52' }}
          >
            Citizenship
          </h1>
        </div>
        <div className="mt-3 flex flex-column ">
          {citizenshipOptions.map((option, index) => (
            <div key={index}>
              <input
                type="radio"
                id={option.value}
                name="citizenship"
                value={option.value}
                checked={citizenship === option.value}
                onChange={handleCitizenshipChange}
                className="cursor-pointer"
              />
              <label
                htmlFor={option.value}
                className="cursor-pointer txt-clr font-medium"
                style={{ fontSize: "16px" }}
              >
                {option.label}
              </label>
            </div>
          ))}
          {citizenship === "OTHER" && (
            <div className={`${!isMobile ? "pl-3" : "pl-2 pt-2"}`}>
              <select
                value={selectedCountry}
                onChange={handleCountryChange}
                className="cursor-pointer px-3 mt-2"
                style={{
                  width: !isMobile ?  "200px" : "unset",
                  height: "40px",
                  borderRadius: "10px",
                  border: "1px solid #585858",
                }}
              >
                <option value="">Select a country</option>
                {c.countriesIso.map((country) => (
                  <option key={country.id} value={country.value}>
                    {country.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
      {countryError && (
        <p className="p-0 m-0" style={{ color: "red" }}>
          {countryError}
        </p>
      )}
    </div>
  );
};

export default Citizenship;
