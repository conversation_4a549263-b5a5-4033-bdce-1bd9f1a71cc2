import React, { useState } from 'react';
import AwaitingConfirmationCard from '../../containers/Common/payments/Common/AwaitingConfirmationCard';
import { TimesheetDetails } from '../../hooks/useTimesheetDetails';
import Service from '../../services/services';
import useLoader from '../../hooks/LoaderHook';

interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

interface TimesheetDetailsPopupProps {
  selectedEntry: TimesheetEntry | null;
  timesheetDetails: TimesheetDetails | null;
  onClose: () => void;
  onApprovalSuccess?: () => void;
}

// Utility functions
const convertTo24Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [time, modifier] = timeStr.split(" ");
  if (!time || !modifier) return timeStr; // fallback
  let [hours, minutes] = time.split(":").map(Number);

  if (modifier === "PM" && hours < 12) {
    hours += 12;
  }
  if (modifier === "AM" && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

const calculateHours = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const start = new Date(`1970-01-01T${startTime}`);
  const end = new Date(`1970-01-01T${endTime}`);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.warn("Invalid time format", { startTime, endTime });
    return 0;
  }

  const diffMs = end.getTime() - start.getTime();
  const hours = diffMs / (1000 * 60 * 60);
  return parseFloat(hours.toFixed(2));
};

const calculateTotalPrice = (hours: number, rate: number): number => {
  if (isNaN(hours) || isNaN(rate)) return 0;
  return parseFloat((hours * rate).toFixed(2));
};

const TimesheetDetailsPopup: React.FC<TimesheetDetailsPopupProps> = ({
  selectedEntry,
  timesheetDetails,
  onClose,
  onApprovalSuccess
}) => {
  const { enableLoader, disableLoader } = useLoader();

  if (!selectedEntry || !timesheetDetails) return null;

  const startTime = convertTo24Hour(timesheetDetails.jobStartTime || "");
  const endTime = convertTo24Hour(timesheetDetails.jobEndTime || "");
  const rate = Number(timesheetDetails.price) || 0;

  const hoursWorked = calculateHours(startTime, endTime);
  const totalPrice = calculateTotalPrice(hoursWorked, rate);

  console.log('TimesheetDetailsPopup - hoursWorked:', hoursWorked);
  console.log('TimesheetDetailsPopup - timesheetDetails:', timesheetDetails);

  const handleApprove = async () => {
    if (!timesheetDetails) return;

    enableLoader();
    const payload = {
      timesheetId: timesheetDetails.timesheetId,
      JobId: timesheetDetails.jobId,
      ApplicantId: timesheetDetails.applicantId,
      NewJobStartTime: timesheetDetails.jobStartTime,
      NewJobEndTime: timesheetDetails.jobEndTime,
      NewEstimatedJobHours: timesheetDetails.estimatedJobHours,
      NewEstimatedJobValue: timesheetDetails.estimatedJobValue,
      NewPrice: timesheetDetails.price,
      userId: timesheetDetails.userId,
    };

    console.log('TimesheetDetailsPopup - approval payload:', payload);

    try {
      await new Promise<void>((resolve, reject) => {
        Service.postUpdateHistory(
          (response: any) => {
            console.log("Timesheet approved:", response);
            resolve();
          },
          (error: any) => {
            console.error("Error approving timesheet:", error);
            reject(error);
          },
          payload
        );
      });

      onApprovalSuccess?.();
      onClose();
    } catch (error) {
      console.error('Approval failed:', error);
    } finally {
      disableLoader();
    }
  };

  return (
    <div className="overlay-popup">
      <div className="slide-up-card">
        <AwaitingConfirmationCard
          profileName={`${timesheetDetails.firstName} ${timesheetDetails.lastName}`}
          profileImage={timesheetDetails.originalImageUrl}
          jobType={timesheetDetails.jobType}
          jobDate={timesheetDetails.jobDate}
          jobAddress={timesheetDetails.formattedAddress}
          baseRate={timesheetDetails.price}
          extraHoursRate={timesheetDetails.overtimeRate}
          initialTimesheetRows={[
            {
              start: startTime,
              finish: endTime,
              hours: hoursWorked,
              rate,
              total: totalPrice,
            },
          ]}
          onSubmit={handleApprove}
          onGoBack={onClose}
        />
      </div>
    </div>
  );
};

export default TimesheetDetailsPopup;
