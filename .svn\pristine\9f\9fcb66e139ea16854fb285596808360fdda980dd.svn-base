import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';
import CookiesConstant from '../helper/cookiesConst';
import utils from '../components/utils/util';

interface TimesheetEntry {
  id: number;
  status: string;
  statusCode: number;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

interface TimesheetApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_CONFIRMATION]: "Awaiting Your Confirmation", // Status 1 for awaiting-confirmation
};

export const useTimesheetData = () => {
  const location = useLocation();
  const [timesheetData, setTimesheetData] = useState<TimesheetEntry[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isBusiness = clientType === c.clientType.BUSINESS;
  const showApprove = isParent || isBusiness;
  const isHelper = clientType === c.clientType.UNSPECIFIED;
  // const statusMap: { [key: number]: string } = {
  //   [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
  // };
  const statusKey = showApprove
    ? c.ApprovalStatus.AWAITING_APPROVAL
    : c.ApprovalStatus.AWAITING_CONFIRMATION;

  const statusMap: { [key: number]: string } = {
    [statusKey]: showApprove ? "Awaiting Your Approval" : "Awaiting Your Confirmation",
  };
  const isAwaitingConfirmationRoute =
    location.pathname.includes(showApprove ? '/helper-confirm' : '/awaiting-confirmation') ||
    (isParent && location.pathname === '/parent-home/timesheet') ||
    (isBusiness && location.pathname === '/business-home/timesheet') ||
    (isHelper && location.pathname === '/helper-home/timesheet');

  const fetchTimesheetData = async (): Promise<void> => {
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getTimeSheet(
          (response: TimesheetApiItem[]) => {
            console.log("API Response getTimeSheet:", response);

            if (!Array.isArray(response)) {
              console.warn("Expected array response, got:", typeof response);
              setTimesheetData([]);
              resolve();
              return;
            }

            // Filter for awaiting confirmation timesheets (status 1: AWAITING_CONFIRMATION)
            const filteredData = response.filter((item: TimesheetApiItem) =>
              item.status === c.ApprovalStatus.AWAITING_APPROVAL ||
              item.status === c.ApprovalStatus.AWAITING_CONFIRMATION
            );
            console.log("Filtered awaiting confirmation data (status 1):", filteredData);

            const mappedData: TimesheetEntry[] = filteredData.map((item) => ({
              id: item.id,
              status: statusMap[item.status] || "Unknown",
              statusCode: item.status,
              type: jobTypeMap[item.jobType] || "Unknown",
              date: new Date(item.jobDate).toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }),
              location: item.formattedAddress,
              userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
              originalImageUrl: item.originalImageUrl,
            }));

            console.log("Mapped timesheet data:", mappedData);
            setTimesheetData(mappedData);

            
            resolve();
          },
          (error: any) => {
            console.error('Error fetching timesheet data:', error);
            setError(error?.message || 'Failed to fetch timesheet data');
            reject(error);
          }
        );
      });
    } catch (err) {
      console.error('Fetch timesheet data failed:', err);
    } finally {
      disableLoader();
    }
  };

  useEffect(() => {

    if (isAwaitingConfirmationRoute) {
      console.log('🔄 Route changed to awaiting-confirmation, fetching data...');
      fetchTimesheetData();
    } else {
      console.log('📍 Not on awaiting-confirmation route, skipping API call');
    }
  }, [location.pathname]);
  const refreshData = () => {
    fetchTimesheetData();
  };

  return {
    timesheetData,
    error,
    refreshData
  };
};

export type { TimesheetEntry };
