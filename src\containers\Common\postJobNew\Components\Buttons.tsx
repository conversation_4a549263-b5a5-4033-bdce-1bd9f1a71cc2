import React from 'react';
import { FaCheck } from 'react-icons/fa6';

const BaseButton = ({ ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
    return (
        <button
            {...props}
            style={{
                border: 'none',
                backgroundColor: 'transparent',
                fontWeight: 500,
                fontSize: '14px',
                color: '#585858',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '98px',
                height: '41px',
                padding: '0',
                margin: '0',
                cursor: 'pointer',
                userSelect: 'none',
                ...props.style,
            }}
        >
            {props.children}
        </button>
    );
};

export const GoBack = ({ ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
    return <BaseButton {...props}>{`<`} Go Back</BaseButton>;
};
export const Next = ({ ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
    return (
        <BaseButton
            {...props}
            style={{
                backgroundColor: props.disabled ? '#DFDFDF' : '#FFA500',
                color: props.disabled ? 'rgba(88, 88, 88, 0.5)' : '#FFFFFF',
                fontWeight: props.disabled ? 400 : 700,
                borderRadius: '10px',
                width: '226px',
                cursor: props.disabled ? 'not-allowed' : 'pointer',
            }}
        >
            Next
        </BaseButton>
    );
};

interface Props {
    selected: boolean;
}

export const RadioButton = ({ selected }: Props) => {
    return (
        <div
            className="flex justify-content-center align-items-center"
            style={{
                minHeight: '18px',
                height: '18px',
                width: '18px',
                minWidth: '18px',
                borderRadius: '50%',
                padding: '1px',
                border: `1px solid ${selected ? '#179d52' : '#DFDFDF'}`,
            }}
        >
            {selected && (
                <div
                    style={{
                        height: '100%',
                        width: '100%',
                        borderRadius: '50%',
                        backgroundColor: '#179D52',
                    }}
                />
            )}
        </div>
    );
};

export const RadioButton2 = ({ selected }: Props) => {
    return (
        <div
            className="flex justify-content-center align-items-center"
            style={{
                minHeight: '16px',
                height: '16px',
                width: '16px',
                minWidth: '16px',
                borderRadius: '50%',
                border: `1px solid ${selected ? '#179D52' : '#DFDFDF'}`,
                backgroundColor: selected ? '#179D52' : '',
            }}
        >
            {selected && <FaCheck fontSize={'10px'} color="#ffffff" />}
        </div>
    );
};
