import { MutableRefObject, useEffect, useRef, useState } from "react";
import JuggleMaps from "../Parent/JuggleMaps";
import SidePannel from "./Components/SidePannel";
import { FiMap } from "react-icons/fi";
import {
  User<PERSON><PERSON><PERSON>,
  UserSearchResponse,
  useSearchHook,
} from "../../hooks/SearchGeoSearchHook";
import useLoader from "../../hooks/LoaderHook";
import useProviderFilter from "../../hooks/useProviderFilter";
import useScrollListener from "../../hooks/useScrollListener";
import menuIcon from "../../assets/images/Icons/Filter_alt.png";
import environment from "../../helper/environment";
import ClientCard from "./Components/ClientCard";
import HomeHeaderHelper from "./Components/HomeHeaderHelper";
import "../Common/styles/custom-homepage-card.css";
import { IoStarSharp } from "react-icons/io5";
import { SlLocationPin } from "react-icons/sl";
import HelperAgedCarePopup from "./HelperAgedCarePopup";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import {
  updateinterestInHomeAgedCareResponse,
  updateProfileActivationEnabled,
} from "../../store/slices/applicationSlice";
import c from "../../helper/juggleStreetConstants";
import ProfileCompleteCongoLoader from "../Common/ProfileCompleteCongoLoader";
import juggleLogo from "../../assets/images/juggle_white.png";
import styles from "../Common/styles/parent-home.module.css";
import useIsMobile from "../../hooks/useIsMobile";
import WelcomeComponent from "../Common/WelcomeComponent";
import {
  ConfirmationPopupGreen,
  useConfirmationPopup,
} from "../Common/ConfirmationPopup";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import { useNavigate } from "react-router-dom";
import { RxUpdate } from "react-icons/rx";
import WwccAlerts from "./Components/WwccAlerts";
import AllFiltersHelper from "./Components/AllFiltersHelper";
import ShimmerHelperCard from "../Common/ShimmerHelperCard";
const HelperCardContainer = ({
  user,
  onRefresh,
}: {
  user: UserResult;
  onRefresh: () => void;
}) => {
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div className="w-full h-full p-2">
      <ClientCard
        {...user}
        helperName={user.publicName}
        imgSrc={`${environment.getStorageURL(
          window.location.hostname
        )}/images/${user.imageSrc}`}
        items={[
          {
            icon: <SlLocationPin color="#37A950" fontSize={"18px"} />,
            description: (
              <span>
                {user.suburb} - {user.distanceInKm}Km
              </span>
            ),
          },
          {
            icon: <IoStarSharp color="#FFA500" fontSize={"18px"} />,
            description: (
              <span className="font-light">
                {user.ratingsAvg.toFixed(1)} ({user.ratingsCount} ratings)
              </span>
            ),
          },
          ...(user.accountSubtype === c.clientType.BUSINESS
            ? [
                {
                  icon: null,
                  description: (
                    <span className={styles.businessTag}>Business</span>
                  ),
                },
              ]
            : []),
        ]}
        refresh={onRefresh}
      />
    </div>
  ) : (
    <div className="w-full  p-2">
      <ClientCard
        {...user}
        helperName={user.publicName}
        imgSrc={`${environment.getStorageURL(
          window.location.hostname
        )}/images/${user.imageSrc}`}
        items={[
          {
            icon: <SlLocationPin color="#37A950" fontSize={"14px"} />,
            description: (
              <span style={{ fontSize: "12px" }}>
                {user.suburb} - {user.distanceInKm}Km
              </span>
            ),
          },
          {
            icon: <IoStarSharp color="#FFA500" fontSize={"14px"} />,
            description: (
              <span style={{ fontSize: "12px" }}>
                {user.ratingsAvg.toFixed(1)} ({user.ratingsCount} ratings)
              </span>
            ),
          },
          ...(user.accountSubtype === c.clientType.BUSINESS
            ? [
                {
                  icon: null,
                  description: (
                    <span className={styles.businessTagMobile}>Business</span>
                  ),
                },
              ]
            : []),
        ]}
        refresh={onRefresh}
      />
    </div>
  );
};

const HelperCardList = ({
  userSearchResponse,
  onRefresh,
}: {
  userSearchResponse: UserSearchResponse;
  onRefresh: () => void;
}) => {
  const { isMobile } = useIsMobile();

  // Show shimmer if data is not loaded yet
  if (!userSearchResponse || !userSearchResponse.results || userSearchResponse.results.length === 0) {
    return !isMobile ? (
      <div className="flex-grow-1 grid grid-nogutter z-1">
        {Array.from({ length: 6 }).map((_, index) => ( // Show 6 shimmer cards as placeholders
          <div key={index} className="col-12 lg:col-6 xl:col-4">
            <ShimmerHelperCard />
          </div>
        ))}
      </div>
    ) : (
      <div className="flex-grow-1 grid grid-nogutter z-1 mt-2">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="col-6 lg:col-6 xl:col-4">
            <ShimmerHelperCard />
          </div>
        ))}
      </div>
    );
  }

  // Render actual data once loaded
  return !isMobile ? (
    <div className="flex-grow-1 grid grid-nogutter z-1">
      {userSearchResponse?.results?.map((user) => (
        <div key={user.id} className="col-12 lg:col-6 xl:col-4">
          <HelperCardContainer user={user} onRefresh={onRefresh} />
        </div>
      ))}
    </div>
  ) : (
    <div className="flex-grow-1 grid grid-nogutter z-1 mt-2">
      {userSearchResponse?.results?.map((user) => (
      <div key={user.id} className="custom-card col-6 md:col-6 lg:col-3 xl:col-2-4">
          <HelperCardContainer user={user} onRefresh={onRefresh} />
        </div>
      ))}
    </div>
  );
};

const HelperMain = ({
  listener,
}: {
  listener: MutableRefObject<HTMLDivElement>;
}) => {
  const [useSearchResponse, setUseSearchResponse] =
    useState<UserSearchResponse | null>(null);
  const [showAllFilters, setShowAllFilters] = useState<boolean>(false);
  const [filtersApplied, setFiltersApplied] = useState<boolean>(false); // Tracks if filters are applied
  const [isAbove1024, setIsAbove1024] = useState(window.innerWidth > 1024); // New state
  const { enableLoader, disableLoader } = useLoader();
  const { isMobile } = useIsMobile();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  const resetFiltersRef = useRef(null);
  const [showProfileCompletenessDialog, setShowProfileCompletenessDialog] =
    useState(true);
  const { sideBarIsOpened: sidebarIsOpen, shouldShowProfileActivation } =
    useSelector((state: RootState) => state.applicationState);
  const { updateFilters, refreshSearchResult } = useSearchHook((data) => {
    setUseSearchResponse((prev) => {
      if (!data || !data.results) {
        disableLoader();
        return prev;
      }

      if (data.results.length === 0) {
        disableLoader();
        return prev;
      }

      const prevResultsMap = new Map(
        (prev?.results || []).map((item) => [item.id, item])
      );
      let resultsChanged = false;

      const newResults = data.results.map((newItem) => {
        const existingItem = prevResultsMap.get(newItem.id);

        if (!existingItem) {
          resultsChanged = true;
          return newItem;
        }

        if (JSON.stringify(existingItem) !== JSON.stringify(newItem)) {
          resultsChanged = true;
          return newItem;
        }

        return existingItem;
      });

      if (!resultsChanged) {
        disableLoader();
        return prev;
      }

      const finalResults = [...(prev?.results || [])];
      newResults.forEach((newItem) => {
        const existingIndex = finalResults.findIndex(
          (item) => item.id === newItem.id
        );
        if (existingIndex === -1) {
          finalResults.push(newItem);
        } else {
          finalResults[existingIndex] = newItem;
        }
      });

      return {
        ...prev,
        results: finalResults,
        total: data?.total || prev?.total || 0,
      };
    });
    disableLoader();
  });

  useProviderFilter({
    defaultFilters: {
      pageIndex: 1,
      pageSize: 50,
      sortBy: "experience",
      filters: [
        { field: "distance", operator: "eq", value: 2 },
        { field: "activity", operator: "eq", value: 0 },
        { field: "neighbourhood", operator: "eq", value: 0 },
      ],
    },
    onFilterChange: (filters) => {
      updateFilters((prev) => filters);
    },
    enableLoader,
    setSearchResponse: setUseSearchResponse,
  });
  useEffect(() => {
    const handleResize = () => setIsAbove1024(window.innerWidth > 1024);
    window.addEventListener("resize", handleResize);
    handleResize(); // Set initial value
    return () => window.removeEventListener("resize", handleResize);
}, []);
  const handlePagination = () => {
    enableLoader();
    updateFilters((prev) => {
      return {
        ...prev,
        pageIndex: prev.pageIndex + 1,
      };
    });
  };

  return !isMobile ? (
    <div
      ref={listener}
      className="w-full h-full flex flex-column pl-6 pr-3 pb-3 relative overflow-y-auto overflow-x-hidden"
      style={{
        transition: "all 0.3s ease-in-out",
        backgroundColor: "#FFFFFF",
      }}
    >
      {shouldShowProfileActivation &&
        sessionInfo.data["profileCompleteness"] >= 100 && (
          <ProfileCompleteCongoLoader />
        )}

      {!sessionInfo.loading &&
        Number(sessionInfo.data["profileCompleteness"]) < 100 && (
          <WelcomeComponent
            dialogBoxState={showProfileCompletenessDialog}
            reverseWhen={65}
            profileCompletion={Number(sessionInfo.data["profileCompleteness"])}
            userName={sessionInfo.data["firstName"]}
            closeClicked={() => setShowProfileCompletenessDialog(false)}
            onExploreClicked={() => setShowProfileCompletenessDialog(false)}
          />
        )}
      <AllFiltersHelper
        enable={showAllFilters}
        availableHelpers={useSearchResponse}
        onClose={(payload) => {
          setShowAllFilters(false);
          if (payload === null) return;
          enableLoader();
          setUseSearchResponse(null);
          updateFilters(() => payload);
        }}
        selectedJobCategory={"Childcare"}
      />
      <div
        className="sticky top-0  flex justify-content-between align-items-center"
        style={{
          minHeight: "60px",
          borderBottom: "2px solid #58585812",
          backgroundColor: "#FFFFFF",
          transition: "all 0.3s ease-in-out",
          zIndex: 2,
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            color: "#787777",
            textWrap: "nowrap",
            fontSize: "18px",
            fontWeight: "400",
          }}
        >
          <span>
            <b
              style={{
                height: "100px",
                fontSize: "24px",
                color: "#585858",
                fontWeight: "700",
              }}
            >
              {useSearchResponse !== null
                ? useSearchResponse.results.length
                : 0}
              {" of "}
              {useSearchResponse !== null ? useSearchResponse.total : 0}{" "}
            </b>
          </span>
          Clients Near Me
        </p>
        <div className="flex cursor-pointer">
          <div
            className="home-all-filter-btn pl-2 pr-2"
            style={{
              border: "2px solid rgba(88, 88, 88, 0.3)",
              borderRadius: "15px",
              height: "43px",
              alignItems: "center",
            }}
            onClick={() => setShowAllFilters(true)}
          >
            <img
              alt="menuIcon"
              src={menuIcon}
              style={{ width: "20px", color: "#FFFFFF" }}
            />
            &nbsp;
            <p
              className="font-medium "
              style={{
                color: "#585858",
                textWrap: "nowrap",
                fontSize: "16px",
              }}
            >
              All Filters
            </p>
          </div>
        </div>
      </div>
      <HelperCardList
        userSearchResponse={useSearchResponse}
        onRefresh={() => {
          enableLoader();
          setUseSearchResponse(null);
          setTimeout(() => {
            refreshSearchResult();
          }, 1000);
        }}
      />
      <div
        className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
        onClick={handlePagination}
        style={{
          border: "1px solid #000000",
          borderRadius: "5px",
          fontSize: "14px",
          color: "#585858",
          minHeight: "42px",
        }}
      >
        See More 
      </div>
    </div>
  ) : (
    <div
      ref={listener}
     
      style={{
        height: "100vh", width: isAbove1024 && sidebarIsOpen ? "calc(100vw - 288px)" : "100vw",
        marginLeft: isAbove1024 && sidebarIsOpen ? "288px" : "0",
    }}
    >
      {shouldShowProfileActivation &&
        sessionInfo.data["profileCompleteness"] >= 100 && (
          <ProfileCompleteCongoLoader />
        )}

      {!sessionInfo.loading &&
        Number(sessionInfo.data["profileCompleteness"]) < 100 && (
          <WelcomeComponent
            dialogBoxState={showProfileCompletenessDialog}
            reverseWhen={65}
            profileCompletion={Number(sessionInfo.data["profileCompleteness"])}
            userName={sessionInfo.data["firstName"]}
            closeClicked={() => setShowProfileCompletenessDialog(false)}
            onExploreClicked={() => setShowProfileCompletenessDialog(false)}
          />
        )}
      <AllFiltersHelper
        enable={showAllFilters}
        availableHelpers={useSearchResponse}
        onClose={(payload) => {
          setShowAllFilters(false);
          if (payload === null) return;
          enableLoader();
          setUseSearchResponse(null);
          updateFilters(() => payload);
          setFiltersApplied(true); // Add this line
        }}
        selectedJobCategory={"Childcare"}
        onResetRef={(resetFn) => {
          resetFiltersRef.current = resetFn;
      }}
      />
      <div
        className="flex justify-content-between align-items-center px-3"
        style={{
          minHeight: "60px",
          borderBottom: "2px solid #58585812",
          backgroundColor: "#FFFFFF",
          transition: "all 0.3s ease-in-out",
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            color: "#787777",
            textWrap: "nowrap",
            fontSize: "14px",
            fontWeight: "400",
          }}
        >
          <span>
            <b
              style={{
                height: "100px",
                fontSize: "16px",
                color: "#585858",
                fontWeight: "700",
              }}
            >
              {useSearchResponse !== null
                ? useSearchResponse.results.length
                : 0}
              {" of "}
              {useSearchResponse !== null ? useSearchResponse.total : 0}{" "}
            </b>
          </span>
          Clients Near Me
        </p>
        {/* <div className="flex cursor-pointer mt-1">
          <div
            className="home-all-filter-btn pl-2 pr-2"
            style={{
              border: "2px solid rgba(88, 88, 88, 0.3)",
              borderRadius: "15px",
              height: "35px",
              alignItems: "center",
              paddingInline: "10px",
            }}
            onClick={() => setShowAllFilters(true)}
          >
            <img
              alt="menuIcon"
              src={menuIcon}
              style={{ width: "20px", color: "#FFFFFF" }}
            />
            &nbsp;
            <p
              className="font-medium "
              style={{
                color: "#585858",
                textWrap: "nowrap",
                fontSize: "14px",
                margin: "0px",
              }}
            >
              All Filters
            </p>
          </div>
        </div> */}

        <div className="flex cursor-pointer mt-1 gap-3">
        {/* Combined All Filters/Clear Filters Button */}
        <div
        className="home-all-filter-btn pl-2 pr-2"
        style={{
          border: "2px solid rgba(88, 88, 88, 0.3)",
          borderRadius: "15px",
          height: "37px",
          alignItems: "center",
        }}
        onClick={() => {
          if (filtersApplied) {
            enableLoader();
            if (resetFiltersRef.current) {
              resetFiltersRef.current();
            }
            setUseSearchResponse(null);
            updateFilters((prev) => ({
              ...prev,
              pageIndex: 1,
              filters: [
                { field: "distance", operator: "eq", value: 2 },
                { field: "activity", operator: "eq", value: 0 },
                { field: "neighbourhood", operator: "eq", value: 0 },
              ],
            }));
            setFiltersApplied(false);
            setTimeout(() => {
              disableLoader();
            }, 1000);
          } else {
            setShowAllFilters(true);
          }
        }}
      >
        <img
          alt="Filter"
          src={menuIcon}
          style={{ width: "20px", color: "#FFFFFF" }}
        />
     
    <p
      className="font-medium"
      style={{
        color: "#585858",
        textWrap: "nowrap",
        fontSize: "14px",
      }}
    >
      {filtersApplied ? "Clear Filters" : "All Filters"}
    </p>
  </div>
    </div>
      </div>
      <HelperCardList
        userSearchResponse={useSearchResponse}
        onRefresh={() => {
          enableLoader();
          setUseSearchResponse(null);
          setTimeout(() => {
            refreshSearchResult();
          }, 1000);
        }}
      />
      <div
        className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
        onClick={handlePagination}
        style={{
          border: "1px solid #000000",
          borderRadius: "5px",
          fontSize: "14px",
          color: "#585858",
          minHeight: "42px",
        }}
      >
        See More 
      </div>
    </div>
  );
};

const HelperHome = () => {
  const [showBackToMapView, setShowBackToMapView] = useState<boolean>(false);
  const { listener, position } = useScrollListener();
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const applicationState = useSelector(
    (state: RootState) => state.applicationState
  );
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const [accountstatus, setAccountStatus] = useState<number | null>(null);
  // const accountstatus = sessionInfo?.data["accountType"];
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const navigate = useNavigate();
  const showPopup = () => setIsPopupVisible(true);
  const hidePopup = () => dispatch(updateinterestInHomeAgedCareResponse(false));
  const { isMobile } = useIsMobile();
  useEffect(() => {
    if (sessionInfo.loading) return;
    setAccountStatus(sessionInfo.data["accountType"]);
  }, [sessionInfo]);
  useEffect(() => {}, [listener, position]);

  const handleBackToMapView = () => {
    setShowBackToMapView(false);
    if (listener) {
      listener.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    if (position > 300) {
      setShowBackToMapView(true);
    }
  }, [position]);
  const renderHeader = () => (
    <header
      className={`w-full flex align-items-center justify-content-center  overflow-hidden ${styles.headerGradient}`}
    >
      <img className={styles.juggleLogo} src={juggleLogo} alt="juggle logo" />
     
    </header>
  );
  const HorizontalNavigation = {
    Item: (
      index: number,
      icon: React.ReactNode,
      label: string,
      onClick: () => void
    ) => {
      const isPostJob = label === "Profiles"; // Always active for Post Job
      return (
        <div className={styles.horizontalnavitemcontainer}>
          <div
            className={`${styles.horizontalNavItem} ${
              isPostJob ? styles.activeTab : ""
            }`}
            onClick={onClick}
          >
            {icon}
            <span className={styles.navLabel}>{label}</span>
          </div>
          {/* Active line below only for Post Job */}
          {isPostJob && <div className={styles.activeLine}></div>}
        </div>
      );
    },
  };
  return !isMobile ? (
    <div className="w-screen h-screen relative">
      <SidePannel activeindex={0} />
      <HomeHeaderHelper />
      <JuggleMaps
        className="transition-all transition-duration-300 transition-ease-in-out"
        height={`calc(100% - ${showBackToMapView ? "80vh" : "350px"})`}
        width="calc(100% - 255px)"
        position="absolute"
        top={0}
        right={0}
      />
      {showBackToMapView && (
        <div
          className="flex cursor-pointer absolute"
          onClick={handleBackToMapView}
          style={{
            left: "60%",
            transform: "translateX(-50%)",
            bottom: "82vh",
            zIndex: 900,
          }}
        >
          <div
            className="flex align-items-center pl-3 pr-3"
            style={{
              border: "2px solid rgba(88, 88, 88, 0.3)",
              borderRadius: "20px",
              height: "44px",
              backgroundColor: "#585858",
            }}
          >
            <FiMap style={{ color: "#FFFFFF" }} />
            &nbsp;&nbsp;
            <p
              className="font-bold"
              style={{ color: "#FFFFFF", fontSize: "12px" }}
            >
              Back to map view
            </p>
          </div>
        </div>
      )}

      {accountstatus == c.accountStatus.APPROVED &&
        sessionInfo.data?.["interestInHomeAgedCareResponse"] == null &&
        sessionInfo?.data?.["provider"]?.["jobsAwarded"] !== 0 &&
        applicationState.interestInHomeAgedCareResponse && (
          <HelperAgedCarePopup
            visible={
              accountstatus == c.accountStatus.APPROVED &&
              sessionInfo.data?.["interestInHomeAgedCareResponse"] == null &&
              sessionInfo?.data?.["provider"]?.["jobsAwarded"] !== 0 &&
              applicationState.interestInHomeAgedCareResponse
            }
            onHide={hidePopup}
          />
        )}

      <div
        className="absolute bottom-0 right-0 overflow-hidden"
        style={{
          height: showBackToMapView ? "80vh" : "350px",
          width: "calc(100% - 250px)",
          overflow: "hidden",
          transition: "all 0.3s ease-in-out",
        }}
      >
        <HelperMain listener={listener} />
      </div>
    </div>
  ) : (
    <div className="w-screen h-screen relative">
      <SidePannel activeindex={2} />
      <HomeHeaderHelper />
      <ConfirmationPopupGreen confirmationProps={confirmationProps} />
      {renderHeader()}
      <WwccAlerts />
      <div className={styles.tabContainer}>
        {[
          {
            item: ({ index, itemStyles }) =>
              HorizontalNavigation.Item(
                index,
                <i
                  style={{ fontSize: "20px" }}
                  className={`pi pi-users ${itemStyles.navIcon}`}
                ></i>,
                "Profiles",
                () => {
                  const path = "/helper-home";
                  navigate(path);
                }
              ),
          },

          {
            item: ({ index, itemStyles }) =>
              HorizontalNavigation.Item(
                index,
                <i className={`pi pi-list ${itemStyles.navIcon}`}></i>,
                "My Jobs",
                () => {
                  if (sessionInfo.data["profileCompleteness"] <= 99) {
                    showConfirmationPopup(
                      "Complete",
                      `Your account must be 100% complete before proceeding.`,
                      "Complete",
                      <RxUpdate style={{ fontSize: "20px" }} />,
                      () => {
                        dispatch(updateProfileActivationEnabled(true));
                      }
                    );
                    return; // Prevent navigation
                  }
                  const path = "/helper-home/myjobs?jobId=-1";
                  navigate(path);
                }
              ),
          },

          {
            item: ({ index, itemStyles }) =>
              HorizontalNavigation.Item(
                index,
                <i className={`pi pi-comments ${itemStyles.navIcon}`}></i>,
                "Chat",
                () => {
                  if (sessionInfo.data["profileCompleteness"] <= 99) {
                    showConfirmationPopup(
                      "Complete",
                      `Your account must be 100% complete before proceeding.`,
                      "Complete",
                      <RxUpdate style={{ fontSize: "20px" }} />,
                      () => {
                        dispatch(updateProfileActivationEnabled(true));
                      }
                    );
                    return; // Prevent navigation
                  }

                  const path = "/helper-home/inAppChat";
                  navigate(path);
                }
              ),
          },
        ].map((tab, index) => (
          <div key={index} className={styles.tabItem}>
            {tab.item({ index, itemStyles: styles })}
          </div>
        ))}
      </div>

      {/* <JuggleMaps
                className='transition-all transition-duration-300 transition-ease-in-out'
                height={`calc(100% - ${showBackToMapView ? '80vh' : '350px'})`}
                width='calc(100% - 255px)'
                position='absolute'
                top={0}
                right={0}
            /> */}
      {/* {showBackToMapView && (
                <div
                    className='flex cursor-pointer absolute'
                    onClick={handleBackToMapView}
                    style={{
                        left: '60%',
                        transform: 'translateX(-50%)',
                        bottom: '82vh',
                        zIndex: 900,
                    }}
                >
                    <div
                        className='flex align-items-center pl-3 pr-3'
                        style={{
                            border: '2px solid rgba(88, 88, 88, 0.3)',
                            borderRadius: '20px',
                            height: '44px',
                            backgroundColor: '#585858',
                        }}
                    >
                        <FiMap style={{ color: '#FFFFFF' }} />
                        &nbsp;&nbsp;
                        <p className='font-bold' style={{ color: '#FFFFFF', fontSize: '12px' }}>
                            Back to map view
                        </p>
                    </div>
                </div>
            )} */}

{accountstatus == c.accountStatus.APPROVED &&
        sessionInfo.data?.["interestInHomeAgedCareResponse"] == null &&
        sessionInfo?.data?.["provider"]?.["jobsAwarded"] !== 0 &&
        applicationState.interestInHomeAgedCareResponse && (
          <HelperAgedCarePopup
            visible={
              accountstatus == c.accountStatus.APPROVED &&
              sessionInfo.data?.["interestInHomeAgedCareResponse"] == null &&
              sessionInfo?.data?.["provider"]?.["jobsAwarded"] !== 0 &&
              applicationState.interestInHomeAgedCareResponse
            }
            onHide={hidePopup}
          />
        )}

      <div
        style={{
          height: "100%",
          width: "100%",
          // overflow: "hidden",
          transition: "all 0.3s ease-in-out",
        }}
      >
        <HelperMain listener={listener} />
      </div>
    </div>
  );
};

export default HelperHome;
