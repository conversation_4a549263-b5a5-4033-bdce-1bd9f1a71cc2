.introductionContainer {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  max-width: 60vw;
  justify-content: space-between;
  align-items: center;
  padding: 40px;
}

.introductionHeader {
  width: 100%;
}

.introductionHeader h1 {
  width: 100%;
  height: 48px;
  font-weight: 700;
  font-size: 32px;
  color: #585858;
  line-height: 48px;
}
.introductionProgressBar {
  width: 100%;
  max-width: 100%;
  height: 7px;
  margin-top: 1rem;
}

.introductionProgressBar > div {
  background-color: #179d52 !important;
}
.introductionProgressBar > div > div {
  display: none;
}

.introductionTextArea {
  height: 150px;
  border-radius: 10px;
  border: 2px solid #f0f4f7;
  resize: none;
  background-color: #f0f4f7;
  padding: 10px 20px;
  color: #585858;
}

.introductionTextArea::placeholder {
  color: #585858;
}

.introductionTextArea:not(:placeholder-shown) {
  border-color: #179d52;
}
.introbuttonDiv{
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  height: min-content;
  box-shadow: 0 0 8px #00000040;
  background-color: #fff;
}
.IntroductionButtonDiv{}
@media (max-width: 500px) {
  .introductionContainer {
    height: 90vh;
    max-width: 90vw;
  }
}
