import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Profile from "../../../assets/images/Icons/about_me.png";
import Smiley from "../../../assets/images/Icons/my_child.png";
import Book from "../../../assets/images/Icons/book.png";
import OddJobs from "../../../assets/images/Icons/odd_job.png";
import AuPair from "../../../assets/images/Icons/my_family.png";
import Map from "../../../assets/images/Icons/mapIcon.png";
import SideCard from "./SideCard";
import ProfileTab from "./Tabs/ProfileTab";
import ChildCareTab from "./Tabs/ChildCareTab";
import TutoringTab from "./Tabs/TutoringTab";
import OddJobsTab from "./Tabs/OddJobsTab";
import Service from "../../../services/services";
import { ExtendedProfileTabProps, Helper } from "./types";
import useLoader from "../../../hooks/LoaderHook";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import AuPairTab from "./Tabs/AuPairTab";
import MapTab from "./Tabs/MapTab";
import useIsMobile from "../../../hooks/useIsMobile";

const TabFactory: Record<number, React.FC<ExtendedProfileTabProps>> = {
  0: ProfileTab,
  1: ChildCareTab,
  2: TutoringTab,
  3: OddJobsTab,
  4: AuPairTab,
  5: MapTab,
};

interface ProviderProfileProps {
  candidateId: number;
  requestId: number;
}

const ProviderProfilePopup: React.FC<ProviderProfileProps> = ({ candidateId, requestId }) => {
  const [activeTab, changeActiveTab] = useState(0);
  const [helper, setHelper] = useState<Helper | null>(null);
  const [tabs, setTabs] = useState<
    Set<{
      label: string;
      icon: string;
      index: number;
    }>
  >(new Set([{ label: "Helpers Profile", icon: Profile, index: 0 }]));
  const navigate = useNavigate();
  const { isMobile } = useIsMobile();
  const { enableLoader, disableLoader } = useLoader();
  const Tab = TabFactory[activeTab];
  const clientType = utils.getCookie(CookiesConstant.clientType);
  // var livesCloseBy = data.distanceInKiloMetersRounded <= 10;
  const lodHelper = () => {
    enableLoader();
    Service.getHelper(
      candidateId,
      (data: Helper) => {
        const childcare = data.interestedInChildcareJobs;
        const tutoring = data.interestedInTutoringJobs;
        const oddJobs = data.interestedInOddJobs;
        const auPair = data.interestedInAuPairJobs;
        const map = data.ratings.length > 0
        setTabs(() => {
          const newTabs = [];
          newTabs.push({ label: "Helpers Profile", icon: Profile, index: 0 });
          if (childcare) {
            newTabs.push({ label: "Childcare", icon: Smiley, index: 1 });
          }
          if (tutoring && clientType !== "2") {
            newTabs.push({ label: "Tutoring", icon: Book, index: 2 });
          }
          if (oddJobs && clientType !== "2") {
            newTabs.push({ label: "Odd Jobs", icon: OddJobs, index: 3 });
          }
          if (auPair && clientType !== "2") {
            newTabs.push({ label: "Au Pair", icon: AuPair, index: 4 });
          }
          if (map && !isMobile) {
            newTabs.push({ label: "Map", icon: Map, index: 5 });
          }
          return new Set(newTabs);
        });
        setHelper(data);
        disableLoader();
      },
      (error) => {
        disableLoader();
        // alert(error);
      }
    );
  }
  useEffect(() => {
    lodHelper();
  }, []);

  return (
    <div className="flex" style={{ width: '1200px' }}>
      <div
        className="relative flex flex-column px-6"
      >
        <div
          className="flex relative my-4"
          style={{
            borderRadius: "30px",
            overflow: "hidden",
            minHeight: "113px",
            border: "1px solid #F1F1F1",
            paddingInline: "40px",
            paddingBlock: "45px 30px",
          }}
        >
          <div
            className="w-full absolute top-0 left-0"
            style={{
              inset: "0",
              borderRadius: "30px",
              background:
                "linear-gradient(90deg, rgba(255, 165, 0, 0.2), rgba(55, 169, 80, 0.2))",
              height: "113px",
              zIndex: 0,
            }}
          />
          <div className="flex justify-content-between w-full">
            <div className="flex flex-grow-1 flex-column">
              <div
                className="flex gap-2 mb-6"
                style={{
                  minWidth: "673px",
                  paddingInline: "10px",
                  zIndex: 1,

                }}
              >
                {[...tabs].map((value, index) => (
                  <div
                    key={index}
                    className="flex gap-2 justify-content-around align-items-center cursor-pointer"
                    style={{
                      backgroundColor: "#FFFFFF",
                      borderRadius: "20px",
                      padding: "10px 20px",
                      border:
                        activeTab === value.index
                          ? "2px solid rgba(255, 165, 0, 1)"
                          : "",
                      boxShadow:
                        activeTab === value.index
                          ? "0 4px 4px 0 rgba(0, 0, 0, 0.25)"
                          : "",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      changeActiveTab(value.index);
                    }}
                  >
                    <img
                      src={value.icon}
                      alt="Profile"
                      width="14.4"
                      height="14.4px"
                    />
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: activeTab === value.index ? "600" : "400",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {value.label}
                    </p>
                  </div>
                ))}
              </div>
              <Tab helper={helper ?? null} />
            </div>
            <SideCard helper={helper} requestId={requestId} userId={candidateId} hideText={true} refresh={lodHelper} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProviderProfilePopup;