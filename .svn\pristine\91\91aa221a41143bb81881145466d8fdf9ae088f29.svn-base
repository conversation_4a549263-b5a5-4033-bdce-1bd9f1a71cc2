import React, { useEffect } from "react";
import star from "../../../../assets/images/Icons/filled-star.png";
import home from "../../../../assets/images/Icons/home.png";
import calendar from "../../../../assets/images/Icons/manage_job.png";
import clock from "../../../../assets/images/Icons/clockstart.png";
import ChildcareImage from "../../../../assets/images/Icons/childcare-smile.png";
import sideArrow from "../../../../assets/images/Icons/side-aroow.png";
import styles from '../../styles/job-history-card.module.css';
import OddJobImage from "../../../../assets/images/Icons/odd_job.png";
import TutoringImage from "../../../../assets/images/Icons/tutoring-book.png";
import { Jobs } from "../types";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import useIsMobile from "../../../../hooks/useIsMobile";
interface JobHistoryCardProps {
  name: string;
  jobTitle: string;
  time: string;
  date: string;
  address: string;
  avatarUrl?: string;
  className?: string;
  currentJob?: Jobs;
  ratingProvided: any;
  ratingCount?: number;
  isRecurring?: boolean;
  isTutoring?: boolean;
  jobStatus: number;
  onClick?: () => void;
  viewJobHistroy?: () => void;
  duplicateJob?: () => void;
  onClickChat?: () => void;
  applicantsNumber?: number
}
const getJobDetails = (currentJob?: Jobs) => {
  const jobToUse = currentJob || { jobType: 0 };

  switch (jobToUse.jobType) {
    case 256:
      return {
        label: "Odd Job",
        image: (
          <img src={OddJobImage} alt="Odd Job" width={14.33} height={13.5} />
        ),
      };
    case 128:
    case 64:
      return {
        label: "Tutoring",
        image: (
          <img src={TutoringImage} alt="Tutoring" width={14.33} height={13.5} />
        ),
      };
    default:
      return {
        label: "Childcare",
        image: (
          <img
            src={ChildcareImage}
            alt="Childcare"
            width={14.33}
            height={13.5}
          />
        ),
      };
  }
};

const JobHistoryCard: React.FC<JobHistoryCardProps> = ({
  name,
  jobTitle,
  time,
  date,
  address,
  avatarUrl = "/api/placeholder/80/80",
  className = "",
  currentJob,
  ratingProvided,
  ratingCount,
  jobStatus,
  onClick,
  onClickChat,
  viewJobHistroy,
  duplicateJob,
  isRecurring,
  isTutoring,
  applicantsNumber
}) => {
  const { label, image } = getJobDetails(currentJob);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const { isMobile } = useIsMobile();

  // const normalizedRatingCount = typeof ratingCount === "number" && ratingCount >= 0 ? ratingCount : 0;

  // const renderStars = () => {
  //   // Assuming normalizedRatingCount is your ratingProvided value (e.g., 4.8)
  //   const fullStars = Math.floor(normalizedRatingCount); // e.g., 4 for 4.8
  //   const decimalPart = normalizedRatingCount - fullStars; // e.g., 0.8 for 4.8
  //   const hasHalfStar = decimalPart > 0; // Any decimal counts as half

  //   return [...Array(5)].map((_, index) => {
  //     const starValue = index + 1; // 1-based value for comparison
  //     const isFull = starValue <= fullStars; // Full star if within full count
  //     const isHalf = !isFull && hasHalfStar && starValue === fullStars + 1; // Half star for next one
  //     const fillPercentage = isFull
  //       ? 100
  //       : isHalf
  //       ? 50 // Fixed 50% for half star
  //       : 0;

  //     return (
  //       <div
  //         key={index}
  //         className="inline-block relative"
  //         style={{ width: "28px", height: "28px" }}
  //       >
  //         {/* Empty star background */}
  //         <img
  //           src={star}
  //           alt="Rating"
  //           width="28px"
  //           height="28px"
  //           className="absolute top-0 left-0 fill-gray-200 text-gray-200"
  //           style={{ filter: "grayscale(100%)" }}
  //         />
  //         {/* Filled star portion */}
  //         {fillPercentage > 0 && (
  //           <img
  //             src={star}
  //             alt="Rating"
  //             width="28px"
  //             height="28px"
  //             className="absolute top-0 left-0 fill-yellow-400 text-yellow-400"
  //             style={{
  //               filter: "grayscale(0)",
  //               clipPath: `inset(0 ${100 - fillPercentage}% 0 0)`,
  //             }}
  //           />
  //         )}
  //       </div>
  //     );
  //   });
  // };


  const normalizedRatingCount = typeof ratingCount === "number" && ratingCount >= 0 ? ratingCount : 0;

  const renderStars = () => {
    const fullStars = Math.floor(normalizedRatingCount); // e.g., 3 for 3.3 or 3.8
    const decimalPart = normalizedRatingCount - fullStars; // e.g., 0.3 for 3.3, 0.8 for 3.8
    const hasHalfStar = decimalPart >= 0.5; // Half star only if decimal ≥ 0.5

    return [...Array(5)].map((_, index) => {
      const starValue = index + 1; // 1-based value for comparison
      const isFull = starValue <= fullStars; // Full star if within full count
      const isHalf = !isFull && hasHalfStar && starValue === fullStars + 1; // Half star for next one if decimal ≥ 0.5
      const fillPercentage = isFull ? 100 : isHalf ? 50 : 0;

      return (
        <div
          key={index}
          className="inline-block relative"
          style={{ width: "18px", height: "18px" }}
        >
          {/* Empty star background */}
          <img
            src={star}
            alt="Rating"
            width="18px"
            height="18px"
            className="absolute top-0 left-0 fill-gray-200 text-gray-200"
            style={{ filter: "grayscale(100%)" }}
          />
          {/* Filled star portion */}
          {fillPercentage > 0 && (
            <img
              src={star}
              alt="Rating"
              width="18px"
              height="18px"
              className="absolute top-0 left-0 fill-yellow-400 text-yellow-400"
              style={{
                filter: "grayscale(0)",
                clipPath: `inset(0 ${100 - fillPercentage}% 0 0)`,
              }}
            />
          )}
        </div>
      );
    });
  };


  const renderStarsMobile = () => {
    const fullStars = Math.floor(normalizedRatingCount); // e.g., 3 for 3.3 or 3.8
    const decimalPart = normalizedRatingCount - fullStars; // e.g., 0.3 for 3.3, 0.8 for 3.8
    const hasHalfStar = decimalPart >= 0.5; // Half star only if decimal ≥ 0.5

    return [...Array(5)].map((_, index) => {
      const starValue = index + 1; // 1-based value for comparison
      const isFull = starValue <= fullStars; // Full star if within full count
      const isHalf = !isFull && hasHalfStar && starValue === fullStars + 1; // Half star for next one if decimal ≥ 0.5
      const fillPercentage = isFull ? 100 : isHalf ? 50 : 0;

      return (
        <div
          key={index}
          className="inline-block relative"
          style={{ width: "18px", height: "18px" }}
        >
          {/* Empty star background */}
          <img
            src={star}
            alt="Rating"
            width="18px"
            height="18px"
            className="absolute top-0 left-0 fill-gray-200 text-gray-200"
            style={{ filter: "grayscale(100%)" }}
          />
          {/* Filled star portion */}
          {fillPercentage > 0 && (
            <img
              src={star}
              alt="Rating"
              width="18px"
              height="18px"
              className="absolute top-0 left-0 fill-yellow-400 text-yellow-400"
              style={{
                filter: "grayscale(0)",
                clipPath: `inset(0 ${100 - fillPercentage}% 0 0)`,
              }}
            />
          )}
        </div>
      );
    });
  };
  return !isMobile ? (
    <div className={`bg-white rounded-lg shadow-sm pt-2 mt-2 ${className} ${styles.jobHistoryCard}`}>
      <div className={styles.cardContainer}>
        {(applicantsNumber === 1 || (!isRecurring && !isTutoring)) && jobStatus !== c.jobStatus.CANCELLED ? (
          <div className={styles.profileSection}>
            <div className={styles.avatarContainer}>

              <img
                src={avatarUrl}
                alt="User Avatar"
                className={`border-circle object-cover ${styles.avatarImage}`}
              />



            </div>
            <div className={styles.profileDetails}>
              <h2 className={`font-bold flex-nowrap p-0 m-0 ${styles.nameText}`}>{name}</h2>
              <span className={`flex align-items-center gap-2 ${styles.jobInfo}`}>
                {label} - {jobTitle}
              </span>
              <p className={`m-0 p-0 ${styles.viewLink}`} onClick={viewJobHistroy}>
                View Job Summary
              </p>
            </div>
          </div>
        ) : (
          <div className={styles.profileSection}>
            <div>
              {jobStatus !== c.jobStatus.CANCELLED ? (
                <p className={`m-0 p-0 ${styles.awardedText}`}>
                  Awarded to {applicantsNumber} {applicantsNumber > 1 ? "applicants" : "applicant"}
                </p>
              ) : (
                <p ></p>
              )}
            </div>
            <div className={styles.profileDetails}>
              <span className={`flex align-items-center gap-2 ${styles.jobInfo}`}>
                {label} - {jobTitle}
              </span>
              <p className={`m-0 p-0 ${styles.viewLink}`} onClick={viewJobHistroy}>
                View Job Summary
              </p>
            </div>
          </div>
        )}

        <div className={styles.detailsSection}>
          <div className={styles.timeDateContainer}>
            <div className={styles.timeBox}>
              <img alt="Clock" src={clock} className={styles.icon} />
              <span className={`font-semibold ${styles.timeText}`}>{time}</span>
            </div>
            <div className={styles.dateBox}>
              <img alt="Calendar" src={calendar} className={styles.icon} />
              <span className={`font-semibold ${styles.dateText}`}>{date}</span>
            </div>
          </div>
          <div className={styles.addressBox}>
            <img alt="Home" src={home} className={styles.icon} />
            <span className={`font-semibold ${styles.addressText}`}>{address}</span>
          </div>
        </div>

        <div className={styles.divider}></div>

        <div className={styles.actionsSection}>
          {ratingProvided == false ? (
            <div className={styles.ratingContainer}>
              {jobStatus === c.jobStatus.CANCELLED ? (
                <div className={styles.cancelledSection}>
                  <h1 className={`p-0 m-0 font-semibold ${styles.cancelledText}`}>Cancelled Job</h1>
                  <button className={styles.duplicateButton} onClick={duplicateJob}>
                    Duplicate Job
                  </button>
                </div>
              ) : (
                <div className={styles.buttonGroup}>
                  {!isRecurring && !isTutoring && (
                    <button className={styles.rateButton} onClick={onClick}>
                      Rate {name}
                    </button>
                  )}
                  <button className={styles.duplicateButton} onClick={duplicateJob}>
                    Duplicate Job
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className={styles.ratingContainer}>
              {!isRecurring && !isTutoring && (
                <div className={styles.ratingDisplay}>
                  <span className={styles.ratingLabel}>Rating</span>
                  {renderStars()}
                </div>
              )}
              <button className={styles.duplicateButton} onClick={duplicateJob}>
                Duplicate Job
              </button>
            </div>
          )}
          {clientType === "0" ? (
            <button className={styles.chatButton} onClick={onClickChat}>
              <span className={styles.chatText}>Chat</span>
            </button>
          ) : null}
        </div>
      </div>
    </div>
  ) : (
    <div
      // onClick={viewJobHistroy}
      className={`bg-white rounded-lg shadow-sm pt-2 mt-2 ${className}`}
      style={{
        width: "100%",
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0px 0px 3px 0px #00000040",

      }}
    >
      <div className="flex justify-content-around pt-2 pb-3 px-2 flex-column gap-2">
        {(applicantsNumber === 1 || (!isRecurring && !isTutoring)) && jobStatus !== c.jobStatus.CANCELLED ? (
          <div className="flex align-items-flex-start gap-2">
            <div className="relative">

              <img
                src={avatarUrl}
                alt="User Avatar"
                className="border-circle object-cover"
                style={{ width: "66px", height: "61px" }}
              />



            </div>
            <div className="flex flex-column">
              <h2
                className="font-bold flex-nowrap p-0 m-0"
                style={{ fontSize: "16px", color: "#585858" }}
              >
                {name}
              </h2>
              <span
                className="flex align-items-center gap-1"
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "700",
                }}
              >
               {label} - {jobTitle}
              </span>
              <p
                style={{
                  fontSize: "14px",
                  color: "#FFA500",
                  fontWeight: "600",
                  textDecoration: "underline",
                  cursor: "pointer",
                }}
                className="m-0 p-0"
                onClick={viewJobHistroy}
              >
                View Job Summary
              </p>
            </div>
          </div>
        ) : (
          <div className="flex align-items-flex-start gap-2">
            <div className="flex align-items-center justify-content-center ">
              {jobStatus !== c.jobStatus.CANCELLED ? (
                <p
                  style={{
                    fontSize: "8px",
                    width: "56px",
                    fontWeight: "600",
                    color: "#585858",
                  }}
                  className="m-0 p-0"
                >
                  Awarded to {applicantsNumber}{" "}
                  {applicantsNumber > 1 ? "applicants" : "applicant"}
                </p>
              ) : (
                <p
                >
                
                </p>
              )}
            </div>
            <div className="flex flex-column">
              <span
                className="flex align-items-center gap-1"
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "700",
                }}
              >
                {label} - {jobTitle}
              </span>
              <p
                style={{
                  fontSize: "14px",
                  color: "#FFA500",
                  fontWeight: "600",
                  textDecoration: "underline",
                  cursor: "pointer",
                }}
                className="m-0 p-0"
                onClick={viewJobHistroy}
              >
                View Job Summary
              </p>
            </div>
          </div>
        )}

        <div className="">
          <div className="flex gap-2">
            <div className="flex align-items-center gap-2">
              <div
                className="flex pl-2 align-items-center gap-2"
                style={{
                  border: "1px solid #DFDFDF",
                  borderRadius: "10px",
                  padding: "5px",
                }}
              >
                <img
                  alt="Clock"
                  src={clock}
                  style={{
                    width: "10.4px",
                    height: "10.4px",
                    color: "#585858",
                  }}
                />
                <span
                  className="font-semibold"
                  style={{ fontSize: "12px", color: "#585858" }}
                >
                  {time}
                </span>
              </div>
            </div>
            <div className="flex align-items-center gap-2">
              <div
                className="rounded-lg flex align-items-center gap-2"
                style={{
                  border: "1px solid #DFDFDF",
                  borderRadius: "10px",
                  padding: "5px",
                }}
              >
                <img
                  alt="Calendar"
                  src={calendar}
                  style={{
                    width: "10.4px",
                    height: "10.4px",
                    color: "#585858",
                  }}
                />
                <span
                  className="font-semibold"
                  style={{ fontSize: "12px", color: "#585858" }}
                >
                  {date}
                </span>
              </div>
            </div>
          </div>
          <div className="flex align-items-center gap-2 mt-2">
            <div
              className="rounded-lg flex align-items-center gap-2"
              style={{
                border: "1px solid #DFDFDF",
                borderRadius: "10px",
                padding: "5px",
              }}
            >
              <img
                alt="Home"
                src={home}
                style={{ width: "10.4px", height: "10.4px", color: "#585858" }}
              />
              <span
                className="font-semibold"
                style={{ fontSize: "12px", color: "#585858" }}
              >
                {address}
              </span>
            </div>
          </div>
        </div>

        <div style={{ width: "2px", backgroundColor: "#DFDFDF", height: "auto" }}></div>

        <div className="flex flex-column gap-2">
          {ratingProvided == false ? (
            <div className="flex flex-column align-items-start gap-2">
              {jobStatus === c.jobStatus.CANCELLED ? (
                <div className="flex flex-column gap-2 w-full">
                  <h1
                    className="p-0 m-0 font-semibold"
                    style={{
                      color: "#585858",
                      fontSize: "14px",
                      textDecoration: "underline",
                    }}
                  >
                    Cancelled Job
                  </h1>
                  {/* <button
                    style={{
                      border: "none",
                      color: "#fff",
                      cursor: "pointer",
                      fontSize: "10px",
                      fontWeight: "700",
                      padding: "6px",
                      borderRadius: "20px",
                      backgroundColor: "#179D52",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width:"max-content",
                      paddingInline:"43px"
                    }}
                    onClick={duplicateJob}
                  >
                    Duplicate Job
                  </button> */}
                </div>
              ) : (
                <div className="flex flex-column gap-2 w-full align-items-center">
                  {!isRecurring && !isTutoring && (
                    <button
                      style={{
                        border: "none",
                        color: "white",
                        cursor: "pointer",
                        fontSize: "10px",
                        fontWeight: "700",
                        padding: "6px",
                        borderRadius: "20px",
                        backgroundColor: "#ffa500",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: "10px",
                                           width:"max-content",
                  paddingInline:"50px"
                      }}
                      onClick={onClick}
                    >
                      {/* Rate {name} */}
                      Rate Helper
                    </button>
                  )}
                  {/* <button
                    style={{
                      border: "none",
                      color: "#fff",
                      cursor: "pointer",
                      fontSize: "10px",
                      fontWeight: "700",
                      padding: "6px",
                      borderRadius: "20px",
                      backgroundColor: "#179D52",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width:"max-content",
                      paddingInline:"43px"
                    }}
                    onClick={duplicateJob}
                  >
                    Duplicate Job
                  </button> */}
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-column gap-2">
              {!isRecurring && !isTutoring && (
                <div
                  className="flex align-items-center gap-1"
                  style={{}}
                >
                  <span
                    className=" mr-1"
                    style={{ fontSize: "14px", color: "#585858", fontWeight: "600",marginTop:"3px"}}
                  >
                    You Rated:
                  </span>
                  {renderStarsMobile()}
                </div>
              )}
              {/* <button
                style={{
                  border: "none",
                  color: "#fff",
                  cursor: "pointer",
                  fontSize: "10px",
                  fontWeight: "700",
                  padding: "6px",
                  borderRadius: "20px",
                  backgroundColor: "#179D52",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                   width:"max-content",
                  paddingInline:"43px"
                }}
                onClick={duplicateJob}
              >
                Duplicate Job
              </button> */}
            </div>
          )}
          {clientType === "0" ? (
            <button
              style={{
                padding: "10px 20px",
                border: "1px solid black",
                color: "white",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "800",
                height: "39px",
                borderRadius: "8px",
                width: "100px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "10px",
                boxShadow: "0px 4px 4px 0px #00000040",
                
              }}
              onClick={onClickChat}
            >
              <span style={{ color: "black" }}>Chat</span>
            </button>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default JobHistoryCard;