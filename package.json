{"name": "juggle-street", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "dev:preproduction": "vite --mode preproduction", "build": "vite build", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "build:preproduction": "vite build --mode preproduction", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production"}, "dependencies": {"@microsoft/applicationinsights-react-js": "^17.3.5", "@microsoft/applicationinsights-web": "^2.8.18", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "^2.2.7", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.8.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.8", "crisp-sdk-web": "^1.0.25", "dotenv": "^16.4.5", "jquery": "^3.7.1", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "^10.6.3", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-image-crop": "^11.0.7", "react-redux": "^9.1.2", "react-router-dom": "^6.22.3", "underscore": "^1.13.6"}, "devDependencies": {"@types/node": "^20.12.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-image-crop": "^8.1.6", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0"}}