<!DOCTYPE html>
<html lang="en">
  <head>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet" />
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/js-logo.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover"
    />
    <title>Juggle Street</title>
    <script src="//fast.wistia.com/assets/external/api.js" async></script>
    <link rel="stylesheet" href="//fast.wistia.com/assets/external/uploader.css" />

    <style>
      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }

      #root {
        width: 100%;
        min-height: 100vh;
        transition: min-height 0.3s ease-in-out;
      }
    </style>

    <script>
      function adjustHeight() {
        const root = document.getElementById("root");
        if (window.visualViewport) {
          root.style.minHeight = window.visualViewport.height + "px";
        } else {
          root.style.minHeight = window.innerHeight + "px";
        }
      }

      window.addEventListener("resize", adjustHeight);
      window.addEventListener("DOMContentLoaded", adjustHeight);
    </script>

    <script>
      (function () {
        const url = new URL(window.location.href);
        if (url.pathname === "/signup") {
          const ac = url.searchParams.get("ac");
          if (ac) {
            window.location.replace(`/#/join-now?ac=${encodeURIComponent(ac)}`);
          } else {
            window.location.replace("/#/join-now");
          }
        }
      })();
    </script>
  </head>
  <body>
    <style>
      html {
        --safe-area-top: env(safe-area-inset-top);
        --safe-area-bottom: env(safe-area-inset-bottom);
        --safe-area-left: env(safe-area-inset-left);
        --safe-area-right: env(safe-area-inset-right);
      }
    </style>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
