.CandidateMatchingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  padding-bottom: 0px;
  user-select: none;
}

.CandidateMatchingMainContent {
  width: 100%;
  max-width: 800px;
}
.CandidateMatchingMainContentMobile {
  width: 100%;
 
}

.CandidateMatchingSection {
  text-align: center;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.CandidateMatchingTitle {
  font-size: 60px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #585858;
  line-height: 90px;
}

.CandidateMatchingDescription {
  font-size: 18px;
  color: #585858;
  margin-bottom: 30px;
  font-weight: 500;
  line-height: 27px;
  margin-top: 0px;
  width: 82%;
}

.CandidateMatchingButton {
  padding: 10px 20px;
  width: 306px;
  height: 46px;
  color: #fff;
  background-color: #ffa500;
  border: none;
  border-radius: 10px;
  font-weight: 700;
  border-radius: 10px;
  cursor: pointer;
}

.CandidateMatchingButton:hover {
  background-color: #ff8c00;
}

.CandidateMatchingFooter {
  margin-top: 5px;
  display: flex;
  justify-content: center; 
}
.CandidateMatchingFooterImage {
  max-width: 100%;
  height: 100%;
}

.dialogContent {
  padding: 25px;
  max-width: 567px; 
  width: 100%; 
  margin: 0 auto;
  box-sizing: border-box;
  user-select: none;
}

.dialogHead {
  font-size: 32px;
  font-weight: 700;
  line-height: 48px;
  margin-bottom: 10px;
  color: #585858;
  margin-top: 0px;
}
.dialogInstruct {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
}
.dialogDivider {
  width: 100%;
  max-width: 509px;
}


.criteriaSection {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 40px;
}

.criteriaItem {
  flex: 1 1 calc(50% - 10px); 
  border-radius: 4px;
}


.dialogFooter {
  display: flex;
  justify-content: space-between;
}

.goBackButton,
.nextButton {
  padding: 10px 0px;
  font-size: 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.goBackButton {
  background-color: transparent;
  color: #585858;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nextButton {
  background-color: #ffa500;
  width: 226PX;
  height: 41px;
  color: white;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  border-radius: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .criteriaItem {
    flex: 1 1 100%; /* Stack items on smaller screens */
  }

  .dialogFooter {
    flex-direction: column;
    gap: 10px;
  }

  .goBackButton,
  .nextButton {
    width: 100%;
  }
}
.criteriaTag {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: #585858;
  margin-bottom: 13px;
}
.radioOptions {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-top: 8px;
  margin-left: 10px;
  margin-bottom: 8px;
}

.radioOption {
  display: flex;
  
  gap: 10px;
}
.radioOption div div[class="p-radiobutton-box"]
{
  background: #ffffff !important;
  border-color: #179D52;
}
.radioOption div div[class="p-radiobutton-icon"]
{
  width: 16px!important;
  height: 16px!important;
  transition-duration: 0.2s!important;
  background-color: #179D52 !important;
}
/* Unselected state - white */
/* Unselected state - white */
.checkboxOption div div .p-checkbox-box[data-p-highlight="false"] {
  border-color: #179D52 !important;
  background: #FFFFFF !important; /* White background for unselected */
}

/* Selected state - green */
.checkboxOption div .p-checkbox-box[data-p-highlight="true"] {
  border-color: #179D52 !important;
  background: #179D52 !important; /* Green background for selected */
}

.radioLabel {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #585858;
}

.checkboxGroup {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
    color: #585858;
    line-height: 24px;
  }
  
  .checkboxPair {
    display: flex;
    gap: 176px;
  }
  .checkboxPairSec{
    display: flex;
    gap: 166px;
  }
  
  .checkboxOption {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .checkboxLabel {
    font-size: 1rem;
    color: #666;
  }
  .checkboxPairThird{
    display: flex;
    flex-direction: column;
    gap: 10px;

  }
  .nextButton:disabled {
    background-color: #e0e0e0; /* Light gray background for disabled state */
    color: #a0a0a0; /* Gray text color */
    cursor: not-allowed; /* Change the cursor to indicate the button is disabled */
  }
  
  .nextButton:disabled:hover {
    background-color: #e0e0e0; /* No hover effect when disabled */
  }

@media (max-width: 768px) {
  .CandidateMatchingTitle {
    font-size: 1.5rem;
  }
  .CandidateMatchingDescription {
    font-size: 1rem;
  }
  .CandidateMatchingButton {
    font-size: 0.9rem;
  }
}
.checkboxInput {
  /* Hide the default checkbox */
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #585858; /* Default border color */
  border-radius: 3px;
  cursor: pointer;
  position: relative;
  background-color: white; /* Default background color */
  transition: all 0.3s ease; /* Smooth transition */
}

.checkboxInput:checked {
  background-color: #179D52; /* Green background when checked */
  border-color: #179D52; /* Border matches background */
}

.checkboxInput:checked::after {
  content: "✓"; /* Checkmark character */
  color: white; /* White checkmark color */
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Center the checkmark */
}

.checkboxLabel {
  cursor: pointer;
  margin-left: 8px; /* Space between checkbox and label */
  font-size: 16px; /* Adjust font size */
  font-weight: 500;
}

.radioInput {
  appearance: none;
  accent-color: unset !important;
  -webkit-appearance: none;
  width: 18px !important;
  height: 18px !important;
  border: 1px solid #585858; /* Gray border when not selected */
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.radioInput:checked {
  border: 1px solid #179D52;
  border-color: #179D52; /* Green border when selected */
  background-color:#FFFFFF;
}

.radioInput:checked::after {
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  background-color: #179D52; /* Inner white dot when selected */
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.radioLabel {
  cursor: pointer;
  font-size: 16px;
}
.buttonContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px; /* Adjust the gap between buttons as needed */
}

.goBackButton {
  background-color: transparent;
  color: #585858;
  display: flex;
  align-items: center;
  justify-content: center;
}

  .errorMessageCriteria {
    color: #ffffff;
    text-align: end;
    background-color: rgba(255, 99, 89, 1);
    width: max-content;
    border-radius: 10px;
    padding: 3px;
    padding-inline: 5px;
    position: relative;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
  }
  
  .errorMessageCriteria::before {
    content: "✔";
    margin-right: 5px; 
    color: #ffffff;
    font-size: 1em;
  }
  .CandidateMatchingContainerMobile {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    user-select: none;
    flex-grow: 1;
    overflow: hidden;
    overflow-y: scroll;
  }
  .checkboxGroupMobile {
    display: flex;
    flex-direction: column; /* Stack rows vertically */
    gap: 10px;
    margin-block: 8px;
  }
  
  .checkboxPairMobile,
  .checkboxPairSecMobile {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
  }
  
  .dialogContentMobile {
    /* max-width: 567px;  */
    width: 100%; 
    margin: 0 auto;
    box-sizing: border-box;
    user-select: none;
    padding: 20px;
  }
  .dialogHeadMobile {
    font-size: 24px;
    font-weight: 700;
    line-height: 48px;
    margin-bottom: 10px;
    color: #585858;
    margin-top: 0px;
  }
  .checkboxGroupMobile {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .checkboxPairFirstMobile,
  .checkboxPairSecondMobile {
    display: flex;
    gap: 10px;
    justify-content: flex-start; /* Center buttons */
  }
  .footerButtonArrow {
    width: min-content;
    position: fixed;
    top: 32px;
    left: 35px;
    width: 35px;
    height: 25px;
  }
  


