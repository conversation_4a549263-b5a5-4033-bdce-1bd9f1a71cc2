*,
*::before,
*::after {
  box-sizing: border-box;
}

.originalRow {
  color: #ff6359!important;
  text-decoration: line-through !important;
  opacity: 0.7;

}

.originalRow .column {
  color: inherit;
  text-decoration: inherit;
}
.originalRow {
  transition: all 0.3s ease-in-out;
}

.adjustedTimesheetBlock {
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 16px;

}

.adjustedHeader {
  display: flex;
  align-items: center;
  gap: 6px;
}

.adjustedTitle {
  color: #ff6359;
  font-weight: 600;
  font-size: 14px;
}

.adjustedIcon {
  color: #ff6359;
  border: 2px solid #ff6359;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  cursor: help;
}

.adjustedDescription {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

/* *************************************** */

.headerWrapper {
  width: 100%;
  padding: 5px 16px 100px;
  border-radius: 20px 20px 20px 20px;
  background-color: #179d52;
  min-height: 290px;
}

.backBtn {
  background-color: transparent;
  border: none;
  color: #fff;
  padding: 30px 16px 100px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.arrowCircle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  color: #fff;
  font-size: 10px;
}

.card {
  width: 98%;
  /* min-height: 502px; */
  background-color: #fff;
  border-radius: 20px;
  color: #333;
  margin: -220px auto 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  overflow: hidden;
  overflow-y: auto;
}

.headerSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 1px;
  padding: 2px;
}

.title {
  font-weight: 700;
  font-size: 22px;
  margin: 0;
  color: #585858;
}

.status {
  font-size: 14px;
  margin-top: 4px;
}

.statusHighlight {
  color: #16a34a;
  text-decoration: underline;
  font-weight: 700;
}

.profileContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.avatar {
  width: 49px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
}

.profileName {
  font-size: 12px;
  font-weight: 600;
  color: #585858;
  text-decoration: underline;
}

.info {
  font-size: 14px;
  margin-bottom: 16px;
}

.infoBlock {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
  padding: 5px;
}

.row {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  font-weight: 400;
}

.rowIcon {
  width: 16px;
  height: 16px;
}

.rowIcon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  color: #585858;
  margin-top: 2px;
}

.rateBlock {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 5px;
}

.rateText {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  font-weight: 400;
}

.indented {
  margin-left: 4px;
  color: #555;
}

.hrFull {
  width: 100%;
  height: 1px;
  background-color: #f0f4f7;
  margin: 8px 0;
  border: none;
}


.timesheetContainer {
  width: 100%;
  padding: 5px;
}

.rowHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-weight: 700;
  color: #179d52;
  font-size: 14px;
  flex-wrap: wrap;
}

.rowData {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
  color: #585858;
  flex-wrap: wrap;
}

.hr {
  border: none;
  height: 1px;
  background-color: #f0f4f7;
  margin: 4px 0;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
  color: #585858;
  flex-wrap: wrap;
}

.column {
  flex: 1;
  text-align: left;
}

.totalAmount {
  font-weight: 700;
}

.footer {
  width: 100%;
  margin-top: 10px;
}

.section {
  width: 100%;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 8px;
  background-color: #f4f4f4;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
  gap: 10px;
}

.editSection {
  border: 1px solid #f0f0f0;
  background-color: #f4f4f4;
}

.sectionText {
  flex: 1;
  padding: 5px;
}

.titlee {
  font-size: 16px;
  font-weight: 700;
  color: #585858;
  text-decoration: underline;
}

.subText {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  font-weight: 300;
}

.submitBtn {
  background-color: #ffa500;
  border: none;
  padding: 8px 18px;
  border-radius: 999px;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  min-width: 138px;
  height: 41px;
}

.editBtn {
  background-color: #fce4ec;
  border: 1px solid #f8b5c5;
  padding: 8px 18px;
  border-radius: 999px;
  color: #ff5359;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  min-width: 138px;
  height: 41px;
}

/* '''''''''''''''''''''''''''''''''''''' */
.confirmedHeader {
  background-color: #179D52;
  padding: 6px;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.inlineContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.head {
  display: flex;
}

.confirmRow {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirmedIconCircle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: white;
  color: #179D52;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.headerConfirmedTitle {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  padding-top: 7px;
}

.confirmedSubtitle {
  font-size: 14px;
  color: #ffffff;
  text-align: center;
}

.checkIcon {
  color: #179D52;
}

.pendingTitle {
  font-weight: 700;
  font-size: 14px;
  text-decoration: underline;
  color: #585858;
  margin: 0 0 4px;
}

.pendingSub {
  font-size: 12px;
  font-weight: 300;
  color: #585858;
  margin: 0 0 12px;
}

.profileContainerConfirm {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  gap: 8px;
}

.avatarWrapper {
  position: relative;
  display: inline-block;
}

.redDot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background-color:#ff6359;
  border-radius: 50%;
  border: 2px solid white; 
}
.profileName1 {
  font-size: 12px;
  font-weight: 700;
  color: #585858;
}

.rowAlign {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.textBlock {
  display: flex;
  flex-direction: column;
  width: 60%;
}

.confirmedBox {
  border: 2px solid #16a34a;
  border-radius: 10px;
  padding: 10px 12px;
  margin-bottom: 10px;
  text-align: left;
  background-color: #f6fff8;
}

.confirmedTitle {
  color: #179D52;
  font-weight: 700;
  font-size: 16px;
  text-decoration: underline;
  margin: 0 0 4px;
}

.confirmedSubText {
  color: #179D52;
  font-size: 12px;
  font-weight: 300;
  margin: 0;
}

.editDisabledBtn {
  background-color: #f0f0f0;
  color: #999;
  padding: 8px 18px;
  border-radius: 999px;
  font-size: 14px;
  font-weight: 700;
  cursor: not-allowed;
  border: none;
  min-width: 138px;
  height: 41px;
}

.nextBtn {
  background-color: #ffa500;
  border: none;
  padding: 10px 20px;
  border-radius: 999px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  width: 100%;
}

.disabledBlock {
  opacity: 0.5;
  pointer-events: none;
  background-color: #f0f0f0 !important;
  border: 1px dashed #ccc;
}

.cardSubmitted {
  border: 2px solid #179D52;
  border-radius: 20px;
}