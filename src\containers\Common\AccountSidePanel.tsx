import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "primereact/button";
import styles from "../Common/styles/account-side-panel.module.css";
import { AppDispatch, RootState } from "../../store";
import { removeSession } from "../../store/slices/sessionInfoSlice";
import { updateAccountAndSettingsActiveTab, updateChatWindowState, updateShowAccountAndSettings } from "../../store/slices/applicationSlice";
import userprofile from "../../assets/images/sample_profile.png";
import aboutMeIcon from "../../assets/images/Icons/about_me.png";
import familyIcon from "../../assets/images/Icons/my_family.png";
import childrenIcon from "../../assets/images/Icons/my_child.png";
import addressIcon from "../../assets/images/Icons/my_addresses.png";
import paymentsIcon from "../../assets/images/Icons/payments.png";
import familyMembershipIcon from "../../assets/images/Icons/family_membership.png";
import settingsIcon from "../../assets/images/Icons/settings.png";
import logoutIcon from "../../assets/images/Icons/logout.png";
import chatIcon from "../../assets/images/Icons/chat.png";
import giftIcon from "../../assets/images/Icons/refer.png";
import defaultGiftIcon from "../../assets/images/Icons/referGrey.png";
import CookiesConstant from "../../helper/cookiesConst";
import utils from "../../components/utils/util";
import {
  MdCardMembership,
  MdChildCare,
  MdDryCleaning,
  MdFeed,
  MdGrading,
  MdLocationCity,
  MdMonetizationOn,
  MdStars,
  MdSupervisedUserCircle,
} from "react-icons/md";
import { FaCar } from "react-icons/fa6";
import { FaHome } from "react-icons/fa";
import { IoCheckmarkSharp, IoEye, IoVideocamSharp } from "react-icons/io5";
import { updateActiveTabIndex } from "../../store/slices/accountSettingSlice";
import c from "../../helper/juggleStreetConstants";
import { TiArrowBack } from "react-icons/ti";
import { IoMdSchool } from "react-icons/io";
interface Tab {
  index: number;
  icon: React.ReactNode;
  text: string;
  content: JSX.Element;
  onClick?: () => void;
}
interface SidePanelProps {}
const ProfileHeader: React.FC<{ sessionInfo: any }> = ({ sessionInfo }) => (
  <div className={styles.header}>
    <div className={`${styles.userProfile} ${styles.profileClosed}`}>
      <img src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? userprofile} alt="Profile" className={styles.profilePhoto} />
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          marginLeft: "10px",
          marginRight: "10px",
        }}
      >
        <p className={styles.profileName}>Hi {sessionInfo.loading ? "" : sessionInfo.data["firstName"]}</p>
      </div>
    </div>
  </div>
);
const TabSection: React.FC<{
  title: string;
  tabs: Tab[];
  offsetIndex?: number;
  activeTabOffset?: number;
}> = ({ title, tabs, offsetIndex = 0, activeTabOffset = 0 }) => {
  const accountSettings = useSelector((state: RootState) => state.accountSetting);
  const dispatch = useDispatch<AppDispatch>();

  return (
    <div className={styles.contentDiv}>
      <div className={styles.accountSidePanelcontentDiv}>
        <p className={styles.myaccounttitle}>{title}</p>
        <div className={styles.verticalTabContainer}>
          <div className={styles.tabs}>
            {tabs.map((tab, index) => (
              <div
                key={index}
                className={`${styles.tabItem} ${accountSettings.activeTabIndex === tab.index ? styles.activeTab : ""} ${
                  accountSettings.activeTabIndex === tab.index ? styles.activeTabColor : ""
                }`}
                onClick={() => {
                  if (title !== "Support") {
                    dispatch(updateAccountAndSettingsActiveTab(tab.index + activeTabOffset));
                    dispatch(updateActiveTabIndex(tab.index));
                  }
                  if (tab.onClick) {
                    tab.onClick();
                  }
                }}
              >
                <div className={styles.spaceDiv} data-index={index === 0 ? "first" : index === tabs.length - 1 ? "last" : ""} />
                <div className={styles.iconContainer}>
                  {typeof tab.icon === "string" ? <i className={tab.icon} style={{ fontSize: "18px" }}></i> : tab.icon}
                </div>
                <span>{tab.text}</span>
              </div>
            ))}
          </div>
        </div>
        <div className={styles.tabContent}>{/* Content can be added here if needed */}</div>
      </div>
    </div>
  );
};

// const ReferralButton: React.FC = () => {
//     const clientType = utils.getCookie(CookiesConstant.clientType);
//     const navigate = useNavigate();
//     const handleReferClick = () => {
//         if (clientType === '0') {
//             navigate('/helper-home/referJob');
//         } else {
//             navigate('/parent-home/referJob');
//         }
//     };
//     const isApproved = (): boolean => {
//         const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
//         if (!sessionInfo.data) {
//             return false;
//         }
//         return sessionInfo.data?.['accountStatus'] === c.accountStatus.APPROVED;
//     };
//     const approved = isApproved();
//     return (
//         <div>
//             {(clientType !== '0' ? true : approved) && (
//                 <Button
//                     label='Get $10 for each friend you refer'
//                     icon={
//                         <>
//                             <img
//                                 src={defaultGiftIcon}
//                                 alt='Gift Icon'
//                                 className={styles.defaultIcon}
//                                 width='14.4px'
//                                 height='14.48px'
//                             />
//                             <img
//                                 src={giftIcon}
//                                 alt='Hover Gift Icon'
//                                 className={styles.hoverIcon}
//                                 width='14.4px'
//                                 height='14.48px'
//                             />
//                         </>
//                     }
//                     className={styles.referbtn}
//                     iconPos='left'
//                     onClick={handleReferClick}
//                 />
//             )}
//         </div>
//     );
// };

// Main Component
const AccountSidePanel: React.FC<SidePanelProps> = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isApproved = (): boolean => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    if (!sessionInfo.data) {
      return false;
    }
    return sessionInfo.data?.["accountStatus"] === c.accountStatus.APPROVED;
  };
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const jugglestreetProfileTabs: Tab[] = [
    ...(sessionInfo.data?.["accountStatus"] === c.accountStatus.APPROVED
      ? [
          {
            index: 21,
            icon: <IoCheckmarkSharp width="14.4px" height="14.47px" />,
            text: "Profile Strength",
            content: <p>Manage your super helper settings here.</p>,
          },
        ]
      : []),
    {
      index: 0,
      icon: <img src={aboutMeIcon} alt="About Me" width="14.47px" height="14.4px" />,
      text: "About Me",
      content: <p>Find childcare assistance here.</p>,
    },
    {
      index: 10,
      icon: <IoVideocamSharp style={{ width: "14.47px", height: "14.4px" }} color="#6D6D6D" />,
      text: "Profile Video",
      content: <p>Manage odd jobs and services here.</p>,
    },

    ...(sessionInfo.data?.["accountStatus"] === c.accountStatus.APPROVED
      ? [
          {
            index: 18,
            icon: <TiArrowBack style={{ width: "17px", height: "19px" }} color="#6D6D6D" />,
            text: "Response Start",
            content: <p>Access tutoring services here.</p>,
          },
          {
            index: 20,
            icon: <img src={familyIcon} alt="View Profile" width="14.47px" height="14.4px" />,
            text: "View Profile",
            content: <p>Find Au Pair services here.</p>,
          },
        ]
      : []),
  ];

  const helpermyAccountTabs: Tab[] = [
    ...(sessionInfo.data?.["provider"]["isSuperProvider"] === true
      ? [
          {
            index: 22,
            icon: <MdStars width="14.4px" height="14.47px" />,
            text: "Super Helper Setting",
            content: <p>Manage your super helper settings here.</p>,
          },
        ]
      : []),
    {
      index: 11,
      icon: <MdChildCare width="14.4px" height="14.47px" />,
      text: "Childcare",
      content: <p>Find childcare assistance here.</p>,
    },
    {
      index: 12,
      icon: <MdDryCleaning style={{ width: "14.4px", height: "14.47px" }} color="#6D6D6D" />,
      text: "Odd Jobs",
      content: <p>Manage odd jobs and services here.</p>,
    },
    {
      index: 23,
      icon: <IoMdSchool style={{ width: "14.4px", height: "14.47px" }} color="#6D6D6D" />,
      text: "Tutoring",
      content: <p>Access tutoring services here.</p>,
    },
    {
      index: 13,
      icon: <MdSupervisedUserCircle width="14.4px" height="14.47px" color="#6D6D6D" />,
      text: "Au Pair",
      content: <p>Find Au Pair services here.</p>,
    },
    {
      index: 15,
      icon: <MdLocationCity width="14.4px" height="14.47px" color="#6D6D6D" />,
      text: "Citizenship",
      content: <p>Manage your citizenship-related information here.</p>,
    },
    {
      index: 19,
      icon: <MdFeed width="14.4px" height="14.47px" color="#6D6D6D" />,
      text: "Certificates",
      content: <p>Upload or manage your certificates here.</p>,
    },
    {
      index: 17,
      icon: <MdGrading width="14.4px" height="14.47px" color="#6D6D6D" />,
      text: "References",
      content: <p>View and manage your references here.</p>,
    },
    {
      index: 16,
      icon: <FaCar width="14.4px" height="14.47px" />,
      text: "Driving & Language",
      content: <p>Manage driving and language skills here.</p>,
    },
  ];

  const myAccountTabs: Tab[] = [
    {
      index: 0,
      icon: <img src={aboutMeIcon} alt="About Me" width="14.4px" height="14.47px" />,
      text: "About me",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
    },
    {
      index: 1,
      icon: <img src={familyIcon} alt="My Family" width="14.4px" height="12.92px" />,
      text: "My Family",
      content: <p>You can chat with your helper here.</p>,
    },
    {
      index: 2,
      icon: <img src={childrenIcon} alt="My Children" width="16.5px" height="15.82px" />,
      text: "My Children",
      content: <p>Here you can rate your helper after the service.</p>,
    },
    {
      index: 3,
      icon: <img src={addressIcon} alt="My Addresses" width="14.32px" height="14.32px" />,
      text: "My Addresses",
      content: <p>Here you can rate your helper after the service.</p>,
    },
  ];

  const billingTabs: Tab[] =
    clientType === 0
      ? [
          {
            index: 25,
            icon: <MdMonetizationOn width="14.4px" height="14.47px" color="#6D6D6D" />,
            text: "Payment Methods",
            content: <p>Here you can discover helpers nearby for your needs.</p>,
          },
        ]
      : [
          {
            index: 5,
            icon: <img src={paymentsIcon} alt="Payments" width="13.5px" height="15.07px" />,
            text: "Payments",
            content: <p>Here you can discover helpers nearby for your needs.</p>,
          },
          {
            index: 6,
            icon: <img src={familyMembershipIcon} alt="Family Membership" width="9.33px" height="16.07px" />,
            text: "Family Membership",
            content: <p>You can chat with your helper here.</p>,
          },
        ];

  const settingsTabs: Tab[] =
    clientType === 0
      ? [
          {
            index: 3,
            icon: <FaHome width="16px" height="16px" />,
            text: "My Addresses",
            content: <p>Manage your saved addresses here.</p>,
          },
          {
            index: 24,
            icon: <MdCardMembership width="16px" height="16px" />,
            text: "Membership",
            content: <p>View and manage your membership details.</p>,
          },
          {
            index: 14,
            icon: <IoEye width="16px" height="16px" />,
            text: "Privacy Settings",
            content: <p>Adjust your privacy preferences here.</p>,
          },
          {
            index: 7,
            icon: <img src={settingsIcon} alt="General Settings" width="16px" height="16.08px" />,
            text: "General Settings",
            content: <p>Here you can discover helpers nearby for your needs.</p>,
          },
          {
            index: 27,
            icon: <img src={logoutIcon} alt="Log out" width="16px" height="16.08px" />,
            text: "Log out",
            content: <p>Logging out...</p>,
            onClick: () => {
              try {
                dispatch(removeSession());
                dispatch(updateShowAccountAndSettings(false));
                utils.obliterateEverything();
              } catch (error) {
                console.error("Logout failed", error);
              } finally {
                navigate("/");
              }
            },
          },
        ]
      : [
          {
            index: 7,
            icon: <img src={settingsIcon} alt="General Settings" width="16px" height="16.08px" />,
            text: "General Settings",
            content: <p>Here you can discover helpers nearby for your needs.</p>,
          },

          {
            index: 27,
            icon: <img src={logoutIcon} alt="Log out" width="16px" height="16.08px" />,
            text: "Log out",
            content: <p>Logging out...</p>,
            onClick: () => {
              try {
                dispatch(removeSession());
                dispatch(updateShowAccountAndSettings(false));
                utils.obliterateEverything();
              } catch (error) {
                console.error("Logout failed", error);
              } finally {
                navigate("/");
              }
            },
          },
        ];

  const supportTabs: Tab[] = [
    {
      index: 28,
      icon: <img src={chatIcon} alt="Contact Customer Service" width="16px" height="16.08px" />,
      text: "Contact Customer Service",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
      onClick: () => {
        dispatch(updateChatWindowState(true));
      },
    },
  ];

  const ReferTab: Tab[] = [
    ...(clientType !== 0 || isApproved()
      ? [
          {
            index: 26, // Unique index for referral tab
            icon: (
              <div className={styles.referButtonWrapper}>
                <img src={defaultGiftIcon} alt="Gift Icon" className={styles.defaultIcon} width="14.4px" height="14.48px" />
                <img src={giftIcon} alt="Hover Gift Icon" className={styles.hoverIcon} width="14.4px" height="14.48px" />
              </div>
            ),
            text: "Get $10 for each friend you refer", // Full label as text
            content: <p>Get $10 for each friend you refer.</p>,
          },
        ]
      : []),
  ];

  return (
    <div className={styles.accountSidePanel}>
      <ProfileHeader sessionInfo={sessionInfo} />
      {clientType === 0 && <TabSection title="Jugglestreet Profile" tabs={jugglestreetProfileTabs} offsetIndex={0} activeTabOffset={0} />}
      <div className={styles.content}>
        {clientType === 0 ? (
          <TabSection title="My Account" tabs={helpermyAccountTabs} offsetIndex={0} activeTabOffset={0} />
        ) : (
          <TabSection title="My Account" tabs={myAccountTabs} offsetIndex={0} activeTabOffset={0} />
        )}
        <TabSection title="Billing" tabs={billingTabs} offsetIndex={0} activeTabOffset={0} />
        <TabSection title="Settings" tabs={settingsTabs} offsetIndex={0} activeTabOffset={0} />
        <TabSection title="Support" tabs={supportTabs} offsetIndex={0} activeTabOffset={0} />
        <TabSection title="Refer" tabs={ReferTab} offsetIndex={0} activeTabOffset={0} />

        {/* <ReferralButton /> */}
      </div>
    </div>
  );
};

export default AccountSidePanel;
