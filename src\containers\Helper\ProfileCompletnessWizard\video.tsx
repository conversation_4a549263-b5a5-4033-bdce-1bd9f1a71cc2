import React, { useState, useRef, useEffect } from 'react';

export interface UploadedFile {
    id: number;
    name: string;
    type: string;
    archived: boolean;
    created: string;
    updated: string;
    duration: number;
    hashed_id: string;
    description: string;
    progress: number;
    status: string;
    thumbnail: Thumbnail;
    account_id: number;
}

interface Thumbnail {
    url: string;
    width: number;
    height: number;
}

const CustomWistiaUploader = ({
    onUploadStateChange,
    onUploadSuccess,
    onUploadProgress,
}: {
    onUploadStateChange: (state: boolean) => void;
    onUploadSuccess: (data: UploadedFile) => void;
    onUploadProgress: (progress: number) => void;
}) => {
    const [isUploading, setIsUploading] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [fileChosen, setFileChosen] = useState(false);
    const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);

    useEffect(() => {
        onUploadStateChange(isUploading);
    }, [isUploading]);

    const handleFileUpload = async (file: File) => {
        const accessToken = 'b95fc6a5bb75e12e61bf9574b068b795b807ff4b692692ed9ebec580c9362042';
        const projectId = 'nq5sn3fza0';

        try {
            setIsUploading(true);
            

            // Create form data
            const formData = new FormData();
            formData.append('file', file);
            formData.append('project_id', projectId);

            // Make direct request to Wistia upload endpoint
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://upload.wistia.com', true);
            xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);

            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const progress = Math.round((event.loaded / event.total) * 100);
                    onUploadProgress(progress);
                }
            };

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const result = JSON.parse(xhr.responseText);
                    onUploadSuccess(result);
                    setUploadedVideoUrl(`https://fast.wistia.net/embed/iframe/${result.hashed_id}`);
                } else {
                    console.error('Upload failed:', xhr.statusText);
                }
                setIsUploading(false);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            };

            xhr.onerror = () => {
                console.error('Upload error:', xhr.statusText);
                setIsUploading(false);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            };

            xhr.send(formData);
        } catch (error) {
            console.error('Upload error:', error);
            setIsUploading(false);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setFileChosen(true);
            handleFileUpload(file);
        }
    };

    const uploadingMessageStyle = {
        marginTop: '10px',
        fontWeight: 'bold',
        color: '#FFA500',
        animation: 'loading 1s infinite',
    };

    const keyframesStyle = `
        @keyframes loading {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    `;
    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
      };
    
      const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        const file = e.dataTransfer.files[0];
        if (file) {
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          const mockEvent = {
            target: {
              files: dataTransfer.files,
            },
          } as React.ChangeEvent<HTMLInputElement>;
          handleFileSelect(mockEvent);
        }
      };
    
      const handleClick = () => {
        fileInputRef.current?.click();
      };
      
    return (
        // <div className='p-4'>
        //     <style>{keyframesStyle}</style>
        //     {!fileChosen && (
        //         <input
        //             type='file'
        //             onChange={handleFileSelect}
        //             accept='video/*'
        //             disabled={isUploading}
        //             style={{
        //                 padding: '10px',
        //                 border: '1px solid #ccc',
        //                 borderRadius: '4px',
        //                 width: '100%',
        //                 // maxWidth: '300px',
        //                 backgroundColor: '#585858'
        //             }}
        //             className='flex align-item-center'
        //         />
        //     )}

        //     {isUploading && (
        //         <div className="" style={uploadingMessageStyle}>
        //             Uploading... Please wait.
        //         </div>
        //     )}
        //     {uploadedVideoUrl && !isUploading && (
        //         <div style={{ marginTop: '10px', position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden' }}>
        //             <h1 className='m-0 p-0 mb-2 font-semibold' style={{ color: "#585858", fontSize: '20px' }}>Uploaded Video:</h1>
        //             <iframe
        //                 src={uploadedVideoUrl}
        //                 title="Uploaded Video"
        //                 style={{
        //                     position: 'absolute',
        //                     top: 0,
        //                     left: 0,
        //                     width: '100%',
        //                     height: '100%',
        //                 }}
        //                 frameBorder="0"
        //                 allow="autoplay; fullscreen"
        //                 allowFullScreen
        //             ></iframe>
        //         </div>
        //     )}
        // </div>
        <div className="p-4">
        <style>{keyframesStyle}</style>
    {!fileChosen && (
      <div
        className="relative border-2 border-dashed border-gray-300 rounded-lg p-12 text-center cursor-pointer hover:border-gray-400 transition-colors"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        {/* Upload Icon */}
        <div className="flex justify-content-center mb-2 mt-2">
          <svg
            className="w-2 h-2 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
        </div>
        
        <div className="mb-2" style={{color:'#585858'}}>
          Drag and drop a file or
        </div>
        
        <button
          className="bg-gray-600 text-white px-4 py-2 mb-2 hover:bg-gray-700 transition-colors cursor-pointer"
        >
          Upload Video
        </button>
        
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          accept="video/*"
          disabled={isUploading}
          className="hidden"
        />
      </div>
    )}

    {isUploading && (
      <div className="" style={uploadingMessageStyle}>
        Uploading... Please wait.
      </div>
    )}

    {uploadedVideoUrl && !isUploading && (
      <div className="mt-4">
        <h1 className="text-lg font-semibold mb-2" style={{color:'#585858'}}>
          Uploaded Video:
        </h1>
        <div className="relative pt-[56.25%]">
        <iframe
                      src={uploadedVideoUrl}
                     title="Uploaded Video"
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                        }}
                   frameBorder="0"
                         allow="autoplay; fullscreen"
                         allowFullScreen
                 ></iframe>
        </div>
      </div>
    )}
  </div>
    );
};

export default CustomWistiaUploader;
