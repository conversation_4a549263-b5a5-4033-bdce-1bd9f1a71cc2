@import "primeicons/primeicons.css";
:root {
  font-family: "Poppins" !important;
  /* line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; */
}

body {
  margin: 0;
  /* display: flex;
  place-items: center;
  min-width: 320px; */
  min-height: 100vh;
}

@supports (-moz-appearance: none) {
  /* Your Firefox-specific CSS here */
  body {
    scrollbar-width: auto; /* Options: auto, thin */
    scrollbar-color: #ffa500 #d6d6d6;
  }
}

::-webkit-scrollbar {
  border-radius: 20px;
  max-width: 4px;
  max-height: 4px;
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-thumb {
  background-color: #ffa500 !important;
}

::-webkit-scrollbar-track {
  background-color: #d6d6d6;
  width: 4px;
  height: 4px;
}

* {
  outline: none;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

@keyframes CustomSlideUp {
  from {
    opacity: 0%;
    bottom: -100%;
  }
  to {
    opacity: 100%;
    bottom: 0%;
  }
}

.custom_slide_up {
  animation: CustomSlideUp 500ms ease-in-out forwards;
}

.hide_scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide_scrollbar::-webkit-scrollbar {
  display: none;
}

.p-divider {
  border-left-style: solid;
  border-left: 1px #e5e7eb;
}

.p-divider-solid .p-divider-horizontal:before {
  border-left-style: solid;
  border-top: 1px #e5e7eb;
}
