// hooks/useAdjustedTimesheetData.ts
import { useEffect, useState } from "react";
import Service from "../services/services";
import c from "../helper/juggleStreetConstants";

export interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

export const useAdjustedTimesheetData = () => {
  const [adjustedTimesheetData, setAdjustedTimesheetData] = useState<TimesheetEntry[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchAdjustedData = async (timesheetId: number) => {
    try {
      Service.getHelperAdjustedTimesheet(
        (response: any[]) => {
          const mapped: TimesheetEntry[] = response.map((item) => ({
            id: item.id || 0,
            status: statusMap[item.status] || "Awaiting Your Approval",
            type: jobTypeMap[item.jobType] || "Before & After School Care",
            date: new Date(item.jobDate).toLocaleDateString("en-AU", {
              day: "numeric",
              month: "long",
              year: "numeric",
            }),
            location: item.formattedAddress,
            userName: `${item.firstName} ${item.lastName?.charAt(0) || ""}`,
            originalImageUrl: item.originalImageUrl,
          }));
          setAdjustedTimesheetData(mapped);
        },
        (err) => {
          console.error("Error fetching adjusted timesheets:", err);
          setError("Failed to fetch adjusted timesheets");
        },
        timesheetId
      );
    } catch (e) {
      console.error("Unexpected error:", e);
      setError("Unexpected error occurred");
    }
  };

  // useEffect(() => {
  //   fetchAdjustedData();
  // }, []);

  return {
    adjustedTimesheetData,
    error,
    refreshData: fetchAdjustedData,
  };
};
