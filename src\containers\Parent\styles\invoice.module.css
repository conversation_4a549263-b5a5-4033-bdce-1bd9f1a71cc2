.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
  }
  
  .title {

    margin: 0;
    padding: 0;
    font-weight: 700;
    font-size: 16px;
    color: #585858;
  }
  .titleFirst{
    margin-top: 0;
    padding: 0;
    font-weight: 700;
    font-size: 24px;
    color: #585858;
  }
  .noInvoices{
    color: #585858;
  }
  .planContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .planTitle {
    margin: 0;
    padding: 0;
    font-weight: 700;
    font-size: 16px;
    color: #585858;
  }
  
  .subscriptionText {
    margin: 0;
    padding: 0;
    font-weight: 700;
    font-size: 16px;
    color: #179D52;
  }
  
  .priceText {
    margin: 0;
    padding: 0;
    font-weight: 700;
    font-size: 16px;
    color: #585858;
  }
  .subscriptionDate{
    font-size: 16px;
    color: #585858;
    font-weight: 400;
    margin: 0px;
  }
  

  
  .cardTitle {
    margin-bottom: 0;
    padding: 0;
    font-weight: 500;
    font-size: 16px;
    color: #585858;
  }
  
  .cardNumber {
    margin: 0;
    padding: 0;
    font-weight: 400;
    font-size: 16px;
    color: #585858;
  }
  
  .cardNumberContainer {
    display: flex;
    gap: 0.5rem;
    width: 100%;
    margin-top: 0.5rem;
  }
  
  .invoiceContainer {
    margin-top: 1rem;
    width: 100%;
  }
  
  .invoiceHeader {
    font-weight: 700;
    font-size: 16px;
    color: #585858;
    margin-bottom: 0px;
  }
  
  .invoiceDetails {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .invoiceItem {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
  .invoiceList {
    width: 100%;
    margin-top: 10px;
  }
  
  .listHeader {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 1rem 0;
    border-bottom: 1px solid #e5e5e5;
  }
  
  .headerCell {
    color: #BBBBBB;
    font-size: 16px;
    font-weight: 700;
  }
  
  .invoiceRow {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 1rem 0;
    border-bottom: 1px solid #e5e5e5;
    align-items: center;
  }
  
  .cell {
    color: #585858;
    font-size: 16px;
    font-weight: 500;
  }
  
  .statusPill {
    display: inline-flex;
    border-radius: 10px;
    background: var(--Selected-button-input, #179D5233);
    color: #179D52;
    font-size: 14px;
    width: 77px;
    height: 30px;
    border: 2px solid  #179D52 ;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .amount {
    font-weight: 600;
    text-decoration: underline;
  }
  .cancelBtn{
    font-size: 12px;
    font-weight: 700;
    color: #585858;
    text-decoration: underline;
    width: min-content;
    text-wrap: noWrap;
    background-color: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
  }
  .updateBtn{
    width: 147px;
    height: 34px;
    border-radius: 10px;
    background-color: #585858;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    border: none;
    cursor: pointer;
    margin-top: 10px;
  }
  .DialogContent {
    position: relative;
    background-color: #ffff;
    width: 100%;
    border-radius: 33px;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
    padding-inline: 10px;
    overflow-y: auto;
  }
  .mainDiv{
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .CloseBtn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #585858;
  }
  .resonsValue{
    display: flex;
    flex-direction: row;
    gap: 10px;
    font-size: 16px;
    font-weight: 500;
    color: #585858;
  }
  .invoiceTitle{
    font-size: 24px;
    font-weight: 700;
    color: #585858;
    margin-top: 0px;
  }
  .btnContaioner{
    display: flex;
    flex-direction: row;
    gap: 15px;
    margin-top: 25px;
  }
  .cancelBtnSec{
    width: 88px;
    height: 42px;
    background-color: #FFFFFF;
    border-radius: 5px;
    color: #585858;
    font-size: 18px;
    font-weight: 700;
    text-decoration: underline;
    box-shadow: 0px 0px 4px 0px #00000040;
    cursor: pointer;
    border: none;

  }
  .confirmBtn{
    width: 207px;
    height: 42px;
    background-color: #FFA500;
    border-radius: 5px;
    box-shadow: 0px 4px 4px 0px #00000040;
    cursor: pointer;
    border: none;
    font-size: 18px;
    font-weight: 700;
    color: #fff;
    text-decoration: underline;

  }
  .radioOption {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .radioInput {
    appearance: none;
    -webkit-appearance: none;
    width: 18px !important;
    height: 18px !important;
    border: 1px solid #585858;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: border-color 0.3s ease, background-color 0.3s ease;
  }
  
  .radioInput:checked {
    border: 1px solid #179D52;
    border-color: #179D52;
    background-color: #FFFFFF;
  }
  
  .radioInput:checked::after {
    content: "";
    display: block;
    width: 14px;
    height: 14px;
    background-color: #179D52;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .radioLabel {
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #585858;
  }
  .enableBtn {
    background-color: #FFA500;
    width: max-content;
    border-radius: 10px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    text-decoration: underline;
    color: #fff;
  
    padding-inline: 5px;
    padding-block: 3px;


  }
  .inputGroup {
    margin-bottom: 1rem;
    width: 100%;
}

.inputGroup label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.inputRow {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.inputRow .inputGroup {
    flex: 1;
}
  