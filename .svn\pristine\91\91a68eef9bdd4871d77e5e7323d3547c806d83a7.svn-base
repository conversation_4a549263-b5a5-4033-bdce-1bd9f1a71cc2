import { useState } from 'react'
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import References from '../../Parent/AccountSetting/References';
import useLoader from '../../../hooks/LoaderHook';
import CustomButton from '../../../commonComponents/CustomButton';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';

const Referencess = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [showReferences] = useState<boolean>(true);
  const dispatch = useDispatch<AppDispatch>();
  const { disableLoader, enableLoader } = useLoader();
  const [hasChanges, setHasChanges] = useState(false);
  const handlePrev = () => {
    dispatch(decrementProfileActivationStep());
  };

  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  const handleReferenceChange = () => {
    setHasChanges(true);

  };

  const handleNext = async () => {
    enableLoader();
    try {
      // Update session info or perform any necessary actions
      await dispatch(updateSessionInfo({ payload: sessionInfo.data }));
      dispatch(incrementProfileActivationStep());
    } finally {
      disableLoader();
    }
  };

  return (
    <div className={`${styles.utilcontainer} overflow-hidden	`}>
      <ProfileCompletenessHeader
        title="References"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
      />
      <div>
        {showReferences && <References onChange={handleReferenceChange} />}
      </div>

      <footer className={styles.utilfooterContainer}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left"></i>
              Previous
            </>
          }
          onClick={handlePrev}
          style={{
            backgroundColor: 'transparent',
            color: '#585858',
            width: '156px',
            height: '39px',
            fontSize: '14px',
            fontWeight: '500',
          }}
        />
        <div style={{ flexGrow: 1 }} />

        <CustomButton
          className={styles.hoverClass}
          data-skip={hasChanges ? 'false' : 'true'}
          onClick={hasChanges ? handleNext : handleSkip}
          label={
            <>
              {hasChanges ? 'Next' : 'Skip'}
              <i
                className={`pi pi-angle-${hasChanges ? 'right' : 'right'}`}
                style={{ marginLeft: '8px' }}
              ></i>
            </>
          }
          style={
            hasChanges
              ? {
                backgroundColor: '#FFA500',
                color: '#fff',
                width: '156px',
                height: '39px',
                fontWeight: '800',
                fontSize: '14px',
                borderRadius: '8px',
                border: '2px solid transparent',
                boxShadow: '0px 4px 12px #00000',
                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
              }
              : {
                backgroundColor: 'transparent',
                color: '#585858',
                width: '156px',
                height: '39px',
                fontWeight: '400',
                fontSize: '14px',
                borderRadius: '10px',
                border: '1px solid #F0F4F7',
              }
          }
        />
      </footer>
    </div>
  )
}

export default Referencess