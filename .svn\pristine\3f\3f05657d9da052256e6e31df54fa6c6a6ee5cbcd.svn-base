import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useState } from "react";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import { useNavigate } from 'react-router-dom';
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import ProfileImage from "../../../../assets/images/Icons/my_child.png";
import { Badge } from "primereact/badge";

const TimeSheet: React.FC<{
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;

}> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [showCard, setShowCard] = useState(false);
  const navigate = useNavigate();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));

  function generatePDF(): void {
    throw new Error("Function not implemented.");
  }

  const handleGoBack = () => {
    console.log("Go back clicked");
  };

  const handleSubmit = () => {
    alert("Timesheet confirmed!");
  };

  const handleEdit = () => {
    alert("Edit timesheet clicked");
  };

  const isParent = clientType === c.clientType.INDIVIDUAL;

  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };
  const timesheetData = [
    {
      status: "Awaiting Your Approval",
      type: "One Off Job",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Craig S",
    },
  ];

  const HelperAdjusted = [
    {
      status: "Awaiting Your Approval",
      type: "One Off Job",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Craig S",
    },
  ];
  const [selectedEntry, setSelectedEntry] = useState<any | null>(null);
  const handleReview = (entry) => {
    setSelectedEntry(entry);
  };

  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel
        header={
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              fontSize: '10px',
              lineHeight: '1.4',
              position: 'relative',
            }}
          >
            <span
              dangerouslySetInnerHTML={{ __html: headers.awaitingConfirmation }}
              style={{ textAlign: 'center' }}
            />
            {timesheetData.length > 0 && (
              <Badge
                value={timesheetData.length}
                style={{
                  backgroundColor: "#FF6359",
                  minHeight: "5px",
                  minWidth: "15px",
                  fontSize: "10px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              />
            )}
          </div>
        }
      >


        {timesheetData.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry?.status}
            type={entry?.type}
            date={entry?.date}
            location={entry?.location}
            userName={entry?.userName}
            onReview={() => handleReview(entry)}
          />
        ))}

        {selectedEntry && (
          <div className="overlay-popup">
            <div className="slide-up-card">
              <AwaitingConfirmationCard
                profileName={selectedEntry.userName}
                profileImage={ProfileImage}
                jobType={selectedEntry.type}
                jobDate={selectedEntry.date}
                jobAddress={selectedEntry.location}
                baseRate={25}
                extraHoursRate={35}
                initialTimesheetRows={[
                  { start: "6:00am", finish: "9:00am", hours: 3, rate: 30, total: 90 },
                  { start: "3:30pm", finish: "6:30pm", hours: 3, rate: 45, total: 135 }
                ]}
                onSubmit={() => setSelectedEntry(null)}
                onGoBack={() => setSelectedEntry(null)}
              />
            </div>
          </div>
        )}
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        <span dangerouslySetInnerHTML={{ __html: headers.adjustedTimesheets }} />
      </div>
      }
      >
        {HelperAdjusted.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry?.status}
            type={entry?.type}
            date={entry?.date}
            location={entry?.location}
            userName={entry?.userName}
            onReview={() => handleReview(entry)}
          />
        ))}
        {selectedEntry && (
          <div className="overlay-popup">
            <div className="slide-up-card">
              <AwaitingConfirmationCard
                profileName={selectedEntry.userName}
                profileImage={ProfileImage}
                jobType={selectedEntry.type}
                jobDate={selectedEntry.date}
                jobAddress={selectedEntry.location}
                baseRate={25}
                extraHoursRate={35}
                initialTimesheetRows={[
                  { start: "6:00am", finish: "9:00am", hours: 3, rate: 30, total: 90 },
                  { start: "3:30pm", finish: "6:30pm", hours: 3, rate: 45, total: 135 }
                ]}
                onSubmit={() => setSelectedEntry(null)}
                onGoBack={() => setSelectedEntry(null)}
              />
            </div>
          </div>
        )}
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        <span dangerouslySetInnerHTML={{ __html: headers.awaitingApproval }} /> </div>}>
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Awaiting Parent Approval</div>
      </TabPanel>
    </TabView>
  );
};

export default TimeSheet;