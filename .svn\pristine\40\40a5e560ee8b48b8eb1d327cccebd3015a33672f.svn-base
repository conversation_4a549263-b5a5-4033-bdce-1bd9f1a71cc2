import React, { useCallback, useEffect, useRef, useState } from "react";
import styles from "../styles/my-business.module.css";
import { Dropdown } from "primereact/dropdown";
import { useDispatch, useSelector } from "react-redux";

import useLoader from "../../../hooks/LoaderHook";
import {
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import CustomButton from "../../../commonComponents/CustomButton";
import { InputTextarea } from "primereact/inputtextarea";
import { AppDispatch, RootState } from "../../../store";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import { Divider } from "primereact/divider";
import { InputText } from "primereact/inputtext";
import ReactCrop, {
  centerCrop,
  Crop,
  makeAspectCrop,
  PixelCrop,
} from "react-image-crop";
import Userimage from "../../../assets/images/Group_light.png";
import { FaCheck } from "react-icons/fa6";
import useIsMobile from "../../../hooks/useIsMobile";

const MyBusiness: React.FC = () => {
  const { disableLoader, enableLoader } = useLoader();

  // Dropdown State Variables
  const [selectedYear, setSelectedYear] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [selectedEmployeesRange, setSelectedEmployeesRange] = useState("");
  const [selectedGeneral, setSelectedGeneral] = useState("");
  const [businessTradingName, setBusinessTradingName] = useState("");
  const [registeredBusinessName, setRegisteredBusinessName] = useState("");
  const [businessAddress, setBusinessAddress] = useState("");
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const [_, setCroppedImageUrl] = useState<string | null>(null);
  const [isImageUploaded, setIsImageUploaded] = useState(false);
  const { isMobile } = useIsMobile();
  const currentYear = new Date().getFullYear();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();

  const [accountIdentifier, setAccountIdentifier] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const handleNext = () => {
    if (isFormComplete()) {
      const payload = {
        ...(sessionInfo.data as object),
        aboutMe: inputValue,
        numberOfMembers: selectedEmployeesRange,
        establishYear: selectedYear,
        industrySectorId: selectedGeneral,
        industrySectorText: accountIdentifier,
        businessTradingName,
        registeredBusinessName,
        businessAddress,
      };

      enableLoader();
      dispatch(updateUser({ payload })).finally(() => {
        setIsSuccessful(true);
        disableLoader();
        dispatch(incrementProfileActivationStep());
      });
    } else {
      dispatch(incrementProfileActivationStep());
    }
  };

  const yearOptions = Array.from(
    { length: currentYear - 2000 + 1 },
    (_, i) => ({
      label: (2000 + i).toString(),
      value: 2000 + i,
    })
  ).reverse();

  useEffect(() => {
    if (sessionInfo) {
      setSelectedYear(
        sessionInfo.loading ? "" : sessionInfo.data["establishYear"]
      );
      setSelectedEmployeesRange(
        sessionInfo.loading ? "" : sessionInfo.data["numberOfMembers"]
      );
      setAccountIdentifier(
        sessionInfo.loading ? "" : sessionInfo.data["industrySectorText"]
      );
      setSelectedGeneral(
        sessionInfo.loading ? "" : sessionInfo.data["industrySectorId"]
      );
      setInputValue(sessionInfo.loading ? "" : sessionInfo.data["aboutMe"]);
      setBusinessTradingName(
        sessionInfo.loading ? "" : sessionInfo.data["tradingName"]
      );
      setRegisteredBusinessName(
        sessionInfo.loading ? "" : sessionInfo.data["businessName"]
      );
      setBusinessAddress(
        sessionInfo.loading
          ? ""
          : sessionInfo.data["addresses"][0]["formattedAddress"]
      );
    }
  }, [sessionInfo]);

  const industrySectorsList = [
    { label: "Accommodation - Hotels", value: 101 },
    { label: "Accommodation - Property Operators & Agents", value: 102 },
    { label: "Childcare - Preschool Childcare Centre", value: 201 },
    { label: "Childcare - Before & After School Centre", value: 202 },
    { label: "Childcare - School Holiday Provider", value: 203 },
    { label: "Childcare - Online Childcare Platform", value: 204 },
    { label: "Childcare - Service Provider", value: 205 },
    { label: "Health - Health Care Provider", value: 401 },
    { label: "Health - Recreation & Amusement Activities", value: 402 },
    { label: "Health - Sports & Physical Activities", value: 403 },
    { label: "Recruitment - Childcare", value: 501 },
    { label: "Recruitment - Tutoring", value: 502 },
    { label: "Other ", value: 1001 },
  ];

  const numberOfMembersList = [
    { label: "1 - 5", value: 5 },
    { label: "5 - 10", value: 10 },
    { label: "10 - 20", value: 20 },
    { label: "20 - 50", value: 50 },
    { label: "Over 50", value: 100 },
  ];

  //   const handleNext = () => {
  //     if (isFormComplete()) {
  //       enableLoader();
  //       dispatch(incrementProfileActivationStep());
  //       disableLoader();
  //     } else {
  //       dispatch(incrementProfileActivationStep());
  //     }
  //   };
  // Handle input changes
  const handleInputChange = (e) => {
    const value = e.target.value;

    const countWords = (text) => {
      return text
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
    };

    const totalWords = countWords(value);

    if (totalWords <= 120) {
      setInputValue(value.trim().length > 0 ? value : "");
    }
  };

  const isFormComplete = (): boolean => {
    return (
      selectedYear !== null &&
      selectedEmployeesRange !== null &&
      selectedGeneral !== null &&
      inputValue.trim() !== ""
    );
  };

  // const handlePrev = () => {
  //   dispatch(decrementProfileActivationStep());
  // };


  useEffect(() => {
    if (sessionInfo.data?.["defaultImage"]?.["scale1ImageUrl"]) {
      setSelectedImage(sessionInfo.data["defaultImage"]["scale1ImageUrl"]);
    }
  }, [sessionInfo.data]);

  const handleCancel = () => {
    setSelectedImage(null);
    setCroppedImageUrl(null);
    setCompletedCrop(undefined);
    setIsImageUploaded(false);
    setCrop(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        4 / 4,
        width,
        height
      ),
      width,
      height
    )
    setCrop(crop)
  }

  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop) => {
      const canvas = document.createElement("canvas");
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      const pixelCrop = {
        x: Math.round(crop.x * scaleX),
        y: Math.round(crop.y * scaleY),
        width: Math.round(crop.width * scaleX),
        height: Math.round(crop.height * scaleY),
        unit: 'px' as const // Ensure the unit is explicitly set to "px"
      };

      canvas.width = pixelCrop.width;
      canvas.height = pixelCrop.height;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(
          image,
          pixelCrop.x,
          pixelCrop.y,
          pixelCrop.width,
          pixelCrop.height,
          0,
          0,
          pixelCrop.width,
          pixelCrop.height
        );
      }

      return {
        croppedImageUrl: canvas.toDataURL("image/jpeg"),
        x: pixelCrop.x,
        y: pixelCrop.y,
        height: pixelCrop.height,
        width: pixelCrop.width,
        unit: pixelCrop.unit // Include the unit property in the return object
      };
    },
    []
  );

  const handleSavePhoto = useCallback(() => {
    if (imgRef.current && completedCrop) {
      enableLoader();
      const { croppedImageUrl, x, y, height, width, unit } = getCroppedImg(imgRef.current, completedCrop);
      setCroppedImageUrl(croppedImageUrl);
      setSelectedImage(croppedImageUrl); // Set the cropped image as the selected image
      const payload = {
        id: sessionInfo.data["defaultImage"]["id"],
        cropDimensions: { x, y, width, height, unit },
        isCropped: true,
        scale1ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"], // Set the cropped image URL as scale1ImageUrl
        scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
        scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
      };
      dispatch(
        updateUser({
          payload: {
            ...sessionInfo.data,
            defaultImage: payload,
          },
        })
      )
        .then(() => {
          // Ensure the component re-renders with the new image
          setIsImageUploaded(false);
          setSelectedImage(null);
          setCroppedImageUrl(null);
          setCompletedCrop(undefined);
          setCrop(undefined);
        })
        .catch((error) => {
          console.error("Error updating user:", error);
        })
        .finally(() => {
          disableLoader();
        });
    }
  }, [completedCrop, getCroppedImg, sessionInfo.data, dispatch, disableLoader]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      enableLoader();
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        setIsImageUploaded(true);

        setSelectedImage(reader.result as string);
        const binaryData = e.target?.result as ArrayBuffer;
        const payload = {
          id: sessionInfo.data["defaultImage"]["id"],
          imageBinary: binaryData,
          isCropped: true,
          scale1ImageUrl: reader.result as string, // Set the uploaded image as scale1ImageUrl
          scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
          scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
        };
        dispatch(
          updateUser({
            payload: {
              ...sessionInfo.data,
              defaultImage: payload,
            },
          })
        )
          .then(() => { })
          .catch((error) => {
            console.error("Error updating user:", error);
          })
          .finally(() => {
            disableLoader();
          });
      };
      reader.readAsDataURL(file);
    }
  };
  return (
    <div style={{ paddingTop: isMobile && "0px" }} className={styles.buisnesscontainer}>
      <div
        className={`${styles.successBusiness} flex-column md:flex-row md:align-items-baseline`}
      >
        <h2 style={{ marginTop: isMobile && "0px", marginBottom: isMobile && "5px" }} className={styles.businessHeader}>My business</h2>
        {isSuccessful && (
          <div
            style={{
              maxWidth: "230px",
              width: "100%",
              backgroundColor: "#37a950",
              padding: "10px",
              borderRadius: "10px",
              color: "#ffffff",
              fontSize: "12px",
              fontWeight: "800",
              lineHeight: "18px",
              marginBottom: "5px",
              display: "flex", // To align the icon and text
              alignItems: "center", // Vertically center align the items
            }}
          >
            <FaCheck
              style={{
                backgroundColor: "#FFFFFF",
                borderRadius: "50%",
                color: "#179D52",
                padding: "5px",
                height: "16px",
                width: "16px",
                display: "inline-flex",
                alignItems: "center",
                marginRight: "5px",
              }}
            />{" "}
            {/* Add margin for spacing */}
            Changes Made Successfully!
          </div>
        )}
      </div>

      <Divider style={{ width: "744px", marginTop: "5px" }} />
      <main className="p-grid p-nogutter">
        <h2 style={{ marginBottom: isMobile && "0px" }} className={styles.businessTitle}>Business Name</h2>
        <Divider style={{ width: "744px", marginTop: "3px" }} />
        <div className="p-field mb-3" style={{ marginLeft: !isMobile ? "10px" : "0px" }}>
          <div
            className="input-container"
            style={{ marginTop: "8px", maxWidth: "100%" }}
          >
            <p
              className="p-mb-0"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Business Trading Name *
            </p>{" "}
            <InputText
              readOnly
              id="businessTradingName"
              name="businessTradingName"
              value={businessTradingName}
              onChange={(e) => setBusinessTradingName(e.target.value)}
              placeholder=""
              className={styles.inputPlaceholder}
            />
          </div>
        </div>
        <div className="p-field mb-3" style={{ marginLeft: !isMobile ? "10px" : "0px" }}>
          <div
            className="input-container"
            style={{ marginTop: "8px", maxWidth: "100%" }}
          >
            <p
              className="p-mb-0"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Registered Business Name *
            </p>{" "}
            <InputText
              readOnly
              id="registeredBusinessName"
              name="registeredBusinessName"
              value={registeredBusinessName}
              onChange={(e) => setRegisteredBusinessName(e.target.value)}
              placeholder=""
              className={styles.inputPlaceholder}
            />
          </div>
        </div>
        <div className="p-field mb-3" style={{ marginLeft: !isMobile ? "10px" : "0px" }}>
          <div
            className="input-container"
            style={{ marginTop: "8px", maxWidth: "100%" }}
          >
            <p
              className="p-mb-0"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Business Address *
            </p>{" "}
            <InputText
              readOnly
              id="businessAddress"
              name="businessAddress"
              value={businessAddress}
              onChange={(e) => setBusinessAddress(e.target.value)}
              placeholder=""
              className={styles.inputPlaceholder}
            />
          </div>
        </div>
        <h2 className={styles.businessTitle}>Business Details</h2>
        <Divider style={{ width: "744px", marginTop: "3px" }} />
        <div className={`${styles.buisnessflexItem} p-shadow-2 p-p-3`}>
          <div className="p-field mb-3">
            <p
              className="p-mb-0"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Company establishment
            </p>{" "}
            {/* Column Name Above Dropdown */}
            <Dropdown
              id="year"
              value={selectedYear}
              options={yearOptions}
              onChange={(e) => setSelectedYear(e.value)}
              placeholder="Select a Year"
              className={`${styles.buisnessyear} ${currentYear ? styles["green-border"] : ""
                }`}
            />
            <div className="p-field mb-3">
              <p
                className="p-mb-0"
                style={{
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Number of employees
              </p>{" "}
              {/* Column Name Above Dropdown */}
              <Dropdown
                id="employees"
                value={selectedEmployeesRange} // Use state variable for selected value
                options={numberOfMembersList} // Provide options list
                onChange={(e) => setSelectedEmployeesRange(e.value)}
                placeholder="Select Employee Range"
                className={`${styles.buisnessemployee} ${currentYear ? styles["green-border"] : ""
                  }`}
              />
            </div>
          </div>

          <div className="p-field mb-3">
            <p className={styles.globalParagraphStyle}>Industry sector</p>{" "}
            {/* Column Name Above Dropdown */}
            <Dropdown
              id="general"
              value={selectedGeneral} // Correctly binding to the selected value
              options={industrySectorsList} // Using the predefined options
              onChange={(e) => setSelectedGeneral(e.value)}
              placeholder="Select an Option"
              className={`${styles.buisnessindustrysector} ${currentYear ? styles["green-border"] : ""
                }`}
            />
            <div className="p-field mb-3">

              {sessionInfo.data?.['client']?.clientCategory === 2 && (
                <div className="p-field mb-3">
                  <p className="p-mb-0" style={{ fontWeight: "600", fontSize: "16px", color: "#585858" }}>
                    Specify Industry
                  </p>
                  <InputText
                    id="username"
                    name="username"
                    value={accountIdentifier}
                    onFocus={() => {
                      setIsFocused(true);
                    }}
                    onBlur={() => setIsFocused(false)}
                    onChange={(e) => {
                      const value = e.target.value;
                      setAccountIdentifier(value);
                    }}
                    placeholder="Specify Industry"
                    className={`${styles.buisnessemployee} ${accountIdentifier ? styles["green-border"] : ""
                      }`}
                  />
                </div>
              )}
            </div>
          </div>

        </div>
        <h2 className={styles.businessTitle}>Business Introduction</h2>
        <Divider style={{ width: "744px", marginTop: "3px" }} />
        <div className="p-col-12 p-md-6">
          <div className={`${styles.buisnessflexsecond} p-shadow-2 p-p-3`}>
            <p className={styles.businessIntrodiscribe}>
              Please describe your business, but do not include any info that
              will need keeping up-to-date. Do NOT include job details here, you
              add these when you Post a Job.
            </p>

            <InputTextarea
              autoResize
              value={inputValue}
              required
              onChange={handleInputChange}
              rows={3}
              cols={30}
              className={styles.inputTextareabuisness}
              placeholder="Example - ABC Pilates is a family owned and run business providing individual and group classes for women of all ages since 2014."
            />
          </div>
        </div>
        <h2 className={styles.businessTitle}>Business Logo</h2>
        <Divider style={{ width: "744px", marginTop: "3px" }} />
        <div className="" style={{ marginTop: "10px" }}>
          <div className="">
            <div className="" style={{ display: "", justifyContent: "center" }}>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                id="image-upload"
                style={{ display: "none" }}
              />
              {!isImageUploaded ? (
                <label
                  htmlFor="image-upload"
                  className="flex align-items-center flex-wrap "
                >
                  <div
                    className="cursor-pointer	"
                    style={{
                      width: "150px",
                      height: "150px",
                      borderRadius: "50%",
                      backgroundColor: "#F0F4F7",
                      border: "1px solid #FFA500",
                    }}
                  >
                    {!isImageUploaded && !sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
                      <img
                        src={Userimage}
                        alt=""
                        className="cursor-pointer mt-4 ml-4 "
                        style={{
                          width: "95px",
                          height: "99px",
                          // borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                        }}
                      />
                    ) : (
                      <img
                        src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? Userimage}
                        alt=""
                        className="cursor-pointer"
                        style={{
                          width: "148px",
                          height: "148px",
                          borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                        }}
                      />
                    )}
                  </div>
                  <div
                    className="ml-4 pl-3 pr-3 p-2 cursor-pointer"
                    style={{
                      backgroundColor: "#1F9EAB",
                      color: "#FFFFFF",
                      fontSize: "12px",
                      fontWeight: "500",
                      borderRadius: "8px",
                    }}
                  >
                    <span className="pi pi-camera"></span>
                    &nbsp;&nbsp;Upload Logo
                  </div>
                </label>
              ) : (
                <ReactCrop
                  crop={crop}
                  onChange={(_, percentCrop) => setCrop(percentCrop)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={1}
                  // circularCrop
                  style={{ objectFit: "contain" }}
                >
                  <img
                    ref={imgRef}
                    src={selectedImage}
                    alt="Selected"
                    crossOrigin="anonymous"
                    onLoad={onImageLoad}
                    className="cursor-pointer"
                    style={{
                      //  borderRadius: "50%",
                      backgroundColor: "#F0F4F7",
                    }}
                  />
                </ReactCrop>
              )}
            </div>
            {isImageUploaded && (
              <div className="">
                <div
                  style={{
                    display: "flex",
                  }}
                >
                  <CustomButton
                    label="Cancel"
                    onClick={handleCancel}
                    style={{
                      marginRight: "10px",
                      backgroundColor: "transparent",
                      color: "#585858",
                      height: "30px",
                      fontSize: "14px",
                      fontWeight: "500",
                      boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                      width: "150px",
                    }}
                  />
                  <CustomButton
                    label="Save logo"
                    onClick={handleSavePhoto}
                    style={{
                      backgroundColor: "#1F9EAB",
                      width: "150px",
                      height: "30px",
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      <footer className={styles.buisnessfooter}>
        <div
          style={{
            display: "flex",
            justifyContent: "end", // Center the button
            alignItems: "center",
            marginTop: "10px",
            paddingRight: "45px",
          }}
        >
          {/* Next/Submit button */}
          <CustomButton
            label={
              <>
                Save
                <i
                  className="pi pi-angle-right"
                  style={{ marginLeft: "8px" }}
                ></i>
              </>
            }
            style={
              isFormComplete()
                ? {
                  backgroundColor: "#FFA500",
                  color: "#fff",
                  width: "156px",
                  height: "39px",
                  fontWeight: "800",
                  fontSize: "14px",
                  borderRadius: "8px",
                  border: "2px solid transparent",
                  boxShadow: "0px 4px 12px #00000",
                  transition:
                    "background-color 0.3s ease, box-shadow 0.3s ease",
                }
                : {
                  backgroundColor: "#ddd", // Disable state color
                  color: "#aaa",
                  fontWeight: "600",
                  width: "156px",
                  height: "39px",
                  fontSize: "14px",
                }
            }
            onClick={handleNext} // Set the onClick to handleNext function
            disabled={!isFormComplete()} // Disable until form is complete
          />

        </div>
      </footer>
      <p className={styles.instruct}>
        *This field is not editable. Contact Juggle Street to update.
      </p>
    </div>
  );
};

export default MyBusiness;
