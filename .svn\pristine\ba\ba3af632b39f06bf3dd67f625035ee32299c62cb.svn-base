import * as $ from "jquery";

import { useNavigateTo } from "../helper/navigation";
import environment from "../helper/environment";
import ClientAPIRequest from "../helper/clientApiRequest";
// import helpdeskManager from './helpdeskManager';
import utils from "../components/utils/util";
import Service from "../services/services";
import helpdeskManager from "../services/helpdeskManager";
import Api from "../helper/juggleStreetApi";
import CookiesConstant from "../helper/cookiesConst";
export interface UserData {
  id: string;
  name: string;
  email: string;
  callback: any;
  failureCallback: any;
  // Add other user properties as needed
}

var GET = "GET";
var POST = "POST";
// var DELETE = "DELETE";
// var PUT = "PUT";
var PATCH = "PATCH";
class Auth {
  pendingStats = {
    pendingChatMessages: 0,
    pendingJobs: 0,
    pendingInvitations: 0,
    unratedJobs: 0,
  };
  // write function for hanlde login
  async handleAttemptLogin(email, password, success, failure) {
    try {
      const payload = {
        userName: email,
        password: password,
      };

      let url = ClientAPIRequest.baseURL() + Api.identity.handleAttemptLogin;
      ClientAPIRequest.makeRequest(url, "POST", payload, success, failure);
    } catch (error) {
      failure(error);
    }
  }

  // static handleAttemptLogins(email, password, success, failure) {
  //   this.login2(email, password, (response) => this.handleLogin(response, success, failure), (response) => {
  //     if (failure != null) {
  //       return failure(response);
  //     } else {
  //       return this.handleServiceError(response);
  //     }
  //   });

  // }
  static handleAttemptLogins(email: string, password: string, success: (data: any) => void, failure: (error: any) => void) {
    this.login2(
      email,
      password,
      (response) => this.handleLogin(response, success, failure),
      (response) => {
        if (failure != null) {
          return failure(response);
        } else {
          return this.handleServiceError(response);
        }
      }
    );
  }

  setRaygunUser(identifier, isAnonymous, email, firstName, fullName, uuid) {
    (<any>window).rg4js("setUser", {
      identifier: identifier,
      isAnonymous: isAnonymous,
      email: email,
      firstName: firstName,
      fullName: fullName,
      uuid: uuid,
    });
  }
  handleLoginComplete = (user) => {
    this.requestPendingStats();
    var accountDetails =
      (user.attributes.isClient ? "Parent" : "Sitter") + " - " + user.id + " - " + user.attributes.firstName + " " + user.attributes.lastInitial;
    this.setRaygunUser(user.id, false, "mailto:<EMAIL>", user.attributes.firstName, accountDetails, user.id);
    (<any>window).ga("set", "userId", user.id);
    helpdeskManager.boot(user.attributes);
  };

  requestPendingStats = () => {
    if (!utils.isBrowser()) return;

    Service.requestPendingStats(
      (stats) => {
        this.pendingStats = stats;
      },
      () => {}
    );
  };

  static handleServiceError = (response) => {
    let message: string;

    if (!response) {
      message = "Something went wrong";
    } else if (response.status == 0) {
      if (response.errorText == "error") message = "Unable to connect to server";
      else message = response.statusText;
    } else if (response.responseJSON) {
      message = response.responseJSON.message || "Something went wrong";
    } else if (response.message) {
      message = response.message;
    }
  };

  // static handleLogin = (response, success, failure) => {
  //   // this.storeToken(response.access_token);
  //   // this.setupToken(response.access_token);
  //   this.storeToken("jugglestreet-access-token", response.accessToken);
  //   this.storeToken("jugglestreet-refresh-token", response.refreshToken);
  //   this.setupToken(response.accessToken);
  //   this.checkSession(response.accessToken,
  //     (data) => {
  //       // if (success != null) {
  //       //   success();
  //       // }
  //
  //       // this.loader.fadeOut();
  //       return this.redirectAfterLogin();
  //     },
  //     () => {
  //       if (failure) {
  //         return failure();
  //       }
  //     }
  //   );
  // }

  static handleLogin(response: any, success: (data: any) => void, failure: (error: any) => void) {
    this.storeToken(CookiesConstant.accessToken, response.accessToken);
    this.storeToken(CookiesConstant.refreshToken, response.refreshToken);
    this.storeToken(CookiesConstant.clientType, response.clientType ?? 0);
    this.setupToken(response.accessToken);

    this.checkSession(
      response.accessToken,
      (data) => {
        if (success != null) {
          success(data);
        }
      },
      (error) => {
        // Even if session check fails, we'll consider the login successful
        // as we have received and stored the tokens
        failure(error);
      }
    );
  }
  static redirectAfterLogin() {
    const navigateTo = useNavigateTo();
    navigateTo("/", { replace: true });
  }

  static async login2(email: any, password, callback, failureCallback) {
    const payload = {
      userName: email,
      password: password,
    };
    let url = Api.identity.handleAttemptLogin;

    // const lockBusinessAccounts = env.isProduction(window.location.hostname);
    const lockBusinessAccounts = false;
    const loginCallback = (response) => {
      if (lockBusinessAccounts && response.accountType == 1 && response.clientType == 2) {
        const failureResponse = {
          message: "Business accounts can only use the Juggle St app at this time, contact us for more details.",
        };
        failureCallback(failureResponse);
        return;
      }
      callback(response);
    };
    return await ClientAPIRequest.makeRequest(url, POST, payload, loginCallback, failureCallback);
  }

  static storeToken(type, token) {
    var expires = new Date();
    expires.setFullYear(new Date().getFullYear() + 1);
    utils.setCookie(type, token, expires);
  }

  static setupToken(token) {
    return $.ajaxSetup({
      headers: {
        Authorization: "Bearer " + token,
      },
    });
  }
  static getWithHeaders(url, data, callback, failureCallback: any = this.handleError) {
    return ClientAPIRequest.makeRequest(url, GET, data, callback, failureCallback);
  }
  static checkSession(token, callback, failureCallback) {
    var headers = {
      Authorization: "Bearer " + token,
    };
    return this.getWithHeaders(Api.account.checkSession, headers, callback, failureCallback);
  }
  static handleError(error) {}

  static suburbSearch(term, callback, failureCallback) {
    const payload = {
      searchText: term.trim(),
      country: environment.getCountry(window.location.hostname),
    };
    return ClientAPIRequest.makeRequest(`https://api2.jugglestreet.com${Api.geo.suburbSearch}`, POST, payload, callback, failureCallback);
  }

  //   suburbSearch(term, callback, failureCallback) {
  //     const request = {
  //         searchText: term.trim(),
  //         country: environment.getCountry(),
  //     };

  //     return ClientAPIRequest.makeRequest('https://sapi2.jugglestreet.com/api/geo/suburbsearch', POST, request, callback, failureCallback);
  // }

  static registerUserV2(userData, callback, failureCallback) {
    return ClientAPIRequest.makeRequest(Api.account.registerUserV2, POST, userData, callback, failureCallback);
  }
  static forgotPassword(accountIdentifier, callback, failureCallback) {
    return ClientAPIRequest.makeRequest(
      Api.account.forgotPassword,
      POST,
      {
        accountIdentifier: accountIdentifier,
      },
      callback,
      failureCallback
    );
  }
  static resetPassword(code, password, confirmPassword, callback, failureCallback) {
    return ClientAPIRequest.makeRequest(
      Api.account.resetPassword,
      POST,
      {
        code: code,
        password: password,
        confirmPassword: confirmPassword,
      },
      callback,
      failureCallback
    );
  }
  static updateUser(userData, callback, failureCallback) {
    return ClientAPIRequest.makeRequest(Api.account.updateUser, PATCH, userData, callback, failureCallback);
  }

  static getVerificationCode(phoneNumber: string, callback: Function, failureCallback: Function = this.handleError): void {
    const encodedTerm = encodeURIComponent(phoneNumber.trim().replace(/[^0-9]/g, ""));
    const url = `${Api.account.getVerificationCode}${encodedTerm}`;

    // Assuming `ClientAPIRequest` is your abstraction for making HTTP requests
    ClientAPIRequest.makeRequest(url, "GET", null, callback, failureCallback);
  }

  static submitVerificationCode(code: string, callback: Function, failureCallback: Function) {
    const encodedTerm = encodeURIComponent(code.trim().replace(/[^0-9]/g, ""));
    const url = `${Api.account.submitVerificationCode}${encodedTerm}`;

    // Assuming ClientAPIRequest is your abstraction for making HTTP requests
    ClientAPIRequest.makeRequest(url, "GET", null, callback, failureCallback);
  }
}
export default Auth;
