import ClientAPIRequest from '../helper/clientApiRequest';
import environment from '../helper/environment';
import Api from '../helper/juggleStreetApi';

var GET = 'GET';
var POST = 'POST';
var PATCH = 'PATCH';
// var DELETE = "DELETE";
// var PUT = "PUT";
// var PATCH = "PATCH";

export class Service {
    static singleValueFilters = [
        'experience',
        'distance',
        'activity',
        'responseTime',
        'language',
        'neighbourhood',
        'nationality',
        'auPairCategory',
        'jobDeliveryMethod',
        'year12GraduationYear',
        'hourlyRate',
    ];

    static updateSessionInfo(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.updateSessionInfo,
            PATCH,
            data,
            callback,
            failureCallback
        );
    }

    static updateUser(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.updateUser,
            PATCH,
            data,
            callback,
            failureCallback
        );
    }

    static requestPendingStats = (callback, failureCallback) => {
        return ClientAPIRequest.makeRequest(
            Api.account.requestPendingStats,
            GET,
            null,
            callback,
            failureCallback
        );
    };
    static requestPendingStatsPartial = (callback, failureCallback) => {
        return ClientAPIRequest.makeRequest(
            Api.account.requestPendingStatsPartial,
            GET,
            null,
            callback,
            failureCallback
        );
    };

    static geoSearch = (callback, failureCallback, userData) => {
        return ClientAPIRequest.makeRequest(
            Api.graph.geoSearch,
            POST,
            userData,
            callback,
            failureCallback
        );
    };

    static getConnections = (callback, failureCallback, data) => {
        return ClientAPIRequest.makeRequest(
            Api.graph.getConnections,
            POST,
            data,
            callback,
            failureCallback
        );
    };

    static addFriend = (friendData, callback, failureCallback) => {
        return ClientAPIRequest.makeRequest(
            Api.connections.connection,
            POST,
            friendData,
            callback,
            failureCallback
        );
    };
    static removeFriend = (friendData, callback, failureCallback, requestId: Number) => {
        return ClientAPIRequest.makeRequest(
            `${Api.connections.connection}/${requestId}`,
            PATCH,
            friendData,
            callback,
            failureCallback
        );
    };
    static acceptRequest = (friendData, callback, failureCallback, requestId: Number) => {
        return ClientAPIRequest.makeRequest(
            `${Api.connections.connection}/${requestId}`,
            PATCH,
            friendData,
            callback,
            failureCallback
        );
    };
    static getServerFilters(filterInfo) {
        const serverFilters = [];

        // Check if filterInfo and filterInfo.helpers are defined
        if (!filterInfo || !filterInfo.helpers || !filterInfo.helpers.criteria) {
            return serverFilters; // Return empty array or handle as needed
        }

        const filters = Object.keys(filterInfo.helpers.criteria);
        let addedSchoolSubject = false;
        let addedSchoolYear = false;

        filters.forEach((filterKey) => {
            let filterValue = filterInfo.helpers.criteria[filterKey];

            if (
                filterKey === 'primarySchoolOtherSkills' ||
                filterKey === 'highSchoolOtherSkills' ||
                filterKey === 'auPairOtherSkills' ||
                filterKey === 'onlinePrimarySchoolOtherSkills' ||
                filterKey === 'onlineHighSchoolOtherSkills' ||
                filterKey === 'juggleAssistOtherSkills'
            ) {
                filterKey = 'otherSkills';
            }

            if (filterKey === 'primarySchoolYears' || filterKey === 'highSchoolYears') {
                if (addedSchoolYear) return;

                addedSchoolYear = true;

                filterKey = 'schoolYears';
                const jobTypes = filterInfo.helpers.criteria.jobTypes;
                if (jobTypes && jobTypes.length > 0) {
                    if (jobTypes[0] === 64)
                        filterValue = filterInfo.helpers.criteria.primarySchoolYears;
                    else if (jobTypes[0] === 128)
                        filterValue = filterInfo.helpers.criteria.highSchoolYears;
                    else return;
                }

                return;
            }

            if (filterKey === 'primarySchoolSubjects' || filterKey === 'highSchoolSubjects') {
                if (addedSchoolSubject) return;

                addedSchoolSubject = true;
                filterKey = 'schoolSubject';
                const jobTypes = filterInfo.helpers.criteria.jobTypes;
                if (filterValue === '1') filterValue = -1;
                else if (jobTypes && jobTypes.length > 0) {
                    if (jobTypes[0] === 64 && filterInfo.helpers.criteriaArgs.primarySchoolSubject)
                        filterValue = parseInt(
                            filterInfo.helpers.criteriaArgs.primarySchoolSubject
                        );
                    else if (
                        jobTypes[0] === 128 &&
                        filterInfo.helpers.criteriaArgs.highSchoolSubject
                    )
                        filterValue = parseInt(filterInfo.helpers.criteriaArgs.highSchoolSubject);
                    else filterValue = -1;
                } else {
                    filterValue = -1;
                }
            }

            if (filterValue && this.singleValueFilters.indexOf(filterKey) !== -1) {
                if (filterValue.length > 0) filterValue = parseInt(filterValue[0]);
                else filterValue = null;
            }

            serverFilters.push({
                field: filterKey,
                value: filterValue,
                operator: 'eq',
            });
        });

        if (filterInfo.helpers.criteriaArgs && filterInfo.helpers.criteriaArgs.languageId) {
            serverFilters.push({
                field: 'language',
                operator: 'eq',
                value: filterInfo.helpers.criteriaArgs.languageId,
            });
        }

        return serverFilters;
    }
    static addressSearch(term, callback, failureCallback) {
        const payload = {
            country: environment.getCountry(window.location.hostname),
            term: term.trim(),
        };

        return ClientAPIRequest.makeRequest(
            `https://api2.jugglestreet.com${Api.geo.addresssearch}`,
            POST, // Assuming POST request
            payload, // Data payload with term and country
            callback, // Success callback
            failureCallback // Failure callback
        );
    }
    static geocodeAddress(term, callback, failureCallback) {
        const payload = {
            country: environment.getCountry(window.location.hostname),
            term: term,
        };

        return ClientAPIRequest.makeRequest(
            `https://api2.jugglestreet.com${Api.geo.geocodeAddress}`,
            POST, // Assuming POST request
            payload, // Data payload with term and country
            callback, // Success callback
            failureCallback // Failure callback
        );
    }
    static async payments(data) {
        const response = await new Promise((resole, reject) => {
            ClientAPIRequest.makeRequest(Api.payments.payments, PATCH, data, resole, reject);
        });
        return response;
    }

    static refreshAccount(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.updateUser,
            GET,
            null,
            callback,
            failureCallback
        );
    }

    static changePassword(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.changePassword,
            'POST',
            data,
            callback,
            failureCallback
        );
    }
    static jobClient(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.client,
            'POST',
            data,
            callback,
            failureCallback,
        );
    }
    static jobClientUpdate(data, jobId, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.client.client}/${jobId}`,
            'PATCH',
            data,
            callback,
            failureCallback
        );
    }
    static jobClientDetails(callback, failureCallback, id: number) {
        return ClientAPIRequest.makeRequest(
            `${Api.client.client}/${id}`,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static jobProviderDetails(callback, failureCallback, id: number) {
        return ClientAPIRequest.makeRequest(
            `${Api.provider.provider}/${id}`,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static jobProviderDetailsUpdate(callback, failureCallback, id: number, payload: object) {
        return ClientAPIRequest.makeRequest(
            `${Api.provider.provider}/${id}`,
            PATCH,
            payload,
            callback,
            failureCallback
        );
    }
    static jobClientAwardJobs(callback, failureCallback, id: number, payload: object) {
        return ClientAPIRequest.makeRequest(
            `${Api.client.client}/${id}`,
            PATCH,
            payload,
            callback,
            failureCallback
        );
    }
    static AwardRecurringJob(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.AwardRecurringJob,
            POST,
            data,
            callback,
            failureCallback,
        );
    }
    static hideMyAccount(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.hideAccount,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static requestDeleteAccount(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.requestdeleteaccount,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static getHelper(id: number, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.profiles.provider}/${id}`,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static fetchClients(id: number, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.profiles.client}/${id}`,
            GET,
            null,
            callback,
            failureCallback
        );
    }
    static getUpComingJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.upComing,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static gethelperUpComingJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.provider.upComing,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getCompletedJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.completed,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static gethelperCompletedJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.provider.completed,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getUnratedJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.unratedJobs,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static gethelperUnratedJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.provider.unratedJobs,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static rateHelper(callback, failureCallback, id, data) {
        return ClientAPIRequest.makeRequest(
            `${Api.jobs.provider}/${id}/rating`,
            'POST',
            data,
            callback,
            failureCallback
        );
    }
    static rateParent(callback, failureCallback, id, data) {
        return ClientAPIRequest.makeRequest(
            `${Api.jobs.client}/${id}/rating`,
            'POST',
            data,
            callback,
            failureCallback
        );
    }
    static fetchArchivedJobs(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.client.completed,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static jobEditClient(callback, failureCallback, data, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.client.client}/${id}`,
            PATCH,
            data,
            callback,
            failureCallback
        );
    }
    static getChatSessions(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.chats.conversations,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getChatInitiate(callback, failureCallback, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.chats.conversations}/with/${id}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getChatSpecific(callback, failureCallback, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.chats.conversations}/${id}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static setChatSpecific(callback, failureCallback, data, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.chats.conversations}/${id}`,
            'PATCH',
            data,
            callback,
            failureCallback
        );
    }
    static getChatHelepr(callback, failureCallback, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.chats.conversations}/${id}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getChat(callback, failureCallback, id) {
        return ClientAPIRequest.makeRequest(
            `${Api.chats.conversations}/canchatwithhelper/${id}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getinvoice(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.payments.invoices,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static logAppEvent = function (callback, failureCallback, payload) {
        return ClientAPIRequest.makeRequest(
            Api.account.logAppEvent, // Endpoint
            POST,                   // Correct HTTP method
            payload,
            callback,
            failureCallback
        );
    };
    static updateProfileVisibility(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.profilevisibliity,
            'POST',
            data,
            callback,
            failureCallback
        );
    }
    static contactUsRequest(data, callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.contactUsRequest,
            'POST',
            data,
            callback,
            failureCallback
        );
    }

    static sendMobileLink(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            Api.account.sendmobilelink,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getTimeSheet(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.timesheet.timesheet}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
     static getFinalizedTimesheetslist(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.timesheet.finalizedtimesheetslist}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getHelperAdjustedlist(callback, failureCallback) {
        return ClientAPIRequest.makeRequest(
            `${Api.timesheet.helperadjustedlist}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static getTimeSheetDetails(callback,failureCallback,timesheetId: number) {
        return ClientAPIRequest.makeRequest(
            `${Api.timesheet.timesheetdetails}/${timesheetId}`,
            'GET',
            null,
            callback,
            failureCallback
        );
    }
    static postUpdateHistory = function (callback, failureCallback, payload) {
        return ClientAPIRequest.makeRequest(
            Api.timesheet.updatehistory,
            POST,
            payload,
            callback,
            failureCallback
        );
    };


}

export default Service;
