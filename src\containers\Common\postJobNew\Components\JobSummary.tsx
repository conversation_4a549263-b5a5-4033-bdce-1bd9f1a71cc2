import { useEffect, useRef, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import { PayloadTemplate, useJobManager } from "../provider/JobManagerProvider";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import useLoader from "../../../../hooks/LoaderHook";
import { useNavigate } from "react-router-dom";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import styles from "../../../Common/styles/job-summary.module.css";
import c from "../../../../helper/juggleStreetConstants";
import Service from "../../../../services/services";
import { Tag } from "primereact/tag";
import calendar from "../../../../assets/images/Icons/Icon (1).png";
import clock from "../../../../assets/images/Icons/Vector.png";
import home from "../../../../assets/images/Icons/home-05.png";
import doller from "../../../../assets/images/Icons/Dollar.png";
import { Divider } from "primereact/divider";
import JobBreakdown from "./JobSummary/JobBreakdown";
import ProfileCard from "./JobSummary/InvitedCandidatesCard";
import { Dialog } from "primereact/dialog";
import ProviderProfilePopup from "../../../Parent/ProviderProfile/ProviderProfilePopup";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import { GoBack } from "./Buttons";
import { Schedule } from "./Pricing&Payments/Interface";
import { Toast } from "primereact/toast";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";
function JobSummary() {
  const { isMobile } = useIsMobile();
  return isMobile ? <JobSummaryMobile /> : <JobSummaryWeb />;
}

export default JobSummary;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();
  const jobDate = payload.jobDate || new Date().toISOString();
  const { isMobile } = useIsMobile();
  const toast = useRef(null);
  const [schedules, setSchedules] = useState<Schedule[]>(
    payload.weeklySchedule?.["weeklyScheduleEntries"].map((entry) => ({
      ...entry,
      hourlyPrice: Number(entry.hourlyPrice), // Convert hourlyPrice to a number
      shifts: entry.shifts.map((shift) => ({
        ...shift,
        hourlyPrice: Number(shift.hourlyPrice), // Convert hourlyPrice in shifts to a number
      })),
    })) || []
  );
  // Function to add the correct ordinal suffix
  function getOrdinalSuffix(day) {
    if (day > 3 && day < 21) return "th"; // 11th to 20th
    switch (day % 10) {
      case 1:
        return "st";
      case 2:
        return "nd";
      case 3:
        return "rd";
      default:
        return "th";
    }
  }
  const getJobDetails = (jobclientProp) => {
    const jobToUse = jobclientProp || { jobType: 0 };
    switch (jobToUse.jobType) {
      case 256:
        return {
          label: "Odd Job",
        };
      case 64:
        return {
          label: "Primary School",
        };
      case 128:
        return {
          label: "High School",
        };
      default:
        return {
          label: "Childcare",
        };
    }
  };
  const weekMap = new Map([
    [1, "Mon"],
    [2, "Tues"],
    [3, "Wed"],
    [4, "Thurs"],
    [5, "Fri"],
    [6, "Sat"],
    [0, "Sun"],
  ]);

  const formatTime = (time: string | null | undefined) => {
    if (!time) {
      return ""; // or handle this case as needed, e.g., return a default time or throw an error
    }
    const [hours, minutes] = time.split(":");
    if (hours === undefined || minutes === undefined) {
      return ""; // handle the case where time format is incorrect
    }

    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));

    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    })
      .format(date)
      .toLocaleLowerCase();
  };
  // Format the date

  const formattedDateMobile = jobDate
    ? new Intl.DateTimeFormat("en-GB", {
      weekday: "short", // "Mon"
      day: "2-digit", // "31"
      month: "short", // "Mar"
      year: "2-digit", // "25"
    }).format(new Date(jobDate))
    : "Date not specified";
  const startTime = payload.jobStartTime;
  const endTime = payload.jobEndTime;
  const dateObj = new Date(jobDate);
  const shortDay = dateObj.toLocaleDateString("en-GB", { weekday: "short" }); // Short weekday
  const day = dateObj.getDate();
  const ordinalSuffix = getOrdinalSuffix(day);
  const month = dateObj.toLocaleDateString("en-GB", { month: "long" }); // Full month name
  const formattedDate = `${shortDay} ${day}${ordinalSuffix} of ${month}`;
  const duration = payload.duration;
  // const durationType = payload.durationType === 1 ? "Week " : "Month";
  const [address, setAddress] = useState(null);
  const price = payload.price;
  const specialInstructions = payload.specialInstructions || "";
  const [visible, setVisible] = useState<boolean>(false);
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState("");
  const formattedStartTime = formatTime(startTime);
  const formattedEndTime = formatTime(endTime);
  const [isExpanded, setIsExpanded] = useState(false);
  const helperPaymentMethod = payload.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer";
  const clientType = utils.getCookie(CookiesConstant.clientType);

  useEffect(() => {
    if (sessionInfo.loading) return;
    const temp = sessionInfo.data["addresses"].find((val) => val["id"] === payload.addressId);
    if (temp === undefined) return;
    setAddress(temp);
  }, [sessionInfo]);
  useEffect(() => {
    if (errorMessage && isMobile && toast.current) {
      toast.current.clear();
      toast.current.show({
        severity: "error",
        summary: null, // Remove the title
        detail: errorMessage,
        life: 3000,
        className: styles.customToast, // Reference module class
        contentClassName: styles.customToastContent,
      });
      setErrorMessage("");
    }
  }, [errorMessage, isMobile]);
  const distanceMapping = {
    0: "Within 2.5km",
    1: "Within 5km",
    2: "Within 10km",
  };

  const ageMapping = {
    1: "16-17",
    2: "18-24",
    3: "25-44",
    4: "45+",
  };

  const otherSkillsMapping = {
    4: "Special Needs",
    9: "Driving Licence",
  };

  const tutoringCategoryMapping = {
    1: "Newbie",
    2: "Apprentice",
    3: "Experienced",
    4: "Professional",
  };

  const filteredApplicantFilters = payload?.applicantFilters?.filter(
    (filter) => filter.field === "age" || filter.field === "distance" || filter.field === "otherSkills" || filter.field === "tutoringCategory"
  );

  const footerContent = (
    <div>
      <button
        onClick={() => {
          setVisible(false);
          handlePostJobSubmit();
        }}
        autoFocus
        className={`${styles.okGotItBtn}`}
      >
        Ok,Got it
      </button>
    </div>
  );

  // const handlePostJobSubmit = () => {
  //   enableLoader();
  //   Service.jobClient(
  //     utils.omitProperty(currentPayload, "metadata"),
  //     () => {
  //       disableLoader();
  //     },
  //     () => {
  //       disableLoader();
  //     }
  //   );
  //   currentPayload;
  // };
  const getSchoolSubject = (payload, sessionInfo) => {
    // Check if schoolSubjects is empty or not provided
    if (!payload.jobSettings?.schoolSubjects || payload.jobSettings.schoolSubjects.length === 0) {
      return 'All';
    }

    // Helper function to find an option in nested structure
    const getOption = (value, options) => {
      const getOptionInternal = (value, option) => {
        if (option.optionId === value) return option;

        let result = null;
        if (option.children) {
          for (let i = 0; i < option.children.length; i++) {
            result = getOptionInternal(value, option.children[i]);
            if (result) break;
          }
        }
        return result;
      };

      if (!options) return null;

      for (let i = 0; i < options.length; i++) {
        const result = getOptionInternal(value, options[i]);
        if (result) return result;
      }
      return null;
    };

    // Get the appropriate subject list based on job type
    const schoolSubjects = payload.jobType === 64
      ? sessionInfo.data?.["client"]?.["primarySchoolSubjects"]
      : sessionInfo.data?.["client"]?.["highSchoolSubjects"]

    // Find the matching option
    const option = getOption(payload.jobSettings.schoolSubjects[0], schoolSubjects);

    // Return the option text or 'All' if not found
    return option?.text || 'All';
  };


  const createPayload = () => {
    const createFinalEntries = (entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]) => {
      const newEntries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = [];
      entries.forEach((e) => {
        e.shifts.forEach((s) => {
          newEntries.push({
            ...e,
            hourlyPrice: s.hourlyPrice,
            price: s.price,
            jobStartTime: s.jobStartTime,
            jobEndTime: s.jobEndTime,
            shiftType: s.shiftType,
          });
        });
      });

      return newEntries;
    };

    const finalPayload: Partial<PayloadTemplate> = {
      ...payload,
      weeklySchedule: {
        ...payload.weeklySchedule,
        weeklyScheduleEntries: createFinalEntries(payload.weeklySchedule.weeklyScheduleEntries),
      },
    };

    if (finalPayload.durationToStart === null) {
      return utils.omitProperties(finalPayload, ["metadata", "durationToStart", "jobStartDurationType", "selectedCandidates"]);
    } else {
      return utils.omitProperties(finalPayload, ["metadata", "jobDate", "jobDateDay", "jobDateMonth", "jobDateYear", "selectedCandidates"]);
    }
  };

  const handlePostJobSubmit = () => {
    enableLoader();
    const finalPayload = createPayload();

    if (finalPayload.actionType === c.jobActionType.EDIT_JOB) {
      Service.jobEditClient(
        (res) => {
          disableLoader();
          const newPram = new URLSearchParams();
          newPram.set("jobId", String(payload["id"]));
          newPram.set("activeTab", "0");
          newPram.set("managedBy", String(payload["managedBy"]));
          newPram.set("jobType", String(payload["jobType"]));
          newPram.set("show", [64, 128].includes(payload.jobType) ? "1" : "0");
          sessionStorage.removeItem("jobManagement");
          navigate({
            pathname: `/${Number(clientType) === 1 ? "parent-home" : "business-home"}/post-job/complete`,
            search: newPram.toString(),
          });
        },
        (error) => {
          disableLoader();
        },
        finalPayload,
        finalPayload["id"]
      );
    } else {
      Service.jobClient(
        finalPayload,
        (response) => {
          // Success callback
          disableLoader();
          const newPram = new URLSearchParams();
          newPram.set("jobId", String(response.id));
          newPram.set("show", [64, 128].includes(payload.jobType) ? "1" : "0");
          newPram.set("managedBy", String(payload["managedBy"]));
          newPram.set("jobType", String(payload["jobType"]));
          sessionStorage.removeItem("jobManagement");
          navigate({
            pathname: `/${Number(clientType) === 1 ? "parent-home" : "business-home"}/post-job/complete`,
            search: newPram.toString(),
          });
        },
        (error) => {
          // Error callback
          disableLoader();
          const errorMsg =
            error.response?.data?.message || // API-provided error message
            error.message || // Fallback to a generic error message from Axios
            "An unexpected error occurred while posting the job.";
          setErrorMessage(errorMsg);
        }
      );
    }
  };
  const handleTotalPrice = () => {
    let cost = 0;
    schedules?.forEach((val) => {
      val.shifts.forEach((shift) => {
        cost += shift.price;
      });
    });
    return cost;
  };
  const truncateText = (text, wordLimit) => {
    if (!text) return ""; // Handle undefined or null
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };

  const wordLimit = 20; // Set your word limit here for mobile
  const [showPopup, setShowPopup] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [applicantId, setApplicantId] = useState(null);

  const handleImageClick = (candidate, id) => {
    setSelectedCandidate(candidate);
    setApplicantId(id);
    setShowPopup(true);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setSelectedCandidate(null);
    setApplicantId(null);
  };

  return {
    payload,
    next,
    prev,
    setpayload,
    handleClosePopup,
    handleImageClick,
    applicantId,
    selectedCandidate,
    showPopup,
    wordLimit,
    truncateText,
    handleTotalPrice,
    tutoringCategoryMapping,
    filteredApplicantFilters,
    footerContent,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    isExpanded,
    errorMessage,
    visible,
    specialInstructions,
    price,
    address,
    duration,
    formattedDate,
    helperPaymentMethod,
    setIsExpanded,
    setSchedules,
    setVisible,
    setErrorMessage,
    schedules,
    toast,
    weekMap,
    getJobDetails,
    formattedDateMobile,
    formattedStartTime,
    formattedEndTime,
    sessionInfo,
    getSchoolSubject
  };
};

const JobSummaryWeb = () => {
  const {
    payload,
    prev,
    setpayload,
    handleClosePopup,
    handleImageClick,
    applicantId,
    showPopup,
    wordLimit,
    truncateText,
    handleTotalPrice,
    tutoringCategoryMapping,
    filteredApplicantFilters,
    footerContent,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    isExpanded,
    errorMessage,
    visible,
    specialInstructions,
    price,
    address,
    duration,
    formattedDate,
    helperPaymentMethod,
    setIsExpanded,
    setSchedules,
    setVisible,
    setErrorMessage,
    schedules,
  } = useJobTypeHook();
  return (
    <div
      className=" h-full overflow-hidden overflow-y-auto relative"
      style={{
        width: "100%",
      }}
    >
      <div className={styles.reviewPost}>
        <div className={styles.jobSummary}>
          <h3 className="font-bold" style={{ color: "#585858", fontSize: "30px" }}>
            Job Summary
          </h3>
          <div className={styles.tagButton}>
            <Tag style={{ background: "#2F9ACD" }}>
              <div className={styles.tagData}>
                <img alt="Calendar" src={calendar} style={{ width: "18px", color: "#FFFFFF" }} />
                {formattedDate}
              </div>
            </Tag>
            <Tag style={{ background: "#8577DB" }}>
              <div className={styles.tagData}>
                <img alt="Clock" src={clock} style={{ width: "18px", color: "#FFFFFF" }} />
                {duration} week duration
              </div>
            </Tag>

            <Tag style={{ background: "#77DBC9" }}>
              <div className={styles.tagData}>
                <img alt="Doller" src={doller} style={{ width: "10px", color: "#FFFFFF" }} />
                {helperPaymentMethod}
              </div>
            </Tag>
            <Tag style={{ background: "#179D52" }}>
              <div className={styles.tagData}>
                <img alt="Home" src={home} style={{ width: "18px", color: "#FFFFFF" }} />
                {address !== null && utils.cleanAddress(address["formattedAddress"])}
              </div>
            </Tag>
          </div>
          <div>
            {filteredApplicantFilters?.length > 0 && (
              <div>
                <h4 className={`${styles.headerH4} mb-1`}>Candidate Criteria</h4>
                <div>
                  {filteredApplicantFilters?.map((filter, index) => {
                    let displayValue = "";
                    let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                    if (filter.field === "distance") {
                      displayValue = distanceMapping[filter.value];
                    } else if (filter.field === "age") {
                      if (filter.value === 0) {
                        displayValue = Object.values(ageMapping).join(", ");
                      } else {
                        displayValue = (filter.value as number[]).map((val) => ageMapping[val]).join(", ");
                      }
                    } else if (filter.field === "otherSkills") {
                      displayValue = (filter.value as number[]).map((val) => otherSkillsMapping[val]).join(", ");
                      displayField = "Other";
                    } else if (filter.field === "tutoringCategory") {
                      displayValue = (filter.value as number[]).map((val) => tutoringCategoryMapping[val]).join(", ");
                      displayField = "Experience";
                    }
                    return (
                      <div key={index} className={styles.criteriaItem}>
                        <span className={styles.criteriaLabel}>{displayField}:&nbsp;</span>
                        <span className={styles.criteriaValue}>{displayValue}</span>
                      </div>
                    );
                  })}
                </div>
                <Divider className="mt-4" />
              </div>
            )}
          </div>
          <div>
            <h4 className={styles.headerH4}>Job Description</h4>
            <p className={styles.yourJobDescriptionMobile}>{isExpanded ? specialInstructions : truncateText(specialInstructions, wordLimit)}</p>
            {specialInstructions?.split(" ")?.length > wordLimit && (
              <button className={styles.seeMoreButton} onClick={() => setIsExpanded(!isExpanded)}>
                {isExpanded ? "See Less" : "See More"}
              </button>
            )}
          </div>
          <Divider className="mt-4" />
          <div className={styles.jobPrice}>
            <p style={{ textAlign: "end" }}>
              Weekly Job Total = <strong className={styles.jobPriceDoller}>${handleTotalPrice()}</strong>{" "}
            </p>
          </div>
        </div>
        <div className="" style={{ width: "75%" }}>
          <h3 className="font-bold mt-4  pl-3" style={{ color: "#585858", fontSize: "30px" }}>
            Job Roster
          </h3>
          <div
            style={{
              border: "1px solid #DFDFDF",
              borderRadius: "20px",
              padding: "8px 45px",
              marginTop: "-1%",
            }}
          >
            <div
              style={{
                overflow: "hidden auto",
                maxHeight: "154px",
                paddingRight: "65px",
              }}
            >
              {schedules?.map((schedule, index) => (
                <JobBreakdown
                  schedule={schedule}
                  onSchedulePriceChange={(i, s) => {
                    setSchedules((prev) => {
                      const updatedSchedules = [...prev];
                      const shift = [...updatedSchedules[index].shifts];
                      shift[i] = { ...s, price: price };
                      updatedSchedules[index] = {
                        ...updatedSchedules[index],
                        hourlyPrice: price,
                        shifts: shift,
                      };

                      return updatedSchedules;
                    });
                  }}
                  key={index}
                />
              ))}
            </div>
          </div>
        </div>
        <div style={{ width: "75%" }}>
          {payload.managedBy === 1 && (
            <div>
              <h3 className="font-bold p-0 m-0 mt-3 mb-3" style={{ color: "#585858", fontSize: "30px" }}>
                Invited Candidates <strong style={{ color: "#197D52" }}> ({payload?.["selectedCandidates"]?.length || 0})</strong>
              </h3>

              <div className="flex flex-wrap gap-2">
                {payload?.["selectedCandidates"]?.map((profile) => {
                  const applicant = payload.applicants.find((app) => app.applicantId === profile.id);
                  const applicantId = applicant ? applicant.applicantId : null;

                  return (
                    <ProfileCard
                      key={profile.id}
                      name={profile.publicName}
                      location={profile.suburb || "Location not available"}
                      jobsCompleted={profile.jobsCompleted}
                      imageUrl={profile.imageSrc || "default_image.jpg"}
                      isSelected={true} // Always true as per requirement
                      onClick={() => handleImageClick(profile, applicantId)} // Pass the applicantId here
                    />
                  );
                })}
                <Dialog
                  visible={showPopup}
                  style={{ width: "auto" }}
                  // footer={<Button label="Close" icon="pi pi-times" onClick={handleClosePopup} />}
                  onHide={handleClosePopup}
                  draggable={false}
                >
                  <ProviderProfilePopup candidateId={applicantId} requestId={0} />
                </Dialog>
              </div>
            </div>
          )}
        </div>
        {/* <button className={styles.postJobBtn}>Post Job</button> */}
        <Dialog
          header="Important Information"
          visible={visible}
          style={{ width: "50vw", color: "#585858" }}
          onHide={() => {
            if (!visible) return;
            setVisible(false);
          }}
          footer={footerContent}
          draggable={false}
        >
          <p className="m-0">
            <>
              {(() => {
                const jobTypeOneOff = [1, 256];
                if (payload.managedBy === 1) {
                  if (jobTypeOneOff.includes(payload.jobType)) {
                    return (
                      <>
                        This job can be active for up to 14 days. During this time you will be able to chat online with invited candidates. If you
                        don't find the right person quickly, you can change the job details and invite more candidates to apply. When a candidate
                        applies for your job, they will be waiting to hear from you! Make sure to confirm your chosen candidate by clicking the AWARD
                        button.
                      </>
                    );
                  }
                  return (
                    <>
                      This recurring job post will be active for 30 days, during this time you will be able to chat online with invited candidates. If
                      you don’t find the right person right away you can invite more candidates to apply, change the job details and repost the job.
                      Remember, when a candidate applies for your job they will be waiting to hear from you!
                    </>
                  );
                } else {
                  if (jobTypeOneOff.includes(payload.jobType)) {
                    return (
                      <>
                        This job can be active for up to 14 days. The Juggle Assist algorithm will now invite candidates who meet your criteria and
                        invite them to your job. You will be notified each time a candidate applies for this job – these applicants will appear in
                        your job shortlist
                      </>
                    );
                  }
                  return (
                    <>
                      The Juggle Assist algorithm will now invite candidates who meet your criteria and invite them to your job. You will be notified
                      each time a candidate applies for this job – these applicants will appear in your job shortlist.
                    </>
                  );
                }
              })()}
            </>
          </p>
        </Dialog>
        <button
          className={styles.postJobBtn}
          onClick={() => {
            setErrorMessage(""); // Reset error message before new submission
            setVisible(true);
          }}
        >
          Post Job
        </button>
        {/* <p className={styles.goBack} onClick={() => prevClicked(payload)}>
          <span className='pi pi-angle-left'></span>Go Back
        </p> */}
        <GoBack
          onClick={() => {
            setpayload({
              ...payload,
            });
            if (payload.managedBy === 20) {
              prev("candidate-matching"); // Navigate to "candidate-matching" if managedBy is 20
            } else if ([2, 4, 8, 12].includes(payload.jobType)) {
              prev("candidate-selection"); // Navigate to "candidate-selection" for specific job types
            } else {
              prev("candidate-selection"); // Default navigation to "candidate-selection"
            }
          }}
        />
        {errorMessage && <div className={styles.errorMessage}>{errorMessage}</div>}
      </div>
    </div>
  );
};
const JobSummaryMobile = () => {
  const {
    payload,
    prev,
    setpayload,
    wordLimit,
    truncateText,
    handleTotalPrice,
    footerContent,
    isExpanded,
    visible,
    specialInstructions,
    price,
    address,
    duration,
    formattedDate,
    helperPaymentMethod,
    filteredApplicantFilters,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    tutoringCategoryMapping,
    setIsExpanded,
    setSchedules,
    setVisible,
    setErrorMessage,
    schedules,
    toast,
    weekMap,
    formattedDateMobile,
    formattedStartTime,
    formattedEndTime,
    getJobDetails,
    sessionInfo,
    getSchoolSubject

  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        overflow: "hidden",
      }}
    >
      <Toast ref={toast} position="bottom-center" /> {/* Add Toast here */}
      <div className={styles.containerMobile}>
        <div className={styles.reviewPostMobile}>
          <h3 className="font-bold" style={{ color: "#585858", fontSize: "24px", marginBottom: "5px" }}>
            Job Summary
          </h3>
          {/* <div className={styles.tagButton}>
            <Tag style={{ background: "#2F9ACD" }}>
              <div className={styles.tagData}>
                <img alt="Calendar" src={calendar} style={{ width: "18px", color: "#FFFFFF" }} />
                {formattedDate}
              </div>
            </Tag>
            <Tag style={{ background: "#8577DB" }}>
              <div className={styles.tagData}>
                <img alt="Clock" src={clock} style={{ width: "18px", color: "#FFFFFF" }} />
                {duration} week duration
              </div>
            </Tag>

            <Tag style={{ background: "#77DBC9" }}>
              <div className={styles.tagData}>
                <img alt="Doller" src={doller} style={{ width: "10px", color: "#FFFFFF" }} />
                {helperPaymentMethod}
              </div>
            </Tag>
            <Tag style={{ background: "#179D52" }}>
              <div className={styles.tagData}>
                <img alt="Home" src={home} style={{ width: "18px", color: "#FFFFFF" }} />
                {address !== null && utils.cleanAddress(address["formattedAddress"])}
              </div>
            </Tag>
          </div> */}
          <div>
            <div style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }}>
              {`${getJobDetails(payload).label} - ${[64, 128].includes(payload.jobType) ? "Tutoring Job" : [2, 4, 8, 16].includes(payload.jobType) ? "Recurring Job" : "One Off Job"
                }`}
            </div>
            <div className={styles.jobDetailItem}>
              <span className={styles.detailValue}>
                {payload.jobType === 64
                  ? `You are looking for a Primary School Tutor starting on ${formattedDateMobile}, for ${duration} weeks.`
                  : payload.jobType === 128
                    ? `You are looking for a High School Tutor starting on ${formattedDateMobile}, for ${duration} weeks.`
                    : `You are looking to fill a recurring job starting on ${formattedDateMobile}, for ${duration} weeks.`}
              </span>

            </div>

            <div className="flex flex-column">
              <p className={styles.addressTag}>{payload.addressLabel}</p>
              <span className={styles.detailValue}>{address !== null && utils.cleanAddress(address["formattedAddress"])}</span>
            </div>
            <div>
              <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="m-0 p-0 mt-2">
                Payment
              </p>
            </div>
            {payload.paymentType !== 1 && (
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Base rate: </span>
                <span className={styles.detailValue}>${price} per hour</span>
              </div>
            )}

            {payload.paymentType !== 1 && (
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Payment Method: </span>
                <span className={styles.detailValue}> {helperPaymentMethod}</span>
              </div>
            )}
          </div>

          {payload.jobType === 64 || payload.jobType === 128 && (
            <>
              <div>
                <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="m-0 p-0 mt-2">
                  Tutoring Requirements
                </p>
              </div>
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Type: </span>
                <span className={styles.detailValue}>{Number(payload.jobDeliveryMethod) === 1 ? "In-home Tutoring" : "Online Tutoring"}</span>
              </div>
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Subject: </span>
                <span className={styles.detailValue}>{getSchoolSubject(payload, sessionInfo)}</span>
              </div>
            </>
          )}
          <div>
            <div style={{ backgroundColor: "#BBBBBB33", borderRadius: "20px", paddingInline: "20px", paddingBlock: "10px", marginTop: "10px" }}>
              <h4 className="font-bold p-0 m-0 ">Job Description</h4>
              <p style={{ marginTop: "5px" }} className={styles.yourJobDescriptionMobile}>
                {isExpanded ? specialInstructions : truncateText(specialInstructions, wordLimit)}
              </p>
              {specialInstructions.split(" ").length > wordLimit && (
                <button className={styles.seeMoreButton} onClick={() => setIsExpanded(!isExpanded)}>
                  {isExpanded ? "See Less" : "See More"}
                </button>
              )}
            </div>
            {filteredApplicantFilters?.length > 0 && (
              <div>
                <h4 className={`${styles.headerH4} mb-1`}>Candidate Criteria</h4>
                <div>
                  {filteredApplicantFilters?.map((filter, index) => {
                    let displayValue = "";
                    let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                    if (filter.field === "distance") {
                      displayValue = distanceMapping[filter.value];
                    } else if (filter.field === "age") {
                      if (filter.value === 0) {
                        displayValue = Object.values(ageMapping).join(", ");
                      } else {
                        displayValue = (filter.value as number[]).map((val) => ageMapping[val]).join(", ");
                      }
                    } else if (filter.field === "otherSkills") {
                      displayValue = (filter.value as number[]).map((val) => otherSkillsMapping[val]).join(", ");
                      displayField = "Other";
                    } else if (filter.field === "tutoringCategory") {
                      displayValue = (filter.value as number[]).map((val) => tutoringCategoryMapping[val]).join(", ");
                      displayField = "Experience";
                    }
                    return (
                      <div key={index} className={styles.criteriaItem}>
                        <span className={styles.criteriaLabel}>{displayField}:&nbsp;</span>
                        <span className={styles.criteriaValue}>{displayValue}</span>
                      </div>
                    );
                  })}
                </div>
                <Divider className="mt-4" />
              </div>
            )}
          </div>
        </div>
        <div style={{ width: "100%", paddingInline: "25px" }}>
          <h3 className="font-bold mb-1 mt-2" style={{ color: "#585858", fontSize: "24px" }}>
            Job Roster
          </h3>
          <div>
            <div
              style={{
                overflow: "hidden auto",
                maxHeight: "154px",
              }}
            >
              {/* Define weekMap if not already present */}
              {(() => {
                // Flatten all shifts from all schedules into a single array
                const allShifts = schedules.flatMap((schedule, scheduleIndex) =>
                  schedule.shifts.map((shift, shiftIndex) => ({
                    schedule,
                    shift,
                    scheduleIndex,
                    shiftIndex,
                    rate: shift.hourlyPrice !== null ? shift.hourlyPrice : shiftIndex === 0 ? 30 : 30,
                  }))
                );

                // Function to calculate time difference (from JobBreakdown)
                const getTimeDifferenceInHourMinuteFormat = (start, end) => {
                  const [startHours, startMinutes] = start.split(":").map(Number);
                  const [endHours, endMinutes] = end.split(":").map(Number);
                  const startTotalMinutes = startHours * 60 + startMinutes;
                  const endTotalMinutes = endHours * 60 + endMinutes;
                  let differenceInMinutes = endTotalMinutes - startTotalMinutes;
                  if (differenceInMinutes < 0) {
                    differenceInMinutes += 24 * 60;
                  }
                  const hours = Math.floor(differenceInMinutes / 60);
                  const minutes = differenceInMinutes % 60;
                  return parseFloat((hours + minutes / 60).toFixed(2));
                };

                // Function to convert time to 12-hour format (from ShiftRow)
                const convertTo12HourFormat = (time) => {
                  const [hourStr, minuteStr] = time.split(":");
                  const hour = parseInt(hourStr, 10);
                  const minute = parseInt(minuteStr, 10);
                  const period = hour >= 12 ? "PM" : "AM";
                  const hour12 = hour % 12 || 12;
                  return `${hour12}:${minute.toString().padStart(2, "0")} ${period}`;
                };

                // Calculate grand total hours and cost across all shifts
                const grandTotalHours = allShifts.reduce((sum, { shift }) => {
                  return sum + getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                }, 0);

                const grandTotalCost = allShifts.reduce((sum, { shift, rate }) => {
                  const hours = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                  return sum + hours * rate;
                }, 0);

                return allShifts.length > 0 ? (
                  <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "12px" }}>
                    <thead>
                      <tr>
                        <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                        <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                        <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Hours</th>
                        <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Rate</th>
                        <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allShifts.map(({ schedule, shift, scheduleIndex, shiftIndex, rate }, index) => {
                        const hours = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                        return (
                          <tr key={`${scheduleIndex}-${shiftIndex}`}>
                            <td style={{ padding: "4px", color: "#585858", fontWeight: "700", textAlign: "center" }}>
                              {weekMap.get(schedule.dayOfWeek)}
                            </td>
                            <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>
                              {convertTo12HourFormat(shift.jobStartTime)} to {convertTo12HourFormat(shift.jobEndTime)}
                            </td>
                            <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>{hours.toFixed(2)}</td>
                            <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${rate.toFixed(2)}</td>
                            <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${(hours * rate).toFixed(2)}</td>
                          </tr>
                        );
                      })}
                      {/* Grand Total Row for All Shifts */}
                      <tr style={{ fontWeight: "bold" }}>
                        <td style={{ padding: "4px", color: "#585858" }} colSpan={2}></td>
                        <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>{grandTotalHours.toFixed(2)}</td>
                        <td style={{ padding: "4px", color: "#585858" }}></td>
                        <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${grandTotalCost.toFixed(2)}</td>
                      </tr>
                    </tbody>
                  </table>
                ) : null;
              })()}
            </div>
          </div>
        </div>
        <div style={{ width: "100%", paddingInline: "25px" }}>
          {payload.managedBy === 1 && (
            <div>
              <h3 className="font-bold mt-3 mb-3" style={{ color: "#585858", fontSize: "24px" }}>
                Invited Candidates <strong style={{ color: "#197D52" }}> ({payload?.["selectedCandidates"]?.length || 0})</strong>
              </h3>

              <div className="flex  flex-wrap gap-2">
                {payload?.["selectedCandidates"]?.map((profile) => (
                  <ProfileCard
                    key={profile.id}
                    name={profile.publicName}
                    location={profile.suburb || "Location not available"}
                    jobsCompleted={profile.jobsCompleted}
                    imageUrl={profile.imageSrc || "default_image.jpg"}
                    isSelected={true} // Always true as per requirement
                  />
                ))}
              </div>
            </div>
          )}
        </div>
        {/* <button className={styles.postJobBtn}>Post Job</button> */}
        <Dialog
          header="Important Information"
          visible={visible}
          style={{
            width: "100%",
            color: "#585858",
            fontSize: "14px",
            paddingInline: "20px",
          }}
          onHide={() => {
            if (!visible) return;
            setVisible(false);
          }}
          footer={footerContent}
          draggable={false}
        >
          <p className="m-0">
            <>
              {(() => {
                const jobTypeOneOff = [1, 256];
                if (payload.managedBy === 1) {
                  if (jobTypeOneOff.includes(payload.jobType)) {
                    return (
                      <>
                        This job can be active for up to 14 days. During this time you will be able to chat online with invited candidates. If you
                        don't find the right person quickly, you can change the job details and invite more candidates to apply. When a candidate
                        applies for your job, they will be waiting to hear from you! Make sure to confirm your chosen candidate by clicking the AWARD
                        button.
                      </>
                    );
                  }
                  return (
                    <>
                      This recurring job post will be active for 30 days, during this time you will be able to chat online with invited candidates. If
                      you don’t find the right person right away you can invite more candidates to apply, change the job details and repost the job.
                      Remember, when a candidate applies for your job they will be waiting to hear from you!
                    </>
                  );
                } else {
                  if (jobTypeOneOff.includes(payload.jobType)) {
                    return (
                      <>
                        This job can be active for up to 14 days. The Juggle Assist algorithm will now invite candidates who meet your criteria and
                        invite them to your job. You will be notified each time a candidate applies for this job – these applicants will appear in
                        your job shortlist
                      </>
                    );
                  }
                  return (
                    <>
                      The Juggle Assist algorithm will now invite candidates who meet your criteria and invite them to your job. You will be notified
                      each time a candidate applies for this job – these applicants will appear in your job shortlist.
                    </>
                  );
                }
              })()}
            </>
          </p>
        </Dialog>

        <BackButtonPortal id="back-button-portal">
          <div
            onClick={() => {
              setpayload({
                ...payload,
              });
              if (payload.managedBy === 20) {
                prev("candidate-matching"); // Navigate to "candidate-matching" if managedBy is 20
              } else {
                prev("candidate-selection"); // Navigate to "candidate-selection" for specific job types
              }
            }}
          >
            <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
          </div>
        </BackButtonPortal>

        <div className={styles.fixedFooter}>
          {/* <div className={styles.jobPrice}>
            <p
              style={{
                textAlign: "end",
                color: "#585858",
                textDecoration: "underline",
              }}
            >
              <strong className={styles.jobPriceDoller}>${handleTotalPrice()}</strong> total{" "}
            </p>
          </div> */}

          <button
            className={styles.nextButtonMobile}
            onClick={() => {
              setErrorMessage(""); // Reset error message before new submission
              setVisible(true);
            }}
          >
            Post Job
          </button>
        </div>
        {/* <p className={styles.goBack} onClick={() => prevClicked(currentPayload)}>
                    <span className="pi pi-angle-left"></span>Go Back
                </p> */}
        {/* {errorMessage && <div className={styles.errorMessage}>{errorMessage}</div>} */}
      </div>
    </div>
  );
};
