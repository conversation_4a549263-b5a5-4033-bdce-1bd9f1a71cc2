.cardDiv {
  width: 366px;
  height: 144px;
  background-color: #ffff;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  position: absolute;
  left: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.cardDivSmall {
  width: 6px;
  height: 144px;
  background-color: #179d52;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.cardDivLast {
  width: 121px;
  height: 144px;
  background-color: #179d52;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.cardPara {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  line-height: 36px;
  margin: 0px;
  padding-left: 30px;
  color: #585858;
}
.cardContent {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding-top: 15px;
}
.subPara {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin: 0px;
  margin-top: 5px;
  padding-left: 30px;
  color: #585858;
}
.commencesBtn {
  width: 215px;
  height: 41px;
  font-size: 14px;
  font-weight: 700;
  background-color: #ffff;
  border-radius: 20px;
  border: 2px solid #179d52;
  color: #585858;
}
.commencesDiv {
  padding-left: 23px;
  margin-top: 10px;
}
.commences {
  color: #179d52;
  font-weight: 900;
  text-decoration: underline;
}
.jobCountDiv {
  width: 15px;
  height: 16px;
  position: absolute;
  left: 185px;
  background-color: rgba(255, 99, 89, 1);
  border-radius: 50px;
}

.jobCountText {
  font-size: 12px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  margin: 0px;
  text-align: center;
}
.jobInfoDiv {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.latestJobDateText {
  font-size: 20px;
  text-align: center;
  font-weight: 700;
  line-height: 21px;
  color: rgba(255, 255, 255, 1);
}
.dayName {
  font-size: 14px;
  text-align: center;
  font-weight: 700;
  line-height: 21px;
  color: rgba(255, 255, 255, 1);
}
.CloseBtn {
  position: absolute;
  left: 92%;
  top: -5%;
  display: flex;
  justify-content: end;
  width: 25px;
  height: 27px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  cursor: pointer;
}
