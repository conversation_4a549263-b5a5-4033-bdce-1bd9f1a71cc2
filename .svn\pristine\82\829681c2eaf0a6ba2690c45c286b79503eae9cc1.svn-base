   @media screen and (min-width: 1000px) and (max-width: 1700px) {
    .ClientCard {
        box-sizing: border-box;
        flex-wrap: nowrap !important;
    }
  }
@media screen and (min-width: 1000px) and (max-width: 1625px) {
    .xl\:col-3 {
      width: auto;
    }
  }
  @media screen and (max-width: 1024px) {
    .ClientCard {
      flex-wrap: nowrap !important;
      /* width: 134% !important; */
    }
  }
  @media screen and (max-width: 1024px) {
  .ClientCardGap{
    /* gap: 0.5rem !important; */
    flex-grow: 1;
  }
}
  @media (min-width: 1020px) and (max-width: 1250px) {
    .lg\:col-4 {
      width: 34.3333 !important;
    }
  }  
.responsive-button {
    width: 20%;
  }
  
  @media (max-width: 1950px) {
    .responsive-button {
      width: 30%;
    }
  }
  
  @media (max-width: 1200px) {
    .responsive-button {
      width: 40%;
    }
  }
  
  @media (max-width: 768px) {
    .responsive-button {
      width: 50%;
    }
  }
  
  @media (max-width: 480px) {
    .responsive-button {
      width: 60%;
    }
  }