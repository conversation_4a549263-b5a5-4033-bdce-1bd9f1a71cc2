import React from 'react';

interface CustomMarkerCardProps {
    clientFirstName?: string;
    clientLastName?: string;
    clientImageUrl?: string;
    feedback?: string;
    ratingAvg?: number;
    ratingDate?: string;
}

const CustomMarkerCard: React.FC<CustomMarkerCardProps> = ({
    clientFirstName,
    clientLastName,
    clientImageUrl,
    feedback,
    ratingAvg,
    ratingDate,
}) => {
    return (
        <div style={{ padding: '10px', backgroundColor: '#fff', borderRadius: '8px', boxShadow: '0 2px 6px rgba(0,0,0,0.2)' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                <img
                    src={clientImageUrl}
                    alt={`${clientFirstName} ${clientLastName}`}
                    style={{ width: '50px', height: '50px', borderRadius: '50%', marginRight: '10px' }}
                />
                <div>
                    <h4 style={{ margin: 0 }}>{`${clientFirstName} ${clientLastName}`}</h4>
                    <p style={{ margin: 0, fontSize: '12px', color: '#888' }}>{new Date(ratingDate).toLocaleDateString()}</p>
                </div>
            </div>
            <p style={{ margin: '10px 0', fontSize: '14px', color: '#333' }}>{feedback}</p>
            <p style={{ margin: 0, fontSize: '14px', fontWeight: 'bold' }}>Rating: {ratingAvg.toFixed(1)}</p>
        </div>
    );
};

export default CustomMarkerCard;