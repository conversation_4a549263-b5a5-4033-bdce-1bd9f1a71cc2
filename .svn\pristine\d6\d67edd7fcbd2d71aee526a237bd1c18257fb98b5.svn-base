const Api = {
    graph: {
        geoSearch: '/api/graph/geosearch',
        getConnections: '/api/graph/search',
    },
    account: {
        updateSessionInfo: '/api/account/update',
        updateUser: '/api/account',
        requestPendingStats: '/api/account/requestpendingstats',
        requestPendingStatsPartial:'/api/account/requestpendingstatspartial',
        checkSession: '/api/account/sessioninfo',
        registerUserV2: '/api/account/register',
        forgotPassword: '/api/account/requestresetpasswordbysms',
        resetPassword: '/api/account/resetpasswordbysms',
        sendmobilelink: '/api/account/sendmobilelink',
        getVerificationCode: '/api/account/sendverificationcode/',
        submitVerificationCode: '/api/account/verifycode/',
        changePassword: '/api/account/changepassword',
        hideAccount: '/api/account/hideaccount',
        logAppEvent:'/api/account/logappevent',
        requestdeleteaccount:'/api/account/requestdeleteaccount',
        profilevisibliity:'/api/account/profilevisibliity',
        contactUsRequest:'/api/account/contactus'
    },
    identity: {
        refreshToken: '/api/identity/refreshtoken',
        handleAttemptLogin: '/api/identity/token',
    },
    geo: {
        suburbSearch: '/api/geo/suburbsearch',
        addresssearch: '/api/geo/addresssearch',
        geocodeAddress: '/api/geo/geocodeaddress',
    },
    connections: {
        connection: '/api/connections',
    },

    payments: {
        payments:'/api/payments',
        invoices:'/api/payments/invoices'
    },

    client: {
        client: '/api/jobs/client',
        upComing: '/api/jobs/client/upcoming',
        completed: '/api/jobs/client/completed',
        unratedJobs: '/api/jobs/client/unratedjobs',
        AwardRecurringJob:'/api/jobs/client/awardRecurringJob'
    },
    provider: {
        provider: '/api/jobs/provider',
        upComing: '/api/jobs/provider/upcoming',
        completed: '/api/jobs/provider/completed',
        unratedJobs: '/api/jobs/provider/unratedjobs',
    },
    profiles: {
        provider: '/api/profiles/provider',
        client:'/api/profiles/client'
    },
    jobs: {
        provider: '/api/jobs/provider',
        client:'/api/jobs/client'
    },
    chats:{
        conversations:'/api/conversations'
    },
    timesheet:{
        timesheet:'/api/Timesheet/list',
        timesheetdetails:'/api/Timesheet/details',
        updatehistory:'/api/Timesheet/updatehistory',
    }
};

export default Api;
