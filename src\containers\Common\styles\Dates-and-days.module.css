.DatesContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    -webkit-user-select: none;
    user-select: none;
    padding-inline: 120px;
    width: 100%;
    padding-top: 55px;
  }
  .DatesContainerSec{

  }
  .DateTitle{
    font-size: 30px;
    font-weight: 700;
    color: #585858;
    line-height: 45px;
    margin-bottom: 5px;
  }
  
  .DatesOptions {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    
  }
  
  .DatesOption {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 196px;
    height: 56px;
    border: 1px solid #dfdfdf !important;
    border-radius: 10px;
    padding-inline: 5px;
    cursor: pointer;
  }
  .selectedOption {
    border: 2px solid #179D52 !important; 
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  }


  
  .dividerPostDetails{
    margin-block: 22px;
    width: 667px !important;
  }
  
  .DatesCalendar {
    margin-top: 1rem;
  }
  
  .customCalendar {
    background-color: transparent !important;
    display: flex !important;
    flex-direction: row !important;
    height: 56px !important;
    align-items: unset !important;
    margin-top: 10px !important;
    width: 667px !important;
  }
  .customCalendar button {
    border: none !important;
    border-radius: 0 !important;
    background-color: #ffffff !important;
    color: #585858 !important;
    box-shadow: none !important;
    border-left: 1px solid #dfdfdf !important;
    border-top: 1px solid #dfdfdf !important;
    border-bottom: 1px solid #dfdfdf !important;
    border-top-left-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
    padding-left: 35% !important;

  }
  .customCalendar input {
    border: none !important;
    border-radius: 0 !important;
    background-color: #ffffff !important;
    box-shadow: none !important;
    border-right: 1px solid #dfdfdf !important;
    border-top: 1px solid #dfdfdf !important;
    border-bottom: 1px solid #dfdfdf !important;
    border-top-right-radius: 10px !important;
    border-bottom-right-radius: 10px !important;
    background-color: transparent !important;
    outline-color: unset !important;
    height: 56px !important;
    font-weight: 400 !important;
    font-size: 16px !important;
    width: min-content !important;
    margin-left: -1px !important;
   

  }
  .customCalendar input:enabled:focus {
    background-color: transparent !important;
    box-shadow: none !important;
    border-right: 1px solid #dfdfdf !important;
    border-top: 1px solid #dfdfdf !important;
    border-bottom: 1px solid #dfdfdf !important;
    border-left: none !important;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
  }
  .greenBorder input,
.greenBorder input:enabled:focus {
  border-right: 2px solid #179d52 !important;
  border-top: 2px solid #179d52 !important;
  border-bottom: 2px solid #179d52 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}
.greenBorder button {
  border-left: 2px solid #179d52 !important;
  border-top: 2px solid #179d52 !important;
  border-bottom: 2px solid #179d52 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}
.selectedBorder input,
.selectedBorder input:enabled:focus {
  border-right: 2px solid #179d52 !important;
  border-top: 2px solid #179d52 !important;
  border-bottom: 2px solid #179d52 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  box-shadow: -4px 4px 4px rgba(0, 0, 0, 0.25) !important;
}
.selectedBorder button {
  border-left: 2px solid #179d52 !important;
  border-top: 2px solid #179d52 !important;
  border-bottom: 2px solid #179d52 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  box-shadow: -4px 4px 4px rgba(0, 0, 0, 0.25) !important;
}
  .radioInput {
    appearance: none;
    -webkit-appearance: none; /* For Safari */
    width: 18px !important;
    height: 18px !important;
    border: 1px solid #dfdfdf; /* Gray border when not selected */
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: border-color 0.3s ease, background-color 0.3s ease;
  }
  
  .radioInput:checked {
    border: 1px solid #179D52;
    background-color: #FFFFFF;
  }
  
  .radioInput:checked::after {
    content: "";
    display: block;
    width: 14px;
    height: 14px;
    background-color: #179D52; /* Inner green dot when selected */
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .dropdownWeek{
    width: 92px;
    height: 56px;
    border: 1px solid #dfdfdf !important;
    border-radius: 10px !important;
    margin-left: 10px;
  }
  .icon{
    width: 18px;
    height: 18px;
  }
  .dropdownWeek > span{
    width: 92px;
    height: 56px;
    background-color: transparent !important;
    padding-top: 15px;
    padding-inline: 0px;
    padding-left: 22px;
  }

  .dropdownWeek div[class="p-dropdown-trigger"]
  {
    width: 20px !important;
    padding-right: 5px !important;
  }
  .jobTitle{
    margin-top: 0px;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    margin-bottom: 8px
  }

  .DatesSecondDiv {
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  .dropdownWeekSec{
    width: 160px;
    height: 56px;
    border: 1px solid #dfdfdf !important;
    border-radius: 10px !important;
    margin-left: 10px;
  }
  .icon{
    width: 18px;
    height: 18px;
  }
  .dropdownWeekSec > span{
    width: 92px;
    height: 56px;
    background-color: transparent !important;
    padding-top: 15px;
    padding-inline: 0px;
    padding-left: 22px;
  }

  .dropdownWeekSec div[class="p-dropdown-trigger"]
  {
    width: 20px !important;
    padding-right: 5px !important;
  }
  
  .DatesThirdDiv {
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  .jobDetailsDateLabel{
    color: #585858;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
  
  .weekdayContainer {
    display: flex;
    gap: 5px;
    margin-top: 15px;
    margin-bottom: 50px;
  }
  
  .weekdayButton {
    background-color: white;
    border: 1px solid #dfdfdf; 
    border-radius: 10px;
    color: #585858;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 12px;
    width: 121px;
    height: 56px;
    cursor: pointer;
    line-height: 24px;
  }
  
  .weekdayButton.selected {
    border:3px solid #179D52;
  }
  .monthBorder{
    border: 2px solid #179D52 !important;
  }
  .jobTitleDes{
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    color: #585858;
    margin: 0px;
  }
  .footer {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: auto;
  }
  
  .footerActions {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    padding-bottom: 25px;
  }
  

  /* Medium Screens (Tablets) */
  @media (max-width: 768px) {
    .DatesContainer {
      padding: 1rem;
    }
  
    .DatesOptions {
      gap: 0.75rem;
    }
  
    .customCalendar input {
      width: 100% !important; /* Adjust to full width */
      font-size: 14px !important; /* Slightly smaller font */
    }
  
    .DatesSecondDiv, .DatesThirdDiv {
      padding: 0.75rem;
    }
  }
  
  /* Small Screens (Mobile) */
  @media (max-width: 480px) {
    .DatesContainer {
      padding: 0.5rem;
    }
  
    .DatesOptions {
      gap: 0.5rem;
    }
  
    .customCalendar input {
      font-size: 12px !important; /* Smaller font for smaller screens */
    }
  
    .DatesSecondDiv, .DatesThirdDiv {
      padding: 0.5rem;
    }
  }
  