import React from 'react';
import { FaLock, FaHome } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

const Unauthorized: React.FC = () => {
    const navigate = useNavigate();
    return (
        <div className='flex flex-column align-items-center justify-content-center min-h-screen text-green-900 p-4'>
            <div className='text-8xl font-bold text-green-700'>403</div>

            <div className='flex align-items-center justify-content-center'>
                <FaLock size={64} className='text-orange-500 mr-3' />
                <p className='text-2xl font-semibold'>Access Denied</p>
            </div>

            <p className='text-lg text-center'>
                Looks like you've stumbled into a restricted area. <br />
                Time to make a strategic retreat!
            </p>

            <button
                className='flex align-items-center justify-content-centerS text-white px-4 py-3 border-round 
        hover:bg-orange-600 hover:shadow-2 transition-duration-300 border-none'
                style={{
                    backgroundColor: '#f56f22',
                }}
                onClick={(e) => {
                    e.preventDefault();
                    navigate('/');
                }}
            >
                <FaHome size={20} className='mr-2' />
                Return to Safety
            </button>
        </div>
    );
};

export default Unauthorized;
