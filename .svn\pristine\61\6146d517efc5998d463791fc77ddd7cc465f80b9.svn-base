import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import CustomButton from "../../../commonComponents/CustomButton";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import useLoader from "../../../hooks/LoaderHook";
import { updateSessionInfo } from "../../../store/tunks/sessionInfoTunk";
import c from "../../../helper/juggleStreetConstants";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";
const Citizenship = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const session = useSelector((state: RootState) => state.sessionInfo.data);
  const [citizenship, setCitizenship] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("");
  const { disableLoader, enableLoader } = useLoader();
  const [countryError, setCountryError] = useState("");
  const [nationality, setNationality] = useState("");
  const {isMobile}=useIsMobile()
  useEffect(() => {
    if (session?.['nationality']) {
      setNationality(session?.['nationality']);
      if (session?.['nationality'] === "AU") {
        setCitizenship("AU");
      } else if (session?.['nationality'] === "NZ") {
        setCitizenship("NZ");
      } else {
        setCitizenship("OTHER");
        setSelectedCountry(session?.['nationality'].toUpperCase());
      }
    }
  }, [session]);
  const citizenshipOptions = [
    {
      value: "AU",
      label: "Australian Citizen",
    },
    {
      value: "NZ",
      label: "New Zealand Citizen",
    },
    {
      value: "OTHER",
      label: "Other Nationality",
    },
  ];

  const handleCitizenshipChange = (event) => {
    const value = event.target.value;
    setCitizenship(value);
    setCountryError(""); // Reset country error when citizenship changes

    if (value === "AU") {
      setNationality("AU");
    } else if (value === "NZ") {
      setNationality("NZ");
    } else {
      setNationality("");
    }
  };

  const handleCountryChange = (event) => {
    const selectedCountryValue = event.target.value;
    setSelectedCountry(selectedCountryValue);
    setCountryError(""); // Reset country error when country changes
    const selectedCountryIso = c.countriesIso.find(
      (country) => country.value === selectedCountryValue
    );
    if (selectedCountryIso) {
      setNationality(selectedCountryIso.value.toLowerCase());
    }
  };

  const handleNext = () => {
    if (citizenship === 'Other Nationality' && !selectedCountry) {
      setCountryError('Please select a country.');
      return;
    }
    const payload = {
      ...session,
      nationality: nationality,
    };
    
    enableLoader();
    dispatch(updateSessionInfo({ payload: payload })).finally(() => {
      disableLoader();
      // dispatch(incrementProfileActivationStep());
    });
  };

  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  const handleprev = () => {
    dispatch(decrementProfileActivationStep());
  };

  return (
    <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
      <ProfileCompletenessHeader
        title="Citizenship"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div style={{ maxWidth: "100%", width: "800px", paddingInline:isMobile && "15px" }}>
        <div>
          <h1
            className="p-0 m-0 txt-clr flex-wrap font-bold line-height-1"
            style={{ fontSize: "18px" }}
          >
            Citizenship
          </h1>
        </div>
        <div className="mt-3 flex flex-column ">
          {citizenshipOptions.map((option, index) => (
            <div key={index}>
              <input
                type="radio"
                id={option.value}
                name="citizenship"
                value={option.value}
                checked={citizenship === option.value}
                onChange={handleCitizenshipChange}
                className="cursor-pointer"
              />
              <label
                htmlFor={option.value}
                className="cursor-pointer txt-clr font-semibold"
                style={{ fontSize: "16px" }}
              >
                {option.label}
              </label>
            </div>
          ))}
          {citizenship === "OTHER" && (
            <div className="pl-3">
              <select
                value={selectedCountry}
                onChange={handleCountryChange}
                className="cursor-pointer px-3 mt-2"
                style={{
                  width: "200px",
                  height: "40px",
                  borderRadius: "10px",
                  border: "1px solid #585858",
                }}
              >
                <option value="">Select a country</option>
                {c.countriesIso.map((country) => (
                  <option key={country.id} value={country.value}>
                    {country.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
      {countryError && (
        <p className="p-0 m-0" style={{ color: "red" }}>
          {countryError}
        </p>
      )}
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left"></i>
              Previous
            </>
          }
          onClick={handleprev}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
             margin:isMobile && "5px"
          }}
        />
        <div style={{ flexGrow: 1 }} />

        <CustomButton
          className={styles.hoverClass}
          data-skip={citizenship ? "false" : "true"}
          onClick={citizenship ? handleNext : handleSkip}
          label={
            <>
              {citizenship ? "Next" : "Skip"}
              <i
                className={`pi pi-angle-${citizenship ? "right" : "right"}`}
                style={{ marginLeft: "8px" }}
              ></i>
            </>
          }
          style={
            citizenship
              ? {
                backgroundColor: "#FFA500",
                color: "#fff",
                width: "156px",
                height: "39px",
                fontWeight: "800",
                fontSize: "14px",
                borderRadius: "8px",
                border: "2px solid transparent",
                boxShadow: "0px 4px 12px #00000",
                transition:
                  "background-color 0.3s ease, box-shadow 0.3s ease",
              margin : isMobile && "10px"
              }
              : {
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontWeight: "400",
                fontSize: "14px",
                borderRadius: "10px",
                border: "1px solid #F0F4F7",
              margin : isMobile && "10px"
              }
          }
        />
      </footer>
    </div>
  );
};

export default Citizenship;
