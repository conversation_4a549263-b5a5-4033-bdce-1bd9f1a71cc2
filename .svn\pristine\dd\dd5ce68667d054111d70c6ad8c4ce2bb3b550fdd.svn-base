import { ProgressBar } from 'primereact/progressbar';
import { Children as ChildrenInfo, CSSProperties, useEffect, useMemo, useState } from 'react';
import '../styles/add-children.css';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import {
    decrementProfileActivationStep,
    incrementProfileActivationStep,
} from '../../../store/slices/applicationSlice';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import useLoader from '../../../hooks/LoaderHook';
import { HiPencil } from 'react-icons/hi2';
import useIsMobile from '../../../hooks/useIsMobile';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';

interface FloatingLabelInputProps {
    label: string;
    type?: string;
    style?: CSSProperties;
    onChange: (value: string) => void;
    noLabel?: boolean;
    length?: number;
    value: string | null;
    onlyText?: boolean;
}

const FloatingLabelInput: React.FC<FloatingLabelInputProps> = ({
    label,
    type = 'text',
    style,
    onChange,
    noLabel = false,
    length = -1,
    value = null,
    onlyText = false,
}) => {
    const [isFocused, setIsFocused] = useState(false);
    const [inputValue, setInputValue] = useState('');

    useEffect(() => {
        if (value !== null) {
            setInputValue(value);
        }
    }, [value]);

    const labelStyle: CSSProperties = {
        position: 'absolute',
        top: inputValue || isFocused ? '-10px' : '10px',
        left: '10px',
        fontSize: inputValue || isFocused ? '12px' : '16px',
        color: '#585858',
        background: '#F0F4F7',
        transition: '0.2s ease all',
        pointerEvents: 'none',
        padding: '0 5px',
        borderRadius: '10px',
    };

    const inputStyle: CSSProperties = {
        width: '100%',
        padding: '10px',
        fontSize: '16px',
        border: isFocused || inputValue.length > 0 ? '2px solid #179D52' : '2px solid #F0F4F7',
        borderRadius: '10px',
        outline: isFocused ? 'none' : '',
        backgroundColor: '#F0F4F7',
        ...style,
    };

    const containerStyle: CSSProperties = {
        position: 'relative',
        display: 'inline-block',
        width: '100%',
        margin: '10px 0',
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let newValue = e.target.value;

        if (onlyText) {
            newValue = newValue.replace(/[^a-zA-Z]/g, '');
        }

        if (length === -1 || newValue.length <= length) {
            setInputValue(newValue);
            onChange(newValue);
        }
    };

    return (
        <div style={containerStyle}>
            {!noLabel && <label style={labelStyle}>{label}</label>}
            <input
                type={type}
                style={inputStyle}
                value={inputValue}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(inputValue !== '')}
                placeholder={label}
                onChange={handleInputChange}
                maxLength={length !== -1 ? length : undefined}
            />
        </div>
    );
};

interface RadioButtonProps {
    value: number;
    id: string;
    checked: boolean;
    label: string;
    onChange: (value: number) => void;
}

const RadioButton: React.FC<RadioButtonProps> = ({ value, label, checked, onChange }) => {
    return (
        <div
            style={{
                border: !checked ? '1px solid #DFDFDF' : '2px solid #179D52',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '12px 15px',
                gap: '10px',
                cursor: 'pointer',
                borderRadius: '10px',
                backgroundColor: 'white',
                transition: 'background-color 0.2s ease',
            }}
            onClick={() => onChange(value)}
        >
            <div
                style={{
                    width: '15px',
                    height: '15px',
                    borderRadius: '50%',
                    border: !checked ? '1px solid #585858' : '1px solid #179D52',
                    backgroundColor: checked ? '#179D52' : 'white', // Fill circle with color if checked
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                {checked && (
                    <div
                        style={{
                            width: '12px',
                            height: '12px',
                            borderRadius: '50%',
                            backgroundColor: '#179D52',
                        }}
                    />
                )}
            </div>
            <label
                style={{ cursor: 'pointer', fontSize: '14px', color: '#585858', fontWeight: '500' }}
            >
                {label}
            </label>
        </div>
    );
};

interface AddChildrenProps { }

interface ChildrenInfo {
    name: string;
    birthMonth: number;
    birthYear: number;
    gender: number;
    about: string;
    allergyDetails?: string;
}

const AddChildrenWelcome = ({ onAddClicked }: { onAddClicked: () => void }) => {
    const sessionInfo = useSelector<RootState>((state) => state.sessionInfo.data);
    const dispatch = useDispatch<AppDispatch>();
    const applicationSate = useSelector<RootState>((state) => state.applicationState.profileActivationCurrentStep);
    const { isMobile } = useIsMobile()
    return (
        <div className={!isMobile ? `${'add-children-container'}` : `${'add-children-container-mobile'}`}>
            <div className='main-contents-container'>
                {!isMobile ? (<h1 className='headerH1'>Add Your Children</h1>) : (
                    <HorizontalNavigation
                        title='Add Your Children'
                        onBackClick={() => {

                            if (applicationSate === 1) {
                                window.location.href = '/';
                            } else {
                                dispatch(decrementProfileActivationStep());
                            }
                        }}
                    />
                )}
                <div className='progress-bar-style'>
                    <ProgressBar value={sessionInfo['profileCompleteness']} />
                </div>
                <div className='progress-info'>
                    <span>Your Profile is </span>
                    <span>{sessionInfo['profileCompleteness']}% complete</span>
                </div>
                <div
                    className={`scrollable-area ${isMobile ? 'scrollable-area-mobile' : ""}`}
                    style={{
                        marginBottom: '75px',
                    }}
                >
                    <div className='info-container'>
                        <div>
                            <h1 className='headerH1'>Your Children</h1>
                            <p>
                                It is essential Juggle Street helpers know the number of children
                                you have and how old they are.
                            </p>
                            <p>
                                We ask for the birth month and year so your child’s age is always
                                up-to-date. Only your child’s age is displayed, not the birth month.
                            </p>
                        </div>
                    </div>
                    <div
                        className='add-child'
                        onClick={onAddClicked}
                        style={{
                            marginInline: 'auto',
                        }}
                    >
                        <i className='pi pi-plus' />
                        <p>Add Child One</p>
                    </div>
                </div>
                <div style={{ flexGrow: 1 }} />
                <div className={`button-container ${isMobile ? 'button-container-mobile' : ""}`}>
                    {applicationSate !== 1 && (
                        <div onClick={() => dispatch(decrementProfileActivationStep())}>
                            <i className='pi pi-angle-left' />
                            <button
                                style={{
                                    color: '#585858',
                                    fontSize: '14px',
                                    fontWeight: '500',
                                }}
                            >
                                Previous
                            </button>
                        </div>
                    )}
                    <div
                        data-skip
                        style={{
                            border: '1px solid #F0F4F7',
                            borderRadius: '10px',
                            width: '156px',
                        }}
                        className='ml-auto'
                        onClick={() => dispatch(incrementProfileActivationStep())}
                    >
                        <button>Skip</button>
                        <i className='pi pi-angle-right' />
                    </div>
                </div>
            </div>
        </div>
    );
};

const AddChild = ({
    childIndex,
    onSubmit,
    prevChildren = {
        name: '',
        birthMonth: -1,
        birthYear: -1,
        gender: -1,
        about: '',
        allergyDetails: '',
    },
    onCancel,
}: {
    childIndex: number;
    onSubmit: (data: ChildrenInfo, index) => void;
    prevChildren?: ChildrenInfo;
    onCancel: () => void;
}) => {
    const [childrenData, setChildrenData] = useState<ChildrenInfo>(prevChildren);
    const [dataValid, setDataValid] = useState<boolean>(false);
    const { isMobile } = useIsMobile();
    const sessionInfo = useSelector<RootState>((state) => state.sessionInfo.data);
    const prevChildrenData = prevChildren;

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const [selectedAllergies, setSelectedAllergies] = useState<boolean>(false);

    const availableYears = useMemo(() => {
        return Array.from({ length: 50 }, (_, index) => (currentYear - index).toString());
    }, [currentYear]);

    const availableMonths = useMemo(() => {
        if (childrenData.birthYear === -1) return [];
        const yearNumber = childrenData.birthYear;
        const monthsInYear = yearNumber === currentYear ? currentDate.getMonth() + 1 : 12;
        return Array.from({ length: monthsInYear }, (_, index) => {
            const monthNumber = index + 1;
            return {
                value: String(monthNumber).padStart(2, '0'),
                label: new Date(0, monthNumber - 1).toLocaleString('default', {
                    month: 'long',
                }),
            };
        });
    }, [childrenData.birthYear, currentYear, currentDate]);

    const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setChildrenData((prev) => {
            const updatedData = { ...prev, birthMonth: -1 };
            validateData(updatedData);
            return updatedData;
        });
    };

    const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setChildrenData((prev) => {
            const updatedData = { ...prev, birthMonth: parseInt(e.target.value) };
            validateData(updatedData);
            return updatedData;
        });
    };

    useEffect(() => {
        validateData(prevChildren);
    }, [prevChildren]);

    useEffect(() => {
        validateData(childrenData);
    }, [selectedAllergies]);
    

    const validateData = (updatedData: ChildrenInfo) => {
        const isAllergyDetailsValid = !selectedAllergies || (selectedAllergies && updatedData.allergyDetails?.trim().length > 0);
        setDataValid(
            updatedData.name.length > 0 &&
            updatedData.birthMonth > 0 &&
            updatedData.birthYear > 1000 &&
            updatedData.gender >= 0 &&
            isAllergyDetailsValid && // Add this condition
            updatedData.about.length > 0 &&
            (updatedData.name !== prevChildrenData.name ||
                updatedData.about !== prevChildrenData.about ||
                parseInt(updatedData.birthYear.toString()) !== prevChildrenData.birthYear ||
                parseInt(updatedData.birthMonth.toString()) !== prevChildrenData.birthMonth ||
                updatedData.gender !== prevChildrenData.gender)
        );
    };

    const handleInputChange = (key: keyof ChildrenInfo, value: any) => {
        setChildrenData((prev) => {
            const updatedData = { ...prev, [key]: value };
            validateData(updatedData);
            return updatedData;
        });
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        onSubmit(childrenData, childIndex);
    };
    const handleReset = (e: React.FormEvent<HTMLFormElement>) => {
        if (e !== null) {
            e.preventDefault();
        }
        onCancel();
    };

    return (
        <div className={`add-new-child-container  ${isMobile ? 'add-new-child-container-mobile' : ""}`}>
            {!isMobile ? (
                <h1 className='headerH1'>Add Your Children</h1>
            ) : (
                <HorizontalNavigation
                    title='Add Your Children'
                    onBackClick={() => {
                        handleReset(null)
                    }}
                />
            )}
            <div className='progress-bar-style'>
                <ProgressBar value={sessionInfo['profileCompleteness']} />
            </div>
            <div className='progress-info'>
                <span>Your Profile is </span>
                <span>{sessionInfo['profileCompleteness']}% complete</span>
            </div>
            <h3>Child {childIndex + 1}</h3>
            <form onSubmit={handleSubmit} style={{ display: 'flex' }} onReset={handleReset}>
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                    <FloatingLabelInput
                        label='First Name'
                        style={{ maxWidth: '362px', minWidth: '50px', color: '#585858' }}
                        onChange={(e) => handleInputChange('name', e)}
                        value={childrenData.name !== '' ? childrenData.name : null}
                        onlyText={true}
                    />
                    <h3 style={{ fontWeight: '400', marginBottom: '10px' }}>Date of Birth</h3>
                    <div
                        style={{
                            display: 'flex',
                            width: '100%',
                            gap: '15px',
                            alignItems: 'center',
                        }}
                    >
                        <select
                            value={childrenData.birthYear !== -1 ? childrenData.birthYear : ''}
                            onChange={(e) => {
                                handleYearChange(e);
                                handleInputChange('birthYear', e.target.value);
                            }}
                            style={{
                                padding: '10px',
                                borderRadius: '10px',
                                height: '56px',
                                width: '90px',
                                textAlign: 'center',
                                border:
                                    childrenData.birthYear > 0
                                        ? '2px solid #179d52'
                                        : '2px solid #f0f4f7',
                                outline: 'none',
                                backgroundColor: '#f0f4f7',
                                color: '#585858',
                                fontWeight: '400',
                                fontSize: '16px',
                            }}
                        >
                            <option
                                value=''
                                disabled
                                style={{
                                    appearance: 'none',
                                }}
                            >
                                Year
                            </option>
                            {availableYears.map((year) => (
                                <option
                                    key={year}
                                    value={year}
                                    style={{
                                        appearance: 'none',
                                    }}
                                >
                                    {year}
                                </option>
                            ))}
                        </select>
                        <select
                            value={
                                childrenData.birthMonth !== -1
                                    ? String(childrenData.birthMonth).padStart(2, '0')
                                    : ''
                            }
                            onChange={(e) => {
                                handleMonthChange(e);
                                handleInputChange('birthMonth', e.target.value);
                            }}
                            disabled={childrenData.birthYear === -1}
                            style={{
                                padding: '10px',
                                borderRadius: '10px',
                                height: '56px',
                                // minWidth: '86px',
                                width: '120px',
                                textAlign: 'center',
                                border:
                                    childrenData.birthMonth > 0
                                        ? '2px solid #179d52'
                                        : '2px solid #f0f4f7',
                                outline: 'none',
                                backgroundColor: '#f0f4f7',
                                color: '#585858',
                                fontWeight: '400',
                                fontSize: '16px',

                                maxWidth: '120px',
                            }}
                        >
                            <option
                                value=''
                                disabled
                                style={{
                                    appearance: 'none',
                                }}
                            >
                                Month
                            </option>
                            {availableMonths.map((month) => (
                                <option
                                    key={month.value}
                                    value={month.value}
                                    style={{
                                        appearance: 'none',
                                    }}
                                >
                                    {month.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    <h3 style={{ fontWeight: '400', marginBlock: '5px', marginTop: '15px' }}>
                        Gender
                    </h3>
                    <div
                        style={{
                            display: 'flex',
                            width: '100%',
                            flexWrap: 'wrap',
                            gap: '10px',
                            marginTop: '5px',
                        }}
                    >
                        <RadioButton
                            id={`gender-female-${childIndex}`}
                            checked={childrenData.gender === 2}
                            value={2}
                            label='Female'
                            onChange={(value) => handleInputChange('gender', value)}
                        />
                        <RadioButton
                            id={`gender-male-${childIndex}`}
                            checked={childrenData.gender === 1}
                            value={1}
                            label='Male'
                            onChange={(value) => handleInputChange('gender', value)}
                        />
                        <RadioButton
                            id={`gender-prefer-not-say-${childIndex}`}
                            checked={childrenData.gender === 0}
                            value={0}
                            label='Prefer Not To Say'
                            onChange={(value) => handleInputChange('gender', value)}
                        />
                    </div>
                    <h4
                        className='p-0'
                        style={{
                            fontWeight: '700',
                            fontSize: '16px',
                            margin: '0',
                            marginBlock: '10px',
                        }}
                    >
                        Allergies
                    </h4>
                    <div className='flex gap-2'>
                        <RadioButton
                            checked={selectedAllergies}
                            value={1}
                            label='Yes'
                            id='allergies'
                            onChange={(value) => setSelectedAllergies(Boolean(value))}
                        />
                        <RadioButton
                            checked={!selectedAllergies}
                            value={0}
                            label='No'
                            id='allergies'
                            onChange={(value) => {
                                setSelectedAllergies(Boolean(value));
                                if (!Boolean(value)) { // When "No" is selected
                                    handleInputChange('allergyDetails', ''); // Clear allergyDetails
                                }
                            }}
                        />
                    </div>
                    {selectedAllergies && (
                        <textarea
                            className='mt-3 add-new-child-text-area'
                            value={childrenData?.allergyDetails}
                            style={{
                                height: '74px',
                                resize: 'none',
                                borderRadius: '10px',
                                border:
                                    childrenData?.allergyDetails?.length > 0
                                        ? '2px solid #179d52'
                                        : '2px solid #179d52',
                                backgroundColor: 'rgba(241, 241, 241, 0.5)',
                                padding: '5px 10px',
                                outline: 'none',
                                color: '#585858',
                                fontSize: '12px',
                            }}
                            placeholder="What does your babysitter need to know about your child's allergies?"
                            onChange={(e) => {
                                const value = e.target.value;
                                const wordCount = value
                                    .trim()
                                    .split(/\s+/)
                                    .filter((word) => word.length > 0).length;

                                if (wordCount > 0 || value.trim().length === 0) {
                                    handleInputChange('allergyDetails',
                                        value.trim().length > 0 ? value : ''
                                    );
                                }
                            }}
                        />
                    )}
                    <h3 style={{ fontWeight: '400', marginBlock: '5px', marginTop: '10px' }}>
                        A brief intro about your child
                    </h3>
                    <div style={{ flexGrow: 1, width: '100%', height: '114px', color: '#585858' }}>
                        <textarea
                            value={childrenData.about}
                            className='add-new-child-text-area'
                            style={{ color: '#585858' }}
                            placeholder='Tell us something special about your child'
                            onChange={(e) => {
                                const value = e.target.value;
                                const wordCount = value
                                    .trim()
                                    .split(/\s+/)
                                    .filter((word) => word.length > 0).length;

                                if (wordCount > 0 || value.trim().length === 0) {
                                    handleInputChange(
                                        'about',
                                        value.trim().length > 0 ? value : ''
                                    );
                                }
                            }}
                        />
                    </div>
                    <div
                        style={{
                            width: '100%',
                            display: 'flex',
                            flexDirection: 'row-reverse',
                            justifyContent: 'flex-end',
                            gap: '10px',
                            marginTop: '12px',
                        }}
                    >
                        <button
                            type='submit'
                            disabled={!dataValid}
                            style={{
                                marginTop: '10px',
                                maxWidth: '156px',
                                width: '100%',
                                height: '39px',
                                boxShadow: dataValid ? '0 4px 4px 0 rgba(0,0,0,0.25)' : '',
                                backgroundColor: dataValid ? '#FFA500' : '#F0F4F7',
                                border: 'none',
                                borderRadius: '10px',
                                padding: '10px',
                                fontWeight: '800',
                                fontSize: '14px',
                                color: dataValid ? '#FFFFFF' : '#5858584D',
                                cursor: dataValid ? 'pointer' : 'not-allowed',
                            }}
                        >
                            Save
                        </button>
                        <button
                            type='reset'
                            style={{
                                marginTop: '10px',
                                maxWidth: '156px',
                                width: '100%',
                                height: '39px',
                                backgroundColor: 'transparent',
                                borderRadius: '10px',
                                padding: '10px',
                                fontWeight: '800',
                                fontSize: '14px',
                                color: 'rgba(0,0,0,0.75)',
                                cursor: 'pointer',
                                border: '1px solid rgba(0,0,0,0.75)',
                            }}
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    );
};

const AllChildrenView = ({
    allChildrens,
    onEditClicked,
    onRemoveClicked,
    onAddNewClicked,
}: {
    allChildrens: ChildrenInfo[];
    onEditClicked: (index: number) => void;
    onRemoveClicked: (index: number) => void;
    onAddNewClicked: () => void;
}) => {
    const sessionInfo = useSelector<RootState>((state) => state.sessionInfo.data);
    const dispatch = useDispatch<AppDispatch>();
    const applicationSate = useSelector<RootState>((state) => state.applicationState.profileActivationCurrentStep);
    const { isMobile } = useIsMobile()
    const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
    ];

    function showNext() {
        return allChildrens.length > 0;
    }
    return (
        <div className={`all-children-view ${isMobile ? 'all-children-view-mobile' : ""}`}>
            {!isMobile ? (
                <h1 className='headerH1'>Add Your Children</h1>
            ) : (
                <HorizontalNavigation
                    title='Add Your Children'
                    onBackClick={() => {
                 
                        if (applicationSate === 1) {
                            window.location.href = '/';
                        } else {
                            dispatch(decrementProfileActivationStep());
                        }
                    }}
                />
            )}
            <div className='progress-bar-style'>
                <ProgressBar value={sessionInfo['profileCompleteness']} />
            </div>
            <div className='progress-info'>
                <span>Your Profile is </span>
                <span>{sessionInfo['profileCompleteness']}% complete</span>
            </div>
            <div
                style={{
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    marginTop: '20px',
                }}
            >
                {allChildrens.map((children, index) => {
                    return (
                        <div key={index}>
                            <div
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    alignContent: 'center',
                                    gap: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <h3
                                    style={{
                                        margin: 0,
                                    }}
                                >
                                    Child {index + 1}
                                </h3>
                                <i
                                    className='pi pi-check'
                                    style={{
                                        fontSize: '8px',
                                        borderRadius: '50%',
                                        backgroundColor: '#179D52',
                                        border: 'none',
                                        color: '#FFFFFF',
                                        padding: '5px',
                                    }}
                                />
                            </div>
                            <div
                                className='hideScrollBar'
                                style={{
                                    width: '100%',
                                    display: 'flex',
                                    overflow: 'hidden',
                                    alignItems: 'center',
                                    gap: '10px',
                                    overflowX: 'auto',
                                }}
                            >
                                <input
                                    type='text'
                                    disabled
                                    name='name'
                                    id='name'
                                    defaultValue={children.name}
                                    style={{
                                        border: 'none',
                                        maxWidth: '150px',
                                        backgroundColor: '#F0F4F7',
                                        padding: '10px 20px',
                                        borderRadius: '10px',
                                        color: '#585858',
                                        fontWeight: '500',
                                        fontSize: '16px',
                                        outline: 'none',
                                    }}
                                />
                                <input
                                    type='text'
                                    disabled
                                    name='month'
                                    id='month'
                                    defaultValue={months[children.birthMonth - 1]}
                                    style={{
                                        border: 'none',
                                        maxWidth: '70px',
                                        backgroundColor: '#F0F4F7',
                                        padding: '10px 20px',
                                        borderRadius: '10px',
                                        color: '#585858',
                                        fontWeight: '500',
                                        fontSize: '16px',
                                        textAlign: 'center',
                                        outline: 'none',
                                    }}
                                />
                                <input
                                    type='text'
                                    disabled
                                    name='year'
                                    id='year'
                                    defaultValue={children.birthYear}
                                    style={{
                                        border: 'none',
                                        maxWidth: '80px',
                                        backgroundColor: '#F0F4F7',
                                        padding: '10px 20px',
                                        borderRadius: '10px',
                                        color: '#585858',
                                        fontWeight: '500',
                                        fontSize: '16px',
                                        textAlign: 'center',
                                        outline: 'none',
                                    }}
                                />
                                <button
                                    style={{
                                        width: '31px',
                                        height: '31px',
                                        borderRadius: '50%',
                                        backgroundColor: '#FFA500',
                                        border: 'none',
                                        color: '#FBFBFB',
                                        cursor: 'pointer',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                    onClick={() => onEditClicked(index)}
                                >
                                    <HiPencil fontSize={24} />
                                </button>
                                <button
                                    style={{
                                        width: '31px',
                                        height: '31px',
                                        borderRadius: '50%',
                                        backgroundColor: 'transparent',
                                        border: '1px solid #585858',
                                        color: '#585858',
                                        cursor: 'pointer',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                    onClick={() => onRemoveClicked(index)}
                                >
                                    <i className='pi pi-times' />
                                </button>
                            </div>
                        </div>
                    );
                })}
            </div>
            <h3
                style={{
                    marginTop: '20px',
                    marginBottom: '10px',
                    fontSize: '16px',
                    fontWeight: '600',
                }}
            >
                Add another child
            </h3>
            <button
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#F0F4F7',
                    border: '2px solid #FFA500',
                    padding: '10px 20px',
                    borderRadius: '10px',
                    gap: '10px',
                    color: '#585858',
                    fontWeight: '500',
                    fontSize: '16px',
                    maxWidth: '300px',
                    cursor: 'pointer',
                }}
                onClick={() => onAddNewClicked()}
            >
                <i
                    className='pi pi-plus'
                    style={{
                        borderRadius: '50%',
                        backgroundColor: '#FFA500',
                        border: 'none',
                        color: '#FFFFFF',
                        padding: '5px',
                        margin: '0',
                    }}
                />
                <p
                    style={{
                        margin: '0',
                        fontSize: '16px',
                        fontWeight: '400',
                    }}
                >
                    Add another child
                </p>
            </button>
            <div
                style={{
                    flexGrow: 1,
                }}
            ></div>
            <div className={`button-container ${isMobile ? 'button-container-mobile' : ""}`}>
                <div onClick={() => dispatch(decrementProfileActivationStep())}>
                    <i className='pi pi-angle-left' />
                    <button
                        style={{
                            color: '#585858',
                            fontSize: '14px',
                            fontWeight: '500',
                        }}
                    >
                        Previous
                    </button>
                </div>
                <div
                    {...(showNext() ? { 'data-next': true } : { 'data-skip': true })}
                    style={{
                        border: !showNext() && '1px solid #F0F4F7',
                        borderRadius: '10px',
                        width: '156px',
                    }}
                    onClick={() => dispatch(incrementProfileActivationStep())}
                >
                    <button>{showNext() ? 'Next' : 'Skip'}</button>
                    <i className='pi pi-angle-right' />
                </div>
            </div>
        </div>
    );
};

function AddChildren({ }: AddChildrenProps) {
    const [childrensData, setChildrensData] = useState<ChildrenInfo[]>([]);
    const [addNewChild, setAddNewChild] = useState<boolean>(false);
    const [childToEdit, setChildToEdit] = useState<number>(-1);
    const childrenDataKey = useMemo(() => JSON.stringify(childrensData), [childrensData]);

    const sessionInfo = useSelector<RootState>((state) => state.sessionInfo.data);
    const dispatch = useDispatch<AppDispatch>();

    const { enableLoader, disableLoader } = useLoader();

    function getDateStringAndAge(
        year: number,
        month: number
    ): { isoDateString: string; age: number } {
        const date = new Date(Date.UTC(year, month - 1, 1));

        const isoDateString = date.toISOString();

        const today = new Date();
        let age = today.getUTCFullYear() - year;

        if (
            today.getUTCMonth() < month - 1 ||
            (today.getUTCMonth() === month - 1 && today.getUTCDate() < 1)
        ) {
            age--;
        }

        return { isoDateString, age };
    }

    useEffect(() => {
        const data: object[] = sessionInfo['children'] || [];
        if (data.length > 0) {
            setChildrensData(() => {
                const updateValue: ChildrenInfo[] = [];
                data.forEach((value) => {
                    const date = new Date(value['dateOfBirth']);

                    updateValue.push({
                        name: value['firstName'],
                        about: value['aboutMe'] || '',
                        birthMonth: date.getMonth() + 1,
                        birthYear: date.getFullYear(),
                        gender: value['gender'],
                        allergyDetails: value['allergyDetails'] || '',
                    });
                });
                return updateValue;
            });
        }
    }, [sessionInfo]);

    return (
        <div
            style={{
                fontFamily: 'Poppins',
            }}
        >
            {addNewChild ? (
                <AddChild
                    childIndex={childToEdit === -1 ? childrensData.length : childToEdit}
                    onSubmit={(data, index) => {
                        setChildrensData((prev) => {
                            enableLoader();
                            const updatedData = [...prev];

                            if (index >= 0 && index < updatedData.length) {
                                const temp = sessionInfo['children'][index];
                                const { isoDateString, age } = getDateStringAndAge(
                                    data.birthYear,
                                    data.birthMonth
                                );
                                const preUpdatePayload = {
                                    id: Number(temp['id']),
                                    firstName: String(data.name),
                                    gender: Number(data.gender),
                                    dateOfBirth: isoDateString,
                                    ageInYears: age,
                                    aboutMe: String(data.about),
                                    allergyDetails: data.allergyDetails?.trim().length > 0 ? String(data.allergyDetails) : '',
                                    hasAllergies: data.allergyDetails?.trim().length > 0, // Set based on whether allergyDetails is provided
                                };
                                const childrens = [...sessionInfo['children']];
                                childrens[index] = preUpdatePayload;
                                const payload: object = {
                                    ...(sessionInfo as object),
                                    children: childrens,
                                };
                                dispatch(updateSessionInfo({ payload: payload })).finally(() => {
                                    disableLoader();
                                });
                                updatedData[index] = data;
                            } else {
                                const { isoDateString, age } = getDateStringAndAge(
                                    data.birthYear,
                                    data.birthMonth
                                );
                                const preNewPayload = {
                                    firstName: String(data.name),
                                    gender: Number(data.gender),
                                    dateOfBirth: isoDateString,
                                    ageInYears: age,
                                    aboutMe: String(data.about),
                                    allergyDetails: data.allergyDetails?.trim().length > 0 ? String(data.allergyDetails) : '',
                                    hasAllergies: data.allergyDetails?.trim().length > 0, // Set based on whether allergyDetails is provided
                                };
                                const childrens = [...sessionInfo['children'], preNewPayload];
                                const payload: object = {
                                    ...(sessionInfo as object),
                                    children: childrens,
                                };
                                updatedData.push(data);
                                dispatch(updateSessionInfo({ payload: payload })).finally(() => {
                                    disableLoader();
                                });
                            }

                            return updatedData;
                        });

                        setChildToEdit(-1);
                        setAddNewChild(false);
                    }}
                    prevChildren={
                        childToEdit === -1
                            ? {
                                name: '',
                                birthMonth: -1,
                                birthYear: -1,
                                gender: -1,
                                about: '',
                                allergyDetails: '',
                            }
                            : childrensData[childToEdit]
                    }
                    onCancel={() => {
                        setChildToEdit(-1);
                        setAddNewChild(false);
                    }}
                />
            ) : childrensData.length < 1 ? (
                <AddChildrenWelcome onAddClicked={() => setAddNewChild(true)} />
            ) : (
                <AllChildrenView
                    key={childrenDataKey}
                    allChildrens={childrensData}
                    onEditClicked={(i) => {
                        setChildToEdit(i);
                        setAddNewChild(true);
                    }}
                    onRemoveClicked={(i) => {
                        setChildrensData((prev) => {
                            enableLoader();
                            const updatedData = [...prev];
                            updatedData.splice(i, 1);
                            const childrens = [...sessionInfo['children']];
                            childrens.splice(i, 1);
                            const payload: object = {
                                ...(sessionInfo as object),
                                children: childrens,
                            };
                            dispatch(updateSessionInfo({ payload: payload })).finally(() => {
                                disableLoader();
                            });

                            return updatedData;
                        });
                    }}
                    onAddNewClicked={() => {
                        setAddNewChild(true);
                    }}
                />
            )}
        </div>
    );
}

export default AddChildren;
