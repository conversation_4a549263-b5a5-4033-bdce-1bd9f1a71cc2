import React, { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import c from "../../helper/juggleStreetConstants";
import IdentityCheck from "./IdentityCheck";
import { setCurrentProfileActivationStep } from "../../store/slices/applicationSlice";
import CustomDialog from "./CustomDialog";
import Responsibilities from "../Helper/ProfileCompletnessWizard/Responsibilities";
import WWCCDetails from "../Helper/ProfileCompletnessWizard/WWCCDetails";
import Childcare from "../Helper/ProfileCompletnessWizard/Childcare";
import AddProfilePhoto from "../Helper/ProfileCompletnessWizard/AddProfilePhoto";
import AddProfileVideo from "../Helper/ProfileCompletnessWizard/AddProfileVideo";
import Citizenship from "../Helper/ProfileCompletnessWizard/Citizenship";
import PrivacySettings from "../Helper/ProfileCompletnessWizard/PrivacySettings";
import Introduction from "../Helper/ProfileCompletnessWizard/Introduction";
import OddJobs from "../Helper/ProfileCompletnessWizard/OddJobs";
import AuPair from "../Helper/ProfileCompletnessWizard/AuPair";
import Tutoring from "../Helper/ProfileCompletnessWizard/Tutoring";
import Introduction2 from "../Helper/ProfileCompletnessWizard/Introduction2";
import PrimarySchool from "../Helper/ProfileCompletnessWizard/PrimarySchool";
import HighSchool from "../Helper/ProfileCompletnessWizard/HighSchool";
import Membership from "../Helper/ProfileCompletnessWizard/Membership";
import { PaymentMethods } from "../Helper/ProfileCompletnessWizard/PaymentMethods";

interface ProfileCompletionProps {
    isVisible: boolean;
    closeDialog: () => void;
}

function ProfileCompletionWizardHelper({
    isVisible,
    closeDialog,
}: ProfileCompletionProps) {
    const { data: sessionData }: { data: { interestedInChildcareJobs?: boolean; interestedInJobTypes?: number; interestedInOddJobs?: boolean; interestedInTutoringJobs?: boolean; interestedInAuPairJobs?: boolean; provider?: any;[key: string]: any } } = useSelector(
        (state: RootState) => state.sessionInfo
    );
    const applicationState = useSelector(
        (state: RootState) => state.applicationState.profileActivationCurrentStep
    );
    const dispatch = useDispatch<AppDispatch>();

    const profileCompleteness = sessionData["profileCompleteness"];

    const stepsMap = new Map<number, React.ComponentType>();

    const identityCheckComplete = useCallback(() => {
        return (
            (c.profileItemsHelper.trustVerification & sessionData["profileItems"]) !== 0
        );
    }, [sessionData]);

    const shouldIncludeWWCCDetails = useCallback(() => {
        return sessionData?.['country'] === 'au' && !sessionData?.['provider'].requiresGuardianApproval;
    }, [sessionData]);

    const shouldIncludeChildcare = useCallback(() => {
        return sessionData?.['interestedInChildcareJobs'] &&
            (sessionData?.['interestedInJobTypes'] > 0 || (sessionData?.['interestedInJobTypes'] & 15) === 0);
    }, [sessionData]);

    const shouldIncludeOddJobs = useCallback(() => {
        return sessionData?.['interestedInOddJobs'];
    }, [sessionData]);

    const shouldIncludeAuPair = useCallback(() => {
        return sessionData?.['interestedInAuPairJobs'] &&
            (sessionData?.['interestedInJobTypes'] > 0 || (sessionData?.['interestedInJobTypes'] & c.jobType.AU_PAIR) === 0);
    }, [sessionData]);

    const shouldIncludeTutorings = useCallback(() => {
        return sessionData?.['interestedInTutoringJobs'];
    }, [sessionData]);

    const shouldIncludePrimarySchool = useCallback(() => {
        return sessionData?.['interestedInTutoringJobs'] &&
            sessionData?.['interestedInJobTypes'] > 0 &&
            (sessionData?.['interestedInJobTypes'] & c.jobType.PRIMARY_SCHOOL_TUTORING) !== 0;
    }, [sessionData]);

    const shouldIncludeHighSchool = useCallback(() => {
        return sessionData?.['interestedInTutoringJobs'] &&
            sessionData?.['interestedInJobTypes'] > 0 &&
            (sessionData?.['interestedInJobTypes'] & c.jobType.HIGH_SCHOOL_TUTORING) !== 0;
    }, [sessionData]);

    const shouldIncludeAddProfileVideo = useCallback(() => {
        return sessionData?.['medias'].length === 0;
    }, [sessionData]);

    const shouldIncludePrivacySettings = useCallback(() => {
        return !sessionData?.provider?.['requiresGuardianApproval'];
    }, [sessionData]);

    const shouldIncludeMembership = useCallback(() => {
        return !sessionData?.provider?.['requiresGuardianApproval'];
    }, [sessionData]);

    const responsibilitiesCheckComplete = useCallback(() => {
        return (
            (sessionData["networkResponsibilitiesAcceptanceDate"] &&
                sessionData["networkResponsibilitiesAcceptanceDate"]) !== null
        );
    }, [sessionData]);

    const commanCkeck = (profileItem?: number) => {
        return (profileItem === null || (sessionData.profileItems & profileItem) !== profileItem);
    }

    let stepIndex = 1;

    // Add steps to Map based on conditions
    if (!responsibilitiesCheckComplete()) {
        stepsMap.set(stepIndex++, Responsibilities);
    }

    if (!identityCheckComplete()) {
        stepsMap.set(stepIndex++, IdentityCheck);
    }

    if (commanCkeck(c.profileItemsHelper.providerAboutMe | c.profileItemsHelper.providerJobCategories)) {
        stepsMap.set(stepIndex++, Introduction);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories)) {
        stepsMap.set(stepIndex++, Introduction2);
    }

    if (shouldIncludeWWCCDetails() && commanCkeck(c.profileItemsHelper.providerCertificates)) {
        stepsMap.set(stepIndex++, WWCCDetails);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeChildcare()) {
        stepsMap.set(stepIndex++, Childcare);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeOddJobs()) {
        stepsMap.set(stepIndex++, OddJobs);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeAuPair()) {
        stepsMap.set(stepIndex++, AuPair);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeTutorings()) {
        stepsMap.set(stepIndex++, Tutoring);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludePrimarySchool()) {
        stepsMap.set(stepIndex++, PrimarySchool);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeHighSchool()) {
        stepsMap.set(stepIndex++, HighSchool);
    }

    if (commanCkeck(c.profileItemsHelper.providerPhoto)) {
        stepsMap.set(stepIndex++, AddProfilePhoto);
    }

    if (commanCkeck(c.profileItemsHelper.providerJobCategories) && shouldIncludeAddProfileVideo()) {
        stepsMap.set(stepIndex++, AddProfileVideo);
    }

    if (commanCkeck(c.profileItemsHelper.citizenship)) {
        stepsMap.set(stepIndex++, Citizenship);
    }

    if (commanCkeck(c.profileItemsHelper.profileVisibility) && shouldIncludePrivacySettings()) {
        stepsMap.set(stepIndex++, PrivacySettings);
    }

    if (commanCkeck(c.profileItemsHelper.paymentMethods)) {
        stepsMap.set(stepIndex++, PaymentMethods);
    }

    if (commanCkeck(c.profileItemsHelper.providerMembership) && shouldIncludeMembership()) {
        stepsMap.set(stepIndex++, Membership);
    }

    useEffect(() => {
        dispatch(setCurrentProfileActivationStep(1));
    }, [isVisible, dispatch]);

    const renderStepComponent = () => {
        const StepComponent = stepsMap.get(applicationState);
        return StepComponent ? <StepComponent /> : <CloseDialog closeDialog={closeDialog} />;
    };

    return (
        <React.Fragment>
            <CustomDialog
                visible={isVisible}
                onHide={closeDialog}
                closeClicked={() => { }}
                profileCompletion={profileCompleteness}
            >
                {renderStepComponent()}
            </CustomDialog>
        </React.Fragment>
    );
}

const CloseDialog = ({ closeDialog }: { closeDialog: () => void }) => {
    useEffect(() => {
        if (closeDialog) {
            closeDialog();
        }
    }, [closeDialog]);
    return <></>;
};

export default ProfileCompletionWizardHelper;