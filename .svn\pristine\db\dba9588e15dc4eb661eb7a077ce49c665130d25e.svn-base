import { useState, useEffect, useRef } from "react";
import style from "../commonStyle/NumberInput.module.css";

interface NumberInputProps {
  value?: string;
  onChange?: (val: string | undefined) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

function NumberInput({ value, onChange , onBlur , onFocus }: NumberInputProps) {
  const [inputValue, setInputValue] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (value !== undefined) {
      setInputValue(value);
    } else {
      setInputValue("");
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value;
    const sanitized = raw.replace(/\D+/g, "");

    setInputValue(sanitized);

    if (onChange) {
      onChange(sanitized === "" ? undefined : sanitized);
    }
  };

  const [isFocused, setIsFocused] = useState(false);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      inputRef.current?.blur();
    }
  };

  return (
    <div className="flex align-items-center justify-content-center">
      <p
        className="m-0 p-0"
        style={{
          color: isFocused ? "#585858" : "#DFDFDF",
          fontSize: "60px",
          fontWeight: "700",
        }}
      >
        $
      </p>
      <input
        ref={inputRef}
        className={style.input}
        placeholder="00"
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => {
          setIsFocused(true);
          onFocus?.(); // Call the onFocus prop if provided
        }}
        onBlur={() => {
          setIsFocused(false);
          onBlur?.(); // Call the onBlur prop if provided
        }}
        onKeyDown={handleKeyDown}
        data-focused={isFocused}
        style={{
          border: "none",
          width: "135px",
          maxWidth: "150px",
          // textAlign: "center",
          fontSize: "60px",
          fontWeight: "700",
          color: isFocused ? "#585858" : "#DFDFDF",
        }}
      />
    </div>
  );
}

export default NumberInput;
