/* General panel styles */
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@300&family=Poppins:wght@300;400;500;600;700&display=swap");
.panel {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 288px; /* Default width for large screens */
  max-width: 100%; /* Make sure it doesn't overflow the screen width */
  transition: transform 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
  border-radius: 0 52px 52px 0;
  transform: translateX(0);
  background-color: #fff;
  overflow: visible;
  z-index: 2;
}

/* For screens smaller than 768px (tablets and smaller devices) */
/* @media (max-width: 768px) {
  .panel {
    width: 80vw; 
  }
} */

/* For screens smaller than 480px (mobile devices) */
/* @media (max-width: 480px) {
  .panel {
    width: 100vw; 
    border-radius: 0; 
  }
} */

.panel.closed {
  transform: translateX(-100%);
}

.logoDefault {
  width: 176px;
  height: 88.75px;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.logoAlt {
  position: absolute;
  width: 57px;
  height: 39px;
  top: 32px;
  left: 191px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.panel.closed .logoDefault {
  opacity: 0;
}

.panel.closed .logoAlt {
  opacity: 1;
}

.imgcontainer {
  border-radius: 0 48px 48px 0;
  height: 50%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 0px 0px 429px 13px;
  width: 288px;
}
.profileName {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  margin: 0px;
  color: rgba(88, 88, 88, 1);
}

.panel.closed .buttonDropdownContainer {
  align-items: flex-end;
  width: 224px;
  margin-top: 13px;
}

.shrinked .text {
  display: none;
}

.paneldropdown {
  display: flex;
  border: none !important;
  margin-top: 8px !important;
  width: 216.35px !important;
  height: 38px !important;
  align-items: center;
  background-color: transparent !important;
}

.userProfile {
  display: flex;
  align-items: center;
}

.userProfile > img {
  border-radius: 50%;
  overflow: hidden;
}
.userProfile {
  display: flex;
  align-items: center;
  margin-top: -8px;
  transition: all 0.3s ease;
  gap: 3px;
  margin-left: 16px;
}

.profilePhoto {
  height: 45px;
  width: 48px;
}

.avatar {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 51px;
  border-radius: 50%;
}

.divider {
  margin-top: 10px;
  width: 80% !important;
  margin-inline: auto;
  transition: margin 0.3s ease;
  color: #d6d6d6;
}
.postjobbtn {
  min-height: 36px !important;
  height: 36px !important;
  width: 80% !important;
  border-radius: 40px !important;
  margin-inline: auto;
  margin-top: 15px;
}

/* Adjust margin when the panel is closed */
.panel.closed .divider {
  margin-right: 9px; /* Align to the right when the panel is closed */
}
.verticalTabContainer {
  display: flex;
  margin-top: 12px;
  flex-direction: row; /* Align icons and tabs in a row */
  width: 100%;
  height: min-content;
  margin-inline: auto;
  transition: width 0.3s ease;
  padding-left: 10px;
}

.icons {
  display: flex;
  flex-direction: column;
  width: 40px;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
  padding-top: 15px;
  font-size: 10px;
}

.iconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 35px;
  width: 40px;
  text-align: center;
  cursor: pointer;
}

.tabs {
  display: flex;
  flex-direction: column;
  width: calc(100% - 40px); /* Adjust width according to the icon container */
  padding: 10px 0;
  border-left: none; /* No border for this section */
}

.tabItem {
  position: relative;
  display: flex;
  align-items: center;
}
.tabItem .spaceDiv {
  position: relative;
  height: 100%;
  left: 0;
  top: 0;
  width: 4px;
  background-color: #f1f1f1;
}
.tabItem .spaceDiv[data-index="first"] {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.tabItem .spaceDiv[data-index="last"] {
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
.activeTab .spaceDiv::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #ffa500;
  border-radius: 20px;
}
.paneldropdown div[class="p-dropdown p-focus"] {
  border: none !important;
  border-color: #585858 !important;
}

.textContainer {
  flex-grow: 1;
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #585858;
  padding-top: 18px;
  padding-left: 14px;
  height: 38px;
}

.activeTab {
  position: relative;
  background-color: transparent;
  color: #585858;
  font-weight: 600 !important;
}
.activeTab span {
  font-weight: 600 !important;
}
/* .activeTab::after {
  position: absolute;
  content: "•";
  color: #ffa500;
  font-size: 20px;
  right: 30px;
  top: -2px;
  height: max-content;
} */
.activeTabDot {
  background-color: #ffa500;  /* Orange color */
  width: 5px;
  height: 5px;              /* Fixed typo: removed unnecessary '.' before px */
  border-radius: 50%;       /* Makes it perfectly circular */
  display: inline-block;    /* Ensures proper rendering */
  margin-left: 5px;
}

.paneldropdown {
  width: 100%; /* Full width for the dropdown */
}
.tabItem > div {
  border: none !important;
  box-shadow: none !important;
  max-width: 100%;
}
.tabItem > div[class="p-focus"] {
  border: none !important;
  box-shadow: none !important;
  max-width: 100%;
}
.panelClosed .tabs {
  display: none;
}

.panelClosed .textContainer {
  display: none; /* Hide text when panel is closed */
}

.panelClosed .tabItem {
  display: none;
}
.panelClosed .tabs {
  width: 0; /* Collapse the width of the tabs when closed */
}
.panelClosed .icons {
  width: 229px;
  display: flex;
  /* justify-content: flex-end; */
  align-items: flex-end;
}

.iconOnlyContainer {
  margin-top: 19px;
  display: flex;
  justify-content: flex-end;
  width: 218px;
  height: 40px;
  background-color: transparent;
  cursor: pointer;
}

.iconOnlyContainer i {
  color: #585858; /* Color of the icon */
  font-size: 18px; /* Size of the icon */
}
.dividersecond {
  width: 107px;
  color: #d6d6d6;
}
.dividerContainer {
  margin-top: 18px;
  display: flex;
  margin-inline: auto;
  justify-content: center;
  align-items: center;
  width: 80%; /* Ensure the container takes full width */
}

.dividersecond {
  width: 107px;
  background-color: #d6d6d6;
}
.simpleIconContainer {
  display: flex;
  flex-direction: column;
  gap: 0px;
  margin-left: 10px;
}

.iconRow {
  display: flex;
  align-items: center;
  gap: 0px; /* Add space between icon and button */
}

.iconItem {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px; /* Set a fixed width for the icon container */
  height: 40px; /* Set a fixed height for the icon container */
  cursor: pointer;
}

.button {
  font-weight: 300;
  padding: 8px 16px;
  font-size: 14px;
  background-color: transparent;
  color: #585858;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.button:hover {
  font-weight: 400;
  padding: 8px 16px;
  font-size: 14px;
  background-color: transparent;
  color: #585858;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
/* styles/userPanel.module.css */
.userPanel {
  height: 293px;
  width: 100%;
  transition: transform 0.3s ease;
  border-radius: 0 48px 10px 0; /* Updated border radius */
  transform: translateX(0);
  background-color: rgba(240, 244, 247, 1);
  z-index: 3;
}
.userPanel:hover {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25); /* Adjusted to use rgba for transparency */
}

.userPanel.closed {
  transform: translateX(-99%);
}

.imgContainer {
  position: relative;
  width: 100%;
}

.logoDefault {
  width: 176px;
  height: 88.75px;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

.userPanel.closed .logoDefault {
  opacity: 0;
}

.toggleButton {
  position: absolute;
  top: 24%;
  right: -6px;
  background: transparent;
  border: none;
  padding: 10px;
  cursor: pointer;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  width: max-content;
  height: 30px;
}

.textContent {
  z-index: 10;
  display: flex;
  margin-top: -20px;
  width: 100%;
  padding-left: 18px;
  flex-direction: column;
}

.activationMessage {
  color: rgba(88, 88, 88, 1);
  width: 254px;
  line-height: 15px;
  font: 500 10px/15px Poppins, -apple-system, Roboto, Helvetica, sans-serif;
}

.activationMessageHighlight {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.progressLabel {
  color: #585858;
  align-self: start;
  font: 400 8px Poppins, sans-serif;
  margin-bottom: 0px;
  line-height: 12px;
  font-size: 11px;

}

.progressPercentage {
  color: #179d52;
  align-self: start;
  margin-top: 0px;
  font: 700 16px Poppins, sans-serif;
}

/* Adjust the default PrimeReact ProgressBar styles if needed */
.p-progressbar {
  border-radius: 20px;
  width: 251px;
  height: 10px;
}

.progressBar {
  width: 251px;
  margin-top: -10px;
  height: 10px;
}

.progressBar > div {
  background-color: #179d52 !important;
}

.progressBar > div > div {
  display: none;
}

.ctaWrapper {
  align-self: center;
  display: flex;
  margin-top: 25px;
  width: 184px;
  max-width: 100%;
  margin-left: -28px;
}

.ctaButton {
  width: 184px;
  height: 27.88px;
  display: flex;
  justify-content: center;
  border-radius: 20px;
  background-color: #179d52;
  margin-right: -63px;
  min-height: 28px;
  padding: 5px 10px;
  color: rgba(255, 255, 255, 1);
  white-space: nowrap;
  text-align: center;
  font: 700 12px Poppins, sans-serif;
  border: none;
  cursor: pointer;
}
.greenDot {
  width: 13px;
  height: 13px;
  left: 53px;
  top: 118px;
  position: fixed;
}
.upcomingJobs {
  height: 15px;
  width: 15px;
  border-radius: 50%;
  color: #fff;
  background-color: #ff6359;
  font-size: 70%;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
}
.employeeBenifit{
  font-weight: 300;
  padding: 8px 16px;
  font-size: 14px;
  background-color: transparent;
  color: #585858;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}
.employeeBenifit:hover{
  font-weight: 400;
  padding: 8px 16px;
  font-size: 14px;
  background-color: transparent;
  color: #585858;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.defaultIcon {
  display: block;
  transition: opacity 0.1s ease-in-out;
}

.hoverIcon {
  display: none;
  transition: opacity 0.1s ease-in-out;
}

.referbtn:hover .defaultIcon {
  display: none;
}

.referbtn:hover .hoverIcon {
  display: block;
}
