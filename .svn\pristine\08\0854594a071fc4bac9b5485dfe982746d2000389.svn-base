import React, { useRef, useState } from "react";
import styles from "../../../../containers/Common/styles/editTimesheet.module.css";
import { FaArrowLeft } from "react-icons/fa";
import { RiArrowDropDownLine } from "react-icons/ri";
import { FiPlus } from "react-icons/fi";
import CalenderIcon from "../../../../assets/images/Icons/calender.png"
import Doller from "../../../../assets/images/Icons/Dollar1.png"
import c from "../../../../helper/juggleStreetConstants";

interface Shift {
    start: string;
    finish: string;
}

interface EditTimesheetProps {
    day: string,
    date: string;
    profileImage: string;
    profileName: string;
    baseRate: number;
    extraRate: number;
    initialShifts: Shift[];
    onClose: () => void;
    onSave?: (shifts: Shift[]) => void;
    jobType: string;
    subType?:string;//** */
}

const EditTimesheet: React.FC<EditTimesheetProps> = ({
    day,
    date,
    profileImage,
    profileName,
    baseRate,
    extraRate,
    initialShifts,
    onClose,
    onSave,
    jobType,
    subType //*
}) => {
    const [errors, setErrors] = useState<{
        [key: number]: {
            start?: boolean;
            finish?: boolean;
            custom?: string;
            custom1?: string;
        };
    }>({});
    console.log("typeof jobType:", typeof jobType); // to confirm it's string or number
    console.log("jobType value:", jobType);

    const initialShiftsRef = useRef<Shift[]>(JSON.parse(JSON.stringify(initialShifts)));
    const [shifts, setShifts] = useState<Shift[]>(initialShifts);
    const [isChanged, setIsChanged] = useState(false);
    const [openDropdown, setOpenDropdown] = useState<{ shiftIndex: number, field: 'start' | 'finish' } | null>(null);

    const handleTimeChange = (index: number, field: 'start' | 'finish', value: string) => {
        const updatedShifts = [...shifts];
        updatedShifts[index][field] = value;
        setShifts(updatedShifts);
        setOpenDropdown(null);
        setIsChanged(true);

        validateSingleShift(index, field, updatedShifts);
    };

    const handleAddShift = () => {
        const newShift1 = { start: "", finish: "" };
        const newShift2 = { start: "", finish: "" };
        const updatedShifts = [...shifts, newShift1, newShift2];
        setShifts(updatedShifts);

        initialShiftsRef.current = [...initialShiftsRef.current, { ...newShift1 }, { ...newShift2 }];

        setIsChanged(true);
        validateAllShifts([...updatedShifts]);
    };

    const timeToDate = (timeStr: string): Date | null => {
        if (!timeStr) return null;

        const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(am|pm)/i);
        if (!match) return null;

        let hours = parseInt(match[1], 10);
        const minutes = parseInt(match[2], 10);
        const meridian = match[3].toLowerCase();

        if (meridian === "pm" && hours < 12) hours += 12;
        if (meridian === "am" && hours === 12) hours = 0;

        const date = new Date();
        date.setHours(hours, minutes, 0, 0);
        return date;
    };

    const validateSingleShift = (
        index: number,
        changedField: 'start' | 'finish',
        allShifts: typeof shifts
    ) => {
        const shift = allShifts[index];
        const shiftErrors: { start?: boolean; finish?: boolean; custom?: string; custom1?: string } = {};

        const startDate = timeToDate(shift.start);
        const endDate = timeToDate(shift.finish);

        if (changedField === "start" && !shift.start) shiftErrors.start = true;
        if (changedField === "finish" && !shift.finish) shiftErrors.finish = true;

        if (startDate && endDate) {
            if (startDate.getTime() === endDate.getTime()) {
                shiftErrors.custom = "Start and end times cannot be the same";
            }
            if (endDate.getTime() < startDate.getTime()) {
                shiftErrors.custom1 = "End time must be after start time";
            }
        }

        if (index % 2 === 0 && allShifts[index + 1]) {
            const beforeFinish = timeToDate(allShifts[index].finish);
            const afterStart = timeToDate(allShifts[index + 1].start);

            if (beforeFinish && afterStart && afterStart.getTime() <= beforeFinish.getTime()) {
                shiftErrors.custom1 = "Before School must end before After School starts";
            }
        }

        if (index % 2 === 1 && allShifts[index - 1]) {
            const beforeFinish = timeToDate(allShifts[index - 1].finish);
            const afterStart = timeToDate(allShifts[index].start);

            if (beforeFinish && afterStart && afterStart.getTime() <= beforeFinish.getTime()) {
                shiftErrors.custom = "After School must start after Before School ends";
            }
        }

        setErrors(prev => ({
            ...prev,
            [index]: shiftErrors
        }));
    };

    const validateAllShifts = (allShifts: typeof shifts): boolean => {
        const newErrors: {
            [key: number]: { start?: boolean; finish?: boolean; custom?: string; custom1?: string }
        } = {};

        allShifts.forEach((shift, index) => {
            const shiftErrors: { start?: boolean; finish?: boolean; custom?: string; custom1?: string } = {};

            const startDate = timeToDate(shift.start);
            const endDate = timeToDate(shift.finish);

            if (!shift.start) shiftErrors.start = true;
            if (!shift.finish) shiftErrors.finish = true;

            if (startDate && endDate && startDate.getTime() === endDate.getTime()) {
                shiftErrors.custom = "Start and end times cannot be the same";
            }

            if (startDate && endDate && endDate.getTime() < startDate.getTime()) {
                shiftErrors.custom1 = "End time must be after start time";
            }

            if (Object.keys(shiftErrors).length > 0) {
                newErrors[index] = shiftErrors;
            }
        });
        for (let i = 0; i < allShifts.length; i += 2) {
            const beforeFinish = timeToDate(allShifts[i]?.finish);
            const afterStart = timeToDate(allShifts[i + 1]?.start);

            if (beforeFinish && afterStart && afterStart.getTime() <= beforeFinish.getTime()) {
                newErrors[i] = { ...(newErrors[i] || {}), custom1: "Before School must end before After School starts" };
                newErrors[i + 1] = { ...(newErrors[i + 1] || {}), custom: "After School must start after Before School ends" };
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSave = () => {
        const isValid = validateAllShifts(shifts);

        if (!isValid) {
            return;
        }

        onSave(shifts);
    };


    const handleCancel = () => {
        const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
        setShifts(clonedInitial);
        setIsChanged(false);
        setOpenDropdown(null);
    };

    const toggleDropdown = (shiftIndex: number, field: 'start' | 'finish') => {
        if (openDropdown?.shiftIndex === shiftIndex && openDropdown?.field === field) {
            setOpenDropdown(null);
        } else {
            setOpenDropdown({ shiftIndex, field });
        }
    };

    const timeOptions = [
        "5:00am", "5:15am", "5:30am", "5:45am",
        "6:00am", "6:15am", "6:30am", "6:45am",
        "7:00am", "7:15am", "7:30am", "7:45am",
        "8:00am", "8:15am", "8:30am", "8:45am",
        "9:00am", "9:15am", "9:30am", "9:45am",
        "10:00am", "10:15am", "10:30am", "10:45am",
        "11:00am", "11:15am", "11:30am", "11:45am",
        "12:00pm", "12:15pm", "12:30pm", "12:45pm",
        "1:00pm", "1:15pm", "1:30pm", "1:45pm",
        "2:00pm", "2:15pm", "2:30pm", "2:45pm",
        "3:00pm", "3:15pm", "3:30pm", "3:45pm",
        "4:00pm", "4:15pm", "4:30pm", "4:45pm",
        "5:00pm", "5:15pm", "5:30pm", "5:45pm",
        "6:00pm", "6:15pm", "6:30pm", "6:45pm",
        "7:00pm", "7:15pm", "7:30pm", "7:45pm",
        "8:00pm", "8:15pm", "8:30pm", "8:45pm",
        "9:00pm", "9:15pm", "9:30pm", "9:45pm",
        "10:00pm", "10:15pm", "10:30pm", "10:45pm",
        "11:00pm"
    ];

    const getJobTypeValue = (type: string): number | null => {
        switch (type.toUpperCase()) {
            case "RECURRING":
            case "RECURRING JOB":
                return c.jobFrequency.RECURRING;
            case "ONE OF JOB":
            case "ONE_OFF":
                return c.jobFrequency.ONE_OFF;
            default:
                return null;
        }
    };
    const jobTypeValue = getJobTypeValue(jobType);


    const getJobTypeConstant = (type: string): number | null => {
        switch (type.toUpperCase()) {
            case "BABYSITTING":
                return c.jobType.BABYSITTING;
            case "NANNYING":
                return c.jobType.NANNYING;
            case "BEFORE_SCHOOL_CARE":
                return c.jobType.BEFORE_SCHOOL_CARE;
            case "AFTER_SCHOOL_CARE":
                return c.jobType.AFTER_SCHOOL_CARE;
            case "BEFORE_AFTER_SCHOOL_CARE":
                return c.jobType.BEFORE_AFTER_SCHOOL_CARE;
            case "AU_PAIR":
                return c.jobType.AU_PAIR;
            case "HOME_TUTORING":
                return c.jobType.HOME_TUTORING;
            case "PRIMARY_SCHOOL_TUTORING":
                return c.jobType.PRIMARY_SCHOOL_TUTORING;
            case "HIGH_SCHOOL_TUTORING":
                return c.jobType.HIGH_SCHOOL_TUTORING;
            case "ONE_OFF_ODD_JOB":
                return c.jobType.ONE_OFF_ODD_JOB;
            default:
                return null;
        }
    };

    const jobValue = getJobTypeConstant(jobType);

    const getFirstShiftLabel = (jobType: number | null): string => {
        switch (jobType) {
            case c.jobType.BABYSITTING:
            case c.jobType.NANNYING:
                return 'Nanny Time';
            case c.jobType.BEFORE_SCHOOL_CARE:
                return 'Before School Shift';
            case c.jobType.AFTER_SCHOOL_CARE:
                return 'After School Shift';
            case c.jobType.HOME_TUTORING:
            case c.jobType.PRIMARY_SCHOOL_TUTORING:
            case c.jobType.HIGH_SCHOOL_TUTORING:
                return 'Tutoring Shift';
            case c.jobType.ONE_OFF_ODD_JOB:
                return 'Odd Job Shift';
            default:
                return 'First Shift';
        }
    };

//********** */
    // const getSubTypeConstant = (type: string): number | null => {
    //     switch (type.toUpperCase()) {
    //         case "NANNYING": return c.subType.NANNYING;
    //         case "BEFORE_SCHOOL_CARE": return c.subType.BEFORE_SCHOOL_CARE;
    //         case "AFTER_SCHOOL_CARE": return c.subType.AFTER_SCHOOL_CARE;
    //         case "BEFORE_AFTER_SCHOOL_CARE": return c.subType.BEFORE_AFTER_SCHOOL_CARE;
    //         default: return null;
    //     }
    // };

    // const getJobTypeConstant = (type: string): number | null => {
    //     switch (type.toUpperCase()) {
    //         case "BABYSITTING": return c.jobType.BABYSITTING;
    //         case "AU_PAIR": return c.jobType.AU_PAIR;
    //         case "HOME_TUTORING": return c.jobType.HOME_TUTORING;
    //         case "PRIMARY_SCHOOL_TUTORING": return c.jobType.PRIMARY_SCHOOL_TUTORING;
    //         case "HIGH_SCHOOL_TUTORING": return c.jobType.HIGH_SCHOOL_TUTORING;
    //         case "ONE_OFF_ODD_JOB": return c.jobType.ONE_OFF_ODD_JOB;
    //         default: return null;
    //     }
    // };
    // const getFirstShiftLabel = (subType: number | null, jobType: number | null): string => {
    //     switch (subType) {
    //         case c.subType.NANNYING:
    //             return 'Nanny Time';
    //         case c.subType.BEFORE_SCHOOL_CARE:
    //             return 'Before School Shift';
    //         case c.subType.AFTER_SCHOOL_CARE:
    //             return 'After School Shift';
    //         case c.subType.BEFORE_AFTER_SCHOOL_CARE:
    //             return 'Before School'; // special case, handled below
    //     }

    //     switch (jobType) {
    //         case c.jobType.BABYSITTING:
    //             return 'Nanny Time';
    //         case c.jobType.HOME_TUTORING:
    //         case c.jobType.PRIMARY_SCHOOL_TUTORING:
    //         case c.jobType.HIGH_SCHOOL_TUTORING:
    //             return 'Tutoring Shift';
    //         case c.jobType.ONE_OFF_ODD_JOB:
    //             return 'Odd Job Shift';
    //         default:
    //             return 'First Shift';
    //     }
    // };
    // const jobValue = getJobTypeConstant(jobType);
    // const subTypeValue = getSubTypeConstant(subType);
//**************** */
    return (
        <div className={styles.container}>
            <div className={styles.headerWrapper}>
                <button className={styles.backBtn} onClick={onClose}>
                    <span className={styles.arrowCircle}>
                        <span className={styles.arrow}><FaArrowLeft /></span>
                    </span>
                    Go back
                </button>
            </div>

            <div className={styles.header}>
                <div className={styles.leftSection}>
                    <div className={styles.title}>Edit Timesheet</div>
                    <div className={styles.dateInfo}>
                        <img src={CalenderIcon} alt="calendar" className={styles.rowIcon} />
                        <span>{date}</span>
                    </div>
                </div>
                <div className={styles.profileSection}>
                    <img src={profileImage} alt="Profile" className={styles.profileImg} />
                    <span className={styles.profileName}>{profileName}</span>
                </div>
            </div>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />


            <div className={styles.rateBlock}>
                <img src={Doller} alt="dollar" className={styles.rowIcon} />
                <div className={styles.rateText}>
                    <div>Base Rate: ${baseRate} per hour</div>
                    <div className={styles.indented}>Extra Hours Rate: ${extraRate} per hour</div>
                </div>
            </div>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />

            {shifts.map((shift, index) => (
                <div key={index} className={styles.shiftBlock}>
                    {/* <div className={styles.shiftLabel}>
                        {index % 2 === 0 ? "Before School" : "After School"}
                    </div> */}

                    <div className={styles.shiftLabel}>
                        {jobValue === c.jobType.BEFORE_AFTER_SCHOOL_CARE
                            ? (index % 2 === 0 ? "Before School" : "After School")
                            : (index % 2 === 0
                                ? getFirstShiftLabel(jobValue)
                                : "Second Shift")
                        }
                    </div>

                    {/* <div className={styles.shiftLabel}>
                        {subTypeValue === c.subType.BEFORE_AFTER_SCHOOL_CARE
                            ? (index % 2 === 0 ? "Before School" : "After School")
                            : (index % 2 === 0
                                ? getFirstShiftLabel(subTypeValue, jobValue)
                                : "Second Shift")
                        }
                    </div> */}


                    <div className={styles.shiftRow}>
                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} 
                                 ${openDropdown?.shiftIndex === index && openDropdown?.field === 'start' ? styles.active : ''} 
                                 ${(errors[index]?.start || errors[index]?.custom) ? styles.errorBorder : ''}`}
                            >

                                <span
                                    className={`${styles.label} ${shift.start !== (initialShiftsRef.current[index]?.start || "")
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                >
                                    Start
                                </span>

                                <div className={styles.divider} />
                                <span
                                    className={`${styles.time} ${shift.start !== initialShiftsRef.current[index]?.start
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.start || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.start !== initialShiftsRef.current[index]?.start ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'start')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' ? (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px', transform: 'rotate(180deg)' }} />
                                        ) : (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px' }} />
                                        )}
                                    </span>
                                </div>
                            </div>

                            {errors[index]?.start && (
                                <div className={styles.errorMessage}>Start time is required</div>
                            )}

                            {errors[index]?.custom && (
                                <div className={styles.errorMessage}>{errors[index].custom}</div>
                            )}

                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.start === time ? styles.dropdownItemSelected : ''}`}
                                            onClick={() => handleTimeChange(index, "start", time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} 
                                 ${openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' ? styles.active : ''} 
                                  ${(errors[index]?.finish || errors[index]?.custom1) ? styles.errorBorder : ''}`}
                            >

                                <span
                                    className={`${styles.label} ${shift.finish !== (initialShiftsRef.current[index]?.finish || "") ? styles.modifiedTime : ''
                                        }`}
                                >
                                    Finish
                                </span>

                                <div className={styles.divider} />

                                <span
                                    className={`${styles.time} ${shift.finish !== initialShiftsRef.current[index]?.finish ? styles.modifiedTime : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.finish || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.finish !== initialShiftsRef.current[index]?.finish ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'finish')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' ? (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px', transform: 'rotate(180deg)' }} />
                                        ) : (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px' }} />
                                        )}
                                    </span>
                                </div>
                            </div>
                            {errors[index]?.finish && (
                                <div className={styles.errorMessage}>Finish time is required</div>
                            )}

                            {errors[index]?.custom1 && (
                                <div className={styles.errorMessage}>{errors[index].custom1}</div>
                            )}

                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.finish === time ? styles.dropdownItemSelected : ''
                                                }`}
                                            onClick={() => handleTimeChange(index, 'finish', time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            ))
            }

            {jobTypeValue === c.jobFrequency.RECURRING && (
                <button className={styles.addShiftBtn} onClick={handleAddShift}>
                    <span className={styles.plusCircle}><FiPlus /></span>Add another shift
                </button>
            )}

            <hr style={{ border: '1px solid #F0F4F7', width: '100%', marginTop: '30px' }} />
            <div className={styles.footerButtons}>
                <button
                    className={styles.revertBtn}
                    onClick={() => {
                        if (isChanged) {
                            handleCancel();
                        } else {
                            const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
                            setShifts(clonedInitial);
                            setOpenDropdown(null);
                        }
                    }}

                >
                    {isChanged ? "Cancel Changes" : "Revert Changes"}
                </button>

                <button
                    className={`${styles.saveBtn} ${isChanged ? styles.saveBtnEnabled : ''}`}
                    disabled={!isChanged}
                    onClick={handleSave}
                >
                    Save Changes
                </button>
            </div>
        </div >
    );
};

export default EditTimesheet;