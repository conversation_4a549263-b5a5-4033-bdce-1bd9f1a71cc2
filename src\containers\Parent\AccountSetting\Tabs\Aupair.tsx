import React, { CSSProperties, useState } from 'react';
import { ExtendedProfileTabProps } from '../../ProviderProfile/types';
import styles from '../../styles/child-careTab.module.css';
import earth from '../../../../assets/images/Icons/earth.png';
import c from '../../../../helper/juggleStreetConstants';
import checkStar from '../../../../assets/images/Icons/check-star.png';

const LimitedText = ({
    text,
    limit,
    disableLimit,
    style,
    className,
}: {
    text: string;
    limit: number;
    disableLimit: boolean;
    style?: CSSProperties;
    className?: string;
}) => {
    const displayText = disableLimit || text.length <= limit ? text : text.slice(0, limit);
    return (
        <span className={className} style={{ ...style }}>
            {displayText}
        </span>
    );
};

const Checks = ({ date1, date2 }: { date1: string; date2: string }) => {
    return (
        <div className="px-4 pt-2">
            <h1
                className="m-0 p-0"
                style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                }}
            >
                Checks
            </h1>
            <div className="flex gap-2 mt-2 mx-2">
                <img src={checkStar} alt="check" height="23px" width="23px" />
                <p
                    className="m-0 p-0"
                    style={{
                        fontWeight: '600',
                        fontSize: '16px',
                        color: '#585858',
                    }}
                >
                    Working With Children Check
                </p>
            </div>
            <p
                className="m-0 p-0 mt-2 mb-3"
                style={{
                    fontWeight: '700',
                    fontSize: '12px',
                    color: '#179D52',
                }}
            >
                {`Verified on: ${date1} | Expires on: ${date2}`}
            </p>
        </div>
    );
};

const Aupair: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
    const [textState, toggleTextState] = useState<boolean>(false);
    const displayLimit = 300;
    const text = helper.providerMyExperience4 || '';
    const selectedCountryOfCitizenship = c.countriesIso.find(
        (country) =>
            country.value.toLowerCase() === helper?.nationality?.toLowerCase() ||
            country.alpha2.toLowerCase() === helper?.nationality?.toLowerCase()
    );

    const juggleStRef = helper.vouches.find(
        (v) => v.referenceType === c.vouchReferenceType.juggleStreet
    );

    const juggleStRefBody = () => {
        const ref = helper.vouches.find((v) => v.referenceType === c.vouchReferenceType.juggleStreet);
        if (!ref) return '';
        return ref.whatWouldYouLikeToSay;
    };

    const isNewAuPair = () => {
        const { providerAuPairCategory } = helper;
        return providerAuPairCategory === c.auPairCategory.newAuPair;
    };

    const isExperiencedAuPair = () => {
        const { providerAuPairCategory } = helper;
        return providerAuPairCategory === c.auPairCategory.experiencedAuPair;
    };

    const displayAuPairExperience = helper.providerAuPairCategory !== undefined;

    return (
        <div>
            <div className={styles.childCareContainer}>
                <div className={styles.childCareBoxOne}>
                    <h1 className={styles.childCare}>Au Pair Experience</h1>
                    <div className={styles.childCareExperience}>
                        <div className="py-2 px-3">
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '16px',
                                    color: '#585858',
                                }}
                            >
                                <span
                                    className="m-0 p-0 inline-block text-left"
                                    style={{
                                        fontWeight: '400',
                                        fontSize: '16px',
                                        color: '#585858',
                                    }}
                                >
                                    <LimitedText
                                        className="m-0 p-0"
                                        text={text}
                                        limit={displayLimit}
                                        disableLimit={textState}
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    />
                                    {text.length >= displayLimit && (
                                        <span
                                            className="cursor-pointer hover:text-gray-300"
                                            style={{
                                                fontWeight: '400',
                                                fontSize: '12px',
                                                color: '#585858',
                                            }}
                                            onClick={() => toggleTextState((prev) => !prev)}
                                        >
                                            {' '}
                                            {textState ? 'Show Less.' : 'Read More...'}
                                        </span>
                                    )}
                                </span>
                            </p>
                        </div>
                    </div>
                    <br />
                </div>
            </div>
            <br />
            {displayAuPairExperience && isExperiencedAuPair() && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <div className="flex mt-3">
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    gap: '120px',
                                    paddingLeft: '10px',
                                    marginTop: '10px',
                                }}
                            >
                                <div>
                                    <h1
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '20px',
                                            color: '#585858',
                                        }}
                                    >
                                        Has worked as an Au Pair before
                                    </h1>
                                </div>
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        marginLeft: '15px',
                                    }}
                                >
                                    <h1
                                        style={{
                                            fontSize: '16px',
                                            fontWeight: '700',
                                            color: '#585858',
                                            margin: '0px',
                                        }}
                                    >
                                        Nationality
                                    </h1>
                                    <button className={styles.Nationality}>
                                        <img src={earth} alt="" />
                                        {selectedCountryOfCitizenship?.label}
                                    </button>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            )}
            {displayAuPairExperience && !isExperiencedAuPair() && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <div>
                            <h1
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '700',
                                    fontSize: '20px',
                                    color: '#585858',
                                }}
                            >
                                Has not worked as an Au Pair before
                            </h1>
                        </div>
                    </div>
                </div>
            )}
            <br />
            <div className={styles.childCareContainer}>
                <div className={styles.childCareBoxOne}>
                    {(helper?.certificates.length ?? 0) > 0 && (
                        <>
                            <Checks
                                date1={new Date(helper.certificates[0].verificationDate).toLocaleDateString('en-GB')}
                                date2={new Date(helper.certificates[0].expiryDate).toLocaleDateString('en-GB')}
                            />
                        </>
                    )}
                </div>
            </div>
            <br />
            <div className={styles.childCareContainer}>
                <div className={styles.childCareBoxOne}>
                    <div>
                        <h1
                            className="m-0 p-0"
                            style={{
                                fontWeight: '700',
                                fontSize: '20px',
                                color: '#585858',
                            }}
                        >
                            Host Family Responsibilities
                        </h1>
                        Interview Au Pair and check references.<br />
                        Sight and verify citizenship & visa documents<br />
                        Comply with all applicable government regulations
                    </div>
                </div>
            </div>
            {juggleStRef && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <div>
                            <h1
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '700',
                                    fontSize: '20px',
                                    color: '#585858',
                                }}
                            >
                                {`Au Pair Screening - ${new Date(juggleStRef.submissionDate).toLocaleDateString('en-GB')}`}
                            </h1>
                            <div className="full-width q-mt-md">
                                {juggleStRefBody()}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Aupair;