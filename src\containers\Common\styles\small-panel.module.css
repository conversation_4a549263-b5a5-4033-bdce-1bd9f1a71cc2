.smallpanel {
  position: fixed;
  top: 0;
  left: 0;
  height: 293px;
  width: 288px;
  transition: transform 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
  border-radius: 0 52px 20px 0;
  transform: translateX(0);
  background-color: rgba(240, 244, 247, 1);
  overflow: visible;
  z-index: 3;
}

.smallpanel.closed {
  /* Change to .smallpanel.closed */
  transform: translateX(-99%);
}

/* Logo container styles */
.imgcontainer {
  position: relative;
  width: 100%;
}

.logoDefault {
  width: 176px;
  height: 88.75px;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

.smallpanel.closed .logoDefault {
  opacity: 0;
}

.toggleButton {
  position: absolute;
  top: 24%;
  right: -6px;
  background: #585858;
  border: none;
  padding: 10px;
  cursor: pointer;
  border-radius: 50%;
  /* Makes the button round */
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 19.16px;
}

.textContent {
  z-index: 10;
  display: flex;
  margin-top: -20px;
  width: 100%;
  padding-left: 18px;
  flex-direction: column;
}

.activationMessage {
  color: rgba(88, 88, 88, 1);
  width: 254px;
  font: 500 10px/15px Poppins, -apple-system, Roboto, Helvetica, sans-serif;
}

.activationMessageHighlight {
  font-weight: 700;
  font-size: 16px;
}

.progressLabel {
  color: #585858;
  font-size: 11px;
  align-self: start;
  font: 400 8px Poppins, sans-serif;

}

.progressPercentage {
  color: #179d52;
  align-self: start;
  margin: 0px;
  font: 600 16px Poppins, sans-serif;
}

/* Adjust the default PrimeReact ProgressBar styles if needed */
.p-progressbar {
  border-radius: 20px;
  width: 251px;
  height: 10px;
}

.myprogressbar {
  width: 251px;
  margin-top: -10px;
  height: 10px;
}

.myprogressbar>div {
  background-color: #179d52 !important;
}

.myprogressbar>div>div {
  display: none;
}

.ctaWrapper {
  align-self: center;
  display: flex;
  margin-top: 25px;
  width: 184px;
  max-width: 100%;
  margin-left: -28px;
}

.ctaButton {
  width: 184px;
  height: 27.88px;
  display: flex;
  justify-content: center;
  border-radius: 20px;
  background-color: #179d52;
  margin-right: -63px;
  min-height: 28px;
  padding: 5px 10px;
  color: rgba(255, 255, 255, 1);
  white-space: nowrap;
  text-align: center;
  font: 700 12px Poppins, sans-serif;
  border: none;
  cursor: pointer;
}