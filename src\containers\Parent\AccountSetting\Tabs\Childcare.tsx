import React, { CSSProperties, useState } from 'react'
import star from '../../../../assets/images/Icons/star.png'
import rank from '../../../../assets/images/Icons/rank.png';
import { Divider } from 'primereact/divider';
import { ExtendedProfileTabProps } from '../../ProviderProfile/types';
import degree from '../../../../assets/images/Icons/degree.png';
import checkStar from '../../../../assets/images/Icons/check-star.png';
import styles from '../../styles/child-careTab.module.css';
import c from '../../../../helper/juggleStreetConstants';
import tickIcon from '../../../../assets/images/Icons/check-green.png';
import earth from '../../../../assets/images/Icons/earth.png';

const LimitedText = ({
    text,
    limit,
    disableLimit,
    style,
    className,
}: {
    text: string;
    limit: number;
    disableLimit: boolean;
    style?: CSSProperties;
    className?: string;
}) => {
    const displayText = disableLimit || text.length <= limit ? text : text.slice(0, limit);
    return (
        <span className={className} style={{ ...style }}>
            {displayText}
        </span>
    );
};
const Checks = ({ date1, date2 }: { date1: string; date2: string }) => {
    return (
        <div className="px-4 pt-2">
            <h1
                className="m-0 p-0"
                style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                }}
            >
                Checks
            </h1>
            <div className="flex gap-2 mt-2 mx-2">
                <img src={checkStar} alt="check" height="23px" width="23px" />
                <p
                    className="m-0 p-0"
                    style={{
                        fontWeight: '600',
                        fontSize: '16px',
                        color: '#585858',
                    }}
                >
                    Working With Children Check
                </p>
            </div>
            <p
                className="m-0 p-0 mt-2 mb-3"
                style={{
                    fontWeight: '700',
                    fontSize: '12px',
                    color: '#179D52',
                }}
            >
                {`Verified on: ${date1} | Expires on: ${date2}`}
            </p>
        </div>
    );
};
const ChildcareQualifications = ({ data }: { data: string[] }) => {
    return (
        <div className="px-4 pt-2 mt-2 mb-4">
            <h1
                className="m-0 p-0"
                style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                }}
            >
                Qualifications
            </h1>
            {data.map((val, index) => (
                <div key={index} className="flex gap-2 mt-2 mx-2">
                    <img src={degree} alt="degree" width="19.82px" height="18.62px" />
                    <p
                        className="m-0 p-0"
                        style={{
                            fontWeight: '600',
                            fontSize: '16px',
                            color: '#585858',
                        }}
                    >
                        {val}
                    </p>
                </div>
            ))}
        </div>
    );
};
const ReviewAndRatingHead = ({
    rating,
    ratingCount,
    isSuperHelper,
}: {
    rating: number;
    ratingCount: number;
    isSuperHelper: boolean;
}) => {
    return (
        <div className="px-4 pt-2 mt-2 mb-4">
            <div className="flex justify-content-between align-items-center">
                <div className="flex flex-column gap-1">
                    <h1
                        className="m-0 p-0"
                        style={{
                            fontWeight: '700',
                            fontSize: '20px',
                            color: '#585858',
                        }}
                    >
                        Reviews
                    </h1>
                    <div className="flex gap-1">
                        <img src={star} alt="star" width="19.82px" height="18.62px" />
                        <p
                            className="m-0 p-0"
                            style={{
                                fontWeight: '300',
                                fontSize: '14px',
                                color: '#585858',
                            }}
                        >
                            {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
                        </p>
                    </div>
                </div>
                {isSuperHelper && (
                    <div className="flex gap-2 align-items-center">
                        <img src={rank} alt="star" width="19.82px" height="18.62px" />
                        <p
                            className="m-0 p-0"
                            style={{
                                fontWeight: '700',
                                fontSize: '18px',
                                color: '#585858',
                            }}
                        >
                            Super Helper
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};
const ReviewAndRatingList = ({
    ratings,
}: {
    ratings: Array<{
        clientFirstName: string;
        clientLastName: string;
        feedback: string;
        ratingDate: string;
        ratingAvg: number;
        clientImageUrl: string;
    }>;
}) => {
    return (
        <div className="flex flex-column px-4 pt-2 mt-2 mb-4">
            {ratings.map((rating, index) => (
                <React.Fragment key={index}>
                    <Divider />
                    <div className="flex gap-2 my-2">
                        <div
                            style={{
                                height: '38px',
                                width: '38px',
                                background: 'gray',
                                borderRadius: '50%',
                                overflow: 'hidden',
                            }}
                        >
                            <img
                                src={rating.clientImageUrl}
                                alt="client Image"
                                width="100%"
                                height="100%"
                            />
                        </div>
                        <div className="flex-grow-1 flex flex-column gap-2">
                            <div className="flex">
                                <div className="flex-grow-1 flex flex-column">
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '12px',
                                            color: '#585858',
                                        }}
                                    >
                                        {new Date(rating.ratingDate).toLocaleDateString('en-GB')}
                                    </p>
                                </div>
                                <div className="flex gap-1">
                                    <img src={star} alt="star" width="19.82px" height="18.62px" />
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '300',
                                            fontSize: '14px',
                                            color: '#585858',
                                        }}
                                    >
                                        {`${rating.ratingAvg.toFixed(1)}`}
                                    </p>
                                </div>
                            </div>
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '14px',
                                    color: '#585858',
                                }}
                            >
                                {rating.feedback}
                            </p>
                        </div>
                    </div>
                </React.Fragment>
            ))}
        </div>
    );
};
const Childcare: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
    const [textState, toggleTextState] = useState<boolean>(false);
    const displayLimit = 300;
    const text = helper.providerMyExperience || '';
    const text1 = helper.providerMyExperience2 || '';
    const selectedQualifications = helper?.qualifications.filter((qualification) => qualification.selected) ?? [];
    const selectedCountryOfCitizenship = c.countriesIso.find((country) => country.value.toLowerCase() === helper?.nationality?.toLowerCase() || country.alpha2.toLowerCase() === helper?.nationality?.toLowerCase())
    const hasData = (helper?.certificates.length ?? 0) > 0 ||
        (helper?.qualifications.length ?? 0) > 0 && selectedQualifications.length > 0
    return (
        <div>
            <div className={styles.childCareContainer}>
                <div className={styles.childCareBoxOne}>
                    <h1 className={styles.childCare}>Childcare Experience</h1>
                    <div className={styles.childCareExperience}>
                        {/* {helper.providerMyExperience} */}
                        <div className="py-2 px-3">
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '16px',
                                    color: '#585858',
                                }}
                            >
                                <span
                                    className="m-0 p-0 inline-block text-left"
                                    style={{
                                        fontWeight: '400',
                                        fontSize: '16px',
                                        color: '#585858',
                                    }}
                                >
                                    <LimitedText
                                        className="m-0 p-0"
                                        text={text}
                                        limit={displayLimit}
                                        disableLimit={textState}
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    />
                                    {text.length >= displayLimit && (
                                        <span
                                            className="cursor-pointer hover:text-gray-300"
                                            style={{
                                                fontWeight: '400',
                                                fontSize: '12px',
                                                color: '#585858',
                                            }}
                                            onClick={() => toggleTextState((prev) => !prev)}
                                        >
                                            {' '}
                                            {textState ? 'Show Less.' : 'Read More...'}
                                        </span>
                                    )}
                                </span>
                            </p>
                        </div>
                    </div>
                    <br />
                    {/* <div className="flex mt-3"> */}
                    <h1
                        style={{
                            fontSize: '16px',
                            fontWeight: '700',
                            color: '#585858',
                            margin: '0px',
                        }}
                    >
                        Services
                    </h1>
                    <div className='flex justify-content-between'>
                        <div
                            style={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                flexDirection: 'row',
                                gap: '15px',
                                maxWidth: '680px',
                                paddingLeft: '10px',
                            }}
                        >
                            {(helper.interestedInJobTypes & c.jobType.BABYSITTING) !== 0 && (
                                <div className={styles.row}>
                                    <span>One-Off</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                            {(helper.interestedInJobTypes & c.jobType.NANNYING) !== 0 && (
                                <div className={styles.row}>
                                    <span>Recurring</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}
                            {(helper.interestedInJobTypes & c.jobType.BEFORE_SCHOOL_CARE) !== 0 && (
                                <div className={styles.row}>
                                    <span>Before School Care</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}
                            {(helper.interestedInJobTypes & c.jobType.AFTER_SCHOOL_CARE) !== 0 && (
                                <div className={styles.row}>
                                    <span>After School Care</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                        </div>
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                marginLeft: '15px',
                            }}
                        >
                            <h1
                                style={{
                                    fontSize: '16px',
                                    fontWeight: '700',
                                    color: '#585858',
                                    margin: '0px',
                                }}
                            >
                                Nationality
                            </h1>
                            <div
                                className="flex gap-2 justify-content-center align-items-center"
                                style={{
                                    width: '145.28px',
                                    height: '42.28px',
                                    borderRadius: '20px',
                                    backgroundColor: '#F1F1F1',
                                }}
                            >
                                <img src={earth} alt="" />
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '12px',
                                        color: '#585858',
                                    }}
                                >
                                    {selectedCountryOfCitizenship?.label || 'Not specified'}
                                    {/* {selectedCountryOfCitizenship?.label} */}
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* </div> */}
                </div>
            </div>
            <br />
            {helper?.providerMyExperience2 && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <h1 className={styles.childCare}>Special Needs Experience</h1>
                        <div className={styles.childCareExperience}>
                            {/* {helper.providerMyExperience} */}
                            <div className="py-2 px-3">
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '400',
                                        fontSize: '16px',
                                        color: '#585858',
                                    }}
                                >
                                    <span
                                        className="m-0 p-0 inline-block text-left"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    >
                                        <LimitedText
                                            className="m-0 p-0"
                                            text={text1}
                                            limit={displayLimit}
                                            disableLimit={textState}
                                            style={{
                                                fontWeight: '400',
                                                fontSize: '16px',
                                                color: '#585858',
                                            }}
                                        />
                                        {text1.length >= displayLimit && (
                                            <span
                                                className="cursor-pointer hover:text-gray-300"
                                                style={{
                                                    fontWeight: '400',
                                                    fontSize: '12px',
                                                    color: '#585858',
                                                }}
                                                onClick={() => toggleTextState((prev) => !prev)}
                                            >
                                                {' '}
                                                {textState ? 'Show Less.' : 'Read More...'}
                                            </span>
                                        )}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <br />
            {hasData && (
                <>
                    <div className={styles.childCareContainer}>
                        <div className={styles.childCareBoxOne}>
                            {(helper?.certificates.length ?? 0) > 0 && (
                                <>
                                    <Checks
                                        date1={new Date(
                                            helper.certificates[0].verificationDate
                                        ).toLocaleDateString('en-GB')}
                                        date2={new Date(helper.certificates[0].expiryDate).toLocaleDateString(
                                            'en-GB'
                                        )}
                                    />
                                </>
                            )}
                            {(helper?.qualifications.length ?? 0) > 0 && selectedQualifications.length > 0 && (
                                <>
                                    {(helper?.certificates.length ?? 0) > 0 && <Divider />}
                                    <ChildcareQualifications
                                        data={selectedQualifications.map((val) => val.text)}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </>
            )}
            <br />
            {(helper?.hasVouches) && (
                <>
                    <div className={styles.childCareContainer}>
                        <div className={styles.childCareBoxOne}>
                            <div>
                                <h1
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '20px',
                                        color: '#585858',
                                    }}
                                >
                                    References
                                </h1>
                                <p>Available on request. Please contact Customer Service to obtain referee details.</p>
                            </div>
                        </div>
                    </div>
                </>
            )}
            <br />
            {(helper?.providerReviewsCount ?? 0) > 0 && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <>
                            <ReviewAndRatingHead
                                rating={helper?.providerRatingsAvg ?? 0}
                                ratingCount={helper?.providerRatingsCount ?? 0}
                                isSuperHelper={helper?.isSuperProvider ?? false}
                            />
                            {(helper?.ratingsExtended.length ?? 0) > 0 && (
                                <ReviewAndRatingList ratings={helper.ratingsExtended} />
                            )}
                        </>
                    </div>
                </div>
            )}
        </div>
    )
}

export default Childcare