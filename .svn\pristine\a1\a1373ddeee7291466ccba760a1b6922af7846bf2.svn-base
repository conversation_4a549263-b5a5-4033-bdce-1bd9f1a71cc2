.ratehelperContainer {
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
  width: 100%;
  padding: 0 10px;
}

.rateHelperMain {
  background-color: #ffff;
  border-radius: 20px;
  width: 100%;
  padding: 16px;
}
.rateHelperMainMobile {
  background-color: #ffff;
  border-radius: 20px;
  width: 100%;

}

.ratehelperFirstDiv {
  width: 100%;
  max-width: 670px;
  height: auto;
  min-height: 100px;
  display: flex;
  align-items: center;
  border-radius: 10px;
  border: 1px solid rgba(223, 223, 223, 1);
  margin-bottom: 15px;
  background-color: #ffff;
  padding: 10px 10px;
}

.ratehelperSecondDiv {
  width: 100%;
  max-width: 969px;
  height: auto;
  min-height: 100px;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  background-color: #ffff;
  text-align: center;
}

.rateHelperImg {
  width: 70px;
  height: 65px;
  border-radius: 50%;
  object-fit: cover;
}

.rateName {
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  color: #585858;
}

.rateNameDiv {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-inline: 10px;
  gap: 3px;
  width: 89px;
  min-width: 89px;
}

.jobInfoContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-right: 5px;
  margin-left: 8px;
  flex: 1.5;
}

.timeAndDateContainer {
  display: flex;
  gap: 8px;
  /* flex-wrap: wrap; */
}

.rateDivider {
  /* width: 71px; */
}

.timeGet {
  width: 10vw;
  max-width: 125px;
  height: 34px;
  font-size: 9px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  border-radius: 8px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-inline: 8px;
}

.dateGet {
  width: 12vw;
  max-width: 130px;
  height: 34px;
  font-size: 9px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  border-radius: 8px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-inline: 8px;
  text-wrap: nowrap;
}

.adressGet {
  width: max-content;
  height: 34px;
  font-size: 9px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  border-radius: 8px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-inline: 8px;
  text-wrap: nowrap;
}

.ratehelperButton {
  padding: 8px 15px;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: 800;
  height: 34px;
  border-radius: 6px;
  background-color: #ffa500;
  width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0px 3px 3px 0px #00000040;
}

.ratehelperButton:hover {
  box-shadow: 0px 3px 3px 0px #00000040;
}

.dateGetSec {
  width: 140px;
  height: 34px;
  font-size: 9px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-inline: 8px;
  text-wrap: nowrap;
  text-decoration: underline;
}

.horizontalTabContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  position: relative;
  max-width: 796px;
  width: 100%;
}

.maxDivider {
  max-width: 796px;
  width: 100%;
  margin-top: 30px;

}

.tabItem {
  flex-grow: 1;
  text-align: center;
  padding: 8px;
  color: #888;
  position: relative;
  cursor: pointer;
}

.tabItem::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #ddd;
}

.activeTab {
  color: #333;
  font-weight: bold;
}

.tabSpacing {
  width: 6px; /* or whatever spacing you prefer */
}

.activeTab::after {
  background-color: #179d52; /* or your primary color */
}

.ratingStepContent {
  font-weight: 700;
  line-height: 40px;
  font-size: 26px;
  color: #585858;
  display: flex;
  flex-direction: column;
}

.ratingNavigation {
  max-width: 796px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;

}

.navigationButtonSecond {
  width: 160px;
  height: 34px;
  border: none;
  font-size: 12px;
  color: #ffff;
  font-weight: 800;
  border-radius: 6px;
  background-color: #ffa500;
  margin-left: auto;
  box-shadow: 0px 3px 3px 0px #00000040;
  cursor: pointer;
}

.navigationButtonSecond:disabled {
  width: 160px;
  height: 34px;
  border: none;
  font-size: 12px;
  color: #585858;
  font-weight: 400;
  border-radius: 6px;
  background-color: #dfdfdf;
  margin-left: auto;
  box-shadow: 0px 3px 3px 0px #00000040;
  cursor: pointer;
}

.navigationButton {
  width: 160px;
  height: 34px;
  border: none;
  font-size: 12px;
  color: #585858;
  font-weight: 500;
  border-radius: 6px;
  background-color: #ffff;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.textArea {
  width: 100%;
  max-width: 804px;
  height: 100px !important;
  overflow-y: auto !important;
  font-size: 14px;
  font-weight: 300;
}

/* Media Queries for Responsive Design */
@media screen and (max-width: 1024px) {
  .ratehelperFirstDiv,
  .ratehelperSecondDiv {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    padding: 10px;
  }
  .rateDivider {
    display: none;
  }
  
  .rateNameDiv {
    width: 100%;
    margin-bottom: 10px;
  }

  .jobInfoContainer {
    margin: 0;
    margin-top: 15px;
    flex: 1;
  }

  .ratehelperButton {
    width: 100%;
    max-width: 160px;
  }

  .horizontalTabContainer {
    flex-wrap: wrap;
  }

  .ratingStepContent {
    font-size: 22px;
    line-height: 34px;
  }

  .textArea {
    width: 100%;
  }
}



@media screen and (max-width: 480px) {
  .ratehelperContainer {
    padding: 0 8px;
  }

  .rateHelperImg {
    width: 45px;
    height: 40px;
  }

  .rateName {
    font-size: 12px;
  }

  .rateNameDiv {
    width: 100%;
    min-width: 100px;
  }

  .timeGet,
  .dateGet,
  .adressGet,
  .dateGetSec {
    width: 100%;
    max-width: 120px;
    font-size: 8px;
    height: 30px;
    padding-inline: 6px;
  }

  .ratehelperButton {
    width: 140px;
    font-size: 10px;
    padding: 6px 12px;
    height: 30px;
  }

  .ratingStepContent {
    font-size: 18px;
    line-height: 28px;
  }

  .navigationButton,
  .navigationButtonSecond,
  .navigationButtonSecond:disabled {
    width: 140px;
    font-size: 10px;
    height: 30px;
  }

  .textArea {
    font-size: 12px;
  }

  .tabItem {
    padding: 6px;
  }

  .tabSpacing {
    width: 4px;
  }
}
.ratehelperFirstDivMobile {
  width: 100%;
  height: 100%;
  display: flex;
  border-radius: 20px;
  margin-bottom: 20px;
  background-color: #ffff;
  padding-top: 15px;
  padding-bottom: 5px;
  flex-direction: column;
  box-shadow: 0px 0px 3px 0px #00000040;
}
.rateHelperImgMobile {
  width: 66.7px;
  height: 61px;
  border-radius: 50%;
}
.rateNameDivMobile {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-inline: 15px;
}
.rateNameMobile {
  font-size: 16px;
  font-weight: 700;
  margin: 0px;
  color: #585858;
}
.timeGetMobile {
  font-size: 10px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 7px;
  padding-inline: 10px;
  padding-block: 5px;
  text-wrap: noWrap;
}
.adressGetMobile {
  width: max-content;
  font-size: 10px;
  font-weight: 600;
  background-color: #ffff;
  color: #585858;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 7px;
  padding-inline: 10px;
  text-wrap: nowrap;
  padding-block: 5px;
}
.ratehelperButtonMobile {
  padding: 5px;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 10px;
  font-weight: 600;
  border-radius: 20px;
  background-color: #ffa500;
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 5px;
}
.timeDateMobile {
  font-size: 10px;
  font-weight: 600;
  background-color: #ededed;
  color: #585858;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  align-items: center;
  gap: 7px;
  padding-inline: 10px;
  padding-block: 5px;
  text-wrap: noWrap;
}
.ratehelperSecondDivMobile {
  display: flex;
  /* align-items: center; */
  margin-bottom: 20px;
  background-color: #ffff;
  text-align: center;
  flex-direction: row;
}
.dateGetSecMobile {
  font-size: 10px;
  font-weight: 600;
  background-color: #EDEDED;
  color: #585858;
  display: flex;
  align-items: center;
border-radius: 10px;
padding-inline: 5px;
padding-block: 3px;
  text-wrap: nowrap;
}
.ratehelperContainerMobile {
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
}
.horizontalTabContainerMobile {
  display: flex;
  margin-bottom: 70px;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  padding-inline: 13px;
}
.ratingNavigationMobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin-bottom: 10px;
  padding-inline: 30px;
}
.ratingStepContentMobile {
  font-weight: 700;
  line-height: 45px;
  font-size: 22px;
  color: #585858;
  display: flex;
  flex-direction: column;
}
.navigationButtonSecondMobile {
  padding-inline: 50px;
  padding-block: 8px;
  border: none;
  font-size: 14px;
  color: #ffff;
  font-weight: 800;
  border-radius: 8px;
  background-color: #ffa500;
  margin-left: auto;
  box-shadow: 0px 4px 4px 0px #00000040;
  cursor: pointer;
}
.navigationButtonSecondMobile:disabled {
  padding-block: 8px;
  padding-inline: 50px;
  border: none;
  font-size: 14px;
  color: #585858;
  font-weight: 400;
  border-radius: 8px;
  background-color: #dfdfdf;
  margin-left: auto;
  box-shadow: 0px 4px 4px 0px #00000040;
  cursor: pointer;
}
.textAreaMobile {
  width: 100%;
  height: 120px !important;
  overflow-y: auto !important;
  font-size: 14px;
  font-weight: 300;
  border: 1px solid #dfdfdf;
  background-color: transparent !important;
}
.closeBtn {
  position: absolute;
  right: 12px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0px 4px 4px 0px #00000040;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -9px;
  cursor: pointer;
}
.RatingHeader{
  height: 55px;
  background-color: #179D52;
  font-size: 24px;
  font-weight: 700;
  display: flex;
  justify-content: center;
  color: #fff;
  align-items: center;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}
.yourJobDescriptionMobile {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  margin-bottom: 0px;
}
.seeMoreButton {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  background-color: transparent;
  border: none;
  text-decoration: underline;
}
