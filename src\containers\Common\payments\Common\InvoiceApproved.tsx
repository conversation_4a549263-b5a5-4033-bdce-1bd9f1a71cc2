import React, { useEffect, useState } from 'react';
import juggleStreetLogo from '../../../../assets/images/juggle-st-transparent-card.png';
import styles from '../../styles/invoice-approved.module.css'; // 👈 CSS Module import
import useIsMobile from '../../../../hooks/useIsMobile';


const InvoiceApproved = () => {
    const { isMobile } = useIsMobile();
    const [redirect, setRedirect] = useState(false);

    if (redirect) {
        return null;
    }

    return (
        isMobile && (
            <div className={styles.container}>
                <div className={styles.header} />
                <div className={styles.content}>
                    <img src={juggleStreetLogo} alt="juggleStreetLogo" width="200" height="115" />
                    <div className={styles.checkmarkContainer}>
                        <svg width="80" height="80" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="50" fill="#C8E6C9" />
                            <circle cx="50" cy="50" r="40" fill="#37A950" />
                            <path
                                className={styles.checkmark}
                                d="M35 53 L45 63 L65 38"
                                stroke="#ffffff"
                                strokeWidth="8"
                                strokeLinecap="round"
                                fill="none"
                            />
                        </svg>
                          <p className={styles.title}>Invoice Approved</p>
                    </div>
                  
                    <p className={styles.sub}>Continuing to payment... </p>
                    <p className={styles.wait}>Please wait one moment</p>
                </div>
            </div>
        )
    );
};

export default InvoiceApproved;
