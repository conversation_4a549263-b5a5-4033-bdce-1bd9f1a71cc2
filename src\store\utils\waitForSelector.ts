import { RootState } from "..";

export const waitForSelector = <T>(selector: (state: RootState) => T, expectedValue: T, store: any): Promise<void> => {
  return new Promise((resolve) => {
    const check = () => selector(store.getState());

    // First check immediately
    if (check() === expectedValue) {
      resolve();
      return;
    }

    // Otherwise subscribe to changes
    const unsubscribe = store.subscribe(() => {
      const currentValue = check();
      if (currentValue === expectedValue) {
        unsubscribe();
        resolve();
      }
    });
  });
};
