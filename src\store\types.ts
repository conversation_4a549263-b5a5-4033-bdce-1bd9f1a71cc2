// SessionInfo
export interface SessionInfoType {
  data: object | null;
  loading: boolean;
  error: string | null;
}

// Application

export interface ApplicationStateType {
  isAdminMode: boolean;
  inIframe: boolean;
  isMobile: boolean;
  sideBarIsOpened: boolean;
  profileActivationCurrentStep: number | null;
  shouldShowProfileActivation: boolean;
  defaultFilters: SearchFilters;
  filters: SearchFilters;
  selectedMarkerSignal: number;
  showChatBox: boolean;
  enableAccountAndSettings: boolean;
  accountAndSettingsActiveTab: number;
  enableAccountAndSettingsBuisness: boolean;
  accountAndSettingsActiveTabBuisness: number;
  loaderEnabled: boolean;
  profileActivationEnabled: boolean;
  membershipActivationEnabled: boolean;
  interestInHomeAgedCareResponse: boolean;
  hasRequestedEmployeeBenefits: boolean;
  crispChatWindow: boolean;
}

export interface AccountSettingStateType {
  activeTabIndex: number;
}

// Search & Geo Search

export interface Filter {
  field: string;
  operator: string;
  value: any;
}

export interface SearchFilters {
  filters: Filter[];
  pageIndex: number;
  pageSize: number;
  sortBy: "experience";
}
export interface CandidateSearchFilters {
  filters: Filter[];
  pageIndex: number;
  pageSize: number;
  includeUsers: [];
}
// GeoSearch Server Response

export interface FeatureCollection {
  features: Features[];
}

export interface Features {
  geometry: {
    coordinates: number[];
  };
  properties: {
    canConnect: boolean;
    isCenterPoint: boolean;
    featureType: number;
    metadata: any;
    id: number;
    requestId: any;
    jobsCompleted: number;
    jobsCancelled: number;
    ratingsCount: number;
    ratingsAvg: number;
    firstName: string;
    publicName: string;
    distanceInKm: number | null;
    imageSrc: string;
    aboutMe25: any;
    actionRequired: boolean;
    friendStatus: number;
    suburb: string;
    state: string;
    lastSeenInDays: number;
    visibilityLimit: number;
    invitationDate: any;
    accountSubtype: number;
    jugglerTypes: number;
  };
}
