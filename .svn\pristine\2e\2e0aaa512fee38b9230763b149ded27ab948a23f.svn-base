import { configureStore } from "@reduxjs/toolkit";
import { rootReducer } from "./rootReducer";
import utils from "../components/utils/util";
import {
  updatehasRequestedEmployeeBenefits,
  updateinterestInHomeAgedCareResponse,
  updateIsMobile,
  updateShowProfileActivation,
} from "./slices/applicationSlice";
import { fetchSessionInfo } from "./tunks/sessionInfoTunk";
import CookiesConstant from "../helper/cookiesConst";
import c from "../helper/juggleStreetConstants";

const store = configureStore({
  reducer: rootReducer,
  devTools: true,
});

if (utils.getCookie(CookiesConstant.accessToken)) {
  store.dispatch(fetchSessionInfo()).then((_) => {
    store.dispatch(updateShowProfileActivation(store.getState().sessionInfo.data["profileCompleteness"] < 100));
  });
}

const updateRegisterSession = () => {
  store.dispatch(
    updateinterestInHomeAgedCareResponse(
      ![c.interestinhomeagedcareResponse.Yes, c.interestinhomeagedcareResponse.No, c.interestinhomeagedcareResponse.NA].includes(
        store.getState().sessionInfo.data["interestInHomeAgedCareResponse"]
      )
    )
  );

  store.dispatch(updatehasRequestedEmployeeBenefits(store.getState().sessionInfo.data["hasRequestedEmployeeBenefits"]));

  store.dispatch(updateShowProfileActivation(store.getState().sessionInfo.data["profileCompleteness"] < 100));
};

const mobileRoutes = ["sm2", "pm2", "sm3", "pm3","m2",'localhost'];

if (mobileRoutes.some((r) => window.location.origin.includes(r))) {
  store.dispatch(updateIsMobile(true));
}

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export { store, updateRegisterSession };
