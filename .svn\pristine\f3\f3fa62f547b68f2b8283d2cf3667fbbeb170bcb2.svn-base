import axios, { AxiosHeaders, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

class Auth {
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  saveToken(token: string): void {
    localStorage.setItem('token', token);
  }

  async refreshToken(): Promise<string> {
    // Call your refresh token function
   // const newToken = await refreshAuthToken();
   const newToken='';
    this.saveToken(newToken);
    return newToken;
  }
}

class Api {
  private auth: Auth;
  private api: AxiosInstance;

  constructor(auth: Auth) {
    this.auth = auth;
    this.api = axios.create({
      baseURL: 'http://your-api-url.com',
    });

    this.api.interceptors.request.use(
        async (config: InternalAxiosRequestConfig) => {
          const token = this.auth.getToken();
          if (token) {
            config.headers = config.headers || new AxiosHeaders();
            config.headers.Authorization = `Bearer ${token}`;
          }
      
          return config;
        },
        (error) => {
          return Promise.reject(error);
        }
      );
      

    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          const refreshToken = await this.auth.refreshToken();

          originalRequest.headers.Authorization = `Bearer ${refreshToken}`;

          return this.api(originalRequest);
        }

        return Promise.reject(error);
      }
    );
  }

  getApi(): AxiosInstance {
    return this.api;
  }
}

const auth = new Auth();
const api = new Api(auth).getApi();

export default api;
