import { createContext, useContext, useEffect, useReducer } from "react";
import { useSearchParams } from "react-router-dom";

const JoinNowContext = createContext(null);

const initialState = {
  clientType: "",
  clientCategory: 0,
  address: {
    addressLine1: "",
    addressLine2: "",
    postCode: "",
    suburb: "",
    name: "",
    state: "",
  },
  affiliateCode: "",
  accountType: 0,
  firstName: "",
  lastName: "",
  gender: "",
  email: "",
  password: "",
  confirmPassword: "",
  deviceType: "",
  phoneNumber: "",
  marketingInfo: {
    utmSource: "",
    utmMedium: "",
    utmCampaign: "",
    utmTerm: "",
    utmContent: "",
    identifier: "",
    referrer: "",
  },
  interestedInChildcareJobs: false,
  interestedInAuPairJobs: false,
  interestedInTutoringJobs: false,
  providerType: "",
};

function reducer(state, action) {
  switch (action.type) {
    case "UPDATE_FORM":
      return {
        ...state,
        ...action.payload,
      };
    default:
      return state;
  }
}

export const JoinNowProvider = ({ children }) => {
  const [joinNowData, dispatch] = useReducer(reducer, initialState);

  const [params] = useSearchParams();

  useEffect(() => {
    if (params.has("ac")) {
      dispatch({ type: "UPDATE_FORM", payload: { affiliateCode: params.get("ac") } });
    }
  }, [params]);

  return <JoinNowContext.Provider value={{ joinNowData, dispatch }}>{children}</JoinNowContext.Provider>;
};

export const useJoinNowData = () => useContext(JoinNowContext);
