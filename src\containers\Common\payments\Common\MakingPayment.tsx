import React from 'react';
import styles from '../../styles/making-payment.module.css';
import juggleStreetLogo from '../../../../assets/images/juggle-st-transparent-card.png';

const MakingPayment: React.FC = () => {
  return (
    <div className={styles.container}>
      <div className={styles.header} />
    <div style={{height:"75%"}} className='h-full flex flex-column align-items-center justify-content-center'>
      <div className={styles.content}>
        {/* Payment Texts */}
        <div className={styles.textContainer}>
          <h1 className={styles.title} style={{ color: '#179D52', fontWeight: '700', fontSize: '18px' }}>
            Making payment
          </h1>
          <div className={styles.logoContainer}>
          <img 
            src={juggleStreetLogo} 
            alt="Juggle Street" 
            className={styles.bouncingLogo}
          />
        </div>
          <p className={styles.subtitle} style={{ color: '#585858', fontWeight: '600', marginBottom: '2px' }}>
            Please wait one moment
          </p>
          <p className={styles.subtitle} style={{ color: '#585858', fontSize: '14px' }}>
            Do not exit screen
          </p>
        </div>
      </div>
      </div>
    </div>
  );
};

export default MakingPayment;