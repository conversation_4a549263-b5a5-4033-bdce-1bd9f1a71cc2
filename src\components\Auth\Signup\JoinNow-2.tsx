import React, { useState, useEffect } from "react";
import { InputText } from "primereact/inputtext";
import { useJoinNowData } from "../../../model/JoinNowContext";
// import '../../utils/util.css';
import "./join-now.css";
import { Calendar } from "primereact/calendar";
import c from "../../../helper/juggleStreetConstants";

interface SignUpPageProps {
  onNext: () => void;
  onPrevious?: () => void;
}

export const Joinnow2: React.FC<SignUpPageProps> = ({ onNext, onPrevious }) => {
  const { joinNowData, dispatch } = useJoinNowData();
  const [isFormValid, setIsFormValid] = useState(false);
  const [formData, setFormData] = useState({
    firstName: joinNowData.firstName || "",
    lastName: joinNowData.lastName || "",
    gender: joinNowData.gender || "",
    dateOfBirth: joinNowData.dateOfBirth || "",
  });
  const showDateOfBirth = joinNowData.clientType === 2;
  const showGender = joinNowData.clientType === 2;
  const filteredGenders = showGender
    ? Object.keys(c.genders).filter(
      (key) =>
        key === "MALE" || key === "FEMALE" || key === "PREFER_NOT_TO_SAY"
    )
    : Object.keys(c.genders);

  const [error, setError] = useState({
    firstName: "",
    lastName: "",
    gender: "",
    dateOfBirth: "",
  });

  useEffect(() => {
    setFormData({
      firstName: joinNowData.firstName || "",
      lastName: joinNowData.lastName || "",
      gender: joinNowData.gender ?? "",
      dateOfBirth: joinNowData.dateOfBirth || "",
    });
  }, [joinNowData]);

  useEffect(() => {
    checkFormValidity();
  }, [formData]);

  const handleDateChange = (e: any) => {
    const selectedDate = e.value;

    // Ensure selectedDate is a valid Date object
    if (selectedDate instanceof Date && !isNaN(selectedDate.getTime())) {
      // Calculate age

      if (!isValidAge(selectedDate)) {
        setError({
          ...error,
          dateOfBirth: "You must be at least 16 years old.",
        });
        return;
      }

      // Create a new Date object set to noon UTC on the selected date
      const utcDate = new Date(
        Date.UTC(
          selectedDate.getFullYear(),
          selectedDate.getMonth(),
          selectedDate.getDate(),
          12,
          0,
          0,
          0
        )
      );

      const payload = {
        dateOfBirth: utcDate.toISOString(), // Full ISO string
        dateOfBirthDay: selectedDate.getDate(), // Day (1-31)
        dateOfBirthMonth: selectedDate.getMonth() + 1, // Month (1-12)
        dateOfBirthYear: selectedDate.getFullYear(), // Year (YYYY)
      };
      // Dispatch the payload
      dispatch({ type: "UPDATE_FORM", payload: payload });

      // Update formData state
      setFormData({ ...formData, dateOfBirth: selectedDate });
      setError({ ...error, dateOfBirth: "" });
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === "firstName" || name === "lastName") {
      if (/^[a-zA-Z\s]*$/.test(value) || value === "") {
        setFormData((prevData) => ({ ...prevData, [name]: value }));
        dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
        setError((prevError) => ({ ...prevError, [name]: "" }));
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "Only characters are allowed",
        }));
      }
    } else {
      setFormData((prevData) => ({ ...prevData, [name]: value }));
      dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
      setError((prevError) => ({ ...prevError, [name]: "" }));
    }
  };

  const handleGenderChange = (value: string) => {
    setFormData((prevData) => ({ ...prevData, gender: value }));
    dispatch({ type: "UPDATE_FORM", payload: { gender: value } });
    setError((prevError) => ({ ...prevError, gender: "" }));
  };

  const checkFormValidity = () => {
    const isFirstNameValid = formData.firstName.trim() !== "";
    const isLastNameValid = formData.lastName.trim() !== "";
    const isGenderValid = formData.gender !== "";
    const isdateOfBirthValid =
      joinNowData.clientType !== 2 ||
      (formData.dateOfBirth !== null && isValidAge(formData.dateOfBirth));

    const isValid =
      isFirstNameValid &&
      isLastNameValid &&
      isGenderValid &&
      isdateOfBirthValid;

    setIsFormValid(isValid);
  };

  const isValidAge = (date: Date | null): boolean => {
    if (!date) return false;
    const today = new Date();
    const dateOfBirth = new Date(date);
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())
    ) {
      age--;
    }
    return age >= 16;
  };

  const handleSubmit = (e: React.FormEvent) => {

    e.preventDefault();
    let validationError = {
      firstName: "",
      lastName: "",
      gender: "",
      dateOfBirth: "",
    };
    if (!formData.firstName.trim()) {
      validationError.firstName = "Please enter your first name";
    } else if (!formData.lastName.trim()) {
      validationError.lastName = "Please enter your last name";
    } else if (formData.gender < 0) {
      validationError.gender = "Please select your gender.";
    }
    if (showDateOfBirth) {
      if (!formData.dateOfBirth) {
        validationError.dateOfBirth = "Please select your date of birth.";
      } else if (!isValidAge(formData.dateOfBirth)) {
        validationError.dateOfBirth = "You must be at least 16 years old.";
      }
    }
    const firstError = Object.values(validationError).find(
      (error) => error !== ""
    );
    setError(validationError);
    if (!firstError) {
      dispatch({ type: "UPDATE_FORM", payload: formData });
      onNext();
    }
  };

  const handlePrevious = (e) => {
    e.preventDefault();
    onPrevious();
  };

  return (
    <div>
      <form
        className="flex pl-4 pr-4"
        onSubmit={handleSubmit}
        style={{ display: "flex", flexDirection: "column" }}
      >
        <div
          className="flex-column flex justify-content-center align-items-center"
          style={{ marginTop: "-29px" }}
        >
          <div className="">
            <p className="h-joinnow">About You</p>
            <p className="text-color">Please tell us a bit about you</p>
          </div>
          <div className="flex flex-column align-items-center">
            <div className=" col-12 md:col-12 ">
              {/* <label className="mb-3 font-semibold">First Name</label> */}
              <div className="input-container">
                <InputText
                  name="firstName"
                  value={formData.firstName}
                  placeholder=""
                  onChange={handleChange}
                  // className={error.firstName ? 'border-red' : ''}
                  className={`input-placeholder ${error.firstName
                    ? "border-red"
                    : formData.firstName
                      ? "border-green"
                      : ""
                    }`}
                />
                <label htmlFor="firstName" className="label-name">
                  First Name
                </label>
              </div>
              {/* {error.firstName && <span style={{ color: "red" }}>{error.firstName}</span>} */}
            </div>
            <div className="mt-2 col-12 md:col-12">
              {/* <label className="mb-3 font-semibold">Last Name</label> */}
              <div className="input-container">
                <InputText
                  name="lastName"
                  value={formData.lastName}
                  placeholder=""
                  onChange={handleChange}
                  // className={error.lastName ? 'border-red' : ''}
                  className={`input-placeholder ${error.lastName
                    ? "border-red"
                    : formData.lastName
                      ? "border-green"
                      : ""
                    }`}
                />
                <label htmlFor="lastName" className="label-name">
                  Last Name
                </label>
              </div>
              {/* {error.lastName && <span style={{ color: "red" }}>{error.lastName}</span>} */}
            </div>
          </div>
          <div
            className="flex flex-column align-items-center mb-3"
            style={{ marginTop: "-19px" }}
          >
            <p
              className="h-joinnow2-prg col-12 md:col-8"
              style={{
                textAlign: "justify",
                textAlignLast: "center",
                fontSize: "13px",
              }}
            >
              Only the first initial of your last name will be shown on Juggle
              Street, e.g. Emily Smith will become Emily S.
            </p>
          </div>
          <div
            className="justify-content-start col-12 md:col-8"
            style={{ marginTop: "-26px" }}
          >
            <label
              htmlFor="gender"
              className="h-joinnow-quetion"
              style={{
                fontWeight: 600,
              }}
            >
              Your Gender
            </label>
            <div className="flex flex-wrap gap-2 mb-2 mt-3">
              {filteredGenders.map((key, index) => (
                <div
                  key={index}
                  className="align-items-center radiobutton-style"
                  data-should_hover={formData.gender !== c.genders[key]}
                  style={{
                    border: error.gender
                      ? "1px solid red"
                      : formData.gender === c.genders[key]
                        ? "1px solid green"
                        : "1px solid gainsboro",
                    padding: "11px 16px 11px 11px",
                    borderRadius: "10px",
                    position: "relative",
                    cursor: "pointer",
                  }}
                  onClick={() => handleGenderChange(c.genders[key])}
                >
                  <label
                    htmlFor={`gender${index}`}
                    className="ml-1 h-joinnow2-gender pr-5 cursor-pointer"
                    style={{
                      color: formData.gender === c.genders[key] ? "green" : "",
                      fontWeight:
                        formData.gender === c.genders[key] ? 700 : 500,
                    }}
                  >
                    {String(key)
                      .replace(/_/g, " ") // Replace underscores with spaces
                      .split(" ") // Split the string into words
                      .map(
                        (word) =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase()
                      ) // Capitalize each word
                      .join(" ")}{" "}
                    {/* Join the words back into a single string */}
                  </label>
                  <div
                    style={{
                      position: "absolute",
                      top: 3.5,
                      right: 6,
                    }}
                  >
                    <input
                      type="radio"
                      id={`gender${index}`}
                      value={c.genders[key]}
                      checked={formData.gender === c.genders[key]}
                      onChange={() => handleGenderChange(c.genders[key])}
                      style={{
                        cursor: "pointer",
                        opacity: 0,
                        position: "absolute",
                      }}
                    />
                    {formData.gender === c.genders[key] ? (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          backgroundColor: "#179D52",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <span
                          style={{
                            color: "white",
                            fontWeight: "bold",
                            fontSize: "12px",
                            lineHeight: "12px",
                          }}
                        >
                          ✓
                        </span>
                      </div>
                    ) : (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          border: "1px solid gainsboro",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: "transparent",
                        }}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
          {showDateOfBirth && (
            <div>
              <p className="h-joinnow-quetion" style={{ fontWeight: 600 }}>
                Date of Birth
              </p>
              <div className="input-container">
                <Calendar
                  id="dateOfBirth"
                  value={
                    formData.dateOfBirth === ""
                      ? formData.dateOfBirth
                      : new Date(formData.dateOfBirth)
                  }
                  onChange={handleDateChange}
                  dateFormat="dd/mm/yy"
                  placeholder="DD/MM/YYYY"
                  maxDate={new Date()}
                  className={`input-placeholder ${error.dateOfBirth
                    ? "border-red"
                    : formData.dateOfBirth
                      ? "border-green"
                      : ""
                    }`}
                />
              </div>
            </div>
          )}
        </div>
        <div className="text-center error-message mt-2">
          {error.gender && <span>{error.gender}</span>}
          {error.dateOfBirth && <span>{error.dateOfBirth}</span>}
        </div>
        {/* <div style={{ flex: 1 }}></div> */}
        <div
          className="flex"
          style={{
            marginBottom: "8%",
            marginTop: "7%",
            // marginInline: '100px',
            justifyContent: "space-around",
          }}
        >
          <div
            className="flex align-items-center justify-content-center"
            style={{ textWrap: "nowrap" }}
          >
            <i
              className="h-joinnow4-lable pi pi-angle-left pr-2"
              style={{ fontSize: "medium" }}
            ></i>
            <a
              className="h-joinnow4-lable cursor-pointer"
              onClick={handlePrevious}
            >
              Go Back
            </a>
          </div>
          <button
            className={`flex align-items-center justify-content-center h-joinnow-button ${isFormValid ? "shadow-4" : ""
              }`}
            onClick={handleSubmit}
            style={{
              backgroundColor: isFormValid ? "#FFA500" : "#DFDFDF",
              borderColor: isFormValid ? "#FFA500" : "#DFDFDF",
              color: isFormValid ? "#FFFFFF" : "#c2c7d1",
              cursor: isFormValid ? "pointer" : "not-allowed",
              minWidth: "177px",
            }}
            disabled={!isFormValid}
          >
            Next
            <i className="pi pi-angle-right" style={{ fontSize: "large" }}></i>
          </button>
        </div>
      </form>
    </div>
  );
};
