import React, { useEffect, useState } from "react";
import childrenIcon from "../../../../assets/images/Icons/my_child.png";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { IoHomeOutline } from "react-icons/io5";
import { FaArrowLeft } from "react-icons/fa";
import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
import EditTimesheet from "./EditTimesheet";
import Doller from "../../../../assets/images/Icons/Dollar1.png"
import c from "../../../../helper/juggleStreetConstants";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";


interface TimesheetRow {
    start: string;
    finish: string;
    hours: number;
    rate: number;
    total: number;
    originalId?: string;
    isOriginal?: boolean;
    editVersion?: number;
}

interface AwaitingConfirmationCardProps {
    profileName: string;
    profileImage: string;
    jobType: string;
    jobDate: string;
    jobAddress: string;
    baseRate: number;
    extraHoursRate: number;
    initialTimesheetRows: TimesheetRow[];
    statusText: string;
    isConfirmed?: boolean;
    isSubmitting?: boolean;
    onSubmit?: () => void;
    onGoBack?: () => void;
    onTimesheetRowsChange?: (rows: TimesheetRow[]) => void;
     isEdited: boolean;
    setIsEdited: (isEdited: boolean) => void;
}

const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
    profileName,
    profileImage,
    jobType,
    jobDate,
    jobAddress,
    baseRate,
    extraHoursRate,
    initialTimesheetRows,
    statusText,
    isConfirmed = false,
    isSubmitting = false,
    isEdited,
    setIsEdited,
    onSubmit,
    onGoBack,
    onTimesheetRowsChange,
}) => {

    const [submitted, setSubmitted] = useState(false);
    const handleSubmit = () => {
        setSubmitted(true);
        onSubmit?.();
    };

    const [timesheetRows, setTimesheetRows] = useState<TimesheetRow[]>(
        initialTimesheetRows.map(row => ({ ...row, isOriginal: false, editVersion: 0 }))
    );
    const [showEditPage, setShowEditPage] = useState(false);
    const [editVersionCounter, setEditVersionCounter] = useState(1);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const isParent = clientType === c.clientType.INDIVIDUAL;
    const isBusiness = clientType === c.clientType.BUSINESS;
    const showApprove = isParent || isBusiness;
    const totalAmount = timesheetRows
        .filter(row => !row.isOriginal)
        .reduce((sum, row) => sum + row.total, 0);
    const dateObj = new Date(jobDate);
    const getDayWithSuffix = (day: number) => {
        if (day > 3 && day < 21) return `${day}th`;
        switch (day % 10) {
            case 1: return `${day}st`;
            case 2: return `${day}nd`;
            case 3: return `${day}rd`;
            default: return `${day}th`;
        }
    };

    const day = dateObj.getDate();
    const dayWithSuffix: string = getDayWithSuffix(day);

    const month: string = dateObj.toLocaleString('default', { month: 'long' });
    const year: number = dateObj.getFullYear();

    const formattedDate: string = `${dayWithSuffix} of ${month}, ${year}`;

    // Helper function to calculate hours from AM/PM time strings
   const calculateHoursFromTimes = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  // This helper function manually parses "HH:mm" strings into total minutes from midnight.
  // It's much more reliable than new Date().
  const timeToMinutes = (timeStr: string): number | null => {
    const parts = timeStr.split(':');
    if (parts.length < 2) {
      console.error(`Invalid time format for calculation: "${timeStr}"`);
      return null; // Invalid format
    }
    
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) {
      console.error(`Could not parse hours/minutes from: "${timeStr}"`);
      return null; // Not numbers
    }

    return hours * 60 + minutes;
  };

  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);

  // If either time string was invalid, return 0 hours.
  if (startMinutes === null || endMinutes === null) {
    return 0;
  }
  
  // Calculate the difference in minutes and convert to hours
  const diffMinutes = endMinutes - startMinutes;
  const diffHours = diffMinutes / 60;
  
  // Return the difference, rounded to 2 decimal places.
  return parseFloat(Math.max(0, diffHours).toFixed(2));
};

    const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
        const currentActiveRows = timesheetRows.filter(row => !row.isOriginal);
   // <<< ADD THIS LINE TO SEE THE EXACT INPUT
    console.log("Data received from EditTimesheet component:", updatedShifts);
        const previousOriginalRows = timesheetRows.filter(row => row.isOriginal);

        const newRows: TimesheetRow[] = [];
        const changedOriginalRows: TimesheetRow[] = [];
        const unchangedRows: TimesheetRow[] = [];

        updatedShifts.forEach((updatedShift, index) => {
            const currentRow = currentActiveRows[index];

            if (currentRow) {
                const wasChanged = currentRow.start !== updatedShift.start ||
                    currentRow.finish !== updatedShift.finish;

                if (wasChanged) {
                    const clonedOriginal = JSON.parse(JSON.stringify(currentRow));

                    newRows.push({
                        ...clonedOriginal,
                        isOriginal: true,
                        editVersion: editVersionCounter - 1
                    });

                    // Calculate hours and total for the new time
                    const hours = calculateHoursFromTimes(updatedShift.start, updatedShift.finish);
                    const total = hours * baseRate;

                    newRows.push({
                        start: updatedShift.start,
                        finish: updatedShift.finish,
                        hours: hours,
                        rate: baseRate,
                        total: total,
                        isOriginal: false,
                        editVersion: editVersionCounter
                    });
                } else {
                    newRows.push({
                        ...currentRow,
                        editVersion: editVersionCounter
                    });
                }
            } else {
                // Calculate hours and total for new row
                const hours = calculateHoursFromTimes(updatedShift.start, updatedShift.finish);
                const total = hours * baseRate;

                newRows.push({
                    start: updatedShift.start,
                    finish: updatedShift.finish,
                    hours: hours,
                    rate: baseRate,
                    total: total,
                    isOriginal: false,
                    editVersion: editVersionCounter
                });
            }
        });

        const updatedRows = [
            ...previousOriginalRows,
            ...changedOriginalRows,
            ...unchangedRows,
            ...newRows
        ];

        setTimesheetRows(updatedRows);
        setEditVersionCounter(prev => prev + 1);
        setShowEditPage(false);

        // Notify parent component of the changes
        onTimesheetRowsChange?.(updatedRows);
         setIsEdited(true);
    };

    if (showEditPage) {
        const activeShiftsForEdit = timesheetRows
            .filter(row => !row.isOriginal)
            .map(({ start, finish }) => ({ start, finish }));

        return (
            <EditTimesheet
                day={String(day)}
                date={formattedDate}
                profileImage={profileImage}
                profileName={profileName}
                baseRate={baseRate}
                extraRate={extraHoursRate}
                initialShifts={activeShiftsForEdit}
                onClose={() => setShowEditPage(false)}
                onSave={handleSaveShifts}
                jobType={jobType}
            />
        );
    }
    const sortedRows = [...timesheetRows].sort((a, b) => {
        if (a.isOriginal && !b.isOriginal) return -1;
        if (!a.isOriginal && b.isOriginal) return 1;
        if (a.isOriginal && b.isOriginal) return (a.editVersion || 0) - (b.editVersion || 0);
        return 0;
    });

    return (
        <>
            <div className={styles.headerWrapper}>
                {isConfirmed ? (
                    <div className={styles.confirmedHeader}>
                        <div className={styles.inlineContainer}>
                            <div className={styles.head}>
                                <div className={styles.confirmRow}>
                                    <div className={styles.confirmedIconCircle}>
                                        <span className={styles.checkIcon}>✔</span>
                                    </div>
                                </div>
                                <div className={styles.headerConfirmedTitle}>Confirmed</div>
                            </div>

                            <div className={styles.confirmedSubtitle}>
                                Thanks for confirming your Timesheet
                            </div>
                        </div>
                    </div>
                ) : (
                    <button className={styles.backBtn} onClick={onGoBack}>
                        <span className={styles.arrowCircle}>
                            <span className={styles.arrow}><FaArrowLeft /></span>
                        </span>
                        Go back
                    </button>
                )}
            </div>

            <div className={`${styles.card} ${submitted ? styles.cardSubmitted : ""}`}>
                <div className={styles.headerSection}>
                    <div>
                        <h3 className={styles.title}>Review Timesheet</h3>

                        <div className={styles.status}>
                            Status: <span className={styles.statusHighlight}>
                                {submitted ? "Awaiting Craig's Approval" : statusText}
                            </span>
                        </div>
                    </div>
                    <div className={styles.profileContainer}>
                        <img
                            src={profileImage}
                            alt="Profile"
                            className={styles.avatar}
                        />
                        <div className={styles.profileName}>{profileName}</div>
                    </div>
                </div>

                <div className={styles.info}>
                    <div className={styles.infoBlock}>
                        <div className={styles.row}>
                            <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
                            <div>{jobType}</div>
                        </div>
                        <div className={styles.row}>
                            <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
                            <div>{formattedDate}</div>
                        </div>
                        <div className={styles.row}>
                            <IoHomeOutline className={styles.rowIcon} />
                            <div>{jobAddress}</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />

                    <div className={styles.rateBlock}>
                        <img src={Doller} alt="dollar" className={styles.rowIcon} />
                        <div className={styles.rateText}>
                            <div>Base Rate: ${baseRate} per hour</div>
                            {
                                extraHoursRate ? (
                                    <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div>
                                ) : null
                            }
                            {/* <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div> */}
                        </div>
                    </div>

                    <hr className={styles.hrFull} />
                </div>

                {!submitted && timesheetRows.some(row => row.isOriginal) && (
                    <div className={styles.adjustedTimesheetBlock}>
                        <div className={styles.adjustedHeader}>
                            <span className={styles.adjustedTitle}>Adjusted Timesheet</span>
                            <span className={styles.adjustedIcon}>?</span>
                        </div>
                        <div className={styles.adjustedDescription}>
                            You have adjusted the hours worked. Please review changes and then "Approve".
                        </div>
                        <hr className={styles.hrFull} />
                    </div>

                )}

                <div className={styles.timesheetContainer}>
                    <div className={styles.rowHeader}>
                        <div className={styles.column}>Start</div>
                        <div className={styles.column}>Finish</div>
                        <div className={styles.column}>Hours</div>
                        <div className={styles.column}>Rate</div>
                        <div className={styles.column}>Total</div>
                    </div>

                    {sortedRows.map((row, index) => (
                        <div
                            key={`${index}-${row.editVersion}-${row.isOriginal}`}
                            className={`${styles.rowData} ${row.isOriginal ? styles.originalRow : ''}`}
                            style={row.isOriginal ? {
                                color: '#ff6359',
                                textDecoration: 'line-through',
                                opacity: 0.7,
                            } : {}}
                        >
                            <div className={styles.column}>{row.start}</div>
                            <div className={styles.column}>{row.finish}</div>
                            <div className={styles.column}>{row.hours}</div>
                            <div className={styles.column}>${row.rate}</div>
                            <div className={styles.column}>${row.total}</div>
                        </div>
                    ))}
                    <hr className={styles.hr} />

                    <div className={styles.totalRow}>
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
                    </div>

                    <hr className={styles.hr} />
                </div>

                {submitted && (
                    <div className={styles.rowAlign}>
                        <div className={styles.textBlock}>
                            <p className={styles.pendingTitle}>Pending {profileName} Approval</p>
                            <p className={styles.pendingSub}>
                                Awaiting for {profileName} to approve your timesheet
                            </p>
                        </div>
                        <div className={styles.profileContainerConfirm}>
                            <div className={styles.avatarWrapper}>
                                <img src={profileImage} className={styles.avatar} alt="profile" />
                                <div className={styles.redDot}></div>
                            </div>
                            <div className={styles.profileName1}>{profileName}</div>
                        </div>

                    </div>
                )}

                <div className={styles.footer}>
                    {!isConfirmed ? (
                        <>
                            <div className={styles.section}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>
                                        {showApprove ? 'Approve Timesheet' : 'Confirm Timesheet'}
                                    </strong>
                                    <div className={styles.subText}>The Timesheet is an accurate record of the job.</div>
                                </div>
                                <button className={styles.submitBtn} onClick={handleSubmit}>
                                    {isSubmitting ? 'Submitting...' : (showApprove ? 'Approve' : 'Submit')}
                                </button>
                            </div>
                            <div className={`${styles.section} ${styles.editSection}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make adjustments to the Timesheet.</div>
                                </div>
                                <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>Edit Timesheet</button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className={styles.confirmedBox}>
                                <p className={styles.confirmedTitle}>Timesheet Confirmed</p>
                                <p className={styles.confirmedSubText}>This Timesheet will now be submitted to the parent for approval</p>
                            </div>

                            <div className={`${styles.section} ${styles.editSection} ${submitted ? styles.disabledBlock : ""}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make an adjustment to the actual hours worked</div>
                                </div>
                                <button className={styles.editDisabledBtn} disabled>Edit Timesheet</button>
                            </div>

                            <button className={styles.nextBtn} onClick={onGoBack}>Next</button>
                        </>
                    )}
                </div>
            </div>
        </>
    );
};

export default AwaitingConfirmationCard;