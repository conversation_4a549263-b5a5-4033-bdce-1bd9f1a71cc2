.headerGradient {
  background: linear-gradient(90deg, #37a950, #ffa500);
  height: 81px;
}

.mainSection {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  padding-top: 95px;
  padding-left: 95px;
  gap: 80px;
  justify-content: space-between;
}

.leftSideSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.juggleLogo {
  object-fit: contain;
  width: 173px;
  height: 36px;
  margin-left: 50px;
}

.textStyle1 {
  color: #179d52;
  font-size: 60px;
  font-weight: 700;
  margin: 0px;
  line-height: 90px;
}

.leftPara {
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  color: #585858;
}

.manageMentBtn {
  width: 306px;
  height: 46px;
  border: none;
  border-radius: 10px;
  background-color: rgba(255, 165, 0, 1);
  font-size: 14px;
  font-weight: 700;
  color: rgba(255, 255, 255, 1);
  margin-top: 30px;
  cursor: pointer;
}

.manageMentBtn:hover {
  cursor: pointer;
  box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .mainSection {
    flex-direction: column;
    padding: 20px;
    gap: 40px;
  }

  .leftSideSection {
    width: 100%;
    align-items: center;
    padding: 0 15px;
  }

  .textStyle1 {
    font-size: 32px;
    line-height: 1.2;
    margin-bottom: 20px;
  }

  .leftPara {
    font-size: 16px;
    line-height: 24px;
    text-align: left;
    width: 100%;
  }

  .manageMentBtn {
    width: 100%;
    max-width: 306px;
    margin-top: 20px;
  }

  .rightSection {
    width: 100%;
    padding: 0 15px;
  }

  .rightSection img {
    width: 100%;
    height: auto;
    max-width: 636px;
  }

  .juggleLogo {
    margin-left: 20px;
    width: 120px;
    height: auto;
  }

  .headerGradient {
    height: 60px;
  }
}

@media (max-width: 480px) {
  .mainSection {
    padding: 15px;
  }

  .textStyle1 {
    font-size: 28px;
  }

  .leftPara {
    font-size: 14px;
    line-height: 22px;
  }
}