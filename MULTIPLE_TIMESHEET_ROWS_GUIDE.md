# Multiple Timesheet Rows Implementation Guide

## 🎯 **What Was Implemented**

The TimesheetDetailsPopup component has been enhanced to support multiple timesheet rows instead of just a single entry. Here's what's new:

### ✅ **1. Multiple Timesheet Rows Display**
- Component now accepts and displays an array of timesheet entries
- Each row shows: Start Time, Finish Time, Hours, Rate, and Total
- All rows are displayed in AM/PM format with proper calculations

### ✅ **2. Complete Array Passed to EditTimesheet**
- When user clicks "Edit Timesheet", the entire array is passed to EditTimesheet component
- Users can edit all entries, not just a single one
- Maintains existing AM/PM time display and real-time calculation features

### ✅ **3. Backward Compatibility**
- Still works with single timesheet entry (from timesheetDetails)
- Automatically falls back to single-row mode if no array is provided

## 🔧 **New Interface & Props**

### **Enhanced Props Interface**
```typescript
interface TimesheetRow {
  start: string;        // Start time (will be converted to AM/PM)
  finish: string;       // End time (will be converted to AM/PM)
  hours: number;        // Calculated hours
  rate: number;         // Hourly rate
  total: number;        // Calculated total (hours * rate)
  isOriginal?: boolean; // For tracking edits
  editVersion?: number; // For version control
  id?: number;          // Unique identifier
}

interface TimesheetDetailsPopupProps {
  selectedEntry: TimesheetEntry | null;
  timesheetDetails: TimesheetDetails | null;
  timesheetRows?: TimesheetRow[];  // NEW: Array of timesheet entries
  onClose: () => void;
  onApprovalSuccess?: () => void;
}
```

## 🚀 **How to Use**

### **Option 1: Multiple Rows (New Feature)**
```typescript
const timesheetRowsArray = [
  {
    start: "09:00",      // 24-hour format (will convert to "9:00 AM")
    finish: "12:00",     // 24-hour format (will convert to "12:00 PM")
    hours: 3,
    rate: 25,
    total: 75,
    id: 1
  },
  {
    start: "1:00 PM",    // Already AM/PM format
    finish: "5:00 PM",   // Already AM/PM format
    hours: 4,
    rate: 25,
    total: 100,
    id: 2
  }
];

<TimesheetDetailsPopup
  selectedEntry={selectedEntry}
  timesheetDetails={timesheetDetails}
  timesheetRows={timesheetRowsArray}  // Pass the array here
  onClose={closePopup}
  onApprovalSuccess={handleApprovalSuccess}
/>
```

### **Option 2: Single Row (Backward Compatible)**
```typescript
// Works exactly as before - no changes needed
<TimesheetDetailsPopup
  selectedEntry={selectedEntry}
  timesheetDetails={timesheetDetails}
  // No timesheetRows prop - uses timesheetDetails.jobStartTime/jobEndTime
  onClose={closePopup}
  onApprovalSuccess={handleApprovalSuccess}
/>
```

## 🔄 **How It Works Internally**

### **1. Data Processing**
```typescript
// Function processes array and converts times to AM/PM
const processTimesheetRows = (rows: TimesheetRow[]): TimesheetRow[] => {
  return rows.map((row, index) => ({
    ...row,
    start: formatTimeForDisplay(row.start),    // "14:00" → "2:00 PM"
    finish: formatTimeForDisplay(row.finish),  // "17:00" → "5:00 PM"
    isOriginal: false,
    editVersion: 0,
    id: row.id || index
  }));
};
```

### **2. Fallback Logic**
```typescript
// If timesheetRows array provided, use it; otherwise fallback to single row
const getInitialTimesheetRows = (): TimesheetRow[] => {
  if (timesheetRows && timesheetRows.length > 0) {
    return processTimesheetRows(timesheetRows);  // Multiple rows
  } else {
    return createSingleRowFromTimesheetDetails(); // Single row fallback
  }
};
```

### **3. Total Calculation**
```typescript
// Calculates total from all rows
const calculateTotalAmount = (rows: TimesheetRow[]): number => {
  return rows
    .filter(row => !row.isOriginal)
    .reduce((sum, row) => sum + (row.total || 0), 0);
};
```

## 📊 **Example Data Structures**

### **Input Array Example**
```typescript
const timesheetData = [
  {
    start: "09:00",     // Morning shift
    finish: "12:00",
    hours: 3,
    rate: 25,
    total: 75,
    id: 1
  },
  {
    start: "13:00",     // Afternoon shift
    finish: "17:00", 
    hours: 4,
    rate: 25,
    total: 100,
    id: 2
  },
  {
    start: "6:00 PM",   // Evening shift (already AM/PM)
    finish: "9:00 PM",
    hours: 3,
    rate: 30,           // Higher evening rate
    total: 90,
    id: 3
  }
];

// Total: 10 hours, $265
```

### **Display Result**
```
┌─────────────┬─────────────┬───────┬──────┬─────────┐
│ Start Time  │ Finish Time │ Hours │ Rate │ Total   │
├─────────────┼─────────────┼───────┼──────┼─────────┤
│ 9:00 AM     │ 12:00 PM    │ 3.00  │ $25  │ $75.00  │
│ 1:00 PM     │ 5:00 PM     │ 4.00  │ $25  │ $100.00 │
│ 6:00 PM     │ 9:00 PM     │ 3.00  │ $30  │ $90.00  │
├─────────────┼─────────────┼───────┼──────┼─────────┤
│             │             │ 10.00 │      │ $265.00 │
└─────────────┴─────────────┴───────┴──────┴─────────┘
```

## 🎯 **Benefits**

### **✅ For Users:**
- Can view and edit multiple timesheet entries in one popup
- Each row maintains AM/PM time display
- Real-time calculation for each row
- Total amount calculated across all rows

### **✅ For Developers:**
- Backward compatible - existing code still works
- Flexible - supports both single and multiple rows
- Maintains all existing features (AM/PM display, calculations)
- Clean, typed interfaces

### **✅ For EditTimesheet Component:**
- Receives complete array of timesheet entries
- Can edit all rows simultaneously
- Maintains data integrity across multiple entries

## 📝 **Migration Guide**

### **No Changes Required for Existing Code**
Your existing code will continue to work without any changes:
```typescript
// This still works exactly as before
<TimesheetDetailsPopup
  selectedEntry={selectedEntry}
  timesheetDetails={timesheetDetails}
  onClose={closePopup}
/>
```

### **To Use Multiple Rows**
Simply add the `timesheetRows` prop:
```typescript
// Enhanced version with multiple rows
<TimesheetDetailsPopup
  selectedEntry={selectedEntry}
  timesheetDetails={timesheetDetails}
  timesheetRows={yourTimesheetArray}  // Add this line
  onClose={closePopup}
/>
```

## 🚀 **Summary**

The TimesheetDetailsPopup now supports:
- ✅ **Multiple timesheet rows** from an array
- ✅ **Complete array passed to EditTimesheet** component
- ✅ **AM/PM time display** for all rows
- ✅ **Real-time calculations** for each row
- ✅ **Total amount calculation** across all rows
- ✅ **Backward compatibility** with existing single-row usage

Your timesheet functionality is now much more powerful and flexible! 🎉
