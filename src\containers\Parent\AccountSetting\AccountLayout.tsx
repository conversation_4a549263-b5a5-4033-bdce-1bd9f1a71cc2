// AccountLayout.tsx
import React from 'react';
import styles from '../styles/account-layout.module.css';
import AboutMe from './AboutMe';
import MyFamily from './MyFamily';
import AccountSidePanel from '../../Common/AccountSidePanel';
import MyChildren from './MyChildren';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import MyAddresses from './MyAddresses';
import FamilyMembership from './FamilyMembership';
import Payments from './Payments';
import GeneralSettings from '../../Common/GeneralSettings';
import ProfileVideo from './ProfileVideo';
import Childcare from './Childcare';
import OddJobs from './OddJobs';
import AuPair from './AuPair';
import PrivacySettings from './PrivacySettings';
import Citizenship from './Citizenship';
import DrivingLanguage from './DrivingLanguage';
import References from './References';
import ResponseStats from './ResponseStats';
import Certificates from './Certificates';
import ViewProfile from './ViewProfile';
import ProfileStrength from './ProfileStrength';
import SuperHelperSettings from './SuperHelperSettings';
import Tutoring from './Tutoring';
import { PaymentMethods } from './PaymentMethodsHelper';
import useIsMobile from '../../../hooks/useIsMobile';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';
import { useNavigate } from 'react-router-dom';
import { updateActiveTabIndex } from '../../../store/slices/accountSettingSlice';
import { updateAccountAndSettingsActiveTab, updateShowAccountAndSettings } from '../../../store/slices/applicationSlice';
import JobReferral from '../JobReferral';

interface AccountLayoutProps {
    visible: boolean;
}

const AccountLayout: React.FC<AccountLayoutProps> = ({ visible }) => {
    const [isSidePanelVisible, setSidePanelVisible] = React.useState(true);
    const {isMobile}=useIsMobile();
    const { accountAndSettingsActiveTab } = useSelector(
        (state: RootState) => state.applicationState
    );

  
    const navigate = useNavigate();
    const dispatch = useDispatch<AppDispatch>();
    const toggleSidePanel = () => {
        setSidePanelVisible((prev) => !prev);
    };
    const redirectAfterHome = (id: number) => {
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set('id', id.toString());
        window.history.replaceState(
            null,
            '',
            `${window.location.pathname}?${searchParams.toString()}`
        );
    };
    function renderPanels(panelId: number) {
        switch (panelId) {
            case 0:
                return <AboutMe />;
            case 1:
                return <MyFamily />;
            case 2:
                return <MyChildren />;
            case 3:
                return <MyAddresses />;
            case 5:
                return <Payments />;
            case 6:
                return <FamilyMembership />;
            case 7:
                return <GeneralSettings />;

            case 10:
                return <ProfileVideo />;
            case 11:
                return <Childcare />;
            case 12:
                return <OddJobs />;
            case 13:
                return <AuPair />;
            case 14:
                return <PrivacySettings />;
            case 15:
                return <Citizenship />;
            case 16:
                return <DrivingLanguage />;
            case 17:
                return <References onChange={() => {}} />;
            case 18:
                return <ResponseStats />;
            case 19:
                return <Certificates />;
            case 20:
                return <ViewProfile />;
            case 21:
                return <ProfileStrength onBack={undefined} />;
            case 22:
                return <SuperHelperSettings />;
            case 23:
                return <Tutoring />;
            case 24:
                return <Payments />;
            case 25:
                return <PaymentMethods />;
                case 26: // Add Referral Case
                return <JobReferral />;
            default:
                return null;
        }
    }
    if (!visible) return null;
    return (
        <div className={styles.overlay}>
            {/* <button className={styles.toggleButton} onClick={toggleSidePanel}>
                <i className="pi pi-bars" />
            </button> */}
           {!isMobile ? (
             <div className={styles.header}>
             <h2 className={styles.headerTitle}>Account & Settings</h2>
             {/* <button className={styles.closeButton} onClick={onHide}>
                 Close
             </button> */}
         </div>
           ):(
            //  <div className={styles.headerMobile}>
            //     <h2 className={styles.headerTitleMobile}>Account & Settings</h2>
            //     {/* <button className={styles.closeButton} onClick={onHide}>
            //         Close
            //     </button> */}
            // </div>
             <HorizontalNavigation
             title='Account & Settings'
             onBackClick={() => {
                dispatch(updateActiveTabIndex(-1));
                dispatch(updateAccountAndSettingsActiveTab(-1));
                dispatch(updateShowAccountAndSettings(false));
             }}
         />
           )}

           {!isMobile  ? (
             <div className={styles.dialogContainer}>
             {isSidePanelVisible && (
                 <div className={styles.leftPane}>
                     <AccountSidePanel />
                 </div>
             )}

             <div style={{
                 width:isMobile ?"100%": ""
             }}className={styles.rightPane}>
                 <div className={styles.content}>
                     {renderPanels(accountAndSettingsActiveTab)}
                 </div>
             </div>
         </div>
           ):(
            <div className={styles.dialogContainerMobile}>
            {isSidePanelVisible && (
                <div className={styles.leftPane}>
                    <AccountSidePanel />
                </div>
            )}

            <div style={{
                width:isMobile ?"100%": ""
            }}className={styles.rightPaneMobile}>
                <div className={styles.content}>
                    {renderPanels(accountAndSettingsActiveTab)}
                </div>
            </div>
        </div>
           )}
        </div>
    );
};

export default AccountLayout;
