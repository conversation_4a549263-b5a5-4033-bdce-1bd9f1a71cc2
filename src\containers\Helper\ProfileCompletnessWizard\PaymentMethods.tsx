import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import CustomButton from '../../../commonComponents/CustomButton';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import { useEffect, useState } from 'react';
import { InputText } from 'primereact/inputtext';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";
interface FormData {
    bankAccountName: string;
    bsbNumber: string;
    bankAccountNumber: string;
    bankAccountNameErrorText?: string;
    bsbNumberErrorText?: string;
    bankAccountNumberErrorText?: string;
}

export const PaymentMethods = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { disableLoader, enableLoader } = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [hasChanges, setHasChanges] = useState<boolean>(false);
    const {isMobile}=useIsMobile();
    const [formData, setFormData] = useState<FormData>({
        bankAccountName: '',
        bsbNumber: '',
        bankAccountNumber: '',
        bankAccountNameErrorText: '',
        bsbNumberErrorText: '',
        bankAccountNumberErrorText: ''
    });

    const validateBSBNumber = (value: string) => {
        if (sessionInfo.data['country'] === 'au') {
            return value.length === 6 || value.length === 7;
        }
        return true;
    };

    const handleInputChange = (field: string, value: string) => {
        let errorText = '';
        if (field === 'bankAccountNumber' && value.length < 5) {
            errorText = 'At least 5 characters long';
        } else if (field === 'bsbNumber' && !validateBSBNumber(value)) {
            errorText = '6 or 7 digit BSB';
        }

        setFormData((prev) => ({
            ...prev,
            [field]: value,
            [`${field}ErrorText`]: errorText
        }));
        setHasChanges(true);
    };

    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };
    const paymentMethods = ['Cash', 'Online Bank Transfer'];
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');

    const handlePaymentMethodChange = (method: string) => {
        setSelectedPaymentMethod(method);
        setHasChanges(true);
    };

    useEffect(() => {
        if (sessionInfo.data?.['paymentInfo']?.interestedInBankTransfers) {
            setSelectedPaymentMethod('Online Bank Transfer');
        }
    }, [sessionInfo]);

    const handleContinue = () => {
        let hasError = false;
        // Reset error messages
        formData.bankAccountNameErrorText = '';
        formData.bsbNumberErrorText = '';
        formData.bankAccountNumberErrorText = '';

        if (selectedPaymentMethod === 'Online Bank Transfer') {
            if (!formData.bankAccountName) {
                formData.bankAccountNameErrorText = 'Bank Account Name is required';
                hasError = true;
            }

            if (!formData.bsbNumber) {
                formData.bsbNumberErrorText = 'BSB Number is required';
                hasError = true;
            } else if (!/^\d{6}$/.test(formData.bsbNumber)) {
                formData.bsbNumberErrorText = 'BSB Number must be exactly 6 digits';
                hasError = true;
            }

            if (!formData.bankAccountNumber) {
                formData.bankAccountNumberErrorText = 'Bank Account Number is required';
                hasError = true;
            } else if (formData.bankAccountNumber.length < 5) {
                formData.bankAccountNumberErrorText = 'Bank Account Number must be at least 5 digits';
                hasError = true;
            }
        }

        if (hasError) {
            // Prevent further processing if there are validation errors
            return;
        }

        const payload = {
            ...sessionInfo.data,
            paymentInfo: {
                interestedInCashPayments: selectedPaymentMethod === 'Cash',
                interestedInBankTransfers: selectedPaymentMethod === 'Online Bank Transfer',
                bankAccountName: formData.bankAccountName,
                bankAccountBsb: formData.bsbNumber,
                bankAccountNumber: formData.bankAccountNumber,
            },
        };

        enableLoader();
        dispatch(updateSessionInfo({ payload })).finally(() => {
            // dispatch(incrementProfileActivationStep());
            disableLoader();
        });
    };


    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ProfileCompletenessHeader
                title="Payment Methods"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={()=>dispatch(decrementProfileActivationStep())}
            />
            <PaymentMethodSelection
                paymentMethods={paymentMethods}
                selectedPaymentMethod={selectedPaymentMethod}
                handlePaymentMethodChange={handlePaymentMethodChange}
                formData={formData}
                handleInputChange={handleInputChange}
            />
            <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <>
                            <i className="pi pi-angle-left"></i>
                            Previous
                        </>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: "transparent",
                        color: "#585858",
                        width: "156px",
                        height: "39px",
                        fontSize: "14px",
                        fontWeight: "500",
                         margin:isMobile && "5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />

                <CustomButton
                    className={styles.hoverClass}
                    data-skip={hasChanges ? "false" : "true"}
                    onClick={hasChanges ? handleContinue : handleSkip}
                    label={
                        <>
                            {hasChanges ? "Next" : "Skip"}
                            <i
                                className={`pi pi-angle-${hasChanges ? "right" : "right"}`}
                                style={{ marginLeft: "8px" }}
                            ></i>
                        </>
                    }
                    style={
                        hasChanges
                            ? {
                                backgroundColor: "#FFA500",
                                color: "#fff",
                                width: "156px",
                                height: "39px",
                                fontWeight: "800",
                                fontSize: "14px",
                                borderRadius: "8px",
                                border: "2px solid transparent",
                                boxShadow: "0px 4px 12px #00000",
                                transition:
                                    "background-color 0.3s ease, box-shadow 0.3s ease",
                               margin : isMobile && "10px"
                            }
                            : {
                                backgroundColor: "transparent",
                                color: "#585858",
                                width: "156px",
                                height: "39px",
                                fontWeight: "400",
                                fontSize: "14px",
                                borderRadius: "10px",
                                border: "1px solid #F0F4F7",
                             margin : isMobile && "10px"
                            }
                    }
                />
            </footer>
        </div>
    )
}


const PaymentMethodSelection = ({ paymentMethods, selectedPaymentMethod, handlePaymentMethodChange, formData, handleInputChange }) => {
    const {isMobile}=useIsMobile();
    return (
        <>
            <div style={{ paddingInline:isMobile && "15px"}}>
                <h1 className='m-0 p-0 txt-clr font-medium line-height-3' style={{ fontSize: "18px" }}>
                    Parents can chose to pay by cash or online bank transfer.
                </h1>
            </div>
            <div style={{ paddingInline:isMobile && "15px"}}>
                <h1 className='m-0 p-0 txt-clr font-medium line-height-3' style={{ fontSize: "18px" }}>
                    Businesses only pay by online bank transfer.
                </h1>
            </div>
            <div style={{ paddingInline:isMobile && "15px"}}>
                <h1 className='m-0 p-0 txt-clr font-medium line-height-3' style={{ fontSize: "18px" }}>
                    You will be paid directly at the end of each job, Juggle Street does not take a percentage.
                </h1>
            </div>
            <div style={{ paddingInline:isMobile && "15px"}}>
                <h1 className='m-0 p-0 mt-2 txt-clr font-bold line-height-5' style={{ fontSize: "20px" }}>Select your payment methods below:</h1>
            </div>
            {paymentMethods?.map((method) => (
                <div key={method} className='flex items-center txt-clr font-semibold'   style={{ paddingInline:isMobile && "15px"}}>
                    <input
                        type="checkbox"
                        id={method}
                        checked={selectedPaymentMethod === method}
                        onChange={() => handlePaymentMethodChange(method)}
                        className={`${styles.customCheckbox} cursor-pointer`}
                        style={{ fontSize: '18px' }}
                    />
                    <label htmlFor={method} className='cursor-pointer'>{method}</label>
                </div>
            ))}
            {selectedPaymentMethod === 'Online Bank Transfer' && (
                <div  style={{ paddingInline:isMobile && "15px", marginBottom:"70px"}}>
                    <h2 className='m-0 p-0 txt-clr font-bold' style={{ fontSize: '18px' }}>Bank Account Details</h2>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bankAccountName"
                            type="text"
                            placeholder=" "
                            value={formData.bankAccountName}
                            onChange={(e) => handleInputChange("bankAccountName", e.target.value.replace(/[^a-zA-Z\s]/g, ""))}
                            className={`input-placeholder ${formData.bankAccountNameErrorText ? "border-red" : formData.bankAccountName ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bankAccountName"
                            className={`label-name ${formData.bankAccountName || formData.bankAccountNameErrorText ? "label-float" : ""} ${formData.bankAccountNameErrorText ? "input-error" : ""}`}
                        >
                            {formData.bankAccountNameErrorText && !formData.bankAccountName ? formData.bankAccountNameErrorText : "Bank Account Name*"}
                        </label>
                    </div>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bsbNumber"
                            type="text"
                            placeholder=" "
                            value={formData.bsbNumber}
                            onChange={(e) => handleInputChange("bsbNumber", e.target.value.replace(/\D/g, ""))}
                            className={`input-placeholder ${formData.bsbNumberErrorText ? "border-red" : formData.bsbNumber ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bsbNumber"
                            className={`label-name ${formData.bsbNumber || formData.bsbNumberErrorText ? "label-float" : ""} ${formData.bsbNumberErrorText ? "input-error" : ""}`}
                        >
                            {formData.bsbNumberErrorText && !formData.bsbNumber ? formData.bsbNumberErrorText : "BSB Number*"}
                        </label>
                    </div>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bankAccountNumber"
                            type="text"
                            placeholder=""
                            value={formData.bankAccountNumber}
                            onChange={(e) => handleInputChange("bankAccountNumber", e.target.value.replace(/\D/g, ""))}
                            className={`input-placeholder ${formData.bankAccountNumberErrorText ? "border-red" : formData.bankAccountNumber ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bankAccountNumber"
                            className={`label-name ${formData.bankAccountNumber || formData.bankAccountNumberErrorText ? "label-float" : ""} ${formData.bankAccountNumberErrorText ? "input-error" : ""}`}
                        >
                            {formData.bankAccountNumberErrorText && !formData.bankAccountNumber ? formData.bankAccountNumberErrorText : "Bank Account Number*"}
                        </label>
                    </div>
                    <h4 className='p-0 m-0 font-semibold mt-2' style={{ color: 'red' }}>Note</h4>
                    <p className='p-0 m-0 txt-clr'>
                        Your bank details will only be released when you are awarded a job with payment by online bank transfer.
                    </p>
                </div>
            )}
        </>
    );
};
export default PaymentMethodSelection;