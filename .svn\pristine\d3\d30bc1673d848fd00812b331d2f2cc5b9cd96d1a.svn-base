import { useNavigate, useSearchParams } from 'react-router-dom';
import LeftHandUserPanel from '../../Common/LeftHandUserPanel';
import HomeHeader from '../../Common/HomeHeader';
import ArrowLeft from '../../../assets/images/Icons/arrow-left.png';
import Profile from '../../../assets/images/Icons/about_me.png';
import Smiley from '../../../assets/images/Icons/my_child.png';
import Book from '../../../assets/images/Icons/book-mobile.png';
import OddJobs from '../../../assets/images/Icons/odd_job_mobile.png';
import AuPair from '../../../assets/images/Icons/my_family.png';
import Map from '../../../assets/images/Icons/mapIcon.png';
import React, { useEffect, useState } from 'react';
import SideCard from './SideCard';
import ProfileTab from './Tabs/ProfileTab';
import ChildCareTab from './Tabs/ChildCareTab';
import TutoringTab from './Tabs/TutoringTab';
import OddJobsTab from './Tabs/OddJobsTab';
import Service from '../../../services/services';
import { ExtendedProfileTabProps, Helper } from './types';
import useLoader from '../../../hooks/LoaderHook';
import utils from '../../../components/utils/util';
import CookiesConstant from '../../../helper/cookiesConst';
import AuPairTab from './Tabs/AuPairTab';
import useIsMobile from '../../../hooks/useIsMobile';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';
import '../../../components/utils/util.css';
import MapTab from './Tabs/MapTab';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';

const TabFactory: Record<number, React.FC<ExtendedProfileTabProps>> = {
    0: ProfileTab,
    1: ChildCareTab,
    2: TutoringTab,
    3: OddJobsTab,
    4: AuPairTab,
    5: MapTab,
};
interface ProviderProfileProps {
    candidateId: number;
    requestId?: number;
    onClose?: () => void;
}

const ProviderProfile: React.FC<ProviderProfileProps> = ({ candidateId, requestId, onClose }) => {
    const [activeTab, changeActiveTab] = useState(0);
    const [helper, setHelper] = useState<Helper | null>(null);
    const { isMobile } = useIsMobile();

    const [tabs, setTabs] = useState<
        Set<{
            label: string;
            icon: string;
            index: number;
        }>
    >(new Set([{ label: 'Helpers Profile', icon: Profile, index: 0 }]));
    const navigate = useNavigate();

    const [searchParams] = useSearchParams();
    const { enableLoader, disableLoader } = useLoader();
    const id = isMobile ? Number(searchParams.get('id')) : candidateId;
    const { sideBarIsOpened: sidebarIsOpen, shouldShowProfileActivation } =
    useSelector((state: RootState) => state.applicationState);
    const reqId = isMobile ? Number(searchParams.get('requestId')) : requestId;
    const Tab = TabFactory[activeTab];
    const clientType = utils.getCookie(CookiesConstant.clientType);

    useEffect(() => {
        if (isMobile) {
            // Use a more aggressive approach for mobile
            document.body.scrollTop = 0; // For Safari
            document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
        } else {
            window.scrollTo(0, 0);
        }
    }, [isMobile]);

    // var livesCloseBy = data.distanceInKiloMetersRounded <= 10;
    const lodHelper = () => {
        enableLoader();
        Service.getHelper(
            id,
            (data: Helper) => {
                const childcare = data.interestedInChildcareJobs;
                const tutoring = data.interestedInTutoringJobs;
                const oddJobs = data.interestedInOddJobs;
                const auPair = data.interestedInAuPairJobs;
                const map = data.ratings.length > 0;

                setTabs(() => {
                    const newTabs = [];
                    newTabs.push({ label: 'Helpers Profile', icon: Profile, index: 0 });
                    if (childcare) {
                        newTabs.push({ label: 'Childcare', icon: Smiley, index: 1 });
                    }
                    if (tutoring && clientType !== '2') {
                        newTabs.push({ label: 'Tutoring', icon: Book, index: 2 });
                    }
                    if (oddJobs && clientType !== '2') {
                        newTabs.push({ label: 'Odd Jobs', icon: OddJobs, index: 3 });
                    }
                    if (auPair && clientType !== '2') {
                        newTabs.push({ label: 'Au Pair', icon: AuPair, index: 4 });
                    }
                    if (map && !isMobile) {
                        newTabs.push({ label: 'Map', icon: Map, index: 5 });
                    }
                    return new Set(newTabs);
                });
                setHelper(data);
                disableLoader();
            },
            (error) => {
                disableLoader();
                // alert(error);
            }
        );
    };
    useEffect(() => {
        lodHelper();
    }, []);

    return !isMobile ? (
        <div className="flex">
            <LeftHandUserPanel activeindex={0} />
            <HomeHeader />
            <div
                className="relative flex flex-column px-6"
                style={{
                    minWidth: '1150px',
                    width: 'calc(100% - 288px)',
                    left: '288px',
                }}
            >
                <div
                    className={
                        'flex gap-2 justify-content-around align-items-center w-min mt-4 cursor-pointer box-shadow'
                    }
                    style={{
                        textWrap: 'nowrap',
                        border: '1px solid #F1F1F1',
                        padding: '10px 25px',
                        borderRadius: '20px',
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        if (isMobile) {
                            navigate(-1);
                        } else {
                            onClose();
                        }
                    }}
                >
                    <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
                    <p
                        className="m-0 p-0"
                        style={{
                            fontWeight: '400',
                            fontSize: '14px',
                            color: '#585858',
                        }}
                    >
                        Go Back
                    </p>
                </div>
                <div
                    className="flex relative my-4"
                    style={{
                        borderRadius: '30px',
                        overflow: 'hidden',
                        minHeight: '113px',
                        border: '1px solid #F1F1F1',
                        paddingInline: '40px',
                        paddingBlock: '45px 30px',
                    }}
                >
                    <div
                        className="w-full absolute top-0 left-0"
                        style={{
                            inset: '0',
                            borderRadius: '30px',
                            background:
                                'linear-gradient(90deg, rgba(255, 165, 0, 0.2), rgba(55, 169, 80, 0.2))',
                            height: '113px',
                            zIndex: 0,
                        }}
                    />

                    <div className="flex justify-content-between w-full">
                        <div className="flex flex-grow-1 flex-column">
                            <div
                                className="flex gap-2 mb-6"
                                style={{
                                    minWidth: '673px',
                                    paddingInline: '10px',
                                    zIndex: 1,
                                }}
                            >
                                {[...tabs].map((value, index) => (
                                    <div
                                        key={index}
                                        className="flex gap-2 justify-content-around align-items-center cursor-pointer"
                                        style={{
                                            backgroundColor: '#FFFFFF',
                                            borderRadius: '20px',
                                            padding: '10px 20px',
                                            border:
                                                activeTab === value.index
                                                    ? '2px solid rgba(255, 165, 0, 1)'
                                                    : '',
                                            boxShadow:
                                                activeTab === value.index
                                                    ? '0 4px 4px 0 rgba(0, 0, 0, 0.25)'
                                                    : '',
                                        }}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            changeActiveTab(value.index);
                                        }}
                                    >
                                        <img
                                            src={value.icon}
                                            alt="Profile"
                                            width="14.4"
                                            height="14.4px"
                                        />
                                        <p
                                            className="m-0 p-0"
                                            style={{
                                                fontWeight:
                                                    activeTab === value.index ? '600' : '400',
                                                fontSize: '14px',
                                                color: '#585858',
                                            }}
                                        >
                                            {value.label}
                                        </p>
                                    </div>
                                ))}
                            </div>
                            <Tab helper={helper ?? null} />
                        </div>
                        <SideCard
                            helper={helper}
                            requestId={reqId}
                            userId={id}
                            refresh={lodHelper}
                        />
                    </div>
                    <p
                        className="cursor-pointer"
                        style={{
                            marginTop: '-45px',
                            zIndex: 1,
                            fontSize: '24px',
                            color: '#585858',
                            position: 'absolute',
                            right: '18px',
                        }}
                        onClick={(e) => {
                            onClose();
                        }}
                    >
                        ⨉{' '}
                    </p>
                </div>
            </div>
        </div>
    ) : (
        <div className="flex">
            <LeftHandUserPanel activeindex={0} />
            <HomeHeader />
            <div
                className="relative flex flex-column"
                style={{
                    width: sidebarIsOpen ? "calc(100vw - 288px)" : "100vw",
                    marginLeft: sidebarIsOpen ? "288px" : "0",
                }}
            >
                <div
                    className="flex gap-2 justify-content-around align-items-center w-min mt-4 cursor-pointer"
                    style={{
                        textWrap: 'nowrap',
                        border: '1px solid #F1F1F1',
                        padding: '10px 25px',
                        borderRadius: '20px',
                    }}
                ></div>
                <HorizontalNavigation
                    title={helper?.firstName}
                    onBackClick={() => {
                        navigate(-1);
                    }}
                />
                <div
                    className="flex relative "
                    style={{
                        // borderRadius: '30px',
                        overflow: 'hidden',
                        paddingBlock: '12px 20px',
                    }}
                >
                    <div
                        className="w-full absolute left-0"
                        style={{
                            borderRadius: !isMobile &&  '0px',
                            background: !isMobile && 
                                'linear-gradient(90deg, rgba(255, 165, 0, 0.2), rgba(55, 169, 80, 0.2))',
                            height: !isMobile && '88px',
                            zIndex:!isMobile &&  -1,
                            top: !isMobile && '13px',
                        }}
                    />
                    <div className="flex justify-content-center w-full flex-column align-items-center">
                        <div
                            className={
                                !isMobile
                                    ? `${'flex flex-grow-1 flex-column align-items-center'}`
                                    : `${'flex flex-grow-1 flex-column align-items-center w-full'}`
                            }
                        >
                            {!isMobile ? (
                                <div
                                    className="flex gap-2 mb-3"
                                    style={{
                                        paddingInline: '10px',
                                    }}
                                >
                                    {[...tabs].map((value, index) => (
                                        <div
                                            key={index}
                                            className="flex gap-2 justify-content-around align-items-center cursor-pointer"
                                            style={{
                                                backgroundColor: '#FFFFFF',
                                                borderRadius: '20px',
                                                padding: '5px',
                                                border:
                                                    activeTab === value.index
                                                        ? '2px solid rgba(255, 165, 0, 1)'
                                                        : '',
                                                boxShadow:
                                                    activeTab === value.index
                                                        ? '0 4px 4px 0 rgba(0, 0, 0, 0.25)'
                                                        : '',
                                            }}
                                            onClick={(e) => {
                                                e.preventDefault();
                                                changeActiveTab(value.index);
                                            }}
                                        >
                                            <img
                                                src={value.icon}
                                                alt="Profile"
                                                width="14.4"
                                                height="14.4px"
                                            />
                                            <p
                                                className="m-0 p-0"
                                                style={{
                                                    fontWeight:
                                                        activeTab === value.index ? '600' : '400',
                                                    fontSize: '14px',
                                                    color: '#585858',
                                                }}
                                            >
                                                {value.label}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div
                                className="flex gap-2 mb-1 hide_scrollbar"
                                style={{
                                    paddingInline: '6px',
                                    width: "100%",
                                    justifyContent: "center",
                                    paddingBlock:"12px",
                                    flexWrap: 'wrap', // Already present, enables wrapping
                                    background: 'linear-gradient(90deg, rgba(255, 165, 0, 0.2), rgba(55, 169, 80, 0.2))', // Added background gradient
                                }}
                            >
                                {[...tabs].map((value, index) => (
                                    <div
                                        key={index}
                                        className="flex gap-2 justify-content-around align-items-center cursor-pointer"
                                        style={{
                                            backgroundColor: '#FFFFFF',
                                            borderRadius: '20px',
                                            padding: '5px',
                                            border: activeTab === value.index
                                                ? '2px solid rgba(255, 165, 0, 1)'
                                                : '',
                                            boxShadow: activeTab === value.index
                                                ? '0 4px 4px 0 rgba(0, 0, 0, 0.25)'
                                                : '',
                                        }}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            changeActiveTab(value.index);
                                        }}
                                    >
                                        <p
                                            className="m-0 p-0"
                                            style={{
                                                fontWeight: activeTab === value.index ? '600' : '400',
                                                fontSize: '14px',
                                                color: '#585858',
                                                textWrap: 'nowrap',
                                            }}
                                        >
                                            {value.label}
                                        </p>
                                    </div>
                                ))}
                            </div>
                            )}
                            {isMobile && activeTab === 0 && (
                                <SideCard
                                    helper={helper}
                                    requestId={reqId}
                                    userId={id}
                                    refresh={lodHelper}
                                />
                            )}

                            <Tab helper={helper ?? null} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProviderProfile;
