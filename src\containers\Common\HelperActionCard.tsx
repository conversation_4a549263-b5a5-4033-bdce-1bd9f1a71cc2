import React, { useEffect, useRef } from 'react';
import '../Common/styles/helper-action-card.css'
import { Dialog } from 'primereact/dialog';
export interface ProfileCardDialogProps {
  visible: boolean;
  onHide: () => void;
  onClose: () => void;
  onAddToFavorites: () => void;
  name: string;
  imageSrc: string;
}

const HelperActionCard: React.FC<ProfileCardDialogProps> = ({
  visible,
  onHide,
  onClose,
  onAddToFavorites,
  name,
  imageSrc
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);

  // Close dialog when clicking outside the card
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
        onClose(); // Close the dialog when clicking outside
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  return (

    <Dialog
      visible={visible}
      onHide={() => { }}
      content={
        <div className="dialog-overlay ">

          <div className="dialog-content" ref={dialogRef}>
            <div className='gradient-color'></div>

            <button className="close-icon" onClick={onClose}>&times;</button>

            <div className="image-container">
              <img
                src={imageSrc}
                alt={name}
                className="actionCard-profile-image"
              />
            </div>
            <div className="profile-name p-0">
              {name}
            </div>
            <div className="dialog-message font-normal pl-4 pr-4">
              Do you want to add {name.split(' ')[0]} to your list of favorites or hide?
            </div>
            <div className="flex flex-wrap align-items-center gap-3 p-4">
              <button className="btn btn-warning font-bold" onClick={onAddToFavorites}>
                <i className="pi pi-heart"></i> Add to Favorites
              </button>
              <div className=' text-center' style={{ color: "#585858" }}>Or</div>
              <button className="btn btn-secondary font-bold" onClick={onHide}>
                Hide
              </button>
            </div>
          </div>
        </div>
      }
    />
  );
};

export default HelperActionCard;
