/* src/components/CustomButton.module.css */

.customButton {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  background-color: rgba(255, 165, 0, 1);
  margin-top: 28px;
  min-height: 46px;
  font-size: 14px;
  color: rgba(255, 255, 255, 1);
  font-weight: 700;
  text-align: center;
  padding: 13px 10px;
  border: none;
  cursor: pointer;
  width: 100%; /* Full width by default for responsiveness */
  max-width: 362px; /* Max width for larger screens */
}

.customButton:hover,
.customButton:focus {
  background-color: rgba(255, 140, 0, 1); /* Slightly darker shade on hover/focus */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .customButton {
    max-width: 300px; /* Adjust max width for tablets */
    font-size: 12px; /* Reduce font size for smaller screens */
  }
}

@media (max-width: 480px) {
  .customButton {
    max-width: 250px; /* Adjust max width for mobile */
    padding: 10px 8px;
    font-size: 10px; /* Further reduce font size for smaller devices */
  }
}

/* src/components/CardContainer.module.css */

.cardContainer {
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.card {
  position: relative;
  margin: 16px;
  width: 100%; /* Make card take full width */
  max-width: 400px; /* Set a max-width for larger screens */
}

.logoDiv {
  text-align: center;
}

.logoImage {
  width: 100%; /* Logo scales with container */
  max-width: 239px; /* Set a maximum width */
  height: auto; /* Keep aspect ratio */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    max-width: 350px; /* Adjust card width for tablets */
  }

  .logoImage {
    max-width: 200px; /* Adjust logo size for tablets */
  }
}

@media (max-width: 480px) {
  .card {
    max-width: 100%; /* Full width on mobile */
  }

  .logoImage {
    max-width: 150px; /* Adjust logo size for mobile */
  }
}
