// environment.ts

type EnvironmentType = "production" | "staging" | "development" | "preproduction" | "vnext" | "frontendServer" | "devServer";

const Environment = {
  Production: "production",
  Staging: "staging",
  Development: "development",
  Preproduction: "preproduction",
  VNext: "vnext",
  FrontendServer: "frontendServer",
  DevServer: "devServer",
} as const;

type CountryType = "au" | "nz";

const Country = {
  Australia: "au",
  NewZealand: "nz",
} as const;

function parseHostnames(envVar: string): string[] {
  return import.meta.env[envVar]?.split(",") || [];
}

function checkHostname(hostname: string, hostnames: string[]): boolean {
  return hostnames.includes(hostname);
}

const environment = {
  getCurrentEnvironment(hostname: string): EnvironmentType {
    if (checkHostname(hostname, parseHostnames("VITE_IS_PRODUCTION_HOSTNAMES"))) {
      return Environment.Production;
    }
    if (checkHostname(hostname, parseHostnames("VITE_IS_STAGING_HOSTNAMES"))) {
      return Environment.Staging;
    }
    if (checkHostname(hostname, parseHostnames("VITE_IS_PREPRODUCTION_HOSTNAMES"))) {
      return Environment.Preproduction;
    }
    if (checkHostname(hostname, parseHostnames("VITE_IS_VNEXT_HOSTNAMES"))) {
      return Environment.VNext;
    }
    if (checkHostname(hostname, parseHostnames("VITE_IS_FRONTEND_SERVER_HOSTNAMES"))) {
      return Environment.FrontendServer;
    }
    if (hostname.includes(import.meta.env.VITE_IS_DEV_SERVER_HOSTNAME || "")) {
      return Environment.DevServer;
    }
    if (hostname.includes(import.meta.env.VITE_IS_DEV_HOSTNAME || "")) {
      return Environment.Development;
    }
    return Environment.Development; // Default to development
  },

  getCountry(hostname: string): CountryType {
    const app = (window as any).app;
    if (app?.user?.id > 0) {
      return app.user.get("country");
    }
    // Fetch environment variable for New Zealand hostnames
    const newZealandHostnames: string[] = import.meta.env.VITE_IS_NEW_ZEALAND_HOSTNAMES?.split(",") || [];

    // Check if the hostname belongs to New Zealand
    if (newZealandHostnames.some(h => hostname.includes(h))) {
      return Country.NewZealand;
    }

    // Default to Australia
    return Country.Australia;

    // if (
    //     hostname.includes(
    //         import.meta.env.VITE_IS_NEW_ZEALAND_HOSTNAME[hostname]?.split(',') || ''
    //     )
    // ) {
    //     return Country.NewZealand;
    // }
    // return Country.Australia;
  },

  getConfigValue<T>(envConfig: Record<string, T>, env: EnvironmentType, defaultValue: T): T {
    return envConfig[env] || defaultValue;
  },

  getServiceProtocol(): string {
    return import.meta.env.VITE_SERVICE_PROTOCOL || "https://";
  },

  getServiceDomain(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const domainConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_SERVICE_DOMAIN_PROD || "",
      [Environment.Staging]: import.meta.env.VITE_SERVICE_DOMAIN_STAGING || "",
      [Environment.Development]: import.meta.env.VITE_SERVICE_DOMAIN_DEV || "",
      [Environment.Preproduction]: import.meta.env.VITE_SERVICE_DOMAIN_PREPROD || "",
      [Environment.VNext]: import.meta.env.VITE_SERVICE_DOMAIN_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_SERVICE_DOMAIN_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_SERVICE_DOMAIN_DEV_SERVER || "",
    };
    return this.getConfigValue(domainConfig, env, import.meta.env.VITE_SERVICE_DOMAIN_DEV || "");
  },
  getDomainName(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const country = this.getCountry(hostname);

    if (env === Environment.Staging) {
      if (country === Country.Australia) {
        return "jugglestreet.com.au";
      } else if (country === Country.NewZealand) {
        return "jugglestreet.co.nz";
      } else {
        return "jugglestreet.com";
      }
    } else {
      return hostname;
    }
  },
  getMarketingRoot(hostname: string): string {
    const country = this.getCountry(hostname);
    if (country === Country.Australia) {
      return "jugglestreet.com.au";
    } else if (country === Country.NewZealand) {
      return "jugglestreet.co.nz";
    } else {
      return "jugglestreet.com";
    }
  },
  getServicePort(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const portConfig: Record<EnvironmentType, string> = {
      [Environment.FrontendServer]: `:${import.meta.env.VITE_SERVICE_PORT}`,
      [Environment.DevServer]: `:${import.meta.env.VITE_SERVICE_PORT}`,
      // Other environments don't use ports
      [Environment.Production]: "",
      [Environment.Staging]: "",
      [Environment.Development]: "",
      [Environment.Preproduction]: "",
      [Environment.VNext]: "",
    };
    return portConfig[env] || "";
  },

  getServiceURL(hostname: string): string {
    const protocol = this.getServiceProtocol();
    const domain = this.getServiceDomain(hostname);
    const port = this.getServicePort(hostname);
    return `${protocol}${domain}${port}`;
  },

  getMobileSite(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const country = this.getCountry(hostname);
    const mobileSiteConfig: Record<string, any> = {
      [Environment.Production]: {
        [Country.Australia]: import.meta.env.VITE_MOBILE_SITE_AU,
        [Country.NewZealand]: import.meta.env.VITE_MOBILE_SITE_NZ,
        default: import.meta.env.VITE_MOBILE_SITE_OTHER,
      },
      [Environment.Staging]: import.meta.env.VITE_MOBILE_SITE_STAGING,
      [Environment.Development]: import.meta.env.VITE_MOBILE_SITE_DEV,
      [Environment.Preproduction]: import.meta.env.VITE_MOBILE_SITE_PREPROD,
      [Environment.VNext]: import.meta.env.VITE_MOBILE_SITE_VNEXT,
      [Environment.FrontendServer]: import.meta.env.VITE_MOBILE_SITE_FRONTEND_SERVER,
      [Environment.DevServer]: import.meta.env.VITE_MOBILE_SITE_DEV_SERVER,
    };

    if (env === Environment.Production) {
      return mobileSiteConfig[Environment.Production][country] || mobileSiteConfig[Environment.Production].default;
    }

    return mobileSiteConfig[env] || mobileSiteConfig[Environment.Development];
  },

  getRaygunApiKey(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const apiKeyConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_RAYGUN_API_KEY_PROD || "",
      [Environment.Staging]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
      [Environment.Development]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
      [Environment.Preproduction]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
      [Environment.VNext]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
      [Environment.FrontendServer]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
      [Environment.DevServer]: import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "",
    };
    return apiKeyConfig[env] || import.meta.env.VITE_RAYGUN_API_KEY_OTHER || "";
  },

  getAccountsDomain(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const accountsDomainConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_ACCOUNTS_DOMAIN_PROD || "",
      [Environment.Staging]: import.meta.env.VITE_ACCOUNTS_DOMAIN_STAGING || "",
      [Environment.Development]: import.meta.env.VITE_ACCOUNTS_DOMAIN_DEV || "",
      [Environment.Preproduction]: import.meta.env.VITE_ACCOUNTS_DOMAIN_PREPROD || "",
      [Environment.VNext]: import.meta.env.VITE_ACCOUNTS_DOMAIN_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_ACCOUNTS_DOMAIN_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_ACCOUNTS_DOMAIN_DEV_SERVER || "",
    };
    return this.getConfigValue(accountsDomainConfig, env, import.meta.env.VITE_ACCOUNTS_DOMAIN_OTHER || "");
  },

  getRealtimeDomain(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const realtimeDomainConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_REALTIME_DOMAIN_PROD || "",
      [Environment.Staging]: import.meta.env.VITE_REALTIME_DOMAIN_STAGING || "",
      [Environment.Development]: import.meta.env.VITE_REALTIME_DOMAIN_DEV || "",
      [Environment.Preproduction]: import.meta.env.VITE_REALTIME_DOMAIN_PREPROD || "",
      [Environment.VNext]: import.meta.env.VITE_REALTIME_DOMAIN_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_REALTIME_DOMAIN_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_REALTIME_DOMAIN_DEV_SERVER || "",
    };
    return this.getConfigValue(realtimeDomainConfig, env, import.meta.env.VITE_REALTIME_DOMAIN_OTHER || "");
  },

  getStripePublishableKey(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const stripeKeyConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_STRIPE_PUBLISHABLE_PROD_KEY || "",
      [Environment.Staging]: import.meta.env.VITE_STRIPE_PUBLISHABLE_STAGING_KEY || "",
      [Environment.Development]: import.meta.env.VITE_STRIPE_PUBLISHABLE_DEV_KEY || "",
      [Environment.Preproduction]: import.meta.env.VITE_STRIPE_PUBLISHABLE_PRE_PROD_KEY || "",
      [Environment.VNext]: import.meta.env.VITE_STRIPE_PUBLISHABLE_VNEXT_KEY || "",
      [Environment.FrontendServer]: import.meta.env.VITE_STRIPE_PUBLISHABLE_FS_KEY || "",
      [Environment.DevServer]: import.meta.env.VITE_STRIPE_PUBLISHABLE_DS_KEY || "",
    };
    return stripeKeyConfig[env] || import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || "";
  },

  getAppInsightsSettings(hostname: string): {
    instrumentationKey: string;
    appInsightsEnabled: boolean;
  } {
    const env = this.getCurrentEnvironment(hostname);
    const appInsightsKey: Record<EnvironmentType, { instrumentationKey: string; appInsightsEnabled: boolean }> = {
      [Environment.Preproduction]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_PREPROD || "",
      [Environment.Staging]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_STAGING || "",
      [Environment.Production]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_PROD || "",
      [Environment.Development]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_DEV || "",
      [Environment.VNext]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_APPINSIGHTS_CONNECTION_STRING_DEV_SERVER || "",
    };
    const appInsightsFlag: Record<EnvironmentType, { instrumentationKey: string; appInsightsEnabled: boolean }> = {
      [Environment.Preproduction]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_PREPROD || "",
      [Environment.Staging]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_STAGING || "",
      [Environment.Production]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_PROD || "",
      [Environment.Development]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_DEV || "",
      [Environment.VNext]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_ENABLE_APP_INSIGHTS_DEV_SERVER || "",
    };
    return {
      instrumentationKey: appInsightsKey[env],
      appInsightsEnabled: appInsightsFlag[env] === "true",
    };
  },

  getStorageURL(hostname: string): string {
    const env = this.getCurrentEnvironment(hostname);
    const storageURLConfig: Record<EnvironmentType, string> = {
      [Environment.Production]: import.meta.env.VITE_STORAGE_URL_PROD || "",
      [Environment.Staging]: import.meta.env.VITE_STORAGE_URL_STAGING || "",
      [Environment.Development]: import.meta.env.VITE_STORAGE_URL_DEV || "",
      [Environment.Preproduction]: import.meta.env.VITE_STORAGE_URL_PREPROD || "",
      [Environment.VNext]: import.meta.env.VITE_STORAGE_URL_VNEXT || "",
      [Environment.FrontendServer]: import.meta.env.VITE_STORAGE_URL_FRONTEND_SERVER || "",
      [Environment.DevServer]: import.meta.env.VITE_STORAGE_URL_DEV_SERVER || "",
    };
    return this.getConfigValue(storageURLConfig, env, import.meta.env.VITE_STORAGE_URL_PROD || "");
  },

  getAppVersion(): string {
    return import.meta.env.VITE_APP_VERSION || "1.0.0";
  },

  getHelpDeskId(): string {
    return import.meta.env.VITE_HELP_DESK_ID || "";
  },

  getVersion(): string {
    return __VERSION__ || "Undefined version";
  },
};

export default environment;
