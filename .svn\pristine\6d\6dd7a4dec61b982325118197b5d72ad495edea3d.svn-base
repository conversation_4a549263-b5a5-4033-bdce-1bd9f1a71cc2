import { useState } from "react";
import Auth from "../../../services/authService";
import { useNavigateTo } from "../../../helper/navigation";
import { ResetPasswordForm } from "./ResetPasswordForm";
import Loader from "../../../commonComponents/Loader";
import { useDispatch } from "react-redux";
import { AppDispatch, updateRegisterSession } from "../../../store";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import useLoader from "../../../hooks/LoaderHook";

const ResetPassword: React.FC = () => {
  const navigateTo = useNavigateTo();
  const [formError, setFormError] = useState<string | null>(null);
  const [formsucess, setFormsucess] = useState<string | null>(null);
  const{disableLoader,enableLoader}=useLoader()
  const dispatch = useDispatch<AppDispatch>();

  const handleForgotPassword = (
    code: string,
    password: string,
    confirmPassword: string
  ) => {
    enableLoader();
    Auth.resetPassword(
      code,
      password,
      confirmPassword,
      async (response) => {
        Auth.handleAttemptLogins(
          response["email"],
          password,
          async (data) => {
            disableLoader()
            await dispatch(createNewSessionInfo(data));
            await updateRegisterSession();
            redirectAfterLogin();
          },
          (_) => {}
        );
      },
      (error) => {}
    );
  };
  const redirectAfterLogin = () => {
    setFormsucess("SucessFull");
    navigateTo("/", { replace: true });
  };

  return (
    <div className="form-container">
      <ResetPasswordForm
        onResetPassword={handleForgotPassword}
        formError={formError}
      />
    </div>
  );
};

export default ResetPassword;
