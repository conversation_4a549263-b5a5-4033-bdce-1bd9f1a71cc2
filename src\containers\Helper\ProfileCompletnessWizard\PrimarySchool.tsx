import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import CustomButton from '../../../commonComponents/CustomButton';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import OutlineButton from '../../../commonComponents/OutlineButton';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import useLoader from '../../../hooks/LoaderHook';
import { InputTextarea } from 'primereact/inputtextarea';
import { IoSchool } from 'react-icons/io5';
import { SiHyperskill } from 'react-icons/si';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';

const PrimarySchool = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [ageGroups, setAgeGroups] = useState(sessionInfo.data["provider"]["primarySchoolYears"]);
  const [subjectGroups, setSubjectGroups] = useState(sessionInfo.data["provider"]["primarySchoolSubjects"]);
  const [ageGroupError, setAgeGroupError] = useState<boolean>(false);
  const [subjectError, setSubjectError] = useState<boolean>(false);
  const [myExperience, setMyExperience] = useState(sessionInfo.data["provider"]["myExperience3"]);
  const [experienceError, setExperienceError] = useState<boolean>(false);
  const [showHighSchoolComponent, setShowHighSchoolComponent] = useState(false)
  const [hasChanges, setHasChanges] = useState(false);
  const minCharLimit = 100;
  const { disableLoader, enableLoader } = useLoader();
  const {isMobile}=useIsMobile()

  useEffect(() => {
    // Ensure "Academic" group is open by default
    setSubjectGroups(subjectGroups.map(group => ({
      ...group,
      isOpen: group.text === "Academic" ? true : group.isOpen
    })));
  }, []);


  const toggleAgeGroup = (index: number) => {
    const updatedAgeGroups = ageGroups.map((group, i) => ({
      ...group,
      selected: i === index ? !group.selected : group.selected,
    }));
    setAgeGroups(updatedAgeGroups);
    setAgeGroupError(false);
    setHasChanges(true);
  };

  const toggleSubject = (groupIndex: number, subjectIndex: number) => {
    const updatedSubjectGroups = subjectGroups.map((group, i) => ({
      ...group,
      children: group.children.map((subject, j) => ({
        ...subject,
        selected: i === groupIndex && j === subjectIndex ? !subject.selected : subject.selected,
      })),
    }));
    setSubjectGroups(updatedSubjectGroups);
    setSubjectError(false);
    setHasChanges(true);
  };

  // Toggle group collapse/expand
  const toggleGroup = (groupIndex: number) => {
    setSubjectGroups(subjectGroups.map((group, i) => ({
      ...group,
      isOpen: i === groupIndex ? !group.isOpen : group.isOpen,
    })));
  };

  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMyExperience(event.target.value);
    setExperienceError(false);
    setHasChanges(true);
  };

  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  const handleNext = async () => {
    let hasError = false;
    const selectedAgeGroups = ageGroups.some(group => group.selected);
    const selectedSubjects = subjectGroups.some(group =>
      group.children.some(subject => subject.selected)
    );
    if (!selectedAgeGroups) {
      setAgeGroupError(true);
      hasError = true;
    }
    if (!selectedSubjects) {
      setSubjectError(true);
      hasError = true;
    }
    if (!myExperience) {
      setExperienceError(true);
      hasError = true;
    }
    if (myExperience?.length < minCharLimit) {
      hasError = true;
    }
    if (hasError) {
      return;
    }
    // Only update session info when clicking Next
    const payload = {
      ...sessionInfo.data,
      provider: {
        ...sessionInfo.data["provider"],
        primarySchoolYears: ageGroups,
        primarySchoolSubjects: subjectGroups,
        myExperience3: myExperience,
      }
    };
    enableLoader();
    try {
      await dispatch(updateSessionInfo({ payload }));
      // Check if interestedInJobTypes is greater than or equal to 79
      if (sessionInfo.data['interestedInJobTypes'] > 79) {
        setShowHighSchoolComponent(true);
      }
    } finally {
      disableLoader();
      dispatch(incrementProfileActivationStep());
    }
  };

  return (
    <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
      <ProfileCompletenessHeader
        title="Primary School"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div className='flex align-items-center justify-content-center'>
        <h1
          className="p-0 m-0 txt-clr font-bold line-height-1"
          style={{ fontSize: "24px" }}
        >
          Primary School
        </h1>
      </div>
      <div className=" p-4">
        <div className="mb-4">
          <div className='flex'>
            <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0"
              style={{ fontSize: "16px", color: ageGroupError ? 'red' : '#585858' }}
            >
              <span><SiHyperskill /></span> Select age groups you work with</h1>
          </div>
          <div className="flex flex-wrap gap-2">
            {ageGroups.map((primarySchoolYears, index) => (
              <OutlineButton
                key={index}
                onClick={() => toggleAgeGroup(index)}
                style={{
                  fontSize: "14px",
                  border: primarySchoolYears.selected ? "2px solid #179D52" : "none",
                  paddingBlock: "15px",
                  fontWeight: primarySchoolYears.selected ? "700" : "500",
                  backgroundColor: primarySchoolYears.selected ? "#F0F4F7" : "#FFFFFF",
                }}
              >
                {primarySchoolYears.text}
              </OutlineButton>
            ))}
          </div>
        </div>
        <div>
          <div className='flex'>
            <h1 className="flex flex-wrap gap-1 font-medium line-height-1 p-0 m-0"
              style={{ fontSize: "18px", color: subjectError ? 'red' : '#585858' }}>
              <span><IoSchool style={{ color: '#585858', fontSize: '18px' }} /></span> Primary School Subjects
            </h1>
          </div>

          <div className="space-y-4">
            {subjectGroups.map((group, groupIndex) => (
              <div key={group.name} className="border-l-2 border-gray-200 pl-4">
                <div
                  className="flex items-center gap-2 mb-1 cursor-pointer"
                  onClick={() => toggleGroup(groupIndex)}
                >
                  <span className="transition-transform duration-200"
                    style={{ transform: group.isOpen ? 'rotate(180deg)' : 'rotate(0deg)', color: '#179D52' }}>
                    ▼
                  </span>
                  <span className="txt-clr font-semibold">{group.text}</span>
                </div>
                {group.isOpen && (
                  <div className="space-y-2 ml-4">
                    {group.children.map((subject, subjectIndex) => (
                      <label
                        key={subject.name}
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={subject.selected}
                          onChange={() => toggleSubject(groupIndex, subjectIndex)}
                          className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                        />
                        <span className="txt-clr font-medium">{subject.text}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className='flex align-items-center justify-content-center'>
        <h1
          className="p-0 m-0 txt-clr font-bold line-height-1"
          style={{ fontSize: "24px" }}
        >
          Tutoring Experience
        </h1>
      </div>
      <div style={{paddingInline:isMobile && "15px", paddingBlock:isMobile && "10px"}} className='flex'>
        <h1 className="text-base font-medium p-0 m-0 "
          style={{ fontSize: '18px', color: experienceError || myExperience?.length < minCharLimit ? 'red' : '#585858' }}
        >
          Describe your Tutoring experience
        </h1>
      </div>
      <div  style={{paddingInline:isMobile && "15px", marginBottom:isMobile && "20%"}} className="txt-clr mt-1"  >
        <InputTextarea
          autoResize
          value={myExperience}
          required
          onChange={handleTextareaChange}
          rows={3}
          cols={30}
          className={styles.inputTextareafamily}
          placeholder='How long have you been tutoring? What subjects are you passionate about? What academic qualifications do you have?
What formal teaching experience do you have? Etc.'
        />
        <p
          style={{
            fontSize: '14px',
            color: myExperience?.length < minCharLimit ? 'red' : 'green',
            fontWeight: '400',
          }}
        >
          {myExperience?.length < minCharLimit &&
            `${minCharLimit - myExperience?.length} characters remaining`}
        </p>
      </div>
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left"></i>
              Previous
            </>
          }
          onClick={() => dispatch(decrementProfileActivationStep())}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
             margin:isMobile && "5px"
          }}
        />
        <div style={{ flexGrow: 1 }} />
        <CustomButton
          className={styles.hoverClass}
          data-skip={hasChanges ? 'false' : 'true'}
          onClick={hasChanges ? handleNext : handleSkip}
          label={
            <>
              {hasChanges ? 'Next' : 'Skip'}
              <i
                className={`pi pi-angle-${hasChanges ? 'right' : 'right'}`}
                style={{ marginLeft: '8px' }}
              ></i>
            </>
          }
          style={
            hasChanges ? {
              backgroundColor: '#FFA500',
              color: '#fff',
              width: '156px',
              height: '39px',
              fontWeight: '800',
              fontSize: '14px',
              borderRadius: '8px',
              border: '2px solid transparent',
              boxShadow: '0px 4px 12px #00000',
              transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
             margin : isMobile && "10px"
            }
              : {
                backgroundColor: 'transparent',
                color: '#585858',
                width: '156px',
                height: '39px',
                fontWeight: '400',
                fontSize: '14px',
                borderRadius: '10px',
                border: '1px solid #F0F4F7',
            margin : isMobile && "10px"
              }
          }
        />
      </footer>
    </div>
  )
}

export default PrimarySchool