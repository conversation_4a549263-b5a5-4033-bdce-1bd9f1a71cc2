import React from 'react';
import styles from '../../styles/child-careTab.module.css';
import { ExtendedProfileTabProps } from '../../ProviderProfile/types';
import star from '../../../../assets/images/Icons/star.png';
import rank from '../../../../assets/images/Icons/rank.png';
import { Divider } from 'primereact/divider';
import c from '../../../../helper/juggleStreetConstants';
import tickIcon from '../../../../assets/images/Icons/check-green.png';
import earth from '../../../../assets/images/Icons/earth.png';
const ReviewAndRatingHead = ({
    rating,
    ratingCount,
    isSuperHelper,
}: {
    rating: number;
    ratingCount: number;
    isSuperHelper: boolean;
}) => {
    return (
        <div className="px-4 pt-2 mt-2 mb-4">
            <div className="flex justify-content-between align-items-center">
                <div className="flex flex-column gap-1">
                    <h1
                        className="m-0 p-0"
                        style={{
                            fontWeight: '700',
                            fontSize: '20px',
                            color: '#585858',
                        }}
                    >
                        Reviews
                    </h1>
                    <div className="flex gap-1">
                        <img src={star} alt="star" width="19.82px" height="18.62px" />
                        <p
                            className="m-0 p-0"
                            style={{
                                fontWeight: '300',
                                fontSize: '14px',
                                color: '#585858',
                            }}
                        >
                            {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
                        </p>
                    </div>
                </div>
                {isSuperHelper && (
                    <div className="flex gap-2 align-items-center">
                        <img src={rank} alt="star" width="19.82px" height="18.62px" />
                        <p
                            className="m-0 p-0"
                            style={{
                                fontWeight: '700',
                                fontSize: '18px',
                                color: '#585858',
                            }}
                        >
                            Super Helper
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};
const ReviewAndRatingList = ({
    ratings,
}: {
    ratings: Array<{
        clientFirstName: string;
        clientLastName: string;
        feedback: string;
        ratingDate: string;
        ratingAvg: number;
        clientImageUrl: string;
    }>;
}) => {
    return (
        <div className="flex flex-column px-4 pt-2 mt-2 mb-4">
            {ratings.map((rating, index) => (
                <React.Fragment key={index}>
                    <Divider />
                    <div className="flex gap-2 my-2">
                        <div
                            style={{
                                height: '38px',
                                width: '38px',
                                background: 'gray',
                                borderRadius: '50%',
                                overflow: 'hidden',
                            }}
                        >
                            <img
                                src={rating.clientImageUrl}
                                alt="client Image"
                                width="100%"
                                height="100%"
                            />
                        </div>
                        <div className="flex-grow-1 flex flex-column gap-2">
                            <div className="flex">
                                <div className="flex-grow-1 flex flex-column">
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '12px',
                                            color: '#585858',
                                        }}
                                    >
                                        {new Date(rating.ratingDate).toLocaleDateString('en-GB')}
                                    </p>
                                </div>
                                <div className="flex gap-1">
                                    <img src={star} alt="star" width="19.82px" height="18.62px" />
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '300',
                                            fontSize: '14px',
                                            color: '#585858',
                                        }}
                                    >
                                        {`${rating.ratingAvg.toFixed(1)}`}
                                    </p>
                                </div>
                            </div>
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '14px',
                                    color: '#585858',
                                }}
                            >
                                {rating.feedback}
                            </p>
                        </div>
                    </div>
                </React.Fragment>
            ))}
        </div>
    );
};
const OddJobs: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
    const selectedCountryOfCitizenship = c.countriesIso.find(
        (country) =>
            country.value.toLowerCase() === helper?.nationality?.toLowerCase() ||
            country.alpha2.toLowerCase() === helper?.nationality?.toLowerCase()
    );
    return (
        <div>
            <div className={styles.childCareContainer}>
                <div className={styles.childCareBoxOne}>
                    {/* <div style={{ display: 'flex', flexDirection: 'row', gap: '390px' }}> */}
                    <h1
                        style={{
                            fontSize: '16px',
                            fontWeight: '700',
                            color: '#585858',
                            margin: '0px',
                        }}
                    >
                        Services
                    </h1>
                    {/* </div> */}
                    <div className='flex justify-content-between'>
                        <div
                            style={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: '15px',
                                paddingLeft: '10px',
                                marginTop: '10px',
                                maxWidth: '680px'
                            }}
                        >
                            {helper.oddJobPreferences.laundry && (
                                <div className={styles.row}>
                                    <span>Laundry</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                            {helper.oddJobPreferences.errands && (
                                <div className={styles.row}>
                                    <span>Errand running</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                            {helper.oddJobPreferences.outdoorChores && (
                                <div className={styles.row}>
                                    <span>Outdoor chores</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                            {helper.oddJobPreferences.elderlyHelp && (
                                <div className={styles.row}>
                                    <span>Help for the elderly</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}

                            {helper.oddJobPreferences.otherOddJobs && (
                                <div className={styles.row}>
                                    <span>Other Odd Jobs</span>
                                    <img src={tickIcon} alt="tick" className={styles.tick} />
                                </div>
                            )}
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                marginLeft: '15px',
                                marginTop: '10px',
                            }}
                        >
                            <h1
                                style={{
                                    fontSize: '16px',
                                    fontWeight: '700',
                                    color: '#585858',
                                    margin: '0px',
                                }}
                            >
                                Nationality
                            </h1>
                            <div
                                className="flex gap-2 justify-content-center align-items-center"
                                style={{
                                    width: '145.28px',
                                    height: '42.28px',
                                    borderRadius: '20px',
                                    backgroundColor: '#F1F1F1',
                                }}
                            >
                                <img src={earth} alt="" />
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '12px',
                                        color: '#585858',
                                    }}
                                >
                                    {selectedCountryOfCitizenship?.label || 'Not specified'}
                                    {/* {selectedCountryOfCitizenship?.label} */}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br />
            {(helper?.hasVouches) && (
                <>
                    <div className={styles.childCareContainer}>
                        <div className={styles.childCareBoxOne}>
                            <div>
                                <h1
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '20px',
                                        color: '#585858',
                                    }}
                                >
                                    References
                                </h1>
                                <p>Available on request. Please contact Customer Service to obtain referee details.</p>
                            </div>
                        </div>
                    </div>
                </>
            )}
            <br />
            {(helper?.providerReviewsCount ?? 0) > 0 && (
                <div className={styles.childCareContainer}>
                    <div className={styles.childCareBoxOne}>
                        <>
                            <ReviewAndRatingHead
                                rating={helper?.providerRatingsAvg ?? 0}
                                ratingCount={helper?.providerRatingsCount ?? 0}
                                isSuperHelper={helper?.isSuperProvider ?? false}
                            />
                            {(helper?.ratingsExtended.length ?? 0) > 0 && (
                                <ReviewAndRatingList ratings={helper.ratingsExtended} />
                            )}
                        </>
                    </div>
                </div>
            )}
        </div>
    )
}

export default OddJobs