import { Marker, OverlayView } from '@react-google-maps/api';
import { GeoSearchFeature } from '../../hooks/SearchGeoSearchHook';
import MarkerPin1 from '../../assets/images/map-legend-1.png';
import MarkerPin2 from '../../assets/images/map-legend-2.png';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { TbChecklist } from 'react-icons/tb';
import { PiChatCircleTextBold } from 'react-icons/pi';
import { IoClose, IoStarSharp } from 'react-icons/io5';
import environment from '../../helper/environment';
import { useNavigate } from 'react-router-dom';
import utils from '../../components/utils/util';
import CookiesConstant from '../../helper/cookiesConst';
import { SlLocationPin } from 'react-icons/sl';
import CustomDialog from '../../commonComponents/CustomDialog';
import ProviderProfile from './ProviderProfile/ProviderProfile';

interface MarkerWithInfoWindowProps extends GeoSearchFeature {
    map: google.maps.Map;
    position?: { lat: number; lng: number };
    title?: string;
    description?: string;
    imageUrl?: string;
}

const MemoizedOverlay = React.memo(
    ({
        overlayRef,
        handleCloseInfoWindow,
        imageUrl,
        value,
        viewProvider,
        viewClientDetails,
    }: {
        overlayRef: React.MutableRefObject<HTMLDivElement>;
        handleCloseInfoWindow: () => void;
        imageUrl: string;
        value: MarkerWithInfoWindowProps;
        viewProvider: () => void;
        viewClientDetails?: () => void;
    }) => {
        const clientType = utils.getCookie(CookiesConstant.clientType);
        return (
            <div
                ref={overlayRef}
                className='h-10rem w-max shadow-5 border-round-xl flex relative'
                style={{ transform: 'translate(-105%, -120%)' }}
            >
                <div
                    className='bg-white shadow-4 text-xl absolute flex justify-content-center align-items-center p-1 cursor-pointer z-3'
                    style={{ borderRadius: '50%', right: 0, top: -10 }}
                    onClick={handleCloseInfoWindow}
                >
                    <IoClose />
                </div>
                <div
                    className=' flex z-2 bg-white over border-round-xl'
                    style={{
                        width: '380px',
                        maxWidth: '420px',
                    }}
                >
                    <div className='h-full border-round-left-xl relative'>
                        <img
                            className='h-full border-round-left-xl'
                            style={{ objectFit: 'contain', width: '10rem' }}
                            src={imageUrl}
                            alt='helper image'
                        />
                        {clientType !== '0' && value.properties.metadata?.isSuperProvider && (
                            <div
                                className='absolute w-full flex justify-content-center'
                                style={{ bottom: '10px' }}
                            >
                                <p
                                    className='m-0 border-round-2xl w-min'
                                    style={{
                                        background: '#444444',
                                        color: '#ffffff',
                                        textWrap: 'nowrap',
                                        padding: '5px 18px',
                                        fontWeight: '700',
                                        fontSize: '10px',
                                    }}
                                >
                                    Super Helper
                                </p>
                            </div>
                        )}
                    </div>
                    <div className='flex-grow-1 flex flex-column p-3 gap-2'>
                        <h1
                            className='m-0 p-0'
                            style={{
                                fontWeight: '700',
                                color: '#585858',
                                fontSize: '22px',
                                fontFamily: 'Poppins',
                            }}
                        >
                            {value.properties.publicName}
                        </h1>
                        {clientType !== '0' ? (
                            <div className='w-full flex flex-column text-xl gap-2'>
                                <div className='flex align-items-center gap-2'>
                                    <TbChecklist style={{ color: '585858' }} />
                                    <span
                                        className='flex align-items-center gap-1'
                                        style={{
                                            color: '#585858',
                                            fontWeight: '400',
                                            fontSize: '14px',
                                            fontFamily: 'Poppins',
                                        }}
                                    >
                                        <p className='m-0 p-0'>{value.properties.jobsCompleted}</p>
                                        <p className='m-0 p-0'>Jobs Completed</p>
                                    </span>
                                </div>
                                <div className='flex align-items-center gap-2'>
                                    <PiChatCircleTextBold
                                        style={{
                                            color: '585858',
                                            transform: 'rotateY(180deg)',
                                        }}
                                    />
                                    <span
                                        className='flex align-items-center gap-1'
                                        style={{
                                            color: '#585858',
                                            fontWeight: '400',
                                            fontSize: '14px',
                                            fontFamily: 'Poppins',
                                        }}
                                    >
                                        <p className='m-0 p-0'>
                                            {value.properties.metadata?.responseRate ?? 0}
                                        </p>
                                        <p className='m-0 p-0'>Hour response rate</p>
                                    </span>
                                </div>
                                <button
                                    className='border-none w-min align-self-center'
                                    style={{
                                        textWrap: 'nowrap',
                                        color: '#ffffff',
                                        fontWeight: '600',
                                        fontSize: '14px',
                                        backgroundColor: '#FFA500',
                                        padding: '2px 15px',
                                        cursor: 'pointer',
                                        fontFamily: 'Poppins',
                                        borderRadius: '10px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        viewProvider();
                                    }}
                                >
                                    Helper Details
                                </button>
                            </div>
                        ) : (
                            <div className='w-full flex flex-column text-xl gap-2'>
                                <div className='flex align-items-center gap-2'>
                                    <SlLocationPin color='#37A950' fontSize={'18px'} />
                                    <span
                                        className='flex align-items-center gap-1'
                                        style={{
                                            color: '#585858',
                                            fontWeight: '400',
                                            fontSize: '14px',
                                            fontFamily: 'Poppins',
                                        }}
                                    >
                                        <p className='m-0 p-0'>{value.properties.suburb}</p>
                                    </span>
                                </div>
                                <div className='flex align-items-center gap-2'>
                                    <IoStarSharp color='#FFA500' fontSize={'18px'} />
                                    <span
                                        className='flex align-items-center gap-1'
                                        style={{
                                            color: '#585858',
                                            fontWeight: '400',
                                            fontSize: '14px',
                                            fontFamily: 'Poppins',
                                        }}
                                    >
                                        <p className='m-0 p-0'>{value.properties.ratingsAvg}</p>
                                        <p className='m-0 p-0'>Rating</p>
                                    </span>
                                </div>
                                <button
                                    className='border-none w-min align-self-center'
                                    style={{
                                        textWrap: 'nowrap',
                                        color: '#ffffff',
                                        fontWeight: '600',
                                        fontSize: '14px',
                                        backgroundColor: '#FFA500',
                                        padding: '2px 15px',
                                        cursor: 'pointer',
                                        fontFamily: 'Poppins',
                                        borderRadius: '10px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        viewClientDetails();
                                    }}
                                >
                                    Client Details
                                </button>
                            </div>
                        )}
                    </div>
                </div>
                <div
                    className='bg-white absolute shadow-3'
                    style={{
                        right: -5,
                        bottom: -5,
                        height: '50px',
                        width: '50px',
                        borderRadius: '80px 0 80px 0',
                        transform: 'rotateZ(-95deg)',
                    }}
                />
            </div>
        );
    }
);

function MarkerWithInfoWindow({ map, ...value }: MarkerWithInfoWindowProps) {
    const [infoWindowShown, setInfoWindowShown] = useState<boolean>(false);
    const [overlayPosition, setOverlayPosition] = useState<google.maps.LatLng | null>(null);

    const navigate = useNavigate();

    const overlayRef = useRef<HTMLDivElement | null>(null);
    const markerRef = useRef<Marker>();
    const boundsListenerRef = useRef<google.maps.MapsEventListener | null>(null);

    const imageUrl = useMemo(() => {
        return `${environment.getStorageURL(window.location.hostname)}/images/${value.properties.imageSrc
            }`;
    }, [value.properties.imageSrc]);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
                handleCloseInfoWindow();
            }
        }
        document.addEventListener('mouseup', handleClickOutside);
        return () => {
            document.removeEventListener('mouseup', handleClickOutside);
        };
    }, []);

    const viewProvider = () => {
        const newParams = new URLSearchParams();
        newParams.set('id', String(value.properties.id));
        navigate({
            pathname: 'provider-profile',
            search: newParams.toString(),
        });
    };
    const viewClientDetails = () => {
        const newParams = new URLSearchParams();
        newParams.set('id', String(value.properties.id));
        navigate({
            pathname: 'client-profile',
            search: newParams.toString(),
        });
    };
    const checkOverlayBounds = () => {
        if (!infoWindowShown || !overlayRef.current || !map) return;

        const mapBounds = map.getBounds();
        const overlayBounds = overlayRef.current?.getBoundingClientRect();

        if (overlayBounds && mapBounds) {
            const mapDiv = map.getDiv();
            const mapRect = mapDiv.getBoundingClientRect();

            const isOutsideX =
                overlayBounds.left < mapRect.left || overlayBounds.right > mapRect.right;
            const isOutsideY =
                overlayBounds.top < mapRect.top || overlayBounds.bottom > mapRect.bottom;

            if (isOutsideX || isOutsideY) {
                const markerPosition = markerRef.current?.marker?.getPosition();
                if (markerPosition) {
                    const projection = map.getProjection();
                    if (projection) {
                        const markerPoint = projection.fromLatLngToPoint(markerPosition);
                        const mapSize = new google.maps.Size(mapRect.width, mapRect.height);

                        const offsetX = (mapSize.width * 0) / Math.pow(2, map.getZoom());
                        const offsetY = (mapSize.height * -0.45) / Math.pow(2, map.getZoom());

                        const newPoint = new google.maps.Point(
                            markerPoint.x + offsetX,
                            markerPoint.y + offsetY
                        );

                        const newLatLng = projection.fromPointToLatLng(newPoint);
                        if (newLatLng) {
                            setInfoWindowShown(false); // Hide info window during pan
                            map.panTo(newLatLng); // Pan map to adjusted position
                            setOverlayPosition(markerPosition); // Reset overlay position
                        }
                    }
                }
            }
        }
    };

    useEffect(() => {
        if (infoWindowShown && map) {
            // Initial check after a small delay to ensure overlay is mounted
            const initialCheckTimeout = setTimeout(checkOverlayBounds, 100);

            // Set up bounds changed listener
            boundsListenerRef.current = map.addListener('bounds_changed', checkOverlayBounds);

            return () => {
                clearTimeout(initialCheckTimeout);
                if (boundsListenerRef.current) {
                    google.maps.event.removeListener(boundsListenerRef.current);
                    boundsListenerRef.current = null;
                    setInfoWindowShown(true);
                }
            };
        }
    }, [infoWindowShown, map]);

    const handleMarkerClick = () => {
        const latLng = markerRef.current?.marker?.getPosition();
        if (latLng) {
            setOverlayPosition(latLng);
            setInfoWindowShown(true);
        }
    };

    const handleCloseInfoWindow = () => {
        // Clean up bounds listener before closing
        if (boundsListenerRef.current) {
            google.maps.event.removeListener(boundsListenerRef.current);
            boundsListenerRef.current = null;
        }
        setInfoWindowShown(false);
        setOverlayPosition(null);
    };
    const [showPopup, setShowPopup] = useState(false);
    const id = value.properties.id;
    const requestId = value.properties.requestId;
    const redirectProfilePopup = () => {
        setShowPopup(true);
    };
    const handleCloseProfilePopup = () => {
        setShowPopup(false);
        // setSelectedCandidate(null);
    };
    
    return (
        <div>
            <Marker
                ref={markerRef}
                clickable={!value.properties.isCenterPoint}
                position={{
                    lat: value.geometry.coordinates[1],
                    lng: value.geometry.coordinates[0],
                }}
                icon={{
                    url: value.properties.isCenterPoint ? MarkerPin1 : MarkerPin2,
                    scaledSize: value.properties.isCenterPoint
                        ? undefined
                        : !infoWindowShown
                            ? undefined
                            : new google.maps.Size(20, 30),
                }}
                cursor={value.properties.isCenterPoint ? '' : 'pointer'}
                zIndex={
                    value.properties.isCenterPoint
                        ? google.maps.Marker.MAX_ZINDEX + 3
                        : google.maps.Marker.MAX_ZINDEX + 2
                }
                onClick={handleMarkerClick}
            />
            {infoWindowShown && overlayPosition && (
                <OverlayView mapPaneName='overlayMouseTarget' position={overlayPosition}>
                    {
                        <MemoizedOverlay
                            imageUrl={imageUrl}
                            value={{ ...value, map: map }}
                            overlayRef={overlayRef}
                            viewProvider={redirectProfilePopup}
                            viewClientDetails={viewClientDetails}
                            handleCloseInfoWindow={handleCloseInfoWindow}
                        />
                    }
                </OverlayView>
            )}
            <CustomDialog
                visible={showPopup}
                style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%', backgroundColor: '#ffffff', borderRadius: '0px', overflowY: 'auto' }}
                onHide={() => { }}
                draggable={false}
            >
                <ProviderProfile
                    candidateId={id}
                    requestId={requestId}
                    onClose={handleCloseProfilePopup}
                />
            </CustomDialog>
        </div>
    );
}

export default React.memo(MarkerWithInfoWindow);
