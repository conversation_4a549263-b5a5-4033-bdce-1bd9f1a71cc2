export type ManageJobSectionProps = {
  upComingJobs: Jobs[];
  jobHistory: Jobs[];
  unRatedJobs: Jobs[];
  viewJob?: (jobId: number ,  showAnalytics?: boolean) => void;
  refresh?: () => void;
  isLoading?: JobLoadingStates; // Add this
};
export type MyJobSectionProps = {
  upComingJobs: Jobs[];
  jobHistory: Jobs[];
  unRatedJobs: Jobs[];
  CompletedJobs: Jobs[];
  viewJob?: (jobId: number) => void;
  refresh?: () => void;
};
export interface ApplicantAvailability {
  jobApplicantId: number;
  dayOfWeek: number;
  availabilityStatus: number;
  comments: any;
  jobStartTime: string;
  jobEndTime: string;
  isRequired: any;
  price: any;
  id: number;
}

export interface WeeklyScheduleEntries {
  applicantId: number;
  comments: string;
  dayOfWeek: number;
  id: number;
  isRequired: boolean;
  jobEndTime: string;
  jobStartTime: string;
  price: number;
  weeklyScheduleId: number;
}

export interface WeeklySchedule {
  id: number;
  isActive: boolean;
  scheduleName: string;
  weeklyScheduleEntries: WeeklyScheduleEntries[];
}

export interface Applicants {
  applicantId: number;
  applicationMessage: any;
  applicationStatus: number;
  applicantFirstName: any;
  applicantLastInitial: any;
  applicantImageSrc: any;
  applicantPhoneNumber: any;
  applicantBankAccountName: any;
  applicantBankAccountBsb: any;
  applicantBankAccountNumber: any;
  applicantIsHidden: boolean;
  applicantIsDisabled: boolean;
  applicantEmail: any;
  applicantFriendStatus: number;
  gettingHome: number;
  unavailableReason: number;
  withdrawReason: number;
  completedJobsCount: number;
  matchType: any;
  matchesCount: any;
  hasBeenRated: boolean;
  numOfMatches: any;
  applicantAvailability: ApplicantAvailability[];
  creationDate: any;
  withdrawDate: any;
  applicationDate: any;
  applicantRatingsAvg: number;
  applicantRatingsCount: number;
  applicantReviewsCount: number;
  suburb: any;
  distanceInKiloMetersRounded: any;
  id: number;
  responseRate: number;
}

export type Jobs = {
  tokenId: any;
  actionType: any;
  relatedJobId: any;
  jugglerId: number;
  expirationDate: string;
  expiresInDays: number;
  repostCount: number;
  expirationExtensionCount: number;
  jobDate: string;
  jobDateDay: any;
  jobDateMonth: any;
  jobDateYear: any;
  creationDate: string;
  jobFinishDate: string;
  jobStartTime: string;
  jobEndTime: string;
  jobType: number;
  jobSubType: number;
  jobSettings: any;
  pricePerWeek: number;
  group: number;
  addressId: any;
  managedBy: number;
  invitationsCount: number;
  invitationsPerAttempt: number;
  invitationAttempts: number;
  targetInvitationAttempts: number;
  managementStatus: number;
  targetApplicationsCount: number;
  isRecurringJob: boolean;
  isTutoringJob: boolean;
  isConciergeJob: boolean;
  price: number;
  isPriceNegotiable: boolean;
  paymentType: number;
  willPayOvertime: boolean;
  overtimePaymentType: any;
  overtimeRate: any;
  jobStatus: number;
  applicants: Array<Applicants>;
  newApplicants: any;
  applicationSummaries: any;
  applicantAvailability: ApplicantAvailability[];
  applicantsTotal: number;
  applicationStatus: number;
  applicantsViewed: number;
  applicantsNotAvailable: number;
  applicantsApplied: number;
  applicantsCancelled: number;
  applicantsNotInterested: number;
  awardedApplicantId: number;
  awardNewApplicant: boolean;
  awardedApplicantIds: any;
  awardedFirstName: any;
  awardedPhoneNumber: any;
  awardedIsHidden: any;
  awardedIsDisabled: any;
  awardedBankAccountName: any;
  awardedBankAccountBsb: any;
  awardedBankAccountNumber: any;
  awardedApplicantGettingHome: number;
  jobDescription: any;
  specialInstructions: string;
  suburb: string;
  addressLabel: string;
  weeklySchedule: any;
  applicantFilters: Array<{ field: string; operator: string; value: any }>;
  jobInvitations: any;
  formattedAddress: string;
  awardedLastInitial: any;
  awardedImageSrc: any;
  isRatedByClient: boolean;
  isRatedByProvider: boolean;
  ratingProvided: number;
  weeklyScheduleId: any;
  jobFinishType: number;
  durationType: number;
  jobStartType: number;
  jobStartDurationType: any;
  durationToStart: any;
  duration: number;
  deviceType: any;
  userPaymentType: any;
  helperPaymentMethod: number;
  jobDeliveryMethod: number;
  extendExpirationDateBy: any;
  returnUpdatedJob: any;
  latitude: number;
  longitude: number;
  id: number;
  ownerImageSrc: string;
  ownerFirstName: string;
  ownerLastInitial: string;
  ownerPublicName: string;
  ownerClientType: number;
  jobOwnerId: number;
  ownerPhoneNumber: string;
  gettingHome: number;
};

export enum JobLoadingStates {
  initial = 0,
  loading = 1,
  finished = 2,
  error = 3,
}
