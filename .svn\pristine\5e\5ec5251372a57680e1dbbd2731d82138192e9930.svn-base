import styles from '../../styles/time-sheet-card.module.css';
import ChildcareImage from "../../../../assets/images/Icons/childcare-smile.png";
import { PiUserCircle } from 'react-icons/pi';
import { Divider } from 'primereact/divider';
import calendar from "../../../../assets/images/Icons/calender.png";
import home from "../../../../assets/images/Icons/home.png";
interface TimeSheetCardProps {
  status: string;
  type: string;
  date: string;
  location: string;
  userName?: string;
  onReview: () => void;
  userImage?: string;
  buttonText?: string;
}

function TimeSheetCard({ status, type, date, location, userName, onReview, userImage,buttonText }: TimeSheetCardProps) {
  // Function to get status class for styling
  const getStatusClass = (status: any) => {
    const statusStr = String(status).toLowerCase();

    if (statusStr.includes('pending') || statusStr.includes('review')) {
      return `${styles.status} ${styles.pending}`;
    } else if (statusStr.includes('approved')) {
      return `${styles.status} ${styles.approved}`;
    } else if (statusStr.includes('declined')) {
      return `${styles.status} ${styles.declined}`;
    }
    return styles.status;
  };


  return (
    <div className='mx-3 my-3'>
      <div className={styles.card}>
        <div className='flex flex-row justify-content-between h-full py-3 px-3'>
          <div className={styles.cardContent}>
            <div className={styles.header}>
              <span style={{ fontSize: "16px", fontWeight: "700", color: "#585858" }}>Status:</span>
              <span className={getStatusClass(status)}>{status ?? 'N/A'}</span>
            </div>

            <div className={styles.infoRow}>
              <img
                src={ChildcareImage}
                alt="Job Type"
                width={18}
                height={18}
              />
              <span className={styles.spantag}>{type ?? 'N/A'}</span>
            </div>

            <div className={styles.infoRow}>
              <img
                alt="Date"
                src={calendar}
                style={{ width: "16px", height: "16px" }}
              />
              <span className={styles.spantag}>{date ?? 'N/A'}</span>
            </div>

            <div className={styles.infoRow}>
              <img
                alt="Location"
                src={home}
                style={{ width: "16px", height: "16px", flexShrink: 0 }}
              />
              <span className={styles.spantag}>{location ?? 'N/A'}</span>
            </div>
          </div>

          <div className={styles.user}>
            {userImage ? (
              <img
                src={userImage}
                alt="User"
                className={styles.avatar}
              />
            ) : (
              <PiUserCircle className={styles.avatar} />
            )}
            <span className={styles.userName}>{userName ?? 'N/A'}</span>
          </div>
        </div>

        <Divider />

        <div className='flex justify-content-center'>
          <button
            onClick={onReview}
            className={styles.button}
          >
               {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
}

export default TimeSheetCard;