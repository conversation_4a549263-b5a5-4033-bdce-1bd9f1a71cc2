import { useState, useEffect } from 'react';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';

export interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  statusCode: number; // Add status code for filtering
}

interface TimesheetApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
  2: "Adjusted Timesheet", // Status 2 for adjusted-timesheets
  3: "Awaiting Approval",  // Status 3 for awaiting-approval
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

// Static sample data based on your API response structure
const staticTimesheetData: TimesheetApiItem[] = [
  {
    id: 1,
    status: 2, // Adjusted timesheet
    jobType: 2,
    jobDate: "2025-06-24T00:00:00",
    formattedAddress: "Marrickville, Nsw, 2204, Arncliffe NSW 2205",
    firstName: "Natasha",
    lastName: "Kelley",
    originalImageUrl: "https://jsstagingaue.blob.core.windows.net/images/org_profile_picture_c3bf730d6bf24853a6b37d4b00c6f6d5.jpeg"
  },
  {
    id: 2,
    status: 2, // Adjusted timesheet
    jobType: 1,
    jobDate: "2025-06-25T00:00:00",
    formattedAddress: "Sydney, NSW, 2000",
    firstName: "John",
    lastName: "Smith",
    originalImageUrl: "https://jsstagingaue.blob.core.windows.net/images/profile_picture_sample.jpeg"
  },
  {
    id: 3,
    status: 3, // Awaiting approval
    jobType: 3,
    jobDate: "2025-06-26T00:00:00",
    formattedAddress: "Melbourne, VIC, 3000",
    firstName: "Sarah",
    lastName: "Johnson",
    originalImageUrl: "https://jsstagingaue.blob.core.windows.net/images/profile_picture_sample2.jpeg"
  },
  {
    id: 4,
    status: 3, // Awaiting approval
    jobType: 2,
    jobDate: "2025-06-27T00:00:00",
    formattedAddress: "Brisbane, QLD, 4000",
    firstName: "Mike",
    lastName: "Wilson",
    originalImageUrl: "https://jsstagingaue.blob.core.windows.net/images/profile_picture_sample3.jpeg"
  }
];

export const useStaticTimesheetData = () => {
  const [allTimesheetData, setAllTimesheetData] = useState<TimesheetEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  const fetchTimesheetData = async (): Promise<void> => {
    console.log('🔄 Starting static timesheet data fetch...');
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use static data instead of API call
      const response = staticTimesheetData;
      
      console.log("Static Timesheet Data:", response);
      
      if (!Array.isArray(response)) {
        console.warn("Expected array response, got:", typeof response);
        setAllTimesheetData([]);
        return;
      }

      const mappedData: TimesheetEntry[] = response.map((item) => ({
        id: item.id,
        status: statusMap[item.status] || "Unknown",
        statusCode: item.status, // Keep original status code for filtering
        type: jobTypeMap[item.jobType] || "Unknown",
        date: new Date(item.jobDate).toLocaleDateString('en-AU', {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
        }),
        location: item.formattedAddress,
        userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
        originalImageUrl: item.originalImageUrl,
      }));

      console.log("Mapped static timesheet data:", mappedData);
      setAllTimesheetData(mappedData);
      console.log('✅ Static timesheet data loaded successfully, count:', mappedData.length);
      
    } catch (err) {
      console.error('Fetch static timesheet data failed:', err);
      setError('Failed to fetch timesheet data');
    } finally {
      console.log('🔄 Finishing static timesheet data fetch...');
      setIsLoading(false);
      disableLoader();
      console.log('✅ Loader disabled, isLoading set to false');
    }
  };

  useEffect(() => {
    fetchTimesheetData();
  }, []);

  const refreshData = () => {
    fetchTimesheetData();
  };

  // Filter data by status
  const getTimesheetsByStatus = (statusCode: number): TimesheetEntry[] => {
    return allTimesheetData.filter(item => item.statusCode === statusCode);
  };

  // Get data for specific tabs
  const adjustedTimesheetData = getTimesheetsByStatus(2); // Status 2
  const awaitingApprovalData = getTimesheetsByStatus(3); // Status 3

  return {
    allTimesheetData,
    adjustedTimesheetData,
    awaitingApprovalData,
    isLoading,
    error,
    refreshData,
    getTimesheetsByStatus
  };
};

export default useStaticTimesheetData;
