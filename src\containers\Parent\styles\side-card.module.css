.videoBtn {
  width: 119px;
  height: 41px;
  border: none;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  background-color: #179d52;
  border: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 800;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 9px;
}

.photoBtn {
  width: 119px;
  height: 41px;
  border: none;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  background-color: #f1f1f1;
  border: none;
  color: #585858;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 9px;
}

.InfoDiv {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.InfoName {
  font-size: 26px;
  font-weight: 700;
  line-height: 39px;
  color: #585858;
  margin: 0px;
}

.InfoSpan {
  font-size: 16px;
  font-weight: 275;
  color: #585858;
  line-height: 24px;
  margin: 0px;
}

.superBtn {
  width: 141px;
  height: 25px;
  border: none;
  border-radius: 10px;
  background-color: #444444;
  color: #ffffff;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 10px;
}

.rateCount {
  font-size: 14px;
  font-weight: 700;
  color: #ffa500;
}

.helperBtn {
  width: 282px;
  height: 43px;
  border: none;
  border-radius: 10px;
  background-color: #ffa500;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  color: #ffffff;
  margin-top: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.helperHide {
  background-color: transparent;
  color: rgba(88, 88, 88, 1);
  font-size: 16px;
  font-weight: 300;
  line-height: 24px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  cursor: pointer;
}

.helperHide:hover {
  border-radius: 10px;
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.25);
  width: 140px;
}

.photRender {
  width: 207px;
  height: 238px;
  border-radius: 10px;
}

.dialogContent {
  background-color: #ffffff;
  width: 610px;
  border-radius: 20px;
  padding: 20px;
}

.buttonOk {
  width: 88px;
  height: 42px;
  background-color: transparent;
  box-shadow: 0px 0px 4px 0px #00000040;
  text-decoration: underline;
  font-size: 18px;
  font-weight: 500;
  color: #585858;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}

.buttonPost {
  width: 209.78px;
  height: 42px;
  background-color: #ffa500;
  box-shadow: 0px 0px 4px 0px #00000040;
  text-decoration: underline;
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}

.CloseBtn {
  display: flex;
  justify-content: end;
  position: absolute;
  right: -5px;
  width: 30px;
  height: 32px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  top: -9px;
  cursor: pointer;
}

.HeartButton {
  position: absolute;
  height: 43px;
  width: 41px;
  top: -20px;
  right: 6px;
  background-color: #ffffff;
  border-radius: 50%;
  border: 0.1px solid #ebe7e7;
}

.HeartButton:hover {
  box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.25);
}
.photRenderMobile {
  width: 268px;
  height: 260px;
  border-radius: 10px;
}
.HeartButtonMobile {
  height: 33px;
  width: 31px;
  top: -20px;
  right: 6px;
  background-color: #ffffff;
  border-radius: 50%;
  border: 0.1px solid #ebe7e7;
}

.HeartButtonMobile:hover {
  box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.25);
}
.helperBtnMobile {
  border: none;
  border-radius: 10px;
  background-color: #ffa500;
  font-size: 16px;
  font-weight: 700;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding-inline: 10px;
  padding-block: 7px;
}