import React, { useEffect, useState } from "react";
import Auth from "../../../services/authService";
import { LoginForm } from "./LoginForm";
import { useNavigateTo } from "../../../helper/navigation";
import { useDispatch } from "react-redux";
import { AppDispatch, updateRegisterSession } from "../../../store";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import useLoader from "../../../hooks/LoaderHook";
import utils from "../../utils/util";

const Login: React.FC = () => {
  const navigateTo = useNavigateTo();
  const [error, setError] = useState<JSX.Element | string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();

  const handleLogin = (email: string, password: string) => {
    setError(null);
    enableLoader(); // Start loader
    Auth.handleAttemptLogins(email, password, success, failure);
  };

  const success = (data) => {
    disableLoader(); // Stop loader on success
    dispatch(createNewSessionInfo(data));
    updateRegisterSession();
    navigateTo("/", { replace: true });
  };

  const failure = (error: any) => {
    setFormError("Invalid email or password.");
    disableLoader(); // Stop loader on success
    const errorMessage = (
      <div className="error-box">
        <div className="error-details"></div>
      </div>
    );

    setError(errorMessage);
  };

  useEffect(() => {
    utils.obliterateEverything();
  }, []);

  return (
    <div className="form-container">
      <LoginForm onLogin={handleLogin} formError={formError} />
    </div>
  );
};

export default Login;
