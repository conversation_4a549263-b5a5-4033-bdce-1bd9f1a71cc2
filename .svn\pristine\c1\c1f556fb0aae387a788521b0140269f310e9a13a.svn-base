import { useEffect, useRef, useState } from 'react';

const useScrollListener = () => {
    const [position, setPosition] = useState<number>(0);
    const scrollContainerRef = useRef<HTMLDivElement | null>(null);

    const handleScroll = () => {
        if (scrollContainerRef.current) {
            setPosition(scrollContainerRef.current.scrollTop);
        }
    };

    useEffect(() => {
        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', handleScroll);
        }

        return () => {
            if (scrollContainer) {
                scrollContainer.removeEventListener('scroll', handleScroll);
            }
        };
    }, []);
    return { position, listener: scrollContainerRef };
};

export default useScrollListener;
