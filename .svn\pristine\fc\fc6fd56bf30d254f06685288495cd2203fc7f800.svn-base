/* src/pages/ActivityTimeline.module.css */

.container {
  min-width:  20%;
  margin: 0 auto;
  border: 0.5px solid #dfdfdf;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.header {
  background-color: #f8f8f8;
  padding: 10px;
  text-align: center;
  border-radius: 5px;
}

.mainContent {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.activityColumn p {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}
.participantContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.chatPhoto {
  width: 207px;
  height: 230px;
  border-radius: 10px;
}
