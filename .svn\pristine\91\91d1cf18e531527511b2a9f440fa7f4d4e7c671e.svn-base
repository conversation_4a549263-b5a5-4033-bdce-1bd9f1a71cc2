import { useEffect, useState } from "react";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import { useSearchParams } from "react-router-dom";
import Section4Mobile from "./Section4Mobile";
import styles from "../../../styles/job-shifts.module.css";
import Calender from "../../../../../assets/images/Icons/Icon (1).png";
import Checklist from "../../../../../assets/images/Icons/checklist.png";
import Clock from "../../../../../assets/images/Icons/Vector.png";
import shiftmobile from "../../../../../assets/images/Icons/shift-mobile.png";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import Dollar from "../../../../../assets/images/Icons/Dollar.png";
import { GoBack, Next, RadioButton } from "../Buttons";
import { Divider } from "primereact/divider";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

function PricingPaymentsStep2() {
  const { isMobile } = useIsMobile();
  return isMobile ? <PricingPaymentsStep2Mobile /> : <PricingPaymentsStep2Web />;
}

export default PricingPaymentsStep2;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();

  const [isNegotiable, setIsNegotiable] = useState(payload.isPriceNegotiable === null ? -1 : payload.isPriceNegotiable ? 0 : 1);
  const [paymentType, setPaymentType] = useState(payload.helperPaymentMethod === null ? -1 : payload.helperPaymentMethod - 1);
  const [specialInstruction, setSpecialInstruction] = useState(payload.specialInstructions ?? "");

  const [searchParams, setSearchParams] = useSearchParams();
  const { isMobile } = useIsMobile();

  const schedules = payload.weeklySchedule?.["weeklyScheduleEntries"];

  const [metadata, setMetadata] = useState<{
    days: number;
    shifts: number;
    hours: number;
    cost: number;
  }>({
    days: 0,
    shifts: 0,
    cost: 0,
    hours: 0,
  });

  useEffect(() => {
    function getTimeDifferenceInHourMinuteFormat(start: string, end: string): number {
      const [startHours, startMinutes] = start.split(":").map(Number);
      const [endHours, endMinutes] = end.split(":").map(Number);

      const startTotalMinutes = startHours * 60 + startMinutes;
      const endTotalMinutes = endHours * 60 + endMinutes;

      let differenceInMinutes = endTotalMinutes - startTotalMinutes;
      if (differenceInMinutes < 0) {
        differenceInMinutes += 24 * 60;
      }

      const hours = Math.floor(differenceInMinutes / 60);
      const minutes = differenceInMinutes % 60;

      return parseFloat((hours + minutes / 60).toFixed(2));
    }

    let days = schedules?.length;
    let shiftsVar = 0;
    let cost = 0;
    let hours = 0;
    schedules?.forEach((val) => {
      val.shifts.forEach((shift) => {
        const temp = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
        shiftsVar += 1;
        cost += shift.price;
        hours += temp;
      });
    });
    setMetadata({
      days: days,
      shifts: shiftsVar,
      cost: cost,
      hours: hours,
    });
  }, [schedules]);

  const weekMap = new Map([
    [0, "Sun"],
    [1, "Mon"],
    [2, "Tue"],
    [3, "Wed"],
    [4, "Thu"],
    [5, "Fri"],
    [6, "Sat"],
  ]);

  return {
    payload,
    next,
    prev,
    setpayload,
    isNegotiable,
    setIsNegotiable,
    paymentType,
    setPaymentType,
    specialInstruction,
    setSpecialInstruction,
    schedules,
    searchParams,
    setSearchParams,
    isMobile,
    metadata,
    setMetadata,
    weekMap,
  };
};

const PricingPaymentsStep2Web = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    isNegotiable,
    setIsNegotiable,
    paymentType,
    setPaymentType,
    specialInstruction,
    setSpecialInstruction,
    searchParams,
    setSearchParams,
    isMobile,
    metadata,
    setMetadata,
  } = useJobTypeHook();
  return (
    <div
      className="w-full h-full flex flex-column align-items-center select-none relative "
      style={
        {
          // width: "80%"
        }
      }
    >
      <div
        className="h-full flex-grow-1 flex align-items-center flex-column mx-auto overflow-auto"
        style={{
          width: "75%",
        }}
      >
        <div
          className="flex-grow-1 flex flex-column py-5 mx-auto"
          style={{
            width: "75%",
          }}
        >
          <div
            className="flex flex-column px-5 py-3"
            style={{
              maxWidth: "545px",
              border: "1px solid #DFDFDF",
              borderRadius: "20px",
            }}
          >
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "30px",
                color: "#585858",
              }}
            >
              Weekly Summary
            </h1>
            <div className="flex gap-2 mt-3 flex-wrap">
              {[
                {
                  label: "Days",
                  value: `${metadata.days} Days`,
                  icon: <img src={Calender} alt="Calender" height="13.5px" width="12px" />,
                  backgroundColor: "#2F9ACD",
                },
                {
                  label: "Shifts",
                  value: `${metadata.shifts} shifts`,
                  icon: <img src={Checklist} alt="Checklist" height="15.2px" width="14.09px" />,
                  backgroundColor: "#179D52",
                },
                {
                  label: "Hours",
                  value: `${metadata.hours} hours`,
                  icon: <img src={Clock} alt="Clock" height="14.4px" width="14.4px" />,
                  backgroundColor: "#8577DB",
                },
                {
                  label: "Pay",
                  value: `${metadata.cost} per week`,
                  icon: <img src={Dollar} alt="Dollar" height="15px" width="8px" />,
                  backgroundColor: "#77DBC9",
                },
              ].map((value, index) => (
                <div key={index} className="flex flex-column gap-1">
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "600",
                      fontSize: "18px",
                      color: "#585858",
                    }}
                  >
                    {value.label}
                  </h1>
                  <div
                    className="flex gap-2 justify-content-center align-items-center"
                    style={{
                      backgroundColor: value.backgroundColor,
                      border: "1px solid #DFDFDF",
                      padding: "10px",
                      borderRadius: "10px",
                    }}
                  >
                    {value.icon}
                    <p
                      className="m-0 p-0"
                      style={{
                        color: "#ffffff",
                        fontWeight: "600",
                        fontSize: "14px",
                      }}
                    >
                      {value.value}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex flex-column mt-5">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "30px",
                color: "#585858",
              }}
            >
              Job Payment
            </h1>
            <div className="flex flex-column md:flex-row align-items-center mt-1 w-9">
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#585858",
                  minWidth: "60%",
                }}
              >
                Is this price negotiable?
              </p>
              <div className="flex gap-3">
                {["Yes", "No"].map((value, index) => (
                  <div className="flex align-items-center gap-2 cursor-pointer" key={index} onClick={() => setIsNegotiable(index)}>
                    <RadioButton selected={index === isNegotiable} />
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "500",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {value}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <Divider className="my-5" />
            <div className="flex flex-column md:flex-row align-items-center mt-1 w-9">
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#585858",
                  minWidth: "60%",
                }}
              >
                How will you pay the helper for this job?
              </p>
              <div className="flex gap-3">
                {["Cash payment", "Bank transfer"].map((value, index) => (
                  <div key={index} className="flex flex-column">
                    <div className="flex align-items-center gap-2 cursor-pointer" onClick={() => setPaymentType(index)}>
                      <RadioButton selected={index === paymentType} />
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "500",
                          fontSize: "16px",
                          color: "#585858",
                          textWrap: "nowrap",
                        }}
                      >
                        {value}
                      </p>
                    </div>

                    {index === 1 && paymentType === 1 && (
                      <p
                        className="m-0 p-0 mt-1"
                        style={{
                          fontWeight: 400,
                          fontSize: "14px",
                          color: "#585858",
                          maxWidth: "317px",
                          width: "max-content",
                        }}
                      >
                        When you award this job you will be provided with the helper’s bank details.
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
            <Divider className="mt-5 mb-2" />
            <div className="flex flex-column">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "30px",
                  color: "#585858",
                }}
              >
                Job Details
              </h1>
              <p
                className="m-0 p-0 my-2"
                style={{
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Describe the job
              </p>

              <textarea
                className={styles.textAreaInput}
                placeholder={`What does your ${
                  payload.jobType === 64 || payload.jobType === 128 ? "tutor" : ""
                } need to know about this job and working with your family?`}
                name="instruction"
                id="instruction"
                value={specialInstruction}
                style={{
                  border: specialInstruction.length > 0 ? "2px solid #179D52" : "1px solid #585858",
                }}
                onChange={(e) => {
                  setSpecialInstruction(e.target.value);
                }}
              ></textarea>
            </div>
          </div>
        </div>
      </div>
      <Divider className="mb-3" />
      <div
        className="flex justify-content-between py-3  sticky bottom-0 bg-white"
        style={{
          width: "80%",
        }}
      >
        {/* <GoBack
            onClick={(e) => {
              e.preventDefault();
              prevClicked({
                ...currentPayload,
                isPriceNegotiable: isNegotiable === 0,
                helperPaymentMethod: paymentType + 1,
                paymentType: 2,
                specialInstructions: specialInstruction,
              });
            }}
          />
          <Next
            disabled={
              isNegotiable === -1 ||
              paymentType === -1 ||
              specialInstruction.length < 1
            }
            onClick={(e) => {
              e.preventDefault();
              nextClicked({
                ...currentPayload,
                isPriceNegotiable: isNegotiable === 0,
                helperPaymentMethod: paymentType + 1,
                paymentType: 2,
                specialInstructions: specialInstruction,
              });
            }}
          /> */}
        <GoBack
          onClick={() => {
            setpayload({
              ...payload,
              isPriceNegotiable: isNegotiable === 0,
              helperPaymentMethod: paymentType + 1,
              paymentType: 2,
              specialInstructions: specialInstruction,
            });

            prev("pricing-payments-step1");
          }}
        />
        <Next
          disabled={isNegotiable === -1 || paymentType === -1 || specialInstruction.length < 1}
          onClick={() => {
            setpayload({
              ...payload,
              isPriceNegotiable: isNegotiable === 0,
              helperPaymentMethod: paymentType + 1,
              paymentType: 2,
              specialInstructions: specialInstruction,
            });
            if (payload.managedBy === 20) {
              next("candidate-matching");
            } else {
              next("candidate-selection");
            }
          }}
        />
      </div>
    </div>
  );
};
const PricingPaymentsStep2Mobile = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    isNegotiable,
    setIsNegotiable,
    paymentType,
    setPaymentType,
    specialInstruction,
    setSpecialInstruction,
    schedules,
    weekMap,
    searchParams,
    setSearchParams,
    isMobile,
    metadata,
    setMetadata,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        position: "relative",
      }}
    >
      <div className={styles.weeklySummaryMobile}>
        <div className="h-full flex-grow-1 flex flex-column w-full">
          <div className="flex-grow-1 flex flex-column ">
            <div className="flex flex-column px-5 py-3">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                Weekly Summary
              </h1>
              {/* Replace the existing summary cards with the table */}
              {(() => {
                // Flatten all shifts from all schedules into a single array
                const allShifts =
                  schedules?.flatMap((schedule, scheduleIndex) =>
                    schedule.shifts.map((shift, shiftIndex) => ({
                      schedule,
                      shift,
                      scheduleIndex,
                      shiftIndex,
                      rate: shift.hourlyPrice !== null ? shift.hourlyPrice : shiftIndex === 0 ? 30 : 30,
                    }))
                  ) || [];

                // Function to calculate time difference
                const getTimeDifferenceInHourMinuteFormat = (start, end) => {
                  const [startHours, startMinutes] = start.split(":").map(Number);
                  const [endHours, endMinutes] = end.split(":").map(Number);
                  const startTotalMinutes = startHours * 60 + startMinutes;
                  const endTotalMinutes = endHours * 60 + endMinutes;
                  let differenceInMinutes = endTotalMinutes - startTotalMinutes;
                  if (differenceInMinutes < 0) {
                    differenceInMinutes += 24 * 60;
                  }
                  const hours = Math.floor(differenceInMinutes / 60);
                  const minutes = differenceInMinutes % 60;
                  return parseFloat((hours + minutes / 60).toFixed(2));
                };

                // Function to convert time to 12-hour format
                const convertTo12HourFormat = (time) => {
                  const [hourStr, minuteStr] = time.split(":");
                  const hour = parseInt(hourStr, 10);
                  const minute = parseInt(minuteStr, 10);
                  const period = hour >= 12 ? "PM" : "AM";
                  const hour12 = hour % 12 || 12;
                  return `${hour12}:${minute.toString().padStart(2, "0")} ${period}`;
                };

                // Calculate grand total hours and cost across all shifts
                const grandTotalHours = allShifts.reduce((sum, { shift }) => {
                  return sum + getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                }, 0);

                const grandTotalCost = allShifts.reduce((sum, { shift, rate }) => {
                  const hours = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                  return sum + hours * Number(rate);
                }, 0);

                return allShifts.length > 0 ? (
                  <div style={{ marginTop: "16px", overflowX: "auto" }}>
                    <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "12px" }}>
                      <thead>
                        <tr>
                          <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                          <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                          <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Hours</th>
                          <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Rate</th>
                          <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {allShifts.map(({ schedule, shift, scheduleIndex, shiftIndex, rate }, index) => {
                          const hours = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
                          return (
                            <tr key={`${scheduleIndex}-${shiftIndex}`}>
                              <td style={{ padding: "4px", color: "#585858", fontWeight: "700" }}>{weekMap.get(schedule.dayOfWeek)}</td>
                              <td style={{ padding: "4px", color: "#585858" }}>
                                {convertTo12HourFormat(shift.jobStartTime)} to {convertTo12HourFormat(shift.jobEndTime)}
                              </td>
                              <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>{hours.toFixed(2)}</td>
                              <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>
                                ${(typeof rate === "number" ? rate : parseFloat(rate)).toFixed(2)}
                              </td>
                              <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${(hours * Number(rate)).toFixed(2)}</td>
                            </tr>
                          );
                        })}
                        {/* Grand Total Row for All Shifts */}
                        <tr style={{ fontWeight: "bold", borderTop: "1px solid #ddd" }}>
                          <td style={{ padding: "4px", color: "#585858" }} colSpan={2}></td>
                          <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>{grandTotalHours.toFixed(2)}</td>
                          <td style={{ padding: "4px", color: "#585858" }}></td>
                          <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${grandTotalCost.toFixed(2)}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                ) : null;
              })()}
            </div>

            <div className="flex flex-column  px-5 ">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                Job Payment
              </h1>
              <div className="flex flex-column md:flex-row mt-1 md:align-items-center">
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "600",
                    fontSize: "16px",
                    color: "#585858",
                    minWidth: "30%",
                  }}
                >
                  Is this price negotiable?
                </p>
                <div className="flex gap-3 mt-2">
                  {["Yes", "No"].map((value, index) => (
                    <button
                      key={index}
                      className="flex align-items-center justify-content-center cursor-pointer"
                      style={{
                        padding: "12px 30px",
                        border: `2px solid ${isNegotiable === index ? "#179d52" : "#DFDFDF"}`,
                        borderRadius: "20px",
                        backgroundColor: isNegotiable === index ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                        color: isNegotiable === index ? "#179D52" : "#585858",
                        fontSize: "16px",
                        fontWeight: isNegotiable === index ? "700" : "500",
                        cursor: "pointer",
                      }}
                      onClick={() => setIsNegotiable(index)}
                    >
                      {value}
                    </button>
                  ))}
                </div>
              </div>
              <Divider className="my-5" />
              {isNegotiable === 0 || isNegotiable === 1 ? (
                <div className="flex flex-column lg:flex-row lg:align-items-center gap-3">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "600",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    How will you pay the helper for this job?
                  </p>
                  <div className="flex flex-column md:flex-row gap-2 mt-2">
                    {["Cash payment", "Bank transfer"].map((value, index) => (
                      <button
                        key={index}
                        className="flex align-items-center justify-content-center cursor-pointer"
                        style={{
                          padding: "12px 30px",
                          border: `2px solid ${paymentType === index ? "#179d52" : "#DFDFDF"}`,
                          borderRadius: "20px",
                          backgroundColor: paymentType === index ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                          color: paymentType === index ? "#179D52" : "#585858",
                          fontSize: "16px",
                          fontWeight: paymentType === index ? "700" : "500",
                          cursor: "pointer",
                          whiteSpace: "nowrap",
                        }}
                        onClick={() => setPaymentType(index)}
                      >
                        {value}
                      </button>
                    ))}
                  </div>
                  {paymentType === 1 && (
                    <p
                      className="m-0 p-0 mt-2"
                      style={{
                        fontWeight: 400,
                        fontSize: "14px",
                        color: "#585858",
                        maxWidth: "317px",
                      }}
                    >
                      When you award this job you will be provided with the helper's bank details.
                    </p>
                  )}
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      <BackButtonPortal id="back-button-portal">
        <div
          onClick={(e) => {
            e.preventDefault();
            setpayload({
              ...payload,
              isPriceNegotiable: isNegotiable === 0,
              helperPaymentMethod: paymentType + 1,
              paymentType: 2,
              specialInstructions: specialInstruction,
            });
            prev("pricing-payments-step1");
          }}
        >
          <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
        </div>
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        isDisabled={isNegotiable === -1 || paymentType === -1}
        onClick={() => {
          setpayload({
            ...payload,
            isPriceNegotiable: isNegotiable === 0,
            helperPaymentMethod: paymentType + 1,
            paymentType: 2,
            specialInstructions: specialInstruction,
          });
          next("section4-mobile");
        }}
      />
    </div>
  );
};
