import { Divider } from 'primereact/divider';
import { <PERSON><PERSON><PERSON>age<PERSON><PERSON>ck, LuSmilePlus } from 'react-icons/lu';
import { HiOutlineBookOpen } from 'react-icons/hi2';
import { FaCheck } from 'react-icons/fa6';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import { MdOutlineKeyboardArrowLeft } from 'react-icons/md';
import AON from '../../../assets/images/aon.png';
import style from '../../Parent/styles/family-membership.module.css';
import { GoLock } from 'react-icons/go';
import { loadStripe } from '@stripe/stripe-js';
import environment from '../../../helper/environment';
import {
    CardCvcElement,
    CardElement,
    CardExpiryElement,
    CardNumberElement,
    Elements,
    useElements,
    useStripe,
} from '@stripe/react-stripe-js';
import useLoader from '../../../hooks/LoaderHook';
import Service from '../../../services/services';
import c from '../../../helper/juggleStreetConstants';
import { refreshAccount } from '../../../store/tunks/sessionInfoTunk';
import Smiley from '../../../assets/images/Icons/my_child.png';
import Book from '../../../assets/images/Icons/book.png';
import Odd from '../../../assets/images/Icons/odd_job.png';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { CgDanger } from 'react-icons/cg';
import {
    updateChatWindowState,
    updateProfileActivationEnabled,
} from '../../../store/slices/applicationSlice';

function getPercentOfAmount(amount: number, percent: number) {
    return (amount * percent) / 100 + amount;
}

function CustomInput<T>({
    label,
    onChange,
    mask,
    maskWith,
    showAstrict = false,
}: {
    label: string;
    onChange: (value: T) => void;
    mask?: string;
    maskWith?: string;
    showAstrict?: boolean;
}) {
    const [inputValue, setInputValue] = useState<string>('');

    function applyMask(value: string, mask: string): string {
        let maskedValue = '';
        let maskIndex = 0;

        for (let i = 0; i < value.length; i++) {
            if (maskIndex >= mask.length) {
                break;
            }

            if (mask[maskIndex] === maskWith) {
                maskedValue += maskWith;
                maskIndex++;
            }

            if (/\d/.test(mask[maskIndex])) {
                maskedValue += value[i];
                maskIndex++;
            }
        }

        return maskedValue;
    }

    function handleChange(e: ChangeEvent<HTMLInputElement>) {
        let newValue = e.target.value;
        if (newValue.trim() === '') {
            setInputValue('');
            onChange('' as unknown as T);
            return;
        }

        if (mask) {
            newValue = applyMask(newValue.replace(/\D/g, ''), mask);
        } else {
            newValue = newValue.replace(/\d/g, '');
        }

        setInputValue(newValue);
        onChange(newValue as unknown as T);
    }

    return (
        <div
            className='flex relative'
            style={{
                borderBottom: '1px solid #F1F1F1',
                position: 'relative',
            }}
        >
            <input
                className='flex-grow-1 border-none px-3 py-2 w-full'
                style={{
                    color: '#787777',
                    fontSize: '14px',
                }}
                type='text'
                name={label.replace(/ /g, '').toLowerCase()}
                id={label.replace(/ /g, '').toLowerCase()}
                value={inputValue}
                onChange={handleChange}
            />
            {!inputValue && (
                <label
                    className='absolute mx-3'
                    style={{
                        color: '#787777',
                        fontSize: '14px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        pointerEvents: 'none',
                    }}
                    htmlFor={label.replace(/ /g, '').toLowerCase()}
                >
                    {label}{' '}
                    {showAstrict && (
                        <span
                            style={{
                                color: '#FF6359',
                            }}
                        >
                            *
                        </span>
                    )}
                </label>
            )}
        </div>
    );
}

function PricingView({
    plan,
    setPlan,
    onSubscribeClicked,
}: {
    plan: number;
    setPlan: (plan: number) => void;
    onSubscribeClicked: () => void;
}) {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const dispatch = useDispatch<AppDispatch>();
    return (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            <div className='px-4 py-3 '>
                <h1
                    className='m-0 p-0 mt-2'
                    style={{
                        color: '#585858',
                        fontWeight: '600',
                        fontSize: '16px',
                    }}
                >
                    Pricing
                </h1>
            </div>
            <Divider />
            <div className='py-2 flex flex-column align-items-center'>
                <h1
                    className='m-0 p-0'
                    style={{
                        color: '#179D52',
                        fontWeight: '800',
                        fontSize: '22px',
                    }}
                >
                    Juggle St Pro
                </h1>
            </div>
            <div
                className='flex flex-column w-full w-9 align-self-center my-4 gap-1'
                style={{
                    fontWeight: '300',
                    fontSize: '14px',
                    color: '#585858',
                }}
            >
                <div className='flex align-items-center' style={{ marginTop: '8px' }}>
                    <div className='flex-grow-1'>
                        <p className='m-0 p-0' style={{ fontSize: '14px', fontWeight: '300' }}>
                            One-off Jobs
                        </p>
                    </div>
                    <FaCheck color='#179D52' />
                </div>
                <div className='flex align-items-center' style={{ marginTop: '8px' }}>
                    <div className='flex-grow-1'>
                        <p className='m-0 p-0' style={{ fontSize: '14px', fontWeight: '300' }}>
                            Recurring Jobs
                        </p>
                    </div>
                    <FaCheck color='#179D52' />
                </div>
                <div className='flex align-items-center' style={{ marginTop: '8px' }}>
                    <div className='flex-grow-1'>
                        <p className='m-0 p-0' style={{ fontSize: '14px', fontWeight: '300' }}>
                            Outside School Hours
                        </p>
                    </div>
                    <FaCheck color='#179D52' />
                </div>
                <div className='flex align-items-center' style={{ marginTop: '8px' }}>
                    <div className='flex-grow-1'>
                        <p className='m-0 p-0' style={{ fontSize: '14px', fontWeight: '300' }}>
                            In-app Chat with helpers invited to jobs
                        </p>
                    </div>
                    <FaCheck color='#179D52' />
                </div>
            </div>
            <Divider />
            <div className='my-4 flex flex-column align-items-center'>
                {sessionInfo.data?.["client"]?.["clientCategory"] !== 3 ? (
                    <h1
                        className='m-0 p-0'
                        style={{
                            color: '#585858',
                            fontWeight: '600',
                            fontSize: '20px',
                        }}
                    >
                        Select Your Plan
                    </h1>
                ) : ("")}
                <div className='shadow-4 flex gap-2 my-2 px-2 py-1 border-round-xl'>
                    <button
                        className='m-0 border-none cursor-pointer'
                        style={{
                            backgroundColor: plan === 0 ? '#FFA500' : 'transparent',
                            borderRadius: '10px',
                            padding: '6px 21px',
                            fontWeight: plan === 0 ? '700' : '500',
                            fontSize: '14px',
                            color: plan === 0 ? 'white' : '#585858',
                            transition: 'all 0.2s ease-in-out',
                        }}
                        onClick={() => setPlan(0)}
                    >
                        Pay{' '}
                        {sessionInfo.data &&
                            sessionInfo.data['client']['clientCategory'] !==
                            c.clientCategory.BUSINESS_MULTI_SITE
                            ? 'Annually'
                            : 'Quarterly '}
                    </button>
                    {sessionInfo.data &&
                        sessionInfo.data['client']['clientCategory'] !==
                        c.clientCategory.BUSINESS_MULTI_SITE && (
                            <button
                                className='m-0 border-none cursor-pointer'
                                style={{
                                    backgroundColor: plan === 1 ? '#FFA500' : 'transparent',
                                    borderRadius: '10px',
                                    padding: '6px 21px',
                                    fontWeight: plan === 1 ? '700' : '500',
                                    fontSize: '14px',
                                    color: plan === 1 ? 'white' : '#585858',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                                onClick={() => setPlan(1)}
                            >
                                Pay Quarterly
                            </button>
                        )}
                </div>
            </div>
            <Divider />
            <div className='my-4 flex justify-content-center align-items-center gap-3'>
                <div
                    className='m-0 p-0 flex gap-2 align-items-baseline'
                    style={{
                        color: '#585858',
                        fontWeight: '600',
                        fontSize: '8px',
                    }}
                >
                    <div className='m-0 p-0 flex align-items-baseline'>
                        <h1 className='m-0 p-0'>$</h1>
                        <h1
                            className='m-0 p-0'
                            style={{
                                fontSize: '40px',
                            }}
                        >
                            {plan === 0
                                ? sessionInfo.data &&
                                    sessionInfo.data['client']?.['clientCategory'] !== c.clientCategory.BUSINESS_MULTI_SITE
                                    ? (
                                        <>
                                            1500 <span style={{ fontSize: "16px" }}>plus GST</span>
                                        </>
                                    )
                                    : (
                                        <>
                                            750 <span style={{ fontSize: "16px" }}>plus GST</span>
                                        </>
                                    )
                                : (
                                    <>
                                        500 <span style={{ fontSize: "16px" }}>plus GST</span>
                                    </>
                                )
                            }
                        </h1>
                    </div>
                    <h1 className='m-0 p-0'>
                        {plan === 0 &&
                            sessionInfo.data &&
                            sessionInfo.data['client']['clientCategory'] !==
                            c.clientCategory.BUSINESS_MULTI_SITE
                            ? 'Per Annum'
                            : ' '}
                        {plan === 1 && 'Per  Quarter'}
                    </h1>
                </div>

                {!sessionInfo.loading &&
                    sessionInfo.data['paymentInfo']['paymentType'] === c.userPaymentType.FREE && (
                        <button
                            className='m-0 border-none border-round-md cursor-pointer'
                            style={{
                                backgroundColor: '#FFA500',
                                padding: '5px 20px',
                                fontWeight: '700',
                                color: 'white',
                            }}
                            onClick={() => {
                                if (sessionInfo.loading) return;
                                if (sessionInfo.data['profileCompleteness'] < 100) {
                                    showConfirmationPopup(
                                        'Action Required',
                                        'Please Complete Your Profile Before Subscribing',
                                        'Activate',
                                        <CgDanger />,
                                        () => {
                                            dispatch(updateProfileActivationEnabled(true));
                                        }
                                    );
                                    return;
                                }
                                onSubscribeClicked();
                            }}
                        >
                            Subscribe
                        </button>
                    )}
            </div>
            <Divider />
            <div className='my-3 flex justify-content-center align-items-baseline gap-1'>
                <p
                    className='m-0 p-0'
                    style={{
                        color: '#585858',
                        fontWeight: '600',
                        fontSize: '14px',
                    }}
                >
                    Your Account Balance:
                </p>
                <p
                    className='m-0 p-0'
                    style={{
                        color: '#585858',
                        fontWeight: '600',
                        fontSize: '20px',
                    }}
                >
                    ${!sessionInfo.loading ? sessionInfo.data['walletBalanceDecimal'] : 0}
                </p>
            </div>
        </>
    );
}

function SubscriptionView({ plan, goBackClicked }: { plan: number; goBackClicked: () => void }) {
    const [nameOnCard, setNameOnCard] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    const [cardNumberDetails, setCardNumberDetails] = useState<boolean>(false);
    const [cardExpiryDetails, setCardExpiryDetails] = useState<boolean>(false);
    const [cardCvcDetails, setCardCvcDetails] = useState<boolean>(false);
    const [useExistingCard, setUseExistingCard] = useState<boolean>(true);
    const [valid, setValid] = useState<boolean>(false);
    const { enableLoader, disableLoader } = useLoader();

    const stripe = useStripe();
    const elements = useElements();

    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const existingCard = sessionInfo.data['paymentInfo']?.cardLast4Digits
    ? `XXXX XXXX XXXX ${sessionInfo.data['paymentInfo'].cardLast4Digits}`
    : null;
    function handleNameChange(value: string) {
        setNameOnCard(value.trim());
    }

    async function onPayClicked(e: React.MouseEvent<HTMLButtonElement>) {
        e.preventDefault();
        enableLoader();
        if (useExistingCard) {
            // Use existing card (no token needed)
            try {
               await Service.payments({
                    actionType: c.paymentActionType.ACTIVATE_MEMBERSHIP,
                    deviceType: c.deviceType.DESKTOP,
                    paymentType:
                    plan === 0
                         ? sessionInfo.data &&
                                sessionInfo.data['client']['clientCategory'] !==
                                c.clientCategory.BUSINESS_MULTI_SITE
                                ? c.userPaymentType.ANNUAL_BUSINESS_TIER1_SUBSCRIPTION_1500
                                : c.userPaymentType.QUARTERLY_BUSINESS_TIER2_SUBSCRIPTION_750
                            : c.userPaymentType.QUARTERLY_BUSINESS_TIER1_SUBSCRIPTION_500,
                // No tokenId; backend uses stored card
            });
                await dispatch(refreshAccount());
                goBackClicked();
            } catch (e) {
                setError(e.message || 'An error occurred with the existing card');
            } finally {
                disableLoader();
            }
        } else {
        if (!stripe || !elements) {
            alert('Stripe has not loaded');
            return;
        }

        const cardElement = elements.getElement(CardNumberElement);
        if (!cardElement) {
            setError('Card element not found');
            return;
        }

        const { token, error } = await stripe.createToken(cardElement, {
            name: nameOnCard,
        });

        if (error) {
            setError(error.message || 'An error occurred');
        } else if (token) {
            try {
                await Service.payments({
                    actionType: c.paymentActionType.ACTIVATE_MEMBERSHIP,
                    deviceType: c.deviceType.DESKTOP,
                    paymentType:
                        plan === 0
                            ? sessionInfo.data &&
                                sessionInfo.data['client']['clientCategory'] !==
                                c.clientCategory.BUSINESS_MULTI_SITE
                                ? c.userPaymentType.ANNUAL_BUSINESS_TIER1_SUBSCRIPTION_1500
                                : c.userPaymentType.QUARTERLY_BUSINESS_TIER2_SUBSCRIPTION_750
                            : c.userPaymentType.QUARTERLY_BUSINESS_TIER1_SUBSCRIPTION_500,
                    tokenId: token.id,
                });
                await dispatch(refreshAccount());
                goBackClicked();
            } catch (e) {
                setError(e || 'An error occurred');
            } finally {
                disableLoader();
            }
        }
    }
        disableLoader();
    }

   useEffect(() => {
          // Validation only applies to new card input
          setValid(
              useExistingCard || // Valid if using existing card
              (cardNumberDetails && cardExpiryDetails && cardCvcDetails && nameOnCard.trim() !== '')
          );
      }, [cardNumberDetails, cardExpiryDetails, cardCvcDetails, nameOnCard, useExistingCard]);

      useEffect(() => {
        if (existingCard) {
            setUseExistingCard(true); // Default to existing card if available
        } else {
            setUseExistingCard(false); // Default to new card input if no existing card
        }
    }, [existingCard]);
    return (
        <>
            <div className='px-3 py-2 mt-2 flex justify-content-center relative'>
                <div
                    className='absolute top-50 flex align-items-center cursor-pointer'
                    style={{
                        color: '#585858',
                        fontWeight: '500',
                        fontSize: '14px',
                        transform: 'translateY(-50%)',
                        left: '10px',
                    }}
                    onClick={() => goBackClicked()}
                >
                    <MdOutlineKeyboardArrowLeft fontSize={'15px'} />
                    Go back
                </div>
                <h1
                    className='m-0 p-0'
                    style={{
                        color: '#585858',
                        fontWeight: '600',
                        fontSize: '18px',
                    }}
                >
                    Complete Payment
                </h1>
            </div>
            <Divider />
            <div className='my-3 flex flex-column align-items-center lg:align-items-start  px-3 lg:flex-row lg:gap-3'>
                <div
                    className='flex flex-column'
                    style={{
                        border: '1px solid #F0F4F7',
                        borderRadius: '0 0 20px 20px',
                    }}
                >
                    <h1
                        className='m-0 p-0 px-3 py-2'
                        style={{
                            color: '#585858',
                            fontWeight: '700',
                            fontSize: '18px',
                        }}
                    >
                        Order summary
                    </h1>
                    <Divider />
                    <div className='my-3 px-3'>
                        <div className='flex flex-column w-full'>
                            <p
                                className='m-0 p-0'
                                style={{
                                    color: '#179D52',
                                    fontWeight: '800',
                                    fontSize: '14px',
                                }}
                            >
                                {sessionInfo.data &&
                                    sessionInfo.data['client']['clientCategory'] !==
                                    c.clientCategory.BUSINESS_MULTI_SITE
                                    ? `Juggle St Pro - ${plan === 0 ? '40' : '10'} Jobs`
                                    : 'Juggle Street Pro - 3 Job Posts'}
                            </p>
                            <div className='flex justify-content-between align-items-center'>
                                <p
                                    className='m-0 p-0'
                                    style={{
                                        color: '#585858',
                                        fontWeight: '400',
                                        fontSize: '14px',
                                    }}
                                >
                                    {sessionInfo.data &&
                                        sessionInfo.data['client']['clientCategory'] !==
                                        c.clientCategory.BUSINESS_MULTI_SITE
                                        ? `${plan === 0 ? 'Annual' : 'Quarterly'} Subscription`
                                        : 'Quarterly Subscription'}
                                </p>
                                <p
                                    className='m-0 p-0'
                                    style={{
                                        color: '#585858',
                                        fontWeight: '600',
                                        fontSize: '16px',
                                    }}
                                >
                                    $
                                    {plan === 0
                                        ? sessionInfo.data &&
                                            sessionInfo.data['client']['clientCategory'] !== c.clientCategory.BUSINESS_MULTI_SITE
                                            ? <>
                                                1,500
                                            </>
                                            : (
                                                <>
                                                    750                                        </>
                                            )
                                        : '500'}

                                </p>
                            </div>
                        </div>
                    </div>
                    <Divider />
                    <div className='my-3 px-3'>
                        <div className='flex justify-content-between align-items-center'>
                            <p
                                className='m-0 p-0'
                                style={{
                                    color: '#585858',
                                    fontWeight: '400',
                                    fontSize: '14px',
                                }}
                            >
                                Less Credit Balance
                            </p>
                            <p
                                className='m-0 p-0'
                                style={{
                                    color: '#585858',
                                    fontWeight: '600',
                                    fontSize: '16px',
                                }}
                            >
                                ${!sessionInfo.loading ? sessionInfo.data['walletBalanceDecimal'] : 0}
                            </p>
                        </div>
                    </div>
                    <Divider />
                    <div className='my-3 px-3'>
                        <div className='flex justify-content-between align-items-center gap-3'>
                            <h1
                                className='m-0 p-0'
                                style={{
                                    color: '#585858',
                                    fontWeight: '700',
                                    fontSize: '14px',
                                }}
                            >
                                Amount due today inc G.S.T
                            </h1>
                            <p
                                className='m-0 p-0'
                                style={{
                                    color: '#585858',
                                    fontWeight: '600',
                                    fontSize: '20px',
                                }}
                            >
                                {(() => {
                                    const baseAmount =
                                        plan === 0
                                            ? sessionInfo.data &&
                                                sessionInfo.data["client"]["clientCategory"] !== c.clientCategory.BUSINESS_MULTI_SITE
                                                ? 1500
                                                : 750
                                            : 500;
                                    const amount = getPercentOfAmount(baseAmount, 10) -
                                        (!sessionInfo.loading ? sessionInfo.data["walletBalanceDecimal"] : 0);
                                    return amount < 0 ? "$0.00" : `$${amount.toFixed(2)}`;
                                })()}
                            </p>
                        </div>
                    </div>
                    {
                        ((plan === 0 ? sessionInfo.data &&
                            sessionInfo.data["client"]["clientCategory"] !== c.clientCategory.BUSINESS_MULTI_SITE
                            ? 1500
                            : 750: 500) - (!sessionInfo.loading ? sessionInfo.data["walletBalanceDecimal"] : 0) !== 0 && (!sessionInfo.loading ? sessionInfo.data["walletBalanceDecimal"] : 0) !== 0 &&
                            <div className='flex justify-content-between align-items-baseline my-4'>
                                <p
                                    className='m-0 p-0 pt-0 px-4'
                                    style={{
                                        fontSize: '14px',
                                        fontWeight: '700',
                                        color: '#585858',
                                    }}
                                >
                                    Your Account Balance
                                </p>
                                <p
                                    className='m-0 p-0 px-4'
                                    style={{
                                        fontSize: '22px',
                                        fontWeight: '600',
                                        color: '#585858',
                                    }}
                                >
                                    {
                                        (() => {

                                            const amount = (plan === 0 ? sessionInfo.data &&
                                                sessionInfo.data["client"]["clientCategory"] !== c.clientCategory.BUSINESS_MULTI_SITE
                                                ? 1500
                                                : 750 : 500) - (!sessionInfo.loading ? sessionInfo.data["walletBalanceDecimal"] : 0)
                                            return `$${Math.abs(amount).toFixed(2)}`
                                        })()
                                    }
                                </p>
                            </div>)
                    }
                </div>
                <div className='flex flex-column'>
                    <div
                        className='my-4 lg:my-0 flex flex-column '
                        style={{
                            border: '1px solid #F0F4F7',
                            minWidth: '250px',
                        }}
                    >
                        <h1
                            className='m-0 p-0 px-3 py-2'
                            style={{
                                color: '#585858',
                                fontWeight: '700',
                                fontSize: '18px',
                            }}
                        >
                            Payment details
                        </h1>
                          {existingCard && (
                                                    <div className='px-3 py-2'>
                                                        <label className='flex align-items-center gap-2'>
                                                            <input
                                                                type="radio"
                                                                checked={useExistingCard}
                                                                onChange={() => setUseExistingCard(true)}
                                                            />
                                                            <span style={{ color: '#585858', fontSize: '14px' }}>
                                                                Use existing card: {existingCard}
                                                            </span>
                                                        </label>
                                                        <label className='flex align-items-center gap-2'>
                                                            <input
                                                                type="radio"
                                                                checked={!useExistingCard}
                                                                onChange={() => setUseExistingCard(false)}
                                                            />
                                                            <span style={{ color: '#585858', fontSize: '14px' }}>
                                                                Add new card
                                                            </span>
                                                        </label>
                                                    </div>
                                                )}
                         {!useExistingCard && (
                            <>
                        <div className='px-3 pt-2 pb-2'>
                            <CustomInput<string>
                                label='Name on Card'
                                onChange={handleNameChange}
                                showAstrict
                            />
                        </div>
                        <div className='pr-3 pt-2 pb-2 pl-5'>
                            <CardNumberElement
                                options={{
                                    disableLink: true,
                                    showIcon: true,
                                    style: {
                                        base: {
                                            fontSize: '14px',
                                            fontWeight: '400',
                                            color: '#787777',
                                            '::placeholder': {
                                                fontSize: '14px',
                                                fontWeight: '400',
                                                color: '#787777',
                                            },
                                        },
                                        invalid: {
                                            fontSize: '14px',
                                            fontWeight: '400',
                                            color: '#ff5252',
                                        },
                                        complete: {
                                            fontSize: '14px',
                                            fontWeight: '400',
                                            color: '#787777',
                                        },
                                    },
                                }}
                                onChange={(event) => {
                                    setCardNumberDetails(event.complete);
                                    if (event.error) {
                                        setError(event.error.message);
                                    } else {
                                        setError(null);
                                    }
                                }}
                            />
                            <div className='flex mt-3'>
                                <div className='flex flex-column mb-3 flex-grow-1'>
                                    <div
                                        className='flex w-min mb-3'
                                        style={{
                                            position: 'relative',
                                            textWrap: 'nowrap',
                                        }}
                                    >
                                        <p
                                            className='m-0 p-0'
                                            style={{
                                                fontSize: '12px',
                                                fontWeight: '700',
                                                color: '#585858',
                                            }}
                                        >
                                            Card expiration date
                                        </p>
                                        <p
                                            className='m-0 p-0'
                                            style={{
                                                position: 'absolute',
                                                top: '0px',
                                                right: '-10px',
                                                color: '#FF6359',
                                                fontSize: '12px',
                                            }}
                                        >
                                            *
                                        </p>
                                    </div>
                                    <CardExpiryElement
                                        options={{
                                            style: {
                                                base: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#787777',
                                                    '::placeholder': {
                                                        fontSize: '14px',
                                                        fontWeight: '400',
                                                        color: '#787777',
                                                    },
                                                },
                                                invalid: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#ff5252',
                                                },
                                                complete: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#787777',
                                                },
                                            },
                                        }}
                                        onChange={(event) => {
                                            setCardExpiryDetails(event.complete);
                                            if (event.error) {
                                                setError(event.error.message);
                                            } else {
                                                setError(null);
                                            }
                                        }}
                                    />
                                </div>
                                <div className='flex flex-column  mb-3 flex-grow-1'>
                                    <div
                                        className='flex w-min mb-3'
                                        style={{
                                            position: 'relative',
                                            textWrap: 'nowrap',
                                        }}
                                    >
                                        <p
                                            className='m-0 p-0'
                                            style={{
                                                fontSize: '12px',
                                                fontWeight: '700',
                                                color: '#585858',
                                            }}
                                        >
                                            CVC
                                        </p>
                                        <p
                                            className='m-0 p-0'
                                            style={{
                                                position: 'absolute',
                                                top: '0px',
                                                right: '-10px',
                                                color: '#FF6359',
                                                fontSize: '12px',
                                            }}
                                        >
                                            *
                                        </p>
                                    </div>
                                    <CardCvcElement
                                        options={{
                                            style: {
                                                base: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#787777',
                                                    '::placeholder': {
                                                        fontSize: '14px',
                                                        fontWeight: '400',
                                                        color: '#787777',
                                                    },
                                                },
                                                invalid: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#ff5252',
                                                },
                                                complete: {
                                                    fontSize: '14px',
                                                    fontWeight: '400',
                                                    color: '#787777',
                                                },
                                            },
                                        }}
                                        onChange={(event) => {
                                            setCardCvcDetails(event.complete);
                                            if (event.error) {
                                                setError(event.error.message);
                                            } else {
                                                setError(null);
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                            
                        </div>
                        </>
                        )}
                        <button
                            className='border-none mx-5 my-3 border-round-xl flex justify-content-center align-content-center  align-items-center gap-1'
                            style={{
                                color: 'white',
                                fontWeight: '700',
                                fontSize: '14px',
                                paddingBlock: '8px',
                                backgroundColor: (stripe && valid) || useExistingCard ? '#FFA500' : '#ccc', // Enable for existing card
                                cursor: (stripe && valid) || useExistingCard ? 'pointer' : 'not-allowed', // Enable cursor for existing card
                            }}
                            onClick={onPayClicked}
                            disabled={!stripe && !useExistingCard} // Enable button if existing card is selected
                        >
                            <GoLock />
                            {/* Pay {plan === 0 ? 'annual' : 'Quarterly'} fee */}
                            {existingCard ? 'Upgrade' : `Pay ${plan === 0 ? 'annual' : 'Quarterly'} fee`}
                        
                        </button>
                    </div>
                    <p
                        className='m-0 p-0 px-2 py-1 border-round-2xl lg:mt-3 lg:flex lg:justify-content-center lg:align-items-center lg:gap-1'
                        style={{
                            color: '#585858',
                            fontWeight: '400',
                            fontSize: '10px',
                            textAlign: 'center',
                            backgroundColor: '#F0F4F7',
                            border: '1px solid #1F9EAB',
                        }}
                    >
                        <GoLock
                            className='m-0 p-0'
                            style={{ verticalAlign: 'middle', fontSize: '12px' }}
                        />{' '}
                        All card information is fully encrypted, secure and protected. Learn more
                    </p>
                    {error && (
                        <p
                            className='m-0 p-0 text-center lg:mt-3 mt-2'
                            style={{ color: '#FF6359', fontWeight: '700', fontSize: '12px' }}
                        >
                            {error}
                        </p>
                    )}
                </div>
            </div>
        </>
    );
}

const stripePromise = loadStripe(environment.getStripePublishableKey(window.location.hostname));

function FamilyMembershipBusiness() {
    const [plan, setPlan] = useState<number>(0);
    const [subscribeEnabled, setSubscribeEnabled] = useState<boolean>(false);
    const dispatch = useDispatch<AppDispatch>();

    return (
        <div className={style.familyMembershipContainer}>
            <div className={style.gradientContainer} />
            <div className={style.gradientClipper} />
            <div
                className={`${style.fmMainContent} flex flex-column overflow-x-auto ${subscribeEnabled ? style.upgrade : ''
                    }`}
                style={{
                    border: '2px solid #F0F4F7',
                }}
            >
                {subscribeEnabled ? (
                    <Elements stripe={stripePromise}>
                        <SubscriptionView
                            plan={plan}
                            goBackClicked={() => setSubscribeEnabled(false)}
                        />
                    </Elements>
                ) : (
                    <PricingView
                        plan={plan}
                        setPlan={(val) => setPlan(val)}
                        onSubscribeClicked={() => setSubscribeEnabled(true)}
                    />
                )}
            </div>
            <div
                className='flex my-3 gap-1'
                style={{
                    fontSize: '6px',
                    zIndex: 1,
                }}
            >
                <h1
                    className='m-0 p-0'
                    style={{
                        fontWeight: '400',
                        fontSize: '14px',
                        color: '#585858',
                    }}
                >
                    Need Help?
                </h1>
                <h1
                    className='m-0 p-0 cursor-pointer'
                    style={{
                        fontWeight: '500',
                        fontSize: '14px',
                        color: '#FFA500',
                        textDecoration: 'underline',
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        dispatch(updateChatWindowState(true));
                    }}
                >
                    Message Our team
                </h1>
            </div>
        </div>
    );
}

export default FamilyMembershipBusiness;
