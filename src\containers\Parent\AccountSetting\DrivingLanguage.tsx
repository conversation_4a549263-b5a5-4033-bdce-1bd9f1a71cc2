import { useEffect, useState } from 'react';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from '../styles/my-family.module.css';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import useLoader from '../../../hooks/LoaderHook';
import useIsMobile from '../../../hooks/useIsMobile';
interface Language {
  canSelectCategory: boolean;
  childCategory: number;
  children: null;
  optionId: number;
  selected: boolean;
  text: string;
}
interface Licence {
  canSelectCategory: boolean;
  childCategory: number;
  children: null;
  optionId: number;
  selected: boolean;
  text: string;
}
const DrivingLanguage = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo) as { data: { spokenLanguages: Language[], transport: Licence[] } };
  const dispatch = useDispatch<AppDispatch>();
  const { disableLoader, enableLoader } = useLoader();
  const [languageRadioState, setLanguageRadioState] = useState(null);
  const [licenceRadioState, setlicenceRadioState] = useState(null);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [licence, setLicence] = useState<Licence[]>([]);
  const {isMobile}=useIsMobile()
  const [checkedState, setCheckedState] = useState<boolean[]>([]);
  const [checkedlicenceState, setlicenceCheckedState] = useState<boolean[]>([]);
  useEffect(() => {
    const spokenLanguages = sessionInfo?.data?.["provider"]["spokenLanguages"] || [];
    setLanguages(spokenLanguages);
    setCheckedState(spokenLanguages.map((lang) => lang.selected));
    if (spokenLanguages.some((lang) => lang.selected)) {
      setLanguageRadioState('yes');
    }
  }, [sessionInfo.data.spokenLanguages]);
  useEffect(() => {
    const transport = sessionInfo?.data?.["provider"]["transport"] || [];
    const updatedTransport = transport.map((trans) => ({
      ...trans,
      selected: trans.optionId === 26 ? trans.selected : trans.selected,
    }));
    setLicence(updatedTransport);
    setlicenceCheckedState(updatedTransport.map((trans) => trans.selected));
    if (updatedTransport.some((trans) => trans.selected)) {
      setlicenceRadioState('yes');
    }
  }, [sessionInfo.data.transport]);


  const handleCheckboxChange = (index: number) => {
    const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
    setCheckedState(updatedCheckedState);

    const updatedLanguages = languages.map((lang, idx) => ({
      ...lang,
      selected: updatedCheckedState[idx],
    }));
    setLanguages(updatedLanguages);
  };
  const handlelicenceCheckboxChange = (index: number) => {
    const updatedCheckedState = checkedlicenceState.map((item, idx) =>
      idx === index ? !item : item
    );
    setlicenceCheckedState(updatedCheckedState);
    const updatedLicence = licence.map((lang, idx) => ({
      ...lang,
      selected: updatedCheckedState[idx],
    }));
    setLicence(updatedLicence);
  };

  const handleSave = () => {
    enableLoader();
    const payload = {
      ...sessionInfo.data,
      provider: {
        spokenLanguages: languages,
        transport: licence,
      },
    };

    dispatch(updateSessionInfo({ payload })).finally(() => {
      disableLoader();
    });
  };

  const handleLanguageRadioChange = (value: string) => {
    setLanguageRadioState(value);
  };
  const handleLicenceRadioChange = (value: string) => {
    setlicenceRadioState(value);
  };
  return (
    <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "0" }} className={styles.utilcontainerhelper}>
      <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
        <header className={styles.utilheader}>
          <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Driving and Languages</h1>
        </header>
        <CustomButton
          label="Save"
          className={myfamilystyles.customButton}
          style={{ margin: '0', width: '150px' }}
          onClick={handleSave}
        />
      </div>
      <div>
        <h1
          className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
          style={{ fontSize: '16px', color: '#179d52' }}
        >
          Do you speak another language?
        </h1>
      </div>
      <div>
        <div className="flex justify-content-start items-center mt-3 gap-2">
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="language-radio"
              value="yes"
              checked={languageRadioState === 'yes'}
              onChange={() => handleLanguageRadioChange('yes')}
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="language-radio"
              value="no"
              checked={languageRadioState === 'no'}
              onChange={() => handleLanguageRadioChange('no')}
              className="cursor-pointer"
            />
            No
          </label>
        </div>
      </div>
      {languageRadioState === 'yes' && (
        <>
          <div>
          </div>
          <div className="flex flex-column justify-content-center">
            {languages.map((language, index) => (
              <label key={language.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                <input
                  type="checkbox"
                  className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                  checked={checkedState[index]}
                  onChange={() => handleCheckboxChange(index)}
                  style={{ fontSize: '18px' }}
                />
                <div className="flex flex-column items-center">
                  <div className="txt-clr" style={{ fontSize: '16px' }}>
                    {language.text}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </>
      )}
      <div>
        <h1
          className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
          style={{ fontSize: '16px', color: '#179d52' }}
        >
          Do you have driver Licence?
        </h1>
      </div>
      <div>
        <div className="flex justify-content-start items-center mt-3 gap-2">
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="licence-radio"
              value="yes"
              checked={licenceRadioState === 'yes'}
              onChange={() => handleLicenceRadioChange('yes')}
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="licence-radio"
              value="no"
              checked={licenceRadioState === 'no'}
              onChange={() => handleLicenceRadioChange('no')}
              className="cursor-pointer"
            />
            No
          </label>
        </div>
      </div>
      {licenceRadioState === 'yes' && (
        <>
          <div>
          </div>
          <div className="flex flex-column justify-content-center">
            {licence.map((licence, index) => (
              <label key={licence.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                <input
                  type="checkbox"
                  className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                  checked={checkedlicenceState[index]}
                  onChange={() => handlelicenceCheckboxChange(index)}
                  style={{ fontSize: '18px' }}
                />
                <div className="flex flex-column items-center">
                  <div className="txt-clr" style={{ fontSize: '16px' }}>
                    {licence.text}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
export default DrivingLanguage;
