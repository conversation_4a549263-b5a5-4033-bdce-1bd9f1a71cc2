import React, { useEffect, useState } from "react";
import { InputText } from "primereact/inputtext";
import "./join-now.css";
import "../../utils/util.css";
import { useJoinNowData } from "../../../model/JoinNowContext";
import environment from "../../../helper/environment";
import c from "../../../helper/juggleStreetConstants";
import useIsMobile from "../../../hooks/useIsMobile";
interface SignUpPageProps {
  onNext: () => void;
  onPrevious?: () => void;
  setCurrentPage: (page: number) => void;
}
export const BusinessDetails: React.FC<SignUpPageProps> = ({
  setCurrentPage,
}) => {
  const { joinNowData, dispatch } = useJoinNowData();
  const [isFormValid, setIsFormValid] = useState(false);
  const country = environment.getCountry(window.location.hostname);
  const { isMobile } = useIsMobile();
  const [formData, setFormData] = useState({
    tradingName: joinNowData.tradingName || "",
    businessName: joinNowData.businessName || "",
    businessNumber: joinNowData.businessNumber || "",
    firstName: joinNowData.firstName || "",
    lastName: joinNowData.lastName || "",
    gender: joinNowData.gender || "",
    businessTaxNumber: joinNowData.businessTaxNumber || "",
  });

  const [error, setError] = useState({
    tradingName: "",
    businessName: "",
    businessNumber: "",
    firstName: "",
    lastName: "",
    gender: "",
    businessTaxNumber: "",
  });
  useEffect(() => {
    checkFormValidity();
  }, [formData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (
      ["tradingName", "businessName", "firstName", "lastName"].includes(name)
    ) {
      if (/^[a-zA-Z\s]*$/.test(value) || value === "") {
        setFormData({ ...formData, [name]: value });
        dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
        setError({ ...error, [name]: "" });
      } else {
        // setError({ ...error, [name]: 'Only characters are allowed' });
      }
    } else if (name === "businessNumber") {
      const numericValue = value.replace(/\D/g, "").slice(-13);
      setFormData({ ...formData, [name]: numericValue });
      dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
      validateBusinessNumber(numericValue);
    } else if (name === "businessTaxNumber") {
      setFormData({ ...formData, [name]: value });
      dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
      setError({ ...error, [name]: "" });
    } else {
      setFormData({ ...formData, [name]: value });
      dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
      setError({ ...error, [name]: "" });
    }
  };
  const showGender = joinNowData.clientType === 2;
  const filteredGenders = showGender
    ? Object.keys(c.genders).filter(
        (key) =>
          key === "MALE" || key === "FEMALE" || key === "PREFER_NOT_TO_SAY"
      )
    : Object.keys(c.genders);

  const validateBusinessNumber = (value: string) => {
    if (value === "") {
      // setError({ ...error, businessNumber: 'Please enter a business number' });
    } else if (
      (country === "au" && value.length !== 11) ||
      (country === "nz" && value.length !== 13)
    ) {
      // setError({ ...error, businessNumber: `Please enter a valid ${country === 'au' ? '11' : '13'}-digit business number` });
    } else {
      setError({ ...error, businessNumber: "" });
    }
  };

  useEffect(() => {
    setFormData({
      firstName: joinNowData.firstName || "",
      lastName: joinNowData.lastName || "",
      gender: joinNowData.gender ?? "",
      tradingName: joinNowData.tradingName || "",
      businessName: joinNowData.businessName || "",
      businessNumber: joinNowData.businessNumber || "",
      businessTaxNumber: joinNowData.businessTaxNumber || "",
    });
    
  }, [joinNowData]);
  const checkFormValidity = () => {
    const isValid =
      formData.tradingName.trim() !== "" &&
      formData.businessName.trim() !== "" &&
      formData.businessNumber.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.firstName.trim() !== "" &&
      formData.gender !== "" &&
      (country !== "nz" || formData.businessTaxNumber.trim() !== "") &&
      Object.values(error).every((e) => e === "");
    setIsFormValid(isValid);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    let validationError = { ...error };
    let firstErrorField: string | null = null;

    // Validation logic
    if (!formData.tradingName.trim()) {
      validationError.tradingName = "Please enter your trading name";
      firstErrorField = firstErrorField || "tradingName";
    }
    if (!formData.businessName.trim()) {
      validationError.businessName = "Please enter your business name";
      firstErrorField = firstErrorField || "businessName";
    }
    if (!formData.businessNumber.trim()) {
      validationError.businessNumber = "Please enter a business number";
      firstErrorField = firstErrorField || "businessNumber";
    } else if (
      (country === "au" && formData.businessNumber.length !== 11) ||
      (country === "nz" && formData.businessNumber.length !== 13)
    ) {
      validationError.businessNumber = `Please enter a valid ${
        country === "au" ? "11" : "13"
      }-digit business number`;
      firstErrorField = firstErrorField || "businessNumber";
    }
    if (!formData.firstName.trim()) {
      validationError.firstName = "Please enter your first name";
      firstErrorField = firstErrorField || "firstName";
    }
    if (!formData.lastName.trim()) {
      validationError.lastName = "Please enter your last name";
      firstErrorField = firstErrorField || "lastName";
    }
    if (formData.gender < 0) {
      validationError.gender = "Please select your gender";
      firstErrorField = firstErrorField || "gender";
    }
    if (country === "nz") {
      if (!formData.businessTaxNumber.trim()) {
        validationError.businessTaxNumber = "Please enter your GST number";
        firstErrorField = firstErrorField || "businessTaxNumber";
      } else if (
        !/^\d+$/.test(formData.businessTaxNumber) ||
        formData.businessTaxNumber.length !== 9
      ) {
        validationError.businessTaxNumber =
          "Please enter a valid 9 digit GST Number.";
        firstErrorField = firstErrorField || "businessTaxNumber";
      }
    }
    setError(validationError);
    if (!firstErrorField) {
      const payload = {
        tradingName: formData.tradingName,
        businessName: formData.businessName,
        businessNumber: formData.businessNumber,
        firstName: formData.firstName,
        lastName: formData.lastName,
        gender: formData.gender,
        ...(country === "nz" && {
          businessTaxNumber: formData.businessTaxNumber,
        }),
      };

      dispatch({ type: "UPDATE_FORM", payload });
      setCurrentPage(3);
    }
  };

  const handleGenderChange = (value: string) => {
    setFormData({ ...formData, gender: value });
    dispatch({ type: "UPDATE_FORM", payload: { gender: value } });
    setError({ ...error, gender: "" });
  };

  const handlePrevious = (e) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  return (
    <form className="pl-4 pr-4" style={{ marginTop: "-40px" }}>
      <div className="flex flex-column gap-2">
        <p className="h-joinnow">Business Details</p>
        <div className="formgrid grid">
          <div className="col-12 md:col-6 para">
            <div className="input-container-business">
              <InputText
                id="tradingName"
                name="tradingName"
                value={formData.tradingName}
                onChange={handleChange}
                placeholder=" " // Placeholder is needed for the CSS to target :placeholder-shown
                className={`input-placeholder ${
                  error.tradingName
                    ? "border-red"
                    : formData.tradingName
                    ? "border-green"
                    : ""
                }`}
              />
              <label htmlFor="tradingName" className="label-name">
                Business name
              </label>
            </div>
            {/* {error.tradingName && <span style={{ color: 'red' }}>{error.tradingName}</span>} */}
          </div>
          <div className="col-12 md:col-6 para">
            {/* <label className="mb-3 font-semibold">Registered Business Name</label> */}
            <div className="input-container-business">
              <InputText
                id="businessName"
                name="businessName"
                value={formData.businessName}
                placeholder=""
                onChange={handleChange}
                // className={error.businessName ? 'border-red' : ''}
                className={`input-placeholder ${
                  error.businessName
                    ? "border-red"
                    : formData.businessName
                    ? "border-green"
                    : ""
                }`}
              />
              <label htmlFor="tradingName" className="label-name">
                Registered Name
              </label>
            </div>
            {/* {error.businessName && <span style={{ color: "red" }}>{error.businessName}</span>} */}
          </div>
        </div>
        <div className="grid">
          <div className=" col-12 md:col-6 para">
            {/* <label className="mb-3 font-semibold">ABN</label> */}
            <div className="input-container-business">
              <InputText
                id="businessNumber"
                name="businessNumber"
                value={formData.businessNumber}
                placeholder=""
                onChange={handleChange}
                // className={error.businessNumber ? 'border-red' : ''}
                className={`input-placeholder ${
                  error.businessNumber
                    ? "border-red"
                    : formData.businessNumber
                    ? "border-green"
                    : ""
                }`}
                maxLength={13}
              />
              <label htmlFor="businessNumber" className="label-name">
                ABN
              </label>
            </div>
            {/* {error.businessNumber && <span style={{ color: "red" }}>{error.businessNumber}</span>} */}
          </div>
          {country === "nz" && (
            <div className="col-12 md:col-6 para">
              <div className="input-container-business">
                <InputText
                  id="businessTaxNumber"
                  name="businessTaxNumber"
                  value={formData.businessTaxNumber}
                  placeholder=""
                  onChange={handleChange}
                  className={`input-placeholder ${
                    error.businessTaxNumber
                      ? "border-red"
                      : formData.businessTaxNumber
                      ? "border-green"
                      : ""
                  }`}
                  // maxLength={9}
                />
                <label htmlFor="businessTaxNumber" className="label-name">
                  GST Number
                </label>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="p-mb-3 flex flex-column gap-2">
        <p className="font-semibold" style={{ color: "#585858" }}>
          Account Holder Details
        </p>
        <small style={{ marginTop: "-15px" }}>
          The person responsible for posting jobs and receiving responses on
          their mobile phone.
        </small>
        <div className="formgrid grid mt-3">
          <div className="field col-12 md:col-6">
            {/* <label className=" mb-3 font-semibold">First name</label> */}
            <div className="input-container-business">
              <InputText
                id="firstName"
                name="firstName"
                value={formData.firstName}
                placeholder=""
                onChange={handleChange}
                // className={error.firstName ? 'border-red' : ''}
                className={`input-placeholder ${
                  error.firstName
                    ? "border-red"
                    : formData.firstName
                    ? "border-green"
                    : ""
                }`}
              />
              <label htmlFor="firstName" className="label-name">
                First name
              </label>
            </div>
            {/* {error.firstName && <span style={{ color: "red" }}>{error.firstName}</span>} */}
          </div>
          <div className=" field col-12 md:col-6">
            {/* <label className=" mb-3 font-semibold">Last name</label> */}
            <div className="input-container-business">
              <InputText
                id="lastName"
                name="lastName"
                value={formData.lastName}
                placeholder=""
                onChange={handleChange}
                // className={error.lastName ? 'border-red' : ''}
                className={`input-placeholder ${
                  error.lastName
                    ? "border-red"
                    : formData.lastName
                    ? "border-green"
                    : ""
                }`}
              />
              <label htmlFor="lastName" className="label-name">
                Last name
              </label>
            </div>
            {error.lastName && (
              <span style={{ color: "red" }}>{error.lastName}</span>
            )}
          </div>
        </div>
      </div>
      <div className="justify-content-start mt-3">
        <label
          htmlFor="gender"
          className="font-semibold"
          style={{ color: "#585858" }}
        >
          Your Gender
        </label>
        <div className="flex flex-wrap gap-3 mb-2 mt-3 cursor-pointer">
          {filteredGenders.map((key, index) => (
            <div
              key={index}
              className="align-items-center radiobutton-style"
              data-should_hover={formData.gender !== c.genders[key]}
              style={{
                border: error.gender
                  ? "1px solid red" // Apply red border if there's an error
                  : formData.gender === c.genders[key]
                  ? "1px solid green"
                  : "1px solid gainsboro",
                padding: "11px 16px 11px 11px",
                borderRadius: "10px",
                position: "relative",
              }}
              onClick={() => handleGenderChange(c.genders[key])}
            >
              <label
                htmlFor={`gender${index}`}
                className="ml-1 h-joinnow2-gender pr-5 cursor-pointer"
                style={{
                  color: formData.gender === c.genders[key] ? "green" : "",
                  fontWeight: formData.gender === c.genders[key] ? 700 : 500,
                }}
              >
                {String(key)
                  .replace(/_/g, " ") // Replace underscores with spaces
                  .split(" ") // Split the string into words
                  .map(
                    (word) =>
                      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                  ) // Capitalize each word
                  .join(" ")}{" "}
                {/* Join the words back into a single string */}
              </label>
              <div
                style={{
                  position: "absolute",
                  top: 3.5,
                  right: 6,
                }}
              >
                <input
                  type="radio"
                  id={`gender${index}`}
                  value={c.genders[key]}
                  checked={formData.gender === c.genders[key]}
                  onChange={() => handleGenderChange(c.genders[key])}
                  style={{
                    cursor: "pointer",
                    opacity: 0,
                    position: "absolute",
                  }}
                />
                {formData.gender === c.genders[key] ? (
                  <div
                    style={{
                      width: "16px",
                      height: "16px",
                      backgroundColor: "#179D52",
                      borderRadius: "50%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <span
                      style={{
                        color: "white",
                        fontWeight: "bold",
                        fontSize: "12px",
                        lineHeight: "12px",
                      }}
                    >
                      ✓
                    </span>
                  </div>
                ) : (
                  <div
                    style={{
                      width: "16px",
                      height: "16px",
                      border: "1px solid gainsboro",
                      borderRadius: "50%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: "transparent",
                    }}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
        <div>
          {error.gender && <span style={{ color: "red" }}>{error.gender}</span>}{" "}
        </div>
        <div className="text-center font-semibold mt-4">
          {error.businessNumber && (
            <span style={{ color: "red" }}>{error.businessNumber}</span>
          )}
          {error.businessTaxNumber && (
            <span style={{ color: "red" }}>{error.businessTaxNumber}</span>
          )}
        </div>
      </div>
      <div
        className="flex flex-wrap gap-3 align-items-center justify-content-between"
        style={{
          marginBottom: "8%",
          marginTop: "7%",
        }}
      >
        <div className="">
          <i className="h-joinnow4-lable pi pi-angle-left pr-2"></i>
          <a
            className="h-joinnow4-lable cursor-pointer font-semibold"
            onClick={handlePrevious}
          >
            Go back
          </a>
        </div>
        <button
          className={`h-joinnow-button ${isFormValid ? "shadow-4" : ""}`}
          onClick={handleSubmit}
          disabled={!isFormValid}
          style={{
            backgroundColor: isFormValid ? "#FFA500" : "#DFDFDF",
            borderColor: isFormValid ? "#FFA500" : "#DFDFDF", // Set border color conditionally
            color: isFormValid ? "#FFFFFF" : "gray", // Set text color conditionally
            cursor: isFormValid ? "pointer" : "not-allowed",
          }}
        >
          Next
          <i className="pi pi-angle-right"></i>
        </button>
      </div>
    </form>
  );
};
