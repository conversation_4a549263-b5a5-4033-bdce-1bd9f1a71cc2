import React from 'react';
import styles from '../Common/styles/shimmer-helper-card.module.css'; // Import modular CSS

const ShimmerHelperCard: React.FC = () => {
    return (
        <div className={styles.shimmerCard}>
            <div className={`${styles.shimmer} ${styles.shimmerImage}`} />
             <div className={styles.shimmerContent}>
                <div className={`${styles.shimmer} ${styles.shimmerTitle}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
            </div>
            <div className={`${styles.shimmer} ${styles.shimmerImage}`} />
             <div className={styles.shimmerContent}>
                <div className={`${styles.shimmer} ${styles.shimmerTitle}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
                <div className={`${styles.shimmer} ${styles.shimmerLine}`} />
            </div>
        </div>
        
    );
};

export default ShimmerHelperCard;