# StatusCode-Wise TimesheetList Implementation Guide

## 🎯 **What Was Fixed**

I've updated all three hooks to properly filter timesheets by the correct `statusCode` values based on the ApprovalStatus constants, ensuring each route shows only the relevant timesheets.

### ✅ **1. Correct Status Code Mapping**
- **Status 1** (`AWAITING_CONFIRMATION`) → `/awaiting-confirmation` route
- **Status 2** (`AWAITING_APPROVAL`) → `/awaiting-approval` route  
- **Status 4 & 5** (`PARENT_ADJUSTED` & `HELPER_ADJUSTED`) → `/adjusted-timesheets` route

### ✅ **2. Proper Filtering Logic**
- **Each hook now filters** by the correct statusCode before mapping data
- **Consistent filtering approach** across all hooks
- **Proper status mapping** for display purposes

### ✅ **3. Updated All Hooks**
- **`useTimesheetData.ts`** → Filters for status 1 (AWAITING_CONFIRMATION)
- **`useAdjustedTimesheetDetails.ts`** → Filters for status 4 & 5 (PARENT_ADJUSTED & HELPER_ADJUSTED)
- **`useAwaitingApprovalDetails.ts`** → Filters for status 2 (AWAITING_APPROVAL)

## 🔧 **Status Code Constants Reference**

Based on `juggleStreetConstants.js`:
```typescript
ApprovalStatus: {
  AWAITING_CONFIRMATION: 1,    // Parent needs to confirm timesheet
  AWAITING_APPROVAL: 2,        // Timesheet awaiting approval
  FINALIZED: 3,               // Timesheet is finalized
  PARENT_ADJUSTED: 4,         // Parent made adjustments
  HELPER_ADJUSTED: 5,         // Helper made adjustments
  REJECTED: 6,                // Timesheet was rejected
}
```

## 🔧 **Implementation Details**

### **1. useTimesheetData.ts (Awaiting Confirmation)**

#### **Status Mapping:**
```typescript
const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_CONFIRMATION]: "Awaiting Your Confirmation", // Status 1
};
```

#### **Filtering Logic:**
```typescript
// Filter for awaiting confirmation timesheets (status 1: AWAITING_CONFIRMATION)
const filteredData = response.filter((item: TimesheetApiItem) => 
  item.status === c.ApprovalStatus.AWAITING_CONFIRMATION
);
console.log("Filtered awaiting confirmation data (status 1):", filteredData);

const mappedData: TimesheetEntry[] = filteredData.map((item) => ({
  id: item.id,
  status: statusMap[item.status] || "Unknown",
  statusCode: item.status, // Keep original status code
  // ... other properties
}));
```

### **2. useAdjustedTimesheetDetails.ts (Adjusted Timesheets)**

#### **Status Mapping:**
```typescript
const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.PARENT_ADJUSTED]: "Parent Adjusted", // Status 4
  [c.ApprovalStatus.HELPER_ADJUSTED]: "Helper Adjusted", // Status 5
};
```

#### **Filtering Logic:**
```typescript
// Filter for adjusted timesheets (status 4: PARENT_ADJUSTED, status 5: HELPER_ADJUSTED)
const filteredData = response.filter((item: AdjustedTimesheetApiItem) => 
  item.status === c.ApprovalStatus.PARENT_ADJUSTED || 
  item.status === c.ApprovalStatus.HELPER_ADJUSTED
);
console.log("Filtered adjusted timesheet data (status 4 or 5):", filteredData);

const mappedData: AdjustedTimesheetEntry[] = filteredData.map((item: AdjustedTimesheetApiItem) => ({
  id: item.id,
  status: statusMap[item.status] || "Adjusted Timesheet",
  statusCode: item.status, // Keep original status code
  // ... other properties
}));
```

### **3. useAwaitingApprovalDetails.ts (Awaiting Approval)**

#### **Status Mapping:**
```typescript
const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Approval", // Status 2
};
```

#### **Filtering Logic:**
```typescript
// Filter for awaiting approval timesheets (status 2: AWAITING_APPROVAL)
const filteredData = response.filter((item: AwaitingApprovalApiItem) => 
  item.status === c.ApprovalStatus.AWAITING_APPROVAL
);
console.log("Filtered awaiting approval data (status 2):", filteredData);

const mappedData: AwaitingApprovalEntry[] = filteredData.map((item: AwaitingApprovalApiItem) => ({
  id: item.id,
  status: statusMap[item.status] || "Awaiting Approval",
  statusCode: item.status, // Keep original status code
  // ... other properties
}));
```

## 🔄 **Data Flow by Route**

### **Route: `/parent-home/timesheet/awaiting-confirmation`**
```
1. useTimesheetData() hook triggers
         ↓
2. Service.getTimeSheet() API call
         ↓
3. Filter response for status === 1 (AWAITING_CONFIRMATION)
         ↓
4. Map filtered data with status: "Awaiting Your Confirmation"
         ↓
5. TimesheetList shows only status 1 entries
         ↓
6. Badge count = number of status 1 entries
```

### **Route: `/parent-home/timesheet/adjusted-timesheets`**
```
1. useAdjustedTimesheetDetails() hook triggers
         ↓
2. Service.getHelperAdjustedlist() API call
         ↓
3. Filter response for status === 4 OR status === 5
         ↓
4. Map filtered data with status: "Parent Adjusted" or "Helper Adjusted"
         ↓
5. TimesheetList shows only status 4 & 5 entries
         ↓
6. Badge count = number of status 4 & 5 entries
```

### **Route: `/parent-home/timesheet/awaiting-approval`**
```
1. useAwaitingApprovalDetails() hook triggers
         ↓
2. Service.getFinalizedTimesheetslist() API call
         ↓
3. Filter response for status === 2 (AWAITING_APPROVAL)
         ↓
4. Map filtered data with status: "Awaiting Approval"
         ↓
5. TimesheetList shows only status 2 entries
         ↓
6. Badge count = number of status 2 entries
```

## 📊 **Expected Console Output**

### **For Awaiting Confirmation:**
```
🔄 Route changed to awaiting-confirmation, fetching data...
API Response getTimeSheet: [array of all timesheets]
Filtered awaiting confirmation data (status 1): [only status 1 entries]
Mapped timesheet data: [processed status 1 data]
✅ Timesheet data loaded successfully, count: X
```

### **For Adjusted Timesheets:**
```
🔄 Route changed to adjusted-timesheets, fetching data...
API Response getTimeSheet for adjusted: [array of all timesheets]
Filtered adjusted timesheet data (status 4 or 5): [only status 4 & 5 entries]
Mapped adjusted timesheet data: [processed status 4 & 5 data]
✅ Adjusted timesheet data loaded successfully, count: Y
```

### **For Awaiting Approval:**
```
🔄 Route changed to awaiting-approval, fetching data...
API Response getTimeSheet for awaiting approval: [array of all timesheets]
Filtered awaiting approval data (status 2): [only status 2 entries]
Mapped awaiting approval data: [processed status 2 data]
✅ Awaiting approval data loaded successfully, count: Z
```

## 🧪 **Testing**

### **1. Check Status Filtering:**
- Navigate to each route and check console logs
- Verify only correct statusCode entries are shown
- Check that badge counts match filtered data

### **2. Verify TimesheetCard Display:**
- Each route should show TimeSheetCard components
- Status text should match the statusMap values
- No entries should show if no data matches statusCode

### **3. Test NoJobsCard:**
- If no entries match the statusCode, should show NoJobsCard
- Each route should have appropriate empty state message

### **4. Badge Count Verification:**
- Awaiting Confirmation badge = count of status 1 entries
- Adjusted Timesheets badge = count of status 4 & 5 entries
- Awaiting Approval badge = count of status 2 entries

## 🎯 **Benefits**

### **✅ Correct Status Filtering:**
- Each route shows only relevant timesheets
- Uses proper ApprovalStatus constants
- No incorrect or mixed status entries

### **✅ Consistent Implementation:**
- All hooks follow same filtering pattern
- Proper status mapping for display
- Consistent error handling and logging

### **✅ Accurate Badge Counts:**
- Badge counts reflect actual filtered data
- Dynamic updates based on API response
- No hardcoded or incorrect counts

### **✅ Better User Experience:**
- Users see only relevant timesheets per route
- Clear status labels for each timesheet
- Proper empty states when no data matches

## 🚀 **Summary**

Your timesheet routes now have proper statusCode-wise filtering:
- ✅ **Status 1** (AWAITING_CONFIRMATION) → `/awaiting-confirmation` route
- ✅ **Status 2** (AWAITING_APPROVAL) → `/awaiting-approval` route
- ✅ **Status 4 & 5** (PARENT_ADJUSTED & HELPER_ADJUSTED) → `/adjusted-timesheets` route
- ✅ **Proper filtering logic** in all hooks
- ✅ **Correct status mapping** for display
- ✅ **Accurate badge counts** based on filtered data

All TimesheetList components will now show the correct data based on statusCode! 🎉
