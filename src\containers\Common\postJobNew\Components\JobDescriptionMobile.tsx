import React, { useState, useEffect } from "react";
import styles from "../../styles/job-details.module.css";
import { useJobManager } from "../provider/JobManagerProvider";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

const JobDescription = () => {
  const { payload, prev, next, setpayload } = useJobManager();
  const [description, setDescription] = useState(payload.specialInstructions || "");

  const handleDescriptionChange = (e) => {
    const newDescription = e.target.value;
    setDescription(newDescription);
  };

  const handlePrevClick = () => {
    setpayload({ ...payload, specialInstructions: description });
    prev("job-details");
  };

  const isFormValid = () => description.trim().length > 0;

  return (
    <div className={styles.jobDescriptionContainerMobile}>
      <main className={styles.jobDetailsMainMobile}>
        <div className={styles.jobDescriptionSection}>
          <h2 className={styles.jobDescriptionTitleMobile}>Job Details</h2>
          <p className={styles.jobDetailsSectionInstructSec}>Describe the job*</p>
          <textarea
            className={`${styles.jobDetailsTextareaMobile} ${description ? styles.activeBorder : ""}`}
            placeholder="What does your helper need to know about this job and working with your family?"
            value={description}
            onChange={handleDescriptionChange}
          />
        </div>
      </main>

      <BackButtonPortal id="back-button-portal">
        <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" onClick={handlePrevClick} />      
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        isDisabled={!isFormValid()}
        onClick={() => {
          setpayload({ ...payload, specialInstructions: description });
          next("jobpricing-step1"); // Or whatever the next step is
        }}
      />
    </div>
  );
};

export default JobDescription;
