import React from "react";
import { Routes, Route, useNavigate, Navigate } from "react-router-dom";
import c from "./helper/juggleStreetConstants";
import Joinnow from "./components/Auth/Signup/JoinNow";
import utils from "./components/utils/util";
import CookiesConstant from "./helper/cookiesConst";
import FullLoader from "./commonComponents/FullLoader";
import Cards from "./commonComponents/Card";
import { Join<PERSON>owProvider } from "./model/JoinNowContext";
import JoinNowCard from "./commonComponents/JoinNowCard";
import HelperHowItWorks from "./containers/Helper/Components/HelperHowItWorks";
import HelperPricing from "./containers/Helper/Components/HelperPricing";
import Onboarding from "./containers/Helper/Components/Onboarding";
import LearnMore from "./containers/Helper/Components/LearnMore";
import AboutUs from "./containers/Helper/Components/LearnMore/AboutUs";
import TermsCondition from "./containers/Helper/Components/LearnMore/TermsCondition";
import Privacy from "./containers/Helper/Components/LearnMore/Privacy";
import EmployeeBenifits from "./containers/Helper/Components/EmployeeBenifits";
import Unauthorized from "./commonComponents/Unauthorized";
import MyJobs from "./containers/Helper/Components/MyJobs";
import ConnectionRequest from "./containers/Helper/Components/ConnectionRequest";
import BaseLayout from "./commonComponents/BaseLayout";
// import JobType from "./containers/Common/postJobNew/Components/JobType";
// import JobPostStep2 from "./containers/Common/postJobNew/Components/JobPostStep2";

const ClientProfile = React.lazy(() => import("./containers/Helper/Components/ClientProfile"));

function LazyLoad<T extends React.ComponentType<any>>(Component: React.LazyExoticComponent<T>, props: React.ComponentProps<T>) {
  return (
    <React.Suspense fallback={<FullLoader />}>
      <Component {...props} />
    </React.Suspense>
  );
}
const Login = React.lazy(() => import("./components/Auth/Login/Login"));
const Signup = React.lazy(() => import("./components/Auth/Signup/Signup"));
const ForgotPassword = React.lazy(() => import("./components/Auth/ForgotPassword/ForgotPassword"));
const ResetPassword = React.lazy(() => import("./components/Auth/ResetPassword/ResetPassword"));
const ParentHome = React.lazy(() => import("./containers/Parent/Home"));
const ProviderProfile = React.lazy(() => import("./containers/Parent/ProviderProfile/ProviderProfile"));
const HelperHome = React.lazy(() => import("./containers/Helper/HelperNewHome"));
const BusinessHome = React.lazy(() => import("./containers/Business/Home"));
const ManageJobs = React.lazy(() => import("./containers/Common/manageJobs/ManageJobs"));
const JobSuccess = React.lazy(() => import("./containers/Common/postJobNew/Components/JobSuccess"));
const JobReferral = React.lazy(() => import("./containers/Parent/JobReferral"));
const InAppChat = React.lazy(() => import("./containers/Parent/InAppChat/InAppChat"));
const AwardJobConfirmation = React.lazy(() => import("./containers/Common/manageJobs/Common/AwardJobConfirmation"));
const Layout = React.lazy(() => import("./containers/Common/Layout"));
const JobType = React.lazy(() => import("./containers/Common/postJobNew/Components/JobType"));
const JobPosting = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPosting"));
const JobPrice = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPricing/JobPrice"));
const JobDetails = React.lazy(() => import("./containers/Common/postJobNew/Components/JobDetails"));
const JobDescription = React.lazy(() => import("./containers/Common/postJobNew/Components/JobDescriptionMobile"));
const Section4Mobile = React.lazy(() => import("./containers/Common/postJobNew/Components/Pricing&Payments/Section4Mobile"));
const OverTimeSection = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPricing/OverTimeSection"));
const JobPricingStep1 = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPricing/JobPricingStep1"));
const JobPricingStep2 = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPricing/JobPricingStep2"));
const JobPricingStep3 = React.lazy(() => import("./containers/Common/postJobNew/Components/JobPricing/JobPricingStep3"));
const CandidateSelection = React.lazy(() => import("./containers/Common/postJobNew/Components/CandidateSelection/CandidateSelection"));
const CandidateMatching = React.lazy(() => import("./containers/Common/postJobNew/Components/CandidateMatching"));
const ReviewAndPost = React.lazy(() => import("./containers/Common/postJobNew/Components/ReviewAndPost"));
const PricingPaymentsStep1 = React.lazy(() => import("./containers/Common/postJobNew/Components/Pricing&Payments/PricingPaymentsStep1"));
const PricingPaymentsStep2 = React.lazy(() => import("./containers/Common/postJobNew/Components/Pricing&Payments/PricingPaymentsStep2"));
const TypeAndYears = React.lazy(() => import("./containers/Common/postJobNew/Components/TypeAndYears"));
const TutoringSubjects = React.lazy(() => import("./containers/Common/postJobNew/Components/TutoringSubjects"));
const DayAndScheduleRequired = React.lazy(() => import("./containers/Common/postJobNew/Components/DayAndSchedule/DayAndScheduleRequired"));
const Subscription = React.lazy(() => import("./containers/Common/postJobNew/Components/Payments/Payments"));
const JobSummary = React.lazy(() => import("./containers/Common/postJobNew/Components/JobSummary"));
const JobLayout = React.lazy(() => import("./containers/Common/postJobNew/Layout/jobLayout"));
const Payments = React.lazy(() => import("./containers/Common/payments/PaymentsLayout"));
const Timesheet = React.lazy(() => import("./containers/Common/payments/Tabs/TimeSheet"));
const Payment = React.lazy(() => import("./containers/Common/payments/Tabs/Payments"));

function RoleBasedJoinnow({ selectedRole, nextPath }) {
  const navigate = useNavigate();
  const onNext = () => {
    // Navigate based on the selectedRole if necessary
    if (selectedRole === c.lookingFor.BUSINESS_CLIENT) {
      navigate("/business-details");
    } else {
      navigate(nextPath);
    }
  };

  return (
    <Joinnow
      signUpAs={selectedRole}
      onNext={onNext}
      setCurrentPage={function (): void {
        throw new Error("Function not implemented.");
      }}
    />
  );
}

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = !!utils.getCookie(CookiesConstant.accessToken);
  return isAuthenticated ? children : <Navigate to={`/login`} replace />;
};

const AutoLogin = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = !utils.getCookie(CookiesConstant.accessToken);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  let navigateToRoute = null;
  if (clientType == 1) {
    navigateToRoute = `/parent-home`;
  } else if (clientType == 2) {
    navigateToRoute = `/business-home`;
  } else {
    navigateToRoute = `/helper-home`;
  }

  return isAuthenticated ? children : <Navigate to={navigateToRoute} replace />;
};
const route = () => {
  return (
    <>
      <BaseLayout>
        <Routes>
          {/* Public routes */}
          <Route
            path="/"
            element={
              <AutoLogin>
                <Navigate to={`/login`} replace />
              </AutoLogin>
            }
          />
          <Route
            path="login"
            element={
              <AutoLogin>
                <Cards className="custom-card-width">{LazyLoad(Login, {})}</Cards>
              </AutoLogin>
            }
          />
          <Route
            path="register"
            element={
              <AutoLogin>
                <Cards>{LazyLoad(Signup, {})}</Cards>
              </AutoLogin>
            }
          />
          <Route
            path="forgot-password"
            element={
              <AutoLogin>
                <Cards>{LazyLoad(ForgotPassword, {})}</Cards>
              </AutoLogin>
            }
          />
          <Route
            path="reset-password"
            element={
              <AutoLogin>
                <Cards>{LazyLoad(ResetPassword, {})}</Cards>
              </AutoLogin>
            }
          />
          <Route
            path="join-now"
            element={
              <AutoLogin>
                <JoinNowProvider>
                  <JoinNowCard>{LazyLoad(Signup, {})}</JoinNowCard>
                </JoinNowProvider>
              </AutoLogin>
            }
          />
          <Route
            path="signup-family"
            element={
              <AutoLogin>
                <JoinNowProvider>
                  <JoinNowCard>
                    <RoleBasedJoinnow selectedRole={c.lookingFor.INDIVIDUAL_CLIENT} nextPath="/join-now2" />
                  </JoinNowCard>
                </JoinNowProvider>
              </AutoLogin>
            }
          />
          <Route
            path="signup-business"
            element={
              <AutoLogin>
                <JoinNowProvider>
                  <JoinNowCard>
                    <RoleBasedJoinnow selectedRole={c.lookingFor.BUSINESS_CLIENT} nextPath="/business-details" />
                  </JoinNowCard>
                </JoinNowProvider>
              </AutoLogin>
            }
          />
          <Route
            path="signup-helper"
            element={
              <AutoLogin>
                <JoinNowProvider>
                  <JoinNowCard>
                    <RoleBasedJoinnow selectedRole={c.lookingFor.INDIVIDUAL_PROVIDER} nextPath="/join-now" />
                  </JoinNowCard>
                </JoinNowProvider>
              </AutoLogin>
            }
          />
          {/* Protected routes */}
          <Route path="/" element={<ProtectedRoute>{LazyLoad(Layout, {})}</ProtectedRoute>}>
            <Route path="/parent-home" element={LazyLoad(Layout, {})}>
              <Route index element={LazyLoad(ParentHome, {})} />
              <Route path="provider-profile" element={LazyLoad(ProviderProfile, { candidateId: -1 })} />
              <Route path="manage-jobs/:jobId?" element={LazyLoad(ManageJobs, {})} />
              {/* <Route path="timesheet" element={LazyLoad(Timesheet, {})} /> */}
              {/* <Route path="payments" element={LazyLoad(Payments, {})} /> */}
              <Route path="post-job/complete" element={LazyLoad(JobSuccess, {})} />
              <Route path="referJob" element={LazyLoad(JobReferral, {})} />
              <Route path="job/:action" element={<JobLayout />}>
                <Route path="job-type" element={LazyLoad(JobType, {})} />
                <Route path="job-posting" element={LazyLoad(JobPosting, {})} />
                <Route path="job-pricing" element={LazyLoad(JobPrice, {})} />
                <Route path="job-details" element={LazyLoad(JobDetails, {})} />
                <Route path="jobpricing-step1" element={LazyLoad(JobPricingStep1, {})} />
                <Route path="jobpricing-step2" element={LazyLoad(JobPricingStep2, {})} />
                <Route path="jobpricing-step3" element={LazyLoad(JobPricingStep3, {})} />
                <Route path="candidate-selection" element={LazyLoad(CandidateSelection, {})} />
                <Route path="job-description-mobile" element={LazyLoad(JobDescription, {})} />
                <Route path="section4-mobile" element={LazyLoad(Section4Mobile, {})} />
                <Route path="overtime-section-mobile" element={LazyLoad(OverTimeSection, {})} />
                <Route path="candidate-matching" element={LazyLoad(CandidateMatching, {})} />
                <Route path="review-post" element={LazyLoad(ReviewAndPost, {})} />
                <Route path="job-summary" element={LazyLoad(JobSummary, {})} />
                <Route path="pricing-payments-step1" element={LazyLoad(PricingPaymentsStep1, {})} />
                <Route path="pricing-payments-step2" element={LazyLoad(PricingPaymentsStep2, {})} />
                <Route path="typeand-years" element={LazyLoad(TypeAndYears, {})} />
                <Route path="tutoring-subjects" element={LazyLoad(TutoringSubjects, {})} />
                <Route path="day-and-schedule" element={LazyLoad(DayAndScheduleRequired, {})} />
                <Route path="subscription" element={LazyLoad(Subscription, {})} />
              </Route>
              <Route path="payments" element={LazyLoad(Payments, {})}>
                <Route index element={<Navigate to="timesheet" replace />} />
                <Route path="timesheet" element={null} />
                <Route path="payment" element={null} />
                <Route path="chat" element={null} />
              </Route>
              <Route path="inAppChat/:userId?" element={LazyLoad(InAppChat, {})} />
            </Route>
            <Route path="/helper-home" element={LazyLoad(Layout, {})}>
              <Route index element={LazyLoad(HelperHome, {})} />
              <Route path="client-profile" element={LazyLoad(ClientProfile, {})} />
              <Route path="myjobs/:jobId?" element={<MyJobs />} />
              <Route path="referJob" element={LazyLoad(JobReferral, {})} />
              <Route path="inAppChat/:userId?" element={LazyLoad(InAppChat, {})} />
              <Route path="public" element={LazyLoad(Layout, {})}>
                <Route path="onboarding?" element={<Onboarding />} />
                <Route path="helper-how-it-works?" element={<HelperHowItWorks />} />
                <Route path="helper-pricing?" element={<HelperPricing />} />
                <Route path="connections/invitations?" element={<ConnectionRequest />} />
                <Route path="learn-more?" element={<LearnMore />} />
                <Route path="about-us?" element={<AboutUs />} />
                <Route path="privacy?" element={<Privacy />} />
                <Route path="terms-and-conditions?" element={<TermsCondition />} />
                <Route path="employee-benefts?" element={<EmployeeBenifits />} />
              </Route>
            </Route>

            <Route path="/business-home" element={LazyLoad(Layout, {})}>
              <Route index element={LazyLoad(BusinessHome, {})} />
              <Route path="provider-profile" element={LazyLoad(ProviderProfile, { candidateId: -1 })} />
              <Route path="manage-jobs/:jobId?" element={LazyLoad(ManageJobs, {})} />
              <Route path="payments" element={LazyLoad(Payments, {})} />
              <Route path="post-job/complete" element={LazyLoad(JobSuccess, {})} />
              <Route path="referJob" element={LazyLoad(JobReferral, {})} />
              <Route path="job/:action" element={<JobLayout />}>
                <Route path="job-type" element={LazyLoad(JobType, {})} />
                <Route path="job-posting" element={LazyLoad(JobPosting, {})} />
                <Route path="job-pricing" element={LazyLoad(JobPrice, {})} />
                <Route path="job-details" element={LazyLoad(JobDetails, {})} />
                <Route path="jobpricing-step1" element={LazyLoad(JobPricingStep1, {})} />
                <Route path="jobpricing-step2" element={LazyLoad(JobPricingStep2, {})} />
                <Route path="jobpricing-step3" element={LazyLoad(JobPricingStep3, {})} />
                <Route path="candidate-selection" element={LazyLoad(CandidateSelection, {})} />
                <Route path="job-description-mobile" element={LazyLoad(JobDescription, {})} />
                <Route path="section4-mobile" element={LazyLoad(Section4Mobile, {})} />
                <Route path="overtime-section-mobile" element={LazyLoad(OverTimeSection, {})} />
                <Route path="candidate-matching" element={LazyLoad(CandidateMatching, {})} />
                <Route path="review-post" element={LazyLoad(ReviewAndPost, {})} />
                <Route path="job-summary" element={LazyLoad(JobSummary, {})} />
                <Route path="pricing-payments-step1" element={LazyLoad(PricingPaymentsStep1, {})} />
                <Route path="pricing-payments-step2" element={LazyLoad(PricingPaymentsStep2, {})} />
                <Route path="typeand-years" element={LazyLoad(TypeAndYears, {})} />
                <Route path="tutoring-subjects" element={LazyLoad(TutoringSubjects, {})} />
                <Route path="day-and-schedule" element={LazyLoad(DayAndScheduleRequired, {})} />
                <Route path="subscription" element={LazyLoad(Subscription, {})} />
              </Route>
              <Route path="post-job-new/:action/:id?" element={<JobLayout />}></Route>
              <Route path="inAppChat/:userId?" element={LazyLoad(InAppChat, {})} />
            </Route>
            <Route path="/awardJobConfirmation/:name/:imageUrl/:jobId" element={LazyLoad(AwardJobConfirmation, {})} />
          </Route>

          {/* Join now routes */}

          {/* Redirect to login if no route matches */}
          <Route path="*" element={<Navigate to="/login" replace />} />
          <Route path="unauthorized" element={<Unauthorized />} />
        </Routes>
      </BaseLayout>
    </>
  );
};
export default route;
