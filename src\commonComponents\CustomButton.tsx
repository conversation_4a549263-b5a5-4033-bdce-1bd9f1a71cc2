import React from 'react';
import styles from '../commonStyle/custom-button.module.css'; // Import your custom styles

interface CustomButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    label: React.ReactNode;
}

const CustomButton: React.FC<CustomButtonProps> = ({ label, disabled, className, ...props }) => {
    return (
        <button
            {...props}
            className={`${styles.customButton} ${disabled ? styles.disabled : ''} ${className}`} // Apply conditional styles based on 'disabled'
            disabled={disabled} // Pass the disabled prop directly
        >
            {label}
        </button>
    );
};

export default CustomButton;
