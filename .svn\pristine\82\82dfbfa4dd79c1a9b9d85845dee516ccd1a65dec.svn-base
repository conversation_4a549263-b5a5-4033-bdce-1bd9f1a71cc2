import { Tab<PERSON>anel, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useState } from "react";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import PaymentSuccess from "../Common/PaymentSuccess";
import { useNavigate } from 'react-router-dom';

const timesheetData = [
  { start: "7:30 pm", finish: "11:30 pm", hours: 4, rate: 25, total: 100 },
];

const TimeSheet: React.FC<{
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;

}> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [showCard, setShowCard] = useState(false);
  const navigate = useNavigate();
  function generatePDF(): void {
    throw new Error("Function not implemented.");
  }

  const handleGoBack = () => {
    console.log("Go back clicked");
  };

  const handleSubmit = () => {
    alert("Timesheet confirmed!");
  };

  const handleEdit = () => {
    alert("Edit timesheet clicked");
  };
  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel
        header={
          <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
            Awaiting your <br /> Confirmation
          </div>
        }
      >
        {!showCard ? (
          <>
            <div
             style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}
            >
              Content for Awaiting your Confirmation Timesheets
            </div>
            {/* <button
              onClick={() => setShowCard(true)}
              style={{
                marginTop: '20px',
                padding: '8px 16px',
                fontSize: '14px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
              }}
            >
              Confirm Timesheet
            </button> */}
          </>
        ) : (
          // <AwaitingConfirmationCard />
          
          // <AwaitingConfirmationCard
          //   profileName="Craig S"
          //   profileImage="https://primefaces.org/cdn/primereact/images/avatar/amyelsner.png"
          //   jobType="One Off Job"
          //   jobDate="16th of January, 2024"
          //   jobAddress="9 Christie Beach, South Brisbane"
          //   baseRate={25}
          //   extraHoursRate={35}
          //   timesheetRows={timesheetData}
          //   onGoBack={handleGoBack}
          //   onSubmit={handleSubmit}
          // />

          <AwaitingConfirmationCard
            profileName="Craig S"
            profileImage="https://primefaces.org/cdn/primereact/images/avatar/amyelsner.png"
            jobType="Babysitting"
            jobDate="Monday, 16th of January, 2024"
            jobAddress="1234 Elm Street, Sydney"
            baseRate={30}
            extraHoursRate={45}
            initialTimesheetRows={[
              { start: "6:00", finish: "9:00", hours: 3, rate: 30, total: 90 },
              { start: "3:00", finish: "6:00", hours: 3, rate: 45, total: 135 }
            ]}
            onSubmit={() => console.log("Submitted")}
            onGoBack={() => console.log("Go back")}
          />


          // <PaymentSuccess
          //   amount="120.50"
          //   invoiceNo="102938"
          //   status="Complete"
          //   date="17.01.2024"
          //   time="1:24pm"
          //   paymentType="PayTo"
          //   onDone={() => navigate('/home')}
          // />
        )}
      </TabPanel>

      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        Parent-adjusted <br /> Timesheets
      </div>
      }
      >
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Parent-adjusted Timesheets</div>
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        Awaiting Parent <br />Approval </div>}>
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Awaiting Parent Approval</div>
      </TabPanel>
    </TabView>
  );
};

export default TimeSheet;