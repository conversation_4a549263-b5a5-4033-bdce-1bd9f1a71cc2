type EventPayload<T> = Record<string, T>;

type Resolver<T> = {
  resolve: (data: T) => void;
  reject: (err: any) => void;
};

export const Events = {
  userUpdated: "USER_UPDATED",
};

export class BridgeClient {
  private static resolvers: Map<string, Resolver<any>> = new Map();

  static getUUID() {
    if (crypto.randomUUID) {
      return crypto.randomUUID();
    }
    // Simple fallback
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  static sendEvent<Req, Res>(
    event: keyof typeof Events,
    payload: EventPayload<Req>,
    expectResponse = false
  ): Promise<Res> | void {
    const id = this.getUUID();

    const message = {
      id,
      event: Events[event],
      payload,
      type: "request",
    };

    this.postToNative(message);

    if (expectResponse) {
      return new Promise<Res>((resolve, reject) => {
        this.resolvers.set(id, { resolve, reject });

        // Optional timeout cleanup
        setTimeout(() => {
          if (this.resolvers.has(id)) {
            this.resolvers.delete(id);
            reject(new Error("BridgeClient timeout: no native response"));
          }
        }, 10000); // 10s timeout
      });
    }
  }

  static receiveResponse(message: any) {
    const { id, payload, type } = message;
    if (type === "response" && this.resolvers.has(id)) {
      const { resolve } = this.resolvers.get(id)!;
      resolve(payload);
      this.resolvers.delete(id);
    }
  }

  private static postToNative(message: any) {
    const json = JSON.stringify(message);

    if (window.webkit?.messageHandlers?.invokeAction) {
      // iOS WKWebView bridge
      window.webkit.messageHandlers.invokeAction.postMessage(json);
    } else if (window.external?.postMessage) {
      // Android bridge you just created
      window.external.postMessage(json);
    } else {
      console.warn("Native bridge not available. Message:", message);
    }
  }
}

(window as any).nativeBridge = {
  receiveResponse: BridgeClient.receiveResponse.bind(BridgeClient),
};
