.headerGradient {
  background: linear-gradient(90deg, #ffa500 0%, #37a950 100%);
  height: 81px;
}
.chatContainer {
  display: flex;
  flex-direction: column;
}

.chatHeader {
  background-color: #4caf50;
  color: white;
  padding: 10px;
  text-align: center;
}

.chatHeader nav button {
  margin: 0 5px;
}

.chatMain {
  flex: 1;
  flex-grow: 1;
  padding: 20px;
  padding-top: 0px;
  overflow-y: auto;
  padding-bottom: 0px;
  display: flex;
}

.chatImage {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 24px;
  font-weight: bold;
}
.backHome {
  width: 137px;
  height: 47px;
  background-color: transparent;
  color: #fff;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  border: 3px solid #fff;
  margin-left: 25px;
  cursor: pointer;
  text-wrap: nowrap;
}
.backManage {
  width: 137px;
  height: 47px;
  background-color: transparent;
  color: #fff;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  border: 3px solid #fff;
  margin-left: 25px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  text-wrap: nowrap;
  padding-inline: 75px;
}
