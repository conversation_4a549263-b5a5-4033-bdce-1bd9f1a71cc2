import { useEffect, useState } from 'react';
import c from '../../../helper/juggleStreetConstants';
import styles from '../styles/side-card.module.css';
import video from '../../../assets/images/video.png';
import videoBlack from '../../../assets/images/Icons/video-black.png';
import photo from '../../../assets/images/Icons/photo.png';
import photoBlack from '../../../assets/images/Icons/photo-white.png';
import { Helper } from './types';
import { Divider } from 'primereact/divider';
import star from '../../../assets/images/Icons/star.png';
import locationMy from '../../../assets/images/location.png';
import chat from '../../../assets/images/Icons/chat.png';
import cancel from '../../../assets/images/Icons/cancel.png';
import chatSecond from '../../../assets/images/Icons/chat-second.png';
import crossChat from '../../../assets/images/Icons/cross-chat.png';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { GrHide } from 'react-icons/gr';
import Service from '../../../services/services';
import useLoader from '../../../hooks/LoaderHook';
import { RxUpdate } from 'react-icons/rx';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';

import { FaCheck, FaRegHeart } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import { updateProfileActivationEnabled } from '../../../store/slices/applicationSlice';
import CookiesConstant from '../../../helper/cookiesConst';
import utils from '../../../components/utils/util';
import { Dialog } from 'primereact/dialog';
import { IoClose } from 'react-icons/io5';
import useIsMobile from '../../../hooks/useIsMobile';
// import { UserResult } from '../../../hooks/SearchGeoSearchHook';
import { UserResult } from '../../../hooks/SearchGeoSearchHook';

interface Props {
    helper: Helper;
    requestId: number;
    selectedSessionId?: string;
    userId: number;
    width?: string;
    boxShadow?: string;
    hideText?: boolean;
    // refresh: () => void;
};

interface helperhomeProps extends Partial<UserResult> {
    refresh: () => void;
}

type test = Props & helperhomeProps;
function SideCard({
    helper,
    requestId,
    selectedSessionId,
    userId,
    hideText,
    width = '311px',
    boxShadow = '0 4px 4px 0 rgba(0, 0, 0, 0.25)',
    refresh,
}: test) {
    // Move useEffect to the component level
    const [contentType, setContentType] = useState<'video' | 'photo'>('video');
    const [canChat, setCanChat] = useState<boolean | null>(null);
    const [chatErrorMessage, setChatErrorMessage] = useState<string>('');
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const [dialogVisible, setDialogVisible] = useState(false);
    const { enableLoader, disableLoader } = useLoader();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const navigate = useNavigate();
    const { isMobile } = useIsMobile();
    const clientType = utils.getCookie(CookiesConstant.clientType);
    useEffect(() => {
        // Check if video is available
        const hasVideo =
            helper?.profileIntroVideo?.externalHashedId &&
            helper.profileIntroVideo.mediaReviewStatus === c.mediaReviewStatus.APPROVED;

        // If video is not available, set content type to photo
        if (hasVideo == undefined) {
            setContentType('photo');
        } else if (hasVideo == true) {
            setContentType('video');
        }
    }, [helper]);

    const handleHide = () => {
        const searchParams = new URLSearchParams();
        // searchParams.set('requestId', String(requestId));

        // Navigate based on clientType
        // if (clientType === '2') {
        //     navigate(`/business-home/provider-profile?${searchParams.toString()}`);
        // } else if (clientType === '1') {
        //     navigate(`/parent-home/provider-profile?${searchParams.toString()}`);
        // }
        // Check if profile is not 100% complete
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                helper?.publicName,
                `Your profile must be 100% complete before you can connect with ${helper?.publicName}`,
                'Complete',
                <RxUpdate style={{ fontSize: '20px' }} />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        // If profile is complete, proceed with hide confirmation
        showConfirmationPopup(
            helper?.publicName,
            `Are you sure you want to Hide ${helper?.publicName}?`,
            'Hide',
            <GrHide style={{ fontSize: '20px' }} />,
            () => {
                executeHideAction();
            }
        );
    };
    const executeHideAction = () => {
        setDialogVisible(false);
        enableLoader();
        let newStatus = c.friendStatus.HIDDEN;
        const payload = {
            friendId: helper?.id,
            status: newStatus,
            ...(requestId
                ? { requestId } // Include requestId if it's present
                : { invitationMessage: '' }), // Otherwise, include invitationMessage
        };

        if (requestId) {
            // Call removeFriend if requestId is present
            Service.removeFriend(
                payload,
                () => {
                    setDialogVisible(false);
                    disableLoader();
                    navigate('/');
                },
                (error) => {
                    showConfirmationPopup(
                        'Error',
                        error.message || 'Failed to delete the account.',
                        'OK',
                        null,
                        () => { }
                    );
                    disableLoader();
                    console.error('Failed to hide profile via removeFriend:', error);

                },
                requestId // Pass requestId as an argument
            );
        } else {
            // Call addFriend if requestId is not present
            Service.addFriend(
                payload,
                () => {
                    setDialogVisible(false);
                    disableLoader();
                    navigate('/');
                },
                (error) => {
                    showConfirmationPopup(
                        'Error',
                        error.message || 'Failed to delete the account.',
                        'OK',
                        null,
                        () => { }
                    );
                    disableLoader();
                    console.error('Failed to hide profile via addFriend:', error);
                }
            );
        }
    };

    const fetchCanChatWithHelper = () => {
        if (!userId) return;

        // Reset previous state
        setDialogVisible(false);
        setChatErrorMessage('');

        // Assume hasUnlimitedChatAccess is fetched from sessionInfo, cookies, or API
        const hasUnlimitedChatAccess = sessionInfo.data?.["permissions"]['hasUnlimitedChatWithProviders'] ?? false; // Adjust based on actual source

        Service.getChat(
            (data) => {
                // Assuming the API returns a boolean or an object with a canChat property
                const chatEligibility = data.canChat ?? data;

                if (chatEligibility) {
                    // If chat is allowed, navigate to chat page
                    navigate(
                        clientType === '1'
                            ? `/parent-home/inAppChat?userId=${userId}`
                            : `/business-home/inAppChat?userId=${userId}`
                    );
                } else {
                    // If chat is not allowed, show dialog with message based on conditions
                    setDialogVisible(true);
                    let errorMessage = '';
                    let actionPath = '';

                    if (hasUnlimitedChatAccess) {

                        if (clientType === c.clientType.INDIVIDUAL) {
                            errorMessage = `You need an active job post to chat with ${helper?.firstName}.`;
                            actionPath = '/parent-home/post-job/1';
                        } else {
                            errorMessage = `You can chat with helpers who initiate a conversation and candidates who apply for your job.`;
                            actionPath = '/business-home/post-job/1';
                        }
                    } else {
                        errorMessage = `You need to subscribe and post a job to chat with ${helper?.firstName}.`;
                        actionPath = '/pricing'; // Adjust to your actual pricing page route
                    }

                    setChatErrorMessage(errorMessage);

                    // Optionally, store actionPath in state to use in the dialog's button
                    setDialogVisible(true);
                    // Update dialog button to navigate to the appropriate path
                    // This requires modifying the Dialog's Post Job button to use a dynamic path
                }
            },
            (error) => {
                console.error('Error fetching Can Chat With Helper data:', error);
                setDialogVisible(true);

                // Fallback error message based on conditions
                let errorMessage = '';
                if (hasUnlimitedChatAccess) {
                    if (clientType === c.clientType.INDIVIDUAL) {
                        errorMessage = `You need an active job post to chat with ${helper?.firstName}.`;
                    } else {
                        errorMessage = `You can chat with helpers who initiate a conversation and candidates who apply for your job.`;
                    }
                } else {
                    errorMessage = `You need to subscribe and post a job to chat with ${helper?.firstName}.`;
                }
                setChatErrorMessage(errorMessage);
            },
            userId
        );
    };

    const canChatWithHelper = () => {
        if (sessionInfo.data['profileCompleteness'] < 100) {
            showConfirmationPopup(
                helper?.publicName,
                `Your profile must be 100% complete before you can connect with ${helper?.publicName}`,
                'Complete',
                <RxUpdate style={{ fontSize: '20px' }} />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
        } else {
            navigate('');
        }
    };

    const handleFavClicked = () => {
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                helper?.publicName,
                `Your profile must be 100% complete before you can connect with ${helper?.publicName}`,
                'Complete',
                <RxUpdate style={{ fontSize: '20px' }} />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        showConfirmationPopup(
            helper?.publicName,
            `Are you sure you want to Favourite ${helper?.publicName}`,
            'Favourite',
            <RxUpdate />,
            () => {
                handleAddToFavorites();
            }
        );
        return;
    };

    const handleAddToFavorites = () => {

        setDialogVisible(false);
        enableLoader();
        let newStatus = helper?.friendStatus;
        if (helper?.friendStatus === c.friendStatus.NONE) {
            newStatus = c.friendStatus.PENDING;
        } else if (helper?.friendStatus === c.friendStatus.PENDING) {
            newStatus = c.friendStatus.APPROVED;
        }
        const payload = {
            friendId: helper?.id,
            invitationMessage: '',
            status: newStatus,
        };
        Service.addFriend(

            payload,
            () => {
                setDialogVisible(false);
                disableLoader();
                // navigate('');
                refresh();

            },
            (error) => {
                console.error('Failed to add friend:', error);
            }
        );
    };

    const renderVideoContent = () => {
        if (!helper?.profileIntroVideo) {
            return (
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingTop: '20px',
                    }}
                >
                    <p style={{ fontWeight: '700', color: '#585858' }}>No video uploaded</p>
                </div>
            );
        }

        const hasVideo =
            helper.profileIntroVideo.externalHashedId &&
            helper.profileIntroVideo.mediaReviewStatus === c.mediaReviewStatus.APPROVED;

        if (!hasVideo) {
            return null;
        }

        const mediaId = helper.profileIntroVideo.externalHashedId;

        return (
            <div className='mt-4'>
                <iframe
                    className='about-me-video-iframe w-full rounded-lg'
                    src={`//fast.wistia.net/embed/iframe/${mediaId}?playerColor=299C4A`}
                    width='207px'
                    height='238px'
                    style={{ borderRadius: '10px' }}
                    frameBorder='0'
                    scrolling='no'
                    allowFullScreen
                    name='wistia_embed'
                />
            </div>
        );
    };
    const renderPhotoContent = (isMobile?: boolean) => {
        // Assuming helper has a photos array or similar
        // This is a placeholder - you'll need to replace with actual photo logic
        return (
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                {helper?.imageSrc ? (
                    <div style={{ paddingTop: !isMobile && '20px' }}>
                        <img
                            src={helper.imageSrc}
                            alt={`Profile photo`}
                            className={!isMobile ? `${styles.photRender}` : `${styles.photRenderMobile}`}
                        />
                    </div>
                ) : (
                    <p style={{ fontWeight: '700', color: "#585858" }}>No photo uploaded</p>
                )}
            </div>
        );
    };
    const ReviewAndRatingHead = ({
        rating,
        ratingCount,
    }: {
        rating: number;
        ratingCount: number;
    }) => {
        const { isMobile } = useIsMobile()
        return (

            <div
                className='flex items-center gap-2'
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginLeft: !isMobile ? "2px" : "0px",
                }}
            >
                <img src={star} alt='star' style={{ width: !isMobile ? "18px" : "18px", height: !isMobile ? "18px" : "18px" }} />

                <p
                    style={{
                        fontSize: '14px',
                        fontWeight: '300',
                        color: '#585858',
                        margin: '2px',
                        marginLeft: '0px',
                    }}
                >{`${rating.toFixed(1)} Avg Rating`}</p>
                {!isMobile && (
                    <p
                        style={{
                            fontSize: '14px',
                            fontWeight: '700',
                            color: '#ffa500',
                            margin: '2px',
                            marginLeft: '0px',
                        }}
                    >{`(${ratingCount} ratings)`}</p>
                )}
            </div>

        );
    };
    const handleChatWithHelper = () => {
        if (sessionInfo.data['profileCompleteness'] < 100) {
            canChatWithHelper();
        } else {
            fetchCanChatWithHelper();
        }
    };

    return (
        <div
            style={{
                minWidth: !isMobile ? '311px' : '100%',
                width: width,
                maxHeight: '705px',
                backgroundColor: 'transparent',
                position: 'relative',
                marginBottom: isMobile && "20px",
                paddingInline: isMobile && "5px"
            }}
        >
            <Dialog
                visible={dialogVisible}
                onHide={() => setDialogVisible(false)}
                content={
                    <div className={styles.dialogContent}>
                        <IoClose
                            onClick={() => setDialogVisible(false)}
                            className={styles.CloseBtn}
                        />
                        <div>
                            <h1
                                style={{
                                    fontSize: '32px',
                                    fontWeight: '700',
                                    color: '#585858',
                                    margin: '0px',
                                }}
                            >
                                Chat
                            </h1>
                        </div>
                        <Divider />

                        <div>
                            <p
                                style={{
                                    fontSize: '20px',
                                    fontWeight: '500',
                                    color: '#585858',
                                }}
                            >
                                {chatErrorMessage}
                            </p>
                            <div style={{ display: 'flex', flexDirection: 'row', gap: '20px' }}>
                                <button
                                    className={styles.buttonOk}
                                    onClick={() => setDialogVisible(false)}
                                >
                                    Ok
                                </button>
                                <button
                                    className={styles.buttonPost}
                                    onClick={() => {
                                        if (clientType === '1') {
                                            navigate('/parent-home/job/post/job-type');
                                        } else if (clientType === '2') {
                                            navigate('/business-home/job/post/job-type');
                                        }
                                    }}
                                >
                                    Post Job
                                </button>
                            </div>
                        </div>
                    </div>
                }
            />

            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            {!hideText && clientType !== '0' && !isMobile && (
                <>
                    <div
                        className={`flex justify-content-center align-items-center ${helper?.friendStatus === 4 && "cursor-pointer"} ${styles.HeartButton}`}
                    >
                        {helper?.friendStatus === 4 ? (
                            <>
                                <FaRegHeart
                                    className='cursor-pointer'
                                    style={{ height: '20px', width: '20px' }}
                                    onClick={() => handleFavClicked()}
                                />
                            </>
                        ) : (
                            <FaRegHeart
                                style={{
                                    color: 'red',
                                    height: '20px',
                                    width: '20px',
                                    cursor: 'not-allowed',
                                }}
                            />
                        )}
                    </div>
                </>
            )}
            <div className='flex flex-row justify-content-between align-items-center px-2'>
                <div className='flex flex-row  align-items-center px-2 gap-1'>
                    {!hideText && clientType !== '0' && isMobile && (
                        <>
                            <div
                                className={`flex justify-content-center align-items-center ${helper?.friendStatus === 4 && "cursor-pointer"} ${styles.HeartButtonMobile}`}
                                onClick={() => handleFavClicked()}

                            >
                                {helper?.friendStatus === 4 ? (
                                    <>
                                        <FaRegHeart
                                            className='cursor-pointer'
                                            style={{ height: '20px', width: '20px' }}
                                        />
                                    </>
                                ) : (
                                    <FaRegHeart
                                        style={{
                                            color: 'red',
                                            height: '20px',
                                            width: '20px',
                                            cursor: 'not-allowed',
                                        }}
                                    />
                                )}
                            </div>
                        </>
                    )}
                    {clientType !== '0' && isMobile && (
                        <button className={styles.helperHide} onClick={handleHide}>
                            <img src={crossChat} alt='crossChat' width={18} height={18} />

                        </button>
                    )}

                    {isMobile && (
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                gap: '10px',
                                alignItems: 'center',
                                marginTop: '3px',
                            }}
                        >
                            <p style={{ color: "#585858" }} className='text-sm font-semibold '>
                                {helper && helper.firstName ? helper.firstName : null}
                            </p>
                            <p className={styles.InfoSpan}>
                                {helper && helper.ageInYears ? helper.ageInYears : null} yrs
                            </p>
                        </div>
                    )}
                </div>

                <div>
                </div>

                {isMobile && (
                    <button
                        className={styles.helperBtnMobile}
                        onClick={handleChatWithHelper}
                        disabled={canChat === false}
                    >
                        <img src={chatSecond} alt='chatSecond' width={18} height={17} />

                    </button>
                )}
            </div>
            {clientType !== '0' && (
                <div
                    className='w-full'
                    style={{
                        backgroundColor: '#ffffff',
                        borderRadius: '20px',

                        padding: '15px',
                        paddingTop: '8px',
                        paddingInline: isMobile && "0px",
                        boxShadow: !isMobile ? boxShadow : "",
                        display: isMobile && "flex",
                        flexDirection: isMobile ? "column" : "column",
                        alignItems: isMobile && "center",
                        paddingBottom: isMobile ? "0px" : "10px",

                    }}
                >
                    <div>
                        {contentType === 'video' && renderVideoContent()}
                        {contentType === 'photo' && renderPhotoContent(isMobile)}
                    </div>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            marginTop: '6px',
                        }}
                    >
                        <button
                            className={styles.videoBtn}
                            onClick={() => setContentType('video')}
                            style={{
                                backgroundColor: contentType === 'video' ? '#179D52' : '#F1F1F1',
                                color: contentType === 'video' ? '#FFFFFF' : '#585858',
                                fontWeight: contentType === 'video' ? 800 : 500,
                            }}
                        >
                            <img
                                src={contentType === 'video' ? video : videoBlack}
                                alt='Video'
                                width='15.88'
                                height='10.75'
                            />{' '}
                            Video
                        </button>
                        <button
                            className={styles.photoBtn}
                            onClick={() => setContentType('photo')}
                            style={{
                                backgroundColor: contentType === 'photo' ? '#179D52' : '#F1F1F1',
                                color: contentType === 'photo' ? '#FFFFFF' : '#585858',
                                fontWeight: contentType === 'photo' ? 800 : 500,
                            }}
                        >
                            Photo{' '}
                            <img
                                src={contentType === 'photo' ? photoBlack : photo}
                                alt='Image'
                                width='13.5'
                                height='13.5'
                            />
                        </button>
                    </div>
                    <div style={{ width: isMobile && "100%" }} className={styles.InfoDiv}>
                        <Divider />
                        {!isMobile && (
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    gap: '10px',
                                    alignItems: 'center',
                                    marginTop: '3px',
                                }}
                            >
                                <p className={styles.InfoName}>
                                    {helper && helper.firstName ? helper.firstName : null}
                                </p>
                                <p className={styles.InfoSpan}>
                                    {helper && helper.ageInYears ? helper.ageInYears : null} yrs
                                </p>
                            </div>
                        )}
                        {helper?.isSuperProvider && (
                            <button className={styles.superBtn}>Super Helper</button>
                        )}
                        <Divider />
                        {!isMobile ? (
                            <div
                                style={{
                                    display: isMobile ? 'grid' : 'flex', // Switch to grid on mobile, flex otherwise
                                    gridTemplateColumns: isMobile ? '1fr 1fr' : undefined, // 2 equal columns on mobile
                                    flexDirection: isMobile ? undefined : 'column', // Column layout for non-mobile
                                    gap: '3px',
                                    marginTop: !isMobile ? '10px' : '5px',
                                    marginBottom: !isMobile ? '10px' : '5px',
                                    width: isMobile && "100%",
                                    paddingInline: isMobile && "5px"
                                }}
                            >
                                <>
                                    <ReviewAndRatingHead
                                        rating={helper?.providerRatingsAvg ?? 0}
                                        ratingCount={helper?.providerRatingsCount ?? 0}
                                    />
                                </>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                        justifyContent: isMobile && "end"
                                    }}
                                >
                                    <img src={locationMy} alt='location' width={12.52} height={16} />
                                    {helper && helper.suburb ? helper.suburb : null} |{' '}
                                    {helper && helper.distanceInKiloMetersRounded
                                        ? helper.distanceInKiloMetersRounded
                                        : null}
                                    km away
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={chat} alt='chat' width={14.4} height={14.4} />
                                    {helper && helper.responseTime !== undefined
                                        ? utils.getResponseTimeText(helper.responseTime)
                                        : null}
                                    {helper && helper.responseTime !== undefined
                                        ? ' Response Rate'
                                        : null}
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                        justifyContent: isMobile && "end"
                                    }}
                                >
                                    <FaCheck width={13.5} height={13.5} />
                                    {helper && helper.providerJobsCompleted
                                        ? helper.providerJobsCompleted
                                        : 0}&nbsp;
                                    Jobs Completed
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={cancel} alt='chat' width={13.5} height={13.5} />
                                    {helper && helper.providerJobsCancelled
                                        ? helper.providerJobsCancelled
                                        : 0}&nbsp;
                                    Jobs Cancelled
                                </p>
                            </div>
                        ) : (
                            <div
                                style={{
                                    display: isMobile ? 'grid' : 'flex',
                                    gridTemplateColumns: isMobile ? '1fr 1fr' : undefined,
                                    flexDirection: isMobile ? undefined : 'column',
                                    gap: '3px',
                                    marginTop: !isMobile ? '10px' : '5px',
                                    marginBottom: !isMobile ? '10px' : '5px',
                                    width: isMobile && "100%",
                                    paddingInline: isMobile && "5px"
                                }}
                            >
                                <span
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',

                                    }}
                                >
                                    <img src={locationMy} alt='location' width={12.52} height={16} />
                                    {helper && helper.suburb ? helper.suburb : null} |{' '}
                                    {helper && helper.distanceInKiloMetersRounded
                                        ? helper.distanceInKiloMetersRounded
                                        : null}
                                    km
                                </span>


                                <span
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                        justifyContent: isMobile && "end"
                                    }}
                                >

                                    {helper && helper.providerJobsCompleted
                                        ? helper.providerJobsCompleted
                                        : 0}
                                    {" "}Jobs Completed
                                </span>
                                <>
                                    <ReviewAndRatingHead
                                        rating={helper?.providerRatingsAvg ?? 0}
                                        ratingCount={helper?.providerRatingsCount ?? 0}
                                    />
                                </>
                                <span
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                        justifyContent: isMobile && "end"
                                    }}
                                >

                                    {helper && helper.providerJobsCancelled
                                        ? helper.providerJobsCancelled
                                        : 0}
                                    {" "} Jobs Cancelled
                                </span>
                                <span
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: !isMobile ? '5px' : '0px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={chat} alt='chat' width={14.4} height={14.4} />
                                    {helper && helper.responseTime !== undefined
                                        ? utils.getResponseTimeText(helper.responseTime)
                                        : null}
                                    {helper && helper.responseTime !== undefined
                                        ? ' Response Rate'
                                        : null}
                                </span>
                            </div>
                        )}
                    </div>
                    {clientType !== '0' && (
                        <>
                            <Divider />

                            <ConfirmationPopupGreen confirmationProps={confirmationProps} />

                            {!isMobile && (
                                <button
                                    className={styles.helperBtn}
                                    onClick={handleChatWithHelper}
                                    disabled={canChat === false}
                                >
                                    <img src={chatSecond} alt='chatSecond' width={18} height={17} />
                                    Chat With {helper && helper.firstName ? helper.firstName : null}
                                </button>
                            )}
                        </>
                    )}
                    {!hideText && (
                        <>
                            <div
                                style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginTop: '20px',
                                }}
                            >
                                {clientType !== '0' && !isMobile && (
                                    <button className={styles.helperHide} onClick={handleHide}>
                                        <img src={crossChat} alt='crossChat' width={14} height={14} />
                                        Hide Helper
                                    </button>
                                )}
                            </div>
                        </>
                    )}
                </div>
            )}
            {clientType === '0' && (
                <div>
                    <div className='flex justify-content-center'>
                        <div
                            className='w-full h-full'
                            style={{
                                backgroundColor: '#ffffff',
                                borderRadius: '20px',
                                border: '20px',
                                padding: '15px',
                                paddingTop: '8px',
                                boxShadow: boxShadow,
                            }}
                        >
                            <div>
                                {contentType === 'video' && renderVideoContent()}
                                {contentType === 'photo' && renderPhotoContent()}
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    marginTop: '6px',
                                }}
                            >
                                <button
                                    className={styles.videoBtn}
                                    onClick={() => setContentType('video')}
                                    style={{
                                        backgroundColor: contentType === 'video' ? '#179D52' : '#F1F1F1',
                                        color: contentType === 'video' ? '#FFFFFF' : '#585858',
                                        fontWeight: contentType === 'video' ? 800 : 500,
                                    }}
                                >
                                    <img
                                        src={contentType === 'video' ? video : videoBlack}
                                        alt='Video'
                                        width='15.88'
                                        height='10.75'
                                    />{' '}
                                    Video
                                </button>
                                <button
                                    className={styles.photoBtn}
                                    onClick={() => setContentType('photo')}
                                    style={{
                                        backgroundColor: contentType === 'photo' ? '#179D52' : '#F1F1F1',
                                        color: contentType === 'photo' ? '#FFFFFF' : '#585858',
                                        fontWeight: contentType === 'photo' ? 800 : 500,
                                    }}
                                >
                                    Photo{' '}
                                    <img
                                        src={contentType === 'photo' ? photoBlack : photo}
                                        alt='Image'
                                        width='13.5'
                                        height='13.5'
                                    />
                                </button>
                            </div>
                        </div>
                        <div className={styles.InfoDiv}>
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    gap: '10px',
                                    alignItems: 'center',
                                    marginTop: '45px',
                                }}
                            >
                                <p className={styles.InfoName}>
                                    {helper && helper.firstName ? helper.firstName : null}
                                </p>
                                <p className={styles.InfoSpan}>
                                    {helper && helper.ageInYears ? helper.ageInYears : null} yrs
                                </p>
                            </div>
                            {helper?.isSuperProvider && (
                                <button className={styles.superBtn}>Super Helper</button>
                            )}
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '3px',
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <>
                                    <ReviewAndRatingHead
                                        rating={helper?.providerRatingsAvg ?? 0}
                                        ratingCount={helper?.providerRatingsCount ?? 0}
                                    />
                                </>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: '5px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={locationMy} alt='location' width={12.52} height={16} />
                                    {helper && helper.suburb ? helper.suburb : null} |{' '}
                                    {helper && helper.distanceInKiloMetersRounded
                                        ? helper.distanceInKiloMetersRounded
                                        : null}
                                    km away
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: '5px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={chat} alt='chat' width={14.4} height={14.4} />
                                    {helper && helper.responseTime !== undefined
                                        ? utils.getResponseTimeText(helper.responseTime)
                                        : null}
                                    {helper && helper.responseTime !== undefined
                                        ? ' Hour Response Rate'
                                        : null}
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: '5px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <FaCheck width={13.5} height={13.5} />
                                    {helper && helper.providerJobsCompleted
                                        ? helper.providerJobsCompleted
                                        : 0}&nbsp;
                                    Jobs Completed
                                </p>
                                <p
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        gap: '8px',
                                        margin: '5px',
                                        alignItems: 'center',
                                        color: '#585858',
                                        fontSize: '14px',
                                        fontWeight: '300',
                                    }}
                                >
                                    <img src={cancel} alt='chat' width={13.5} height={13.5} />
                                    {helper && helper.providerJobsCancelled
                                        ? helper.providerJobsCancelled
                                        : 0}&nbsp;
                                    Jobs Cancelled
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default SideCard;
