import { useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Divider } from "primereact/divider";
import { GoBack, Next } from "./Buttons";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import styles from "../../styles/type-and-years.module.css";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

function TypeAndYears() {
  const { isMobile } = useIsMobile();
  return isMobile ? <TypeAndYearsMobile /> : <TypeAndYearsWeb />;
}

export default TypeAndYears;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();

  const [tutorType, setTutorType] = useState<"in-home" | "online" | null>(
    payload.jobDeliveryMethod === "1" ? "in-home" : payload.jobDeliveryMethod === "2" ? "online" : null
  );
  const { isMobile } = useIsMobile();
  const [selectionMode, setSelectionMode] = useState<"all" | "specific" | null>(
    payload.jobSettings?.["schoolYears"]?.length === 0 ? "all" : payload.jobSettings?.["schoolYears"]?.length ? "specific" : null
  );

  const [selectedYears, setSelectedYears] = useState<number[]>(payload.jobSettings?.["schoolYears"] || []);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const highSchoolYears =
    sessionInfo.data?.["client"]["highSchoolYears"].map((year: any) => ({
      id: year.optionId,
      label: year.text,
    })) || [];
  const primarySchoolYears =
    sessionInfo.data["client"]["primarySchoolYears"].map((year: any) => ({
      id: year.optionId,
      label: year.text,
    })) || [];
  const [schoolYears] = useState(payload.jobType === 64 ? primarySchoolYears : highSchoolYears);
  // Handle year selection logic
  const handleYearSelection = (yearId: number) => {
    if (selectedYears.includes(yearId)) {
      setSelectedYears(selectedYears.filter((id) => id !== yearId));
    } else if (selectedYears.length < 2) {
      setSelectedYears([...selectedYears, yearId]);
    }
  };

  const buttonDisabled = () => {
    if (selectionMode === "all") {
      return false;
    }
    if (!tutorType || !selectionMode || selectedYears.length < 1) {
      return true;
    }
    return false;
  };

  const RadioButton = ({ selected, number }: { selected: boolean; number?: number }) => (
    <div
      className="flex justify-center align-items-center"
      style={{
        height: "18px",
        width: "18px",
        minHeight: "18px",
        minWidth: "18px",
        borderRadius: "50%",
        padding: "1px",
        border: `1px solid ${selected ? "#179d52" : "#DFDFDF"}`,
        backgroundColor: number ? "#179D52" : "transparent",
      }}
    >
      {selected && (
        <div
          style={{
            height: "100%",
            width: "100%",
            borderRadius: "50%",
            backgroundColor: "#179D52",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <span className="text-white text-xs font-bold p-0 m-0">{number}</span>
        </div>
      )}
    </div>
  );
  const handleNext = () => {
    const jobDeliveryMethod = tutorType === "in-home" ? 1 : tutorType === "online" ? 2 : null;

    const updatedPayload = {
      ...payload,
      jobDeliveryMethod: jobDeliveryMethod?.toString(),
      jobSettings: {
        ...payload.jobSettings,
        schoolYears: selectedYears.length > 0 ? selectedYears : [], // Use selectedYears for up to 2 IDs
      },
      applicantFilters:
        payload.applicantFilters === null
          ? [
              {
                field: "jobDeliveryMethod",
                operator: "eq",
                value: jobDeliveryMethod,
              },

              ...(selectedYears.length > 0
                ? [
                    {
                      field: "schoolYears",
                      operator: "eq",
                      value: selectedYears,
                    },
                  ]
                : []),
            ]
          : [
              ...payload.applicantFilters.filter(
                (af) => af.field !== "jobDeliveryMethod" && (selectedYears.length > 0 || af.field !== "schoolYears") // Exclude schoolYears only if not selected
              ),
              {
                field: "jobDeliveryMethod",
                operator: "eq",
                value: jobDeliveryMethod,
              },
              ...(selectedYears.length > 0
                ? [
                    {
                      field: "schoolYears",
                      operator: "eq",
                      value: selectedYears,
                    },
                  ]
                : []),
            ],
    };

    setpayload(updatedPayload);
    next("tutoring-subjects");
  };
  // write handleGoback function t pass payload
  const handleGoBack = () => {
    const jobDeliveryMethod = tutorType === "in-home" ? 1 : tutorType === "online" ? 2 : null;

    setpayload({
      ...payload,
      jobDeliveryMethod: jobDeliveryMethod?.toString(),
      jobSettings: {
        ...payload.jobSettings,
        schoolYears: selectedYears.length > 0 ? selectedYears : [],
      },
    });

    prev("job-posting");
  };

  return {
    payload,
    next,
    prev,
    setpayload,
    schoolYears,
    handleYearSelection,
    buttonDisabled,
    RadioButton,
    tutorType,
    setTutorType,
    isMobile,
    selectionMode,
    setSelectionMode,
    sessionInfo,
    highSchoolYears,
    primarySchoolYears,
    setSelectedYears,
    selectedYears,
    handleGoBack,
    handleNext,
  };
};

const TypeAndYearsWeb = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    schoolYears,
    handleYearSelection,
    buttonDisabled,
    RadioButton,
    tutorType,
    setTutorType,
    setSelectedYears,
    isMobile,
    selectionMode,
    setSelectionMode,
    sessionInfo,
    highSchoolYears,
    selectedYears,
    handleGoBack,
    handleNext,
    primarySchoolYears,
  } = useJobTypeHook();
  return (
    <div className=" h-full select-none  flex flex-column justify-content-center align-content-center relative">
      <div className="flex flex-column p-7" style={{}}>
        {/* Tutoring Type Selection */}
        <div>
          <h2 className={`${styles.tutoringHeader}`}>Select type of Tutoring</h2>
          <div className="flex flex-wrap gap-4 mb-3">
            <button
              onClick={() => setTutorType("in-home")}
              className={`px-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
              ${tutorType === "in-home" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
              style={{ width: "auto", height: "51px" }}
            >
              <RadioButton selected={tutorType === "in-home"} />
              <span>In-home Tutoring</span>
            </button>
            <button
              onClick={() => setTutorType("online")}
              className={`px-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
              ${tutorType === "online" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
              style={{ width: "auto", height: "51px" }}
            >
              <RadioButton selected={tutorType === "online"} />
              <span>Online Tutoring</span>
            </button>
          </div>
          {tutorType === "online" && (
            <span
              className="flex flex-wrap w-6 mb-2"
              style={{
                fontSize: "16px",
                fontWeight: "400",
                color: "#585858",
                lineHeight: "24px",
                marginTop: "-10px",
              }}
            >
              Juggle Street does not endorse any video or meeting platform. If you have a preference please include in Job Description, ie. prefer
              lessons to be delivered via Zoom.
            </span>
          )}
        </div>

        {/* Show School Year Selection only if a tutor type is selected */}
        {tutorType && (
          <div className="">
            <h2 className={styles.tutoringHeader}>Select School Year</h2>
            <div className="flex flex-wrap gap-4" style={{ marginTop: "-17px" }}>
              {/* All and Specific Year Buttons */}
              <button
                onClick={() => {
                  setSelectionMode("all");
                  setSelectedYears([]);
                }}
                className={`px-3 py-2 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                ${selectionMode === "all" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                style={{ width: "auto", height: "41px" }}
              >
                <RadioButton selected={selectionMode === "all"} />
                <span>All</span>
              </button>

              <div className="relative flex items-center">
                <button
                  onClick={() => setSelectionMode("specific")}
                  className={`px-4 py-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                  ${selectionMode === "specific" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                  style={{
                    width: "auto",
                    height: "41px",
                    position: "relative",
                  }}
                >
                  <RadioButton selected={selectionMode === "specific"} />
                  <span>Specific year</span>
                </button>
                {selectionMode === "specific" && <div className={`${styles.verticalLine}`}></div>}
                {/* Show the message beside the "Specific year" button */}
                {selectionMode === "specific" && <span className={`${styles.inlineMessage} pl-2`}>Up to 2 school years can be selected</span>}
              </div>

              {/* Specific Year Selection */}
              {selectionMode === "specific" && (
                <div className="flex flex-wrap gap-3 mt-2">
                  {/* Vertical line */}
                  <div className="flex flex-wrap gap-4 relative " style={{ left: "12%" }}>
                    {schoolYears.map((year) => {
                      const isSelected = selectedYears.includes(year.id);
                      const selectionIndex = selectedYears.indexOf(year.id);

                      return (
                        <button
                          key={year.id}
                          onClick={() => handleYearSelection(year.id)}
                          className={`px-3 py-2 rounded-lg border-2 border-round-xl flex gap-2 bg-white cursor-pointer
                          ${isSelected ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                          style={{ width: "auto", height: "41px" }}
                        >
                          <RadioButton selected={isSelected} number={isSelected ? selectionIndex + 1 : undefined} />
                          <span>{year.label}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      <div
        className="mt-auto sticky bottom-0 pz"
        style={{
          width: "100%",
        }}
      >
        <Divider />
        <div className="flex justify-content-between flex-wrap py-3 pl-6" style={{ width: "80%" }}>
          <GoBack onClick={handleGoBack} />
          <Next disabled={buttonDisabled()} onClick={handleNext} />
        </div>
      </div>
    </div>
  );
};
const TypeAndYearsMobile = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    schoolYears,
    handleYearSelection,
    buttonDisabled,
    RadioButton,
    tutorType,
    setTutorType,
    isMobile,
    selectionMode,
    setSelectionMode,
    sessionInfo,
    highSchoolYears,
    primarySchoolYears,
    setSelectedYears,
    selectedYears,
    handleGoBack,
    handleNext,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        position: "relative",
      }}
    >
      <div
        className={styles.subjectMain}
        style={{
          padding: "25px",
        }}
      >
        <div className="flex flex-column w-full" style={{}}>
          {/* Tutoring Type Selection */}
          <div>
            <h2 className={`${styles.tutoringHeaderMobile}`}>Select type of Tutoring</h2>
            <div className="flex flex-wrap gap-4 mb-3 mt-3">
              <button
                onClick={() => setTutorType("in-home")}
                className={`px-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                ${tutorType === "in-home" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                style={{ width: "auto", height: "51px" }}
              >
                <RadioButton selected={tutorType === "in-home"} />
                <span>In-home Tutoring</span>
              </button>
              <button
                onClick={() => setTutorType("online")}
                className={`px-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                ${tutorType === "online" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                style={{ width: "auto", height: "51px" }}
              >
                <RadioButton selected={tutorType === "online"} />
                <span>Online Tutoring</span>
              </button>
            </div>
            {tutorType === "online" && (
              <span
                className="flex flex-wrap mb-2"
                style={{
                  fontSize: "14px",
                  fontWeight: "400",
                  color: "#585858",
                  lineHeight: "24px",
                  marginTop: "-10px",
                }}
              >
                Juggle Street does not endorse any video or meeting platform. If you have a preference please include in Job Description, ie. prefer
                lessons to be delivered via Zoom.
              </span>
            )}
          </div>

          {/* Show School Year Selection only if a tutor type is selected */}
          {tutorType && (
            <div className="">
              <h2 className={styles.tutoringHeaderMobileSec}>Select School Year</h2>
              <div className="flex flex-wrap gap-4" style={{ marginTop: "0px" }}>
                {/* All and Specific Year Buttons */}
                <button
                  onClick={() => {
                    setSelectionMode("all");
                    setSelectedYears([]);
                  }}
                  className={`px-3 py-2 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                  ${selectionMode === "all" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                  style={{ width: "auto", height: "41px" }}
                >
                  <RadioButton selected={selectionMode === "all"} />
                  <span>All</span>
                </button>

                <div className="relative flex items-center">
                  <button
                    onClick={() => setSelectionMode("specific")}
                    className={`px-4 py-3 rounded-lg border-2 border-round-xl flex align-items-center gap-2 bg-white cursor-pointer
                    ${selectionMode === "specific" ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                    style={{
                      width: "auto",
                      height: "41px",
                      position: "relative",
                    }}
                  >
                    <RadioButton selected={selectionMode === "specific"} />
                    <span>Specific year</span>
                  </button>
                  {selectionMode === "specific" && <div className={`${styles.verticalLine}`}></div>}
                  {/* Show the message beside the "Specific year" button */}
                  {selectionMode === "specific" && <span className={`${styles.inlineMessage} pl-2`}>Up to 2 school years can be selected</span>}
                </div>

                {/* Specific Year Selection */}
                {selectionMode === "specific" && (
                  <div className="flex flex-wrap gap-3 mt-2">
                    {/* Vertical line */}
                    <div className="flex flex-wrap gap-4 relative " style={{ left: "12%" }}>
                      {schoolYears.map((year) => {
                        const isSelected = selectedYears.includes(year.id);
                        const selectionIndex = selectedYears.indexOf(year.id);

                        return (
                          <button
                            key={year.id}
                            onClick={() => handleYearSelection(year.id)}
                            className={`px-3 py-2 rounded-lg border-2 border-round-xl flex gap-2 bg-white cursor-pointer
                            ${isSelected ? `${styles.tutoringSelectedText}` : `${styles.tutoringUnselectedText}`}`}
                            style={{ width: "auto", height: "41px" }}
                          >
                            <RadioButton selected={isSelected} number={isSelected ? selectionIndex + 1 : undefined} />
                            <span>{year.label}</span>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* <div className="mt-auto">
        <Divider className="mb-4" />

        <div className="flex justify-content-between flex-wrap pb-5">
          <GoBack onClick={handleGoBack} />
          <Next disabled={buttonDisabled()} onClick={handleNext} />
        </div>
      </div> */}
      </div>

      <BackButtonPortal id="back-button-portal">
        <div onClick={handleGoBack}>
          <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
        </div>
      </BackButtonPortal>

      <CustomFooterButton label="Next" onClick={handleNext} isDisabled={buttonDisabled()} />
    </div>
  );
};
