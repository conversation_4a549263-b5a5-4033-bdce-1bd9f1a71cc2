.customGradient {
  position: relative;
  height: 100vh;
  width: 100vw;
  background-image: url("/src/assets/images/post-job-background.jpg");
  overflow: hidden;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.customGradient::before {
  content: "";
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 50%;
  background: rgba(255, 165, 0, 0.2);
}

.customGradient::after {
  content: "";
  position: absolute;
  bottom: 0%;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("/src/assets/images/post-job-background.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  clip-path: ellipse(90% 75% at 50% 100%);
}

.popUpHolder {
  width: 85vw;
  max-width: 1241px;
  height: 90vh;
  max-height: 893px;
  background-color: transparent;
  z-index: 1;
}

.popUp {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 25px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.stepComponent li::before {
  border-top: none !important;
  height: 4px !important;
  background-color: #f5f3f3;
}
.stepComponent li:first-child::before {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.stepComponent li:last-child:before {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.customGradientMobile {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.popUpHolderMobile {
  width: 100%;
  /* max-width: 1241px; */
  height: 100%;
  /* max-height: 893px; */
  background-color: transparent;
  z-index: 1;
}
.popUpMobile {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #179D52;
}
