/* AboutMe.module.css */

.aboutMeContainer {
  overflow: auto;
 padding: 20px;
}

.aboutMeHeader {
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  color: #585858;
  margin-top: 0px;
}

.contentContainer {
  display: flex; /* Flex layout for the two sections */
  flex-direction: column; /* Stack sections on small screens */
}

.aboutMeSection {
  flex: 1;
  margin-bottom: 20px;
  padding: 10px;
  padding-bottom: 0px;
}
.inputbtn {
  display: flex;
  /* flex-direction: row; */
  /* align-items: baseline; */
  gap: 20px;
}
.changeNumber {
  width: 156px !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  line-height: 21px !important;
  text-align: center !important;
  /* width: 156px; */
  height: 39px !important;
  margin: 0 !important;
}
.changeNumber:disabled {
  background-color: #F1f1f1 !important;
  cursor: not-allowed !important;
  font-size: 14px !important;
  font-weight: 700 !important;
}
.changeEmail {
  width: 156px !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  line-height: 21px !important;
  text-align: center !important;
  height: 39px !important;
  margin: 0 !important;
  background-color: #FFA500; 
  cursor: pointer;
}

.changeEmail:disabled {
  background-color: #F1f1f1 !important;
  cursor: not-allowed !important;
  font-size: 14px !important;
  font-weight: 700 !important;
}
.aboutMeSectionsecond {
  flex: 1; /* Allow sections to grow equally */
  margin-bottom: 20px; /* Space between sections */
  padding: 10px; /* Inner padding for each section */
}
.aboutMegender {
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
  color: #585858;
  margin-bottom: 0px;
}
.instruct {
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  color: #585858;
}
.successMessagegender {
  width: 214px;
  height: 38px;
  background-color: #37a950;
  padding: 10px;
  border-radius: 10px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 800;
  line-height: 18px;
}
.genderdivider {
  margin-top: 16px !important;
  width: 100%;
}
.numberdialogContent {
  display: flex;
  flex-direction: column;
  padding: 40px;
  /* margin-right: 20px; */
}

.headernumber {
  margin: 0px;
  font-size: 32px;
  font-weight: 700;
  margin-right: 10px;
  color: #585858;
}

.dialogDescription {
  max-width: 462px;
  width: 100%;
  font-weight: 400;
  font-size: 16px;
  color: #585858;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 15px; /* Space between input fields */
}

.inputField {
  display: flex;
  flex-direction: column;
}

.inputField label {
  font-size: 1rem;
  margin-bottom: 5px;
}

.inputField input {
  padding: 10px;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  width: 100%;
}

@media (min-width: 768px) {
  .inputGroup {
    flex-direction: row;
    justify-content: space-between;
  }

  .inputField {
    width: 48%;
  }
}

@media (max-width: 767px) {
  .inputGroup {
    flex-direction: column;
  }

  .inputField {
    width: 100%;
  }
}
.otpinput {
  border: none !important;
  border-radius: 0 !important;
  width: 36px !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  padding: 0px !important;
  text-align: center !important;

  border-color: #585858 !important;
  border-bottom: 2px solid #585858 !important;
  background-color: transparent !important;
  box-shadow: none !important;
}
.otpinput:enabled:focus {
  border: none !important;
  border-radius: 0 !important;
  width: 36px !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  padding: 0px !important;
  text-align: center !important;

  border-color: #585858 !important;
  border-bottom: 2px solid #585858 !important;
  background-color: transparent !important;
  box-shadow: none !important;
}
.resendLink {
  margin-top: 38px;
  color: #1f9eab;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.codebuttonContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}
.disabledResendLink {
  color: grey; /* Grey color for disabled state */
  cursor: not-allowed; /* Indicate that it's not clickable */
}
.successMobile {
  max-width: 516px;
  width: 100%;
  background-color: #37a950;
  padding: 10px;
  border-radius: 10px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 800;
  line-height: 18px;
  margin-top: 20px;
}
.steptext{
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #585858;
}
.verificationMessage {
    font-weight: 400;
    width: 307px;
    font-size: 12px; 
    color: #585858;
    padding-top: 0.5rem; 
}

.headernumberMobile {
  margin: 0px;
  font-size: 20px;
  font-weight: 700;
  color: #fff;
}
.numberdialogContentMobile {
  display: flex;
  flex-direction: column;
}
.dialogDescriptionMobile {
  width: 100%;
  font-weight: 400;
  font-size: 14px;
  color: #585858;
  padding-inline: 20px;
  margin: 0px;
  margin-top: 100px;
}
.numberdialogRightMobile{
  padding-inline: 20px;
}
@media (max-width: 768px) {
    .verificationMessage {
        width: 90%; /* Adjust width for tablets */
        font-size: 11px; /* Slightly reduce font size */
        text-align: center; /* Center the text for better readability */
    }
}

/* For screens 480px and below (small mobile devices) */
@media (max-width: 480px) {
    .verificationMessage {
        width: 100%; /* Full width on small screens */
        font-size: 10px; /* Further reduce font size */
    }
    
}
