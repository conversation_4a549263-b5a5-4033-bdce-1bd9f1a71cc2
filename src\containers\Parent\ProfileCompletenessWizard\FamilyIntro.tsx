import React, { useState } from "react";
import styles from "../styles/family-intro.module.css";
import { ProgressBar } from "primereact/progressbar";
import { useDispatch, useSelector } from "react-redux";
import { InputTextarea } from "primereact/inputtextarea";
import { AppDispatch, RootState } from "../../../store";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import CustomButton from "../../../commonComponents/CustomButton";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import useLoader from "../../../hooks/LoaderHook";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../../Common/HorizontalNavigationMobile";

const FamilyIntro: React.FC = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [inputValue, setInputValue] = useState(
    sessionInfo.data["aboutMe"] || ""
  );
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();
  const { isMobile } = useIsMobile();
  const applicationSate = useSelector<RootState>((state) => state.applicationState.profileActivationCurrentStep);
  const handleprev = () => {
    dispatch(decrementProfileActivationStep());
  };
  const handleInputChange = (e) => {
    const value = e.target.value;

    const countWords = (text) => {
      return text
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
    };

    const totalWords = countWords(value);

    if (totalWords <= 120) {
      setInputValue(value.trim().length > 0 ? value : "");
    }
  };

  return (
    <div
      className={
        !isMobile
          ? `${styles.familycontainer}`
          : `${styles.familycontainerMobile}`
      }
    >
      <header className={styles.familyheader}>
        {!isMobile ? (
          <h1 className={styles.title}>Family Introduction</h1>
        ) : (
          <HorizontalNavigation
            title="Family Introduction"
            onBackClick={handleprev}
          />
        )}
        <ProgressBar
          value={
            sessionInfo.loading ? 100 : sessionInfo.data["profileCompleteness"]
          }
          className={styles.familyprogressbar}
        />
        <p
          style={{
            fontFamily: "Poppins",
            fontWeight: 500,
            fontSize: "14px",
            color: "#585858",
          }}
        >
          Your profile is{" "}
          <span
            style={{
              color: "#179D52",
              fontSize: "18px",
              fontWeight: "700",
            }}
          >
            {" "}
            {sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]}
            % complete.
          </span>
        </p>
      </header>
      <div className={styles.familycontent}>
        <p>Family Introduction</p>
        <p className={styles.familyinstruction}>
          Please describe your family, but do not include any info that will
          need keeping up-to-date, such as the age of your children.
        </p>
        <div style={{ minHeight: "100px", overflow: "auto" }}>
          <InputTextarea
            autoResize
            value={inputValue}
            required
            onChange={handleInputChange}
            rows={3}
            cols={30}
            className={styles.inputTextareafamily}
            placeholder="Example - Hi there, we are a busy family of four. We are looking for local babysitters to help us, as my partner and I both often travel for work."
          />
        </div>
        <div>
          <p className={styles.familyline}>
            Do NOT include job details here, you do this when you post a Job.
          </p>
        </div>{" "}
      </div>
      {!isMobile ? (
        <footer className={styles.footerContainer}>
          {applicationSate !== 1 &&
            <CustomButton
              label={
                <>
                  <i
                    className="pi pi-angle-left"
                    style={{ marginRight: "8px" }}
                  ></i>
                  Previous
                </>
              }
              onClick={handleprev}
              style={{
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontSize: "14px",
                fontWeight: "500",
              }}
            />
          }

          <div style={{ flexGrow: 1 }} />
          <CustomButton
            className={styles.hoverClass}
            data-skip={inputValue ? "false" : "true"}
            onClick={() => {
              if (inputValue.length > 0) {
                const payload = {
                  ...(sessionInfo.data as object),
                  aboutMe: inputValue,
                };
                enableLoader();
                dispatch(updateUser({ payload: payload })).finally(() => {
                  disableLoader();
                  // dispatch(incrementProfileActivationStep());
                });
              } else {
                dispatch(incrementProfileActivationStep());
              }
            }}
            label={
              <>
                {inputValue ? "Next" : "Skip"}
                <i
                  className={`pi pi-angle-${inputValue ? "right" : "right"}`}
                  style={{ marginLeft: "8px" }}
                ></i>
              </>
            }
            style={
              inputValue
                ? {
                  backgroundColor: "#FFA500",
                  color: "#fff",
                  width: "156px",
                  height: "39px",
                  fontWeight: "800",
                  fontSize: "14px",
                  borderRadius: "8px",
                  border: "2px solid transparent",
                  boxShadow: "0px 4px 12px #00000",
                  transition:
                    "background-color 0.3s ease, box-shadow 0.3s ease",
                }
                : {
                  backgroundColor: "transparent",
                  color: "#585858",
                  width: "156px",
                  height: "39px",
                  fontWeight: "400",
                  fontSize: "14px",
                  borderRadius: "10px",
                  border: "1px solid #F0F4F7",
                }
            }
          />
        </footer>
      ) : (
        <footer className={styles.footerContainerMobile}>
          <CustomButton
            label={
              <>
                <i
                  className="pi pi-angle-left"
                  style={{ marginRight: "8px" }}
                ></i>
                Previous
              </>
            }
            onClick={handleprev}
            style={{
              backgroundColor: "transparent",
              color: "#585858",
              width: "156px",
              height: "39px",
              fontSize: "14px",
              fontWeight: "500",
              margin: "5px"
            }}
          />
          <div style={{ flexGrow: 1 }} />
          <CustomButton
            className={styles.hoverClass}
            data-skip={inputValue ? "false" : "true"}
            onClick={() => {
              if (inputValue.length > 0) {
                const payload = {
                  ...(sessionInfo.data as object),
                  aboutMe: inputValue,
                };
                enableLoader();
                dispatch(updateUser({ payload: payload })).finally(() => {
                  disableLoader();
                  dispatch(incrementProfileActivationStep());
                });
              } else {
                dispatch(incrementProfileActivationStep());
              }
            }}
            label={
              <>
                {inputValue ? "Next" : "Skip"}
                <i
                  className={`pi pi-angle-${inputValue ? "right" : "right"}`}
                  style={{ marginLeft: "8px" }}
                ></i>
              </>
            }
            style={
              inputValue
                ? {
                  backgroundColor: "#FFA500",
                  color: "#fff",
                  width: "156px",
                  height: "39px",
                  fontWeight: "800",
                  fontSize: "14px",
                  borderRadius: "8px",
                  border: "2px solid transparent",
                  boxShadow: "0px 4px 12px #00000",
                  margin: "5px",
                  transition:
                    "background-color 0.3s ease, box-shadow 0.3s ease",
                }
                : {
                  backgroundColor: "transparent",
                  color: "#585858",
                  width: "156px",
                  height: "39px",
                  fontWeight: "400",
                  fontSize: "14px",
                  borderRadius: "10px",
                  border: "1px solid #F0F4F7",
                  margin: "5px",
                }
            }
          />
        </footer>
      )}
    </div>
  );
};

export default FamilyIntro;
