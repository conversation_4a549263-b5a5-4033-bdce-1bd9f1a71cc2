import { create<PERSON>ontext, PropsWith<PERSON>hildren, useContext, useEffect, useRef, useState } from "react";

type Filters = {
  neighbourhood: number;
  ageGroups: number[];
  age: number[];
  jobTypes: number[];
  experience: number;
  ratings: number[];
  activity: number;
  distance: number;
  driving: number[];
  otherSkills: number[];
  tutoringCategory: number[];
  auPairCategory: number;
  nationality: number;
  jobDeliveryMethod: number;
  jobSubTypes: number[];
  year12GraduationYear: number;
  responseTime: number;
  name: string;
  address: number[];
  hourlyRate: number;
};

export type PartialFilters = Partial<Filters>;

function convertFiltersToSortedArray(filters: Partial<Filters>) {
  const filterOrder = Object.keys({} as Filters) as (keyof Filters)[];
  const baseEntries = Object.entries(filters);

  const insertIndex = baseEntries.findIndex(([key]) => key === "otherSkills");

  const extraOtherSkills: [string, any][] = [...Array(5)].map(() => ["otherSkills", []]);

  let allEntries: [string, any][];
  if (insertIndex !== -1) {
    allEntries = [...baseEntries.slice(0, insertIndex + 1), ...extraOtherSkills, ...baseEntries.slice(insertIndex + 1)];
  } else {
    allEntries = [...baseEntries];
    const insertAt = filterOrder.indexOf("otherSkills");
    allEntries.splice(insertAt, 0, ...extraOtherSkills);
  }

  // Replace jobTypes 12 with [4, 8]
  const jobTypeIndex = allEntries.findIndex(([key]) => key === "jobTypes");
  if (jobTypeIndex !== -1) {
    const jobTypeValue = allEntries[jobTypeIndex][1];
    if (Array.isArray(jobTypeValue) && jobTypeValue.includes(12)) {
      allEntries[jobTypeIndex][1] = [4, 8];
    }
  }

  return allEntries
    .sort(([a], [b]) => filterOrder.indexOf(a as keyof Filters) - filterOrder.indexOf(b as keyof Filters))
    .map(([key, value]) => ({
      field: key as keyof Filters,
      operator: "eq",
      value,
    }));
}

export function convertArrayToFilters(array: { field: string; value: any }[]): PartialFilters {
  return array.reduce((acc, { field, value }) => {
    acc[field] = value;
    return acc;
  }, {} as PartialFilters);
}

type FilterContextType = {
  filters: PartialFilters;
  setFilters: React.Dispatch<React.SetStateAction<PartialFilters>>;
};

const FilterContext = createContext<FilterContextType>({
  filters: {},
  setFilters: () => {},
});

export const FilterProvider = ({ children }: PropsWithChildren) => {
  const [filters, setFilters] = useState<PartialFilters>({});
  return (
    <FilterContext.Provider
      value={{
        filters: filters,
        setFilters: setFilters,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
};

export const useFilterContext = ({
  initialFilters,
  overrideWith,
  onUpdate,
}: {
  initialFilters?: PartialFilters;
  overrideWith?: PartialFilters;
  onUpdate?: (filters: ReturnType<typeof convertFiltersToSortedArray>, actionType: "Intial" | "Update" | "LoadMore" | "Reset") => void;
}) => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error("useFilterContext must be used within a FilterProvider");
  }

  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const debouncedOnUpdate = (filters: ReturnType<typeof convertFiltersToSortedArray>, actionType: "Intial" | "Update" | "LoadMore" | "Reset") => {
    if (!onUpdate) return;
    if (debounceTimer.current) clearTimeout(debounceTimer.current);
    debounceTimer.current = setTimeout(() => {
      onUpdate(filters, actionType);
    }, 300); // adjust debounce delay as needed
  };

  useEffect(() => {
    if (Object.keys(context.filters).length === 0) {
      const combined = {
        ...initialFilters,
        ...(overrideWith ?? {}),
      };
      context.setFilters(combined);
      debouncedOnUpdate(convertFiltersToSortedArray(combined), "Intial");
    }
  }, []);

  const getFilters = (): ReturnType<typeof convertFiltersToSortedArray> => {
    const combined = {
      ...context.filters,
      ...(overrideWith ?? {}),
    };
    return convertFiltersToSortedArray(combined);
  };

  const updateFilters = (newFilters: PartialFilters) => {
    context.setFilters((prevFilters) => {
      const updatedFilters = {
        ...prevFilters,
        ...newFilters,
        ...(overrideWith ?? {}),
      };
      debouncedOnUpdate(convertFiltersToSortedArray(updatedFilters), "Update");
      return updatedFilters;
    });
  };

  const resetFilters = () => {
    const reset = {
      ...(initialFilters ?? {}),
      ...(overrideWith ?? {}),
    };
    context.setFilters(reset);
    debouncedOnUpdate(convertFiltersToSortedArray(reset), "Reset");
  };

  const update = () => {
    debouncedOnUpdate(getFilters(), "LoadMore");
  };

  const getInitialFilters = () => {
    const combined = {
      ...initialFilters,
      ...(overrideWith ?? {}),
    };
    return convertFiltersToSortedArray(combined);
  };

  return { getFilters, updateFilters, resetFilters, update, getInitialFilters };
};
