import { useDispatch } from 'react-redux';
import { AppDispatch } from '../store';
import { updateLoaderEnabled } from '../store/slices/applicationSlice';

function useLoader() {
    const dispatch = useDispatch<AppDispatch>();
    function enableLoader() {
        dispatch(updateLoaderEnabled(true));
    }
    function disableLoader() {
        dispatch(updateLoaderEnabled(false));
    }
    return { enableLoader, disableLoader };
}

export default useLoader;
