/* Making Payment Component Styles */
.container {
  display: flex;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
  background: var(--Juggle-street-background-colour, #F0F4F7);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  height: 100vh;
}

.header {
  height: 60px;
  background: linear-gradient(90deg, #FFA500 0%, #37A950 100%);
  width: 100%;
}

.content {
  padding: 40px;
  text-align: center;
  max-width: 450px;
  width: 100%;
}

/* Bouncing Logo Animation */
.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bouncingLogo {
  width: 200px;
  height: 150px;
  object-fit: contain;
  animation: bounce 1.5s ease-in-out infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-20px);
  }

  60% {
    transform: translateY(-10px);
  }
}

/* Text Styles */
.textContainer {
  margin-bottom: 50px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #585858;
  margin: 0;
}

.subtitle {
  font-size: 16px;
  color: #585858;
  margin: 0;
  line-height: 1.5;
  font-weight: 300;
}

/* Responsive Design */
/* @media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .content {
    padding: 30px 25px;
    border-radius: 15px;
  }
  
  .bouncingLogo {
    width: 70px;
    height: 70px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .subtitle {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 25px 20px;
  }
  
  .bouncingLogo {
    width: 60px;
    height: 60px;
  }
  
  .title {
    font-size: 22px;
  }
  
  .subtitle {
    font-size: 14px;
  }
} */