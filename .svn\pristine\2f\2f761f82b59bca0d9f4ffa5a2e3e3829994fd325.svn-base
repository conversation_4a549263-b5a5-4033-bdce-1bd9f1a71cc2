.codeinputField {
    border-radius: 10px;
    background-color: rgba(240, 244, 247, 1);
    width:362px;
    height: 56px;
    
  }
  .codeErrorTextLabel {
    height: 24px;
    width: 208px;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    margin-left: 14px;
}
.instructionText {
    margin-top: -10px;
    color: rgba(88, 88, 88, 1);
    text-align: center;
    font: 500 14px/21px 'Poppins', '-apple-system', 'Roboto', Helvetica, sans-serif;
  }
  .emailErrorLabel {
    height: 24px;
    width: 208px;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    margin-left: 14px;
  
  }
  .backToLogin {
    color: rgba(88, 88, 88, 1);
    text-align: center;
    margin: 28px 0 -59px;
    font: 500 14px 'Poppins', sans-serif;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
  .formsucess {
    margin-top: 15px;       /* Adds space between the error and the "Back to log in" link */
    color: #FF6359;          /* Error text color */
    font-size: 16px;        /* Font size for the error */
    font-weight: bold;      /* Makes the error text bold */
    text-align: center;     /* Center the error message */
}

  @media (max-width: 600px) {
    .codeinputField,
    .instructionText,
    .imageTextContainer {
        width: 100%; /* Adjust width to fit the screen */
    }

    .codeinputField {
        height: auto; /* Adjust height if necessary */
    }

    .codeErrorTextLabel,
    .emailErrorLabel {
        width: auto; /* Adjust width to fit the screen */
        margin-left: 0; /* Adjust margin for smaller screens */
    }

    .instructionText {
        margin-top: 0; /* Adjust margin for smaller screens */
    }

    .imageTextContainer {
        margin-top: 0; /* Adjust margin for smaller screens */
    }

    .backToLogin {
        margin: 14px 0 0; /* Adjust margin for smaller screens */
    }
}

/* Styles for screens between 601px and 900px (e.g., tablets) */
@media (min-width: 601px) and (max-width: 900px) {
    .codeinputField {
        width: 90%; /* Adjust width to fit the screen */
    }


    .imageTextContainer {
        margin-top: -30px; /* Adjust margin for tablets */
    }

    .backToLogin {
        margin: 20px 0 -40px; /* Adjust margin for tablets */
    }
}

