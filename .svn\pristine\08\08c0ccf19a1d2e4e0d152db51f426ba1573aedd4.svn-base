type IframeMessage = {
  type: string;
  data?: any;
};

type MessageHandler = (data: any, event: MessageEvent) => void;

export class IframeBridge {
  private static handlers: Map<string, MessageHandler[]> = new Map();
  private static pendingPromises: Map<string, (data: any) => void> = new Map();
  private static initialized = false;

  // Auto-initialize when this class is first loaded
  private static init() {
    if (this.initialized || typeof window === "undefined") return;

    window.addEventListener("message", this.handleMessage.bind(this));
    this.initialized = true;
  }

  private static handleMessage(event: MessageEvent) {
    const message = event.data as IframeMessage;

    // Resolve any awaited message
    if (this.pendingPromises.has(message.type)) {
      this.pendingPromises.get(message.type)?.(message.data);
      this.pendingPromises.delete(message.type);
    }

    // Notify listeners
    const listeners = this.handlers.get(message.type);
    if (listeners) {
      listeners.forEach((fn) => fn(message.data, event));
    }
  }

  static sendToParent(message: IframeMessage, targetOrigin = "*") {
    this.init();
    if (window.parent !== window) {
      window.parent.postMessage(message, targetOrigin);
    }
  }

  static waitFor(type: string): Promise<any> {
    this.init();
    return new Promise((resolve) => {
      this.pendingPromises.set(type, resolve);
    });
  }

  static on(type: string, handler: MessageHandler) {
    this.init();
    if (!this.handlers.has(type)) {
      this.handlers.set(type, []);
    }
    this.handlers.get(type)!.push(handler);
  }

  static off(type: string, handler: MessageHandler) {
    const list = this.handlers.get(type);
    if (!list) return;
    this.handlers.set(
      type,
      list.filter((fn) => fn !== handler)
    );
  }

  static clear() {
    if (!this.initialized) return;
    window.removeEventListener("message", this.handleMessage.bind(this));
    this.handlers.clear();
    this.pendingPromises.clear();
    this.initialized = false;
  }
}
