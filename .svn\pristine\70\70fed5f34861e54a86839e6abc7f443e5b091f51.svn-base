.priceInput {
  font-weight: 700;
  font-size: 60px;
  text-align: center;
  color: #585858;
}

.priceInput::placeholder {
  color: #dfdfdf;
}

.priceInput:disabled {
  background-color: transparent;
}
.JobPricingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
  background-color: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.JobPricingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
}

.JobPricingHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-block: 15px;
  background-color: #179D52;
  padding-inline: 20px;
}

.JobPricingTitle {
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  margin: 0;
  padding: 0;
  word-wrap: break-word;
}

.JobPricingItems {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  width: 80%;
}

.JobPricingItem {
  width: 100%;
}

.JobPricingItemContent {
  display: flex;
  width: 100%;
}

.JobPricingItemText {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.JobPricingItemTitle {
  font-weight: 700;
  font-size: 24px;
  color: #585858;
  margin: 0;
  margin-top: 12px;
}

.JobPricingItemSubtitle {
  font-weight: 300;
  font-size: 16px;
  color: #585858;
  margin: 0;
  margin-top: 4px;
  margin-bottom: 16px;
}

.JobPricingItemImage {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
}

.JobPricingButtons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 24px;
}
.JobPricingFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff; /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow for better visibility */
  padding: 10px 0; /* Padding for the button */
  text-align: center;
  z-index: 999; /* Ensure it stays on top of other elements */
  margin-top: auto;
}
.nextButtonMobile {
  padding: 10px 20px;
  width: 80%; /* Full width */
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #ffa500; /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Style for disabled button */
.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
}

/* Optional: If you want the buttons to be stacked vertically */
.MobileFooter button {
  margin-bottom: 10px; /* Adds spacing between buttons */
}
.pricingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
  background-color: #fff;
}

.pricingWrapper {
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.pricingContent {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 80px;
  padding: 25px;
}

.pricingQuestion {
  height: min-content;
  display: flex;
  justify-content: center;
}

.pricingQuestionText {
  margin: 0;
  padding: 0;
  margin-left: auto;
  margin-right: 24px;
  font-weight: 700;
  font-size: 22px;
  color: #585858;
}

.pricingOptions {
  flex-grow: 1;
  height: min-content;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-top: 10px;
}

.pricingOption {
  user-select: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  border-radius: 20px;
  gap: 20px;
  font-weight: 400;
  font-size: 16px;
  padding-block: 13px;
}

.pricingOptionText {
  margin: 0;
  padding: 0;
}

.pricingBankNote {
  margin: 0;
  padding: 0;
  font-weight: 400;
  font-size: 14px;
  color: #585858;
  max-width: 317px;
  width: 90%;
}

.pricingFooter {
  width: 100%;
  margin-top: auto;
}

.pricingDivider {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 24px;
}

.pricingButtons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 24px;
}

/* Responsive Styles for Mobile
@media (max-width: 768px) {
  .JobPricingContent {
      width: 90%;
  }
  .JobPricingTitle {
      font-size: 24px;
  }

  .JobPricingItems {
      width: 100%;
  }

  .JobPricingItemImage {
      width: 100px;
  }

  .JobPricingItemTitle {
      font-size: 20px;
  }

  .JobPricingItemSubtitle {
      font-size: 14px;
  }
} */
.JobpricingMain {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  user-select: none;
  flex-grow: 1;
  /* overflow: hidden;
  overflow-y: scroll; */
  background-color: #fff;
  position: relative;
  overflow: auto;
}
.priceGuide {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  padding-inline: 15px;
}
.shiftDivMobile {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
  width: 100%;
}
.shiftOne {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border-radius: 20px;
  gap: 113px;
  height: 84px;
}
.shiftDivTwoMobile {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border-radius: 20px;
  gap: 75px;
  height: 84px;
}
.jobCost {
  height: 44px;
  font-size: 16px;
  font-weight: 700;
  background-color: #77dbc9;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 10px;
}
.jobLength {
  height: 44px;
  font-size: 16px;
  font-weight: 700;
  background-color: #2f9acd;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 30px;
  gap: 8px;
}
.gotitBtn {
  background-color: #ffa500;
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  height: 45px;
  padding-inline: 50px;
}
.jobContent {
  display: flex;
  flex-direction: column;
  padding: 25px;
}
.extraPayMobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.extraHour {
  background-color: #1f9eab;
  height: 44px;
  border-radius: 10px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  border: none;
}
.priceGuideTutoring {
  display: flex;
  flex-direction: row;
  border-radius: 20px;
  background-color: #d9d9d9;
  /* gap: 24px; */
  padding-inline: 20px;
  justify-content: space-evenly;
  padding-block: 15px;
}
.rateSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
}
.ratecontainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: "Poppins", sans-serif;
  gap: 78px;
  padding-inline: 13px;
}
.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}
@media screen and (max-width: 700px) {
  .shiftOne {
    gap: 113px;
  }
  .shiftDivTwoMobile {
    gap: 75px;
  }
}

/* Media query for devices smaller than 600px */
@media screen and (max-width: 600px) {
  .shiftOne {
    gap: 90px; /* Gradually reduce gap */
  }
  .shiftDivTwoMobile {
    gap: 60px;
  }
}

/* Media query for devices smaller than 500px */
@media screen and (max-width: 500px) {
  .shiftOne {
    gap: 90px; /* Further reduce gap */
  }
  .shiftDivTwoMobile {
    gap: 50px;
  }
}

/* Media query for devices smaller than 400px */
@media screen and (max-width: 400px) {
  .shiftOne {
    gap: 80px; /* Minimum gap */
  }
  .shiftDivTwoMobile {
    gap: 40px;
  }
}
.shiftOneSecond {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border-radius: 20px;
  justify-content: space-evenly;
  height: 84px;
}
.shiftDivTwoMobileSecond {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border-radius: 20px;
  justify-content: space-evenly;
  height: 84px;
}

