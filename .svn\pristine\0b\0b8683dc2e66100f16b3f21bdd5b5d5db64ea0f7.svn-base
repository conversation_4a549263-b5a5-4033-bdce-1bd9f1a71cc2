import React, { useEffect, useState } from "react";
import childrenIcon from "../../../../assets/images/Icons/my_child.png";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { IoHomeOutline } from "react-icons/io5";
import { FaArrowLeft } from "react-icons/fa";
import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
import EditTimesheet from "./EditTimesheet";
import Doller from "../../../../assets/images/Icons/Dollar1.png"
import c from "../../../../helper/juggleStreetConstants";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";


interface TimesheetRow {
    start: string;
    finish: string;
    hours: number;
    rate: number;
    total: number;
    originalId?: string;
    isOriginal?: boolean;
    editVersion?: number;
    modifiedDate?: string;
    lastModified?: number;
}

interface AwaitingConfirmationCardProps {
    profileName: string;
    profileImage: string;
    jobType: string;
    jobDate: string;
    jobAddress: string;
    baseRate: number;
    extraHoursRate: number;
    initialTimesheetRows: TimesheetRow[];
    statusText: string;
    isConfirmed?: boolean;
    isSubmitting?: boolean;
    onSubmit?: () => void;
    onGoBack?: () => void;
    onTimesheetRowsChange?: (rows: TimesheetRow[]) => void;
    isEdited: boolean;
    setIsEdited: (isEdited: boolean) => void;
}

const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
    profileName,
    profileImage,
    jobType,
    jobDate,
    jobAddress,
    baseRate,
    extraHoursRate,
    initialTimesheetRows,
    statusText,
    isConfirmed = false,
    isSubmitting = false,
    isEdited,
    setIsEdited,
    onSubmit,
    onGoBack,
    onTimesheetRowsChange,
}) => {
    const [submitted, setSubmitted] = useState(false);
    const handleSubmit = () => {
        setSubmitted(true);
        onSubmit?.();
    };

    // Sort initial rows by latest modified date/editVersion
    const getSortedInitialRows = () => {
        const sortedRows = [...initialTimesheetRows].sort((a, b) => {
            // Sort by modifiedDate if available
            if (a.modifiedDate && b.modifiedDate) {
                return new Date(b.modifiedDate).getTime() - new Date(a.modifiedDate).getTime();
            }
            // Sort by editVersion if available
            if (a.editVersion !== undefined && b.editVersion !== undefined) {
                return b.editVersion - a.editVersion;
            }
            // Sort by lastModified timestamp if available
            if (a.lastModified && b.lastModified) {
                return b.lastModified - a.lastModified;
            }
            return 0;
        });
        
        return sortedRows.map(row => ({ 
            ...row, 
            isOriginal: row.isOriginal || false, 
            editVersion: row.editVersion || 0 
        }));
    };
    
    const [timesheetRows, setTimesheetRows] = useState<TimesheetRow[]>(getSortedInitialRows());
    
    // Update timesheetRows when initialTimesheetRows change
    useEffect(() => {
        console.log('AwaitingConfirmationCard - initialTimesheetRows changed:', initialTimesheetRows);
        const newSortedRows = getSortedInitialRows();
        setTimesheetRows(newSortedRows);
    }, [initialTimesheetRows]);
    const [showEditPage, setShowEditPage] = useState(false);
    const [editVersionCounter, setEditVersionCounter] = useState(1);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const isParent = clientType === c.clientType.INDIVIDUAL;
    const isBusiness = clientType === c.clientType.BUSINESS;
    const showApprove = isParent || isBusiness;
    const totalAmount = timesheetRows
        .filter(row => !row.isOriginal)
        .reduce((sum, row) => sum + row.total, 0);
    const dateObj = new Date(jobDate);
    const getDayWithSuffix = (day: number) => {
        if (day > 3 && day < 21) return `${day}th`;
        switch (day % 10) {
            case 1: return `${day}st`;
            case 2: return `${day}nd`;
            case 3: return `${day}rd`;
            default: return `${day}th`;
        }
    };

    const day = dateObj.getDate();
    const dayWithSuffix: string = getDayWithSuffix(day);

    const month: string = dateObj.toLocaleString('default', { month: 'long' });
    const year: number = dateObj.getFullYear();

    const formattedDate: string = `${dayWithSuffix} of ${month}, ${year}`;

    const parseTime = (timeStr: string): Date | null => {
        const [_, hourStr, minuteStr = "0", ampm] = timeStr
            .toLowerCase()
            .match(/(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/) || [];

        if (!hourStr) return null;

        let hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr, 10);

        if (ampm === "pm" && hour < 12) hour += 12;
        if (ampm === "am" && hour === 12) hour = 0;

        const date = new Date(1970, 0, 1, hour, minute);
        return isNaN(date.getTime()) ? null : date;
    };

    // Helper function to calculate hours from AM/PM time strings
    const calculateHoursFromTimes = (startTime: string, endTime: string): number => {
        if (!startTime || !endTime) return 0;

        const startDate = parseTime(startTime);
        const endDate = parseTime(endTime);

        if (!startDate || !endDate) return 0;

        let diffMilliseconds = endDate.getTime() - startDate.getTime();

        // Handle overnight shifts
        if (diffMilliseconds < 0) {
            diffMilliseconds += 24 * 60 * 60 * 1000;
        }

        const diffHours = diffMilliseconds / (1000 * 60 * 60);
        return parseFloat(diffHours.toFixed(2));
    };

    const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
        const currentActiveRows = timesheetRows.filter(row => !row.isOriginal);
        // <<< ADD THIS LINE TO SEE THE EXACT INPUT
        console.log("Data received from EditTimesheet component:", updatedShifts);
        const previousOriginalRows = timesheetRows.filter(row => row.isOriginal);

        const newRows: TimesheetRow[] = [];
        const changedOriginalRows: TimesheetRow[] = [];
        const unchangedRows: TimesheetRow[] = [];

        updatedShifts.forEach((updatedShift, index) => {
            const currentRow = currentActiveRows[index];

            if (currentRow) {
                const wasChanged = currentRow.start !== updatedShift.start ||
                    currentRow.finish !== updatedShift.finish;

                if (wasChanged) {
                    const clonedOriginal = JSON.parse(JSON.stringify(currentRow));

                    newRows.push({
                        ...clonedOriginal,
                        isOriginal: true,
                        editVersion: editVersionCounter - 1
                    });

                    // Calculate hours and total for the new time
                    const hours = calculateHoursFromTimes(updatedShift.start, updatedShift.finish);
                    const total = hours * baseRate;

                    newRows.push({
                        start: updatedShift.start,
                        finish: updatedShift.finish,
                        hours: hours,
                        rate: baseRate,
                        total: total,
                        isOriginal: false,
                        editVersion: editVersionCounter
                    });
                } else {
                    newRows.push({
                        ...currentRow,
                        editVersion: editVersionCounter
                    });
                }
            } else {
                // Calculate hours and total for new row
                const hours = calculateHoursFromTimes(updatedShift.start, updatedShift.finish);
                const total = hours * baseRate;

                newRows.push({
                    start: updatedShift.start,
                    finish: updatedShift.finish,
                    hours: hours,
                    rate: baseRate,
                    total: total,
                    isOriginal: false,
                    editVersion: editVersionCounter
                });
            }
        });

        const updatedRows = [
            ...previousOriginalRows,
            ...changedOriginalRows,
            ...unchangedRows,
            ...newRows
        ];

        // Add modification timestamp to track latest changes
        const rowsWithTimestamp = updatedRows.map(row => ({
            ...row,
            modifiedDate: new Date().toISOString(),
            lastModified: Date.now()
        }));

        setTimesheetRows(rowsWithTimestamp);
        setEditVersionCounter(prev => prev + 1);
        setShowEditPage(false);

        // Notify parent component of the changes with timestamp
        console.log('AwaitingConfirmationCard - Notifying parent of changes:', rowsWithTimestamp);
        onTimesheetRowsChange?.(rowsWithTimestamp);
        setIsEdited(true);
    };

    if (showEditPage) {
        const activeShiftsForEdit = timesheetRows
            .filter(row => !row.isOriginal)
            .map(({ start, finish }) => ({ start, finish }));

        return (
            <EditTimesheet
                day={String(day)}
                date={formattedDate}
                profileImage={profileImage}
                profileName={profileName}
                baseRate={baseRate}
                extraRate={extraHoursRate}
                initialShifts={activeShiftsForEdit}
                onClose={() => setShowEditPage(false)}
                onSave={handleSaveShifts}
                jobType={jobType}
            />
        );
    }
    const sortedRows = [...timesheetRows].sort((a, b) => {
        if (a.isOriginal && !b.isOriginal) return -1;
        if (!a.isOriginal && b.isOriginal) return 1;
        if (a.isOriginal && b.isOriginal) return (a.editVersion || 0) - (b.editVersion || 0);
        return 0;
    });

    return (
        <>
            <div className={styles.headerWrapper}>
                {isConfirmed ? (
                    <div className={styles.confirmedHeader}>
                        <div className={styles.inlineContainer}>
                            <div className={styles.head}>
                                <div className={styles.confirmRow}>
                                    <div className={styles.confirmedIconCircle}>
                                        <span className={styles.checkIcon}>✔</span>
                                    </div>
                                </div>
                                <div className={styles.headerConfirmedTitle}>
                                    {showApprove ? "Approved" : "Confirmed"}
                                </div>
                            </div>

                            <div className={styles.confirmedSubtitle}>
                                {showApprove
                                    ? "Thanks for confirming! This Timesheet will now be used to generate an invoice and complete the payment process."
                                    : "Thanks for confirming your Timesheet"}
                            </div>
                        </div>
                    </div>
                ) : (
                    <button className={styles.backBtn} onClick={onGoBack}>
                        <span className={styles.arrowCircle}>
                            <span className={styles.arrow}><FaArrowLeft /></span>
                        </span>
                        Go back
                    </button>
                )}
            </div>

            <div className={`${styles.card} ${submitted ? styles.cardSubmitted : ""}`}>
                <div className={styles.headerSection}>
                    <div>
                        <h3 className={styles.title}>Review Timesheet</h3>

                        <div className={styles.status}>
                            Status: <span className={styles.statusHighlight}>
                                {submitted ? "Awaiting Craig's Approval" : statusText}
                            </span>
                        </div>
                    </div>
                    <div className={styles.profileContainer}>
                        <img
                            src={profileImage}
                            alt="Profile"
                            className={styles.avatar}
                        />
                        <div className={styles.profileName}>{profileName}</div>
                    </div>
                </div>

                <div className={styles.info}>
                    <div className={styles.infoBlock}>
                        <div className={styles.row}>
                            <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
                            <div>{jobType}</div>
                        </div>
                        <div className={styles.row}>
                            <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
                            <div>{formattedDate}</div>
                        </div>
                        <div className={styles.row}>
                            <IoHomeOutline className={styles.rowIcon} />
                            <div>{jobAddress}</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />

                    <div className={styles.rateBlock}>
                        <img src={Doller} alt="dollar" className={styles.rowIcon} />
                        <div className={styles.rateText}>
                            <div>Base Rate: ${baseRate} per hour</div>
                            {
                                extraHoursRate ? (
                                    <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div>
                                ) : null
                            }
                            {/* <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div> */}
                        </div>
                    </div>

                    <hr className={styles.hrFull} />
                </div>

                {!submitted && timesheetRows.some(row => row.isOriginal) && (
                    <div className={styles.adjustedTimesheetBlock}>
                        <div className={styles.adjustedHeader}>
                            <span className={styles.adjustedTitle}>Adjusted Timesheet</span>
                            <span className={styles.adjustedIcon}>?</span>
                        </div>
                        <div className={styles.adjustedDescription}>
                            You have adjusted the hours worked. Please review changes and then "Approve".
                        </div>
                        <hr className={styles.hrFull} />
                    </div>

                )}

                <div className={styles.timesheetContainer}>
                    <div className={styles.rowHeader}>
                        <div className={styles.column}>Start</div>
                        <div className={styles.column}>Finish</div>
                        <div className={styles.column}>Hours</div>
                        <div className={styles.column}>Rate</div>
                        <div className={styles.column}>Total</div>
                    </div>

                    {sortedRows.map((row, index) => (
                        <div
                            key={`${index}-${row.editVersion}-${row.isOriginal}`}
                            className={`${styles.rowData} ${row.isOriginal ? styles.originalRow : ''}`}
                            style={row.isOriginal ? {
                                color: '#ff6359',
                                textDecoration: 'line-through',
                                opacity: 0.7,
                            } : {}}
                        >
                            <div className={styles.column}>{row.start}</div>
                            <div className={styles.column}>{row.finish}</div>
                            <div className={styles.column}>{row.hours}</div>
                            <div className={styles.column}>${row.rate}</div>
                            <div className={styles.column}>${row.total}</div>
                        </div>
                    ))}
                    <hr className={styles.hr} />

                    <div className={styles.totalRow}>
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
                    </div>

                    <hr className={styles.hr} />
                </div>

                {submitted && (
                    <div className={styles.rowAlign}>
                        <div className={styles.textBlock}>
                            <p className={styles.pendingTitle}>Pending {profileName} Approval</p>
                            <p className={styles.pendingSub}>
                                Awaiting for {profileName} to approve your timesheet
                            </p>
                        </div>
                        <div className={styles.profileContainerConfirm}>
                            <div className={styles.avatarWrapper}>
                                <img src={profileImage} className={styles.avatar} alt="profile" />
                                <div className={styles.redDot}></div>
                            </div>
                            <div className={styles.profileName1}>{profileName}</div>
                        </div>

                    </div>
                )}

                <div className={styles.footer}>
                    {!isConfirmed ? (
                        <>
                            <div className={styles.section}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>
                                        {showApprove ? 'Approve Timesheet' : 'Confirm Timesheet'}
                                    </strong>
                                    <div className={styles.subText}>The Timesheet is an accurate record of the job.</div>
                                </div>
                                <button className={styles.submitBtn} onClick={handleSubmit}>
                                    {isSubmitting ? 'Submitting...' : (showApprove ? 'Approve' : 'Submit')}
                                </button>
                            </div>
                            <div className={`${styles.section} ${styles.editSection}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make adjustments to the Timesheet.</div>
                                </div>
                                <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>Edit Timesheet</button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className={styles.confirmedBox}>
                                <p className={styles.confirmedTitle}>Timesheet Confirmed</p>
                                <p className={styles.confirmedSubText}>This Timesheet will now be submitted to the parent for approval</p>
                            </div>

                            <div className={`${styles.section} ${styles.editSection} ${submitted ? styles.disabledBlock : ""}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make an adjustment to the actual hours worked</div>
                                </div>
                                <button className={styles.editDisabledBtn} disabled>Edit Timesheet</button>
                            </div>

                            <button className={styles.nextBtn} onClick={onGoBack}>Next</button>
                        </>
                    )}
                </div>
            </div>
        </>
    );
};

export default AwaitingConfirmationCard;