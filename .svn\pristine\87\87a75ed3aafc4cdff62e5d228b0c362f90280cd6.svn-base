import { useEffect, useState } from 'react';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { InputTextarea } from 'primereact/inputtextarea';
import useLoader from '../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import OutlineButton from '../../../commonComponents/OutlineButton';
import c from '../../../helper/juggleStreetConstants';
import useIsMobile from '../../../hooks/useIsMobile';

interface Extra {
    canSelectCategory: boolean;
    childCategory: number;
    children: null;
    optionId: number;
    selected: boolean;
    text: string;
}
const Childcare = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const specialNeedsExp = sessionInfo.data['provider']['myExperience2'];
    const [checkedState, setCheckedState] = useState<boolean[]>(new Array(c.childcareJobTypes.length).fill(false));
    // const [radioState, setRadioState] = useState<string>('no');
    const [radioState, setRadioState] = useState<string>(specialNeedsExp !== null && specialNeedsExp.length > 0 ? 'yes' : 'no');
    const [childminderRadioState, setChildminderRadioState] = useState('no');
    const handleChildminderRadioChange = (value) => {
        setChildminderRadioState(value);
    };
    const {isMobile}=useIsMobile();
    const minCharLimit = 100;
    const minCharLimitneeds = 50;
    const [myExperience, setMyExperience] = useState("");
    const [myExperience2, setmyExperience2] = useState(sessionInfo.data['myExperience2'] || '');
    const dispatch = useDispatch<AppDispatch>();
    const { enableLoader, disableLoader } = useLoader();
    const [extras, setExtras] = useState<Extra[]>(
        sessionInfo.data["provider"]["extraSkills"] || []
    );
    const [errors, setErrors] = useState({
        myExperienceError: false,
        myExperience2Error: false,
    });
    
    useEffect(() => {
        if (sessionInfo) {
            setChildminderRadioState(sessionInfo.data['interestedInJobTypes'] > 0 ? 'yes' : 'no');
        }
    }, [sessionInfo]);
    const [childcarejobError, setchildcarejobError] = useState<boolean>(false);
    useEffect(() => {
        if (sessionInfo) {
            setMyExperience(sessionInfo.loading ? "" : sessionInfo.data["provider"].myExperience || "");
            setmyExperience2(sessionInfo.loading ? "" : sessionInfo.data["provider"].myExperience2 || "");
        }
    }, [sessionInfo]);

    useEffect(() => {
        if (sessionInfo.data['interestedInJobTypes']) {
            const previousSelections = c.childcareJobTypes.map(jobType =>
                !!(sessionInfo.data['interestedInJobTypes'] & jobType.value)
            );
            setCheckedState(previousSelections);
        }
    }, [sessionInfo.data]);
    const handleCheckboxChange = (index: number) => {
        const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
        setCheckedState(updatedCheckedState);
    };
    const handleRadioChange = (value: string) => {
        setRadioState(value);
        if (value === 'no') {
            setmyExperience2('');
        }
    };
    useEffect(() => {
        if (sessionInfo) {
            setMyExperience(sessionInfo.loading ? "" : sessionInfo.data["provider"].myExperience || "");
            setmyExperience2(sessionInfo.loading ? "" : sessionInfo.data["provider"].myExperience2 || "");
            setRadioState(specialNeedsExp !== null && specialNeedsExp.length > 0 ? 'yes' : 'no');
        }
    }, [sessionInfo]);
    const handleInputChange = (e) => {
        const value = e.target.value;

        const countWords = (text) => {
            return text
                .trim()
                .split(/\s+/)
                .filter((word) => word.length > 0).length;
        };
        const totalWords = countWords(value);

        if (totalWords <= 120) {
            setMyExperience(value.trim().length > 0 ? value : '');
        }
    };
    const handleInputChange1 = (e) => {
        const value = e.target.value;

        const countWords = (text) => {
            return text
                .trim()
                .split(/\s+/)
                .filter((word) => word.length > 0).length;
        };
        const totalWords = countWords(value);

        if (totalWords <= 120) {
            setmyExperience2(value.trim().length > 0 ? value : '');
        }
    };
    const handleSave = () => {
        let isValid = true;
        let hasError = false;
        const newErrors = { ...errors };
        
        // Checkbox validation
        const isAnyCheckboxSelected = checkedState.some((checked) => checked);
        if (!isAnyCheckboxSelected) {
          setchildcarejobError(true);
          hasError = true;
        } else {
          setchildcarejobError(false);
        }
      
        // Experience field validation
        if (!myExperience) {
          newErrors.myExperienceError = true;
          isValid = false;
        } else {
          newErrors.myExperienceError = false;
        }
      
        if (!myExperience2 && radioState === 'yes') {
          newErrors.myExperience2Error = true;
          isValid = false;
        } else {
          newErrors.myExperience2Error = false;
        }
      
        setErrors(newErrors);
      
        // Prevent API call if any validation fails
        if (!isValid || hasError) return;
      
        enableLoader();
        
        // Prepare payload if validation passes
        const interestedInJobTypes = calculateInterestedInJobTypes();
        const payload = {
          ...sessionInfo.data,
          interestedInJobTypes: interestedInJobTypes,
          provider: {
            ...sessionInfo.data["provider"],
            extraSkills: extras,
            myExperience,
            myExperience2,
          },
        };
      
        dispatch(updateSessionInfo({ payload })).finally(() => {
          disableLoader();
        });
      };
      
    const toggleExtra = (optionId: number) => {
        setExtras((prevExtras) =>
            prevExtras.map((extra) =>
                extra.optionId === optionId ? { ...extra, selected: !extra.selected } : extra
            )
        );
    };
    const calculateInterestedInJobTypes = () => {
        return checkedState.reduce((sum, checked, index) => {
            if (checked) {
                // Using bitwise OR to set the bit
                return sum | c.childcareJobTypes[index].value;
            }
            return sum;
        }, 0);
    };
    return (
        <div>
            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between', padding: '40px', paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }}>
                <div className='flex align-items-center justify-content-between mb-2 mt-1
                     flex-wrap'>
                    <header className={styles.utilheader}>
                        <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Childcare</h1>
                    </header>
                    <CustomButton
                        label={"Save"}
                        className={`${myfamilystyles.customButton}`}
                        style={{ margin: "0", width: "150px" }}
                        onClick={handleSave}
                    />
                </div>
                <div>
                    <h1
                        className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                        style={{ fontSize: '16px', color: '#179d52' }}
                    >
                        Would you like to work as Childminder?
                    </h1>
                </div>
                <div>
                    <div className="flex justify-content-start items-center mt-3 gap-2">
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                name="childminder-radio"
                                value="yes"
                                checked={childminderRadioState === 'yes'}
                                onChange={() => handleChildminderRadioChange('yes')}
                                className="cursor-pointer"
                            />
                            Yes
                        </label>
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                name="childminder-radio"
                                value="no"
                                checked={childminderRadioState === 'no'}
                                onChange={() => handleChildminderRadioChange('no')}
                                className="cursor-pointer"
                            />
                            No
                        </label>
                    </div>
                </div>
                {childminderRadioState === 'yes' && (
                    <>
                        <div style={{ maxWidth: '100%', width: 'auto', textWrap: 'wrap' }}>
                            <h1
                                className="mt-3 p-1 txt-clr flex-wrap font-medium line-height-1"
                                style={{ fontSize: '16px', color: childcarejobError ? 'red' : '#179d52' }}
                            >
                                Select all the childcare jobs you would be willing to do for families and
                                businesses nearby
                            </h1>
                        </div>
                        <div className="flex flex-column justify-content-center " style={{ marginBottom: '' }}>
                            {c.childcareJobTypes.map((jobType, index) => (
                                <label key={jobType.value} className="flex items-center gap-2 p-1 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        checked={checkedState[index]}
                                        onChange={() => handleCheckboxChange(index)}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <span className="txt-clr" style={{ fontSize: '16px' }}>
                                        {jobType.label}
                                    </span>
                                </label>
                            ))}
                        </div>
                        {/* <div className="flex flex-column justify-content-center " style={{ marginBottom: '' }}>
                            {labels.map((label, index) => (
                                <label key={label} className="flex items-center gap-2 p-1 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        checked={checkedState[index]}
                                        onChange={() => handleCheckboxChange(index)}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <span className="txt-clr " style={{ fontSize: '16px' }}>
                                        {label}
                                    </span>
                                </label>
                            ))}
                        </div> */}
                        <div>
                            <h1
                                className="m-0 p-1 txt-clr font-medium line-height-1 mt-3"
                                style={{ fontSize: '16px', color: errors.myExperienceError ? 'red' : '#179d52' }}
                            >
                                Describe your childcare experience
                            </h1>
                        </div>
                        {/* Render all textareas */}
                        <div
                            className="mt-2 p-1"
                            style={{ minHeight: '100px', overflow: 'auto', width: '100%' }}
                        >
                            <InputTextarea
                                autoResize
                                value={myExperience}
                                required
                                onChange={handleInputChange}
                                rows={4}
                                cols={30}
                                // className={styles.inputTextareafamily}
                                className={`${styles.inputTextareafamily} w-full p-3 ${errors.myExperienceError ? "border-red-500" : ""
                                    }`}
                                style={{ fontSize: '12px' }}
                                placeholder="How long have you been looking after children for? What age children are you most comfortable with? What do you find most rewarding about childcare? Etc."
                            />
                        </div>
                        <p
                            style={{
                                fontSize: '14px',
                                color: myExperience.length < minCharLimit ? 'red' : 'green',
                                fontWeight: '400',
                            }}
                        >
                            {myExperience.length < minCharLimit &&
                                `${minCharLimit - myExperience.length} characters remaining`}
                        </p>
                        <div>
                            <h1
                                className="m-0 p-1 txt-clr font-medium line-height-1"
                                style={{ fontSize: '16px', color:'#179d52' }}
                            >
                                Do you have special needs training?
                            </h1>
                        </div>
                        {/* Render all Yes/No radio buttons */}
                        <div>
                            <div className="flex justify-content-start items-center mt-2 gap-2 ">
                                <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                                    <input
                                        type="radio"
                                        name="shared-radio"
                                        value="yes"
                                        checked={radioState === 'yes'}
                                        onChange={() => handleRadioChange('yes')}
                                        className=" cursor-pointer"
                                    />
                                    Yes
                                </label>
                                <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                                    <input
                                        type="radio"
                                        name="shared-radio"
                                        value="no"
                                        checked={radioState === 'no'}
                                        onChange={() => handleRadioChange('no')}
                                        className="cursor-pointer"
                                    />
                                    No
                                </label>
                            </div>
                        </div>
                        {radioState === 'yes' && (
                            <>
                                <div>
                                    <h1
                                        className="m-0 p-1 txt-clr font-medium line-height-1 mt-3"
                                        style={{ fontSize: '16px',color: errors.myExperience2Error ? 'red' : '#179d52' }}
                                    >
                                        Describe your special needs training and experience
                                    </h1>
                                </div>
                                {/* Render all textareas */}
                                <div
                                    className="mt-2 p-1"
                                    style={{ overflow: 'auto', width: '80%' }}
                                >
                                    <InputTextarea
                                        autoResize
                                        value={myExperience2}
                                        required
                                        onChange={handleInputChange1}
                                        rows={5}
                                        cols={30}
                                        // className={styles.inputTextareafamily}
                                        className={`${styles.inputTextareafamily} w-full p-3 ${errors.myExperience2Error ? "border-red-500" : ""
                                            }`}
                                        style={{ fontSize: '12px' }}
                                    />
                                </div>
                                <p
                                    style={{
                                        fontSize: '14px',
                                        color: myExperience2.length < minCharLimitneeds ? 'red' : 'green',
                                        fontWeight: '400',
                                    }}
                                >
                                    {myExperience2.length < minCharLimitneeds &&
                                        `${minCharLimitneeds - myExperience2.length} characters remaining`}
                                </p>
                            </>
                        )}
                        <div>
                            <div style={{ maxWidth: '100%', width: 'auto', textWrap: 'wrap' }}>
                                <h1
                                    className="mt-2 p-1 txt-clr flex-wrap font-medium line-height-1"
                                    style={{ fontSize: '16px', color: '#179d52' }}
                                >
                                    Childcare Extras
                                </h1>
                            </div>
                            <div className="flex flex-wrap mt-4 gap-3">
                                {extras.map((extraSkills: Extra) => (
                                    <OutlineButton
                                        key={extraSkills.optionId}
                                        onClick={() => toggleExtra(extraSkills.optionId)}
                                        className={`${extraSkills.selected ? "selected" : ""}`}
                                        style={{
                                            fontSize: "14px",
                                            border: extraSkills.selected ? "2px solid #179D52" : "none",
                                            paddingBlock: "15px",
                                            fontWeight: extraSkills.selected ? "700" : "500",
                                            backgroundColor: extraSkills.selected ? "#F0F4F7" : "#FFFFFF",
                                            textWrap: "nowrap",
                                            marginLeft: "5px",
                                            width: "36%",
                                        }}
                                    >
                                        {extraSkills.text}
                                    </OutlineButton>
                                ))}
                            </div>
                        </div>

                    </>)}
            </div>

        </div >
    )
}

export default Childcare
