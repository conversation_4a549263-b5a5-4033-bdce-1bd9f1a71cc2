import environment from "../../helper/environment";
import * as _ from "underscore";
import c from "../../helper/juggleStreetConstants";

export class utils {
  static getCookie = (name) => {
    name = name + this._getCookieNameSuffix();
    const value = localStorage.getItem(name);
    return value ? _.unescape(value) : null;
  };

  static setCookie = (name, value, _date) => {
    name = name + this._getCookieNameSuffix();
    localStorage.setItem(name, _.escape(value));
  };

  static removeCookie = (name) => {
    name = name + this._getCookieNameSuffix();
    localStorage.removeItem(name);
  };

  static obliterateEverything = () => {
    localStorage.clear();
    sessionStorage.clear();
    const cookies = document.cookie.split(";");
    for (const cookie of cookies) {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.slice(0, eqPos).trim() : cookie.trim();
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    }
  };

  // static getCookie = (name) => {
  //   name = name + this._getCookieNameSuffix();
  //   let cEnd, cStart, cookie;
  //   let value;
  //   cookie = document.cookie;
  //   cStart = cookie.indexOf(" " + name + "=");
  //   if (cStart === -1) {
  //     cStart = cookie.indexOf(name + "=");
  //   }
  //   if (cStart === -1) {
  //     value = null;
  //   } else {
  //     cStart = cookie.indexOf("=", cStart) + 1;
  //     cEnd = cookie.indexOf(";", cStart);
  //     if (cEnd === -1) {
  //       cEnd = cookie.length;
  //     }
  //     value = _.unescape(cookie.substring(cStart, cEnd));
  //   }
  //   return value;
  // };

  // static setCookie = (name, value, date) => {
  //   name = name + this._getCookieNameSuffix();
  //   let cookieValue = _.escape(value);

  //   // Add expiry if provided
  //   if (date != null) {
  //     cookieValue += "; expires=" + date.toUTCString();
  //   }

  //   // Always set path to root
  //   cookieValue += "; path=/";

  //   // Add Secure and SameSite=None to allow third-party cookies (for iframes)
  //   cookieValue += "; SameSite=None; Secure";

  //   const hostname = window.location.hostname;
  //   const currentEnv = environment.getCurrentEnvironment(hostname);

  //   // Optionally set domain for production
  //   if (currentEnv === "production") {
  //     cookieValue += "; domain=" + this.getDomainName(hostname);
  //   }

  //   // Finally, set the cookie
  //   document.cookie = name + "=" + cookieValue;
  // };

  // static removeCookie = (name) => {
  //   name = name + this._getCookieNameSuffix();
  //   const value = ""; // No value is needed
  //   const date = new Date(0); // The epoch time (1970-01-01)
  //   let cookieString = name + "=" + value + "; expires=" + date.toUTCString() + "; path=/";
  //   const hostname = window.location.hostname;
  //   const currentEnv = environment.getCurrentEnvironment(hostname);
  //   if (currentEnv === "production") {
  //     cookieString += "; domain=" + this.getDomainName(hostname);
  //   }
  //   document.cookie = cookieString;
  // };

  static _getCookieNameSuffix = () => {
    const hostname = window.location.hostname;
    const currentEnv = environment.getCurrentEnvironment(hostname);
    switch (currentEnv) {
      case "production":
      case "preproduction":
        return "";
      case "staging":
        return "-staging";
      case "vnext":
        return "-vnext";
      case "development":
        return "-dev";
      case "frontendServer":
      case "devServer":
        return "-local";
      default:
        return "";
    }
  };

  static getDomainName = (hostname: string): string => {
    // Extract the top-level domain for setting cookies
    // E.g., from 'www.example.com', get '.example.com'
    const parts = hostname.split(".");
    if (parts.length > 2) {
      return "." + parts.slice(-2).join(".");
    }
    return "." + hostname;
  };

  static isBrowser() {
    return typeof window !== "undefined";
  }

  static debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout> | null = null;
    return function (this: ThisParameterType<T>, ...args: Parameters<T>): void {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }
  static numberToWords = (num) => {
    const ones = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"];
    const teens = ["Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"];
    const tens = ["", "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"];

    if (num === 0) return "Zero";

    const getBelowHundred = (n) => {
      if (n === 0) return "";
      if (n < 10) return ones[n];
      if (n > 10 && n < 20) return teens[n - 11];
      return tens[Math.floor(n / 10)] + (n % 10 !== 0 ? " " + ones[n % 10] : "");
    };

    const getHundreds = (n) => {
      if (n < 100) return getBelowHundred(n);
      return ones[Math.floor(n / 100)] + " Hundred" + (n % 100 !== 0 ? " " + getBelowHundred(n % 100) : "");
    };

    let word = "";

    if (num >= 1000) {
      word += getHundreds(Math.floor(num / 1000)) + " Thousand ";
      num %= 1000;
    }

    word += getHundreds(num);

    return word.trim();
  };

  static createDateFromTimeString = (time: string): Date => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    const date = new Date();
    date.setHours(hours, minutes, seconds, 0); // Set time and reset milliseconds to 0
    return date;
  };

  static omitProperties<T extends object, K extends keyof T>(obj: T, properties: K[]): Omit<T, K> {
    const rest = Object.keys(obj)
      .filter((key) => !properties.includes(key as K))
      .reduce((acc, key) => {
        acc[key as K] = obj[key as K];
        return acc;
      }, {} as T);
    return rest as Omit<T, K>;
  }

  static getDeviceType(): "mobile" | "tablet" | "desktop" {
    const userAgent = navigator.userAgent.toLowerCase();

    if (/tablet|ipad/.test(userAgent)) {
      return "tablet";
    }

    if (/mobile|android|iphone|ipod/.test(userAgent)) {
      return "mobile";
    }

    return "desktop";
  }
  static calculateDateDifference = (date1: Date, date2: Date, measurement: "days" | "months" | "years"): number => {
    const timeDiff = date1.getTime() - date2.getTime();

    switch (measurement) {
      case "days":
        return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      case "months": {
        const yearDiff = date1.getFullYear() - date2.getFullYear();
        const monthDiff = date1.getMonth() - date2.getMonth();
        return yearDiff * 12 + monthDiff;
      }
      case "years":
        return date1.getFullYear() - date2.getFullYear();
      default:
        throw new Error('Invalid measurement unit. Use "days", "months", or "years".');
    }
  };
  static calculateInterestedInJobTypes = (currentValue: number, jobType: keyof typeof c.jobType, operation: "add" | "remove") => {
    if (operation === "add") {
      let newValue = currentValue + c.jobType[jobType];
      return newValue;
    } else if (operation === "remove") {
      let newValue = currentValue - c.jobType[jobType];
      if (newValue < 0) {
        newValue = 0;
      }
      return newValue;
    } else {
      return currentValue;
    }
  };
  static getHours(time) {
    const items = time.split(":");
    if (items.length < 2) return 0;

    var hours = parseInt(items[0], 10);
    return hours;
  }
  static getMins(time) {
    const items = time.split(":");
    if (items.length < 2) return 0;

    var minutes = parseInt(items[1], 10);
    return minutes;
  }
  static getTimeDifference(startTime: string, endTime: string): number {
    let endHours = this.getHours(endTime);
    let startHours = this.getHours(startTime);

    if (endHours === 0) endHours = 24; // Midnight.
    if (startHours >= 12 && endHours < 12) endHours += 24; // PM to AM event.

    let hoursDiff = endHours - startHours;
    let minsDiff = this.getMins(endTime) - this.getMins(startTime);

    let total = hoursDiff + (minsDiff ? minsDiff / 60 : 0);

    return total;
  }

  static getLanguageList(): { text: string; payload: string }[] {
    return [
      { text: "Any", payload: "-1" },
      { text: "Spanish", payload: "58" },
      { text: "French", payload: "46" },
      { text: "German", payload: "47" },
      { text: "Italian", payload: "51" },
      { text: "Dutch", payload: "44" },
      { text: "Cantonese", payload: "42" },
      { text: "Indonesian", payload: "50" },
      { text: "Filipino", payload: "45" },
      { text: "Korean", payload: "52" },
      { text: "Croatian", payload: "43" },
      { text: "Hindi", payload: "49" },
      { text: "Arabic", payload: "41" },
      { text: "Greek", payload: "48" },
      { text: "Macedonian", payload: "53" },
      { text: "Punjabi", payload: "56" },
      { text: "Serbian", payload: "57" },
      { text: "Maltese", payload: "54" },
      { text: "Mandarin", payload: "55" },
      { text: "Japanese", payload: "5000" },
      { text: "Portuguese", payload: "5001" },
      { text: "Te Reo Māori", payload: "5002" },
    ];
  }
  static getResponseTimeText = (responseTime) => {
    if (!responseTime || responseTime == 0) return "0";

    if (responseTime == 1) {
      return "1 hr";
    } else if (responseTime == 24) {
      return "1 day";
    } else if (responseTime == 48) {
      return "2 days";
    } else if (responseTime == 72) {
      return "3 days";
    } else if (responseTime == 168) {
      return "7 days";
    } else if (responseTime == 1000) {
      return "7+ days";
    }
  };
  static cleanAddress = (address: string | null | undefined): string => {
    if (!address) return "";
    return address.split(" ").slice(0, -2).join(" ");
  };
}

export default utils;
