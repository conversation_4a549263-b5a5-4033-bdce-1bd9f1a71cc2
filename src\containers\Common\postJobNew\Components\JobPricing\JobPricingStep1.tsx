import { Divider } from "primereact/divider";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import styles from "../../../../Common/styles/job-pricing.module.css";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import ReactDOM from "react-dom";
import { GoBack, Next } from "../Buttons";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";
function JobPricingStep1() {
  const { isMobile } = useIsMobile();
  return isMobile ? <JobPricingStep1Mobile /> : <JobPricingStep1Web />;
}

export default JobPricingStep1;
const useJobTypeHook = () => {
  const { next, payload, prev, setpayload } = useJobManager();
  return {
    next,
    payload,
    prev,
    setpayload,
  };
};

const JobPricingStep1Web = () => {
  const { next, payload, prev, setpayload } = useJobTypeHook();
  return (
    <div className="h-full w-full flex flex-column justify-content-center align-items-center relative overflow-hidden overflow-y-auto">
      <div
        className="flex align-items-center flex-column"
        style={{
          height: "100%",
          width: "100%",
          paddingTop: "4%",
        }}
      >
        <div
          className="flex justify-content-center align-items-center flex-column"
          style={{
            width: "75%",
          }}
        >
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: 700,
              fontSize: "30px",
              color: "#585858",
              textWrap: "wrap",
              textAlign: "center",
            }}
          >
            When setting the price of your job, please <br /> consider the following
          </h1>
          <div
            className="flex justify-content-center flex-column"
            style={{
              paddingTop: "25px",
              width: "70%",
            }}
          >
            {[
              {
                title: "Job Requirements",
                subTitle: "The number and age of children being cared for, is driving required, any extra duties etc.",
              },
              {
                title: "Helper Profile",
                subTitle: "Age and experience of the helper, special needs training, formal qualifications, languages etc.",
              },
              {
                title: "Shift Duration",
                subTitle: "A short shift, and multiple shifts in the same day, are harder to fill and require a high pay rate.",
              },
            ].map((item, index) => (
              <div
                key={index}
                className="flex flex-column"
                style={{
                  width: "100%",
                }}
              >
                <div
                  className="flex"
                  style={{
                    width: "100%",
                  }}
                >
                  <div className="flex flex-column flex-grow-1">
                    <p
                      className="p-0 m-0 mt-3 flex align-items-center justify-content-center underline"
                      style={{
                        fontWeight: 700,
                        fontSize: "24px",
                        color: "#179d52",
                      }}
                    >
                      {item.title}
                    </p>
                    <p
                      className="p-0 m-0 mt-1 mb-4 flex text-center"
                      style={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {item.subTitle}
                    </p>
                  </div>
                  <div
                    className="flex justify-content-center align-items-center"
                    style={{
                      width: "120px",
                    }}
                  ></div>
                </div>
                <Divider />
              </div>
            ))}
          </div>
        </div>
      </div>
      <div
        className="flex flex-column sticky justify-content-center align-items-center bottom-0 bg-white"
        style={{
          width: "100%",
        }}
      >
        <Divider className="" />
        <div className=" flex justify-content-between align-content-center py-3" style={{ width: "80%" }}>
          <GoBack
            onClick={() => {
              setpayload(payload);

              if ([2, 4, 8, 12].includes(payload.jobType)) {
                prev("day-and-schedule");
              } else {
                prev("job-details");
              }
            }}
          />
          <Next
            onClick={() => {
              setpayload(payload);
              if ([2, 4, 8, 12].includes(payload.jobType)) {
                next("pricing-payments-step1");
              } else {
                next("jobpricing-step2");
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

const JobPricingStep1Mobile = () => {
  const { next, payload, prev, setpayload } = useJobTypeHook();
  const items = [
    {
      title: "Job Requirements",
      subTitle: "The number and age of children being cared for, is driving required, any extra duties etc.",
    },
    {
      title: "Helper Profile",
      subTitle: "Age and experience of the helper, special needs training, formal qualifications, languages etc.",
    },
    {
      title: "Shift Duration",
      subTitle: "A short shift, and multiple shifts in the same day, are harder to fill and require a high pay rate.",
    },
  ];
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
        height: "100%",
        position: "relative",
      }}
    >
      <div className={styles.JobPricingContainer}>
        <div className={styles.JobPricingContent}>
          <div className="mt-4" style={{ width: "80%" }}>
            <p style={{ fontSize: "18px", fontWeight: "700", color: "#585858" }} className="m-0 p-0">
              {" "}
              When setting the price of your job, please consider the following
            </p>
          </div>
          <div className={styles.JobPricingItems}>
            {items.map((item, index) => (
              <div key={index} className={styles.JobPricingItem}>
                <div className={styles.JobPricingItemContent}>
                  <div className={styles.JobPricingItemText}>
                    <p className={styles.JobPricingItemTitle}>{item.title}</p>
                    <p className={styles.JobPricingItemSubtitle}>{item.subTitle}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <BackButtonPortal id="back-button-portal">
            <div
              onClick={() => {
                setpayload({
                  ...payload,
                });
                prev("job-description-mobile");
              }}
            >
              <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
            </div>
          </BackButtonPortal>

          <CustomFooterButton
            label="Next"
            onClick={() => {
              setpayload(payload);
              next("jobpricing-step2");
            }}
          />
        </div>
      </div>
    </div>
  );
};
