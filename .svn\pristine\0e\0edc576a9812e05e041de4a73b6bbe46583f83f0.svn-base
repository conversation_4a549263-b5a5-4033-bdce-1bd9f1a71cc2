/* ShimmerHelperCard.module.css */
.shimmerCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 10px;
    padding: 10px;
}

.shimmer {
    /* Enhanced radial gradients for a more pronounced curve */
    background: 
        radial-gradient(circle 40px at top left, rgba(200, 200, 200, 1) 0%, rgba(240, 240, 240, 0.3) 50%, transparent 100%),
        radial-gradient(circle 40px at top right, rgba(200, 200, 200, 1) 0%, rgba(240, 240, 240, 0.3) 50%, transparent 100%),
        linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 150% 150%, 150% 150%, 200% 100%; /* Smaller radial gradient size for sharper curve */
    background-position: 100% 0, 0 0, 200% 0; /* Initial positions */
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: 100% 0, 0 0, 200% 0; /* Start with radial gradients at corners */
    }
    50% {
        background-position: 50% 50%, 50% 50%, 0 0; /* Move radial gradients inward for curve effect */
    }
    100% {
        background-position: 0 100%, 100% 100%, -200% 0; /* End with radial gradients at opposite corners */
    }
}

.shimmerImage {
    width: 180px;
    height: 180px;
    border-radius: 10px;
}

.shimmerContent {
    width: 100%;
    padding: 10px;
}

.shimmerTitle {
    height: 20px;
    margin-bottom: 10px;
}

.shimmerLine {
    height: 15px;
    margin-bottom: 10px;
}

@keyframes shimmer {
    0% {
        background-position: 100% 0, 0 0, 200% 0; /* Start with radial gradients at corners and linear gradient off-screen */
    }
    50% {
        background-position: 50% 50%, 50% 50%, 0 0; /* Move radial gradients inward and linear gradient halfway */
    }
    100% {
        background-position: 0 100%, 100% 100%, -200% 0; /* End with radial gradients at opposite corners and linear gradient off-screen */
    }
}