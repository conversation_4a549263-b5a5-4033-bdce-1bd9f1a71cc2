import { Avatar } from "primereact/avatar";
import { IoClose } from "react-icons/io5";
import { Rating } from "primereact/rating";
import Chat from "../../../../assets/images/Icons/chat-pop.png";
import FileCheck from "../../../../assets/images/Icons/file-check.png";
import RatingStarOn from "../../../../assets/images/Icons/rating-star-on.png";
import RatingStarOff from "../../../../assets/images/Icons/rating-star-off.png";
import { useEffect, useRef, useState } from "react";
import { Divider } from "primereact/divider";
import locationicon from "../../../../assets/images/Icons/location-mobile.png";
import styles from "../../../Common/styles/award-shifts-to.module.css";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";
type Props = {
  helperImgSrc?: string;
  applicantId?: number;
  publicName?: string;
  isSuperHelper?: boolean;
  avgRating?: number;
  numRatings?: number;
  responseRate?: number;
  location?: string;
  transport?: string;
  gettingHome?: any;
  applicationStatus?: number;
  isFirstAidQualified?: boolean;
  isOneoffJob?: boolean;
  onViewProfileClicked?: () => void;
  onContactClicked?: () => void;
  onAwardJobClicked?: () => void;
  onDismissClicked?: () => void;
};

const AwardCard = ({
  helperImgSrc,
  applicantId,
  publicName,
  isSuperHelper,
  avgRating,
  numRatings,
  applicationStatus,
  responseRate,
  location,
  transport,
  isFirstAidQualified,
  gettingHome,
  isOneoffJob = true,
  onViewProfileClicked,
  onContactClicked,
  onAwardJobClicked,
  onDismissClicked,
}: Props) => {
  
  
  const { isMobile } = useIsMobile();

  const currentApplicantStatus = Array.isArray(applicationStatus)
    ? applicationStatus.find(
        (applicant) => applicant.applicantId === applicantId
      )?.applicationStatus
    : applicationStatus;

  return !isMobile ? (
    <div
      className="h-min w-min flex align-items-center gap-2"
      style={{
        maxWidth: "952px",
        minWidth: "910px",
        width: "910.84px",
        border: "1px solid #DFDFDF",
        borderRadius: "10px",
        padding: "15px 10px",
      }}
    >
      <div className="flex gap-2 h-full justify-content-center align-items-center">
        <Avatar
          className="relative"
          style={{
            width: "86.7px",
            height: "81px",
          }}
          image={helperImgSrc}
          shape="circle"
        >
          {!helperImgSrc &&
            publicName &&
            (() => {
              const initial1 = publicName.split(" ")[0][0];
              const initial2 = publicName.split(" ")[1][0];
              return (
                <div
                  style={{
                    fontWeight: "600",
                    fontSize: "16px",
                    color: "#585858",
                    textTransform: "uppercase",
                  }}
                >{`${initial1} ${initial2}`}</div>
              );
            })()}
          {isSuperHelper && (
            <div
              className="absolute"
              style={{
                fontWeight: "700",
                fontSize: "10px",
                color: "#FFFFFF",
                backgroundColor: "#444444",
                borderRadius: "30px",
                padding: "5px",
                bottom: "-8px",
              }}
            >
              Super Helper
            </div>
          )}
        </Avatar>
        <div className="flex flex-column">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "18px",
              color: "#585858",
              textTransform: "capitalize",
            }}
          >
            {publicName}
          </h1>
          <p
            className="m-0 p-0 cursor-pointer"
            style={{
              fontWeight: "400",
              fontSize: "14px",
              color: "#585858",
              textDecoration: "underline",
            }}
            onClick={(e) => {
              e.preventDefault();
              if (onViewProfileClicked) {
                onViewProfileClicked();
              }
            }}
          >
            View Profile
          </p>
        </div>
      </div>
      {
        gettingHome  ?
        <div className="flex flex-wrap gap-2 ml-3">
        <span
          style={{
            color: "#585858",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: "250px",
            fontSize: "14px",
          }}
        >
          Getting Home: {gettingHome}
        </span>
      </div> : null
      }
      

      <div className="flex-grow-1 h-full flex align-items-center">
        {!avgRating && !numRatings && !responseRate && !isFirstAidQualified ? (
          <div className="mx-auto" />
        ) : (
          <div className="mx-auto">
            {!!avgRating && !!numRatings && (
              <div className="flex flex-column gap-2">
                <Rating
                  readOnly
                  value={avgRating}
                  cancel={false}
                  onIcon={
                    <img
                      src={RatingStarOn}
                      alt="rating on"
                      width="22.51px"
                      height="23px"
                    />
                  }
                  offIcon={
                    <img
                      src={RatingStarOff}
                      alt="rating off"
                      width="22.51px"
                      height="23px"
                    />
                  }
                />
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "300",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >{`${numRatings} ratings`}</p>
              </div>
            )}
            {!avgRating && !numRatings && responseRate && (
              <div className="flex gap-1 align-items-center">
                <img src={Chat} alt="chat" width="12.8px" height="12.8px" />
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "300",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  {responseRate} hour Response rate
                </p>
              </div>
            )}
            {!avgRating && !numRatings && isFirstAidQualified && (
              <div className="flex gap-1 align-items-center">
                <img
                  src={FileCheck}
                  alt="file check"
                  width="12.4px"
                  height="12.8px"
                />
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "300",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  First Aid qualified
                </p>
              </div>
            )}
          </div>
        )}
        <div
          className="flex align-items-center gap-2 cursor-pointer"
          style={{
            boxShadow: " 0 0 4px 0 rgba(0, 0, 0, 0.25)",
            padding: "10px 25px",
            borderRadius: "15px",
          }}
          onClick={(e) => {
            e.preventDefault();
            if (onContactClicked) {
              onContactClicked();
            }
          }}
        >
          <img src={Chat} alt="chat" width="14.4px" height="14.4" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            Contact
          </p>
        </div>
      </div>
      <div
        style={{
          height: "71px",
          width: "2px",
          backgroundColor: "#DFDFDF",
          marginInline: "30px",
        }}
      />
      {currentApplicantStatus === c.jobApplicationStatus.WITHRDAWN ? (
        <div className="flex flex-column gap-2">
          <p
            style={{
              color: "#585858",
              fontSize: "14px",
              fontWeight: "400",
              textAlign: "center",
            }}
          >
            Application withdrawn
          </p>
        </div>
      ) : (
        <div
          className="cursor-pointer"
          style={{
            backgroundColor: "#FFA500",
            borderRadius: "10px",
            color: "#FFFFFF",
            boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
            padding: "5px 50px",
            fontWeight: "700",
            fontSize: "16px",
            textDecoration: "underline",
            textWrap:"nowrap"
          }}
          onClick={(e) => {
            e.preventDefault();
            if (onAwardJobClicked) {
              onAwardJobClicked();
            }
          }}
        >
          {isOneoffJob ? "Award Job" : "Award Shift"}
        </div>
      )}
      {currentApplicantStatus !== c.jobApplicationStatus.WITHRDAWN &&
       (
        <div
          className="flex justify-content-center align-items-center cursor-pointer"
          style={{
            border: "1px solid #DFDFDF",
            borderRadius: "50%",
            width: "30px",
            height: "32px",
          }}
          onClick={(e) => {
            e.preventDefault();
            if (onDismissClicked) {
              onDismissClicked();
            }
          }}
        >
          <IoClose color="#585858" size="90%" />
        </div>
       )}
    </div>
  ) : (
    <div
      className="h-min flex flex-column  gap-2"
      style={{
        width: "100%",
        border: "2px solid #179D52",
        borderRadius: "20px",
        position: "relative",
      }}
    >
      <div className="flex gap-2 h-full justify-content-center align-items-center"></div>
      <div
        style={{ justifyContent: "space-between", alignItems: "flex-start" }}
        className=" h-full flex px-3 py-2"
      >
        <div>
          {currentApplicantStatus !== c.jobApplicationStatus.WITHRDAWN &&
          !isOneoffJob ? (
            <div
              className="flex justify-content-center align-items-center cursor-pointer"
              style={{
                border: "1px solid #DFDFDF",
                borderRadius: "50%",
                width: "20px",
                height: "22px",
                position: "absolute",
                right: "-1px",
                top: "-8px",
                backgroundColor: "#FFFFFF",
              }}
              onClick={(e) => {
                e.preventDefault();
                if (onDismissClicked) {
                  onDismissClicked();
                }
              }}
            >
              <IoClose color="#585858" size="90%" />
            </div>
          ) : null}
          <div className="flex flex-column">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "18px",
                color: "#585858",
                textTransform: "capitalize",
              }}
            >
              {publicName}
            </h1>
          </div>
          <div className="flex flex-row justify-content-start align-items-center gap-1">
            <img
              src={locationicon}
              width={"11px"}
              height={"14px"}
              alt="locationicon"
            />
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#585858",
                textTransform: "capitalize",
              }}
            >
              {location}
            </h1>
          </div>

          {!avgRating &&
          !numRatings &&
          !responseRate &&
          !isFirstAidQualified ? (
            <div className="mx-auto" />
          ) : (
            <div className="mx-auto">
              {!!avgRating && !!numRatings && (
                <div className="flex flex-row gap-2">
                  <Rating
                    readOnly
                    value={avgRating}
                    cancel={false}
                    onIcon={
                      <img
                        src={RatingStarOn}
                        alt="rating on"
                        width="14.51px"
                        height="15px"
                      />
                    }
                    offIcon={
                      <img
                        src={RatingStarOff}
                        alt="rating off"
                        width="14.51px"
                        height="15px"
                      />
                    }
                  />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >{`(${numRatings} ratings)`}</p>
                </div>
              )}
              {!avgRating && !numRatings && responseRate && (
                <div className="flex gap-1 align-items-center">
                  <img src={Chat} alt="chat" width="12.8px" height="12.8px" />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {responseRate} hour Response rate
                  </p>
                </div>
              )}
              {!avgRating && !numRatings && isFirstAidQualified && (
                <div className="flex gap-1 align-items-center">
                  <img
                    src={FileCheck}
                    alt="file check"
                    width="12.4px"
                    height="12.8px"
                  />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    First Aid qualified
                  </p>
                </div>
              )}
            </div>
          )}
          <div className="flex flex-column">
            <span
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "14px",
                color: "#585858",
                textTransform: "capitalize",
              }}
            >
              {/* <span style={{ fontWeight: "400" }}>
                Getting Home:{" "}
                {transport === undefined ? "Unspecified" : transport}
              </span> */}
            </span>
          </div>
        </div>
        <div>
          <Avatar
            className={`relative flex flex-column ${styles.avatarDiv}`}
            style={{
              width: "50px",
              height: "52px",
            }}
            image={helperImgSrc}
            shape="circle"
          >
            {!helperImgSrc &&
              publicName &&
              (() => {
                const initial1 = publicName.split(" ")[0][0];
                const initial2 = publicName.split(" ")[1][0];
                return (
                  <div
                    style={{
                      fontWeight: "600",
                      fontSize: "16px",
                      color: "#585858",
                      textTransform: "uppercase",
                    }}
                  >{`${initial1} ${initial2}`}</div>
                );
              })()}
            {/* {isSuperHelper && (
            <div
              className="absolute"
              style={{
                fontWeight: "700",
                fontSize: "8px",
                color: "#FFFFFF",
                backgroundColor: "#444444",
                borderRadius: "30px",
                textWrap:"nowrap",
                padding: "3px",
                bottom: "-15px",
              }}
            >
              Super Helper
            </div>
          )} */}
            <p
              style={{
                color: "#179D52",
                fontSize: "12px",
                fontWeight: "400",
              }}
              className="p-0 m-0"
            >
              {"•Applied"}
            </p>
          </Avatar>
        </div>
        {/* <div
                className='flex align-items-center gap-2 cursor-pointer'
                style={{
                    boxShadow: ' 0 0 4px 0 rgba(0, 0, 0, 0.25)',
                    padding: '10px 25px',
                    borderRadius: '15px',
                }}
                onClick={(e) => {
                    e.preventDefault();
                    if (onContactClicked) {
                        onContactClicked();
                    }
                }}
            >
                <img src={Chat} alt='chat' width='14.4px' height='14.4' />
                <p
                    className='m-0 p-0'
                    style={{
                        fontWeight: '500',
                        fontSize: '14px',
                        color: '#585858',
                    }}
                >
                    Contact
                </p>
            </div> */}
      </div>
      <Divider className="my-2" />
      <div className="flex flex-row justify-content-center align-items-center gap-4 py-2">
        <div>
          <p
            className="m-0 cursor-pointer"
            style={{
              backgroundColor: "transparent",
              borderRadius: "20px",
              color: "#585858",
              padding: "8px 22px",
              fontWeight: "700",
              width: "max-content",
              fontSize: "14px",
              border: "1px solid #585858",
              textDecoration: "underline",
            }}
            onClick={(e) => {
              e.preventDefault();
              if (onViewProfileClicked) {
                onViewProfileClicked();
              }
            }}
          >
            View Profile
          </p>
        </div>
        {currentApplicantStatus === c.jobApplicationStatus.WITHRDAWN ? (
          <div className="flex flex-column gap-2">
            <p
              style={{
                color: "#585858",
                fontSize: "14px",
                fontWeight: "400",
                textAlign: "center",
              }}
            >
              Application withdrawn
            </p>
          </div>
        ) : (
          <div
            className="cursor-pointer"
            style={{
              backgroundColor: "#FFA500",
              borderRadius: "20px",
              color: "#FFFFFF",
              padding: "9px 28px",
              fontWeight: "700",
              width: "fit-content",
              fontSize: "14px",
            }}
            onClick={(e) => {
              e.preventDefault();
              if (onAwardJobClicked) {
                onAwardJobClicked();
              }
            }}
          >
            {isOneoffJob ? "Award Job" : "Award Shift"}
          </div>
        )}
      </div>
    </div>
  );
};

export default AwardCard;
