import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useState } from "react";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import { useNavigate } from 'react-router-dom';
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";

const TimeSheet: React.FC<{
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;

}> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [showCard, setShowCard] = useState(false);
  const navigate = useNavigate();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));

  function generatePDF(): void {
    throw new Error("Function not implemented.");
  }

  const handleGoBack = () => {
    console.log("Go back clicked");
  };

  const handleSubmit = () => {
    alert("Timesheet confirmed!");
  };

  const handleEdit = () => {
    alert("Edit timesheet clicked");
  };

  const isParent = clientType === c.clientType.INDIVIDUAL;

  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };

  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel
        header={
          <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
            <span dangerouslySetInnerHTML={{ __html: headers.awaitingConfirmation }} />
          </div>
        }
      >    
       <div
              style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}
            >
              Content for Awaiting your Confirmation Timesheets
            </div>      
      </TabPanel>

      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        <span dangerouslySetInnerHTML={{ __html: headers.adjustedTimesheets }} />
      </div>
      }
      >
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Parent-adjusted Timesheets</div>
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        <span dangerouslySetInnerHTML={{ __html: headers.awaitingApproval }} /> </div>}>
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Awaiting Parent Approval</div>
      </TabPanel>
    </TabView>
  );
};

export default TimeSheet;