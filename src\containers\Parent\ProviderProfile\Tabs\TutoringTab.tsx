import React, { CSSProperties, useEffect, useState } from "react";
import styles from "../../styles/tutoring-tab.module.css";
import earth from "../../../../assets/images/Icons/earth.png";
import tickIcon from "../../../../assets/images/Icons/check-green.png";
import tickBlack from "../../../../assets/images/Icons/check-star.png";
import { ExtendedProfileTabProps } from "../types";
import { Divider } from "primereact/divider";
import degree from "../../../../assets/images/Icons/degree.png";
import star from "../../../../assets/images/Icons/star.png";
import rank from "../../../../assets/images/Icons/rank.png";
import c from "../../../../helper/juggleStreetConstants";
import blocks from "../../../../assets/images/blocks.png";
import blocksone from "../../../../assets/images/block-one.png";
import blocksTwo from "../../../../assets/images/block-two.png";
import blockThree from "../../../../assets/images/block-three.png";
import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import useIsMobile from "../../../../hooks/useIsMobile";
import environment from "../../../../helper/environment";

interface ChecksProps {
  date1: string;
  date2: string;
}

interface Qualification {
  optionId: number;
  text: string;
  selected: boolean;
  canSelectCategory: boolean;
  childCategory: number;
  children: Qualification[];
}
const LimitedText = ({
  text,
  limit,
  disableLimit,
  style,
  className,
}: {
  text: string;
  limit: number;
  disableLimit: boolean;
  style?: CSSProperties;
  className?: string;
}) => {
  const displayText =
    disableLimit || text.length <= limit ? text : text.slice(0, limit);
  return (
    <span className={className} style={{ ...style }}>
      {displayText}
    </span>
  );
};

const Checks: React.FC<ChecksProps> = ({ date1, date2 }) => {
  const { isMobile } = useIsMobile();
  return (
    <>
    <div className="px-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Checks
      </h1>
      <div className="flex gap-2 mt-2 mx-2">
        {!isMobile ? (
          <img src={tickBlack} alt="check" height="23px" width="23px" />
        ) : (
          <img src={tickBlack} alt="check" height="18px" width="18px" />
        )}
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "600",
            fontSize: !isMobile ? "16px" : "14px",
            color: "#585858",
          }}
        >
          Working With Children Check
        </p>
      </div>
      <p
        className="m-0 p-0 mt-2 mb-3"
        style={{
          fontWeight: "700",
          fontSize: "12px",
          color: "#179D52",
        }}
      >
        {`Verified on: ${date1} | Expires on: ${date2}`}
      </p>

    </div>
    <Divider />
    </>
  );
};

const ChildcareQualification: React.FC<{ helper: any }> = ({ helper }) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        marginTop: "10px",
      }}
    >
      {helper.qualifications
        .filter((val) => val.selected === true)
        .map((qualification: Qualification) => (
          <div
            key={qualification.optionId}
            style={{
              fontWeight: "600",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
              display: "flex",
              gap: "10px",
            }}
          >
                     {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
            <span>{qualification.text}</span>
            {qualification.children && qualification.children.length > 0 && (
              <div style={{ marginLeft: "1rem" }}>
                {qualification.children.map((child: Qualification) => (
                  <div
                    key={child.optionId}
                    style={{
                      fontWeight: "600",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    {child.text}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
    </div>
  );
};
const TutoringQualification: React.FC<{ helper: any }> = ({ helper }) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        marginTop: "10px",
      }}
    >
      {helper.tutoringQualifications
        .filter((val) => val.selected === true)
        .map((tutoringQualifications: Qualification) => (
          <div
            key={tutoringQualifications.optionId}
            style={{
              fontWeight: "600",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
              display: "flex",
              gap: "10px",
            }}
          >
            {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
            <span>{tutoringQualifications.text}</span>
            {tutoringQualifications.children &&
              tutoringQualifications.children.length > 0 && (
                <div style={{ marginLeft: "1rem" }}>
                  {tutoringQualifications.children.map(
                    (child: Qualification) => (
                      <div
                        key={child.optionId}
                        style={{
                          fontWeight: "600",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        {child.text}
                      </div>
                    )
                  )}
                </div>
              )}
          </div>
        ))}
    </div>
  );
};
const ReviewAndRatingHead = ({
  rating,
  ratingCount,
  isSuperHelper,
}: {
  rating: number;
  ratingCount: number;
  isSuperHelper: boolean;
}) => {
  const { isMobile } = useIsMobile();

  return (
    <div className="mb-4">
      <div className="flex justify-content-between align-items-center">
        <div>
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: !isMobile ? "20px" : "16px",
              color: "#585858",
            }}
          >
            Reviews
          </h1>
          <div className="flex gap-1">
            <img src={star} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "300",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
            </p>
          </div>
        </div>
        {isSuperHelper && (
          <div className="flex gap-2 align-items-center">
            <img src={rank} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "18px",
                color: "#585858",
              }}
            >
              Super Helper
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
const ReviewAndRatingList = ({
  ratings,
}: {
  ratings: Array<{
    clientFirstName: string;
    clientLastName: string;
    feedback: string;
    ratingDate: string;
    ratingAvg: number;
    clientImageUrl: string;
  }>;
}) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="flex flex-column  pt-2 mt-2 mb-4">
      {ratings.map((rating, index) => (
        <React.Fragment key={index}>
          <Divider />
          <div className="flex gap-2 my-2">
            <div
              style={{
                height: "38px",
                width: "38px",
                background: "gray",
                borderRadius: "50%",
                overflow: "hidden",
                 minWidth:"38px"
              }}
            >
              <img
                src={rating.clientImageUrl}
                alt="client Image"
                width="100%"
                height="100%"
              />
            </div>
            <div className="flex-grow-1 flex flex-column gap-2">
              <div className="flex">
                <div className="flex-grow-1 flex flex-column">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                  >
                    {new Date(rating.ratingDate).toLocaleDateString("en-GB")}
                  </p>
                </div>
                <div
                  style={{ display: "flex", flexDirection: "row", gap: "5px" }}
                >
                  <img
                    src={star}
                    alt="star"
                    width="19.82px"
                    height="18.62px"
                    style={{ marginTop: "2px" }}
                  />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "600",
                      fontSize: "18px",
                      color: "#585858",
                    }}
                  >
                    {`${rating.ratingAvg.toFixed(1)}`}
                  </p>
                </div>
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                {rating.feedback}
              </p>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

const TutoringTab: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const [isvisible, setIsvisible] = useState(false);
  const { isMobile } = useIsMobile();
  const displayLimit = 300;
  const text = helper.providerMyExperience3 || "";

  const nat = c.countriesIso.find(
    (country) =>
      country.alpha2.toLowerCase() === helper?.nationality.toLowerCase() ||
      country.value.toLowerCase() === helper?.nationality.toLowerCase()
  );
  return (
    <div
      style={{ paddingRight: isMobile && "0px", width:isMobile && "100%" }}
      className={styles.childCareContainer}
    >
      <Dialog
        visible={isvisible}
        onHide={() => {
          setIsvisible(false);
        }}
        content={
          <div
            className={
              !isMobile
                ? `${styles.DialogContent}`
                : `${styles.DialogContentMobile}`
            }
          >
            <div>
              {/* <button
                onClick={() => {
                  setIsvisible(false);
                }}
                className={styles.CloseBtn}
              >
                
                Close
              </button> */}
              <IoClose
                onClick={() => {
                  setIsvisible(false);
                }}
                className={styles.CloseBtn}
              />
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <h1
                style={{ fontSize: isMobile && "24px" }}
                className={styles.DialogTag}
              >
                Tutoring skill-set
              </h1>
              <p className={styles.DialogTagPara}>
                Subheading info with more about Tutoring skillset
              </p>
            </div>

            <div
              className={
                !isMobile
                  ? `${styles.DialogOptions}`
                  : `${styles.DialogOptionsMobile}`
              }
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  gap: "23px",
                  paddingTop: "15px",
                  paddingBottom: "8px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <img src={blocks} alt="blocks" height="22px" width="106px" />
                  <h1 className={styles.tagTwo}>Professional</h1>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <p className={styles.tagPara} style={{ marginTop: "0px" }}>
                    • Established tutor with deep academic understanding
                  </p>
                  <p className={styles.tagPara}>• Qualified teacher</p>
                  <p className={styles.tagPara}>• Certified Tutor</p>
                </div>
              </div>
              <Divider />
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  gap: "23px",
                  paddingTop: "15px",
                  paddingBottom: "30px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <img
                    src={blocksone}
                    alt="blocks"
                    height="22px"
                    width="106px"
                  />
                  <h1 className={styles.tagTwo}>Experienced</h1>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <p className={styles.tagPara} style={{ marginTop: "0px" }}>
                    • Received payment for tutoring multiple times
                  </p>
                  <p className={styles.tagPara}>• Studying to be a teacher</p>
                </div>
              </div>
              <Divider />
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  gap: "23px",
                  paddingTop: "15px",
                  paddingBottom: "30px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <img
                    src={blocksTwo}
                    alt="blocks"
                    height="22px"
                    width="106px"
                  />
                  <h1 className={styles.tagTwo}>Apprentice</h1>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <p className={styles.tagPara} style={{ marginTop: "0px" }}>
                    • Some previous tutoring experience mainly unpaid for family
                    & friends
                  </p>
                </div>
              </div>
              <Divider />
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  gap: "23px",
                  paddingTop: "20px",
                  paddingBottom: "30px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <img
                    src={blockThree}
                    alt="blocks"
                    height="22px"
                    width="106px"
                  />
                  <h1 className={styles.tagTwo}>Newbie</h1>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <p className={styles.tagPara} style={{ marginTop: "0px" }}>
                    • No tutoring experience
                  </p>
                </div>
              </div>
              <Divider />
            </div>
          </div>
        }
      />
      <div className={styles.childCareBoxOne}>
        <h1
          style={{ fontSize: isMobile && "16px" }}
          className={styles.childCare}
        >
          Tutoring Experience
        </h1>
        <div className={styles.childCareExperience}>
          {/* {helper.providerMyExperience} */}
          <div className="py-2 px-3">
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",

              }}
            >
              <span
                className="m-0 p-0 inline-block text-left"
                style={{
                  fontWeight: "400",
                  fontSize: !isMobile ? "16px" : "14px",
                  color: "#585858",
                }}
              >
                <LimitedText
                  className="m-0 p-0"
                  text={text}
                  limit={displayLimit}
                  disableLimit={textState}
                  style={{
                    fontWeight: "400",
                    fontSize: !isMobile ? "16px" : "14px",
                    color: "#585858",
                  }}
                />
                {text.length >= displayLimit && (
                  <span
                    className="cursor-pointer hover:text-gray-300"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                    onClick={() => toggleTextState((prev) => !prev)}
                  >
                    {" "}
                    {textState ? "Show Less." : "Read More..."}
                  </span>
                )}
              </span>
            </p>
          </div>
        </div>
        <br />
        <div style={{ display: "flex", flexDirection: "row", gap: "390px" }}>
          <h1
            style={{
              fontSize: "16px",
              fontWeight: "700",
              color: "#585858",
              margin: "0px",
            }}
          >
            Services
          </h1>
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: !isMobile ? "row" : "column",
            gap: !isMobile ? "60px" : "10px",
            paddingLeft: "10px",
            marginTop: "10px",
            textWrap: "nowrap",
          }}
        >
          <div
            style={{ display: "flex", flexDirection: "column", gap: "10px" }}
          >
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerInterestedInFaceToFaceTutoring === true}
                value="In-Home Tutor"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                In-home Tutoring
              </label>
            </div>

            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={
                  (helper.interestedInJobTypes &
                    c.jobType.PRIMARY_SCHOOL_TUTORING) !==
                  0
                }
                value="Primary School"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                Primary School
              </label>
            </div>
          </div>
          <div
            style={{ display: "flex", flexDirection: "column", gap: "10px" }}
          >
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerInterestedInOnlineTutoring === true}
                value="Online Tutor"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                Online Tutoring
              </label>
            </div>

            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={
                  (helper.interestedInJobTypes &
                    c.jobType.HIGH_SCHOOL_TUTORING) !==
                  0
                }
                value="High School"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                {environment.getCountry(window.location.hostname) === "au"
                  ? "High School"
                  : "Secondary School"}
              </label>
            </div>
          </div>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: !isMobile ? "center" : "end",
              marginLeft: "15px",
              marginInline: isMobile && "15px",
              gap: "5px",
            }}
          >
            <h1
              style={{
                fontSize: "16px",
                fontWeight: "700",
                color: "#585858",
                margin: "0px",
              }}
            >
              Nationality
            </h1>
            <button className={styles.Nationality}>
              <img src={earth} alt="" />
              {nat?.label || "Unknown"}
            </button>
          </div>
        </div>
      </div>
      {(helper.interestedInJobTypes & c.jobType.HIGH_SCHOOL_TUTORING) ===
        c.jobType.HIGH_SCHOOL_TUTORING && (
        <div className={styles.childCareBoxFive}>
          {/* Teaches Study Skills Section */}
          <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
            <div style={{ fontSize: "16px", fontWeight: "700" }}>
              Teaches Study Skills
            </div>

            {/* Yes Checkbox */}
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerCanTeachStudySkills === true}
                value="Yes"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                Yes
              </label>
            </div>

            {/* No Checkbox */}
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerCanTeachStudySkills === false}
                value="No"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                No
              </label>
            </div>
          </div>

          {/* Teaches Exam Prep Section */}
          <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
            <div style={{ fontSize: "16px", fontWeight: "700" }}>
              Teaches Exam Prep:
            </div>

            {/* Yes Checkbox */}
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerCanTeachExamPreparation === true}
                value="Yes"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                Yes
              </label>
            </div>

            {/* No Checkbox */}
            <div
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={helper.providerCanTeachExamPreparation === false}
                value="No"
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile && "14px" }}
                className={styles.checkboxLabel}
              >
                No
              </label>
            </div>
          </div>
          {helper.providerYear12GraduationYear && (
            <div className="mt-2">
              <span style={{ fontSize: "16px", fontWeight: "700" }}>
                Completed Year 12:&nbsp;&nbsp;
              </span>
              <span className="bold">
                {helper.providerYear12GraduationYear}
              </span>
            </div>
          )}
        </div>
      )}
    {((helper?.primarySchoolYears?.some((x) => x.selected) || 
     helper?.primarySchoolSubjects?.some((x) => x.selected))) && (
      <div className={styles.childCareBoxFive}>
        <div style={{ gap: "55px" }} className="flex flex-row">
          {(helper?.primarySchoolYears.filter((x) => x.selected === true)
            .length ?? 0) > 0 && (
            <div>
              <div
                style={{
                  fontSize: "16px",
                  fontWeight: "700",
                  marginBlock: "5px",
                }}
              >
                Primary School:
              </div>
              {helper.primarySchoolYears.map((item) => (
                <div
                  key={item.optionId}
                  className={styles.checkboxOption}
                  style={{ display: "flex", alignItems: "center", gap: "5px" }}
                >
                  <input
                    readOnly
                    type="checkbox"
                    checked={item.selected}
                    value={item.text}
                    className={styles.checkboxInput}
                  />
                  <label
                    style={{ fontSize: isMobile ? "14px" : "16px" }}
                    className={styles.checkboxLabel}
                  >
                    {item.text}
                  </label>
                </div>
              ))}
            </div>
          )}
 {
  // Check if there are any selected subjects
  (helper?.primarySchoolSubjects.some((category) =>
    category.children?.some((subject) => subject.selected)
  )) && (
    <div>
      <div
        style={{
          fontSize: "16px",
          fontWeight: "700",
          marginBlock: "5px",
        }}
      >
        Subjects:
      </div>
      {helper.primarySchoolSubjects.map((category) => (
        category.children && category.children.map((subject) => (
          subject.selected && (
            <div
              key={subject.optionId}
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={subject.selected}
                value={subject.text}
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile ? "14px" : "16px" }}
                className={styles.checkboxLabel}
              >
                {subject.text}
              </label>
            </div>
          )
        ))
      ))}
    </div>
  )
}
        </div>
      </div>
     )}
     {((helper?.highSchoolYears?.some((x) => x.selected) || 
     helper?.highSchoolSubjects?.some((x) => x.selected))) && (
      <div className={styles.childCareBoxFive}>
        <div style={{ gap: "55px" }} className="flex flex-row">
          {(helper?.highSchoolYears.filter((x) => x.selected === true).length ??
            0) > 0 && (
            <div>
              <div
                style={{
                  fontSize: "16px",
                  fontWeight: "700",
                  marginBlock: "5px",
                }}
              >
                High School:
              </div>
              {helper.highSchoolYears.map((item) => (
                <div
                  key={item.optionId}
                  className={styles.checkboxOption}
                  style={{ display: "flex", alignItems: "center", gap: "5px" }}
                >
                  <input
                    readOnly
                    type="checkbox"
                    checked={item.selected}
                    value={item.text}
                    className={styles.checkboxInput}
                  />
                  <label
                    style={{ fontSize: isMobile ? "14px" : "16px" }}
                    className={styles.checkboxLabel}
                  >
                    {item.text}
                  </label>
                </div>
              ))}
            </div>
          )}
 {
  // Check if there are any selected high school subjects
  (helper?.highSchoolSubjects.some((category) =>
    category.children?.some((subject) => subject.selected)
  )) && (
    <div >
      <div
        style={{
          fontSize: "16px",
          fontWeight: "700",
          marginBlock: "5px",
        }}
      >
        Subjects:
      </div>
      {helper.highSchoolSubjects.map((category) => (
        category.children && category.children.map((subject) => (
          subject.selected && (
            <div
              key={subject.optionId}
              className={styles.checkboxOption}
              style={{ display: "flex", alignItems: "center", gap: "5px" }}
            >
              <input
                readOnly
                type="checkbox"
                checked={subject.selected}
                value={subject.text}
                className={styles.checkboxInput}
              />
              <label
                style={{ fontSize: isMobile ? "14px" : "16px" }}
                className={styles.checkboxLabel}
              >
                {subject.text}
              </label>
            </div>
          )
        ))
      ))}
    </div>
  )
}
        </div>
      </div>
     )}
      <div className={styles.childCareBoxFive}>
        {helper.providerTutoringCategory == c.tutoringCategory.PROFESSIONAL && (
          <div style={{ display: "flex", flexDirection: "row", gap: "23px" }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <h1 className={styles.tag}>Professional</h1>
              <img src={blocks} alt="blocks" height="22px" width="106px" />
              <button
                onClick={() => {
                  setIsvisible(true);
                }}
                className={styles.learnMore}
              >
                Learn more here
              </button>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              <h3 className={styles.tagSec}>Skills & Experience</h3>

              <p className={styles.tagPara}>
                • Established tutor with deep academic understanding
              </p>
              <p className={styles.tagPara}>• Qualified teacher</p>
              <p className={styles.tagPara}>• Certified Tutor</p>
            </div>
          </div>
        )}

        {helper.providerTutoringCategory == c.tutoringCategory.EXPERIENCED && (
          <div style={{ display: "flex", flexDirection: "row", gap: "23px" }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <h1 className={styles.tag}>Experienced</h1>
              <img src={blocksone} alt="blocks" height="22px" width="106px" />
              <button
                onClick={() => {
                  setIsvisible(true);
                }}
                className={styles.learnMore}
              >
                Learn more here
              </button>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              <h3 className={styles.tagSec}>Skills & Experience</h3>

              <p className={styles.tagPara}>
                • Received payment for tutoring multiple times
              </p>
              <p className={styles.tagPara}>• Studying to be a teacher</p>
            </div>
          </div>
        )}
        {helper.providerTutoringCategory == c.tutoringCategory.APPRENTICE && (
          <div style={{ display: "flex", flexDirection: "row", gap: "23px" }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <h1 className={styles.tag}>Apprentice</h1>
              <img src={blocksTwo} alt="blocks" height="22px" width="106px" />
              <button
                onClick={() => {
                  setIsvisible(true);
                }}
                className={styles.learnMore}
              >
                Learn more here
              </button>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              <h3 className={styles.tagSec}>Skills & Experience</h3>

              <p className={styles.tagPara}>
                • Some previous tutoring experience mainly unpaid for family &
                friends
              </p>
            </div>
          </div>
        )}
        {helper.providerTutoringCategory == c.tutoringCategory.NEWBIE && (
          <div style={{ display: "flex", flexDirection: "row", gap: "23px" }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <h1 className={styles.tag}>Newbie</h1>
              <img src={blockThree} alt="blocks" height="22px" width="106px" />
              <button
                onClick={() => {
                  setIsvisible(true);
                }}
                className={styles.learnMore}
              >
                Learn more here
              </button>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              <h3 className={styles.tagSec}>Skills & Experience</h3>

              <p className={styles.tagPara}>• No tutoring experience</p>
            </div>
          </div>
        )}
      </div>
      <div className={styles.childCareBoxTwo}>
        {(helper?.certificates?.length ?? 0) > 0 && (
          <Checks
            date1={new Date(
              helper.certificates[0].verificationDate
            ).toLocaleDateString("en-GB")}
            date2={new Date(
              helper.certificates[0].expiryDate
            ).toLocaleDateString("en-GB")}
          />
        )}

          {helper?.qualifications.filter((x) => x.selected === true).length > 0 && (
                    <div className={styles.qualificationContainer}>
                        <h1
                            className='m-0 p-0'
                            style={{
                                fontWeight: '400',
                                fontSize: '16px',
                                color: '#585858',
                            }}
                        >
                            Childcare Qualification
                        </h1>
                        <div>
                            <ChildcareQualification helper={helper} />
                        </div>
                    </div>
                )}

        <div >
          <div>
            {helper.tutoringQualifications.filter(
              (val) => val.selected === true
            ).length > 0 && (
              <>
                <Divider />
                <div className={styles.qualificationContainer}>
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    Tutoring Qualification
                  </h1>
                  <div>
                    <TutoringQualification helper={helper} />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {helper?.hasVouches && (
      <div className={styles.childCareBoxThree}>
      <div className="px-4 pt-2 mt-2 mb-4">
      <div className="flex justify-content-between align-items-center">
        <div className="flex flex-column gap-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: !isMobile ? "20px" : "16px",
              color: "#585858",
            }}
          >
           References
          </h1>
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "16px",
              color: "#585858",
            }}
          >
           Available on request. Please contact Customer Service to obtain referee details.
          </h1>
        </div>
       
      </div>
      </div>
      </div>
      )}
      {(helper?.ratingsExtended.length ?? 0) > 0 && (
      <div className={styles.childCareBoxThree}>
        <div className={styles.qualificationContainerTutorSec}>
          <div>
           
              <>
                <ReviewAndRatingHead
                  rating={helper?.providerRatingsAvg ?? 0}
                  ratingCount={helper?.providerRatingsCount ?? 0}
                  isSuperHelper={helper?.isSuperProvider ?? false}
                />
                {(helper?.ratingsExtended.length ?? 0) > 0 && (
                  <ReviewAndRatingList ratings={helper.ratingsExtended} />
                )}
              </>
          
          </div>
        </div>
      </div>
        )}
    </div>
  );
};

export default TutoringTab;
