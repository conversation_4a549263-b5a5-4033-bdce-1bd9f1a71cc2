import { useEffect, useState } from 'react';
import { Helper } from '../../Parent/ProviderProfile/types';
import { useNavigate, useSearchParams } from 'react-router-dom';
import useLoader from '../../../hooks/LoaderHook';
import Service from '../../../services/services';
import star from "../../../assets/images/Icons/star.png";
import locationMy from "../../../assets/images/location.png";
import SidePannel from './SidePannel';
import { updateProfileActivationEnabled } from '../../../store/slices/applicationSlice';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import { RxUpdate } from 'react-icons/rx';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import c from '../../../helper/juggleStreetConstants';
import { GrHide } from 'react-icons/gr';
import { FaRegHeart } from 'react-icons/fa6';
import HomeHeaderHelper from './HomeHeaderHelper';
import { FiAward } from 'react-icons/fi';
import ArrowLeft from "../../../assets/images/Icons/arrow-left.png";
import styles from '../../Parent/styles/child-careTab.module.css';
import { MdOutlineCancel } from 'react-icons/md';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../../Parent/styles/my-family.module.css";
import useIsMobile from '../../../hooks/useIsMobile';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';
const getIndustrySectorText = (value: number) => {
  const item = c.industrySectorsList.find((sector) => sector.value === value);
  if (!item) return '';
  return item.label;
};

const ClientProfile = () => {
  const [helper, setHelper] = useState<Helper | null>(null);
  const [searchParams] = useSearchParams();
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const { enableLoader, disableLoader } = useLoader();
  const [_, setDialogVisible] = useState(false);
  const id = Number(searchParams.get('id'));
  const requestId = Number(searchParams.get('requestId'));
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const {isMobile}=useIsMobile();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const industrySector = () => {
    if (!helper?.industrySectorId) return '';
    if (helper?.industrySectorText) return helper.industrySectorText;
    return getIndustrySectorText(helper.industrySectorId);
  };

  const benefitsProProvidersList = () => {
    if (!helper?.benefitsForProviders) return '';
    const benefits = helper.benefitsForProviders.filter(benefit => benefit.selected).map(benefit => benefit.text);
    return benefits.join(', ');
  };

  useEffect(() => {
    enableLoader();
    Service.fetchClients(
      id,
      (data: Helper) => {
        setHelper(data);
        disableLoader();
      },
      (error) => {
        disableLoader();
        console.error(error);
      }
    );
  }, []);

  const renderPhotoContent = (isMobile?:boolean) => {
    return (
      <div className='flex justify-content-center align-items-center'>
        {helper?.imageSrc ? (
          <div style={{ paddingTop: "20px",marginBottom: !isMobile ? '-30px' : '0px' }}>
            <img
              src={helper?.imageSrc}
              alt={`Profile photo`}
              style={{borderRadius:"20px", width: isMobile ? '100%' : '', height: isMobile ? '222px' : ''}}
            />
          </div>
        ) : (
          <p className='p-0 m-0 font-bold'>No photo uploaded</p>
        )}
      </div>
    );
  };

  const ReviewAndRatingHead = ({
    rating,
    ratingCount,
  }: {
    rating: number;
    ratingCount: number;
  }) => {
    return (
      <div className='flex justify-content-center'>
        <div className='flex items-center gap-2'>
          <img src={star} alt="star" width="20" height="20" />
          <p
            style={{
              fontSize: "14px",
              fontWeight: "300",
              color: "#585858",
              margin: "5px",
              marginLeft: "0px",
            }}
          >{`${rating.toFixed(1)}`}</p>
          <p
            style={{
              fontSize: "14px",
              fontWeight: "700",
              color: "#ffa500",
              margin: "5px",
              marginLeft: "0px",
            }}
          >{`(${ratingCount} ratings)`}</p>
        </div>
      </div>
    );
  };

  const handleHide = () => {
    const searchParams = new URLSearchParams();
    searchParams.set("requestId", requestId.toString());
    if (sessionInfo.data["profileCompleteness"] <= 99) {
      showConfirmationPopup(
        helper?.publicName,
        `Your profile must be 100% complete before you can connect with ${helper?.publicName}`,
        "Complete",
        <RxUpdate style={{ fontSize: "20px" }} />,
        () => {
          dispatch(updateProfileActivationEnabled(true));
        }
      );
      return;
    }
    showConfirmationPopup(
      helper?.publicName,
      `Are you sure you want to Hide ${helper?.publicName}?`,
      "Hide",
      <GrHide style={{ fontSize: "20px" }} />,
      () => {
        executeHideAction();
      }
    );
  };

  const executeHideAction = () => {
    setDialogVisible(false);
    enableLoader();
    let newStatus = c.friendStatus.HIDDEN;
    const payload = {
      friendId: helper?.id,
      status: newStatus,
      ...(requestId ? { requestId } : { invitationMessage: "" }),
    };

    if (requestId) {
      Service.removeFriend(
        payload,
        () => {
          setDialogVisible(false);
          disableLoader();
          navigate("/");
        },
        (error) => {
          disableLoader();
          console.error("Failed to hide profile via removeFriend:", error);
        },
        requestId
      );
    } else {
      Service.addFriend(
        payload,
        () => {
          setDialogVisible(false);
          disableLoader();
          navigate("/");
        },
        (error) => {
          disableLoader();
          console.error("Failed to hide profile via addFriend:", error);
        }
      );
    }
  };

  const handleFavClicked = () => {
    if (sessionInfo.data["profileCompleteness"] <= 99) {
      showConfirmationPopup(
        helper?.publicName,
        `Your profile must be 100% complete before you can connect with ${helper?.publicName}`,
        "Complete",
        <RxUpdate style={{ fontSize: "20px" }} />,
        () => {
          dispatch(updateProfileActivationEnabled(true));
        }
      );
      return;
    }
    showConfirmationPopup(
      helper?.publicName,
      `Are you sure you want to Favourite ${helper?.publicName}`,
      "Favourite",
      <RxUpdate />,
      () => {
        handleAddToFavorites();
      }
    );
    return;
  };

  const handleAddToFavorites = () => {
    setDialogVisible(false);
    enableLoader();
    let newStatus = helper?.friendStatus;
    if (helper?.friendStatus === c.friendStatus.NONE) {
      newStatus = c.friendStatus.PENDING;
    } else if (helper?.friendStatus === c.friendStatus.PENDING) {
      newStatus = c.friendStatus.APPROVED;
    }
    const payload = {
      friendId: helper?.id,
      invitationMessage: "",
      status: newStatus,
    };
    Service.addFriend(
      payload,
      () => {
        setDialogVisible(false);
        disableLoader();
        navigate("/");
      },
      (error) => {
        console.error("Failed to add friend:", error);
      }
    );
  };

  return (
    <div>
      <ConfirmationPopupGreen confirmationProps={confirmationProps} />
      <SidePannel activeindex={0} />
      <HomeHeaderHelper />
        {isMobile && (
          <HorizontalNavigation
          title={helper?.firstName}
          onBackClick={() => {
            navigate(-1);
          }}
        />
        )}
 {!isMobile && (
       <div
       className="flex gap-2 justify-content-around align-items-center w-min mt-4 cursor-pointer"
       style={{
         textWrap: "nowrap",
         border: "1px solid #F1F1F1",
         padding: "10px 25px",
         borderRadius: "20px",
         marginLeft: "19rem",
         paddingTop: "1rem"
       }}
       onClick={(e) => {
         e.preventDefault();
         navigate(-1);
       }}
     >
       <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
       <p
         className="m-0 p-0"
         style={{
           fontWeight: "400",
           fontSize: "14px",
           color: "#585858",
         }}
       >
         Go Back
       </p>
     </div>
 )}
      <div className="responsive-container"
        style={{
          width: !isMobile ? 'calc(100% - 290px)' : '100%',
          position: 'absolute',
          right: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px',
          textAlign: 'center',
          marginTop: !isMobile ? '4rem' : "65px",
          flexWrap:'wrap'
        }}
      >
        <div
          style={{
            minWidth: "70%",
            width:!isMobile ?  'auto': '100%',
            // maxWidth:'80%',
            // minWidth:'80%',
            maxHeight: "auto",
            backgroundColor: "transparent",
            position: "relative",
            boxShadow:!isMobile ? '0 4px 4px 0 rgba(0, 0, 0, 0.25)' : '',
            borderRadius: isMobile && "20px"
          }}
        >
          <div className={!isMobile ? "p-5" : "p-0"}>
            {renderPhotoContent(isMobile)}
            <br />
            <p className='p-0 m-0 font-bold' style={{ color: "#585858",fontSize:'30px',marginTop:'-22px'}}>{helper?.firstName}&nbsp;{helper?.lastInitial}</p>

            <div style={{paddingRight:isMobile && "0px"}} className={styles.childCareContainer}>
              <div style={{padding:isMobile &&  "0px"}} className={styles.childCareBoxOne}>
                <div>
                  <p className='p-4 m-0 text-justify' style={{ color: "#585858", fontSize: '14px' }}>{helper?.aboutMe}
                    <ReviewAndRatingHead
                      rating={helper?.providerRatingsAvg ?? 0}
                      ratingCount={helper?.providerRatingsCount ?? 0}
                    />
                  </p>
                  <div className={`${styles.inputbox} flex align-items-center justify-content-between`}>
                    <div className='flex align-items-center gap-2 p-3'>
                      <img src={locationMy} alt="location" width={12.52} height={16} />
                      {helper && helper.suburb ? helper.suburb : null} {" "}
                    </div>
                    <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}>
                      {helper && helper.distanceInKiloMetersRounded
                        ? helper.distanceInKiloMetersRounded
                        : null}
                      km away</span>
                  </div>
                  <div className={`${styles.inputbox} flex align-items-center justify-content-between`}>
                    <div className='flex align-items-center gap-2 p-3'>
                      <FiAward width={15.5} height={15.5} />
                      Jobs Awarded
                    </div>
                    <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}>  {helper && helper.clientJobsAwarded
                      ? helper.providerJobsCancelled
                      : 0}</span>
                  </div>
                  <div className={`${styles.inputbox} flex align-items-center justify-content-between`}>
                    <div className='flex align-items-center gap-2 p-3'>
                      <MdOutlineCancel width={15.5} height={15.5} color="red" />
                      Jobs Cancelled
                    </div>
                    <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}> {helper && helper.clientJobsCancelled ? helper.clientJobsCancelled : 0}</span>
                  </div>
                </div>
              </div>
              {helper?.isIndividual && (
                <div className={!isMobile ? "p-4 bg-white rounded-lg shadow-md" : "p-1 bg-white rounded-lg shadow-md"} >
                  {/* Family Header */}
                  <h3 className="font-bold p-0 m-0" style={{color:'#585858',fontSize:'30px'}}>{`${helper?.firstName}'s Family`}</h3>
                  {/* Children Section */}
                  <div className="mt-4 p-4 border rounded-md bg-gray-50">
                    <h4 className="font-semibold p-0 m-0 "style={{color:'#585858'}}>Children</h4>
                    {helper?.children?.map((child, index) => (
                      <div key={index} className="mt-2 p-2 border-b last:border-none">
                        <p className="flex items-center gap-2 text-gray-600 text-sm p-0 m-0">
                          <strong>{`${index + 1}.`}</strong> {`${child.firstName} - ${child.ageInYears} yr(s)`}
                        </p>
                        <p className="text-gray-600 text-sm p-0 m-0">Has allergies: <strong>{child.hasAllergies ? 'Yes' : 'No'}</strong></p>
                        {child.allergyDetails && (
                          <p className="text-gray-500 text-sm p-0 m-0">{child.allergyDetails}</p>
                        )}
                        {child.aboutMe && (
                          <p className="italic text-justify p-0 m-0" style={{color: "#585858",fontSize:'14px'}}>{child.aboutMe}</p>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Home Section */}
                  {(helper?.clientJobRules || benefitsProProvidersList()) && (
                    <div className="mt-6 p-4 border rounded-md bg-gray-50">
                      <h4 className="text-gray-700 font-semibold p-0 m-0">{`${helper?.firstName}'s Home`}</h4>
                      {helper?.clientJobRules && (
                        <p className="text-gray-600 text-sm mt-2 p-0 m-0">{helper.clientJobRules}</p>
                      )}
                      {benefitsProProvidersList() && (
                        <p className="text-gray-600 text-sm mt-2 p-0 m-0">{benefitsProProvidersList()}</p>
                      )}
                    </div>
                  )}
                </div>
              )}

            </div>
            {helper?.isBusiness && (
              <>
                <div style={{ color: '#585858' }} className='flex align-items-start font-semibold p-2'>Business Details </div>
                <div style={{paddingRight:isMobile && "0px"}} className={styles.childCareContainer}>
                  <div  style={{padding:isMobile &&  "0px"}} className={styles.childCareBoxOne}>
                    <div
                      style={{
                        flexDirection: "row",
                        gap: "8px",
                        margin: "5px",
                        color: "#585858",
                        fontSize: "14px",
                        fontWeight: "300"
                      }}
                    >
                      <div className={`${styles.inputbox} flex align-items-center justify-content-between`} >
                        <div className='flex align-items-center gap-2 p-3'>
                          {/* <img src={locationMy} alt="location" width={12.52} height={16} /> */}
                          Business Trading Name
                        </div>
                        <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}>
                          {helper?.publicName}</span>
                      </div>
                      <div className={`${styles.inputbox} flex align-items-center justify-content-between`}>
                        <div className='flex align-items-center gap-2 p-3'>
                          {/* <img src={locationMy} alt="location" width={12.52} height={16} /> */}
                          Year Company Established
                        </div>
                        <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}>
                          {helper?.establishYear}</span>
                      </div>
                      <div className={`${styles.inputbox} flex align-items-center justify-content-between`}>
                        <div className='flex align-items-center gap-2 p-3'>
                          {/* <img src={locationMy} alt="location" width={12.52} height={16} /> */}
                          Industry Sector
                        </div>
                        <span style={{ color: "#585858", fontSize: "14px", fontWeight: "300", padding: "1rem" }}>
                          {industrySector()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
            <div className={!isMobile? "flex justify-content-center p-6" : "flex justify-content-center p-1"}>
              <CustomButton
                label={"Hide"}
                className={`${myfamilystyles.customButton}`}
                style={{width: "150px" }}
                onClick={handleHide}
              />
            </div>
            <div
              className="flex justify-content-center align-items-center cursor-pointer"
              style={{
                position: "absolute",
                height: "43px",
                width: "41px",
                top: "-20px",
                right: "6px",
                backgroundColor: "#ffffff",
                borderRadius: "50%",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              }}
            >
              {helper?.friendStatus === 4 ? (
                <FaRegHeart
                  className="cursor-pointer"
                  onClick={() => handleFavClicked()}
                  style={{ height: "20px", width: "20px" }}
                />
              ) : (
                <FaRegHeart
                  style={{
                    color: "red",
                    height: "20px",
                    width: "20px",
                    cursor: "not-allowed",
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientProfile;