import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css"
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import CustomButton from '../../../commonComponents/CustomButton';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import useLoader from '../../../hooks/LoaderHook';
import c from '../../../helper/juggleStreetConstants';
import utils from '../../../components/utils/util';
import { FaFlag } from 'react-icons/fa6';
import { LuBaby } from 'react-icons/lu';
import { IoSchool } from 'react-icons/io5';
import { SiHyperskill } from 'react-icons/si';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';
const Tutoring = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [interestedInFaceToFaceTutoring, setInterestedInFaceToFaceTutoring] = useState<boolean>(false);
  const [interestedInOnlineTutoring, setInterestedInOnlineTutoring] = useState<boolean>(false);
  const [primarySchool, setPrimarySchool] = useState<boolean>(false);
  const [highSchool, setHighSchool] = useState<boolean>(false);
  const [secondarySchool, setSecondarySchool] = useState<boolean>(false);
  const [primaryHighSchoolError, setPrimaryHighSchoolError] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [categoryError, setCategoryError] = useState<boolean>(false);
  const [tutoringError, setTutoringError] = useState<boolean>(false);
  const [qualificationCheckedState, setQualificationCheckedState] = useState<boolean[]>([]);
  const [radioState, setRadioState] = useState<string>('');
  const [selectedJobTypes, setSelectedJobTypes] = useState<number>(0);
  const { disableLoader, enableLoader } = useLoader();
  const [hasChanges, setHasChanges] = useState(false);
  const {isMobile}=useIsMobile();
  useEffect(() => {
    if (isMobile) {
      window.scrollTo(0, 0); // Scroll to Top for Mobile View
    }
  }, [isMobile]);
  useEffect(() => {
    if (sessionInfo.data["provider"]) {
      setInterestedInFaceToFaceTutoring(sessionInfo.data["provider"].interestedInFaceToFaceTutoring || false);
      setInterestedInOnlineTutoring(sessionInfo.data["provider"].interestedInOnlineTutoring || false);
      setPrimarySchool(getPrimarySchoolTutoring());
      setHighSchool(getHighSchoolTutoring());
      setSecondarySchool(getHighSchoolTutoring());
      setSelectedCategory(sessionInfo.data["provider"].tutoringCategory || null);
      setRadioState(sessionInfo.data["provider"].tutoringQualifications.some(q => q.selected) ? 'yes' : 'no');
      setQualificationCheckedState(
        sessionInfo.data["provider"].tutoringQualifications.map((qualification) => qualification.selected || false)
      );
      setSelectedJobTypes(sessionInfo.data['interestedInJobTypes'] || 0);
    }
  }, [sessionInfo]);

  const tutoringLabels = [
    {
      label: "In-home face-to-face Tutoring",
      value: "interestedInFaceToFaceTutoring",
      state: interestedInFaceToFaceTutoring,
      setState: setInterestedInFaceToFaceTutoring
    },
    {
      label: "Online Video Tutoring",
      value: "interestedInOnlineTutoring",
      state: interestedInOnlineTutoring,
      setState: setInterestedInOnlineTutoring
    }
  ];
  const primaryHighSchoolLabels: Array<{
    label: string;
    value: string;
    state: boolean;
    setState: React.Dispatch<React.SetStateAction<boolean>>;
    jobType: string;
  }> = [
      {
        label: "Primary School",
        value: "primarySchool",
        state: primarySchool,
        setState: setPrimarySchool,
        jobType: "PRIMARY_SCHOOL_TUTORING", // This matches the union type.
      },
      ...(sessionInfo.data['country'] === 'au'
        ? [
          {
            label: "High School",
            value: "highSchool",
            state: highSchool,
            setState: setHighSchool,
            jobType: "HIGH_SCHOOL_TUTORING", // This also matches the union type.
          },
        ]
        : [
          {
            label: "Secondary School",
            value: "secondarySchool",
            state: secondarySchool,
            setState: setSecondarySchool,
            jobType: "HIGH_SCHOOL_TUTORING", // Same here.
          },
        ]),
    ];


  const tutoringSkills = [
    {
      label: "Newbie",
      description: "No tutoring experience",
      value: 1
    },
    {
      label: "Apprentice",
      description: "Some previous tutoring experience, mainly unpaid for family & friends",
      value: 2
    },
    {
      label: "Experienced",
      description: "Received payment for tutoring multiple times",
      description2: "Studying to be a teacher",
      value: 3
    },
    {
      label: "Professional",
      description: "Established tutor with deep academic understanding",
      description2: "Qualified teacher",
      description3: "Certified tutor",
      value: 4

    },
  ]
  const handleTutoringCheckboxChange = (setState: React.Dispatch<React.SetStateAction<boolean>>, value: boolean) => {
    setState(value);
    setTutoringError(false);
    setHasChanges(true);
  };

  const handlePrimaryHighSchoolCheckboxChange = (setState: React.Dispatch<React.SetStateAction<boolean>>, value: boolean, jobType: keyof typeof c.jobType) => {
    setState(value);
    setPrimaryHighSchoolError(false);
    setHasChanges(true);
    setSelectedJobTypes(utils.calculateInterestedInJobTypes(sessionInfo.data?.['interestedInJobTypes'], jobType, value ? 'add' : 'remove')
    )
  };

  const handleCategoryChange = (value: number) => {
    setSelectedCategory(value);
    setCategoryError(false);
    setHasChanges(true);
  };


  const handleRadioChange = (value: string) => {
    setRadioState(value);
    if (value === 'no') {
      setQualificationCheckedState(
        sessionInfo.data["provider"]["tutoringQualifications"].map(() => false)
      );
    }
    setHasChanges(true);
  };

  const handleQualificationCheckboxChange = (index: number) => {
    const updatedCheckedState = qualificationCheckedState.map((item, idx) => (idx === index ? !item : item));
    setQualificationCheckedState(updatedCheckedState);
    setHasChanges(true);
  };
  const handleSkip = () => {
    // dispatch(setProfileActivationStep(5));
    dispatch(incrementProfileActivationStep());

  };

  const getPrimarySchoolTutoring = () => {
    return (sessionInfo.data['interestedInJobTypes'] & c.jobType.PRIMARY_SCHOOL_TUTORING) === c.jobType.PRIMARY_SCHOOL_TUTORING
  }
  const getHighSchoolTutoring = () => {
    return (sessionInfo.data['interestedInJobTypes'] & c.jobType.HIGH_SCHOOL_TUTORING) === c.jobType.HIGH_SCHOOL_TUTORING
  }

  const handleNext = () => {
    let hasError = false;
    const selectedQualifications = sessionInfo.data["provider"]["tutoringQualifications"].map((qualification, index) => ({
      ...qualification,
      selected: radioState === 'yes' ? qualificationCheckedState[index] : false,
    }));

    if (!interestedInFaceToFaceTutoring && !interestedInOnlineTutoring) {
      setTutoringError(true);
      hasError = true;
    }

    if (!primarySchool && !highSchool && !secondarySchool) {
      setPrimaryHighSchoolError(true);
      hasError = true;
    }
    if (selectedCategory === null) {
      setCategoryError(true);
      hasError = true;
    }
    if (hasError) {
      return;
    }

    // const interestedInJobTypes = calculateInterestedInJobTypes();
    const payload = {
      ...sessionInfo.data,
      provider: {
        ...sessionInfo.data["provider"],
        interestedInFaceToFaceTutoring,
        interestedInOnlineTutoring,
        tutoringCategory: selectedCategory,
        tutoringQualifications: selectedQualifications,
      },
      interestedInJobTypes: selectedJobTypes,
    };
    enableLoader();
    dispatch(updateSessionInfo({ payload })).finally(() => {
      disableLoader();
      dispatch(incrementProfileActivationStep());
    });
  };

  return (
    <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
      <ProfileCompletenessHeader
        title="Tutoring"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div style={{paddingInline:isMobile && "15px"}}className='flex'>
        <h1
          className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0"
          style={{ fontSize: "18px", color: tutoringError ? 'red' : '#585858' }}
        >
          <span ><LuBaby style={{ color: '#585858', fontSize: '18px' }} /></span>   Select one or both types of Tutoring
        </h1>
      </div>
      <div className='flex flex-wrap justify-center ml-2'>
        {
          tutoringLabels.map((item, index) => (
            <label key={index} className=" flex  gap-2 p-1 cursor-pointer">
              <input
                type="checkbox"
                className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                style={{ fontSize: '18px' }}
                checked={item.state}
                onChange={(e) => handleTutoringCheckboxChange(item.setState, e.target.checked)}
              />
              <span className="text-base txt-clr">
                {item.label}
              </span>

            </label>
          ))
        }
      </div>
      <div style={{paddingInline:isMobile && "15px"}} className='flex'>
        <h1
          className="p-0 m-0 mt-2 txt-clr flex gap-1 flex-wrap font-medium line-height-1"
          style={{ fontSize: "18px", color: primaryHighSchoolError ? 'red' : '#585858' }}
        >
          <span><FaFlag style={{ color: '#585858', fontSize: '18px' }} /></span> Select the jobs you would like to apply for
        </h1>
      </div>

      <div className='flex flex-wrap justify-center ml-2'>
        {
          primaryHighSchoolLabels.map((item, index) => (
            <label key={index} className=" flex  gap-2 p-1 cursor-pointer">
              <input
                type="checkbox"
                className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                style={{ fontSize: '18px' }}
                checked={item.state}
                onChange={(e) => handlePrimaryHighSchoolCheckboxChange(item.setState, e.target.checked, item.jobType as keyof typeof c.jobType)}
              />
              <span className="text-base txt-clr">
                {item.label}
              </span>

            </label>
          ))
        }
      </div>
      <div style={{paddingInline:isMobile && "15px"}} className='flex'>
        <h1
          className="p-0 m-0 mt-3 txt-clr gap-1 flex flex-wrap font-medium line-height-1"
          style={{ fontSize: "18px", color: categoryError ? 'red' : '#585858' }}
        >
          <span><SiHyperskill /></span>     Select the category that best describes your tutoring skill-set
        </h1>
      </div>

      <div style={{marginBottom:"70px"}} className='flex flex-wrap flex-column justify-center mt-1 ml-2'>
        {
          tutoringSkills.map((item, index) => (
            <div key={index} className=" gap-2 p-1 cursor-pointer">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="tutoringCategory"
                  style={{ fontSize: '18px' }}
                  checked={selectedCategory === item.value}
                  onChange={() => handleCategoryChange(item.value)}
                  className='cursor-pointer'
                />
                <span className="text-base text-gray-600 cursor-pointer">
                  {item.label}
                </span>
              </label>
              <ul className="text-base text-gray-600 list-disc p-0 m-0 p-1 pl-6">
                <li className='' style={{ fontSize: '14px' }}>{item.description}</li>
                {item.description2 && <li style={{ fontSize: '14px' }}>{item.description2}</li>}
                {item.description3 && <li style={{ fontSize: '14px' }}>{item.description3}</li>}
              </ul>
            </div>
          ))
        }
      </div>
      <div style={{paddingInline:isMobile && "15px"}} className='flex'>
        <h1
          className="p-0 m-0 mt-3 txt-clr flex gap-1 flex-wrap font-medium line-height-1"
          style={{ fontSize: "18px" }}
        >
          <span><IoSchool style={{ color: '#585858', fontSize: '18px' }} /></span>  Tutoring Qualifications
        </h1>
      </div>

      <div className="flex justify-content-start items-center gap-2 ml-3 mt-1">
        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
          <input
            type="radio"
            name="shared-radio"
            value="yes"
            checked={radioState === 'yes'}
            onChange={() => handleRadioChange('yes')}
            className=" cursor-pointer"
          />
          Yes
        </label>
        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
          <input
            type="radio"
            name="shared-radio"
            value="no"
            checked={radioState === 'no'}
            onChange={() => handleRadioChange('no')}
            className="cursor-pointer"
          />
          No
        </label>
      </div>
      {radioState === 'yes' && (
        <div className="mt-2 ml-3">
          {sessionInfo.data["provider"]["tutoringQualifications"].map((tutoringQualifications, index) => (
            <label key={tutoringQualifications.optionId} className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                // value={lable.text}
                checked={qualificationCheckedState[index]}
                onChange={() => handleQualificationCheckboxChange(index)}
                className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                style={{ fontSize: '18px' }}
              />
              <span className="txt-clr " style={{ fontSize: '16px' }}>
                {tutoringQualifications.text}
              </span>
            </label>
          ))}
        </div>
      )}
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <><i className="pi pi-angle-left"></i>Previous</>
          }
          onClick={() => dispatch(decrementProfileActivationStep())}
          style={{
            backgroundColor: 'transparent',
            color: '#585858',
            width: '156px',
            height: '39px',
            fontSize: '14px',
            fontWeight: '500',
            margin:isMobile && "5px"
          }}
        />
        <div style={{ flexGrow: 1 }} />
        <CustomButton
          className={styles.hoverClass}
          data-skip={hasChanges ? "false" : "true"}
          onClick={hasChanges ? handleNext : handleSkip}
          label={
            <>
              {hasChanges ? 'Next' : 'Skip'}
              <i
                className={`pi pi-angle-${hasChanges ? 'right' : 'right'}`}
                style={{ marginLeft: '8px' }}
              ></i>
            </>
          }
          style={
            hasChanges
              ? {
                backgroundColor: "#FFA500",
                color: "#fff",
                width: "156px",
                height: "39px",
                fontWeight: "800",
                fontSize: "14px",
                borderRadius: "8px",
                border: "2px solid transparent",
                boxShadow: "0px 4px 12px #00000",
                transition:
                  "background-color 0.3s ease, box-shadow 0.3s ease",
                margin : isMobile && "10px"
              }
              : {
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontWeight: "400",
                fontSize: "14px",
                borderRadius: "10px",
                border: "1px solid #F0F4F7",
                margin : isMobile && "10px"
              }
          }
        />
      </footer>
    </div>
  )
}

export default Tutoring