import React, { useState } from "react";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { RadioButton } from "primereact/radiobutton";
import styles from "./styles/employee-benefits.module.css";
import { InputText } from "primereact/inputtext";
import { IoClose } from "react-icons/io5";
import Service from "../../services/services";
import c from "../../helper/juggleStreetConstants";
import environment from "../../helper/environment";
import { AppDispatch, RootState } from "../../store";
import { useDispatch, useSelector } from "react-redux";
import { refreshAccount } from "../../store/tunks/sessionInfoTunk";
import useLoader from "../../hooks/LoaderHook";

interface EmployeeBenefitsProps {
  isDialogVisible: boolean;
  onHide: () => void;
}

interface FormData {
  firstName: string;
  lastName: string;
  companyName: string;
  position: string;
  email: string;
  companySize: string;
  mentionMyName: boolean | null;
  firstNameErrorText: string;
  lastNameErrorText: string;
  companyNameErrorText: string;
  positionErrorText: string;
  emailErrorText: string;
  companySizeErrorText: string;
  mentionMyNameErrorText: string;
}

const COMPANY_SIZES = [
  { label: '50 - 100', value: '50 - 100' },
  { label: '100 - 250', value: '100 - 250' },
  { label: '500 - 1000', value: '500 - 1000' },
  { label: '1000 - 2000', value: '1000 - 2000' },
  { label: 'Over 2000', value: 'Over 2000' },
];

const EmployeeBenefits: React.FC<EmployeeBenefitsProps> = ({
  isDialogVisible,
  onHide,
}) => {
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    companyName: "",
    position: "",
    email: "",
    companySize: "",
    mentionMyName: null,
    firstNameErrorText: "",
    lastNameErrorText: "",
    companyNameErrorText: "",
    positionErrorText: "",
    emailErrorText: "",
    companySizeErrorText: "",
    mentionMyNameErrorText: "",
  });
  const initialFormState: FormData = {
    firstName: "",
    lastName: "",
    companyName: "",
    position: "",
    email: "",
    companySize: "",
    mentionMyName: null,
    firstNameErrorText: "",
    lastNameErrorText: "",
    companyNameErrorText: "",
    positionErrorText: "",
    emailErrorText: "",
    companySizeErrorText: "",
    mentionMyNameErrorText: "",
  };

  const [submitted, setSubmitted] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();


  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      return "Email is required";
    } else if (!emailRegex.test(email)) {
      return "Please include an '@' in the email address";
    }
    return "";
  };

  const validate = () => {
    let isValid = true;
    const newFormData = { ...formData };

    // Company name validation
    if (!formData.companyName || formData.companyName.length < 2) {
      newFormData.companyNameErrorText = "Please enter your company's name";
      isValid = false;
    }

    // First name validation
    if (!formData.firstName || formData.firstName.length < 2) {
      newFormData.firstNameErrorText = "First name is required";
      isValid = false;
    }

    // Last name validation
    if (!formData.lastName || formData.lastName.length < 2) {
      newFormData.lastNameErrorText = "Last name is required";
      isValid = false;
    }

    // Position validation
    if (!formData.position || formData.position.length < 2) {
      newFormData.positionErrorText = "Position is required";
      isValid = false;
    }

    // Email validation
    const emailError = validateEmail(formData.email);
    if (emailError) {
      newFormData.emailErrorText = emailError;
      isValid = false;
    }

    // Company size validation
    if (!formData.companySize) {
      newFormData.companySizeErrorText = "Company size is required";
      isValid = false;
    }

    // Mention name validation
    if (formData.mentionMyName === null) {
      newFormData.mentionMyNameErrorText = "Please select an option";
      isValid = false;
    }

    setFormData(newFormData);
    return isValid;
  };



  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      [`${field}ErrorText`]: field === "email" ? validateEmail(value as string) : "",
    }));
  };


  const isFormValid = () => {
    // Validate all fields including email format
    const emailError = validateEmail(formData.email);

    return (
      formData.firstName.trim() !== '' &&
      formData.lastName.trim() !== '' &&
      formData.companyName.trim() !== '' &&
      formData.position.trim() !== '' &&
      formData.email.trim() !== '' &&
      !emailError && // Check email validation
      formData.companySize !== '' &&
      formData.mentionMyName !== null
    );
  };

  const handleSubmit = async () => {
    if (!validate()) return;

    try {
      enableLoader();
      const payload = {
        name: `${formData.firstName} ${formData.lastName}`,
        subject: "Employee Benefits Program",
        email: formData.email,
        message: `Company Name: ${formData.companyName},
        Contact Name: ${formData.firstName} ${formData.lastName},
        Position: ${formData.position},
        Company Size: ${formData.companySize},
        Mention My Name? ${formData.mentionMyName ? "Yes" : "No"}`,
      };

      // Call the contactUsRequest service
      Service.contactUsRequest(
        payload,
        (response) => {
          // If the contactUsRequest is successful, log the event with extended payload
          const logEventPayload = {
            eventType: c.appEventType.requestedEmployeeBenefits,
            platform: "desktop-web",
            appVersion: environment.getAppVersion(),
            distVersion: environment.getAppVersion(),
            country: environment.getCountry(window.location.hostname),
          };

          Service.logAppEvent(
            () => {
              // Success callback for logEvent
              setSubmitted(true);
              dispatch(refreshAccount());
              disableLoader();
            },
            (error) => {
              // Failure callback for logEvent
              console.error("Failed to log event:", error);
              disableLoader();
            },
            logEventPayload
          );
        },
        (error) => {
          // Handle errors for contactUsRequest
          console.error("Failed to submit contact request:", error);
          setFormData((prev) => ({
            ...prev,
            emailErrorText: "Failed to submit. Please try again.",

          }));
          disableLoader();
        }
      );

    } catch (error) {
      // Catch any other errors
      console.error("Unexpected error:", error);
      setFormData((prev) => ({
        ...prev,
        emailErrorText: "Failed to submit. Please try again.",
      }));
      disableLoader();
    }
  };

  const renderForm = () => (
    <div className={styles.benefitsForm}>
      <div className={styles.benefitsFormRow}>
        <div className="input-container" style={{ maxWidth: "100%" }}>
          <InputText
            id="companyName"
            type="text"
            value={formData.companyName}
            onChange={(e) => handleInputChange("companyName", e.target.value)}
            onFocus={() =>
              handleInputChange("companyName", formData.companyName)
            }
            placeholder=" "
            className={`input-placeholder ${formData.companyNameErrorText
              ? "border-red"
              : formData.companyName
                ? "border-custom"
                : ""
              }`}
          />
          <label
            htmlFor="companyName"
            className={`label-name ${formData.companyName || formData.companyNameErrorText
              ? "label-float"
              : ""
              } ${formData.companyNameErrorText ? "input-error" : ""}`}
          >
            {formData.companyNameErrorText && !formData.companyName
              ? formData.companyNameErrorText
              : "Company Name*"}
          </label>
        </div>
      </div>

      <p style={{ fontSize: "16px", color: "#585858", fontWeight: "500" }}>
        Person in your company responsible for Employee Benefits
      </p>

      <div className={styles.benefitsFormRow}>
        <div className="input-container" style={{ maxWidth: "100%" }}>
          <InputText
            id="firstName"
            type="text"
            value={formData.firstName}
            onChange={(e) => {
              const value = e.target.value;
              if (/^[a-zA-Z\s'-]*$/.test(value) || value === '') {
                handleInputChange("firstName", value);
              }
            }}
            onFocus={() => handleInputChange("firstName", formData.firstName)}
            placeholder=" "
            className={`input-placeholder ${formData.firstNameErrorText
              ? "border-red"
              : formData.firstName
                ? "border-custom"
                : ""
              }`}
          />
          <label
            htmlFor="firstName"
            className={`label-name ${formData.firstName || formData.firstNameErrorText
              ? "label-float"
              : ""
              } ${formData.firstNameErrorText ? "input-error" : ""}`}
          >
            {formData.firstNameErrorText && !formData.firstName
              ? formData.firstNameErrorText
              : "First Name*"}
          </label>
        </div>

        <div className="input-container" style={{ maxWidth: "100%" }}>
          <InputText
            id="lastName"
            type="text"
            value={formData.lastName}
            onChange={(e) => {
              const value = e.target.value;
              if (/^[a-zA-Z\s'-]*$/.test(value) || value === '') {
                handleInputChange("lastName", value);
              }
            }}
            onFocus={() => handleInputChange("lastName", formData.lastName)}
            placeholder=" "
            className={`input-placeholder ${formData.lastNameErrorText
              ? "border-red"
              : formData.lastName
                ? "border-custom"
                : ""
              }`}
          />
          <label
            htmlFor="lastName"
            className={`label-name ${formData.lastName || formData.lastNameErrorText
              ? "label-float"
              : ""
              } ${formData.lastNameErrorText ? "input-error" : ""}`}
          >
            {formData.lastNameErrorText && !formData.lastName
              ? formData.lastNameErrorText
              : "Last Name*"}
          </label>
        </div>
      </div>

      <div className={styles.benefitsFormRow}>
        <div className="input-container" style={{ maxWidth: "100%" }}>
          <InputText
            id="position"
            type="text"
            value={formData.position}
            onChange={(e) => handleInputChange("position", e.target.value)}
            onFocus={() => handleInputChange("position", formData.position)}
            placeholder=" "
            className={`input-placeholder ${formData.positionErrorText
              ? "border-red"
              : formData.position
                ? "border-custom"
                : ""
              }`}
          />
          <label
            htmlFor="position"
            className={`label-name ${formData.position || formData.positionErrorText
              ? "label-float"
              : ""
              } ${formData.positionErrorText ? "input-error" : ""}`}
          >
            {formData.positionErrorText && !formData.position
              ? formData.positionErrorText
              : "Position*"}
          </label>
        </div>

        {/* <div className="input-container" style={{ maxWidth: "100%" }}>
          <InputText
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            onBlur={() => {
              const emailError = validateEmail(formData.email);
              setFormData(prev => ({
                ...prev,
                emailErrorText: emailError
              }));
            }}
            placeholder=" "
            className={`input-placeholder ${formData.emailErrorText
                ? "border-red"
                : formData.email
                  ? "border-custom"
                  : ""
              }`}
          />
          <label
            htmlFor="email"
            className={`label-name ${formData.email || formData.emailErrorText ? "label-float" : ""
              } ${formData.emailErrorText ? "input-error" : ""}`}
          >
            {formData.emailErrorText
              ? formData.emailErrorText
              : "Email*"}
          </label>
        </div> */}

        <div className="input-container mb-2" style={{ maxWidth: "100%" }}>
          <InputText
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder=" "
            className={`input-placeholder ${formData.emailErrorText ? "border-red" : formData.email ? "border-custom" : ""
              }`}
          />
          <label
            htmlFor="email"
            className={`label-name ${formData.email || formData.emailErrorText ? "label-float" : ""} ${formData.emailErrorText ? "input-error" : ""
              }`}
          >
            {formData.emailErrorText ? formData.emailErrorText : "Email*"}
          </label>
        </div>
      </div>

      <div className={styles.benefitsFormRow}>
        <Dropdown
          value={formData.companySize}
          options={COMPANY_SIZES}
          onChange={(e) => handleInputChange("companySize", e.value)}
          placeholder="Select Company Size *"
          className={styles.companySize}
        />
      </div>

      <div className={styles.benefitsRadioGroup}>
        <div style={{ fontWeight: "500" }}>Can we use your name when we contact this person? *</div>
        <div className="flex flex-wrap gap-3">
          <div className="flex align-items-center">
            <RadioButton
              inputId="mentionYes"
              name="mention"
              value={true}
              onChange={(e) => handleInputChange("mentionMyName", e.value)}
              checked={formData.mentionMyName === true}
            />
            <label style={{ fontSize: "16px", color: "#585858", fontWeight: "600" }} htmlFor="mentionYes" className="ml-2">
              Yes
            </label>
          </div>
          <div className="flex align-items-center">
            <RadioButton
              inputId="mentionNo"
              name="mention"
              value={false}
              onChange={(e) => handleInputChange("mentionMyName", e.value)}
              checked={formData.mentionMyName === false}
            />
            <label style={{ fontSize: "16px", color: "#585858", fontWeight: "600" }} htmlFor="mentionNo" className="ml-2">
              No, please don't mention my name
            </label>
          </div>
        </div>
      </div>

      {/* {Object.entries(formData).map(([key, value]) => {
        if (key.endsWith("ErrorText") && value) {
          return (
            <div key={key} className={styles.benefitsError}>
              {value}
            </div>
          );
        }
        return null;
      })} */}

      <div className={styles.benefitsButtons}>
        <button className={styles.benefitsSubmit} onClick={handleSubmit} disabled={!isFormValid()}
          style={{
            backgroundColor: isFormValid() ? "#FFA500" : "#EDE9E9",
            color: isFormValid() ? "#fff" : "#585858",
            cursor: isFormValid() ? 'pointer' : 'not-allowed'
          }}>Submit</button>
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className={styles.benefitsSuccess}>
      <p style={{ fontSize: "16px", color: "#585858", fontWeight: "500" }}>
        Thank you for your referral, we will contact your company and let you
        know how it goes!
      </p>
      <div className={styles.benefitsButtons}>
        <button className={styles.benefitsSubmit} onClick={onHide}>Ok</button>
      </div>
    </div>
  );
  const handleClose = () => {
    setFormData(initialFormState);
    setSubmitted(false);
    onHide();
  };


  return (
    <Dialog
      visible={isDialogVisible}
      onHide={onHide}
      header="Employee Benefits Program"
      className={styles.benefits}
      modal
      content={
        <div className={styles.dialogContent}>
          <IoClose onClick={handleClose} className={styles.closBtn} />
          <div className={styles.benefitsContent}>
            <p className={styles.employeeHeader}>Employee Benefits Program</p>
            <div className={styles.benefitsDescription}>
              The Employee Benefits Program allows companies to pay for Juggle
              Street subscriptions on behalf of their employees, without getting
              involved in the registration, job posting or hiring process.
            </div>

            <div>
              <p
                style={{
                  fontSize: "18px",
                  color: "#585858",
                  marginBottom: "0px",
                  fontWeight: "600",
                }}
              >
                Company Referral
              </p>
              <div className={styles.benefitsDescription}>
                Please complete the form below and we will contact your company to
                discuss the Juggle Street Employee Benefits Program. If your
                company joins, we will refund your personal Juggle Street
                subscription!
              </div>
            </div>

            {!submitted && sessionInfo.data?.["hasRequestedEmployeeBenefits"] == false ? renderForm() : renderSuccess()}
          </div>
        </div>
      }
    />
  );
};
export default EmployeeBenefits;
