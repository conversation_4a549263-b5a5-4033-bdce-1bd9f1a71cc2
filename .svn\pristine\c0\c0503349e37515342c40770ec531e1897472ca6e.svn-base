import React, { CSSProperties, useEffect, useState } from "react";
import { ExtendedProfileTabProps } from "../types";
import { Divider } from "primereact/divider";
import check from "../../../../assets/images/Icons/check-green.png";
import earth from "../../../../assets/images/Icons/earth.png";
import checkStar from "../../../../assets/images/Icons/check-star.png";
import degree from "../../../../assets/images/Icons/degree.png";
import star from "../../../../assets/images/Icons/star.png";
import rank from "../../../../assets/images/Icons/rank.png";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";

const LimitedText = ({
  text,
  limit,
  disableLimit,
  style,
  className,
}: {
  text: string;
  limit: number;
  disableLimit: boolean;
  style?: CSSProperties;
  className?: string;
}) => {
  const displayText =
    disableLimit || text.length <= limit ? text : text.slice(0, limit);
  return (
    <span className={className} style={{ ...style }}>
      {displayText}
    </span>
  );
};

const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{
        border: "1px solid #F1F1F1",
        borderRadius: "30px",
        width: !isMobile ? "90%" : "100%",
        maxWidth: !isMobile ? "90%" : "100%",
      }}
    >
      {children}
    </div>
  );
};
const AboutSection = ({
  text,
  childcare,
  tutoring,
  oddJobs,
  nationality,
}: {
  text: string;
  childcare: boolean;
  tutoring: boolean;
  oddJobs: boolean;
  nationality: string;
}) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 pb-6">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: !isMobile ? "20px" : "16px",
          color: "#585858",
        }}
      >
        Au Pair Experience
      </h1>
      <div className="py-2 px-3">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: "#585858",
            textWrap: "wrap",
            maxWidth: "612px",
          }}
        >
          <span
            className="m-0 p-0 inline-block text-left"
            style={{
              fontWeight: "400",
              fontSize: "16px",
              color: "#585858",
              textWrap: "wrap",
              width: "100%",
            }}
          >
            <LimitedText
              className="m-0 p-0"
              text={text}
              limit={displayLimit}
              disableLimit={textState}
              style={{
                fontWeight: "400",
                fontSize: !isMobile ? "16px" : "14px",
                color: "#585858",
                textWrap: "wrap",
                width: "100%",
              }}
            />
            {text.length >= displayLimit && (
              <span
                className="cursor-pointer hover:text-gray-300"
                style={{
                  fontWeight: "400",
                  fontSize: "12px",
                  color: "#585858",
                  textWrap: "wrap",
                  width: "100%",
                }}
                onClick={() => toggleTextState((prev) => !prev)}
              >
                {" "}
                {textState ? "Show Less." : "Read More..."}
              </span>
            )}
          </span>
        </p>
      </div>
      <div className="flex mt-3">
        <div className="flex-grow-1" />

        <div className="flex flex-column align-items-center">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Nationality
          </h1>
          <div
            className="flex gap-2 justify-content-center align-items-center"
            style={{
              width: "145.28px",
              height: "42.28px",
              borderRadius: "20px",
              backgroundColor: "#F1F1F1",
            }}
          >
            <img src={earth} alt="earth" height="14.4px" width="14.4px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              {nationality}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const Responsibilities = () => {
  return (
    <div className="pl-5 py-3">
      <h1
        className="m-0 p-0"
        style={{ fontSize: "16px", fontWeight: 700, color: "#585858" }}
      >
        Host Family Responsibilities
      </h1>
      <p
        className="m-0 p-0"
        style={{ fontSize: "14px", fontWeight: 400, color: "#585858" }}
      >
        • Interview Au Pair and check references
      </p>
      <p
        className="m-0 p-0"
        style={{ fontSize: "14px", fontWeight: 400, color: "#585858" }}
      >
        • Sight and verify citizenship & visa documents
      </p>
      <p
        className="m-0 p-0"
        style={{ fontSize: "14px", fontWeight: 400, color: "#585858" }}
      >
        • Comply with all applicable government regulations
      </p>
    </div>
  );
};

const Checks = ({ date1, date2 }: { date1: string; date2: string }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Checks
      </h1>
      <div
        className={
          !isMobile ? `${"flex gap-2 mt-2 mx-2"}` : `${"flex gap-2 mt-2 "}`
        }
      >
        {!isMobile ? (
          <img src={checkStar} alt="check" height="23px" width="23px" />
        ) : (
          <img src={checkStar} alt="check" height="18px" width="18px" />
        )}
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "600",
            fontSize: !isMobile ? "16px" : "14px",
            color: "#585858",
          }}
        >
          Working With Children Check
        </p>
      </div>
      <p
        className="m-0 p-0 mt-2 mb-3"
        style={{
          fontWeight: "700",
          fontSize: "12px",
          color: "#179D52",
        }}
      >
        {`Verified on: ${date1} | Expires on: ${date2}`}
      </p>
    </div>
  );
};

const ChildcareQualifications = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Childcare Qualifications
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "600",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const TutoringQualifications = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Tutoring Qualifications
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "600",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};

const ReviewAndRatingHead = ({
  rating,
  ratingCount,
  isSuperHelper,
}: {
  rating: number;
  ratingCount: number;
  isSuperHelper: boolean;
}) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <div className="flex justify-content-between align-items-center">
        <div className="flex flex-column gap-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "20px",
              color: "#585858",
            }}
          >
            Reviews
          </h1>
          <div className="flex gap-1">
            <img src={star} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "300",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
            </p>
          </div>
        </div>
        {isSuperHelper && (
          <div className="flex gap-2 align-items-center">
            <img src={rank} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "18px",
                color: "#585858",
              }}
            >
              Super Helper
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const ReviewAndRatingList = ({
  ratings,
}: {
  ratings: Array<{
    clientFirstName: string;
    clientLastName: string;
    feedback: string;
    ratingDate: string;
    ratingAvg: number;
    clientImageUrl: string;
  }>;
}) => {
  return (
    <div className="flex flex-column px-4 pt-2 mt-2 mb-4">
      {ratings.map((rating, index) => (
        <React.Fragment key={index}>
          <Divider />
          <div className="flex gap-2 my-2">
            <div
              style={{
                height: "38px",
                width: "38px",
                background: "gray",
                borderRadius: "50%",
                overflow: "hidden",
                 minWidth:"38px"
              }}
            >
              <img
                src={rating.clientImageUrl}
                alt="client Image"
                width="100%"
                height="100%"
              />
            </div>
            <div className="flex-grow-1 flex flex-column gap-2">
              <div className="flex">
                <div className="flex-grow-1 flex flex-column">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                  >
                    {new Date(rating.ratingDate).toLocaleDateString("en-GB")}
                  </p>
                </div>
                <div className="flex gap-1">
                  <img src={star} alt="star" width="19.82px" height="18.62px" />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {`${rating.ratingAvg.toFixed(1)}`}
                  </p>
                </div>
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                {rating.feedback}
              </p>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

const AuPairTab: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
  const nat = c.countriesIso.find(
    (country) =>
      country.alpha2.toLowerCase() === helper?.nationality.toLowerCase() ||
      country.value.toLowerCase() === helper?.nationality.toLowerCase()
  );
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{ width: isMobile && "100%" }}
      className="flex flex-column gap-4"
    >
      <Wrapper>
        <AboutSection
          text={helper?.providerMyExperience4 ?? ""}
          childcare={helper?.interestedInChildcareJobs ?? true}
          tutoring={helper?.interestedInTutoringJobs ?? true}
          oddJobs={helper?.interestedInOddJobs ?? true}
          nationality={nat?.label || "Unknown"}
        />
      </Wrapper>
      <Wrapper>
        <Responsibilities />
      </Wrapper>
      {(helper?.certificates.length ?? 0) > 0 && (
        <Wrapper>
          <>
            <Checks
              date1={new Date(
                helper.certificates[0].verificationDate
              ).toLocaleDateString("en-GB")}
              date2={new Date(
                helper.certificates[0].expiryDate
              ).toLocaleDateString("en-GB")}
            />
          </>

          {/* {(helper?.qualifications.length ?? 0) > 0 && (
                    <>
                        {(helper?.certificates.length ?? 0) > 0 && <Divider />}
                        <ChildcareQualifications
                            data={helper.qualifications.map((val) => val.text)}
                        />
                    </>
                )}
                {(helper?.tutoringQualifications.length ?? 0) > 0 && (
                    <>
                        {(helper?.qualifications.length ?? 0) > 0 && <Divider />}
                        <TutoringQualifications
                            data={helper.tutoringQualifications.map((val) => val.text)}
                        />
                    </>
                )} */}
        </Wrapper>
      )}
       {helper?.hasVouches && (
      <Wrapper>
        <>
         
            <div className="px-4 pt-2 mt-2 mb-4">
              <div className="flex justify-content-between align-items-center">
                <div className="flex flex-column gap-1">
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: !isMobile ? "20px" : "16px",
                      color: "#585858",
                    }}
                  >
                    References
                  </h1>
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: !isMobile ? "16px" : "16px",
                      color: "#585858",
                    }}
                  >
                    Available on request. Please contact Customer Service to
                    obtain referee details.
                  </h1>
                </div>
              </div>
            </div>
       
        </>
      </Wrapper>
         )}
      {(helper?.ratingsExtended.length ?? 0) > 0 && (
        <Wrapper>
          <>
            <ReviewAndRatingHead
              rating={helper?.providerRatingsAvg ?? 0}
              ratingCount={helper?.providerRatingsCount ?? 0}
              isSuperHelper={helper?.isSuperProvider ?? false}
            />
            {(helper?.ratingsExtended.length ?? 0) > 0 && (
              <ReviewAndRatingList ratings={helper.ratingsExtended} />
            )}
          </>
        </Wrapper>
      )}
    </div>
  );
};

export default AuPairTab;
