.chatSendContainer {
  display: flex;
  flex-direction: column;
  border: 0.5px solid #dfdfdf;
  padding-inline: 30px;
  padding-block: 20px;
  overflow: auto;
  flex-grow: 1;
}

.chatInput {
  width: 682px !important;
  border-radius: 30px !important;
  height: 56px !important;
  border: 1px solid #dfdfdf !important;
  position: relative;
}

.chatButton {
  background-color: #4caf50;
  color: white;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.chatButton:hover {
  background-color: #45a049;
}
.message {
  margin: 10px 0;
  padding: 10px;
  border-radius: 8px;
  max-width: 70%;
}

.sentMessage {
  background-color: transparent;
  color: #585858;
  border: 1px solid #dfdfdf;
  border-radius: 10px;
}

.receivedMessage {
  background-color: #f1f1f1;
  color: #585858;
  border: 1px solid #dfdfdf;
  border-radius: 10px;

}

.messageList {
  display: flex;
  flex-direction: column;
  padding-inline: 10px;
}
.profileImage {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}
.sentMessageContainer {
  display: flex;
  justify-content: end;
  align-items: end;
  gap: 10px;
}
.receivedMessageContainer {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  align-items: end;
  gap: 10px;
}
.chatInputBtn {
  width: 39px;
  height: 39px;
  border-radius: 50%;
  position: absolute;
}
.customMessageInputContainer {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 137px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0.5px solid #dfdfdf;
}

.customChatInput {
  display: flex;
  width: 100%;
  height: 100px;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #ccc;
  resize: none;
  max-width: 680px;
  height: 57px;
  border-radius: 30px;
  justify-content: start;
  align-items: center;
  padding: 15px;
  font-size: 16px;
  font-weight: 300;
  color: #585858;
}
.customChatInputSecond {
  display: flex;
  width: 100%;
  height: 100px;
  font-size: 14px;
  border-radius: 5px;
  resize: none;
  max-width: 680px;
  height: 57px;
  border-radius: 30px;
  justify-content: start;
  align-items: center;
  padding: 15px;
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  border: none;
  background-color: transparent;
}

.customChatButton {
  bottom: 48px;
  font-size: 14px;
  height: 39px;
  width: 39px;
  border-radius: 50%;
  background-color: #ffa500;
  color: white;
  border: none;
  cursor: pointer;
}
.customChatInput[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #888;
  pointer-events: none;
}

.insuranceNote {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  background-color: #179d52 !important;
}
.messageStatusContainer {
  display: flex;
  justify-content: end;
  font-size: 12px;
  font-weight: 400;
  color: #585858;
}
.receivedMessageTime {
  color: #585858;
  font-size: 12px;
  font-weight: 300;
}
.sentMessageTimeContainer {
  display: flex;
  justify-content: end;
  align-items: end;
  gap: 10px;
  margin-right: 56px;
}
.sentMessageTime {
  color: #585858;
  font-size: 12px;
  font-weight: 300;
}
.receivedMessageTimeContainer {
  display: flex;
  justify-content: start;
  align-items: start;
  gap: 10px;
  margin-left: 56px;
}
.messageTime {
  display: flex;
  align-items: center;
  gap: 2px;
}
.dateGroup {
  margin-bottom: 24px;
}

.dateHeader {
  display: flex;
  align-items: center;
  margin: 16px 0;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  text-decoration: underline;
}
.messageList {
  height: 100%;
  overflow-y: auto;
}
.chatSendContainerMobile {
  display: flex;
  flex-direction: column;
  border: 0.5px solid #dfdfdf;
  padding-block: 20px;
  overflow: auto;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  flex-grow: 1;
}
.customMessageInputContainerMobile {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-block: 20px;
}
.customChatInputMobile {
  display: flex;
  width: 100%;
  height: 100px;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #ccc;
  resize: none;
  height: 57px;
  border-radius: 30px;
  justify-content: start;
  align-items: center;
  padding: 15px;
  font-weight: 300;
  color: #585858;
}
.customChatInputSecondMobile {
  display: flex;
  width: 100%;
  height: 100px;
  font-size: 14px;
  border-radius: 5px;
  resize: none;
  height: 57px;
  border-radius: 30px;
  justify-content: start;
  align-items: center;
  padding: 15px;
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  border: none;
  background-color: transparent;
}


