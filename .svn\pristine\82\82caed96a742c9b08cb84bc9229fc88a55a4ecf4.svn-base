import { useEffect, useState } from "react";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";
import AwardedJobsCard from "../Common/AwardedJobsCard";
import NoJobsCard from "../Common/NoJobsCard";
import { JobLoadingStates, Jobs, ManageJobSectionProps, WeeklySchedule } from "../types";
const JobCardSkeleton = () => (
  <div
    style={{
      borderRadius: '20px',
      border: '1px solid #DFDFDF',
      width: '100%',
      marginBottom: '12px',
      padding: '0',
      backgroundColor: '#fff',
    }}
  >
    {/* Date Section */}
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '12px 20px 8px',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', width: '100px' }}>
        <div
          className="animate-pulse"
          style={{
            width: '60px',
            height: '20px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div
          className="animate-pulse"
          style={{
            width: '40px',
            height: '14px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: '30px',
            height: '14px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
      </div>
    </div>

    {/* Divider */}
    <div
      style={{
        height: '1px',
        backgroundColor: '#DFDFDF',
        margin: '0 0 8px',
      }}
    ></div>

    {/* Header Section */}
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 8px',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', padding: '0 8px' }}>
        <div
          className="animate-pulse"
          style={{
            width: '17px',
            height: '17px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: '80px',
            height: '14px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
      </div>
    </div>

    {/* Job Details */}
    <div style={{ display: 'flex', flexDirection: 'column', padding: '0 16px' }}>
      {[1, 2, 3, 4].map((_, index) => (
        <div
          key={index}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '4px 8px',
          }}
        >
          <div
            className="animate-pulse"
            style={{
              width: '15px',
              height: '15px',
              backgroundColor: '#e0e0e0',
              borderRadius: '4px',
            }}
          ></div>
          <div
            className="animate-pulse"
            style={{
              width: index === 3 ? '120px' : '100px',
              height: '14px',
              backgroundColor: '#e0e0e0',
              borderRadius: '4px',
            }}
          ></div>
          {index === 3 && (
            <div style={{ display: 'flex', gap: '8px' }}>
              {[1, 2].map((_, i) => (
                <div
                  key={i}
                  className="animate-pulse"
                  style={{
                    width: '33px',
                    height: '33px',
                    backgroundColor: '#e0e0e0',
                    borderRadius: '50%',
                    marginLeft: i === 0 ? '0' : '-10px',
                  }}
                ></div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>

    {/* Divider */}
    <div
      style={{
        height: '1px',
        backgroundColor: '#DFDFDF',
        margin: '8px 0',
      }}
    ></div>

    {/* Status Section */}
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '12px 20px',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div
          className="animate-pulse"
          style={{
            width: '50px',
            height: '14px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: '60px',
            height: '14px',
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }}
        ></div>
      </div>
      <div
        className="animate-pulse"
        style={{
          width: '80px',
          height: '28px',
          backgroundColor: '#e0e0e0',
          borderRadius: '20px',
        }}
      ></div>
    </div>
  </div>
);

const AwardedJobs: React.FC<ManageJobSectionProps> = ({
  upComingJobs,
  viewJob,
  isLoading,
}) => {
  const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };
  // const [isLoading, setIsLoading] = useState(true); // Loading state
  const formatJobDateMobile = (jobDate: string | Date | null) => {
    if (!jobDate) {
      return null;
    }
    const dateObj = new Date(jobDate);
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const monthsOfYear = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    // Extract the day of the week
    const day = daysOfWeek[dateObj.getDay()];

    // Extract the date with suffix
    const date = dateObj.getDate();
    const twoDigitDate = date < 10 ? `0${date}` : `${date}`; // Ensure two-digit format
    const suffix =
      date % 10 === 1 && date !== 11
        ? 'st'
        : date % 10 === 2 && date !== 12
          ? 'nd'
          : date % 10 === 3 && date !== 13
            ? 'rd'
            : 'th';

    // Extract the short month
    const month = monthsOfYear[dateObj.getMonth()];

    // Calculate months difference from today
    const today = new Date();
    const monthsDifference =
      (dateObj.getFullYear() - today.getFullYear()) * 12 +
      (dateObj.getMonth() - today.getMonth());

    return {
      day,
      date: `${twoDigitDate}${suffix}`, // Add suffix to two-digit date
      month,
      monthsDifference,
    };
  };

  const formatJobDate = (jobDate: string | Date | null) => {
    if (!jobDate) {
      return null;
    }
    const dateObj = new Date(jobDate);
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthsOfYear = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    // Extract the day of the week
    const day = daysOfWeek[dateObj.getDay()];
    // Extract the date with suffix
    const date = dateObj.getDate();
    const twoDigitDate = date < 10 ? `0${date}` : `${date}`; // Ensure two-digit format
    // Extract the short month
    const month = monthsOfYear[dateObj.getMonth()];
    // Calculate months difference from today
    const today = new Date();
    const monthsDifference =
      (dateObj.getFullYear() - today.getFullYear()) * 12 +
      (dateObj.getMonth() - today.getMonth());

    return {
      day,
      date: `${twoDigitDate}`, // Add suffix to two-digit date
      month,
      monthsDifference,
    };
  };
  
 const awardedJob = upComingJobs.filter(job => job.jobStatus === 2)
  .sort((a, b) => new Date(a.jobDate).getTime() - new Date(b.jobDate).getTime());

  const awardedHelpersImages = (jobId: number) => {
    const result: Array<{ id: number; image: string }> = [];
    const job = awardedJob.find((j) => j.id === jobId);
    if (!job) return [];
    for (const app of job.applicants) {
      if (
        app.applicationStatus === c.jobApplicationStatus.AWARDED &&
        !result.some((r) => r.id === app.applicantId)
      ) {
        result.push({
          id: app.applicantId,
          image: app.applicantImageSrc,
        });
      }
    }

    return result;
  };
  const awardedHelpersNames = (jobId: number) => {
    const result: Array<{ id: number; name: string }> = [];
    const job = awardedJob.find((j) => j.id === jobId);
    if (!job) return [];
    for (const app of job.applicants) {
      if (
        app.applicationStatus === c.jobApplicationStatus.AWARDED &&
        !result.some((r) => r.id === app.applicantId)
      ) {
        result.push({
          id: app.applicantId,
          name: app.applicantFirstName,
        });
      }
    }

    return result;
  };
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div>
      {awardedJob.length > 0 ? (
        // Filter jobs with AWARDED status
        awardedJob.map((job, index) => (
          <AwardedJobsCard
            key={index}
            date={formatJobDate(`${job.jobDate}`)}
            jobOption={job.managedBy === 1 ? "Do It Yourself" : "Juggle Assist"}
            title={job.jobType}
            jobType={
              job.isRecurringJob
                ? "Recurring Job"
                : job.isTutoringJob
                  ? "Tutoring Job"
                  : "One-off Job"
            }
            timeSlot={
              job.isRecurringJob || job.isTutoringJob
                ? `${job.duration} week duration`
                : convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
            }
            location={`${utils.cleanAddress(job.formattedAddress)}`}
            status={job.managedBy === 20 ? 'Awarded' : undefined}
            name={`${job.awardedFirstName} ${job.awardedLastInitial}`}
            image={job.awardedImageSrc}
            onClick={() => viewJob(job.id)}
            images={awardedHelpersImages(job.id)}
            names={awardedHelpersNames(job.id)}
            expiry={job.expiresInDays}
            managedBy={job.managedBy}
          />
        ))
      ) : (
        <div style={{ width: "60%" }} className="mr-4">
          <NoJobsCard description={"No jobs match the specified criteria"} />
        </div>
      )}
    </div>
  ) : (
    <div>

      {isLoading === JobLoadingStates.initial || isLoading === JobLoadingStates.loading ? (
        // Show skeleton cards for mobile during loading
        <div>
          {[...Array(3)].map((_, index) => (
            <JobCardSkeleton key={index} />
          ))}
        </div>
      ) : awardedJob.length > 0 ? (
        // Filter jobs with AWARDED status
        awardedJob.map((job, index) => (
          <AwardedJobsCard
            key={index}
            date={formatJobDateMobile(`${job.jobDate}`)}
            jobOption={job.managedBy === 1 ? "Do It Yourself" : "Juggle Assist"}
            title={job.jobType}
            jobType={
              job.isRecurringJob
                ? "Recurring Job"
                : job.isTutoringJob
                  ? "Tutoring Job"
                  : "One Off Job"
            }
            timeSlot={
              job.isRecurringJob || job.isTutoringJob
                ? `${job.duration} week duration`
                : convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
            }
            location={`${utils.cleanAddress(job.formattedAddress)}`}
            status="Awarded"
            name={`${job.awardedFirstName} ${job.awardedLastInitial}`}
            image={job.awardedImageSrc}
            onClick={() => viewJob(job.id)}
            images={awardedHelpersImages(job.id)}
            names={awardedHelpersNames(job.id)}
            expiry={job.expiresInDays}
          />
        ))
      ) : (
        <div >
          <NoJobsCard description={"No jobs match the specified criteria"} />
        </div>
      )}
    </div>
  )
};

export default AwardedJobs;
