import { Divider } from 'primereact/divider';
import styles from "../../../../containers/Common/styles/rate-helpers.module.css"
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import calender from "../../../../assets/images/Icons/calender.png";
import ChildcareImage from "../../../../assets/images/Icons/childcare-smile.png";
import OddJobImage from "../../../../assets/images/Icons/odd_job.png";
import TutoringImage from "../../../../assets/images/Icons/tutoring-book.png";
import sideArrow from "../../../../assets/images/Icons/side-aroow.png";
import filledStar from "../../../../assets/images/Icons/filled-star.png";
import unfilled from "../../../../assets/images/Icons/unfiled.png";
import { Jobs, MyJobSectionProps } from '../../../Common/manageJobs/types';
import { useEffect, useState } from 'react';
import { Rating } from 'primereact/rating';
import { InputTextarea } from 'primereact/inputtextarea';
import { useSearchParams } from 'react-router-dom';
import useLoader from '../../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../../store';
import { useDispatch, useSelector } from 'react-redux';
import NoJobsCard from '../../../Common/manageJobs/Common/NoJobsCard';
import Service from '../../../../services/services';
import c from '../../../../helper/juggleStreetConstants';
import { LuSchool } from 'react-icons/lu';
import { RiSchoolLine } from 'react-icons/ri';
import useIsMobile from '../../../../hooks/useIsMobile';


const getTutoringLabel = (jobType: number) => {
    switch (jobType) {
        case 1://babysitting
            return "-One Off Job";
        case 2://nannying
            return "-Recurring Job";
        case 4://beforeSchoolCare
            return "Before School Care";
        case 8://afterSchoolCare
            return "After School Care";
        case 12://beforeAndAfterSchoolCare
            return "Before & After School Care";
        case 16://auPair
            return "Au Pair";
        case 32://tutoring
            return "Tutoring";
        case 64:  // PRIMARY_SCHOOL_TUTORING
            return "-Primary School Tutoring";
        case 128: // HIGH_SCHOOL_TUTORING
            return c.countriesIso.some(country => country.alpha2 === 'au') ? "-High School Tutoring" : "-Secondary School Tutoring";
        case 256: // oneOffOddJob
            return "-One Off Odd Job";
        default:
            return "";
    }
};

const renderJobTypeInfo = (job: Jobs) => {
    if (job.jobType === 64 || job.jobType === 128 || job.jobType === 256 || job.jobType === 1 || job.jobType === 2 || job.jobType === 4 || job.jobType === 8 || job.jobType === 12 || job.jobType === 16 || job.jobType === 32) {
        return getTutoringLabel(job.jobType);
    } else {
        return job.isRecurringJob ? "-Recurring Job" : "-One-off Job";
    }
};

const getJobDetails = (currentJob?: Jobs) => {
    const jobToUse = currentJob || { jobType: 0 };

    switch (jobToUse.jobType) {
        case 256:
            return {
                label: "Odd Job",
                image: (
                    <img src={OddJobImage} alt="Odd Job" width={18.33} height={17.5} />
                ),
            };
        case 128:
        case 64:
            return {
                label: "Tutoring",
                image: (
                    <img src={TutoringImage} alt="Tutoring" width={18.33} height={17.5} />
                ),
            };
        case 12:
            return {
                label: "",
                image: (
                    <RiSchoolLine />
                ),
            };
        case 4:
            return {
                label: "",
                image: (
                    <LuSchool />
                ),
            };
        default:
            return {
                label: "Childcare",
                image: (
                    <img
                        src={ChildcareImage}
                        alt="Childcare"
                        width={18.33}
                        height={17.5}
                    />
                ),
            };
    }
};


const formatTime = (time: string) => {
    // Assuming the input time is in 24-hour format
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours);
    const period = hour >= 12 ? "pm" : "am";
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minutes}${period}`;
};
const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
};
function formatDate(dateString: string) {
    const date = new Date(dateString);

    // Array of day names
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    // Array of month names
    const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    // Get day of week
    const dayOfWeek = days[date.getDay()];

    // Get date
    const dateNum = date.getDate();

    // Create ordinal suffix
    function getOrdinalSuffix(n: number) {
        const s = ["th", "st", "nd", "rd"];
        const v = n % 100;
        return n + (s[(v - 20) % 10] || s[v] || s[0]);
    }

    // Get month name
    const monthName = months[date.getMonth()];

    // Combine into desired format
    return `${dayOfWeek} ${getOrdinalSuffix(dateNum)} of ${monthName}`;
}

const RateHelperCard = ({
    index,
    job,
    jobDetails,
    changeSelectedJob,
}: {
    index: number;
    job: Jobs;
    jobDetails: {
        label: string;
        image: JSX.Element;
    };
    changeSelectedJob: (value: Jobs) => void;
}) => {

    const {isMobile}=useIsMobile()

    return !isMobile ? (
        <div className={styles.ratehelperFirstDiv}>
        <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "row" }}>
            <img
                src={job.ownerImageSrc}
                alt={`Rate helper image ${index + 1}`}
                height={81}
                width={86.7}
                className={styles.rateHelperImg}
            />

            <div className={styles.rateNameDiv}>
                <h1 className={styles.rateName}>
                    {job.ownerFirstName} {job.ownerLastInitial}
                </h1>
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        fontSize: "14px",
                        fontWeight: "400",
                        gap: "5px",
                        textDecoration: "underline",
                        color: "#585858",
                        whiteSpace: "nowrap",
                    }}
                >
                    {jobDetails.image}
                    <div style={{
                        display: "flex",
                        flexDirection: job.jobType === 64 || job.jobType === 128 ? "column" : "row",
                    }}>
                        <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                            {jobDetails.label}
                        </span>
                        <p className={styles.subPara} style={{
                            margin: "0px",
                            textWrap: job.jobType === 64 || job.jobType === 128 ? "nowrap" : "nowrap",
                        }}>
                            {renderJobTypeInfo(job)}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div className={styles.jobInfoContainer}>
            <div className={styles.timeAndDateContainer}>
                <div className={styles.timeGet}>
                    <img src={clockStart} alt="Clock" width={14.4} height={14.4} />
                    {/* {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)} */}
                    {job.durationType === null
                        ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                        : job.durationType === 2
                            ? `${job.duration} months duration`
                            : `${job.duration} week duration`}
                </div>
                <div className={styles.dateGet}>
                    <img src={calender} alt="Calender" width={14.4} height={14.4} />
                    {formatDate(job.jobDate)}
                </div>
            </div>
            {/* <div className={styles.adressGet}>
                <img src={home} alt="Location" width={14.4} height={14.4} />
                <p>{job.formattedAddress}</p>
            </div> */}
        </div>

        <Divider layout="vertical" className={styles.rateDivider} />
        <div
            style={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
            }}
        >
            <button
                className={styles.ratehelperButton}
                onClick={() => changeSelectedJob(job)}
            >
                Rate {job.ownerFirstName}{" "}
                <img src={sideArrow} alt="sideArrow" width={6.14} height={12} />
            </button>
        </div>
    </div>
    ):(
        <div onClick={() => changeSelectedJob(job)} className={styles.ratehelperFirstDivMobile}>
        <div style={{ display: "flex", justifyContent: "start", alignItems: "flex-start", flexDirection: "row", paddingInline:"10px" }}>
            <img
                src={job.ownerImageSrc}
                alt={`Rate helper image ${index + 1}`}
                height={81}
                width={86.7}
                className={styles.rateHelperImgMobile}
            />

            <div className={styles.rateNameDivMobile}>
                <h1 className={styles.rateNameMobile}>
                    {job.ownerFirstName} {job.ownerLastInitial}
                </h1>
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        fontSize: "10px",
                        fontWeight: "400",
                        gap: "5px",
                        textDecoration: "underline",
                        color: "#585858",
                        whiteSpace: "nowrap",
                    }}
                >
                    {jobDetails.image}
                    <div style={{
                        display: "flex",
                        flexDirection: job.jobType === 64 || job.jobType === 128 ? "column" : "row",
                    }}>
                        <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                            {jobDetails.label}
                        </span>
                        <p className={styles.subPara} style={{
                            margin: "0px",
                            textWrap: job.jobType === 64 || job.jobType === 128 ? "nowrap" : "nowrap",
                        }}>
                            {renderJobTypeInfo(job)}
                        </p>
                    </div>
                </div>
            </div>
            <div
            style={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
            }}
        >
            <button
                className={styles.ratehelperButtonMobile}
                onClick={() => changeSelectedJob(job)}
            >
                Rate {job.ownerFirstName}{" "}
                <img src={sideArrow} alt="sideArrow" width={6.14} height={12} />
            </button>
        </div>
        </div>
        <div className={styles.jobInfoContainer}>
            <div
             style={{
                alignItems: "center",
                marginLeft: "72px",
                marginBottom:"10px",
                bottom: "20px",
                gap:"5px"
              }}
            className={styles.timeAndDateContainer}>
                <div className={styles.timeGetMobile}>
                  
                    {/* {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)} */}
                    {job.durationType === null
                        ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                        : job.durationType === 2
                            ? `${job.duration} months duration`
                            : `${job.duration} week duration`}
                </div>
                <div className={styles.timeDateMobile}>
                
                    {formatDate(job.jobDate)}
                </div>
            </div>
            {/* <div className={styles.adressGetMobile}>
                <img src={home} alt="Location" width={14.4} height={14.4} />
                <p>{job.formattedAddress}</p>
            </div> */}
        </div>

      
       
    </div>
    )
};
const StepRatingComponent = ({
    job,
    onComplete,
}: {
    job: Jobs;
    onComplete: (
        ratings: { [key: string]: number | string },
        feedback: String
    ) => void;
}) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [ratings, setRatings] = useState({
        "Where you told everything you needed to know(children and duties)?": 0,
        "End of Job - punctuality and communication": 0,
        "How happy are you with the amount you were paid?": 0,
        "How keen would you be to apply for another job?": 0,
    });
    const {isMobile}=useIsMobile()
    const [feedback, setFeedback] = useState<string>("");

    const steps = [
        { label: "Where you told everything you needed to know(children and duties)?", key: "Where you told everything you needed to know(children and duties)?" },
        { label: "End of Job - punctuality and communication", key: "End of Job - punctuality and communication" },
        { label: "How happy are you with the amount you were paid?", key: "How happy are you with the amount you were paid?" },
        {
            label: "How keen would you be to apply for another job?",
            key: "How keen would you be to apply for another job?",
        },
    ];

    const handleRatingChange = (value: number | string) => {
        const currentStepKey = steps[currentStep].key;
        setRatings((prev) => ({
            ...prev,
            [currentStepKey]: value,
        }));
    };

    const handleNext = () => {
        if (currentStep < steps.length - 1) {
            setCurrentStep((prev) => prev + 1);
        } else {
            // Final step - complete ratings
            onComplete(ratings, feedback);
        }
    };

    const handleBack = () => {
        if (currentStep > 0) {
            setCurrentStep((prev) => prev - 1);
        }
    };

    const handleFeedbackChange = (value: string) => {
        setFeedback(value);
    };

    const currentStepDetails = steps[currentStep];

    const renderStepContent = () => {
        const {isMobile}=useIsMobile()
        switch (currentStep) {
            case 0:
            case 1:
            case 2:
            case 3:
                return (
                    <>
                        <Rating
                            value={ratings[currentStepDetails.key] as number}
                            onChange={(e) => handleRatingChange(e.value || 0)}
                            cancel={false}
                            className={styles.ratingsStar}
                            stars={5}
                            onIcon={
                                <img
                                    src={filledStar}
                                    alt="rating on"
                                    width="40px"
                                    height="40px"
                                />
                            }
                            offIcon={
                                <img
                                    src={unfilled}
                                    alt="rating off"
                                    width="40px"
                                    height="40px"
                                />
                            }
                        />
                        <p
                            style={{
                                marginBlock: "20px",
                                color: "#585858",
                                fontSize: "12px",
                                fontWeight: "400",
                            }}
                        >
                            What would you rate out of 5 stars?
                        </p>
                    </>
                );
            case 4:
                return (
                    <>
                        <p style={{ margin: "0px", fontSize: "14px", fontWeight: "400" }}>
                            Optional review which will appear on {`${job.ownerFirstName}'s`} profile
                        </p>
                        <InputTextarea
                            value={feedback}
                            autoResize
                            onChange={(e) => handleFeedbackChange(e.target.value)}
                            rows={5}
                            cols={30}
                            placeholder="Write a quick review...This will appear on the helpers profile and help other parents in your neighbourhood"
                            className={styles.textArea}
                        />
                    </>
                );
        }
    };

    return !isMobile ? (
        <div className={styles.stepRatingContainer}>
        <div className={styles.horizontalTabContainer}>
            {steps.map((step, index) => (
                <>
                    <div
                        key={index}
                        className={`${styles.tabItem} ${index <= currentStep ? styles.activeTab : ""
                            }`}
                    ></div>
                    {index < steps.length - 1 && (
                        <div className={styles.tabSpacing}></div>
                    )}
                </>
            ))}
        </div>

        <div className={styles.ratingStepContent}>
            <label
                style={{
                    marginTop: "15px",
                    marginBottom: currentStep === 4 ? "0px" : "30px",
                }}
                className={styles.ratingLabel}
            >
                {currentStepDetails.label}
            </label>
            {renderStepContent()}
        </div>
        <Divider className={styles.maxDivider} />
        <div className={styles.ratingNavigation}>
            {currentStep > 0 && (
                <button className={styles.navigationButton} onClick={handleBack}>
                    {"<  Go Back"}
                </button>
            )}

            <button
                className={styles.navigationButtonSecond}
                onClick={handleNext}
                disabled={
                    currentStep < 4
                        ? ratings[currentStepDetails.key] === 0
                        : feedback.trim() === ""
                }
            >
                {currentStep < steps.length - 1 ? "Next >" : "Submit >"}
            </button>
        </div>
    </div>
    ):(
        <div className={styles.stepRatingContainer}>
        <div className={styles.horizontalTabContainerMobile}>
            {steps.map((step, index) => (
                <>
                    <div
                        key={index}
                        className={`${styles.tabItem} ${index <= currentStep ? styles.activeTab : ""
                            }`}
                    ></div>
                    {index < steps.length - 1 && (
                        <div className={styles.tabSpacing}></div>
                    )}
                </>
            ))}
        </div>

        <div className={styles.ratingStepContentMobile}>
            <label
                style={{
                    marginTop: "15px",
                    marginBottom: currentStep === 4 ? "0px" : "10px",
                }}
                className={styles.ratingLabel}
            >
                {currentStepDetails.label}
            </label>
            {renderStepContent()}
        </div>
        <div className={styles.ratingNavigationMobile}>
            {currentStep > 0 && (
                <button className={styles.navigationButton} onClick={handleBack}>
                    {"<  Go Back"}
                </button>
            )}

            <button
                className={styles.navigationButtonSecond}
                onClick={handleNext}
                disabled={
                    currentStep < 4
                        ? ratings[currentStepDetails.key] === 0
                        : feedback.trim() === ""
                }
            >
                {currentStep < steps.length - 1 ? "Next >" : "Submit >"}
            </button>
        </div>
    </div>
    ) 
};
const RateFamilies: React.FC<MyJobSectionProps> = ({
    unRatedJobs,
    refresh,
}) => {
    const [selectedJob, setSelectedJob] = useState<Jobs | null>(null);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const { disableLoader, enableLoader } = useLoader();
    const [searchParams] = useSearchParams();
    const {isMobile}=useIsMobile();
    const handleRatingComplete = (
        ratings: { [key: string]: number | string },
        feedback: string
    ) => {
        const payload = {
            category1: ratings["Where you told everything you needed to know(children and duties)?"],
            category2: ratings["End of Job - punctuality and communication"],
            category3: ratings["How happy are you with the amount you were paid?"],
            category4: ratings["How keen would you be to apply for another job?"],
            feedback: feedback,
            jobId: selectedJob?.id, // Assuming there's an id field in the Jobs type
            ratingFor: selectedJob.jobOwnerId,
            ratingBy: sessionInfo.data["id"],
            ratingMode: c.ratingMode.PAGE,
        };

        enableLoader();

        Service.rateParent(
            async (response) => {
                setSelectedJob(null);
                await refresh();
                disableLoader();
            },
            (error) => {
                disableLoader();
            },
            selectedJob.id,
            payload
        );
    };

    useEffect(() => {
        const rateHelperId = searchParams.get("rateJobId");
        const job = unRatedJobs.find((job) => job.id === Number(rateHelperId));
        if (job) {
            setSelectedJob(job);
        }
    }, [unRatedJobs]);
    const renderSelectedJobDetails = (job: Jobs) => {
        const jobDetails = getJobDetails(job);
        return !isMobile ? (
            <>
            <div className={styles.ratehelperSecondDiv}>
                <img
                    src={job.ownerImageSrc}
                    alt={`Rate helper image for ${job.ownerFirstName}`}
                    height={81}
                    width={86.7}
                    className={styles.rateHelperImg}
                />
                <div className={styles.rateNameDiv}>
                    <h1 className={styles.rateName}>
                        {job.ownerFirstName} {job.ownerLastInitial}
                    </h1>
                    <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
                        <div
                            style={{
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "center",
                                fontSize: "14px",
                                fontWeight: "400",
                                gap: "5px",
                                textDecoration: "underline",
                                color: "#585858",
                                whiteSpace: "nowrap",
                            }}
                        >
                            {jobDetails.image}
                            <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                                {jobDetails.label}
                            </span>
                            <p className={styles.subPara} style={{ margin: "0px" }}>
                                {renderJobTypeInfo(job)}
                            </p>
                        </div>
                        <div className={styles.dateGetSec}>
                            <div className={styles.timeGet}>
                                <img src={clockStart} alt="Clock" width={14.4} height={14.4} />
                                {/* {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)} */}
                                {job.durationType === null
                                    ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                                    : job.durationType === 2
                                        ? `${job.duration} months duration`
                                        : `${job.duration} week duration`}
                            </div>
                            <div className={styles.timeGet}>
                                {" "}
                                <img src={calender} alt="calender" width={14.4} height={14.4} />
                                {formatDate(selectedJob.jobDate)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <StepRatingComponent job={job} onComplete={handleRatingComplete} />
        </>
        ):(
            <>
             <div className={styles.backButtonContainer}>
                <div 
                   style={{fontSize:"16px", paddingBottom:"10px", fontWeight:"500",  color:"#585858"}}
                    onClick={() => setSelectedJob(null)}
                >
                    {"< Go Back"}
                </div>
            </div>
            <div className="pt-3 pb-3 pl-3 pr-2" style={{
                boxShadow:"0px 0px 3px 0px #00000040",
                borderRadius:"20px"
               }} 
               >
            <>
            <div className={styles.ratehelperSecondDivMobile}>
                <img
                    src={job.ownerImageSrc}
                    alt={`Rate helper image for ${job.ownerFirstName}`}
                    height={61}
                    width={61}
                    style={{borderRadius:"50%"}}
                />
                <div className={styles.rateNameDivMobile}>
                    <h1 className={styles.rateNameMobile}>
                        {job.ownerFirstName} {job.ownerLastInitial}
                    </h1>
                    <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                        <div
                            style={{
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "center",
                                fontSize: "10px",
                                fontWeight: "400",
                                gap: "5px",
                                textDecoration: "underline",
                                color: "#585858",
                                whiteSpace: "nowrap",
                            }}
                        >
                            {jobDetails.image}
                            <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                                {jobDetails.label}
                            </span>
                            <p className={styles.subPara} style={{ margin: "0px" }}>
                                {renderJobTypeInfo(job)}
                            </p>
                        </div>
                        <div className="flex flex-row gap-2 mt-2">
                            <div className={styles.timeGetMobile}>
                             
                             
                                {job.durationType === null
                                    ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                                    : job.durationType === 2
                                        ? `${job.duration} months duration`
                                        : `${job.duration} week duration`}
                            </div>
                            <div className={styles.timeDateMobile}>
                                {" "}
                              
                                {formatDate(selectedJob.jobDate)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <StepRatingComponent job={job} onComplete={handleRatingComplete} />
        </>
        </div>
        </>
        )
    };
    return !isMobile ? (
        <div className={styles.ratehelperContainer}>
        {!selectedJob ? (
            <div
                className={styles.rateHelperMain}
                style={{
                    border:
                        unRatedJobs.length > 0
                            ? "1px solid rgba(223, 223, 223, 1)"
                            : "none",
                }}
            >
                {unRatedJobs.length > 0 ? (
                    <div className={styles.rateHelperImgContainer}>
                        {unRatedJobs.map((job, index) => {
                            const jobDetails = getJobDetails(job);
                            return (
                                <RateHelperCard
                                    key={index}
                                    index={index}
                                    job={job}
                                    jobDetails={jobDetails}
                                    changeSelectedJob={(v) => setSelectedJob(v)}
                                />
                            );
                        })}
                    </div>
                ) : (
                    <div className="mr-4">
                        <NoJobsCard description="There are currently no jobs awaiting review. " />
                    </div>
                )}
            </div>
        ) : (
            renderSelectedJobDetails(selectedJob)
        )}
    </div>
    ):(
        <div className={styles.ratehelperContainerMobile}>
        {!selectedJob ? (
            <div
                className={styles.rateHelperMain}
            
            >
                {unRatedJobs.length > 0 ? (
                    <div className={styles.rateHelperImgContainer}>
                        {unRatedJobs.map((job, index) => {
                            const jobDetails = getJobDetails(job);
                            return (
                                <RateHelperCard
                                    key={index}
                                    index={index}
                                    job={job}
                                    jobDetails={jobDetails}
                                    changeSelectedJob={(v) => setSelectedJob(v)}
                                />
                            );
                        })}
                    </div>
                ) : (
                    <div >
                        <NoJobsCard description="There are currently no jobs awaiting review. " />
                    </div>
                )}
            </div>
        ) : (
            renderSelectedJobDetails(selectedJob)
        )}
    </div>
    )
}

export default RateFamilies