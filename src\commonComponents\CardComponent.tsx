import React from 'react';
import '../commonStyle/card-component.css';

interface CardComponentProps {
    title: string;
    children: React.ReactNode;
    error?: string;
}

const CardComponent: React.FC<CardComponentProps> = ({ title, children, error }) => {
    return (
        <div className="custom-card card-container">
            <h3 className="card-header">{title}</h3>
            <div className="card-body">
                {children}
                {error && <p className="card-error">{error}</p>}
            </div>
        </div>
    );
};

export default CardComponent;
