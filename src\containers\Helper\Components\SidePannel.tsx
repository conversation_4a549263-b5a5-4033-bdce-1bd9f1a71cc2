import React, { useEffect, useState } from 'react';
import discoverIcon from '../../../assets/images/Icons/discover.png';
import manageJobsIcon from '../../../assets/images/Icons/manage_job.png';
import chatIcon from '../../../assets/images/Icons/chat_helper.png';
import questionIcon from '../../../assets/images/Icons/question.png';
import menuIcon from '../../../assets/images/Icons/menu.png';
import c from '../../../helper/juggleStreetConstants';
import styles from '../../Parent/styles/left-hand-user-panel.module.css';
import { Jobs } from '../../Common/manageJobs/types';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import { Divider } from 'primereact/divider';
import { ProgressBar } from 'primereact/progressbar';
import logoSrc from '../../../assets/images/juggle-st-transparent-card.png';
import altLogoSrc from '../../../assets/images/juggle_small_logo.png';
import {
    toggleSideBar,
    updateChatWindowState,
    updateProfileActivationEnabled,
} from '../../../store/slices/applicationSlice';
import userprofile from '../../../assets/images/sample_profile.png';
import greenDot from '../../../assets/../assets/images/Icons/green-dot.png';
import MembershipPopup from './MembershipPopup';
import HelpdeskManager from '../../../commonComponents/HelpdeskManager';
import { FaCircleExclamation, FaDollarSign } from 'react-icons/fa6';
import { IoBulb, IoMenuOutline } from 'react-icons/io5';
import { IoMdLink } from 'react-icons/io';
import { MdBadge } from 'react-icons/md';
import useIsMobile from '../../../hooks/useIsMobile';
import { Badge } from 'primereact/badge';
import Service from '../../../services/services';
import useLoader from '../../../hooks/LoaderHook';
import { RequestPendindingStats } from '../types';
import WwccAlerts from './WwccAlerts';

interface SidePannelProps {
    activeindex: number;
}
const SidePannel: React.FC<SidePannelProps> = ({ activeindex }) => {
    const [showMembershipPopup, setMembershipPopup] = useState(false);
    const isApproved = (): boolean => {
        const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
        if (!sessionInfo.data) {
            return false;
        }
        return sessionInfo.data?.['accountStatus'] === c.accountStatus?.APPROVED;
    };
    const approved = isApproved();
    // Constants
    const { enableLoader, disableLoader } = useLoader();
    const [upcomingJobs, setUpComingJobs] = useState<Jobs[]>([]);
    const [requestPendingStats, setRequestPendingStats] = useState<RequestPendindingStats>({
        pendingInvitations: 0,
        pendingJobs: 0,
        pendingChatMessages: 0,
        unratedJobs: 0,
    });
    const fetchJobs = async () => {
        enableLoader();
        try {
            const [upcomingJobs] = await Promise.all([
                new Promise<Jobs[]>((resolve, reject) => {
                    Service.gethelperUpComingJobs(
                        (data: Jobs[]) => resolve(data),
                        (error) => reject(error)
                    );
                })
            ]);
            setUpComingJobs(upcomingJobs);
        } catch (error) {
            console.warn('Error fetching jobs:', error);
        } finally {
            disableLoader();
        }
    };
    const fetchPendingStats = async () => {
        Service.requestPendingStatsPartial((data: RequestPendindingStats) => {
            setRequestPendingStats(data);
        }, (error) => {

        });
    };
    useEffect(() => {
        fetchPendingStats();
        fetchJobs();
    }, []);
    const unAwardedCount = upcomingJobs.length;

    const TABS = [
        {
            icon: <img src={discoverIcon} alt='Discover Helpers' width='16.8px' height='16.8px' />,
            text: 'Discover Near Me',
            onclick: (navigate: (path: string) => void) => {
                setActiveTab(0);
                navigate('/helper-home');
                if (isMobile && isOpen) {
                    dispatch(toggleSideBar());
                }
            },
        },
        ...(approved
            ? [
                {
                    icon: (
                        <IoMdLink style={{ width: '22.94px', height: '22.2px', color: 'grey' }} />
                    ),
                    text: (

                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            Connection Requests
                            {requestPendingStats.pendingInvitations > 0 && (
                                <span style={{
                                    backgroundColor: '#FF6359',
                                    color: 'white',
                                    borderRadius: '50%',
                                    padding: '2px 6px',
                                    fontSize: '60%',
                                    marginLeft: '5px',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    minHeight: '15px',
                                    minWidth: '15px',
                                    maxHeight: '15px',
                                    maxWidth: '15px',
                                }}>
                                    {requestPendingStats.pendingInvitations}+
                                </span>
                            )}
                        </div>

                    ),
                    onclick: (navigate: (path: string) => void) => {
                        setActiveTab(1);
                        navigate('/helper-home/public/connections/invitations?');
                        if (isMobile && isOpen) {
                            dispatch(toggleSideBar());
                        }
                    },
                },
            ]
            : []),
        ...(approved
            ? [
                {
                    icon: <img src={manageJobsIcon} alt='My Jobs' width='15px' height='17px' />,
                    text: (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            My Jobs
                            <Badge
                                value={unAwardedCount}
                                style={{
                                    backgroundColor: '#FF6359',
                                    minHeight: '15px',
                                    minWidth: '15px',
                                    maxHeight: '15px',
                                    maxWidth: '15px',
                                    fontSize: '60%',
                                    display: unAwardedCount > 0 ? 'flex' : 'none',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginLeft: '5px'
                                }}
                            />
                        </div>
                    ),
                    onclick: (navigate: (path: string) => void) => {
                        setActiveTab(2);
                        navigate('/helper-home/myjobs?jobId=-1');
                        if (isMobile && isOpen) {
                            dispatch(toggleSideBar());
                        }
                    },
                },
            ]
            : []),
        ...(approved
            ? [
                {
                    icon: (
                        <MdBadge style={{ width: '22.94px', height: '22.2px', color: 'grey' }} />
                    ),
                    text: 'Employee Benefits',
                    onclick: (navigate: (path: string) => void) => {
                        setActiveTab(3);
                        navigate('/helper-home/public/employee-benefts?');
                        if (isMobile && isOpen) {
                            dispatch(toggleSideBar());
                        }
                    },
                },
            ]
            : []),
        ...(approved
            ? [
                {
                    icon: <img src={chatIcon} alt='Chat' width='15px' height='15px' />,
                    text: (
                        <>
                            Chat
                            {requestPendingStats.pendingChatMessages > 0 && (
                                <span
                                    style={{
                                        backgroundColor: '#FF6359',
                                        color: 'white',
                                        borderRadius: '50%',
                                        padding: '2px 6px',
                                        fontSize: '60%',
                                        marginLeft: '5px',
                                        display: 'inline-flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        minHeight: '15px',
                                        minWidth: '15px',
                                        maxHeight: '15px',
                                        maxWidth: '15px',
                                    }}
                                >
                                    {requestPendingStats.pendingChatMessages}
                                </span>
                            )}
                        </>
                    ),
                    requiresFullProfile: true,
                    onclick: (navigate: (path: string) => void) => {
                        const path = '/helper-home/inAppChat';
                        if (sessionInfo.data['membershipType'] == 0) {
                            handleMembershipClick();
                            return;
                        }
                        navigate(path);
                        if (isMobile && isOpen) {
                            dispatch(toggleSideBar());
                        }
                    },
                },
            ]
            : []),
        {
            icon: <FaDollarSign style={{ width: '15.94px', height: '15.2px', color: 'grey' }} />,
            text: 'Pricing',
            onclick: (navigate: (path: string) => void) => {
                setActiveTab(approved ? 5 : 1);
                navigate('/helper-home/public/helper-pricing?');
                if (isMobile && isOpen) {
                    dispatch(toggleSideBar());
                }
            },
        },
        {
            icon: (
                <FaCircleExclamation
                    style={{ width: '15.94px', height: '15.2px', color: 'grey' }}
                />
            ),
            text: 'How it works',
            onclick: (navigate: (path: string) => void) => {
                setActiveTab(approved ? 6 : 2);
                navigate('/helper-home/public/helper-how-it-works?');
                if (isMobile && isOpen) {
                    dispatch(toggleSideBar());
                }
            },
        },

        // {
        //     icon: <img src={rateHelperIcon} alt='Get Started' width='15.94px' height='15.2px' />,
        //     text: 'Get Started',
        //     onclick: (navigate: (path: string) => void) => navigate('/public/onboarding?'),
        // },
        {
            icon: <IoBulb style={{ width: '15.94px', height: '15.2px', color: 'grey' }} />,
            text: 'Learn More',
            onclick: (navigate: (path: string) => void) => {
                setActiveTab(approved ? 7 : 3);
                navigate('/helper-home/public/learn-more?');
                if (isMobile && isOpen) {
                    dispatch(toggleSideBar());
                }
            },
        },
    ];

    const [activeTab, setActiveTab] = useState(activeindex);
    const { sideBarIsOpened: isOpen } = useSelector((state: RootState) => state.applicationState);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const navigate = useNavigate();
    const dispatch = useDispatch<AppDispatch>();
    const { isMobile } = useIsMobile();
    const [touchStart, setTouchStart] = useState<number | null>(null);
    const [touchEnd, setTouchEnd] = useState<number | null>(null);

    // Minimum swipe distance required (in pixels)
    const minSwipeDistance = 50;

    const onTouchStart = (e: React.TouchEvent<HTMLElement>) => {
        setTouchEnd(null);
        setTouchStart(e.targetTouches[0].clientX);
    };

    const onTouchMove = (e: React.TouchEvent<HTMLElement>) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };

    const onTouchEnd = () => {
        if (!touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > minSwipeDistance;

        if (isLeftSwipe && isOpen) {
            dispatch(toggleSideBar());
        }
    };
    // Handle clicks outside the panel
    const handleOutsideClick = (e: MouseEvent | TouchEvent) => {
        if (isMobile && isOpen) {
            const panel = document.querySelector(`.${styles.panel}`);
            const toggleBtn = document.querySelector(`.${styles.toggleButton}`);

            // Check if click is outside both panel and toggle button
            if (
                panel &&
                toggleBtn &&
                !(panel as any).contains(e.target) &&
                !(toggleBtn as any).contains(e.target)
            ) {
                dispatch(toggleSideBar());
            }
        }
    };

    // Add and remove event listeners
    useEffect(() => {
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('touchstart', handleOutsideClick);

        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('touchstart', handleOutsideClick);
        };
    }, [isOpen, isMobile]);
    const toggleSidebar = () => {
        // If on mobile, toggle sidebar state
        if (isMobile) {
            dispatch(toggleSideBar());
        }
    };
    const handleTabClick = (index: number, tab: any) => {
        setActiveTab(index);
        if (tab.onclick) {
            tab.onclick(navigate);
        }
    };
    const handleMembershipClick = () => {
        if (!sessionInfo.loading && sessionInfo.data['membershipType'] == 0) {
            setMembershipPopup(true);
        }
    };
    const renderProfileActivationContent = () => (
        <aside className={`${styles.userPanel} ${!isOpen ? styles.closed : ''}`}>
            <div className={styles.imgContainer}>
                <img
                    loading='lazy'
                    src={logoSrc}
                    alt='User Panel Logo'
                    className={styles.logoDefault}
                />
            </div>
            <div className={styles.textContent}>
                <p className={styles.activationMessage}>
                    <span className={styles.activationMessageHighlight}>
                        Activate your profile{' '}
                    </span>
                    <br />
                    we just need a few more details before your profile will become active.
                </p>
                <p className={styles.progressLabel}>Your progress</p>
                <p className={styles.progressPercentage}>
                    {sessionInfo?.data['profileCompleteness']}% complete
                </p>
                <ProgressBar
                    value={sessionInfo?.data['profileCompleteness']}
                    className={styles.progressBar}
                />
                <div className={styles.ctaWrapper}>
                    <button
                        className={styles.ctaButton}
                        onClick={() => {
                            dispatch(updateProfileActivationEnabled(true));
                        }}
                    >
                        Continue
                        <i className='pi pi-angle-right' style={{ marginLeft: '8px' }}></i>
                    </button>
                </div>
            </div>
        </aside>
    );
    const renderUserProfile = () => (
        <div className={`${styles.userProfile} ${!isOpen ? styles.profileClosed : ''}`}>
            <img
                src={sessionInfo.data?.['defaultImage']?.['scale3ImageUrl'] ?? userprofile}
                alt='Profile'
                className={styles.profilePhoto}
            />
            <img src={greenDot} alt='' className={styles.greenDot} />
            {isOpen && (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        marginLeft: '10px',
                        marginRight: '10px',
                    }}
                >
                    <p className={styles.profileName}>
                        Hi {sessionInfo.loading ? '' : sessionInfo.data['firstName']}
                    </p>

                    {!sessionInfo.loading && sessionInfo?.data['profileCompleteness'] < 100 && (
                        <p
                            onClick={() => dispatch(updateProfileActivationEnabled(true))}
                            style={{
                                margin: '0px',
                                fontFamily: 'Manrope, sans-serif',
                                fontSize: '10px',
                                fontWeight: 300,
                                color: '#585858',
                                cursor: 'pointer',
                                lineHeight: '15px',
                            }}
                        >
                            Activate profile
                        </p>
                    )}
                </div>
            )}
        </div>
    );
    return (
        <>
            <div className='flex justify-content-center' style={{ marginLeft: '10rem' }}>
                {!isMobile && (
                    <WwccAlerts />
                )}
            </div>
            {isMobile && (
                <button
                    onClick={toggleSidebar}
                    className={styles.toggleButton}
                    style={{
                        position: 'fixed',
                        top: '15px',
                        left: '10px',
                        zIndex: 2,
                        padding: '10px',
                        borderRadius: '5px',
                    }}
                >
                    {isOpen ? (
                        <IoMenuOutline fontSize={'30px'} />
                    ) : (
                        <IoMenuOutline fontSize={'30px'} />
                    )}
                </button>
            )}
            {showMembershipPopup && (
                <MembershipPopup
                    isVisible={showMembershipPopup}
                    onHide={() => setMembershipPopup(false)}
                    onXClicked={() => setMembershipPopup(false)}
                />
            )}
            <aside onTouchMove={onTouchMove}
                onTouchStart={onTouchStart}
                onTouchEnd={onTouchEnd} className={`${styles.panel} ${!isOpen ? styles.closed : ''}`}>
                {!sessionInfo?.loading && sessionInfo.data['profileCompleteness'] >= 100 && (
                    <>
                        <img
                            loading='lazy'
                            src={logoSrc}
                            onClick={() => navigate('/helper-home')}
                            alt='User Panel Logo'
                            className={`${styles.logoDefault} ${!isOpen ? styles.hidden : ''}`}
                        />
                        <img
                            loading='lazy'
                            src={altLogoSrc}
                            alt='Alternate logo'
                            className={`${styles.logoAlt} ${!isOpen ? styles.visible : ''}`}
                        />

                        {renderUserProfile()}
                    </>
                )}

                {!sessionInfo.loading &&
                    sessionInfo?.data['profileCompleteness'] < 100 &&
                    renderProfileActivationContent()}
                <Divider className={`${styles.divider} mt-3`} />

                <div
                    className={`${styles.verticalTabContainer} ${!isOpen ? styles.panelClosed : ''
                        }`}
                >
                    <div className={styles.icons}>
                        {TABS.map((tab, index) => (
                            <div key={index} className={styles.iconContainer}>
                                {tab.icon}
                            </div>
                        ))}
                    </div>

                    <div className={styles.tabs}>
                        {TABS.map((tab, index) => (
                            <div
                                key={index}
                                className={`${styles.tabItem} ${activeTab === index ? styles.activeTab : ''
                                    }`}
                                style={{ fontSize: activeTab === index ? 600 : 'normal' }}
                                onClick={() => handleTabClick(index, tab)}
                            >
                                <div
                                    className={styles.spaceDiv}
                                    data-index={index === 0 ? 'first' : index === 3 ? 'last' : ''}
                                />
                                <div className={styles.textContainer}>
                                    <div className="flex">

                                        <span>{tab.text}</span>
                                        <div className={`${activeTab === index ? styles.activeTabDot : ""}`}>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
                <div className={styles.dividerContainer}>
                    <Divider className={styles.dividersecond} />
                </div>
                <div className={styles.simpleIconContainer}>
                    <div className={styles.iconRow}>
                        <div className={styles.iconItem}>
                            <img
                                src={menuIcon}
                                alt='Menu Icon'
                                style={{ width: '18px', height: '18px' }}
                            />
                        </div>
                        <button className={styles.button} type='button'>
                            Explore More
                        </button>
                    </div>

                    <div className={styles.iconRow}>
                        <div className={styles.iconItem}>
                            <img
                                src={questionIcon}
                                alt='Question Icon'
                                style={{ width: '18px', height: '18px' }}
                            />
                        </div>
                        <button className={styles.button} type='button'>
                            Need Help?{' '}
                            <p
                                style={{
                                    fontWeight: '400',
                                    fontSize: '12px',
                                    color: '#585858',
                                }}
                                onClick={(e) => {
                                    e.preventDefault();
                                    dispatch(updateChatWindowState(true));
                                }}
                            >
                                Message Our team
                            </p>
                        </button>
                    </div>
                </div>
            </aside>
            <HelpdeskManager />
        </>
    );
};
export default SidePannel;
