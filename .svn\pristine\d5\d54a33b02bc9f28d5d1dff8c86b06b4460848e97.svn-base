.inputField {
    border-radius: 10px;
    background-color: rgba(240, 244, 247, 1);
    width: 100%;
    max-width: 362px;
    height: 56px;
}

.forgotPasswordLink {
    align-self: start;
    height: 24px;
    width: auto; 
    margin-top: 27px;
    color: #585858;
    text-decoration: none;
    font-size: 16px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    width: max-content !important;
}

.forgotPasswordLink:hover,
.forgotPasswordLink:focus {
    text-decoration: underline;
    width: max-content !important;
    
}

.loginButton {
    display: flex;
    justify-content: center; 
    align-items: center; 
    border-radius: 10px;
    width: 100%; 
    max-width: 362px;
    background-color: rgba(255, 165, 0, 1);
    margin-top: 28px;
    min-height: 46px;
    font-size: 14px;
    color: rgba(255, 255, 255, 1);
    font-weight: 700;
    text-align: center;
    padding: 13px 10px;
    border: none;
    cursor: pointer;
}

.signupPrompt, .joinJuggleStreetContainer {
    color: #179D52;
    font-weight: 600;
    align-self: center;
    margin-top: 28px;
}

.labelText {
    color: #585858;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}

.boldText {
    font-weight: 700;
    cursor: pointer;
}

.loginTitle {
    font-size: 16px;
    font-weight: 600;
    margin-top: -18px;
    text-align: center;
    color: #179D52;
    font-family: 'Poppins', sans-serif;
}

.p-icon-field-right.p-inputtext {
    width: 100%; 
    max-width: 362px;
    height: 56px;
    padding-right: 2.5rem;
}

.passwordErrorLabel {
    height: 24px;
    width: 100%; 
    max-width: 208px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    margin-left: 10px;
}

.emailErrorLabel {
    height: 24px;
    width: 100%; 
    max-width: 301px;
    color: red;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
}

.formError {
    color: red;
    font-weight: bold;
}


@media (max-width: 600px) {
    .inputField,
    .loginButton,
    .forgotPasswordLink,
    .signupPrompt,
    .joinJuggleStreetContainer {
        width: 100%; 
    }

    .inputField {
        height: auto; 
    }

    .loginButton {
        padding: 10px; 
        min-height: auto; 
    }

    .passwordErrorLabel,
    .emailErrorLabel {
        width: auto; 
    }

    .loginTitle {
        font-size: 14px; 
        margin-top: 0; 
    }
}

@media (min-width: 601px) and (max-width: 900px) {
    .inputField,
    .loginButton {
        width: 90%; /* Adjust width to fit the screen */
    }

    .passwordErrorLabel,
    .emailErrorLabel {
        width: auto; /* Adjust width to fit the screen */
    }

    .loginTitle {
        font-size: 15px; /* Adjust font size for tablets */
    }
}
