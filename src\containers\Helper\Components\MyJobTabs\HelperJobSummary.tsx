import React from 'react';
import { Rating } from 'primereact/rating';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';

interface JobProps {
  job: {
    id: number;
    applicationStatus: string;
    jobStatus: string;
    jobType: string;
    jobSubType: string;
    ownerClientType: string;
    isConciergeJob: boolean;
    ownerImageSrc: string;
    ownerPublicName: string;
    conciergeImageUrl: string;
    conciergePublicName: string;
    awardedApplicantGettingHome: string;
    isRatedByProvider: boolean;
    ratingProvided: number;
    helperPaymentMethod: string;
  };
}

const HelperJobSummary: React.FC<JobProps> = ({ job }) => {
  const handleNavigation = (path: string) => {
    window.location.href = path;
  };

  const getJobTypeText = (): string => {
    const baseText = `${job.jobType} (${job.jobSubType})`;
    return job.jobType === 'One-off' ? `${baseText} - SubType Details` : baseText;
  };

  const renderJobStatus = (): React.ReactNode => {
    switch (job.applicationStatus) {
      case 'Pending':
        return <small className="bg-light-red text-white">Response required</small>;
      case 'Applied':
        return <small className="bg-green text-white">Pending response</small>;
      default:
        return null;
    }
  };

  return (
    <Card className="helper-job-summary" onClick={() => handleNavigation(`/job-details/${job.id}`)}>
      <div className="float-right">
        {job.ownerClientType === 'Individual' ? (
          <div className="text-grey">
            <i className="pi pi-dollar"></i> {job.helperPaymentMethod}
          </div>
        ) : (
          <div className="bg-blue text-white">{job.isConciergeJob ? 'Concierge' : 'Business'}</div>
        )}
      </div>

      <div>
        {renderJobStatus()}
        <h3>{getJobTypeText()}</h3>
        <p>Date: {new Date().toLocaleDateString()}</p>
      </div>

      {job.isRatedByProvider ? (
        <div>
          You Rated:
          <Rating value={job.ratingProvided} readOnly stars={5} cancel={false} />
        </div>
      ) : (
        job.jobStatus === 'Completed' && (
          <Button label="Rate Parent" onClick={() => handleNavigation(`/rate/${job.id}`)} />
        )
      )}
    </Card>
  );
};

export default HelperJobSummary;
