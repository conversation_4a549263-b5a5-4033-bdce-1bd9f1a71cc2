.overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background-color: white;
  border-radius: 20px;
  width: 100%;
  max-width: 620px;
  margin: 0 1rem;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(to right, #f97316, #22c55e);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  height: 59px;
}

.title {
  color: white;
  font-size: 30px;
  font-weight: 700;
}

.closeButton {
  color: #585858;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  background-color: #ffffff;
  margin-top: -53px;
  height: 22.87px;
  width: 22.26px;
  font-size: 17px;
  display: flex;
  align-items: center;
}

/* .closeButton:hover {
  opacity: 0.7;
} */

.content {
  padding: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
}

.sectionTitle {
  font-size: 20px;
  color: #4b5563;
  font-weight: 700;
}

.shiftsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 10px;
}

.shiftItem {
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
}

.shiftInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dayLabel {
  width: 2rem;
  color: #4b5563;
  font-weight: 700;
  font-size: 16px;
}

.timeContainer {
  display: flex;
  align-items: center;
}

.timeBox {
  background-color: transparent;
  color: #4b5563;
}

.selectButton {
  position: relative;
  width: 155px;
  height: 51px;
  padding: 0.5rem 1rem;
  border-radius: 10px;
  border: 1px solid #d1d5db;
  color: #6b7280;
  background-color: transparent;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.selectButton.selected {
  border: 3px solid #179d52;
  color: #179d52;
  font-weight: 700;
  box-shadow: "0px 4px 4px rgba(0, 0, 0, 0.25)";
}

.checkmark {
  position: absolute;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #179d52;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
}

.footer {
  display: flex;
  float: inline-end;
  margin-top: 2rem;
  gap: 20px;
  margin-bottom: 20px;
}
.circle {
  background-color: #fff;
  border: 1px solid #dfdfdf;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
}
.selectAllButton {
  background-color: #ffa500;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  width: 154px;
}

.awardButton {
  background-color: #e5e7eb;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  border: none;
  font-size: 16px;
  font-weight: 700;
  text-decoration: underline;
}
.enabledButton {
  background-color: #ffa500;
}
.imageContainer {
  /* position: relative; */
  width: 49px;
  height: 46px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #dfdfdf;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  max-width: 49px;
}

.imageContainer.selected {
  border: 1px solid #179d52;
}

.shiftImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.Imgcheckmark {
  position: relative;
  top: -45px;
  right: -35px;
  background-color: white;
  color: #179d52;
  font-size: 14px;
  font-weight: bold;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid green;
}
.avatarDiv img {
  border: 2px solid #179d52;
}
.overlayMobile {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.dialogMobile {
  background-color: white;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  width: 100%;
  position: fixed;
  bottom: 0;
  
}
.headerMobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  height: 59px;
  padding-left: 20px;
  padding-top: 20px;
}
.titleMobile {
  color: #585858;
  font-size: 22px;
  font-weight: 700;
  margin: 0px;
}
.closeButtonMobile {
  color: #585858;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  background-color: #ffffff;
  margin-top: -70px;
  width: 28px;
  height: 28px;
  margin-right: 10px;
  font-size: 23px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 0px 6px 0px #00000040;

}
.shiftImageMobile {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}
.imageContainerMobile {
  width: 61px;
  height: 61px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #dfdfdf;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.dayLabelMobile {
  color: #585858;
  font-weight: 700;
  font-size: 14px;
}
.selectButtonMobile{
  border: 1px solid #585858;
  width: max-content;
  height: max-content;
  padding-inline: 10px;
  padding-block: 7px;
  border-radius: 20px;
}
.selectedMobile{
  border: none;
  width: max-content;
  height: max-content;
  padding-inline: 10px;
  padding-block: 7px;
  border-radius: 20px;
}
.shiftItemMobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #585858;
  border-radius: 10px;
  padding: 8px;
}
.shiftsListMobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.selectedShiftBorder {
  border: 2px solid #179D52;
}
.selectedImageBorder{
  width: 61px;
  height: 61px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #179D52;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.footerMobile {
  display: flex;
  float: inline-start;
  margin-top: 10px;
  gap: 10px;
  margin-bottom: 20px;
}
.awardButtonMobile{
  background-color: #dfdfdf;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  font-weight: 500;
}
.enabledButtonMobile {
  background-color: #ffa500;
}
