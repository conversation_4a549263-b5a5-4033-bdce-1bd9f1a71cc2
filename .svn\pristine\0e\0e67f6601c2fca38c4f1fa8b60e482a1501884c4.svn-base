import React from "react";
import emptyFile from "../../../../assets/images/Icons/empty-file.png";
import useIsMobile from "../../../../hooks/useIsMobile";

interface NoJobsCardProps {
  icon?: string;
  description?: string;
}

const NoJobsCard: React.FC<NoJobsCardProps> = ({
  icon = emptyFile,
  description = "",
}) => {
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div
    className={`h-min mb-5`}
    style={{ width: "100%", backgroundColor: "#F1F1F1", borderRadius: "15px" ,marginRight:''}}
  >
    <div
      className={`flex flex-column align-items-center justify-content-center p-7 `}
    >
      <div className="flex justify-content-center align-items-center  rounded-full bg-gray-100">
        {icon && (
          <div className={`no-jobs-icon`}>
            <img
              src={icon}
              alt="No Results Icon"
              style={{ width: "41px", height: "41px" }}
            />
          </div>
        )}
      </div>
      <div className="">
        <p className="text-sm text-gray-500">{description}</p>
      </div>
    </div>
  </div>
  ):(
    <div
    className={`h-min mb-5 mt-3`}
    style={{ width: "", backgroundColor: "#F1F1F1", borderRadius: "15px"}}
  >
    <div
      className={`flex flex-column align-items-center justify-content-center p-7 `}
    >
      <div className="flex justify-content-center align-items-center  rounded-full bg-gray-100">
        {icon && (
          <div className={`no-jobs-icon`}>
            <img
              src={icon}
              alt="No Results Icon"
              style={{ width: "41px", height: "41px" }}
            />
          </div>
        )}
      </div>
      <div className="">
        <p style={{textAlign:"center"}} className="text-sm  font-bold text-gray-500 ">{description}</p>
      </div>
    </div>
  </div>
  )
};

export default NoJobsCard;
