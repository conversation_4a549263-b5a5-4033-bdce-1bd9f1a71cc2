/* AccountLayout.module.css */

.dialogContainer {
  width: 100%; /* Use full width */
  max-width: 1200px; /* Optional max width */
  background-color: transparent;
  display: flex;
  flex-direction: column; /* Stack vertically on small screens */
  overflow: hidden; /* Prevent content overflow */
  position: relative; /* Position for absolute elements */
}
.headerTitle {
  font-weight: 500;
  font-size: 30px;
  line-height: 45px;
  color: #585858;
}

/* Add responsiveness to the left pane */
@media (min-width: 768px) {
  .dialogContainer {
    flex-direction: row; /* Side by side layout on larger screens */
  }
}

/* Left pane styles */
.leftPane {
  /* border-right: 1px solid #f5f3f3; */
  width: min-content;
}

/* Right pane styles */
.rightPane {
  flex: 1; /* Allow it to take the remaining space */
}

.rightPane {
  width: 350px;
  overflow-x: scroll;
}

@media (min-width: 768px) {
  .rightPane {
    width: 500px;
  }
}
@media (min-width: 1440px) {
  .rightPane {
    width: 900px;
  }
}
/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 39px;
  font-size: 30px;
  font-weight: 500;
  color: #585858;
  line-height: 45px;
  padding-top: 10px;
  border-bottom: 1px solid #f5f3f3;
}

.headerTitle {
  margin: 0; /* Remove default margin */
  font-size: 1.5rem; /* Increase title size */
}

/* Close button styles */
.closeButton {
  background-color: #f44336; /* Red color for close button */
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.closeButton:hover {
  background-color: #d32f2f; /* Darker red on hover */
}

/* Content styles */
.content {
  overflow: auto; /* Allow content to scroll if too much */
  width: 100%;
  height: 100%;
}
.headerMobile {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  font-weight: 500;
  height: 60px;
  color: #fff;
  background-color: #179D52;
  position: fixed;
  top: 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 98;
  width: 100%;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
.headerTitleMobile {
  font-weight: 500;
  font-size: 22px;
  margin: 0px;
  color: #fff;
}
.dialogContainerMobile {
  width: 100%; /* Use full width */
  max-width: 1200px; /* Optional max width */
  background-color: transparent;
  display: flex;
  flex-direction: row; /* Stack vertically on small screens */
  overflow: hidden; /* Prevent content overflow */
  position: relative; /* Position for absolute elements */
  margin-top: 75px;
}
.rightPaneMobile {
  flex: 1; /* Allow it to take the remaining space */
}
