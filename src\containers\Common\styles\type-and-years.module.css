.tutoringHeader {
  font-size: 30px;
  font-weight: 700;
  color: #585858;
}

.tutoringSelectedText {
  font-weight: 700;
  font-size: 14px;
  border: 2px solid #179d52;
  color: #179d52;
  text-wrap: noWrap;
}
.tutoringUnselectedText {
  font-weight: 500;
  font-size: 14px;
  border: 1px solid #dfdfdf;
  color: #585858;
}
.inlineMessage {
 
  font-size: 14px;
  color: #585858;
  font-weight: 600;
  align-self: center;
  text-wrap: wrap;
}

.verticalLine { 
  border-left: 2px solid #ccc;
  height: 100%;
  margin-right: 16px;
  position: absolute;
  top: 100%;
  left: 7%;
}
.subjectMain{
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
 background-color: #fff;
}
.tutoringHeaderMobile {
  font-size: 24px;
  font-weight: 700;
  color: #585858;
  margin:0px ;
}
.tutoringHeaderMobileSec {
  font-size: 24px;
  font-weight: 700;
  color: #585858;
}
.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}

