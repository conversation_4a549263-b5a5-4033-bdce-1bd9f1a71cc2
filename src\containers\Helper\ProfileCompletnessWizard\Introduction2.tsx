import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import { useEffect, useState } from "react";
import { InputTextarea } from "primereact/inputtextarea";
import { updateSessionInfo } from "../../../store/tunks/sessionInfoTunk";
import CustomButton from "../../../commonComponents/CustomButton";
import { decrementProfileActivationStep, incrementProfileActivationStep } from "../../../store/slices/applicationSlice";
import useLoader from "../../../hooks/LoaderHook";
import { TbManFilled } from "react-icons/tb";
import { PiFirstAidFill, PiGlobeBold } from "react-icons/pi";
import { IoCar } from "react-icons/io5";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";
const Introduction2 = () => {
    const dispatch = useDispatch<AppDispatch>();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [specialNeedsChecked, setSpecialNeedsChecked] = useState<boolean | null>(null);
    const [firstAidChecked, setFirstAidChecked] = useState<boolean | null>(null);
    const [languagesChecked, setLanguagesChecked] = useState<boolean | null>(null);
    const [driverLicenseChecked, setDriverLicenseChecked] = useState<boolean | null>(null);
    const [showSpecialNeedsDetails, setShowSpecialNeedsDetails] = useState<boolean>(false);
    const [showFirstAidDetails, setShowFirstAidDetails] = useState<boolean>(false);
    const [showLanguagesDetails, setShowLanguagesDetails] = useState<boolean>(false);
    const [showDriverLicenseDetails, setShowDriverLicenseDetails] = useState<boolean>(false);
    const [myExperience, setMyExperience] = useState(sessionInfo.data["provider"]['myExperience2'] || '');
    const minCharLimit = 50;
    const [showError, setShowError] = useState(false);
    const { disableLoader, enableLoader } = useLoader();
    const [firstAidCheckedState, setFirstAidCheckedState] = useState<boolean[]>(
        sessionInfo.data["provider"]["firstAid"].map(item => item.selected)
    );
    const [languagesCheckedState, setLanguagesCheckedState] = useState<boolean[]>(
        sessionInfo.data["provider"]["spokenLanguages"].map(item => item.selected)
    );
    const [transportCheckedState, setTransportCheckedState] = useState<boolean[]>(
        sessionInfo.data["provider"]["transport"].map(item => item.selected)
    );
    const [isChanged, setIsChanged] = useState<boolean>(false);
    const {isMobile}=useIsMobile();
    useEffect(() => {
        if (sessionInfo.data["provider"]["firstAid"].some(item => item.selected)) {
            setFirstAidChecked(true);
            setShowFirstAidDetails(true);
        }
        if (sessionInfo.data["provider"]["spokenLanguages"].some(item => item.selected)) {
            setLanguagesChecked(true);
            setShowLanguagesDetails(true);
        }
        if (sessionInfo.data["provider"]["transport"].some(item => item.selected)) {
            setDriverLicenseChecked(true);
            setShowDriverLicenseDetails(true);
        }
        if (sessionInfo.data["provider"]['myExperience2']) {
            setSpecialNeedsChecked(true);
            setShowSpecialNeedsDetails(true);
        }
    }, [sessionInfo]);

    const handleInputChange = (e) => {
        const value = e.target.value;
        const countWords = (text) => {
            return text.trim().split(/\s+/).filter((word) => word.length > 0).length;
        };
        const totalWords = countWords(value);
        if (totalWords <= 120) {
            setMyExperience(value.trim().length > 0 ? value : '');
        }
        setIsChanged(true);
    };

    const handleFirstAidCheckboxChange = (index: number) => {
        const newCheckedState = firstAidCheckedState.map((item, idx) =>
            idx === index ? !item : item
        );
        setFirstAidCheckedState(newCheckedState);
        setIsChanged(true);

    };

    const handleLanguagesCheckboxChange = (index: number) => {
        const newCheckedState = languagesCheckedState.map((item, idx) =>
            idx === index ? !item : item
        );
        setLanguagesCheckedState(newCheckedState);
        setIsChanged(true);

    };

    const handleTransportCheckboxChange = (index: number) => {
        const newCheckedState = transportCheckedState.map((item, idx) =>
            idx === index ? !item : item
        );
        setTransportCheckedState(newCheckedState);
        setIsChanged(true);

    };

    const handleNext = () => {
        if (showSpecialNeedsDetails && myExperience.length < minCharLimit) {
            setShowError(true);
            return;
        } setShowError(false);

        const updatedFirstAid = sessionInfo.data["provider"]["firstAid"].map((item, idx) => ({
            ...item,
            selected: firstAidCheckedState[idx]
        }));
        const updatedLanguages = sessionInfo.data["provider"]["spokenLanguages"].map((item, idx) => ({
            ...item,
            selected: languagesCheckedState[idx]
        }));
        const updatedTransport = sessionInfo.data["provider"]["transport"].map((item, idx) => ({
            ...item,
            selected: transportCheckedState[idx]
        }));
        const payload = {
            ...sessionInfo.data,
            provider: {
                ...sessionInfo.data["provider"],
                firstAid: updatedFirstAid,
                spokenLanguages: updatedLanguages,
                transport: updatedTransport,
                myExperience2: myExperience,
            }
        };
        enableLoader();
        dispatch(updateSessionInfo({ payload })).finally(() => {
            disableLoader();
            dispatch(incrementProfileActivationStep());
        });
    };

    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };


    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ProfileCompletenessHeader
                title="Introduction"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={handleprev}
            />
            <div className={`${isMobile ? "p-3 mb-6" : "max-w-4xl mx-auto p-3"}` }>
                <section className="mb-4">
                    <div className="flex gap-1">
                        <h1
                            className="p-0 m-0 mb-1 txt-clr flex flex-wrap font-medium line-height-1"
                            style={{ fontSize: "16px", color: showError ? 'red' : '#585858' }}
                        >
                            <span><TbManFilled style={{ fontSize: "18px", color: "#585858" }} /></span>
                            Do you have Special Needs training?
                        </h1>
                    </div>

                    <div className="flex gap-2 mb-2 ml-2">
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={specialNeedsChecked === true}
                                onChange={() => {
                                    setSpecialNeedsChecked(true)
                                    setShowSpecialNeedsDetails(true)
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">Yes</span>
                        </label>
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={specialNeedsChecked === false}
                                onChange={() => {
                                    setSpecialNeedsChecked(false);
                                    setShowSpecialNeedsDetails(false);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">No</span>
                        </label>
                    </div>
                    {showSpecialNeedsDetails && (
                        <>
                            <InputTextarea
                                autoResize
                                value={myExperience}
                                required
                                onChange={handleInputChange}
                                rows={3}
                                cols={30}
                                className={styles.inputTextareafamily}
                                placeholder="Describe your special needs training & experience"
                            />
                            <p
                                style={{
                                    fontSize: '14px',
                                    color: myExperience.length < minCharLimit ? 'red' : 'green',
                                    fontWeight: '400',
                                }}
                            >
                                {myExperience.length < minCharLimit &&
                                    `${minCharLimit - myExperience.length} characters remaining`}
                            </p>
                        </>

                    )}
                </section>
                <section className="mb-4">
                    <div className="flex gap-1">
                        <h1
                            className="p-0 m-0 mb-1 gap-1 txt-clr flex  flex-wrap font-medium line-height-1"
                            style={{ fontSize: "16px" }}
                        >
                            <span><PiFirstAidFill style={{ fontSize: "16px", color: "#585858" }} /></span> Do you have First Aid training?
                        </h1>
                    </div>
                    <div className="flex gap-2 mb-2 ml-2">
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={firstAidChecked === true}
                                onChange={() => {
                                    setFirstAidChecked(true);
                                    setShowFirstAidDetails(true);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">Yes</span>
                        </label>
                        <label className="flex justify-content-start txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={firstAidChecked === false}
                                onChange={() => {
                                    setFirstAidChecked(false);
                                    setShowFirstAidDetails(false);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">No</span>
                        </label>
                    </div>
                    {showFirstAidDetails && (
                        <div className="flex flex-column ml-3">
                            {sessionInfo.data["provider"]["firstAid"].map((firstAid, index) => (
                                <label key={firstAid.optionId} className="flex items-center gap-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={firstAidCheckedState[index]}
                                        onChange={() => handleFirstAidCheckboxChange(index)}
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <span className="text-base text-gray-600">
                                        {firstAid.text}
                                    </span>
                                </label>
                            ))}
                        </div>
                    )}
                </section>
                <section className="mb-4">
                    <div className="">
                        <h1
                            className="p-0 m-0 mb-1 gap-1 txt-clr flex flex-wrap font-medium line-height-1"
                            style={{ fontSize: "16px" }}
                        >
                            <span><PiGlobeBold style={{ fontSize: "16px", color: "#585858" }} /></span> Do you speak another language?
                        </h1>
                    </div>
                    <div className="flex gap-2 mb-2 ml-2">
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={languagesChecked === true}
                                onChange={() => {
                                    setLanguagesChecked(true);
                                    setShowLanguagesDetails(true);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">Yes</span>
                        </label>
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={languagesChecked === false}
                                onChange={() => {
                                    setLanguagesChecked(false);
                                    setShowLanguagesDetails(false);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">No</span>
                        </label>
                    </div>
                    {showLanguagesDetails && (
                        <div className="flex flex-column ml-3">
                            {sessionInfo.data["provider"]["spokenLanguages"].map((language, index) => (
                                <label key={language.optionId} className="flex items-center gap-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={languagesCheckedState[index]}
                                        onChange={() => handleLanguagesCheckboxChange(index)}
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <span className="text-base text-gray-600">
                                        {language.text}
                                    </span>
                                </label>
                            ))}
                        </div>
                    )}
                </section>
                <section className="">
                    <div className="flex gap-1">
                        <h1
                            className="p-0 m-0 mb-1 txt-clr gap-1 flex flex-wrap font-medium line-height-1"
                            style={{ fontSize: "16px" }}
                        >
                            <span><IoCar style={{ fontSize: "16px", color: "#585858" }} /></span> Do you have a Driver Licence?
                        </h1>
                    </div>

                    <div className="flex gap-2 mb-2 ml-2">
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={driverLicenseChecked === true}
                                onChange={() => {
                                    setDriverLicenseChecked(true);
                                    setShowDriverLicenseDetails(true);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">Yes</span>
                        </label>
                        <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                            <input
                                type="radio"
                                checked={driverLicenseChecked === false}
                                onChange={() => {
                                    setDriverLicenseChecked(false);
                                    setShowDriverLicenseDetails(false);
                                }}
                                className="w-5 h-5 cursor-pointer"
                            />
                            <span className="text-sm text-gray-600">No</span>
                        </label>
                    </div>
                    {showDriverLicenseDetails && (
                        <div className="flex flex-column ml-3">
                            {sessionInfo.data["provider"]["transport"].map((transport, index) => (
                                <label key={transport.optionId} className="flex items-center gap-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={transportCheckedState[index]}
                                        onChange={() => handleTransportCheckboxChange(index)}
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <span className="text-base text-gray-600">
                                        {transport.text}
                                    </span>
                                </label>
                            ))}
                        </div>
                    )}
                </section>
            </div>
            <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <><i className="pi pi-angle-left"></i>Previous</>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: 'transparent',
                        color: '#585858',
                        width: '156px',
                        height:!isMobile ? '39px' : "0px",
                        fontSize: '14px',
                        fontWeight: '500',
                       marginTop : isMobile && "5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />
                <CustomButton
                    className={styles.hoverClass}
                    data-skip={isChanged ? 'false' : 'true'}
                    onClick={isChanged ? handleNext : handleSkip}
                    label={
                        <>
                            {isChanged ? 'Next' : 'Skip'}
                            <i
                                className={`pi pi-angle-${isChanged ? 'right' : 'right'}`}
                                style={{ marginLeft: '8px' }}
                            ></i>
                        </>
                    }
                    style={
                        isChanged
                            ? {
                                backgroundColor: '#FFA500',
                                color: '#fff',
                                width: '156px',
                                height:!isMobile ? '39px' : "0px",
                                fontWeight: '800',
                                fontSize: '14px',
                                borderRadius: '8px',
                                border: '2px solid transparent',
                                boxShadow: '0px 4px 12px #00000',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                               margin : isMobile && "10px"
                            }
                            : {
                                backgroundColor: 'transparent',
                                color: '#585858',
                                width: '156px',
                                height:!isMobile ? '39px' : "0px",
                                fontWeight: '400',
                                fontSize: '14px',
                                borderRadius: '10px',
                                border: '1px solid #F0F4F7',
                                margin : isMobile && "10px"
                            }
                    }
                />
            </footer>
        </div>
    )
}

export default Introduction2