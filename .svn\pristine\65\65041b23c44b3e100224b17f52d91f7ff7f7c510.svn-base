import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import styles from "../../../styles/job-pricing.module.css";
import { useState, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import { Divider } from "primereact/divider";
import NumberInput from "../../../../../commonComponents/NumberInput";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import clockForword from "../../../../../assets/images/Icons/clock-forward.png";
import { Next } from "../Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

function OverTimeSection() {
  const { isMobile } = useIsMobile();
  return isMobile ? <OverTimeSectionMobile /> : "";
}

export default OverTimeSection;

const useJobTypeHook = () => {
  const { payload, prev, next, setpayload } = useJobManager();
  const [extraPriceEditDisabled, setExtraPriceEditDisabled] = useState(true);
  const [extraPayPopup, setExtraPayPopup] = useState(false);
  const [errorMessage2, setErrorMessage2] = useState("");
  const [extraPaySelection, setExtraPaySelection] = useState(payload.willPayOvertime === null ? -1 : payload.willPayOvertime ? 0 : 1);
  const [extraPrice, setExtraPrice] = useState(payload.overtimeRate ? String(payload.overtimeRate) : String(payload.price || ""));
  const [price] = useState(payload.price ? String(payload.price) : "");

  const getJobHours = () => {
    const jobStart = payload.jobStartTime;
    const jobEnd = payload.jobEndTime;

    const timeStringToMinutes = (timeString) => {
      if (!timeString || typeof timeString !== "string") {
        console.error("Invalid time string:", timeString);
        return 0;
      }
      const [hours, minutes, seconds] = timeString.split(":").map(Number);
      return hours * 60 + minutes + (seconds || 0) / 60;
    };

    const startMinutes = timeStringToMinutes(jobStart);
    const endMinutes = timeStringToMinutes(jobEnd);

    let differenceInMinutes;

    if (startMinutes === endMinutes) {
      differenceInMinutes = 24 * 60;
    } else if (endMinutes > startMinutes) {
      differenceInMinutes = endMinutes - startMinutes;
    } else {
      differenceInMinutes = 24 * 60 - (startMinutes - endMinutes);
    }

    return differenceInMinutes / 60;
  };

  const minPrice = getJobHours() >= 3 ? 25 : 35;

  useEffect(() => {
    if (Number(extraPrice) === 0 && !extraPriceEditDisabled) {
      setErrorMessage2("Please enter the amount you are willing to pay for the extra hours.");
    } else if (Number(extraPrice) < Number(price)) {
      setErrorMessage2("Extra hours rate must be equal to or greater than the base rate.");
    } else {
      setErrorMessage2("");
    }
  }, [extraPrice, extraPriceEditDisabled, price]);

  return {
    payload,
    prev,
    next,
    setpayload,
    extraPriceEditDisabled,
    setExtraPriceEditDisabled,
    extraPayPopup,
    setExtraPayPopup,
    errorMessage2,
    setErrorMessage2,
    extraPaySelection,
    setExtraPaySelection,
    extraPrice,
    setExtraPrice,
    price,
    getJobHours,
    minPrice,
  };
};
const OverTimeSectionMobile = () => {
  const {
    payload,
    prev,
    next,
    setpayload,
    extraPayPopup,
    setExtraPayPopup,
    errorMessage2,
    extraPaySelection,
    setExtraPaySelection,
    extraPrice,
    setExtraPrice,
    price,
    getJobHours,
  } = useJobTypeHook();

  return (
    <div className={styles.JobpricingMain}>
      <Dialog
        visible={extraPayPopup}
        onHide={() => {
          if (Number(extraPrice) > Number(price)) {
            setExtraPrice(String(price));
          }
          setExtraPayPopup(false);
        }}
        focusOnShow={false}
        content={
          <div
            className="flex justify-content-center align-items-center"
            style={{
              maxWidth: "533px",
              width: "80vw",
              maxHeight: "280px",
              height: "80vh",
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "33px",
              position: "relative",
              overflow: "visible",
              userSelect: "none",
            }}
          >
            <div
              style={{
                position: "absolute",
                height: "30px",
                width: "30px",
                borderRadius: "50%",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                top: "-10px",
                right: "10px",
                backgroundColor: "#FFFFFF",
                fontSize: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.preventDefault();
                if (Number(extraPrice) < Number(price)) {
                  setExtraPrice(String(price));
                }
                setExtraPayPopup(false);
              }}
            >
              <IoClose />
            </div>
            <div
              className=" h-full flex flex-column justify-content-center"
              style={{
                borderRadius: "33px",
                overflow: "clip",
                padding: "10px",
              }}
            >
              <div className="flex justify-content-center align-items-center">
                <div className="w-min flex flex-column align-items-center">
                  <NumberInput
                    value={String(extraPrice?.length > 0 ? extraPrice : price)}
                    onChange={(value) => {
                      if (value === "") {
                        setExtraPrice("");
                      } else if (!isNaN(Number(value))) {
                        setExtraPrice(value);
                      }
                    }}
                  />
                  <p
                    className="m-0 p-0 relative mr-6"
                    style={{
                      fontWeight: 400,
                      fontSize: "20px",
                      color: "#585858",
                    }}
                  >
                    Per hour
                  </p>
                </div>
              </div>
              {/* {errorMessage2 !== "" && (
                <p
                  className="m-0"
                  style={{
                    backgroundColor: "#FF6359",

                    borderRadius: "8px",
                    fontSize: "12px",
                    color: "#ffffff",
                    fontWeight: "600",
                    padding: "2px 10px",
                    alignSelf: "center",
                    textAlign: "center",
                  }}
                >
                  {errorMessage2}
                </p>
              )} */}
              <Divider className="my-3" />
              <div className="flex justify-content-center align-items-center">
                <Next
                  disabled={Number(extraPrice) < Number(price)}
                  className="align-self-end mb-2"
                  onClick={(e) => {
                    e.preventDefault();
                    if (Number(extraPrice) < Number(price)) {
                      setExtraPrice(String(price));
                    }
                    setExtraPayPopup(false);
                  }}
                />
              </div>
            </div>
          </div>
        }
      />
      <div className={styles.jobContent}>
        <header
          style={{
            fontSize: "24px",
            fontWeight: "700",
            color: "#585858",
            marginBottom: "10px",
          }}
        >
          Job Summary
        </header>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            gap: "20px",
          }}
        >
          <div>
            <p
              style={{
                margin: "0px",
                fontSize: "16px",
                fontWeight: "500",
                color: "#585858",
                marginBottom: "5px",
              }}
            >
              Job length
            </p>
            <div className={styles.jobLength}>
              <img src={clockForword} alt="clockForword" width="20px" height="18px" />
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: 700,
                  fontSize: "14px",
                  color: "#fff",
                }}
              >
                {getJobHours().toFixed(2)} hrs
              </p>
            </div>
          </div>

          <div>
            <p
              style={{
                margin: "0px",
                fontSize: "16px",
                fontWeight: "500",
                color: "#585858",
                marginBottom: "5px",
              }}
            >
              Job Cost
            </p>
            <div className={styles.jobCost}>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: 700,
                  fontSize: "14px",
                  color: "#fff",
                }}
              >
                ${payload.paymentType === 2 ? (Number(price) * getJobHours()).toFixed(2) : Number(price).toFixed(2)} Job Total
              </p>
            </div>
          </div>
        </div>
        <br />
        <Divider />
        <br />
        {extraPaySelection !== 1 && payload.paymentType !== 1 && (
          <div className="w-full flex justify-content-between ">
            {/* <p
            className="m-0 p-0"
            style={{
              fontWeight: 700,
              fontSize: "30px",
              color: "#585858",
            }}
          >
            Job Summary
          </p> */}
            <div className="flex gap-4 lg:gap-4">
              {extraPaySelection === 0 && (
                <div className="flex flex-column gap-1 text-center">
                  <div className="flex gap-2">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 500,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Extra hours rate
                    </p>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                    }}
                  >
                    <button className={styles.extraHour}>${extraPrice} extra per hour</button>
                    <div
                      className="flex justify-content-center align-items-center"
                      style={{
                        fontSize: "16px",
                        fontWeight: "500",
                        color: "#585858",
                        textDecoration: "underline",
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        setExtraPayPopup(true);
                      }}
                    >
                      <p>Edit rate</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {payload.paymentType !== 1 && (
          <div className={styles.extraPayMobile}>
            <div
              className="flex flex-column"
              style={{
                fontWeight: 500,
                fontSize: "16px",
                color: "#585858",
                marginTop: "10px",
              }}
            >
              <p className="m-0 p-0">If the job exceeds the hours outlined,</p>
              <p className="m-0 p-0">will you increase the hourly rate for the extra hours?</p>
            </div>
            <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
              {["Yes", "No"].map((value, index) => (
                <button
                  key={index}
                  className="flex align-items-center justify-content-center cursor-pointer"
                  style={{
                    padding: "12px 30px",
                    border: `2px solid ${extraPaySelection === index ? "#179d52" : "#DFDFDF"}`,
                    borderRadius: "20px",
                    backgroundColor: extraPaySelection === index ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                    color: extraPaySelection === index ? "#179D52" : "#585858",
                    fontSize: "16px",
                    fontWeight: extraPaySelection === index ? "700" : "500",
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    if (index === 0) {
                      setExtraPayPopup(true);
                    }
                    setExtraPaySelection(index);
                  }}
                >
                  {value}
                </button>
              ))}
            </div>
            <br />
            <Divider />
          </div>
        )}

        <div className="w-full mt-auto" />
        <div
          className="flex flex-column mt-6"
          style={{
            width: "100%",
          }}
        ></div>
      </div>

      <BackButtonPortal id="back-button-portal">
        <div onClick={() => prev("jobpricing-step3")}>
          <img src={SideArrow} alt="back" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
        </div>
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        isDisabled={extraPaySelection === -1 || Number(price) < 1}
        onClick={() => {
          setpayload({
            ...payload,
            willPayOvertime: extraPaySelection === 0,
            price: Number(price),
            overtimeRate: extraPaySelection === 0 ? Number(extraPrice) : null,
            applicantFilters: payload.applicantFilters
              ? [
                  ...payload.applicantFilters.filter((af) => af.field !== "hourlyRate"),
                  {
                    field: "hourlyRate",
                    operator: "eq",
                    value: Number(price),
                  },
                ]
              : [
                  {
                    field: "hourlyRate",
                    operator: "eq",
                    value: Number(price),
                  },
                ],
          });
          if (payload.managedBy === 1) {
            next("candidate-selection");
          } else {
            next("candidate-matching");
          }
        }}
      />
    </div>
  );
};
