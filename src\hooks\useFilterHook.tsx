import { useEffect, useRef, useState } from 'react';
import utils from '../components/utils/util';
import CookiesConstant from '../helper/cookiesConst';
import { RootState } from '../store';
import { useSelector } from 'react-redux';

export interface ChildcareJobTypeFilter {
    babysitting: boolean;
    nannying: boolean;
    beforeSchool: boolean;
    afterSchool: boolean;
}

export interface OddJobsTypeFilter {
    laundry: boolean;
    errands: boolean;
    outdoorChores: boolean;
    elderlyHelp: boolean;
    otherOddJobs: boolean;
}

export interface TutoringJobTypeFilter {
    selectedOption: 'primarySchool' | 'highSchool' | null;
}

export type JobTypeFilters = ChildcareJobTypeFilter | TutoringJobTypeFilter | OddJobsTypeFilter;
export type ReviewFilter = 'All' | 'Over-4-stars' | 'Over-4-stars-with-reviews';
export type JobsCompletedFilter = 'All' | '1-or-more-jobs' | '5-or-more-jobs';
export type JobCategory = 'Childcare' | 'Tutoring' | 'Odd Jobs';

export interface UseFilterHookProps {
    defaultFilters: any;
    onFilterChange: (filters: any) => void;
    enableLoader: () => void;
    setSearchResponse: (response: any) => void;
}

export const useFilterHook = ({
    onFilterChange,
    enableLoader,
    setSearchResponse,
}: UseFilterHookProps) => {
    const [selectedJob, setSelectedJob] = useState<JobCategory>('Childcare');
    const [jobTypeFilters, setJobTypeFilters] = useState<JobTypeFilters>(
        getInitialJobTypeFilters('Childcare')
    );
    const [reviewFilter, setReviewFilter] = useState<ReviewFilter>('All');
    const [jobsCompletedFilter, setJobsCompletedFilter] = useState<JobsCompletedFilter>('All');
    const [isApplyButtonDisabled, setIsApplyButtonDisabled] = useState(false);
    const isFirstRender = useRef(true);

    function getInitialJobTypeFilters(category: JobCategory): JobTypeFilters {
        switch (category) {
            case 'Childcare':
                return {
                    babysitting: true,
                    nannying: true,
                    beforeSchool: true,
                    afterSchool: true,
                };
            case 'Tutoring':
                return {
                    selectedOption: 'primarySchool',
                    
                };
            case 'Odd Jobs':
                return {
                    laundry: true,
                    errands: true,
                    outdoorChores: true,
                    elderlyHelp: true,
                    otherOddJobs: true,
                };
        }
    }
    useEffect(() => {
        if (jobTypeFilters) {
            handleApplyJobTypeFilters();
        }
    }, [jobTypeFilters]);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));

    const getJobTypeOptions = (category: JobCategory) => {
        switch (category) {
            case 'Childcare':
                if (clientType !== 1) {
                    return [
                        { key: 'babysitting', label: 'One-Off', value: 1 }, // Replacing babysitting
                        { key: 'nannying', label: 'Recurring', value: 2 }, // Replacing nannying
                        { key: 'beforeSchool', label: 'Before School', value: 4 },
                        { key: 'afterSchool', label: 'After School', value: 8 },
                    ];
                }
                return [
                    { key: 'babysitting', label: 'Babysitting', value: 1 },
                    { key: 'nannying', label: 'Nannying', value: 2 },
                    { key: 'beforeSchool', label: 'Before School', value: 4 },
                    { key: 'afterSchool', label: 'After School', value: 8 },
                ];
            case 'Tutoring':
                return [
                    { key: 'primarySchool', label: 'Primary School', value: 64 },
                    { key: 'highSchool', label: 'High School', value: 128 },
                ];
            case 'Odd Jobs':
                return [
                    { key: 'laundry', label: 'Laundry', value: 1 },
                    { key: 'errands', label: 'Errands', value: 2 },
                    { key: 'outdoorChores', label: 'Outdoor Chores', value: 4 },
                    { key: 'elderlyHelp', label: 'Elderly Help', value: 8 },
                    { key: 'otherOddJobs', label: 'Other Odd Jobs', value: 16 },
                ];
        }
    };

    const getSelectedFilterValues = (category: JobCategory, filters: JobTypeFilters) => {
        const options = getJobTypeOptions(category);
        if (category === 'Tutoring') {
            const tutoringFilters = filters as TutoringJobTypeFilter;
            const selectedOption = options.find(
                (opt) => opt.key === tutoringFilters.selectedOption
            );
            return selectedOption ? [selectedOption.value] : [];
        }
        return Object.entries(filters)
            .filter(([key, value]) => value)
            .map(([key]) => {
                const option = options.find((opt) => opt.key === key);
                return option ? option.value : null;
            })
            .filter((value) => value !== null);
    };

    const handleJobSelect = (job: JobCategory) => {
        const newFilters = getInitialJobTypeFilters(job);
        setSelectedJob(job);
        setJobTypeFilters(newFilters);
        enableLoader();
        setSearchResponse(null);

        const selectedValues = getSelectedFilterValues(job, newFilters);
        updateFiltersForJobType(job, selectedValues);
    };
    const { filters } = useSelector((state: RootState) => state.applicationState);

    const updateFiltersForJobType = (job: JobCategory, selectedValues: number[]) => {
        const baseFilters = {
            jobTypes: { field: 'jobTypes', operator: 'eq', value: [] },
            jobSubTypes: { field: 'jobSubTypes', operator: 'eq', value: [] },
            tutoringCategory: { field: 'tutoringCategory', operator: 'eq', value: [] },
            jobDeliveryMethod: { field: 'jobDeliveryMethod', operator: 'eq', value: filters.filters.find(f => f.field === "jobDeliveryMethod")?.value},
        };

        let updatedFilters;
        switch (job) {
            case 'Childcare':
                updatedFilters = {
                    ...baseFilters,
                    jobTypes: { ...baseFilters.jobTypes, value: selectedValues },
                };
                break;
            case 'Odd Jobs':
                updatedFilters = {
                    ...baseFilters,
                    jobSubTypes: { ...baseFilters.jobSubTypes, value: selectedValues  },
                };
                break;
            case 'Tutoring':
                const tutoringValue = selectedValues.includes(128) ? 128 : 64;
                updatedFilters = {
                    ...baseFilters,
                      jobTypes: { ...baseFilters.jobTypes, value: [tutoringValue] },
                    tutoringCategory: { ...baseFilters.tutoringCategory, value: [1, 2, 3, 4] },
                    jobDeliveryMethod: { ...baseFilters.jobDeliveryMethod, value: filters.filters.find(f => f.field === "jobDeliveryMethod")?.value },
                };
                break;
        }

        onFilterChange(updatedFilters);
    };
   
    const handleJobTypeChange = (filterKey: string) => {
        setJobTypeFilters((prevFilters) => {
            if (selectedJob === 'Tutoring') {
                return {
                    selectedOption: filterKey as 'primarySchool' | 'highSchool',
                } as TutoringJobTypeFilter;
            } else {
                return {
                    ...prevFilters,
                    [filterKey]: !(prevFilters as any)[filterKey],
                };
            }
        });
    };

    const handleReviewFilterChange = (value: ReviewFilter) => {
        setReviewFilter(value);
        enableLoader();
        setSearchResponse(null);

        const ratingValues = {
            All: [],
            'Over-4-stars': [2],
            'Over-4-stars-with-reviews': [2, 3],
        };

        onFilterChange({
            ratings: { field: 'ratings', operator: 'eq', value: ratingValues[value] },
        });
    };

    const handleJobsCompletedFilterChange = (value: JobsCompletedFilter) => {
        setJobsCompletedFilter(value);
        enableLoader();
        setSearchResponse(null);

        const experienceValues = {
            All: 0,
            '1-or-more-jobs': 1,
            '5-or-more-jobs': 2,
        };

        onFilterChange({
            experience: { field: 'experience', operator: 'eq', value: experienceValues[value] },
        });
    };

    const handleApplyJobTypeFilters = () => {
        enableLoader();
        setSearchResponse(null);
        const selectedValues = getSelectedFilterValues(selectedJob, jobTypeFilters);
        updateFiltersForJobType(selectedJob, selectedValues);
    };

    const areAllOptionsSelected = () => {
        if (selectedJob === 'Tutoring') {
            return false;
        }
        return Object.values(jobTypeFilters as any).every((value) => value === true);
    };

    useEffect(() => {
        setIsApplyButtonDisabled(areAllOptionsSelected());
    }, [jobTypeFilters, selectedJob]);

    useEffect(() => {
        if (isFirstRender.current) {
            const initialFilters = getInitialJobTypeFilters('Childcare');
            const initialValues = getSelectedFilterValues('Childcare', initialFilters);
            onFilterChange({
                jobTypes: { field: 'jobTypes', operator: 'eq', value: initialValues },
            });
            isFirstRender.current = false;
        }
    }, []);

    return {
        selectedJob,
        jobTypeFilters,
        reviewFilter,
        jobsCompletedFilter,
        isApplyButtonDisabled,
        getJobTypeOptions,
        handleJobSelect,
        handleJobTypeChange,
        handleReviewFilterChange,
        handleJobsCompletedFilterChange,
        handleApplyJobTypeFilters,
    };
};
