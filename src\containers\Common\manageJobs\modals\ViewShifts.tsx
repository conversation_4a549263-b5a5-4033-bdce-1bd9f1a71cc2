import React, { useEffect, useMemo, useState } from "react";
import { Dialog } from "primereact/dialog";
import { Jobs, Applicants } from "../types";
import styles from "../../../Common/styles/review-and-post.module.css";
import WeeklyScheduleds from "../Common/WeeklySheduled";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import Loader from "../../../../commonComponents/Loader";
import useIsMobile from "../../../../hooks/useIsMobile";
import { IoClose } from "react-icons/io5";

interface ViewShiftsProps {
  jobId: string; // Accept jobId as a prop
  onClose: () => void; // Accept onClose callback
  visible: boolean; // Control dialog visibility
}

const ViewShifts: React.FC<ViewShiftsProps> = ({ jobId, onClose, visible }) => {
  const { disableLoader, enableLoader } = useLoader();
  const [jobClients, setJobClients] = useState<Jobs | null>(null);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const { isMobile } = useIsMobile();

  const totalInvited = jobClients?.applicantsTotal || 0;
  const totalViewed = jobClients?.applicantsViewed || 0;

  const { responseTable, availableApplicants } = useMemo(() => {
    if (!jobClients) return { responseTable: [], availableApplicants: [] };
    const dows = ["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"];
    const schedules = jobClients.weeklySchedule?.weeklyScheduleEntries || [];
    const applicants = jobClients.applicants || [];
    const responseTable: Array<{
      dayOfWeek: number;
      day: string;
      startTime: string;
      endTime: string;
      shiftId: number;
      price: number;
      awardedAplicantId: number | null;
      applicants: Array<{
        applicantId: number;
        availabilityId: number;
        publicName: string;
        imageSrc: string;
        status: number;
        completedJobsCount: number;
        applicantRatingsCount: number;
        applicantRatingsAvg: number;
        responseRate: number;
        id: number;
        applicationStatus: number;
      }>;
    }> = [];
    const availableApplicants: Array<{
      applicantId: number;
      publicName: string;
      imageSrc: string;
      isSuperHelper: boolean;
      applicantRatingsCount: number;
      applicantRatingsAvg: number;
      responseRate: number;
      id: number;
      applicationStatus: number;
      gettingHome: number;
      suburb: string;
    }> = [];
    const addedApplicantIds = new Set<number>();

    const inPrevShift = (dow: number, id: number) => {
      const shift = responseTable.find((r) => r.dayOfWeek === dow);
      return shift?.applicants.some((a) => a.availabilityId === id) || false;
    };

    for (const schedule of schedules) {
      const shiftData = {
        dayOfWeek: schedule.dayOfWeek,
        day: dows[schedule.dayOfWeek],
        startTime: schedule.jobStartTime,
        endTime: schedule.jobEndTime,
        shiftId: schedule.id,
        price: schedule.price,
        awardedAplicantId: schedule.applicantId,
        applicants: [] as Array<{
          applicantId: number;
          availabilityId: number;
          publicName: string;
          imageSrc: string;
          status: number;
          completedJobsCount: number;
          applicantRatingsCount: number;
          applicantRatingsAvg: number;
          responseRate: number;
          id: number;
          applicationStatus: number;
        }>,
      };

      for (const applicant of applicants) {
        for (const availability of applicant.applicantAvailability) {
          if (
            availability.dayOfWeek === schedule.dayOfWeek &&
            !shiftData.applicants.some((a) => a.applicantId === applicant.applicantId) &&
            !inPrevShift(availability.dayOfWeek, availability.id)
          ) {
            shiftData.applicants.push({
              applicantId: applicant.applicantId,
              availabilityId: availability.id,
              publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
              imageSrc: applicant.applicantImageSrc,
              status: availability.availabilityStatus,
              completedJobsCount: applicant.completedJobsCount,
              applicantRatingsCount: applicant.applicantRatingsCount,
              applicantRatingsAvg: applicant.applicantRatingsAvg,
              responseRate: applicant.responseRate,
              id: applicant.id,
              applicationStatus: applicant.applicationStatus,
            });

            if (
              schedule.applicantId === null &&
              availability.availabilityStatus === c.applicantAvailabilityStatus.AVAILABLE &&
              !addedApplicantIds.has(applicant.applicantId)
            ) {
              availableApplicants.push({
                applicantId: applicant.applicantId,
                publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
                imageSrc: applicant.applicantImageSrc,
                isSuperHelper: applicant.completedJobsCount >= 15,
                applicantRatingsCount: applicant.applicantRatingsCount,
                applicantRatingsAvg: applicant.applicantRatingsAvg,
                responseRate: applicant.responseRate,
                id: applicant.id,
                applicationStatus: applicant.applicationStatus,
                gettingHome: applicant.gettingHome,
                suburb: applicant.suburb,
              });
              addedApplicantIds.add(applicant.applicantId);
            }
          }
        }
      }

      responseTable.push(shiftData);
    }

    return { responseTable, availableApplicants };
  }, [jobClients]);

  useEffect(() => {
    const fetchData = () => {
      enableLoader();
      if (clientType == "0") {
        Service.jobProviderDetails(
          (response: Jobs) => {
            setJobClients(response);
            disableLoader();
          },
          (error: any) => {
            console.error("Error fetching data:", error);
            disableLoader();
          },
          parseInt(jobId) // Use prop jobId
        );
      } else {
        Service.jobClientDetails(
          (response: Jobs) => {
            setJobClients(response);
            disableLoader();
          },
          (error: any) => {
            console.error("Error fetching data:", error);
            disableLoader();
          },
          parseInt(jobId) // Use prop jobId
        );
      }
    };
    fetchData();
  }, [jobId]);

  return jobClients ? (
    <Dialog
      visible={visible} // Use prop to control visibility
      onHide={onClose} // Use prop to handle close
      style={{
        width: isMobile ? "100%" : "max-content",
        marginInline:"25px"
      }}
      content={
        <div
         
          style={{
            padding: isMobile ? "20px" : "30px",
            width: "100%",
            backgroundColor: "#FFFFFF",
            borderRadius: "20px",
            maxHeight:"700px",
            overflowY: "auto",
          }}
        >
             <IoClose onClick={onClose} className={styles.CloseBtn} />
          {jobClients && (jobClients.isRecurringJob || jobClients.isTutoringJob) && (
            <WeeklyScheduleds
              timeSlots={responseTable}
              availabilityStatusEnum={c.applicantAvailabilityStatus}
              totalInvited={totalInvited}
              totalViewed={totalViewed}
              job={jobClients}
              availableApplicants={availableApplicants}
              viewShifts={true}
            />
          )}
        </div>
      }
    />
  ) : (
    <Loader />
  );
};

export default ViewShifts;