import React, { useState } from "react";
import styles from "./styles/GeneralSettings.module.css";
import { FaCheck } from "react-icons/fa6";
import { Divider } from "primereact/divider";
import { InputText } from "primereact/inputtext";
import CustomButton from "../../commonComponents/CustomButton";
import Service from "../../services/services";
import useLoader from "../../hooks/LoaderHook";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";
import { removeSession } from "../../store/slices/sessionInfoSlice";
import { useNavigate } from "react-router-dom";
import { updateShowAccountAndSettings } from "../../store/slices/applicationSlice";
import { ConfirmationPopupRed, useConfirmationPopup } from "./ConfirmationPopup";
import removeIcon from "../../assets/images/Icons/remove.png";
import CookiesConstant from "../../helper/cookiesConst";
import utils from "../../components/utils/util";
import useIsMobile from "../../hooks/useIsMobile";

const GeneralSettings = () => {
  const [message, setMessage] = useState({ text: "", type: "" }); // Combined state for messages
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const navigate = useNavigate();
  const { isMobile } = useIsMobile();
  const dispatch = useDispatch<AppDispatch>();
  const [formdata, setFormdata] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
    confirmPasswordErrorText: "",
    passwordErrorText: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState("");
  const { disableLoader, enableLoader } = useLoader();

  const handlePasswordBlur = () => {
    setPasswordStrength("");
  };

  const handleNewPasswordChange = (e) => {
    setMessage({ text: "", type: "" }); // Clear message on input
    const value = e.target.value;
    setFormdata((prevState) => ({
      ...prevState,
      newPassword: value,
    }));

    if (value.length > 8 && /[A-Z]/.test(value) && /[0-9]/.test(value)) {
      setPasswordStrength("Complex password");
    } else if (value.length > 5) {
      setPasswordStrength("Average complexity");
    } else {
      setPasswordStrength("Weak password");
    }
  };

  const handleConfirmPasswordChange = (e) => {
    setMessage({ text: "", type: "" }); // Clear message on input
    const value = e.target.value;
    setFormdata((prevState) => ({
      ...prevState,
      confirmPassword: value,
      confirmPasswordErrorText: value !== formdata.newPassword ? "Passwords do not match" : "",
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    enableLoader();

    if (!formdata.currentPassword || !formdata.newPassword || !formdata.confirmPassword) {
      setMessage({ text: "All fields are required.", type: "error" });
      disableLoader();
      return;
    }
    if (formdata.newPassword !== formdata.confirmPassword) {
      setMessage({ text: "Passwords do not match", type: "error" });
      disableLoader();
      return;
    }

    const payload = {
      oldPassword: formdata.currentPassword,
      newPassword: formdata.newPassword,
      confirmPassword: formdata.confirmPassword,
    };

    Service.changePassword(
      payload,
      (response) => {
        setMessage({ text: "Changes successfully made!", type: "success" });
        setFormdata({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
          confirmPasswordErrorText: "",
          passwordErrorText: "",
        });
        disableLoader();
      },
      (error) => {
        setMessage({
          text: error.message || "Failed to change password.",
          type: "error",
        });
        disableLoader();
      }
    );
  };

  const handleHideAccount = () => {
    showConfirmationPopup(
      "Hide my account",
      "Are you sure you want to Hide your account? This will log you out.",
      "Hide Account",
      <img src={removeIcon} alt="remove icon" style={{ height: "15px", width: "13.33px" }} />,
      () => {
        enableLoader();
        Service.hideMyAccount(
          (response) => {
            setMessage({
              text: "Your account has been hidden successfully.",
              type: "success",
            });
            try {
              dispatch(removeSession());
              dispatch(updateShowAccountAndSettings(false));
              utils.obliterateEverything();
            } catch (_e) {
            } finally {
              navigate("/");
            }
            disableLoader();
          },
          (error) => {
            setMessage({
              text: error.message || "Failed to hide the account.",
              type: "error",
            });
            disableLoader();
          }
        );
      }
    );
  };

  const handleDeleteAccount = () => {
    showConfirmationPopup(
      "Delete my account",
      "Deleting your account will delete all your data and historical records. This action is irreversible, and you will be logged out immediately. Are you sure you want to delete your account?",
      "Delete Account",
      <img src={removeIcon} alt="remove icon" style={{ height: "15px", width: "13.33px" }} />,
      () => {
        enableLoader();
        Service.requestDeleteAccount(
          (response) => {
            setMessage({
              text: "Your account has been Deleted successfully.",
              type: "success",
            });
            try {
              dispatch(removeSession());
              dispatch(updateShowAccountAndSettings(false));
              utils.obliterateEverything();
            } catch (_e) {
            } finally {
              navigate("/");
            }
            disableLoader();
          },
          (error) => {
            disableLoader();
            showConfirmationPopup("Error", error.message || "Failed to delete the account.", "OK", null, () => {});
          }
        );
      }
    );
  };

  return (
    <div className={styles.settingsContainer}>
      <ConfirmationPopupRed confirmationProps={confirmationProps} />
      <header className="flex flex-column justify-content-between md:flex-row">
        <h2 style={{ margin: isMobile && "0px" }} className={styles.settingsHeader}>
          General Settings
        </h2>
        {message.text && (
          <div className={message.type === "success" ? styles.successMessageSettings : styles.errorMessageSettings}>
            <div
              style={{
                backgroundColor: "#FFFFFF",
                borderRadius: "50%",
                color: message.type === "success" ? "#179D52" : "#DC3545",
                padding: "4px",
                height: "16px",
                width: "16px",
                display: "inline-flex",
                alignItems: "center",
                marginRight: "3px",
              }}
            >
              <FaCheck />
            </div>
            {message.text}
          </div>
        )}
      </header>

      <Divider className={styles.settingsDivider} />

      {/* Current Password Input */}
      <div className={styles.settingsInputContainer} style={{ marginTop: "20px" }}>
        <div style={{ position: "relative", maxWidth: "362px" }}>
          <InputText
            id="currentPassword"
            name="currentPassword"
            type={showPassword ? "text" : "password"}
            value={formdata.currentPassword}
            onChange={(e) => {
              setMessage({ text: "", type: "" });
              setFormdata((prevState) => ({
                ...prevState,
                currentPassword: e.target.value,
              }));
            }}
            placeholder=" "
            className="input-placeholder"
          />
          <label htmlFor="currentPassword" className={`label-name ${formdata.currentPassword ? "label-float" : ""}`}>
            Current Password*
          </label>
        </div>
      </div>

      {/* New Password Input */}
      <div className={styles.settingsInputContainer}>
        <div style={{ position: "relative", maxWidth: "362px" }}>
          <InputText
            id="newPassword"
            name="newPassword"
            type={showPassword ? "text" : "password"}
            value={formdata.newPassword}
            onFocus={() => setFormdata({ ...formdata, passwordErrorText: "" })}
            onChange={handleNewPasswordChange}
            onBlur={handlePasswordBlur}
            placeholder=" "
            className={`input-placeholder ${formdata.passwordErrorText ? "passwordInputError" : ""}`}
          />
          <label
            htmlFor="newPassword"
            className={`label-name ${formdata.newPassword || formdata.passwordErrorText ? "label-float" : ""} ${
              formdata.passwordErrorText ? "input-error" : ""
            }`}
          >
            {formdata.passwordErrorText && !formdata.newPassword ? formdata.passwordErrorText : "Enter New Password*"}
          </label>
          <span
            onClick={() => setShowPassword(!showPassword)}
            style={{
              position: "absolute",
              right: window.innerWidth <= 600 ? "8px" : window.innerWidth <= 1024 ? "10px" : "12px",
              top: "50%",
              transform: "translateY(-50%)",
              fontSize: window.innerWidth <= 600 ? "1.2rem" : window.innerWidth <= 1024 ? "1.5rem" : "1.8rem",
              cursor: "pointer",
              color: "#888",
            }}
          >
            <i className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"}`}></i>
          </span>
        </div>

        <div
          className="password-strength-label"
          style={{
            color: passwordStrength === "Complex password" ? "green" : passwordStrength === "Average complexity" ? "orange" : "red",
          }}
        >
          {passwordStrength}
        </div>
      </div>

      {/* Confirm Password Input */}
      <div className={styles.settingsInputContainer}>
        <div style={{ position: "relative", maxWidth: "362px" }}>
          <InputText
            id="confirmPassword"
            name="confirmPassword"
            type={showPassword ? "text" : "password"}
            value={formdata.confirmPassword}
            onFocus={() => setFormdata({ ...formdata, confirmPasswordErrorText: "" })}
            onChange={handleConfirmPasswordChange}
            placeholder=" "
            className={`input-placeholder ${formdata.confirmPasswordErrorText ? "confirmPasswordErrorText" : ""}`}
          />
          <label
            htmlFor="confirmPassword"
            className={`label-name ${formdata.confirmPassword || formdata.confirmPasswordErrorText ? "label-float" : ""} ${
              formdata.confirmPasswordErrorText ? "input-error" : ""
            }`}
          >
            {formdata.confirmPasswordErrorText && !formdata.confirmPassword ? formdata.confirmPasswordErrorText : "Confirm New Password*"}
          </label>

          <span
            onClick={() => setShowPassword(!showPassword)}
            style={{
              position: "absolute",
              right: window.innerWidth <= 600 ? "8px" : window.innerWidth <= 1024 ? "10px" : "12px",
              top: "50%",
              transform: "translateY(-50%)",
              fontSize: window.innerWidth <= 600 ? "1.2rem" : window.innerWidth <= 1024 ? "1.5rem" : "1.8rem",
              cursor: "pointer",
              color: "#888",
            }}
          >
            <i className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"}`}></i>
          </span>
        </div>
      </div>

      {/* Change Password Button */}
      <div>
        <CustomButton label="Change Password" type="submit" onClick={handleSubmit} />
      </div>
      <br />

      <>
        <div className={styles.hideAccountContainer}>
          <p className={styles.hideAccountText}>Hide My Account</p>
          <p className={styles.hideAccountDescription}>
            Your account is currently active. To hide your account, click 'Hide My Account.' While hidden, you will be invisible on Juggle Street and
            won't receive any communications. You will stay hidden until you log back in, then your account will be active again.
          </p>
        </div>
        <a
          href="#"
          className={styles.hideAccountLink}
          onClick={(e) => {
            e.preventDefault();
            handleHideAccount();
          }}
        >
          Hide My Account
        </a>
        <br />

        <div className={styles.hideAccountContainer}>
          <p className={styles.hideAccountText}>Delete My Account</p>
          <p className={styles.hideAccountDescription}>
            Request your account to be deleted. This action is irreversible, and you will be logged out immediately.
          </p>
        </div>
        <a
          href="#"
          className={styles.deleteAccountLink}
          onClick={(e) => {
            e.preventDefault();
            handleDeleteAccount();
          }}
        >
          Delete My Account
        </a>
      </>
    </div>
  );
};

export default GeneralSettings;
