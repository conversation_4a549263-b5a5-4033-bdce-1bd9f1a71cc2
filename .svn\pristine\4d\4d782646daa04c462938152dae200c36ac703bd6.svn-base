import { useEffect, useState } from "react";
import ReactDOM from "react-dom";

function BackButtonPortal({ children, id }: { children: React.ReactNode; id: string }) {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const findContainer = () => document.getElementById(id);
    setPortalContainer(findContainer());

    const observer = new MutationObserver(() => {
      setPortalContainer(findContainer());
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);

  if (!portalContainer) return null;

  return ReactDOM.createPortal(children, portalContainer);
}

export default BackButtonPortal;
