import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

function SystemNavigationOverride() {
    const navigate = useNavigate();

    useEffect(() => {
        const handleBackNavigation = (event) => {
            event.preventDefault(); // Stop default back button behavior
            navigate(-1); // Navigate one step back
        };

        // Add initial history entry
        window.history.pushState(null, null, window.location.pathname);

        // Listen to back events
        window.addEventListener('popstate', handleBackNavigation);

        // Cleanup function to remove the event listener
        return () => {
            window.removeEventListener('popstate', handleBackNavigation);
        };
    }, [navigate]);

    return null;
}

export default SystemNavigationOverride;