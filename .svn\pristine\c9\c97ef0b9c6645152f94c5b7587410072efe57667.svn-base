export type ExtendedProfileTabProps = { helper: Helper };

export type Helper = {
    isIndividual: boolean;
    isBusiness:boolean;
    clientJobRules: string;
    benefitsForProviders: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    industrySectorId: number;
    industrySectorText: string;
    industrySector: number;
    establishYear: number;
    clientJobsAwarded: Helper;
    clientJobsCancelled: string;
    firstName: string;
    publicName: string;
    lastInitial: string;
    interestedInChildcareJobs: boolean;
    interestedInAuPairJobs: boolean;
    interestedInTutoringJobs: boolean;
    interestedInOddJobs: boolean;
    isGuardian: boolean;
    isProvider: boolean;
    isClient: boolean;
    ageInYears: number;
    hideAgeDisplay: boolean;
    aboutMe: string;
    isPublicProfileAccessible: boolean;
    publicUserId: string;
    providerMyExperience: string;
    providerMyExperience2?: string;
    providerMyExperience3: string;
    providerMyExperience4: string;
    responseTime: number;
    responseRate: number;
    providerCertificateNumber: any;
    isHidden: boolean;
    children?: Array<{
        firstName: string;
        ageInYears: number;
        hasAllergies: boolean;
        allergyDetails: string;
        aboutMe: string
    }>;
    qualifications: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    tutoringQualifications: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    firstAid: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    transport: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    extraSkills: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    spokenLanguages: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    certificates: Array<{
        category: number;
        certificateTypeId: number;
        certificateName: any;
        certificateNumber: string;
        expiryDate: string;
        notes: any;
        verificationDate: string;
        certificateNumericData1: any;
        certificateStatus: number;
        id: number;
    }>;
    hasVouches: boolean;
    vouches: Array<any>;
    primarySchoolSubjects: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: Array<{
            optionId: number;
            text: string;
            selected: boolean;
            canSelectCategory: boolean;
            childCategory: number;
            children: any;
        }>;
    }>;
    highSchoolSubjects: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: Array<{
            optionId: number;
            text: string;
            selected: boolean;
            canSelectCategory: boolean;
            childCategory: number;
            children?: Array<{
                optionId: number;
                text: string;
                selected: boolean;
                canSelectCategory: boolean;
                childCategory: number;
                children: any;
            }>;
        }>;
    }>;
    primarySchoolYears: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    highSchoolYears: Array<{
        optionId: number;
        text: string;
        selected: boolean;
        canSelectCategory: boolean;
        childCategory: number;
        children: any;
    }>;
    friendStatus: number;
    actionRequired: boolean;
    friendshipId: any;
    secondLanguage: number;
    imageSrc: string;
    visibilityLimit: number;
    providerJobsAwarded: number;
    providerJobsCompleted: number;
    providerJobsCancelled: number;
    providerRatingsCount: number;
    providerReviewsCount: number;
    providerRatingsSum: number;
    providerMinHourlyRate: number;
    providerRatingsAvg: number;
    isSuperProvider: boolean;
    providerInterestedInFaceToFaceTutoring: boolean;
    providerInterestedInOnlineTutoring: boolean;
    providerAuPairCategory: number;
    providerTutoringCategory: number;
    suburb: string;
    state: string;
    longitude: number;
    latitude: number;
    distanceInMeters: number;
    distanceInMetersRounded: number;
    distanceInKiloMetersRounded: number;
    nationality: string;
    interestedInJobTypes: number;
    ratings: Array<{
        jobId: number;
        ratingFor: any;
        ratingBy: any;
        ratingMode: any;
        ratingDate: string;
        category1: any;
        category2: any;
        category3: any;
        category4: any;
        feedback: string;
        clientFirstName: string;
        clientLastName: string;
        clientType: number;
        clientId: number;
        clientPrimaryLocation: any;
        clientImageUrl: any;
        ratingAvg: number;
        jobType: number;
        id: number;
    }>;
    ratingsExtended: Array<{
        jobId: number;
        ratingFor: any;
        ratingBy: any;
        ratingMode: any;
        ratingDate: string;
        category1: any;
        category2: any;
        category3: any;
        category4: any;
        feedback: string;
        clientFirstName: string;
        clientLastName: string;
        clientType: number;
        clientId: number;
        clientPrimaryLocation: any;
        clientImageUrl: any;
        ratingAvg: number;
        jobType: number;
        id: number;
    }>;
    profileIntroVideo: {
        externalId: number;
        externalHashedId: string;
        name: any;
        description: any;
        externalUpdatedDate: any;
        mediaStatus: number;
        mediaReviewStatus: number;
        thumbnailUrl: any;
        width: any;
        height: any;
        duration: number;
        mediaType: number;
        assetId: any;
        playbackId: any;
        id: number;
    };
    oddJobPreferences: {
        laundry: boolean;
        errands: boolean;
        outdoorChores: boolean;
        elderlyHelp: boolean;
        otherOddJobs: boolean;
    };
    geoJson: {
        type: string;
        features: Array<{
            type: string;
            properties: {
                canConnect: boolean;
                isCenterPoint: boolean;
                featureType: number;
                metadata: any;
                id: number;
                requestId: any;
                jobsCompleted: number;
                jobsCancelled: number;
                ratingsCount: number;
                ratingsAvg: number;
                firstName?: string;
                publicName?: string;
                distanceInKm: any;
                imageSrc?: string;
                aboutMe25?: string;
                actionRequired: boolean;
                friendStatus: number;
                suburb?: string;
                state: any;
                lastSeenInDays: any;
                visibilityLimit: number;
                invitationDate: any;
                accountSubtype: number;
                jugglerTypes: number;
            };
            geometry: {
                type: string;
                coordinates: Array<any>;
            };
        }>;
    };
    providerCanTeachStudySkills: boolean;
    providerCanTeachExamPreparation: boolean;
    providerYear12GraduationYear: number;
    providerVisaClass: any;
    providerVisaSubclass: any;
    providerVisaCheckStatus: number;
    providerVisaCheckDate: any;
    providerVisaGrantDate: any;
    providerVisaExpiry: any;
    id: number;
};
