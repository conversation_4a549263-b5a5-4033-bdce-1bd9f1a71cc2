import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import c from '../../../helper/juggleStreetConstants';
import { But<PERSON> } from 'primereact/button';
import { updateActiveTabIndex } from '../../../store/slices/accountSettingSlice';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { updateAccountAndSettingsActiveTab, updateShowAccountAndSettings } from '../../../store/slices/applicationSlice';
import useIsMobile from '../../../hooks/useIsMobile';

const WwccAlerts = () => {
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const dispatch = useDispatch<AppDispatch>();
    const { isMobile } = useIsMobile()
    const [open, setOpen] = React.useState(false);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const isApproved = (): boolean => {
        const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
        if (!sessionInfo.data) {
            return false;
        }
        return sessionInfo.data?.['accountStatus'] === c.accountStatus?.APPROVED;
    };
    const [isPulsing, setIsPulsing] = useState(true);

    useEffect(() => {
        const interval = setInterval(() => {
            setIsPulsing(prev => !prev);
        }, 800); // Toggle every 800ms

        return () => clearInterval(interval);
    }, []);
    const requiresCheck = () => {
        return (
            isApproved() &&
            sessionInfo?.data &&
            Array.isArray(sessionInfo.data['certificates']) &&
            (sessionInfo.data['certificates'].length === 0 ||
                sessionInfo.data['certificates'][0]?.certificateStatus === 4 ||
                sessionInfo.data['certificates'][0]?.certificateStatus === 3) &&
            sessionInfo.data['country'] === "au" &&
            sessionInfo.data['defaultAddress']?.state !== "act" &&
            sessionInfo.data['ageInYears'] >= 18
        );
    };
    const handleMissingCheck = () => {
        setOpen(true);
        showConfirmationPopup(
            "WWCC Alert!",
            "A valid Working with Children Check (WWCC) is mandatory on Juggle Street. Go to the Certificates section of My Account and upload WWCC.",
            "Upload",
            <img src={null} />,
            () => {
                dispatch(updateActiveTabIndex(19));
                dispatch(updateAccountAndSettingsActiveTab(19));
                dispatch(updateShowAccountAndSettings(true));
            }
        );
    };

    return (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            {requiresCheck() && (
                <Button label="WWCC Alert!" style={{
                    backgroundColor: isPulsing ? "#FF4C4C" : "#D90000",
                    color: "white",
                    borderRadius: "unset",
                    fontWeight: "bold",
                    width: "100%",
                    border: "none",
                    transition: "background-color 0.8s ease",
                    boxShadow: isPulsing ? "0 0 8px #FF4C4C" : "none"
                }} className={`${!isMobile ? "p-button-outlined bg-red-500 w-full" : ""}`} onClick={handleMissingCheck} />
            )}
            {/* <Dialog header="WWCC Alert!" visible={open} onHide={() => setOpen(false)}>
                <p>A valid Working with Children Check (WWCC) is mandatory on Juggle Street. Go to the Certificates section of My Account and upload WWCC.</p>
                <div className="p-dialog-footer">
                    <Button label="Upload" className="p-button-outlined p-button-primary" onClick={handleUpload} />
                    <Button label="Later" className="p-button-outlined p-button-secondary" onClick={() => setOpen(false)} />
                </div>
            </Dialog> */}
        </>
    )
}

export default WwccAlerts