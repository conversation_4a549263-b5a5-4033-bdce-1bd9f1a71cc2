.shiftHeaderGradient {
  background: linear-gradient(90deg, #37a950, #ffa500);
  height: 81px;
}
.shiftJuggleLogo {
  object-fit: contain;
  width: 173px;
  height: 36px;
  margin-left: 50px;
  cursor: pointer;
}
.shiftReferMainSection {
  flex-grow: 1;
  padding: 20px;
  background-color: #f0f4f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  
}
.shiftContainers {
  background-color: #ffffff;
  flex-grow: 1;
  width: 962px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.ShiftHeader {
  font-size: 60px;
  font-weight: 700;
  color: #179d52;
  max-width: 522px;
  margin: 0px;
 
}
.nameTag {
  font-size: 18px;
  font-weight: 500;
  margin: 0px;
  color: #585858;
}
.shiftBackBtn {
  width: max-content;
  background-color: transparent;
  border: none;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #585858;
}
.returnBtn{
  height: 46px;
  border: none;
  border-radius: 10px;
  padding-inline: 61px;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  color: #ffffff;
  background-color: #FFA500;
  margin-top: 25px;
  cursor: pointer;
}
/* .returnBtn:hover{
  width: 306px;
  height: 46px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  color: #ffffff;
  background-color: #FFA500;
  margin-top: 25px;
  cursor: pointer;
  box-shadow: 0px 4px 4px 0px #00000040;

} */
.shiftHeaderGradientMobile {
  background: linear-gradient(90deg, #37a950, #ffa500);
  height: 55px;
}
.shiftJuggleLogoMobile {
  object-fit: contain;
  width: 123px;
  height: 32px;
  margin-left: 50px;
  cursor: pointer;
}
.shiftContainersMobile {
  background-color: #ffffff;
  flex-grow: 1;
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 50px;
  flex-direction: column;
}
.ShiftHeaderMobile {
  font-size: 28px;
  font-weight: 700;
  color: #179d52;
  max-width: 522px;
  margin: 0px;
 
}
.shiftReferMainSectionMobile {
  flex-grow: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.returnBtnMobile{
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  padding-inline: 70px;
  padding-block: 13px;
  line-height: 21px;
  color: #ffffff;
  background-color: #FFA500;
  margin-top: 25px;
  cursor: pointer;
}
.shiftBackBtnMobile {
  width: max-content;
  background-color: transparent;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #585858;
  margin-top: 15px;
}
.continueFill{
  background-color: #FFA500;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  padding-inline: 45px;
  padding-block: 13px;
  line-height: 21px;
  color: #ffffff;
  margin-top: 25px;
  cursor: pointer;
}
.closeJobBtn{
  background-color: #fff;
  color: #585858;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  padding-inline: 88px;
  padding-block: 13px;
  line-height: 21px;
  cursor: pointer;
   margin-top: 20px;
   text-decoration: underline;
}


.continueFillMobile{
    background-color: #FFA500;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  padding-inline: 45px;
  padding-block: 13px;
  line-height: 21px;
  color: #ffffff;
  margin-top: 25px;
  cursor: pointer;
}
.viewShiftMobile{
  background-color: #fff;
  color: #179d52;
  border: none;
  margin-top: 25px;
  font-size: 14px;
  font-weight: 700;
    cursor: pointer;
  text-decoration: underline;
}

