import React, { useEffect, useMemo, useState } from "react";
import styles from "../styles/activity-timeline.module.css";
import {
  ChatSession,
  Participant,
} from "../../Parent/InAppChat/InAppChatTypes";
import environment from "../../../helper/environment";
import sample from "../../../assets/images/sample_profile.png";
import CookiesConstant from "../../../helper/cookiesConst";
import utils from "../../../components/utils/util";
import { Jobs } from "../../Common/manageJobs/types";
import Service from "../../../services/services";
import { TbChecklist } from "react-icons/tb";
import { IoChevronUp, IoHourglassOutline } from "react-icons/io5";
import { RiProgress4Line, RiUserStarLine } from "react-icons/ri";
import { IoMdCheckboxOutline } from "react-icons/io";
import { GiCheckMark } from "react-icons/gi";
import { FaBoltLightning } from "react-icons/fa6";
import c from "../../../helper/juggleStreetConstants";
import useLoader from "../../../hooks/LoaderHook";

function isDateEqualOrGreater(dateString: string): boolean {
  const inputDate = new Date(dateString);
  const today = new Date();

  today.setHours(0, 0, 0, 0);

  return inputDate >= today;
}

function formatDateToMonthDay(dateString: string): string {
  const date = new Date(dateString);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const day = date.getDate();
  const month = months[date.getMonth()];

  const suffix = (day: number) => {
    if (day % 10 === 1 && day !== 11) return "st";
    if (day % 10 === 2 && day !== 12) return "nd";
    if (day % 10 === 3 && day !== 13) return "rd";
    return "th";
  };

  return `${month} ${day}${suffix(day)}`;
}

const Activity = ({ helperId, name }: { helperId: number; name: string }) => {
  const [latestJob, setLatestJob] = useState<Jobs | null>(null);
  const [opened, setOpened] = useState<boolean>(true);
  const { disableLoader, enableLoader } = useLoader();
  const timeline = useMemo(() => {
    if (!latestJob) {
      return [];
    }

    const applicant = latestJob.applicants.find(
      (applicant) => applicant.applicantId === helperId
    );
    const isAwarded =
      applicant.applicationStatus === c.jobApplicationStatus.AWARDED;
    return [
      {
        icon: GiCheckMark,
        title: "Job Posted",
        value: formatDateToMonthDay(latestJob.creationDate),
        completed: true,
      },
      {
        icon: IoHourglassOutline,
        title: "Not Yet Awarded",
        value: "Waiting for your offer",
        completed: isAwarded,
      },
      {
        icon: TbChecklist,
        title: "Job Awarded",
        value: "Job awarded to Helper",
        completed: isAwarded,
      },
      {
        icon: RiProgress4Line,
        title: "Job In Progress*",
        value: null,
        completed: isDateEqualOrGreater(latestJob.jobDate),
      },
      {
        icon: IoMdCheckboxOutline,
        title: "Job Completed*",
        value: null,
        completed: !isDateEqualOrGreater(latestJob.jobFinishDate),
      },
      {
        icon: RiUserStarLine,
        title: `Review ${name}`,
        value: "Review Helper and give feedback",
        completed: latestJob.ratingProvided !== -1,
      },
    ];
  }, [latestJob]);

  const extractLatestJob = (jobs: Jobs[]): Jobs | null => {
    for (const job of jobs) {
      if (
        job.applicants.some((applicant) => applicant.applicantId === helperId)
      ) {
        return job;
      }
    }
    return null;
  };

  const fetchUpcomingJobs = () => {
    enableLoader();
    Service.getUpComingJobs(
      (response: Jobs[]) => {
        const latestJob = extractLatestJob(response);
        
        setLatestJob(latestJob);
        disableLoader();
      },
      (error) => {
        disableLoader();
        
      }
    );
  };

  useEffect(() => {
    fetchUpcomingJobs();
  }, [helperId]);

  if (!latestJob) {
    return null;
  }
  return (
    <div className="flex-grow-1 flex p-3">
      <div
        className="flex-grow-1 flex flex-column gap-3 py-2 px-3"
        style={{
          border: "1px solid #DFDFDF",
          borderRadius: "10px",
          height: !opened && "min-content",
        }}
      >
        <div
          className="flex justify-content-between align-items-center cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            setOpened(!opened);
          }}
        >
          <div className="flex gap-2 align-items-center">
            <FaBoltLightning color="#585858" />
            <h3
              className="m-0 p-0"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Activity Timeline
            </h3>
          </div>
          <IoChevronUp color="#585858" />
        </div>
        {opened && (
          <div className="flex-grow-1 flex flex-column">
            {timeline.map((item, index) => (
              <div key={index} className="flex gap-2">
                <div className="flex flex-column align-items-center">
                  <div
                    className="flex justify-content-center align-items-center"
                    style={{
                      width: "32px",
                      height: "32px",
                      border: item.completed
                        ? "1px solid #179D52"
                        : "1px solid #585858",
                      borderRadius: "999px",
                    }}
                  >
                    {
                      <item.icon
                        color={item.completed ? "#179D52" : "#585858"}
                      />
                    }
                  </div>
                  {timeline.length !== index + 1 && (
                    <div
                      className="flex-grow-1"
                      style={{
                        minHeight: "51px",
                        width: "1px",
                        backgroundColor: "#585858",
                      }}
                    />
                  )}
                </div>
                <div className="flex-grow-1 flex flex-column">
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    {item.title}
                  </h1>
                  {item.value && (
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "300",
                        fontSize: "12px",
                        color: "#585858",
                      }}
                    >
                      {item.value}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

interface ActivityTimelineProps {
  chatSessions: ChatSession[];
  selectedChatId: string | undefined;
  chatDetails?: any[];
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  chatSessions,
  selectedChatId,
  chatDetails,
}) => {
  const [clientType, setClientType] = useState<number | null>(null);

  useEffect(() => {
    // Retrieve clientType (replace this with the actual method to get clientType)
    const clientTypeFromCookie = Number(
      utils.getCookie(CookiesConstant.clientType)
    );
    setClientType(clientTypeFromCookie);
  }, []);
  // Combine existing chatSessions with new chatDetails
  const combinedSessions = React.useMemo(() => {
    // If chatDetails exists and has items, merge it with existing chatSessions
    if (chatDetails && chatDetails.length > 0) {
      const newSessionsMap = new Map(
        chatDetails.map((detail) => [detail.id, detail])
      );

      // Merge existing sessions with new details
      return chatSessions
        .map((session) => newSessionsMap.get(session.id) || session)
        .concat(
          // Add any completely new sessions from chatDetails
          chatDetails.filter(
            (detail) =>
              !chatSessions.some((session) => session.id === detail.id)
          )
        );
    }

    // If no new details, return original chatSessions
    return chatSessions;
  }, [chatSessions, chatDetails]);

  // Find the selected chat session from combined sessions
  const selectedSession = combinedSessions.find(
    (session) => session.id === selectedChatId
  );

  // Find corresponding chat details
  const selectedChatDetail = chatDetails?.find((detail) =>
    selectedSession?.participants.some(
      (participant) => participant.participantId === detail.participantId
    )
  );

  const juggler: Participant = selectedSession?.participants.find(
    (participant) =>
      clientType === 0
        ? participant.jugglerTypes === 1
        : participant.jugglerTypes === 2
  );

  return (
    <div className={styles.container}>
      <main className={styles.mainContent}>
        <div className={styles.activityColumn}>
          {selectedSession && (
            <div
              key={selectedSession.id}
              className={`${styles.chatMessage} ${
                selectedSession.unreadMessages ? styles.unreadMessage : ""
              }`}
            >
              {/* Participant Information */}
              {selectedSession.participants
                // .filter((participant) => participant.jugglerTypes === 2)
                .filter((participant) =>
                  clientType === 0
                    ? participant.jugglerTypes === 1
                    : participant.jugglerTypes === 2
                )

                .map((participant) => (
                  <React.Fragment key={participant.participantId}>
                    <div className={styles.participantContainer}>
                      <img
                        src={
                          participant.imageUrl
                            ? `${environment.getStorageURL(
                                window.location.hostname
                              )}/images/${participant.imageUrl}`
                            : sample
                        }
                        alt={`${participant.firstName} ${participant.lastInitial}'s profile`}
                        className={styles.chatPhoto}
                      />
                      <span
                        style={{
                          fontSize: "20px",
                          fontWeight: "700",
                          color: "#585858",
                        }}
                      >
                        {participant.firstName} {participant.lastInitial}
                      </span>
                    </div>
                  </React.Fragment>
                ))}
              {/* Chat Details Section */}
              {selectedChatDetail && (
                <div className={styles.chatDetailsSection}>
                  <h3>Chat Details</h3>
                  <div className={styles.detailsContainer}>
                    {Object.entries(selectedChatDetail).map(([key, value]) => {
                      // Filter out or format specific keys as needed
                      if (key === "participantId") return null;

                      return (
                        <div key={key} className={styles.detailItem}>
                          <strong>
                            {key
                              .replace(/([A-Z])/g, " $1")
                              .replace(/^./, (str) => str.toUpperCase())}
                          </strong>
                          :
                          {typeof value === "object"
                            ? JSON.stringify(value)
                            : String(value)}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Fallback if no session is selected */}
          {!selectedSession && (
            <div className={styles.noSelectionMessage}></div>
          )}
        </div>
      </main>
      <div className="flex-grow-1 flex">
        {/* {juggler && (
          <Activity
            helperId={juggler.participantId}
            name={juggler.publicName}
          />
        )} */}
      </div>
    </div>
  );
};

export default ActivityTimeline;
