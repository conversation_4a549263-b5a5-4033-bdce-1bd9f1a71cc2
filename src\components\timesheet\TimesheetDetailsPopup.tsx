import React, { useState, useEffect } from 'react';
import AwaitingConfirmationCard from '../../containers/Common/payments/Common/AwaitingConfirmationCard';
import { TimesheetDetails } from '../../hooks/useTimesheetDetails';
import Service from '../../services/services';
import useLoader from '../../hooks/LoaderHook';

interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

interface TimesheetDetailsPopupProps {
  selectedEntry: TimesheetEntry | null;
  timesheetDetails: TimesheetDetails | null;
  onClose: () => void;
  onApprovalSuccess?: () => void;
}

// Utility functions for time conversion and formatting
const convertTo24Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [time, modifier] = timeStr.split(" ");
  if (!time || !modifier) return timeStr; // fallback
  let [hours, minutes] = time.split(":").map(Number);

  if (modifier.toUpperCase() === "PM" && hours < 12) {
    hours += 12;
  }
  if (modifier.toUpperCase() === "AM" && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

const convertTo12Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [hours, minutes] = timeStr.split(":").map(Number);
  
  if (isNaN(hours) || isNaN(minutes)) return timeStr;
  
  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours % 12 || 12;
  
  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};

const formatTimeForDisplay = (timeStr: string): string => {
  if (!timeStr) return '';
  
  // If already in AM/PM format, return as is
  if (timeStr.includes('AM') || timeStr.includes('PM') || timeStr.includes('am') || timeStr.includes('pm')) {
    return timeStr;
  }
  
  // If in 24-hour format, convert to 12-hour
  return convertTo12Hour(timeStr);
};

const calculateHours = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const start = new Date(`1970-01-01T${startTime}`);
  const end = new Date(`1970-01-01T${endTime}`);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.warn("Invalid time format", { startTime, endTime });
    return 0;
  }

  const diffMs = end.getTime() - start.getTime();
  const hours = diffMs / (1000 * 60 * 60);
  return parseFloat(hours.toFixed(2));
};

const calculateTotalPrice = (hours: number, rate: number): number => {
  if (isNaN(hours) || isNaN(rate)) return 0;
  return parseFloat((hours * rate).toFixed(2));
};

const TimesheetDetailsPopup: React.FC<TimesheetDetailsPopupProps> = ({
  selectedEntry,
  timesheetDetails,
  onClose,
  onApprovalSuccess
}) => {
  const { enableLoader, disableLoader } = useLoader();

  if (!selectedEntry || !timesheetDetails) return null;

  // Helper function to get initial formatted times
  const getInitialFormattedTime = (timeStr: string) => {
    return formatTimeForDisplay(timeStr || '');
  };

  // State for editable times - initialize with current values immediately
  const [editableStartTime, setEditableStartTime] = useState(() => 
    getInitialFormattedTime(timesheetDetails.jobStartTime || '')
  );
  const [editableEndTime, setEditableEndTime] = useState(() => 
    getInitialFormattedTime(timesheetDetails.jobEndTime || '')
  );
  const [rate, setRate] = useState(() => Number(timesheetDetails.price) || 0);

  // Update state when timesheetDetails changes (for subsequent updates)
  useEffect(() => {
    if (timesheetDetails) {
      const startTimeFormatted = getInitialFormattedTime(timesheetDetails.jobStartTime || '');
      const endTimeFormatted = getInitialFormattedTime(timesheetDetails.jobEndTime || '');
      
      // Only update if the values are different to avoid unnecessary re-renders
      if (startTimeFormatted !== editableStartTime) {
        setEditableStartTime(startTimeFormatted);
      }
      if (endTimeFormatted !== editableEndTime) {
        setEditableEndTime(endTimeFormatted);
      }
      if (Number(timesheetDetails.price) !== rate) {
        setRate(Number(timesheetDetails.price) || 0);
      }
      
      console.log('Updated times:', {
        original: { start: timesheetDetails.jobStartTime, end: timesheetDetails.jobEndTime },
        formatted: { start: startTimeFormatted, end: endTimeFormatted },
        current: { start: editableStartTime, end: editableEndTime }
      });
    }
  }, [timesheetDetails, editableStartTime, editableEndTime, rate]);

  // Ensure we have valid times for calculation (fallback to original if editable is empty)
  const displayStartTime = editableStartTime || getInitialFormattedTime(timesheetDetails.jobStartTime || '');
  const displayEndTime = editableEndTime || getInitialFormattedTime(timesheetDetails.jobEndTime || '');
  const displayRate = rate || Number(timesheetDetails.price) || 0;

  // Calculate hours and total based on current display times
  const startTime24 = convertTo24Hour(displayStartTime);
  const endTime24 = convertTo24Hour(displayEndTime);
  const hoursWorked = calculateHours(startTime24, endTime24);
  const totalPrice = calculateTotalPrice(hoursWorked, displayRate);

  console.log('TimesheetDetailsPopup - Current calculation:', {
    original: { start: timesheetDetails.jobStartTime, end: timesheetDetails.jobEndTime },
    editable: { start: editableStartTime, end: editableEndTime },
    display: { start: displayStartTime, end: displayEndTime },
    calculated: { startTime24, endTime24, hoursWorked, totalPrice }
  });

  const handleApprove = async () => {
    if (!timesheetDetails) return;

    enableLoader();
    
    // Use the display times for the payload (edited or original)
    const payload = {
      timesheetId: timesheetDetails.timesheetId,
      JobId: timesheetDetails.jobId,
      ApplicantId: timesheetDetails.applicantId,
      NewJobStartTime: displayStartTime, // Use display start time
      NewJobEndTime: displayEndTime, // Use display end time
      NewEstimatedJobHours: hoursWorked, // Use calculated hours
      NewEstimatedJobValue: totalPrice, // Use calculated total
      NewPrice: displayRate, // Use display rate
      userId: timesheetDetails.userId,
    };

    console.log('TimesheetDetailsPopup - approval payload with edited times:', payload);

    try {
      await new Promise<void>((resolve, reject) => {
        Service.postUpdateHistory(
          (response: any) => {
            console.log("Timesheet approved:", response);
            resolve();
          },
          (error: any) => {
            console.error("Error approving timesheet:", error);
            reject(error);
          },
          payload
        );
      });

      onApprovalSuccess?.();
      onClose();
    } catch (error) {
      console.error('Approval failed:', error);
    } finally {
      disableLoader();
    }
  };

  return (
    <div className="overlay-popup">
      <div className="slide-up-card">
        <AwaitingConfirmationCard
          profileName={`${timesheetDetails.firstName} ${timesheetDetails.lastName}`}
          profileImage={timesheetDetails.originalImageUrl}
          jobType={timesheetDetails.jobType}
          jobDate={timesheetDetails.jobDate}
          jobAddress={timesheetDetails.formattedAddress}
          baseRate={displayRate}
          extraHoursRate={timesheetDetails.overtimeRate}
          initialTimesheetRows={[
            {
              start: displayStartTime, // Use display AM/PM time (with fallback)
              finish: displayEndTime, // Use display AM/PM time (with fallback)
              hours: hoursWorked,
              rate: displayRate,
              total: totalPrice,
            },
          ]}
          onSubmit={handleApprove}
          onGoBack={onClose}
        />
      </div>
    </div>
  );
};

export default TimesheetDetailsPopup;
