// AccountSidePanel.tsx
import React, { useState } from "react";
import styles from "../Common/styles/account-side-panel.module.css";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import userprofile from "../../assets/images/sample_profile.png";
import { removeSession } from "../../store/slices/sessionInfoSlice";
import { updateAccountAndSettingsActiveTabBuisness, updateChatWindowState, updateShowProfileActivation } from "../../store/slices/applicationSlice";
import { useNavigate } from "react-router-dom";
import { Button } from "primereact/button";
import aboutMeIcon from "../../assets/images/Icons/about_me.png";
import familyIcon from "../../assets/images/Icons/my_family.png";
import childrenIcon from "../../assets/images/Icons/my_child.png";
import addressIcon from "../../assets/images/Icons/my_addresses.png";
import paymentsIcon from "../../assets/images/Icons/payments.png";
import familyMembershipIcon from "../../assets/images/Icons/family_membership.png";
import settingsIcon from "../../assets/images/Icons/settings.png";
import logoutIcon from "../../assets/images/Icons/logout.png";
import chatIcon from "../../assets/images/Icons/chat.png";
import giftIcon from "../../assets/images/Icons/refer.png";
import { updateActiveTabIndex } from "../../store/slices/accountSettingSlice";
import utils from "../../components/utils/util";

interface Tab {
  index: number;
  icon: React.ReactNode; // Allow JSX elements like <LuSmilePlus /> or string icons
  text: string;
  content: JSX.Element;
  onClick?: () => void;
}

interface SidePanelProps {}

const AccountSidePanelBuisness: React.FC<SidePanelProps> = ({}) => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const accountSettings = useSelector((state: RootState) => state.accountSetting);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const tabs: Tab[] = [
    {
      index: 29,
      icon: <img src={aboutMeIcon} alt="Account holder" width="14.4px" height="14.47px" />, // Use images instead of icons
      text: "Account holder",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
    },
    {
      index: 30,
      icon: <img src={familyIcon} alt="My Business" width="14.4px" height="12.92px" />,
      text: "My Business",
      content: <p>You can chat with your helper here.</p>,
    },
    ...(sessionInfo.data["client"]["clientCategory"] === 3
      ? [
          {
            index: 36,
            icon: <img src={addressIcon} alt="My Addresses" width="14.32px" height="14.32px" />,
            text: "My Addresses",
            content: <p>You can Add adress here.</p>,
          },
        ]
      : []),
    // {
    //   icon: (
    //     <img
    //       src={childrenIcon}
    //       alt="My Children"
    //       width="16.5px"
    //       height="15.82px"
    //     />
    //   ),
    //   text: "My Children",
    //   content: <p>Here you can rate your helper after the service.</p>,
    // },
    // {
    //   icon: (
    //     <img
    //       src={addressIcon}
    //       alt="My Addresses"
    //       width="14.32px"
    //       height="14.32px"
    //     />
    //   ),
    //   text: "My Addresses",
    //   content: <p>Here you can rate your helper after the service.</p>,
    // },
  ];
  const Billing: Tab[] = [
    {
      index: 31,
      icon: <img src={paymentsIcon} alt="Payments" width="13.5px" height="15.07px" />, // Use images instead of icons
      text: "Payments",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
    },
    {
      index: 32,
      icon: <img src={familyMembershipIcon} alt="Family Membership" width="9.33px" height="16.07px" />,
      text: "Pricing",
      content: <p>You can chat with your helper here.</p>,
    },
  ];
  const Settings: Tab[] = [
    {
      index: 33,
      icon: <img src={settingsIcon} alt="General Settings" width="16px" height="16.08px" />, // Use image instead of icon
      text: "General Settings",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
    },
    {
      index: 34,
      icon: <img src={logoutIcon} alt="Log out" width="16px" height="16.08px" />,
      text: "Log out",
      content: <p>Logging out...</p>, // Content shown before redirection
      onClick: () => {
        try {
          dispatch(removeSession()); // Your logout action
          dispatch(updateShowProfileActivation(false)); // Other session-related actions
          utils.obliterateEverything();
        } catch (error) {
          console.error("Logout failed", error);
        } finally {
          navigate("/"); // Redirect to home or login page after logout
        }
      },
    },
  ];
  const Support: Tab[] = [
    {
      index: 35,
      icon: <img src={chatIcon} alt="Log out" width="16px" height="16.08px" />,
      text: "Contact Customer Service",
      content: <p>Here you can discover helpers nearby for your needs.</p>,
      onClick: () => {
        dispatch(updateChatWindowState(true));
      },
    },
  ];

  return (
    <div className={styles.accountSidePanel}>
      <div className={styles.header}>
        <div className={`${styles.userProfile} ${styles.profileClosed}`}>
          <img src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? userprofile} alt="Profile" className={styles.profilePhoto} />
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              marginLeft: "10px",
              marginRight: "10px",
            }}
          >
            <p className={styles.profileName}>Hi {sessionInfo.loading ? "" : sessionInfo.data["firstName"]}</p>
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.accountSidePanelcontentDiv}>
          <p className={styles.myaccounttitle}>My Account</p>
          {/* Tab Component */}
          <div className={styles.verticalTabContainer}>
            <div className={styles.tabs}>
              {tabs.map((tab, index) => (
                <div
                  key={index}
                  className={`${styles.tabItem} ${accountSettings.activeTabIndex === tab.index ? styles.activeTab : ""} ${
                    accountSettings.activeTabIndex === tab.index ? styles.activeTabColor : ""
                  }`}
                  onClick={() => {
                    dispatch(updateActiveTabIndex(tab.index));

                    dispatch(updateAccountAndSettingsActiveTabBuisness(index));
                  }}
                >
                  <div className={styles.spaceDiv} data-index={index === 0 ? "first" : index === 3 ? "last" : ""} />
                  {/* Icon inside the tab */}
                  <div className={styles.iconContainer}>{typeof tab.icon === "string" ? <i className={tab.icon}></i> : tab.icon}</div>
                  <span>{tab.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Render the content of the active tab */}
          <div className={styles.tabContent}>{/* {tabs[activeTab].content} */}</div>
        </div>
        <div className={styles.contentDiv}>
          <div className={styles.accountSidePanelcontentDiv}>
            <p className={styles.myaccounttitle}>Billing</p>
            {/* Tab Component */}
            <div className={styles.verticalTabContainer}>
              <div className={styles.tabs}>
                {Billing.map((tab, index) => (
                  <div
                    key={index}
                    className={`${styles.tabItem} ${accountSettings.activeTabIndex === tab.index ? styles.activeTab : ""} ${
                      accountSettings.activeTabIndex === tab.index ? styles.activeTabColor : ""
                    }`}
                    onClick={() => {
                      dispatch(updateActiveTabIndex(tab.index));
                      dispatch(updateAccountAndSettingsActiveTabBuisness(index + 5));
                    }}
                  >
                    <div className={styles.spaceDiv} data-index={index === 0 ? "first" : index === 3 ? "last" : ""} />
                    {/* Icon inside the tab */}
                    <div className={styles.iconContainer}>
                      {typeof tab.icon === "string" ? <i className={tab.icon} style={{ fontSize: "18px" }}></i> : tab.icon}
                    </div>
                    <span>{tab.text}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Render the content of the active tab */}
            <div className={styles.tabContent}>{/* {tabs[activeTab].content} */}</div>
          </div>
        </div>
        <div className={styles.contentDiv}>
          <div className={styles.accountSidePanelcontentDiv}>
            <p className={styles.myaccounttitle}>Settings</p>
            <div className={styles.verticalTabContainer}>
              <div className={styles.tabs}>
                {Settings.map((tab, index) => (
                  <div
                    key={index}
                    className={`${styles.tabItem} ${accountSettings.activeTabIndex === tab.index ? styles.activeTab : ""} ${
                      accountSettings.activeTabIndex === tab.index ? styles.activeTabColor : ""
                    }`}
                    onClick={() => {
                      dispatch(updateActiveTabIndex(tab.index));
                      dispatch(updateAccountAndSettingsActiveTabBuisness(index + 7));
                      if (tab.onClick) {
                        tab.onClick(); // Trigger the onClick for logout
                      }
                    }}
                  >
                    <div className={styles.spaceDiv} data-index={index === 0 ? "first" : index === 3 ? "last" : ""} />
                    {/* Icon inside the tab */}
                    <div className={styles.iconContainer}>
                      {typeof tab.icon === "string" ? <i className={tab.icon} style={{ fontSize: "18px" }}></i> : tab.icon}
                    </div>
                    <span>{tab.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className={styles.contentDiv}>
          <div className={styles.accountSidePanelcontentDiv}>
            <p className={styles.myaccounttitle}>Support</p>
            <div className={styles.verticalTabContainer}>
              <div className={styles.tabs}>
                {Support.map((tab, index) => (
                  <div
                    key={index}
                    className={`${styles.tabItem} ${accountSettings.activeTabIndex === tab.index ? styles.activeTab : ""} ${
                      accountSettings.activeTabIndex === tab.index ? styles.activeTabColor : ""
                    }`}
                    onClick={() => {
                      if (tab.text !== "Contact Customer Service") {
                        dispatch(updateActiveTabIndex(tab.index));
                        dispatch(updateAccountAndSettingsActiveTabBuisness(index + 9));
                      }

                      if (tab.onClick) {
                        tab.onClick(); // Trigger the onClick for logout
                      }
                    }}
                  >
                    <div className={styles.spaceDiv} data-index={index === 0 ? "first" : index === 3 ? "last" : ""} />
                    {/* Icon inside the tab */}
                    <div className={styles.iconContainer}>
                      {typeof tab.icon === "string" ? <i className={tab.icon} style={{ fontSize: "18px" }}></i> : tab.icon}
                    </div>
                    <span>{tab.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        {/* <div>
                    <Button
                        label="Get $10 for each friend you refer"
                        icon={
                            <img src={giftIcon} alt="Gift Icon" width="14.4px" height="14.48px" />
                        } // Use image instead of icon
                        className={styles.referbtn}
                        iconPos="left" // Icon on the left
                    />
                </div> */}
      </div>
    </div>
  );
};

export default AccountSidePanelBuisness;
