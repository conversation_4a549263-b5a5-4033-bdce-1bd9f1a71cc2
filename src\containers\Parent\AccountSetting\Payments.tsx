import React, { ChangeEvent, useEffect, useState } from "react";
import { Divider } from "primereact/divider";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import c from "../../../helper/juggleStreetConstants";
import Service from "../../../services/services";
import styles from "../styles/invoice.module.css";
import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import useLoader from "../../../hooks/LoaderHook";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  Elements,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import {
  fetchNoRefreshSessionInfo,
  refreshAccount,
  updateUser,
} from "../../../store/tunks/sessionInfoTunk";
import { GoLock } from "react-icons/go";
import environment from "../../../helper/environment";
import { loadStripe } from "@stripe/stripe-js";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import Membership from "../../Helper/ProfileCompletnessWizard/Membership";
import useIsMobile from "../../../hooks/useIsMobile";

// Types and Interfaces
interface PaymentPlan {
  price: number;
  subscriptionType: string;
  subscriptionFull: string;
}

interface InvoiceData {
  date: string;
  paid: boolean;
  description: string;
  subtotal: number;
  tax: number;
  taxPercent: number;
  total: number;
  amountDue: number;
  failureCode: string | null;
  failureMessage: string | null;
  id: number;
}
interface CancellationDialogProps {
  visible: boolean;
  onHide: () => void;
  onSuccess: () => void;
}
interface UpdateCardDialogProps {
  paymentType?: keyof typeof c.userPaymentType
  visible: boolean;
  onHide: () => void;
  onSuccess: () => void;
}

interface CardDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  name: string;
}

// Constants
const PAYMENT_PLANS: Record<number, PaymentPlan> = {
  [c.userPaymentType.ANNUAL_CHILDCARE_SUBSCRIPTION180]: {
    price: 180,
    subscriptionType: "Annually",
    subscriptionFull: "Complete Family Care Subscription",
  },
  // [c.userPaymentType.MONTHLY_CHILDCARE_SUBSCRIPTION90]: {
  //   price: 90,
  //   subscriptionType: "Monthly",
  //   subscriptionFull: "Complete Family Care Subscription",
  // },
  [c.userPaymentType.ANNUAL_BUSINESS_TIER1_SUBSCRIPTION_1500]: {
    price: 1500,
    subscriptionType: "Annually",
    subscriptionFull: "Juggle St Pro",
  },
  [c.userPaymentType.QUARTERLY_BUSINESS_TIER2_SUBSCRIPTION_750]: {
    price: 750,
    subscriptionType: "Quarterly",
    subscriptionFull: "Juggle St Pro",
  },
  // [c.userPaymentType.QUARTERLY_BUSINESS_TIER1_SUBSCRIPTION_500]: {
  //   price: 500,
  //   subscriptionType: "Quarterly",
  //   subscriptionFull: "Juggle St Pro",
  // },
  [c.userPaymentType.annualHelperStandardSubscription40]: {
    price: 40,
    subscriptionType: "Annually",
    subscriptionFull: "Annual Membership",
  },
  [c.userPaymentType.annualHelperStandardSubscription30]: {
    price: 30,
    subscriptionType: "Annually",
    subscriptionFull: "Annual Membership",
  },
  [c.userPaymentType.annualHelperStandardSubscription24]: {
    price: 24,
    subscriptionType: "Annually",
    subscriptionFull: "Annual Membership",
  },
  [c.userPaymentType.monthlyHelperStandardSubscription5]: {
    price: 5,
    subscriptionType: "Monthly",
    subscriptionFull: "Monthly Subscription",
  },
  [c.userPaymentType.quarterlyHelperStandardSubscription15]: {
    price: 15,
    subscriptionType: "Quarterly",
    subscriptionFull: "Quarterly Subscription",
  },
  [c.userPaymentType.quarterlyHelperStandardSubscription20]: {
    price: 20,
    subscriptionType: "Quarterly",
    subscriptionFull: "Quarterly Subscription",
  },
  [c.userPaymentType.monthlyChildcareSubsription90]: {
    price: 90,
    subscriptionType: "Monthly",
    subscriptionFull: "Complete Family Care Subscription",
  },
  [c.userPaymentType.quarterlyChildcareSubsription90]: {
    price: 90,
    subscriptionType: "Quarterly",
    subscriptionFull: "Complete Family Care Subscription",
  },
  [c.userPaymentType.quarterlyBusinessTier1Subscription500]: {
    price: 500,
    subscriptionType: "Quarterly",
    subscriptionFull: "Juggle St Pro",
  },
  [c.userPaymentType.quarterlyBusinessTier2Subscription500]: {
    price: 500,
    subscriptionType: "Quarterly",
    subscriptionFull: "Juggle St Pro",
  },
  [c.userPaymentType.MONTHLY_STANDARD_SUBSCRIPTION]: {
    price: 15,
    subscriptionType: "Monthly",
    subscriptionFull: "Monthly Subscription",
  },
  [c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION]: {
    price: 40,
    subscriptionType: "Quarterly",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.ANNUAL_FOUNDING_MEMBER]: {
    price: 75,
    subscriptionType: "Annually",
    subscriptionFull: "Yearly Subscription",
  },
  [c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION]: {
    price: 95,
    subscriptionType: "Annually",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_120]: {
    price: 120,
    subscriptionType: "Annually",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_60]: {
    price: 60,
    subscriptionType: "Quarterly",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_180]: {
    price: 180,
    subscriptionType: "Annually",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_75]: {
    price: 75,
    subscriptionType: "Quarterly",
    subscriptionFull: "Unlimited Jobs",
  },
  [c.userPaymentType.ANNUAL_BABYSITTING_SUBSCRIPTION60]: {
    price: 60,
    subscriptionType: "Annually",
    subscriptionFull: "Babysitting Only Subscription",
  },
  [c.userPaymentType.ANNUAL_BABYSITTING_SUBSCRIPTION120]: {
    price: 120,
    subscriptionType: "Annually",
    subscriptionFull: "Babysitting Only Subscription",
  },
  [c.userPaymentType.MONTHLY_BABYSITTING_SUBSCRIPTION40]: {
    price: 40,
    subscriptionType: "Monthly",
    subscriptionFull: "Babysitting Only Subscription",
  },
  [c.userPaymentType.ANNUAL_CHILDCARE_SUBSCRIPTION120]: {
    price: 120,
    subscriptionType: "Annually",
    subscriptionFull: "Complete Family Care Subscription",
  },
  // [c.userPaymentType.QUARTERLY_CHILDCARE_SUBSCRIPTION90]: {
  //   price: 90,
  //   subscriptionType: "Quarterly",
  //   subscriptionFull: "Complete Family Care Subscription",
  // },
  [c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_150]: {
    price: 150,
    subscriptionType: "Annually",
    subscriptionFull: "Complete Family Care Subscription",
  },
  [c.userPaymentType.CONCIERGE_SUBSCRIPTION]: {
    price: 0, // Price not specified in original code
    subscriptionType: "Custom",
    subscriptionFull: "Concierge Service",
  },
  [c.userPaymentType.ANNUAL_AU_PAIR_SUBSCRIPTION195]: {
    price: 195,
    subscriptionType: "Annually",
    subscriptionFull: "Au Pair Subscription",
  },
  [c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION195]: {
    price: 195,
    subscriptionType: "Annually",
    subscriptionFull: "Tutoring Subscription",
  },
  [c.userPaymentType.ANNUAL_PRIMARY_SCHOOL_TUTORING_SUBSCRIPTION120]: {
    price: 120,
    subscriptionType: "Annually",
    subscriptionFull: "Primary School Tutoring Subscription",
  },
  [c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION90]: {
    price: 90,
    subscriptionType: "Annually",
    subscriptionFull: "Tutoring Subscription",
  },
  [c.userPaymentType.ANNUAL_HIGH_SCHOOL_TUTORING_SUBSCRIPTION195]: {
    price: 195,
    subscriptionType: "Annually",
    subscriptionFull: "High School Tutoring Subscription",
  },
  [c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION245]: {
    price: 245,
    subscriptionType: "Annually",
    subscriptionFull: "Tutoring Subscription",
  },
  [c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION180]: {
    price: 180,
    subscriptionType: "Annually",
    subscriptionFull: "Tutoring (In-home and Online) Subscription",
  },
  [c.userPaymentType.ANNUAL_FAMILY_CARE_SUBSCRIPTION240]: {
    price: 240,
    subscriptionType: "Annually",
    subscriptionFull: "Complete Family Care Subscription",
  },
  [c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION360]: {
    price: 360,
    subscriptionType: "Annually",
    subscriptionFull: "Au Pair Premium Subscription",
  },
  [c.userPaymentType.ANNUAL_BUSINESS_TIER2_SUBSCRIPTION_1500]: {
    price: 1500,
    subscriptionType: "Annually",
    subscriptionFull: "Juggle St Pro",
  },
};

// Utility Service
class PlanService {
  static getPlanDetails(paymentType: number): PaymentPlan {
    const plan = PAYMENT_PLANS[paymentType];
    if (!plan) {
      throw new Error("Invalid payment type");
    }
    return plan;
  }
  static canUpgrade(paymentType: number): boolean {
    switch (paymentType) {
      case c.userPaymentType.monthlyHelperStandardSubscription5:
      case c.userPaymentType.quarterlyHelperStandardSubscription15:
      case c.userPaymentType.quarterlyHelperStandardSubscription20:
        return true;
      case c.userPaymentType.monthlyChildcareSubsription90:
      case c.userPaymentType.quarterlyChildcareSubsription90:
        return false;
      case c.userPaymentType.quarterlyBusinessTier1Subscription500:
      case c.userPaymentType.quarterlyBusinessTier2Subscription500:
        return false;
      default:
        return false;
    }
  }
}

// Reusable Components
interface ErrorMessageProps {
  message: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ message }) => (
  <div className="text-red-500">{message}</div>
);

interface LoadingProps {
  message?: string;
}

const Loading: React.FC<LoadingProps> = ({ message = "Loading..." }) => (
  <div className={styles.loading}>{message}</div>
);
const CancellationDialog: React.FC<CancellationDialogProps> = ({
  visible,
  onHide,
  onSuccess,
}) => {
  const [selectedReason, setSelectedReason] = useState(null);
  const [selectedAutoRenew, setSelectedAutoRenew] = useState(null);
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();
  const cancellationReasons = [
    {
      value: 104,
      label:
        "Satisfied – childcare currently sorted, if/when that changes, I'll be back",
    },
    {
      value: 105,
      label: "Satisfied – I no longer need Juggle St, kids are all grown-up",
    },
    {
      value: 106,
      label:
        "Satisfied – no longer need childcare due to new working arrangements",
    },
    {
      value: 107,
      label:
        "Satisfied – my family situation has changed and no longer need it",
    },
    {
      value: 108,
      label: "Not satisfied – not enough helpers in my area to choose from",
    },
    {
      value: 109,
      label: "Not satisfied – helpers didn't meet my age/experience needs",
    },
    {
      value: 110,
      label:
        "Not satisfied – helpers didn't meet my qualification requirements",
    },
    {
      value: 111,
      label: "Not satisfied – didn't use it enough to justify the price",
    },
  ];

  const AutoRenewOptions = [
    {
      label: "Keep My Account Active and continue to get job invites",
      value: "301",
    },
    { label: "Hide My Account – login anytime to reactivate", value: "302" },
    { label: "Delete My Account", value: "303" },
  ];
  const handleConfirm = async () => {
    const payload = {
      ...(sessionInfo.data as object),
      autoRenew: true,
    };
    if (!selectedReason) return;
    enableLoader();
    try {
      await Service.payments({
        actionType: c.paymentActionType.DEACTIVATE_MEMBERSHIP,
        subscriptionCancellationReason: selectedReason,
      });
      await dispatch(updateUser({ payload })); // Ensure updateUser is awaited
      disableLoader();
      onSuccess();
      onHide();
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
      disableLoader();
      // Handle error (e.g., show error message)
    }
  };
  const handleAutoNewConfirm = async () => {
    const payload = {
      ...(sessionInfo.data as object),
      autoRenew: true,
    };
    if (!selectedAutoRenew) return;
    enableLoader();
    try {
      await Service.payments({
        actionType: c.paymentActionType.DEACTIVATE_MEMBERSHIP,
        subscriptionCancellationReason: selectedAutoRenew,
      });
      await dispatch(updateUser({ payload })); // Ensure updateUser is awaited
      dispatch(fetchNoRefreshSessionInfo());
      disableLoader();
      onSuccess();
      onHide();
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
      disableLoader();
      // Handle error (e.g., show error message)
    }
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  // const activeUntil = sessionInfo.data["activeUntil"] || '';
  const activeUntil = sessionInfo?.data["activeUntil"]
    ? formatDate(sessionInfo.data["activeUntil"])
    : "N/A";
  return (
    <>
      {clientType === "0" ? (
        <Dialog
          visible={visible}
          onHide={onHide}
          style={{ width: "50%", alignItems: "center" }}
          content={
            <div className={styles.DialogContent}>
              <div>
                <IoClose onClick={onHide} className={styles.CloseBtn} />
              </div>
              <div className="p-4">
                <h3 className={styles.invoiceTitle}>
                  Your Membership will remain active until {activeUntil} and
                  your credit card will NOT be charged again. What would you
                  like to happen on this date?
                </h3>
                <div className={styles.mainDiv}>
                  {AutoRenewOptions.map((autorenew) => (
                    <div key={autorenew.value} className={styles.radioOption}>
                      <input
                        type="radio"
                        id={`autorenew_${autorenew.value}`}
                        name="AutoRenewOptions"
                        value={autorenew.value}
                        onChange={() => setSelectedAutoRenew(autorenew.value)}
                        checked={selectedAutoRenew === autorenew.value}
                        className={styles.radioInput}
                      />
                      <label
                        htmlFor={`autorenew_${autorenew.value}`}
                        className={styles.radioLabel}
                      >
                        {autorenew.label}
                      </label>
                    </div>
                  ))}
                </div>
                <div className={styles.btnContaioner}>
                  <button onClick={onHide} className={styles.cancelBtnSec}>
                    NO
                  </button>
                  <button
                    onClick={handleAutoNewConfirm}
                    disabled={!selectedAutoRenew}
                    className={styles.confirmBtn}
                    style={{
                      cursor: selectedAutoRenew ? "pointer" : "not-allowed",
                    }}
                  >
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          }
        />
      ) : (
        <Dialog
          visible={visible}
          onHide={onHide}
          content={
            <div className={styles.DialogContent}>
              <div>
                <IoClose onClick={onHide} className={styles.CloseBtn} />
              </div>
              <div className="p-4">
                <h3 className={styles.invoiceTitle}>
                  Please tell us why you're canceling:
                </h3>
                <div className={styles.mainDiv}>
                  {cancellationReasons.map((reason) => (
                    <div key={reason.value} className={styles.radioOption}>
                      <input
                        type="radio"
                        id={`reason_${reason.value}`}
                        name="cancellationReason"
                        value={reason.value}
                        onChange={() => setSelectedReason(reason.value)}
                        checked={selectedReason === reason.value}
                        className={styles.radioInput}
                      />
                      <label
                        htmlFor={`reason_${reason.value}`}
                        className={styles.radioLabel}
                      >
                        {reason.label}
                      </label>
                    </div>
                  ))}
                </div>
                <div className={styles.btnContaioner}>
                  <button onClick={onHide} className={styles.cancelBtnSec}>
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirm}
                    disabled={!selectedReason}
                    className={styles.confirmBtn}
                  >
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          }
        />
      )}
    </>
  );
};
function CustomInput<T>({
  label,
  onChange,
  mask,
  maskWith,
  showAstrict = false,
}: {
  label: string;
  onChange: (value: T) => void;
  mask?: string;
  maskWith?: string;
  showAstrict?: boolean;
}) {
  const [inputValue, setInputValue] = useState<string>("");

  function applyMask(value: string, mask: string): string {
    let maskedValue = "";
    let maskIndex = 0;

    for (let i = 0; i < value.length; i++) {
      if (maskIndex >= mask.length) {
        break;
      }

      if (mask[maskIndex] === maskWith) {
        maskedValue += maskWith;
        maskIndex++;
      }

      if (/\d/.test(mask[maskIndex])) {
        maskedValue += value[i];
        maskIndex++;
      }
    }

    return maskedValue;
  }

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    let newValue = e.target.value;
    if (newValue.trim() === "") {
      setInputValue("");
      onChange("" as unknown as T);
      return;
    }

    if (mask) {
      newValue = applyMask(newValue.replace(/\D/g, ""), mask);
    } else {
      newValue = newValue.replace(/\d/g, "");
    }

    setInputValue(newValue);
    onChange(newValue as unknown as T);
  }

  return (
    <div
      className="flex relative"
      style={{
        borderBottom: "1px solid #F1F1F1",
        position: "relative",
      }}
    >
      <input
        className="flex-grow-1 border-none px-3 py-2 w-full"
        style={{
          color: "#787777",
          fontSize: "14px",
        }}
        type="text"
        name={label.replace(/ /g, "").toLowerCase()}
        id={label.replace(/ /g, "").toLowerCase()}
        value={inputValue}
        onChange={handleChange}
      />
      {!inputValue && (
        <label
          className="absolute mx-3"
          style={{
            color: "#787777",
            fontSize: "14px",
            top: "50%",
            transform: "translateY(-50%)",
            pointerEvents: "none",
          }}
          htmlFor={label.replace(/ /g, "").toLowerCase()}
        >
          {label}{" "}
          {showAstrict && (
            <span
              style={{
                color: "#FF6359",
              }}
            >
              *
            </span>
          )}
        </label>
      )}
    </div>
  );
}
const stripePromise = loadStripe(
  environment.getStripePublishableKey(window.location.hostname)
);
const UpdateCardDialog: React.FC<UpdateCardDialogProps> = ({
  paymentType,
  visible,
  onHide,
  onSuccess,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [error, setError] = useState<string | null>(null);
  const [cardNumberDetails, setCardNumberDetails] = useState(false);
  const [cardExpiryDetails, setCardExpiryDetails] = useState(false);
  const [cardCvcDetails, setCardCvcDetails] = useState(false);
  const [nameOnCard, setNameOnCard] = useState("");
  const [valid, setValid] = useState(false);
  const { disableLoader, enableLoader } = useLoader();
  const stripe = useStripe();
  const elements = useElements();

  const handleNameChange = (value: string) => {
    setNameOnCard(value);
    setValid(
      value.length > 0 &&
      cardNumberDetails &&
      cardExpiryDetails &&
      cardCvcDetails
    );
  };
  useEffect(() => {
    setValid(
      cardNumberDetails &&
      cardExpiryDetails &&
      cardCvcDetails &&
      nameOnCard.trim() !== ""
    );
  }, [cardNumberDetails, cardExpiryDetails, cardCvcDetails, nameOnCard]);

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    enableLoader();

    if (!stripe || !elements) {
      alert("Stripe has not loaded");
      disableLoader();
      return;
    }

    const cardElement = elements.getElement(CardNumberElement);
    if (!cardElement) {
      setError("Card element not found");
      disableLoader();
      return;
    }

    try {
      const { token, error } = await stripe.createToken(cardElement, {
        name: nameOnCard,
      });

      if (error) {
        setError(error.message || "An error occurred");
      } else if (token) {
        try {
          await Service.payments({
            actionType: c.paymentActionType.UPDATE_PAYMENT_INFO,
            deviceType: c.deviceType.DESKTOP,
            tokenId: token.id,
            ...(!!paymentType ? {
              paymentType: c.userPaymentType[paymentType]
            } : {})
          });
          await dispatch(refreshAccount());
          onSuccess();
          onHide();
        } catch (e) {
          setError(e || "An error occurred");
        }
      }
    } catch (e) {
      setError("An unexpected error occurred");
    } finally {
      disableLoader();
    }
  };

  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      content={
        <div className={styles.DialogContent}>
          <div>
            <IoClose onClick={onHide} className={styles.CloseBtn} />
          </div>
          <div className="p-4">
            <h3 className={styles.invoiceTitle}>
              Update Credit Card Information
            </h3>

            <div
              className="my-4 flex flex-column"
              style={{ border: "1px solid #F0F4F7", minWidth: "250px" }}
            >
              <h1
                className="m-0 p-0 px-3 py-2"
                style={{
                  color: "#585858",
                  fontWeight: "700",
                  fontSize: "18px",
                }}
              >
                Payment details
              </h1>

              <div className="px-3 pt-2 pb-2">
                <CustomInput<string>
                  label="Name on Card"
                  onChange={handleNameChange}
                  showAstrict
                />
              </div>

              <div className="pr-3 pt-2 pb-2 pl-5">
                <CardNumberElement
                  options={{
                    disableLink: true,
                    showIcon: true,
                    style: {
                      base: {
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "#787777",
                        "::placeholder": {
                          fontSize: "14px",
                          fontWeight: "400",
                          color: "#787777",
                        },
                      },
                      invalid: {
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "#ff5252",
                      },
                      complete: {
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "#787777",
                      },
                    },
                  }}
                  onChange={(event) => {
                    setCardNumberDetails(event.complete);
                    if (event.error) {
                      setError(event.error.message);
                    } else {
                      setError(null);
                    }
                  }}
                />

                <div className="flex mt-3">
                  <div className="flex flex-column mb-3 flex-grow-1">
                    <div
                      className="flex w-min mb-3"
                      style={{
                        position: "relative",
                        textWrap: "nowrap",
                      }}
                    >
                      <p
                        className="m-0 p-0"
                        style={{
                          fontSize: "12px",
                          fontWeight: "700",
                          color: "#585858",
                        }}
                      >
                        Card expiration date
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          position: "absolute",
                          top: "0px",
                          right: "-10px",
                          color: "#FF6359",
                          fontSize: "12px",
                        }}
                      >
                        *
                      </p>
                    </div>
                    <CardExpiryElement
                      options={{
                        style: {
                          base: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                            "::placeholder": {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                            },
                          },
                          invalid: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#ff5252",
                          },
                          complete: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                          },
                        },
                      }}
                      onChange={(event) => {
                        setCardExpiryDetails(event.complete);
                        if (event.error) {
                          setError(event.error.message);
                        } else {
                          setError(null);
                        }
                      }}
                    />
                  </div>

                  <div className="flex flex-column mb-3 flex-grow-1">
                    <div
                      className="flex w-min mb-3"
                      style={{
                        position: "relative",
                        textWrap: "nowrap",
                      }}
                    >
                      <p
                        className="m-0 p-0"
                        style={{
                          fontSize: "12px",
                          fontWeight: "700",
                          color: "#585858",
                        }}
                      >
                        CVC
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          position: "absolute",
                          top: "0px",
                          right: "-10px",
                          color: "#FF6359",
                          fontSize: "12px",
                        }}
                      >
                        *
                      </p>
                    </div>
                    <CardCvcElement
                      options={{
                        style: {
                          base: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                            "::placeholder": {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                            },
                          },
                          invalid: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#ff5252",
                          },
                          complete: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                          },
                        },
                      }}
                      onChange={(event) => {
                        setCardCvcDetails(event.complete);
                        if (event.error) {
                          setError(event.error.message);
                        } else {
                          setError(null);
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {error && (
                <div className="px-3 py-2 text-red-500 text-sm">{error}</div>
              )}

              <button
                className="border-none mx-5 my-3 border-round-xl flex justify-content-center align-content-center align-items-center gap-1"
                style={{
                  color: "white",
                  fontWeight: "700",
                  fontSize: "14px",
                  paddingBlock: "8px",
                  alignItems: "center",
                  backgroundColor: stripe && valid ? "#FFA500" : "#ccc",
                  cursor:
                    stripe &&
                      cardNumberDetails &&
                      cardExpiryDetails &&
                      cardCvcDetails
                      ? "pointer"
                      : "not-allowed",
                }}
                onClick={handleSubmit}
                disabled={!stripe || !valid}
              >
                <GoLock />
                Update Card
              </button>
            </div>
          </div>
        </div>
      }
    />
  );
};

interface CurrentPlanSectionProps {
  invoiceData?: InvoiceData[];
}
// Main Components
const CurrentPlanSection: React.FC<CurrentPlanSectionProps> = ({
  invoiceData,
}) => {
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [isSubscriptionActive, setIsSubscriptionActive] = useState(true);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { loading, data } = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();

  if (loading || !data?.["paymentInfo"]) {
    return null;
  }

  const paymentType = data?.["paymentInfo"].paymentType;
  const activeUntil = sessionInfo?.data["activeUntil"]
    ? formatDate(sessionInfo.data["activeUntil"])
    : "N/A";
  const renewalPaymentType = data?.["paymentInfo"].renewalPaymentType;
  const autoRenew = data?.["paymentInfo"]?.["autoRenew"];

  const handleSubscriptionToggle = async () => {
    const isAutoRenewEnabled = sessionInfo.data?.["paymentInfo"]?.["autoRenew"];

    if (isAutoRenewEnabled) {
      setShowCancelDialog(true);
    } else {
      enableLoader();
      try {
        const payload = {
          ...(sessionInfo.data as object),
          autoRenew: true,
        };
        await Service.payments({
          actionType: c.paymentActionType.REACTIVATE_MEMBERSHIP,
        });
        await dispatch(updateUser({ payload }));
        setIsSubscriptionActive(true);
      } catch (error) {
        console.error("Failed to reactivate subscription:", error);
      }
      disableLoader();
    }
  };

  const handleUpgradeSubscription = async (newPaymentType: number) => {
    enableLoader();
    try {
      await Service.payments({
        actionType: c.paymentActionType.UPGRADE_PLAN,
        paymentType: newPaymentType,
        deviceType: c.deviceType.DESKTOP,
      });
      await dispatch(refreshAccount());
    } catch (error) {
      console.error("Failed to upgrade subscription:", error);
    }
    disableLoader();
  };

  try {
    const plan = PlanService.getPlanDetails(paymentType);
    const renewalPlan = renewalPaymentType != c.userPaymentType.FREE
      ? PlanService.getPlanDetails(renewalPaymentType)
      : null;

    const latestInvoice = invoiceData?.[0];
    // const calculateNextBillingDate = () => {
    //   const billingDate = latestInvoice ? new Date(latestInvoice.date) : new Date();
    //   let nextBillingDate = new Date(billingDate);

    //   switch (plan.subscriptionType.toLowerCase()) {
    //     case "annually":
    //       nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
    //       break;
    //     case "monthly":
    //       nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    //       break;
    //     case "quarterly":
    //       nextBillingDate.setMonth(nextBillingDate.getMonth() + 3);
    //       break;
    //     default:
    //       return "N/A";
    //   }

    //   return formatDate(nextBillingDate);
    // };

    const clientType = utils.getCookie(CookiesConstant.clientType);
    const canUpgrade = PlanService.canUpgrade(paymentType);

    // Add conditions from old code
    let planHeader = renewalPlan ? "Upgrade Confirmation" : "Current Plan";
    let activePlanInfo: string | null = null;
    let displayUpgradeOptions = true;

    if (renewalPaymentType !== c.userPaymentType.FREE) {
      planHeader = "Upgrade Confirmation";

      if (renewalPaymentType === c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION) {
        displayUpgradeOptions = false;
        activePlanInfo = `Your credit card will be billed $40 on ${activeUntil}.`;
      } else if (renewalPaymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION) {
        displayUpgradeOptions = false;
        activePlanInfo = `Your credit card will be billed $95 on ${activeUntil}.`;
      } else if (renewalPaymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_120) {
        displayUpgradeOptions = false;
        activePlanInfo = `Your credit card will be billed $120 on ${activeUntil}.`;
      } else if (renewalPaymentType === c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_60) {
        displayUpgradeOptions = false;
        activePlanInfo = `Your credit card will be billed $60 on ${activeUntil}.`;
      } else if (renewalPaymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_180) {
        displayUpgradeOptions = false;
        activePlanInfo = `Your credit card will be billed $180 on ${activeUntil}.`;
      }

      // Handle paymentType conditions for expiration text
      if (paymentType === c.userPaymentType.MONTHLY_STANDARD_SUBSCRIPTION) {
        activePlanInfo = activePlanInfo || `Your monthly subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION) {
        displayUpgradeOptions = false;
        activePlanInfo = activePlanInfo || `Your three month subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.ANNUAL_FOUNDING_MEMBER) {
        activePlanInfo = activePlanInfo || `Your annual subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION) {
        displayUpgradeOptions = false;
        activePlanInfo = activePlanInfo || `Your annual subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_120) {
        displayUpgradeOptions = false;
        activePlanInfo = activePlanInfo || `Your annual subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_60) {
        displayUpgradeOptions = false;
        activePlanInfo = activePlanInfo || `Your three month subscription will expire on ${activeUntil}.`;
      } else if (paymentType === c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_180) {
        displayUpgradeOptions = false;
        activePlanInfo = activePlanInfo || `Your annual subscription will expire on ${activeUntil}.`;
      }
    } else {
      planHeader = "Current Plan";
      activePlanInfo = autoRenew
        ? `Your credit card will be billed on ${activeUntil}.`
        : `Your membership will expire on ${activeUntil}.`;
    }

    let planDescription = "";
    switch (paymentType) {
      case c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION:
      case c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION:
      case c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_120:
      case c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_60:
      case c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_180:
      case c.userPaymentType.QUARTERLY_STANDARD_SUBSCRIPTION_75:
        planDescription = "Post unlimited one-off babysitting jobs and recurring nanny, before & after school jobs. Unlimited online chat.";
        break;
      case c.userPaymentType.ANNUAL_BABYSITTING_SUBSCRIPTION60:
      case c.userPaymentType.ANNUAL_BABYSITTING_SUBSCRIPTION120:
      case c.userPaymentType.MONTHLY_BABYSITTING_SUBSCRIPTION40:
        planDescription = "Post unlimited one-off babysitting jobs.";
        break;
      case c.userPaymentType.ANNUAL_CHILDCARE_SUBSCRIPTION120:
      case c.userPaymentType.ANNUAL_CHILDCARE_SUBSCRIPTION180:
      case c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_150:
      case c.userPaymentType.QUARTERLY_CHILDCARE_SUBSCRIPTION90:
      case c.userPaymentType.MONTHLY_CHILDCARE_SUBSCRIPTION90:
      case c.userPaymentType.ANNUAL_FAMILY_CARE_SUBSCRIPTION240:
        planDescription = "Post unlimited jobs.";
        break;
      case c.userPaymentType.ANNUAL_AU_PAIR_SUBSCRIPTION195:
        planDescription = "Post unlimited au pair jobs.";
        break;
      case c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION195:
      case c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION90:
      case c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION245:
        planDescription = "Post unlimited tutoring jobs.";
        break;
      case c.userPaymentType.ANNUAL_PRIMARY_SCHOOL_TUTORING_SUBSCRIPTION120:
        planDescription = "Post unlimited primary school tutoring jobs.";
        break;
      case c.userPaymentType.ANNUAL_HIGH_SCHOOL_TUTORING_SUBSCRIPTION195:
        planDescription = "Post unlimited high school tutoring jobs.";
        break;
      case c.userPaymentType.ANNUAL_TUTORING_SUBSCRIPTION180:
        planDescription = "Post unlimited tutoring jobs (In-home and Online).";
        break;
      case c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION360:
        planDescription = "Post unlimited jobs.";
        break;
      default:
        planDescription = "";
    }
    const displayPlanText =
      paymentType === c.userPaymentType.ANNUAL_FOUNDING_MEMBER && !renewalPlan
        ? "Founding Member $75 per annum"
        : `${renewalPlan ? renewalPlan.subscriptionFull : plan.subscriptionFull} ( $${(renewalPlan || plan).price} ${renewalPlan ? renewalPlan.subscriptionType : plan.subscriptionType} )`;
    return (
      <div className={styles.planContainer}>
        <h1 className={styles.planTitle}>{planHeader}</h1>
        <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
          <p className={styles.subscriptionText}>{displayPlanText}</p>
          {planDescription && paymentType !== c.userPaymentType.ANNUAL_FOUNDING_MEMBER && (
            <p className={styles.subscriptionText}>{planDescription}</p>
          )}
        </div>
        {activePlanInfo && (
          <p className={styles.subscriptionDate}>{activePlanInfo}</p>
        )}
        <div className="flex gap-3">
          {canUpgrade && autoRenew && (
            <button
              className={styles.cancelBtn}
              onClick={() => handleUpgradeSubscription(c.userPaymentType.ANNUAL_STANDARD_SUBSCRIPTION_180)}
            >
              Upgrade to Annual
            </button>
          )}
          <div className="flex flex-row justify-content-center align-items-center gap-3">
            <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="p-0 m-0">
              Auto Renew
            </p>
            {autoRenew ? (
              <div style={{ border: "1px solid #dfdfdf", borderRadius: "20px", paddingInline: "12px", paddingBlock: "4px" }}>
                <p className="p-0 m-0" style={{ fontSize: "14px", color: "#585858", fontWeight: "700" }}>
                  Enabled
                </p>
              </div>
            ) : (
              <p className="p-0 m-0" style={{ fontSize: "14px", color: "#585858" }}>
                Your subscription will be cancelled on {activeUntil}
              </p>
            )}
            <button
              className={`${styles.cancelBtn} ${!autoRenew ? styles.enableBtn : ""}`}
              onClick={handleSubscriptionToggle}
            >
              {autoRenew
                ? clientType === "0"
                  ? "Turn Off Auto Renew"
                  : "Cancel Subscription"
                : "Enable Auto Renew"}
            </button>
          </div>
        </div>
        <CancellationDialog
          visible={showCancelDialog}
          onHide={() => setShowCancelDialog(false)}
          onSuccess={() => setIsSubscriptionActive(false)}
        />
        {canUpgrade && (
          <Elements stripe={stripePromise}>
            <UpdateCardDialog
              paymentType="ANNUAL_STANDARD_SUBSCRIPTION_180"
              visible={showUpdateDialog}
              onHide={() => setShowUpdateDialog(false)}
              onSuccess={() => { }}
            />
          </Elements>
        )}
      </div>
    );
  } catch (error) {
    return (
      <p style={{ margin: "0px", color: "#FF6359" }}>
        Recent invoices may take a few hours to show up.
      </p>
    );
  }
};

const PaymentInformation: React.FC = () => {
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const { loading, data } = useSelector(
    (state: RootState) => state.sessionInfo
  );

  if (loading || !data?.["paymentInfo"]) {
    return null;
  }

  const handleUpdateSuccess = () => {
    // You might want to refresh the payment information here
    // or show a success message
  };

  try {
    const { cardLast4Digits } = data?.["paymentInfo"];
    return (
      <div>
        <p className={styles.cardTitle}>Credit Card on File</p>
        <div className={styles.cardNumberContainer}>
          <p className={styles.cardNumber}>XXXX XXXX XXXX {cardLast4Digits}</p>
        </div>
        <button
          className={styles.updateBtn}
          onClick={() => setShowUpdateDialog(true)}
        >
          Update Credit Card
        </button>
        <Elements stripe={stripePromise}>
          <UpdateCardDialog
            visible={showUpdateDialog}
            onHide={() => setShowUpdateDialog(false)}
            onSuccess={handleUpdateSuccess}
          />
        </Elements>
      </div>
    );
  } catch (error) {
    return <ErrorMessage message="Unable to retrieve payment information" />;
  }
};
interface InvoiceItemProps {
  label: string;
  value: string | number;
}

const InvoiceItem: React.FC<InvoiceItemProps> = ({ label, value }) => (
  <div className={styles.invoiceItem}>
    <span>{label}</span>
    <span>{value}</span>
  </div>
);
interface InvoiceRowProps {
  invoice: InvoiceData;
}

function formatDate(dateString) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = String(date.getFullYear()).slice(-2); // Get last two digits of the year
  return `${day}/${month}/${year}`;
}

// Example usage
// Output: 26/11/24


const InvoiceRow: React.FC<InvoiceRowProps> = ({ invoice }) => (
  <div className={styles.invoiceRow}>
    <div className={styles.cell}>{formatDate(new Date(invoice.date))}</div>
    <div className={styles.cell}>{invoice.description}</div>
    <div className={styles.cell}>
      <span
        className={`${styles.statusPill} ${invoice.paid ? styles.paid : styles.unpaid
          }`}
      >
        {invoice.paid ? "Paid" : "Unpaid"}
      </span>
    </div>
    <div className={`${styles.cell} ${styles.amount}`}>
      ${invoice.total.toFixed(2)}
    </div>
  </div>
);

const InvoiceList: React.FC = () => {
  const [invoices, setInvoices] = useState<InvoiceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    Service.getinvoice(
      (response: InvoiceData[]) => {
        setInvoices(response);
        setLoading(false);
        setError(null);
      },
      (error: Error) => {
        console.error("Failed to fetch invoices:", error);
        setError("Failed to load invoice data");
        setLoading(false);
      }
    );
  }, []);

  if (loading) {
    return <Loading message="Loading invoices..." />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className={styles.invoiceList}>
      <Divider />
      <h2 className={styles.invoiceHeader}>Invoices</h2>
      <div className={styles.listHeader}>
        <div className={styles.headerCell}>Billing Date</div>
        <div className={styles.headerCell}>Description</div>
        <div className={styles.headerCell}>Status</div>
        <div className={styles.headerCell}>Total Paid</div>
      </div>
      {invoices.length > 0 ? (
        invoices.map((invoice) => (
          <InvoiceRow key={invoice.id} invoice={invoice} />
        ))
      ) : (
        <div className={`${styles.noInvoices} mt-2`}>No invoices found</div>
      )}
    </div>
  );
};

const Payments: React.FC = () => {
  const [invoiceData, setInvoiceData] = useState<InvoiceData[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();
  const {isMobile}=useIsMobile();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [plan, setPlan] = useState<number>(0);
  const [subscribeEnabled, setSubscribeEnabled] = useState<boolean>(false);
  useEffect(() => {
    enableLoader();
    Service.getinvoice(
      (response: InvoiceData[]) => {
        disableLoader();
        setInvoiceData(response);
        setError(null);
      },
      (error: Error) => {
        disableLoader();
        console.error("Failed to fetch invoice:", error);
        setError("Failed to load invoice data");
        setInvoiceData(null);
      }
    );
  }, [sessionInfo]);
  const paymentType = sessionInfo.data["paymentInfo"]["paymentType"];
  const ageInYears = sessionInfo.data["ageInYears"];
  const clientType = utils.getCookie(CookiesConstant.clientType);
  if (paymentType === c.userPaymentType.FREE && ageInYears >= 18  && clientType === 0) {
    return (
      <Membership fromAccountSettong={false} showProfileCompletenessHeader={false} />
    );
  }

  return (
    <div style={{paddingTop:isMobile && "0px"}} className={styles.container}>
      <h1 className={styles.titleFirst}>Payments</h1>
      <Divider className="mb-2" />
      <CurrentPlanSection invoiceData={invoiceData} />
      {sessionInfo.data["paymentInfo"]?.["cardLast4Digits"] && (
        <>
          <Divider className="my-2" />
          <h1 className={styles.title}>Payment Details</h1>
          <PaymentInformation />
        </>
      )}
      <InvoiceList />
    </div>
  );
};

export default Payments;

