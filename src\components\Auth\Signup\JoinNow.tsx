import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AutoComplete } from "primereact/autocomplete";
import "./join-now.css";
import { useJoinNowData } from "../../../model/JoinNowContext";
import Auth from "../../../services/authService";
import c from "../../../helper/juggleStreetConstants";

interface JoinnowProps {
  signUpAs?: any;
  onNext: () => void;
  setCurrentPage: (page: number) => void;
}

interface State {
  clientType: any;
  address: {
    autoCompleteSuggestions: any;
    addressLine1: string;
    addressLine2: string;
    postCode: string;
    suburb: string;
    state: string;
  };
  clientCategory: number;
  providerType: number;
  errors: {
    clientType?: string;
    address?: string;
    clientCategory?: string;
    businessLocation?: string;
  };
}

interface UIState {
  showAddressInput: boolean;
  showClientCategory: boolean;
  showBusinessLocationInput: boolean;
  businessLocation: any;
}

const Joinnow: React.FC<JoinnowProps> = ({
  signUpAs,
  onNext,
  setCurrentPage,
}) => {
  const { joinNowData, dispatch } = useJoinNowData();
  const navigate = useNavigate();
  const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<any[]>(
    []
  );
  const [isFormValid, setIsFormValid] = useState(false);
  const [state, setState] = useState<State>({
    clientType: signUpAs || joinNowData.clientType || "",
    address: joinNowData.address || {
      addressLine1: "",
      addressLine2: "",
      postCode: "",
      suburb: "",
      state: "",
    },
    clientCategory: joinNowData.clientCategory || 0,
    providerType:
      joinNowData.providerType ||
      getProviderType(signUpAs || joinNowData.clientType),
    errors: {},
  });
  
  function getProviderType(clientType: any) {
    switch (clientType) {
      case c.lookingFor.BUSINESS_CLIENT:
        return c.providerType.BUSINESS;
      case c.lookingFor.INDIVIDUAL_CLIENT:
      case c.lookingFor.INDIVIDUAL_PROVIDER:
        return c.providerType.INDIVIDUAL;
      default:
        return c.providerType.UNSPECIFIED;
    }
  }

  const [uiState, setUIState] = useState<UIState>({
    showAddressInput: false,
    showClientCategory: false,
    showBusinessLocationInput: false,
    businessLocation: joinNowData.businessLocation || "",
  });

  useEffect(() => {
    const updates = {
      showAddressInput:
        (signUpAs === c.lookingFor.INDIVIDUAL_CLIENT ||
          signUpAs === c.lookingFor.INDIVIDUAL_PROVIDER ||
          joinNowData.clientType === c.lookingFor.INDIVIDUAL_PROVIDER ||
          joinNowData.clientType === c.lookingFor.INDIVIDUAL_CLIENT) &&
        signUpAs !== c.lookingFor.BUSINESS_CLIENT,
      showClientCategory:
        signUpAs === c.lookingFor.BUSINESS_CLIENT ||
        joinNowData.clientType === c.lookingFor.BUSINESS_CLIENT,
      showBusinessLocationInput:
        signUpAs === c.lookingFor.BUSINESS_CLIENT ||
        joinNowData.clientType === c.lookingFor.BUSINESS_CLIENT,
      businessLocation: joinNowData.businessLocation || "",
    };
    setUIState((prevUIState) => ({ ...prevUIState, ...updates }));
    checkFormValidity();
  }, [signUpAs, joinNowData]);

  const [suburbs, setSuburbs] = useState([]);
  const searchAddress = (event: { query: string }) => {
    Auth.suburbSearch(
      event.query,
      (res) => {
        setSuburbs(res);
        const suggestions = res.map(
          (item) => `${item.name}, ${(item.state as string).toUpperCase()}`
        );
        setAutoCompleteSuggestions(suggestions);
      },
      () => { }
    );
  };

  const handleAddressChange = (e) => {
    setState((prevState) => ({
      ...prevState,
      address: {
        ...prevState.address,
        addressLine1: e.target.value,
      },
    }));
    const Name = e.target.value.split(",")[0];
    const suburbInfo = suburbs.find((item) => item.name === Name);
    if (suburbInfo) {
      const updates = {
        addressLine1: e.target?.value,
        addressLine2: null,
        postCode: suburbInfo.postCode,
        state: suburbInfo.state,
        suburb: suburbInfo.name,
      };
      dispatch({ type: "UPDATE_FORM", payload: { address: updates } });
    }
    checkFormValidity();
  };

  const handleRoleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const clientType = parseInt(value);
    const providerType = getProviderType(clientType);

    const localUpdates = {
      showAddressInput:
        clientType === c.lookingFor.INDIVIDUAL_CLIENT ||
        clientType === c.lookingFor.INDIVIDUAL_PROVIDER,
      showClientCategory: clientType === c.lookingFor.BUSINESS_CLIENT,
      showBusinessLocationInput: clientType === c.lookingFor.BUSINESS_CLIENT,
      businessLocation:
        clientType !== c.lookingFor.BUSINESS_CLIENT
          ? ""
          : uiState.businessLocation,
    };

    setUIState((prevUIState) => ({ ...prevUIState, ...localUpdates }));
    const globalUpdates = {
      clientType,
      // clientCategory:
      //   clientType !== c.lookingFor.BUSINESS_CLIENT ? 0 : state.clientCategory
      //   ,
      clientCategory: clientType === c.lookingFor.INDIVIDUAL_CLIENT ? 1 : clientType === c.lookingFor.BUSINESS_CLIENT ? state.clientCategory : 0,
      providerType,
    };
    setState((prevState) => ({ ...prevState, clientType }));
    dispatch({ type: "UPDATE_FORM", payload: globalUpdates });
    checkFormValidity();
  };

  const handleRadioChange = (name: string, value: any) => {
    setState((prevState) => ({ ...prevState, [name]: value }));
    dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
    checkFormValidity();
  };

  const handleNext = (e) => {
    e.preventDefault();
    if (validateForm(e)) {
      if (
        state.clientType === c.lookingFor.INDIVIDUAL_CLIENT ||
        state.clientType === c.lookingFor.INDIVIDUAL_PROVIDER
      ) {
        onNext();
      } else if (state.clientType === c.lookingFor.BUSINESS_CLIENT) {
        setCurrentPage(5);
      } else {
        navigate("/");
      }
    }
  };

  const validateForm = (e) => {
    e.preventDefault();
    const errors: State["errors"] = {};
    if (!state.clientType) {
      errors.clientType = "Please select a role";
    }
    if (
      uiState.showAddressInput &&
      (!state.address.state || !state.address.addressLine1) &&
      !autoCompleteSuggestions.includes(state.address.addressLine1)
    ) {
      errors.address = "Please select address.";
    }
    if (uiState.showClientCategory && !state.clientCategory) {
      errors.clientCategory = "Please select account type.";
    }
    if (uiState.showBusinessLocationInput && !state.address.addressLine1) {
      errors.businessLocation = "Please select address.";
    }
    setState((prevState) => ({ ...prevState, errors }));
    return Object.keys(errors).length === 0;
  };

  const checkFormValidity = () => {
    let isValid = true;

    if (!state.clientType) {
      isValid = false;
    }

    if (
      uiState.showAddressInput &&
      (!state.address.state || !state.address.addressLine1) &&
      !autoCompleteSuggestions.includes(state.address.addressLine1)
    ) {
      isValid = false;
    }

    if (uiState.showClientCategory && !state.clientCategory) {
      isValid = false;
    }

    if (uiState.showBusinessLocationInput && !state.address.addressLine1) {
      isValid = false;
    }

    setIsFormValid(isValid);
  };

  const getBorderClass = (role: any) => {
    return state.clientType === role ? "border-green-singup" : "border-gray";
  };

  const getBorderBusnessClass = (index: number) => {
    return state.clientCategory === index
      ? "border-green-singup"
      : "border-gray";
  };

  return (
    <div className="flex pl-4 pr-4 ">
      <form
        className="mb-5 "
        style={{
          display: "flex",
          justifyContent: "center",
          flexDirection: "column",
        }}
      >
        <div
          className="flex-column flex justify-content-center align-items-center"
          style={{ marginTop: "-29px" }}
        >
          <div className="">
            <p className="h-joinnow ">Welcome!</p>
            <p className="h-joinnow-prg">
              What are you looking for on Juggle Street?
            </p>
          </div>
          {Object.values(c.lookingFor)
            .filter((role) => role !== c.lookingFor.UNSPECIFIED)
            .map((role, index) => {
              const hasError = !!state.errors.clientType;
              const borderClass = hasError
                ? "border-red"
                : getBorderClass(role);
              return (
                <div key={index} className={`field-radiobutton  ${borderClass}`}
                >
                  <input
                    type="radio"
                    id={`role${index}`}
                    value={role}
                    checked={state.clientType === role}
                    onChange={handleRoleChange}
                    style={{
                      cursor: "pointer",
                      opacity: 0,
                      position: "absolute",
                    }}
                  />
                  {state.clientType === role ? (
                    <div
                      style={{
                        width: "16px",
                        height: "16px",
                        backgroundColor: "#179D52",
                        borderRadius: "50%",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <span
                        style={{
                          color: "white",
                          fontWeight: "bold",
                          fontSize: "12px",
                          lineHeight: "12px",
                        }}
                      >
                        
                        ✓
                      </span>
                    </div>
                  ) : (
                    <div
                      style={{
                        width: "16px",
                        height: "16px",
                        border: "2px solid gainsboro",
                        borderRadius: "50%",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: "transparent",
                      }}
                    />
                  )}
                  <label
                    htmlFor={`role${index}`}
                    style={{
                      textAlign: "start",
                      paddingRight: "13px",
                      paddingLeft: "13px",
                    }}
                    className="cursor-pointer"
                  >
                    <div className="h-joinnow-parent">
                      I am a{" "}
                      {role === c.lookingFor.BUSINESS_CLIENT
                        ? "Business"
                        : role === c.lookingFor.INDIVIDUAL_CLIENT
                          ? "Parent"
                          : "Helper looking for work"}
                    </div>
                    {role === c.lookingFor.INDIVIDUAL_PROVIDER ? (
                      <small className="h-joinnow-parent-span">
                        Childcare, Odd jobs, Tutoring
                      </small>
                    ) : (
                      <small className="h-joinnow-parent-span">
                        Looking for Helpers on Juggle Street
                      </small>
                    )}
                  </label>
                </div>
              );
            })}
          <div className="text-danger text-red-500 font-bold">
            {state.errors.clientType && (
              <small>{state.errors.clientType}</small>
            )}
          </div>
          {uiState.showAddressInput && (
            <div className="p-mt-2 mb-3 w-full">
              <p
                className="h-joinnow-quetion"
                style={{
                  height: "24px",
                  margin: 0,
                  width: "min-content",
                  textWrap: "nowrap",
                  position: "relative",
                }}
              >
                Where do you live?
              </p>
              <AutoComplete
                className={`autocomplete-width autocomplete-style ${state.errors.address ? "border-error" : ""
                  }`}
                id="address"
                name="address"
                value={state.address.addressLine1}
                suggestions={autoCompleteSuggestions}
                completeMethod={searchAddress}
                onChange={handleAddressChange}
                placeholder="Enter your suburb or postcode"
                style={{
                  border: state.errors.address
                    ? "1px solid red"
                    : "1px solid #DFDFDF",
                  borderRadius: "10px",
                }}
              />
            </div>
          )}
          <div className="text-danger text-red-500 text-center font-bold">
            {state.errors.address && <small>{state.errors.address}</small>}
          </div>
          {uiState.showClientCategory && (
            <div className="p-mt-2 ">
              <div className="p-d-flex p-ai-start">
                <p className="h-joinnow-quetion">Account Type</p>
              </div>
              {Object.keys(c.clientCategory)
                .filter(
                  (key) =>
                    key === "BUSINESS_SINGLE_SITE" ||
                    key === "BUSINESS_MULTI_SITE"
                )
                .map((key, index) => {
                  const hasError = !!state.errors.clientCategory;
                  const borderClass = hasError
                    ? "border-red  "
                    : getBorderBusnessClass(c.clientCategory[key]);
                  return (
                    <div
                      key={index}
                      className={`field-radiobutton ${borderClass}`}
                      style={{ height: "auto" }}
                    >
                      <input
                        type="radio"
                        id={`clientCategory${index}`}
                        value={c.clientCategory[key]}
                        checked={state.clientCategory === c.clientCategory[key]}
                        onChange={() =>
                          handleRadioChange(
                            "clientCategory",
                            c.clientCategory[key]
                          )
                        }
                        className="cursor-pointer"
                      />
                      <label
                        htmlFor={`clientCategory${index}`}
                        style={{
                          textAlign: "start",
                          paddingRight: "13px",
                          paddingLeft: "13px",
                        }}
                        className="cursor-pointer"
                      >
                        <div className="h-joinnow-parent mb-1">
                          {key === "BUSINESS_SINGLE_SITE" && "Juggle St Pro"}
                          {key === "BUSINESS_MULTI_SITE" &&
                            "Juggle St Pro - Childcare"}
                        </div>
                        {key === "BUSINESS_SINGLE_SITE" && (
                          <small className="h-joinnow-parent-span">
                            On-demand recruitment of casual childcare staff for
                            non-childcare businesses
                          </small>
                        )}
                        {key === "BUSINESS_MULTI_SITE" && (
                          <small className="h-joinnow-parent-span">
                            On-demand recruitment of childcare educators for
                            Childcare Centres and Childcare recruitment agencies
                          </small>
                        )}
                      </label>
                    </div>
                  );
                })}
            </div>
          )}
          <div className="text-danger text-red-500 text-center font-bold">
            {state.errors.clientCategory && (
              <small>{state.errors.clientCategory}</small>
            )}
          </div>
          {uiState.showBusinessLocationInput && (
            <div className="p-mt-2">
              <p
                className="h-joinnow-quetion"
                style={{
                  height: "24px",
                  width: "min-content",
                  textWrap: "nowrap",
                  position: "relative",
                }}
              >
                Where is your business located?
              </p>
              <AutoComplete
                className="autocomplete-width autocomplete-style"
                id="address"
                value={state.address.addressLine1}
                suggestions={autoCompleteSuggestions}
                completeMethod={searchAddress}
                onChange={handleAddressChange}
                placeholder="Enter your suburb or postcode"
                style={{
                  border: state.errors.businessLocation
                    ? "1px solid red"
                    : "1px solid #DFDFDF",
                  borderRadius: "10px",
                }}
              />
              {state.errors.address && (
                <div className="text-danger text-red-500 text-center font-bold">
                  <small>{state.errors.address}</small>
                </div>
              )}
            </div>
          )}
          <div className="text-danger text-red-500 text-center font-bold ">
            {!state.errors.clientCategory && state.errors.businessLocation && (
              <small>{state.errors.businessLocation}</small>
            )}
          </div>
        </div>
        <div style={{ flexGrow: 1, flexBasis: 0 }}></div>
        <div style={{ position: "relative" }}>
          <button
            className={`flex align-items-center justify-content-center h-joinnow-button ${isFormValid ? "shadow-4" : ""
              }`}
            style={{
              backgroundColor: isFormValid ? "#FFA500" : "#DFDFDF",
              borderColor: isFormValid ? "#FFA500" : "#DFDFDF",
              color: isFormValid ? "#FFFFFF" : "#c2c7d1",
              cursor: isFormValid ? "pointer" : "not-allowed",
              marginTop: "50px",
            }}
            onClick={handleNext}
            disabled={!isFormValid}
          >
            Next
            <i
              className="pi pi-angle-right ml-2 text-center"
              style={{ fontSize: "large" }}
            ></i>
          </button>
        </div>
      </form>
    </div>
  );
};

export default Joinnow;
