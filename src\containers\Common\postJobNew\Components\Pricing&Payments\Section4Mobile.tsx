import { useState } from "react";
import { useJobManager } from "../../provider/JobManagerProvider"; // Adjust path as needed
import styles from "../../../styles/job-shifts.module.css";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

const Section4Mobile = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const [specialInstruction, setSpecialInstruction] = useState(payload.specialInstructions ?? "");

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
      }}
    >
      <div className={styles.weeklySummaryMobile}>
        <div className="h-full w-full flex-grow-1 flex flex-column">
          <div className="flex-grow-1 flex flex-column">
            <div className="flex flex-column px-5 py-3">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                Job Details
              </h1>
              <p
                className="m-0 p-0 my-2"
                style={{
                  fontWeight: "400",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Describe the job
              </p>
              <textarea
                className={styles.textAreaInputMobile}
                placeholder={`What does your ${
                  payload.jobType === 64 || payload.jobType === 128 ? "tutor" : ""
                } need to know about this job and working with your family?`}
                name="instruction"
                id="instruction"
                value={specialInstruction}
                style={{
                  border: specialInstruction.length > 0 ? "2px solid #179D52" : "1px solid #585858",
                }}
                onChange={(e) => {
                  setSpecialInstruction(e.target.value);
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <BackButtonPortal id="back-button-portal">
        <div
          onClick={(e) => {
            e.preventDefault();
            setpayload({
              ...payload,
              specialInstructions: specialInstruction,
            });
            prev("pricing-payments-step2"); // Navigate back to PricingPaymentsStep2
          }}
        >
          <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
        </div>
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        isDisabled={specialInstruction.length < 1}
        onClick={() => {
          setpayload({
            ...payload,
            specialInstructions: specialInstruction,
          });
          if (payload.managedBy === 20) {
            next("candidate-matching");
          } else {
            next("candidate-selection");
          }
        }}
      />
    </div>
  );
};

export default Section4Mobile;
