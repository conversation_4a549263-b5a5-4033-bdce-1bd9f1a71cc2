import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { CardCvcElement, CardExpiryElement, CardNumberElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { IoClose } from 'react-icons/io5';
import styles from '../../styles/add-card-dialog.module.css';

// Local CustomInput component
function CustomInput<T>({
  label,
  onChange,
  showAstrict = false,
  value = "",
}: {
  label: string;
  onChange: (value: T) => void;
  showAstrict?: boolean;
  value?: string;
}) {
  const [inputValue, setInputValue] = useState<string>(value);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue as T);
  };

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div className={styles.customInputContainer}>
      <label className={styles.label}>
        {label}
        {showAstrict && <span className={styles.asterisk}> *</span>}
      </label>
      <input
        type="text"
        value={inputValue}
        onChange={handleChange}
        className={styles.customInput}
        placeholder={`Enter ${label.toLowerCase()}`}
      />
    </div>
  );
}

interface AddCardDialogProps {
  visible: boolean;
  onHide: () => void;
  onSuccess: (cardData: any) => void;
}

const AddCardDialog: React.FC<AddCardDialogProps> = ({
  visible,
  onHide,
  onSuccess,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [cardNumberDetails, setCardNumberDetails] = useState(false);
  const [cardExpiryDetails, setCardExpiryDetails] = useState(false);
  const [cardCvcDetails, setCardCvcDetails] = useState(false);
  const [nameOnCard, setNameOnCard] = useState("");
  const [valid, setValid] = useState(false);
  const [loading, setLoading] = useState(false);
  const stripe = useStripe();
  const elements = useElements();

  const handleNameChange = (value: string) => {
    setNameOnCard(value);
  };

  useEffect(() => {
    setValid(
      cardNumberDetails &&
      cardExpiryDetails &&
      cardCvcDetails &&
      nameOnCard.trim() !== ""
    );
  }, [cardNumberDetails, cardExpiryDetails, cardCvcDetails, nameOnCard]);

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    if (!stripe || !elements) {
      setError("Stripe has not loaded");
      setLoading(false);
      return;
    }

    const cardElement = elements.getElement(CardNumberElement);
    if (!cardElement) {
      setError("Card element not found");
      setLoading(false);
      return;
    }

    try {
      const { token, error } = await stripe.createToken(cardElement, {
        name: nameOnCard,
      });

      if (error) {
        setError(error.message || "An error occurred");
      } else if (token) {
        // Create a mock card data object that matches the expected format
        const newCardData = {
          id: `card-${Date.now()}`,
          name: token.card?.brand?.toUpperCase() || 'CARD',
          icon: getCardIcon(token.card?.brand || 'unknown'),
          owner: nameOnCard,
          number: token.card?.last4 || '0000',
          fee: token.card?.brand === 'visa' ? 1.75 : 0,
          token: token.id
        };
        
        onSuccess(newCardData);
        handleClose();
      }
    } catch (err) {
      setError("Failed to add card. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const getCardIcon = (brand: string) => {
    // Return appropriate icon path based on card brand
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'path/to/visa-icon.png';
      case 'mastercard':
        return 'path/to/mastercard-icon.png';
      case 'amex':
        return 'path/to/amex-icon.png';
      default:
        return 'path/to/default-card-icon.png';
    }
  };

  const handleClose = () => {
    setNameOnCard("");
    setCardNumberDetails(false);
    setCardExpiryDetails(false);
    setCardCvcDetails(false);
    setError(null);
    setValid(false);
    onHide();
  };

  return (
    <Dialog
      visible={visible}
      onHide={handleClose}
      modal
      className={styles.addCardDialog}
      showHeader={false}
      draggable={false}
      resizable={false}
    >
      <div className={styles.dialogContent}>
        <div className={styles.header}>
          <h3 className={styles.title}>Add New Card</h3>
          <IoClose onClick={handleClose} className={styles.closeBtn} />
        </div>

        <div className={styles.formContainer}>
          <div className={styles.inputGroup}>
            <CustomInput<string>
              label="Name on Card"
              onChange={handleNameChange}
              showAstrict
              value={nameOnCard}
            />
          </div>

          <div className={styles.inputGroup}>
            <label className={styles.label}>Card Number</label>
            <div className={styles.stripeInput}>
              <CardNumberElement
                options={{
                  disableLink: true,
                  showIcon: true,
                  style: {
                    base: {
                      fontSize: '16px',
                      fontWeight: '400',
                      color: '#333',
                      '::placeholder': {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#999',
                      },
                    },
                    invalid: {
                      fontSize: '16px',
                      fontWeight: '400',
                      color: '#ff5252',
                    },
                    complete: {
                      fontSize: '16px',
                      fontWeight: '400',
                      color: '#333',
                    },
                  },
                }}
                onChange={(event) => {
                  setCardNumberDetails(event.complete);
                  if (event.error) {
                    setError(event.error.message);
                  } else {
                    setError(null);
                  }
                }}
              />
            </div>
          </div>

          <div className={styles.rowInputs}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Expiry Date</label>
              <div className={styles.stripeInput}>
                <CardExpiryElement
                  options={{
                    style: {
                      base: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#333',
                        '::placeholder': {
                          fontSize: '16px',
                          fontWeight: '400',
                          color: '#999',
                        },
                      },
                      invalid: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#ff5252',
                      },
                      complete: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#333',
                      },
                    },
                  }}
                  onChange={(event) => {
                    setCardExpiryDetails(event.complete);
                    if (event.error) {
                      setError(event.error.message);
                    } else {
                      setError(null);
                    }
                  }}
                />
              </div>
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>CVC</label>
              <div className={styles.stripeInput}>
                <CardCvcElement
                  options={{
                    style: {
                      base: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#333',
                        '::placeholder': {
                          fontSize: '16px',
                          fontWeight: '400',
                          color: '#999',
                        },
                      },
                      invalid: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#ff5252',
                      },
                      complete: {
                        fontSize: '16px',
                        fontWeight: '400',
                        color: '#333',
                      },
                    },
                  }}
                  onChange={(event) => {
                    setCardCvcDetails(event.complete);
                    if (event.error) {
                      setError(event.error.message);
                    } else {
                      setError(null);
                    }
                  }}
                />
              </div>
            </div>
          </div>

          {error && (
            <div className={styles.errorMessage}>
              {error}
            </div>
          )}

          <div className={styles.buttonContainer}>
            <Button
              label={loading ? "Adding Card..." : "Add Card"}
              className={styles.addButton}
              onClick={handleSubmit}
              disabled={!valid || loading}
            />
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default AddCardDialog;
