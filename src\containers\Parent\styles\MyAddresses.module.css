.addressContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  padding: 20px;
}

.addressHeader {
  width: 100%;
  font-size: 24px;
  font-weight: 600;
  margin-top: 0px;
  color: #585858;
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.addressSection1 {
  padding-block: 15px;
  border-radius: 8px;
  width: 100%;
  padding-bottom: 0px;
}
.addressItem {
  border-radius: 8px;
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.defaultAddress {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
  margin-bottom: 0px;
}

.defaultHeader {
  font-size: 16px;
  font-weight: 600;
  color: #179d52;
  line-height: 24px;
  margin-top: 0px;
}

.addressDivider {
  width: 100%;
  margin-top: 8px;
  color: #f1f1f1;
}
.defaultAddressContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.addressIcon {
  width: 50px;
  height: 50px;
  background-color: #37a950;
  border-radius: 50%;
  padding: 14px;
  object-fit: contain;
  overflow: visible;
}

.editBtn {
  width: 135px;
  min-height: 34px;
  font-size: 12px;
  display: flex;
  margin-left: auto;
  height: 34px;
  font-weight: 800;
  line-height: 18px;
  color: #ffffff;
  border-radius: 10px;
  align-items: center;
  background-color: #ffa500;
  border: none;
  padding: 8px 14px 8px 10px;
  gap: 8px;
  cursor: pointer;
}
.AddBtn {
  width: 100%;
  max-width: 178px;
  height: 39px;
  font-size: 12px;
  display: flex;
  height: 34px;
  font-weight: 700;
  line-height: 18px;
  color: #ffffff;
  border-radius: 8px;
  align-items: center;
  background-color: #ffa500;
  border: none;

  gap: 8px;
  padding: 13px;
  justify-content: center;
  cursor: pointer;
}

.addAddressContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 25px;
  box-sizing: border-box;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  flex-direction: column;
}

.addAddressDiv {
  flex: 1;
  min-width: 280px;
}
.addAddressSecond {
  flex: 1;
  max-width: min-content;
  display: flex;
  flex-direction: row;
  gap: 18px;
}

.addAddressHeader {
  color: #585858;
  font-size: 32px;
  font-weight: 700;
  padding-bottom: 10px;
  text-align: left;
}

.addaddressDivider {
  width: 100%;
  margin: 8px 0;
  border-top: 1px solid #dee2e6;
}
.dropdownContainer {
  width: 118px;
  margin-top: 18px;
}
.dropdownContainer > div {
  border-radius: 10px !important;
  border: none !important;
  width: 118px !important;
  background-color: rgba(240, 244, 247, 1);
}
.dropdownContainerCountry {
  width: 118px;
  margin-top: 18px;
}
.dropdownContainerCountry > div {
  border-radius: 10px !important;
  border: none !important;
  background-color: #f0f4f7 !important;
  width: 259px !important;max-width: unset;
}

.addressHead {
  font-size: 16px;
  font-weight: 700;
  color: #585858;
  margin-bottom: 8px;
}

.labelStatic {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #585858;
  line-height: 24px;
}

.dialogFooter {
  padding: 16px;
  text-align: right;
}
.addressfetch {
  border-radius: 8px;
  width: 100%;
  /* padding-bottom: 0px; */
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
}
.setAddressIcon {
  width: 50px;
  height: 50px;
  background-color: transparent;
  border-radius: 50%;
  padding: 14px;
  object-fit: contain;
  overflow: visible;
  color: #585858;
  border: 1px solid #585858;
}
.successMessageAddress {
  text-wrap: nowrap;
  width: 241px;
  height: 38px;
  background-color: #37a950;
  padding: 10px;
  border-radius: 20px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 800;
  line-height: 18px;
  margin-right: 32px;
  display: flex;
  gap: 12px;
}
.defaultBtn {
  background-color: transparent;
  color: #585858;
  border: none;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  padding: 0px;
  cursor: pointer;
}
.addressSectionUpdate {
  padding-block: 10px;
}
.addressDividerUpdate {
  width: 100%;
  margin-top: 20px;
}
.removeBtn {
  width: 74px;
  height: 34px;
  background-color: #ffd0cd;
  border-radius: 10px;
  border: 1px solid #ff6359;
  color: #ff6359;
  cursor: pointer;
}
.typeHereContainer {
  width: 100%;
  height: 200px;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  font-size: 24px;
  margin-top: 20px;
}
.saveAddress {
  font-size: 14px;
  background-color: #ffa500;
  color: white;
  padding: 10px;
  border: none;
  border-radius: 8px;
  width: 156px;
  height: 39px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: 700;
}
.saveAddress:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
}

.addressDividerSecond {
  width: 100%;
  margin-top: 25px;
  border-top: 1px solid #dee2e6;
}
.nicknameCont {
  width: 100%;
  margin-top: 18px;
}
.switchToBtn {
  background-color: transparent;
  border: none;
  color: #585858;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  text-decoration: none;
  margin-top: 10px;
}

.switchToBtn:hover {
  text-decoration: underline;
  text-underline-offset: 1px;
  text-decoration-thickness: 1px;
  text-decoration-color: #585858;
}
.suggestionsList {
  list-style: none;
  padding: 0;
  margin: 10px 0;
  border: 1px solid #ddd;
  max-height: 150px;
  overflow-y: auto;
}

.suggestionItem {
  padding: 10px;
  cursor: pointer;
}

.suggestionItem:hover {
  background-color: #f0f0f0;
}
.nzAddressDropdown {
  width: 100%;
}
.nzAddressDropdown .p-autocomplete-input {
  width: 100%;
}
.nzAddressContainer {
  width: 100%;
}
.autoComplete {
  width: 259px !important;
}
