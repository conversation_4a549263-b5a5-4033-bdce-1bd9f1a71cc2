import React, { useState, useEffect, useRef, KeyboardEvent } from "react";
import styles from "../../Parent/styles/AboutMe.module.css";
import { Divider } from "primereact/divider";
import { InputText } from "primereact/inputtext";
import "../../../components/utils/util.css";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import CustomButton from "../../../commonComponents/CustomButton";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import { FaCheck } from "react-icons/fa6";
import CustomDialog from "../../Common/CustomDialog";
import Auth from "../../../services/authService";
import {
  validateEmail,
  validatePhoneNumber,
} from "../../../components/utils/validation";
import useLoader from "../../../hooks/LoaderHook";
import useIsMobile from "../../../hooks/useIsMobile";

interface InputOtpProps {
  length?: number;
  onComplete?: (otp: string | null, disable: boolean) => void;
}
const InputOtp: React.FC<InputOtpProps> = ({ length = 6, onComplete }) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (index: number, value: string) => {
    onComplete(null, true);
    if (isNaN(Number(value))) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value !== "" && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    if (newOtp.every((digit) => digit !== "") && onComplete) {
      onComplete(newOtp.join(""), false);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && otp[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div className="flex align-items-center gap-2">
      {otp.map((digit, index) => (
        <InputText
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          value={digit}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          className={styles.otpinput}
          maxLength={1}
        />
      ))}
    </div>
  );
};

const genders = {
  MALE: "Male",
  FEMALE: "Female",
  PREFER_NOT_TO_SAY: "Prefer not to say", // New gender type
};

const AboutMe: React.FC = () => {
  const [accountIdentifier, setAccountIdentifier] = useState("");
  const [accountIdentifierError, setAccountIdentifierError] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [firstNameError, setFirstNameError] = useState("");
  const [lastNameError, setLastNameError] = useState("");
  const [gender, setGender] = useState("");
  const [genderError, setGenderError] = useState("");
  const [isSaveEnabled, setIsSaveEnabled] = useState(false);
  const [initialGender, setInitialGender] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessageotp, setErrorMessageotp] = useState("");
  const [successMessageotp, setSuccessMessageotp] = useState("");
  const [successMessageEmail, setSuccessMessageEmail] = useState("");
  const [dialogVisible, setDialogVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isFocused, setIsFocused] = useState(false);
  const [_, setErrorMessage] = useState("");
  const [disable, setDisable] = useState(true);
  const [otpValue, setOtpValue] = useState("");
  const [resendDisabled, setResendDisabled] = useState(true);
  const [accountEmail, setAccountEmail] = useState("");
  const [accountEmailError, setAccountEmailError] = useState(null);
  const [originalEmail, setOriginalEmail] = useState("");

  const [isOtpSubmitted, setIsOtpSubmitted] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();
  const {isMobile}=useIsMobile();
  useEffect(() => {
    if (sessionInfo) {
      setFirstName(sessionInfo.loading ? "" : sessionInfo.data["firstName"]);
      setLastName(sessionInfo.loading ? "" : sessionInfo.data["lastName"]);
      setAccountIdentifier(
        sessionInfo.loading ? "" : sessionInfo.data["phoneNumber"]
      );
      setAccountEmail(sessionInfo.loading ? "" : sessionInfo.data["email"]);

      if (!sessionInfo.loading) {
        if (sessionInfo.data["gender"] === 1) {
          setGender(genders["MALE"]);
          setInitialGender(genders["MALE"]);
        } else if (sessionInfo.data["gender"] === 2) {
          setGender(genders["FEMALE"]);
          setInitialGender(genders["FEMALE"]);
        } else if (sessionInfo.data["gender"] === 0) {
          setGender(genders["PREFER_NOT_TO_SAY"]);
          setInitialGender(genders["PREFER_NOT_TO_SAY"]);
        }
      }
    
    }
  }, [sessionInfo]);
  useEffect(() => {
    if (dialogVisible === false) {
      setIsOtpSubmitted(false);
      setDisable(true);
    }
  }, [dialogVisible]);
  useEffect(() => {
    setIsSaveEnabled(gender !== "" && gender !== initialGender);
  }, [gender, initialGender]);

  const handleGenderChange = (selectedGender: string) => {
    setGender(selectedGender);
  };
  const handleDialogOpen = () => {
    setCurrentStep(1);
    setDialogVisible(true);
  };

  const handleDialogClose = () => {
    setAccountIdentifier(sessionInfo.data["phoneNumber"]);
    setDialogVisible(false);
    setAccountIdentifierError("");
  };

  const handleSubmit = () => {
    const payload = {
      ...(sessionInfo.data as object),
      phoneNumber: otpValue,
    };
    if (otpValue.length === 6) {
      enableLoader();
      Auth.submitVerificationCode(
        otpValue,

        () => {
          enableLoader();
          dispatch(updateUser({ payload })).then(() => {
            setSuccessMessageEmail(
              `Your phone number ${accountIdentifier} has been successfully verified and updated`
            );
            disableLoader();
            setAccountIdentifier(accountIdentifier);
          });
          setIsOtpSubmitted(false);
          setDisable(true);
          setDialogVisible(false);
          setCurrentStep(1);
        },
        () => {
          disableLoader();
          setErrorMessageotp("Invalid verification code. Please try again.");
        }
      );
    } else {
      setErrorMessageotp("Please enter a valid 6-digit OTP.");
    }
  };
  const handleOtpComplete = (param, state) => {
    setDisable(state);
    setOtpValue(param);
  };
  const handleNextStep = () => {
    const value = accountIdentifier.trim(); // Trim the input to avoid empty spaces

    // Check if the mobile number is empty
    if (!value) {
      setAccountIdentifierError("Please enter your mobile number.");
      return;
    }

    // Check if the mobile number is valid
    if (validatePhoneNumber(value)) {
      enableLoader();
      Auth.getVerificationCode(
        value,
        () => {
          disableLoader();
          setCurrentStep(2);
        },
        () => {
          disableLoader();
          setAccountIdentifierError("Failed to send OTP. Please try again.");
        }
      );
      setAccountIdentifierError(""); // Clear error on valid phone number
    } else {
      setAccountIdentifierError("Enter a valid mobile number.");
    }
  };

  const handleResendCode = () => {
    handleNextStep();
  };

  useEffect(() => {
    if (sessionInfo.data && sessionInfo.data["email"]) {
      setAccountEmail(sessionInfo.data["email"]);
      setOriginalEmail(sessionInfo.data["email"]);
    }
  }, [sessionInfo]);

  const handleUpdateEmail = () => {
    const error = validateEmail(accountEmail);
    if (error) {
      setAccountEmailError(error);
      return;
    }

    const payload = {
      ...(sessionInfo.data as object),
      email: accountEmail,
    };

    enableLoader();
    dispatch(updateUser({ payload }))
      .then(() => {
        setSuccessMessageEmail(
          `Your email address ${accountEmail} has been successfully verified and updated`
        );

        setOriginalEmail(accountEmail);
      })
      .finally(() => {
        disableLoader();
      });
  };

  const handleEmailChange = (e) => {
    setAccountEmail(e.target.value);
    setAccountEmailError("");
  };

  const isButtonDisabled = accountEmail === originalEmail;
  return (
    <div  style={{paddingTop:isMobile && "0px", paddingLeft:isMobile && "10px" }} className={styles.aboutMeContainer}>
      <h2 style={{marginBottom:isMobile && "5px"}} className={styles.aboutMeHeader}>Account Holder</h2>
      <Divider style={{ width: "744px" }} />
      <br />
      <div className={styles.contentContainer}>
        <div  style={{paddingTop:isMobile && "0px" , marginBottom:isMobile && "0px"}} className={styles.aboutMeSection}>
          {/* First Name Input */}
          <div
            className="input-container"
            style={{ marginTop: "8px", maxWidth: "100%" }}
          >
            <InputText
              readOnly
              id="firstName"
              name="firstName"
              value={firstName}
              onChange={(e) => {
                const value = e.target.value;
                if (/^[A-Za-z\s]*$/.test(value)) {
                  setFirstName(value);
                }
              }}
              placeholder=""
              className={`input-placeholder ${
                firstNameError ? "firstNameError" : ""
              }`}
            />
            <label
              htmlFor="firstName*"
              className={`label-name ${
                firstName || firstNameError ? "label-float" : ""
              } ${firstNameError ? "input-error" : ""}`}
            >
              {firstNameError && !firstName ? firstNameError : "First Name*"}
            </label>
          </div>

          {/* Last Name Input */}
          <div
            className="input-container"
            style={{ marginTop: "25px", maxWidth: "100%" }}
          >
            <InputText
              readOnly
              id="lastName"
              name="lastName"
              value={lastName}
              onChange={(e) => {
                const value = e.target.value;
                if (/^[A-Za-z\s]*$/.test(value)) {
                  setLastName(value);
                }
              }}
              placeholder=""
              className={`input-placeholder ${
                lastNameError ? "lastNameError" : ""
              }`}
            />
            <label
              htmlFor="lastName*"
              className={`label-name ${
                lastName || lastNameError ? "label-float" : ""
              } ${lastNameError ? "input-error" : ""}`}
            >
              {lastNameError && !lastName ? lastNameError : "Last Name*"}
            </label>
          </div>

          {/* Mobile Number Input */}
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div
              className="input-container"
              style={{ marginTop: "25px", maxWidth: "100%" }}
            >
              <InputText
                readOnly
                id="mobile"
                name="mobile"
                value={accountIdentifier}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value) && value.length <= 15) {
                    setAccountIdentifier(value);
                  }
                }}
                onFocus={() => {
                  // setIsFocused(true);
                  // setAccountIdentifierError("");
                  setSuccessMessageotp(""); // Clear success message on focus
                }}
                placeholder=""
                className={`input-placeholder ${
                  accountIdentifierError ? "accountIdentifierError" : ""
                }`}
              />
              <label
                htmlFor="mobile"
                className={`label-name ${
                  accountIdentifier || accountIdentifierError
                    ? "label-float"
                    : ""
                } ${accountIdentifierError ? "input-error" : ""}`}
              >
                {accountIdentifierError && !accountIdentifier
                  ? accountIdentifierError
                  : "Mobile"}
              </label>
            </div>
            <CustomButton
              label="Change Number"
              className={styles.changeNumber}
              onClick={handleDialogOpen}
            />

            <CustomDialog
              visible={dialogVisible}
              onHide={handleDialogClose}
              closeClicked={handleDialogClose}
              profileCompletion={0}
            >
              <main className={styles.numberdialogContent}>
                {currentStep === 1 && (
                  <>
                    <div className={styles.numberdialogLeft}>
                      <h2 className={styles.headernumber}>
                        Update Your Mobile Number
                      </h2>
                      <p
                        className={styles.dialogDescription}
                        style={{
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        At Juggle Street, trust and security are paramount,
                        that’s why we need to verify each person. Your mobile
                        number is used to verify your identity. 
                      </p>
                    </div>
                    <div className={styles.numberdialogRight}>
                      <p className={styles.steptext}>Part {currentStep} of 2</p>
                      <div
                        className={styles.inputGroup}
                        style={{ marginTop: "35px", maxWidth: "100%" }}
                      >
                        <div
                          className="input-container"
                          style={{ maxWidth: "100%" }}
                        >
                          <InputText
                            id="username"
                            name="username"
                            value={accountIdentifier}
                            onFocus={() => {
                              setIsFocused(true);
                              setAccountIdentifierError("");
                              setSuccessMessageotp(""); // Clear success message on focus
                            }}
                            onBlur={() => setIsFocused(false)}
                            onChange={(e) => {
                              const value = e.target.value;

                              if (
                                /^[\d+]*$/.test(value) &&
                                value.length <= 15
                              ) {
                                setAccountIdentifier(value);
                              }
                            }}
                            placeholder=""
                            className={`input-placeholder ${
                              accountIdentifierError
                                ? "accountIdentifierError"
                                : ""
                            }`}
                          />

                          <label
                            htmlFor="username"
                            className={`label-name ${
                              accountIdentifier || accountIdentifierError
                                ? "label-float"
                                : ""
                            } ${accountIdentifierError ? "input-error" : ""}`}
                          >
                            {accountIdentifierError && !accountIdentifier
                              ? accountIdentifierError
                              : "Mobile"}
                          </label>
                        </div>
                      </div>
                      <CustomButton
                        label="Get Code"
                        className="hover:shadow-5"
                        onClick={handleNextStep}
                        style={{
                          width: "156px",
                          height: "39px",
                          backgroundColor: "#179D52",
                        }}
                      />

                      <p className={styles.verificationMessage}>
                        Click <b>'Get Code'</b> to receive your 6-digit
                        verification number.
                      </p>
                      {accountIdentifierError &&
                        accountIdentifier &&
                        !isFocused && (
                          <div className="error-message">
                            {accountIdentifierError}
                          </div>
                        )}
                    </div>
                  </>
                )}

                {currentStep === 2 && (
                  <>
                    <div className={styles.numberdialogLeft}>
                      <p className={styles.headernumber}>
                        Update Your Mobile Number
                      </p>
                      <p
                        className={styles.dialogDescription}
                        style={{
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        To complete your identity verification, please enter the
                        six-digit code that was sent by SMS to your mobile
                        number.
                      </p>
                    </div>
                    <div className={styles.numberdialogRight}>
                      <p className={styles.steptext}>Part {currentStep} of 2</p>
                      <InputOtp onComplete={handleOtpComplete} />
                      <div className={styles.codebuttonContainer}>
                        <CustomButton
                          label="Submit"
                          disabled={disable}
                          onClick={handleSubmit}
                          style={{
                            width: "156px",
                            height: "39px",
                            font: "14px",
                            fontWeight: "800",
                          }}
                          className={`${!disable ? "shadow-4" : ""}`}
                        />

                        {!isOtpSubmitted && (
                          <p
                            onClick={handleResendCode}
                            className={styles.resendLink}
                          >
                            Resend Code
                          </p>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </main>
            </CustomDialog>
          </div>
          {successMessageotp && (
            <div className={styles.successMobile}>
              <div
                style={{
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  color: "#179D52",
                  padding: "5px",
                  height: "16px",
                  width: "16px",
                  display: "inline-flex",
                  alignItems: "center",
                  marginRight: "3px",
                }}
              >
                <FaCheck />
              </div>
              {successMessageotp}
            </div>
          )}
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div
              className="input-container"
              style={{ marginTop: "25px", maxWidth: "100%" }}
            >
              <InputText
                id="email"
                name="email"
                value={accountEmail}
                onChange={(e) => setAccountEmail(e.target.value)}
                placeholder=""
                className={`input-placeholder ${
                  accountEmailError ? "emailInputError" : ""
                }`}
                style={{
                  borderColor: accountEmailError ? "red" : "",
                  borderWidth: "2px",
                }}
                onFocus={() => {
                  setAccountEmailError("");
                  setSuccessMessageEmail("");
                }}
              />
              <label
                htmlFor="username"
                className={`label-name ${
                  accountEmail || accountEmailError ? "label-float" : ""
                } ${accountEmailError ? "input-error" : ""}`}
              >
                {accountEmailError && !accountEmail
                  ? accountEmailError
                  : "Email"}
              </label>
            </div>

            <CustomButton
              label="Update Email"
              className={styles.changeEmail}
              onClick={handleUpdateEmail}
              disabled={isButtonDisabled}
            />
          </div>
          {successMessageEmail && (
            <div className={styles.successMobile}>
              <div
                style={{
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  color: "#179D52",
                  padding: "5px",
                  height: "16px",
                  width: "16px",
                  display: "inline-flex",
                  alignItems: "center",
                  marginRight: "3px",
                }}
              >
                <FaCheck />
              </div>
              {successMessageEmail}
            </div>
          )}
        </div>

        {/* Gender Section */}
        <div className={styles.aboutMeSectionsecond}>
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <h2 className={styles.aboutMegender}>Gender</h2>
            {successMessage && (
              <div className={styles.successMessagegender}>
                <div
                  style={{
                    backgroundColor: "#FFFFFF",
                    borderRadius: "50%",
                    color: "#179D52",
                    padding: "5px",
                    height: "16px",
                    width: "16px",
                    display: "inline-flex",
                    alignItems: "center",
                    marginRight: "3px",
                  }}
                >
                  <FaCheck />
                </div>
                {successMessage}
              </div>
            )}
          </div>
          <Divider className={styles.genderdivider} />

          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div className="flex flex-wrap gap-3 mb-2 mt-3 cursor-pointer">
              {Object.keys(genders).map((key, index) => (
                <label
                  key={index}
                  htmlFor={`gender${index}`}
                  className="align-items-center radiobutton-style cursor-pointer"
                  style={{
                    border: genderError
                      ? "1px solid red"
                      : gender === genders[key]
                      ? "1px solid #179D52"
                      : "1px solid gainsboro",
                    padding: "11px 16px 11px 11px",
                    borderRadius: "10px",
                    position: "relative",
                    cursor: "pointer", // Make it appear clickable
                    backgroundColor:
                      gender === genders[key] ? "" : "transparent",
                  }}
                  onClick={() => handleGenderChange(genders[key])} // Handle click event on the entire label
                >
                  <label
                    htmlFor={`gender${index}`}
                    className="ml-1 h-joinnow2-gender pr-5 cursor-pointer"
                    style={{
                      color: gender === genders[key] ? "#179D52" : "",
                      fontWeight: gender === genders[key] ? 700 : 500,
                    }}
                  >
                    {String(key)
                      .replace(/_/g, " ") // Replace underscores with spaces
                      .split(" ") // Split into words
                      .map(
                        (word) =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase()
                      ) // Capitalize each word
                      .join(" ")}
                  </label>
                  <div
                    style={{
                      position: "absolute",
                      top: 6.5,
                      right: 9,
                    }}
                  >
                    <input
                      type="radio"
                      id={`gender${index}`}
                      value={genders[key]}
                      checked={gender === genders[key]}
                      onChange={() => handleGenderChange(genders[key])}
                      style={{
                        cursor: "pointer",
                        opacity: 0,
                        position: "absolute",
                      }} // Hide default radio button
                    />
                    {/* Show the round shape when not selected */}
                    {gender === genders[key] ? (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          backgroundColor: "#179D52",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <span
                          style={{
                            color: "white",
                            fontWeight: "bold",
                            fontSize: "12px",
                            lineHeight: "12px",
                          }}
                        >
                          ✓
                        </span>
                      </div>
                    ) : (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          border: "1px solid gainsboro",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: "transparent",
                        }}
                      />
                    )}
                  </div>
                </label>
              ))}
            </div>

            <CustomButton
              label="Save"
              onClick={() => {
                let temp = -1;
                if (gender === "Male") {
                  temp = 1;
                } else if (gender === "Female") {
                  temp = 2;
                }else if (gender === "Prefer not to say") {
                  temp = 0; // New gender value
                }
                if (temp === -1) {
                  return;
                }
                const payload = {
                  ...(sessionInfo.data as object),
                  gender: temp,
                };
                enableLoader();
                dispatch(updateUser({ payload: payload }))
                  .then(() => {
                    setSuccessMessage("Changes successfully made");
                  })
                  .finally(() => {
                    disableLoader();
                  });
              }}
              className={styles.changeNumber}
              disabled={!isSaveEnabled}
            />
          </div>
          <p className={styles.instruct}>
            *This field is not editable. Contact Juggle Street to update.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AboutMe;
