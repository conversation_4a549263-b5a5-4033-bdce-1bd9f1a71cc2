.EventDialog {
  padding: 1rem;
}
.dialogContent {
  background-color: #fff;
}

.EventHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.EventTitle {
  font-weight: 600;
  color: #179D52;
  margin: 0px;
  font-size: 30px;
  font-weight: 700;
}

.EventCloseButton {
  display: flex;
  justify-content: end;
  position: absolute;
  width: 30px;
  height: 32px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  top: -8px;
  cursor: pointer;
  right: 4px;
}

.EventContent {
  padding: 30px;
}

.EventDescription {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
}

.EventGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.EventList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: #585858;
  font-size: 16px;
  font-weight: 500;
}

.EventQuestion {
  margin-bottom: 15px;
  color: #585858;
  font-size: 16px;
  font-weight: 500;
}

.EventOptions {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.EventOptionItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.EventOptionLabel {
  cursor: pointer;
}
.DialogContent {
  background-color: #ffff;
  border-radius: 33px;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
}
.submitBtn{
    width: 209.78px;
    height: 42px;
    background-color: #FFA500;
    color: #fff;
    font-size: 18px;
    border: none;
    box-shadow: 0px 4px 4px 0px #00000040;
    border-radius: 5px;
    font-weight: 700;
    cursor: pointer;
}
.goBackBtn{
    width: 118px;
    height: 42px;
    border-radius: 5px;
    cursor: pointer;
    border: none;
    background-color: #fff;
    box-shadow: 0px 0px 4px 0px #00000040;
    font-size: 18px;
    font-weight: 500;
    color: #585858;
    text-decoration: underline;
}
  
  .EventOptionItem {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .EventRadioInput {
    appearance: none;
    -webkit-appearance: none;
    width: 18px !important;
    height: 18px !important;
    border: 1px solid #585858;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: border-color 0.3s ease, background-color 0.3s ease;
  }
  
  .EventRadioInput:checked {
    border: 1px solid #179D52;
    border-color: #179D52;
    background-color: #FFFFFF;
  }
  
  .EventRadioInput:checked::after {
    content: "";
    display: block;
    width: 14px;
    height: 14px;
    background-color: #179D52;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .EventRadioLabel {
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #585858;
  }
