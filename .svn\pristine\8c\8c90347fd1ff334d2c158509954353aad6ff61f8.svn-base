import { ApplicationInsights } from '@microsoft/applicationinsights-web';
import { ReactPlugin } from '@microsoft/applicationinsights-react-js';
import environment from '../helper/environment';

const reactPlugin = new ReactPlugin();

const { instrumentationKey, appInsightsEnabled } = environment.getAppInsightsSettings(
    window.location.hostname
);

let appInsights: ApplicationInsights | null = null;

if (appInsightsEnabled && instrumentationKey) {
    appInsights = new ApplicationInsights({
        config: {
            instrumentationKey: instrumentationKey,
            enableAutoRouteTracking: false, // Disable default tracking (doesn't work with <PERSON>h<PERSON><PERSON><PERSON>)
            disableFetchTracking: false, // Track fetch/XHR calls
            disableExceptionTracking: false, // Capture JavaScript errors
            disableAjaxTracking: false, // Track API requests
            enableRequestHeaderTracking: true,
            enableResponseHeaderTracking: true,
            enableUnhandledPromiseRejectionTracking: true,
            maxBatchInterval: 0, // Send telemetry immediately
            extensions: [reactPlugin],
            extensionConfig: {
                [reactPlugin.identifier]: { history: null },
            },
        },
    });

    appInsights.loadAppInsights();

    // Track page views manually for HashRouter
    const trackPageView = () => {
        appInsights?.trackPageView({ uri: window.location.hash.substring(1) || '/' });
    };

    // Listen for hash changes
    window.addEventListener('hashchange', trackPageView);
    trackPageView(); // Track first page load

    // Capture unhandled JavaScript errors
    window.addEventListener('error', (event) => {
        appInsights?.trackException({
            error: event.error,
            severityLevel: 3, // Warning level
        });
    });

    // Capture unhandled Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        appInsights?.trackException({
            error: event.reason,
            severityLevel: 4,
        });
    });

    setInterval(() => {
        if (appInsights) {
            const perf = window.performance;
            appInsights.trackMetric({
                name: 'Page Load Time',
                average: perf.timing.loadEventEnd - perf.timing.navigationStart,
            });
        }
    }, 5000);
} else {
    console.warn('⚠️ Application Insights is disabled or missing Instrumentation Key.');
}

export { appInsights, reactPlugin };
