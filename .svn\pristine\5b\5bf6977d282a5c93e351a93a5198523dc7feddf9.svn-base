.layout {
    background-color: #179D52;
    display: flex;
}

.stepsArea {
    width: 25%;
}

.outletArea {
    width: 75%;
    background-color: #FFFFFF;
    border-top-left-radius: 80px;
}

.layoutMobile {
    background-color: #179D52;
    display: flex;
    flex-direction: column;
}


.stepsAreaMobile {
    height: 10%;
    transition: 0.5s all ease-in-out;
    margin-top: var(--safe-area-top);
}


.outletAreaMobile {
    height: 90%;
    background-color: #FFFFFF;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding-bottom: var(--safe-area-bottom);
}