import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>layView,
} from "@react-google-maps/api";
import React, { useEffect, useMemo, useState } from "react";
import Legend1 from "../../assets/images/map-legend-1.png";
import Legend2 from "../../assets/images/map-legend-2.png";
import Legend3 from "../../assets/images/map-legend-3.png";
import { Helper } from "./ProviderProfile/types";

// ==========================
// 1. GeoLocation Service (S)
// ==========================
class GeoLocationService {
  static getGeoLocation(helper: Helper) {
    return {
      allPoints: helper?.geoJson?.features?.filter(
        (f) =>
          f?.geometry?.type === "Point" &&
          f?.properties?.isCenterPoint === false &&
          f?.properties?.id !== helper.id
      ),
      user: helper?.geoJson.features.find(
        (f) => f?.properties?.isCenterPoint === true
      ),
      profile: helper?.geoJson.features.find(
        (f) => f?.properties?.id === helper.id
      ),
    };
  }
}

// ==========================
// 2. Map Marker Factory (O)
// ==========================
interface MarkerProps {
  position: google.maps.LatLngLiteral;
  icon: string;
  onClick?: () => void;
  zIndex?: number;
}

const MapMarker: React.FC<MarkerProps> = ({
  position,
  icon,
  onClick,
  zIndex,
}) => (
  <Marker position={position} icon={icon} onClick={onClick} zIndex={zIndex} />
);

// ==========================
// 3. Polyline Factory (O)
// ==========================
interface MapLineProps {
  path: google.maps.LatLngLiteral[];
}

const MapPolyline: React.FC<MapLineProps> = ({ path }) => (
  <Polyline
    path={path}
    options={{
      strokeColor: "#585858",
      strokeOpacity: 1,
      strokeWeight: 1,
      geodesic: true,
    }}
  />
);

// ==========================
// 4. InfoWindow Component (O, I)
// ==========================
interface CustomInfoWindowProps {
  position: google.maps.LatLngLiteral;
  content: Helper["geoJson"]["features"][0]["properties"];
  onClose: () => void;
}

const CustomInfoWindow: React.FC<CustomInfoWindowProps> = ({
  position,
  content,
  onClose,
}) => (
  <OverlayView
    position={position}
    mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
  >
    <div
      className="bg-white border-round-md border border-gray-300 overflow-hidden"
      style={{
        minHeight: "150px",
        maxHeight: "150px",
        minWidth: "300px",
        maxWidth: "300px",
        transform: "translate(-100%, -100%)",
        marginTop: "-10px",
        marginLeft: "-10px",
        cursor: "default",
      }}
    >
      <div className="flex justify-between items-center w-full h-full gap-2 relative">
        <img
          src={content.imageSrc}
          height={"150px"}
          style={{ objectFit: "contain" }}
        />
        <div className="flex flex-column mt-2">
          <h1 className="p-0 m-0">{content.publicName}</h1>
          <h3 className="p-0 m-0">Review</h3>
          <p className="p-0 m-0">{content.metadata.feedback}</p>
        </div>
        <div
          className="absolute right-0 top-0 cursor-pointer"
          style={{ fontSize: "22px" }}
          onClick={(e) => {
            e.preventDefault();
            onClose();
          }}
        >
          🗙
        </div>
      </div>
    </div>
  </OverlayView>
);

// ==========================
// 5. Map Component (D)
// ==========================
function ReviewMap({ helper }: { helper: Helper }) {
  const [state, setState] = useState<ReturnType<
    typeof GeoLocationService.getGeoLocation
  > | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [activeInfoIndex, setActiveInfoIndex] = useState<number | string>(null);
  const [rotationIndex, setRotationIndex] = useState(0);
  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral | undefined>(undefined);
  // const mapCenter = useMemo(() => {
  //   if (!state) return undefined;
  //   return {
  //     lng: state.profile.geometry.coordinates[0],
  //     lat: state.profile.geometry.coordinates[1],
  //   };
  // }, [state]);

  useEffect(() => {
    setState(GeoLocationService.getGeoLocation(helper));
  }, [helper]);

  useEffect(() => {
    if (!state?.allPoints?.length) return;

    const interval = setInterval(() => {
      const current = state.allPoints[rotationIndex];
      setActiveInfoIndex(current?.properties.id);
      setRotationIndex((prev) => (prev + 1) % state.allPoints.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [state?.allPoints, rotationIndex]);

  useEffect(() => {
    if (activeInfoIndex && state) {
      const activePoint = state.allPoints.find(
        (p) => p.properties.id === activeInfoIndex
      );
      if (activePoint) {
        setMapCenter({
          lng: activePoint.geometry.coordinates[0],
          lat: activePoint.geometry.coordinates[1],
        });
      }
    }
  }, [activeInfoIndex, state]);

  const handleMarkerClick = (id: string | number) => {
    setActiveInfoIndex(id === activeInfoIndex ? null : id);
  };

  if (!state) return null;

  return (
    <div style={{ height: "600px", width: "95%", maxWidth: "95%" }}>
      <GoogleMap
        mapContainerStyle={{ width: "100%", height: "100%" }}
        zoom={14}
         center={mapCenter || {
          lng: state.profile.geometry.coordinates[0],
          lat: state.profile.geometry.coordinates[1],
        }}
        onLoad={() => setMapLoaded(true)}
        options={{
          disableDefaultUI: true,
          clickableIcons: false,
          minZoom: 5,
          maxZoom: 18,
          gestureHandling: "greedy",
          mapTypeControl: false,
          streetViewControl: false,
          rotateControl: false,
          styles: [
            { featureType: "all", stylers: [{ visibility: "on" }] },
            { featureType: "transit", stylers: [{ visibility: "off" }] },
            { featureType: "poi", stylers: [{ visibility: "off" }] },
          ],
        }}
      >
        {mapLoaded && (
          <>
            {/* User Marker */}
            <MapMarker
              position={{
                lng: state.user.geometry.coordinates[0],
                lat: state.user.geometry.coordinates[1],
              }}
              icon={Legend1}
            />

            {/* Profile Marker */}
            <MapMarker
              position={{
                lng: state.profile.geometry.coordinates[0],
                lat: state.profile.geometry.coordinates[1],
              }}
              icon={Legend2}
            />

            {/* Line between User and Profile */}
            <MapPolyline
              path={[
                {
                  lng: state.user.geometry.coordinates[0],
                  lat: state.user.geometry.coordinates[1],
                },
                {
                  lng: state.profile.geometry.coordinates[0],
                  lat: state.profile.geometry.coordinates[1],
                },
              ]}
            />

            {/* Other points */}
            {state.allPoints.map((p) => (
              <React.Fragment key={p.properties.id}>
                <MapMarker
                  position={{
                    lng: p.geometry.coordinates[0],
                    lat: p.geometry.coordinates[1],
                  }}
                  icon={Legend3}
                  onClick={() => handleMarkerClick(p.properties.id)}
                  zIndex={1}
                />
                {activeInfoIndex === p.properties.id && (
                  <CustomInfoWindow
                    position={{
                      lng: p.geometry.coordinates[0],
                      lat: p.geometry.coordinates[1],
                    }}
                    content={p.properties}
                    onClose={() => setActiveInfoIndex(null)}
                  />
                )}
              </React.Fragment>
            ))}

            {/* Lines from other points to profile */}
            {state.allPoints.map((p) => (
              <MapPolyline
                key={p.properties.id + "-polyline"}
                path={[
                  {
                    lng: p.geometry.coordinates[0],
                    lat: p.geometry.coordinates[1],
                  },
                  {
                    lng: state.profile.geometry.coordinates[0],
                    lat: state.profile.geometry.coordinates[1],
                  },
                ]}
              />
            ))}
          </>
        )}
      </GoogleMap>
    </div>
  );
}

export default ReviewMap;
