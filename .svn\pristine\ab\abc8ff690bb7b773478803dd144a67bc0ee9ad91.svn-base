import { InputText } from "primereact/inputtext";
import React, { useState, useEffect } from "react";
import "primeicons/primeicons.css";
import { useJoinNowData } from "../../../model/JoinNowContext";
import { validateEmail, validatePassword } from "../../utils/validation";
import "../../utils/util.css";
import "./join-now.css";
import c from "../../../helper/juggleStreetConstants";

interface SignUpPageProps {
  onNext: () => void;
  onPrevious?: () => void;
  setCurrentPage: (page: number) => void;
}

export const Joinnow3: React.FC<SignUpPageProps> = ({
  onNext,
  onPrevious,
  setCurrentPage,
}) => {
  const { joinNowData, dispatch } = useJoinNowData();
  const [isFormValid, setIsFormValid] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false); // State to toggle password visibility
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false); // State to toggle password visibility

  const [formData, setFormData] = useState({
    email: joinNowData.email || "",
    password: joinNowData.password || "",
    confirmPassword: joinNowData.confirmPassword || "",
    affiliateCode: joinNowData.affiliateCode || "",
  });

  const [error, setError] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    affiliateCode: "",
  });

  useEffect(() => {
    setFormData({
      email: joinNowData.email || "",
      password: joinNowData.password || "",
      confirmPassword: joinNowData.confirmPassword || "",
      affiliateCode: joinNowData.affiliateCode || "",
    });
  }, [joinNowData]);

  useEffect(() => {
    checkFormValidity();
  }, [formData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    dispatch({ type: "UPDATE_FORM", payload: { [name]: value } });
    setError((prevError) => ({ ...prevError, [name]: "" }));
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
    setIsConfirmPasswordVisible(!isConfirmPasswordVisible);
  };

  const checkFormValidity = () => {
    const isValid =
      formData.email.trim() !== "" &&
      formData.password.trim() !== "" &&
      formData.confirmPassword !== "";
    setIsFormValid(isValid);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    let validationError = {
      email: "",
      password: "",
      confirmPassword: "",
      affiliateCode: "",
    };

    validationError.email = validateEmail(formData.email);
    if (!validationError.email) {
      validationError.password = validatePassword(formData.password);
      if (!validationError.password) {
        if (!formData.confirmPassword.trim()) {
          validationError.confirmPassword = "Repeat Password is required";
        } else if (formData.password !== formData.confirmPassword) {
          validationError.confirmPassword =
            "Passwords do not match. Please try again.";
        }
      }
    }

    // Check if there are any validation errors
    if (
      validationError.email ||
      validationError.password ||
      validationError.confirmPassword
    ) {
      setError(validationError); // Set the validation errors
      return; // Stop form submission
    }

    // If validation passes, dispatch and move to the next step
    dispatch({ type: "UPDATE_FORM", payload: formData });
    onNext();
  };

  const handlePrevious = (e) => {
    e.preventDefault();
    if (
      joinNowData.clientType === c.lookingFor.INDIVIDUAL_CLIENT ||
      joinNowData.clientType === c.lookingFor.INDIVIDUAL_PROVIDER
    ) {
      onPrevious();
    } else if (joinNowData.clientType === c.lookingFor.BUSINESS_CLIENT) {
      setCurrentPage(5);
    } else {
      onPrevious();
    }
  };

  return (
    <form
      className="pr-4 pl-4"
      onSubmit={handleSubmit}
      style={{ marginTop: "-29px", display: "flex", flexDirection: "column" }}
    >
      <div className="flex-column flex justify-content-center align-items-center">
        <p className="h-joinnow">Account Set up</p>
        <p className="h-joinnow-prg">
          Please input your email address and password
        </p>
        <div className=" flex flex-column align-items-center">
          <div className=" col-12 md:col-12">
            {/* <label className="font-semibold">Your Email Address</label> */}
            <div className="input-container">
              <InputText
                name="email"
                value={formData.email}
                placeholder=""
                onChange={handleChange}
                onFocus={(e) =>
                  (e.currentTarget.style.borderBottom = "1px solid green")
                }
                onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                // className={error.email ? 'border-red' : ''}
                className={`input-placeholder ${error.email ? "border-red" : formData.email ? "border-green" : ""
                  }`}
              />
              <label htmlFor="email" className="label-name">
                 Email*
              </label>
            </div>
          </div>
          <div className="col-12 md:col-12">
            <div className="input-container">
              <InputText
                name="password"
                type={isPasswordVisible ? "text" : "password"} // Toggle type attribute
                value={formData.password}
                placeholder=""
                onChange={handleChange}
                onFocus={(e) =>
                  (e.currentTarget.style.borderBottom = "1px solid green")
                }
                onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                className={`input-placeholder ${error.password ? "border-red" : formData.password ? "border-green" : ""
                  }`}
              />
              <label htmlFor="password" className="label-name">
                Password*
              </label>
              <i
                className={`pi ${isPasswordVisible ? "pi-eye-slash" : "pi-eye"
                  }`}
                onClick={togglePasswordVisibility}
                style={{
                  position: "absolute",
                  right: "10px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  cursor: "pointer",
                  color: "#555",
                }}
              ></i>
            </div>
          </div>
          <div className="flex flex-column align-items-center">
            <div className="col-12 md:col-12">
              <div className="input-container">
                <InputText
                  name="confirmPassword"
                  type={isConfirmPasswordVisible ? "text" : "password"} // Toggle type attribute
                  value={formData.confirmPassword}
                  placeholder=""
                  onChange={handleChange}
                  onFocus={(e) =>
                    (e.currentTarget.style.borderBottom = "1px solid green")
                  }
                  onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                  className={`input-placeholder ${error.confirmPassword ? "border-red" : formData.confirmPassword ? "border-green" : ""
                    }`}
                />
                <label htmlFor="confirmPassword" className="label-name">
                  Repeat password*
                </label>
                <i
                  className={`pi ${isConfirmPasswordVisible ? "pi-eye-slash" : "pi-eye"
                    }`}
                  onClick={togglePasswordVisibility}
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                    color: "#555",
                  }}
                ></i>
              </div>
            </div>
            <div className="mt-1 col-12 md:col-12">
              {/* <label className="font-semibold">Join Code (optional)</label> */}
              <div className="input-container">
                <InputText
                  name="affiliateCode"
                  type="text"
                  placeholder=""
                  value={formData.affiliateCode}
                  onChange={handleChange}
                  onFocus={(e) =>
                    (e.currentTarget.style.borderBottom = "1px solid green")
                  }
                  onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                  className={`input-placeholder ${error.affiliateCode ? "" : formData.affiliateCode ? "border-green" : ""}`}
                />
                <label htmlFor="confirmPassword" className="label-name">
                  Join Code (optional)
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="text-center error-message">
        {error.password && <span>{error.password}</span>}
        {error.confirmPassword && <span>{error.confirmPassword}</span>}
        {error.email && <span>{error.email}</span>}
      </div>

      <div style={{ flex: 1 }}></div>
      <div
        className="flex"
        style={{
          marginBottom: "8%",
          marginTop: "7%",
          justifyContent: "space-between",
        }}
      >
        <div
          className="flex align-items-center justify-content-center"
          style={{ textWrap: "nowrap" }}
        >
          <i
            className="h-joinnow4-lable pi pi-angle-left pr-2"
            style={{ fontSize: "medium" }}
          ></i>
          <a
            className=" h-joinnow4-lable pr-3 cursor-pointer "
            onClick={handlePrevious}
          >
            Go back
          </a>
        </div>
        <button
          className={`flex align-items-center justify-content-center  h-joinnow-button ${isFormValid ? "shadow-4" : ""
            }`}
          onClick={handleSubmit}
          disabled={!isFormValid}
          style={{
            backgroundColor: isFormValid ? "#FFA500" : "#DFDFDF",
            borderColor: isFormValid ? "#FFA500" : "#DFDFDF", // Set border color conditionally
            color: isFormValid ? "#FFFFFF" : "#c2c7d1", // Set text color conditionally
            cursor: isFormValid ? "pointer" : "not-allowed",
            // position: 'absolute',

            minWidth: "177px",
          }}
        >
          Next
          <i className="pi pi-angle-right" style={{ fontSize: "large" }}></i>
        </button>
      </div>
    </form>
  );
};
