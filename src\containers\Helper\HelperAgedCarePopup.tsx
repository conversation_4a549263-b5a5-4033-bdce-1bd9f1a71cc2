import React, { useState } from "react";
import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import useLoader from "../../hooks/LoaderHook";
import Service from "../../services/services";
import environment from "../../helper/environment";
import { useSelector } from "react-redux";
import { RootState } from "../../store";

import styles from "../Parent/styles/event-aged-care-popup.module.css";
import c from "../../helper/juggleStreetConstants";

const HelperAgedCarePopup = ({ visible, onHide }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const { enableLoader, disableLoader } = useLoader();

  const getEventType = (option) => {
    const eventTypes = {
      "Yes Please": c.appEventType.interestinhomeagedcareYes,
      "No Thanks": c.appEventType.interestinhomeagedcareNo,
      //   "N/A": c.appEventType.interestinhomeagedcareNA,
    };
    return eventTypes[option] || c.appEventType.unspecified;
  };

  const handleSubmit = async () => {
    enableLoader();
    try {
      const payload = {
        eventType: getEventType(selectedOption),
        platform: "desktop-web",
        appVersion: environment.getAppVersion(),
        distVersion: environment.getAppVersion(),
        country: environment.getCountry(window.location.hostname),
      };

      await Service.logAppEvent(
        () => {
          setSelectedOption(null);
          onHide();
          disableLoader();
        },
        (error) => {
          console.error("Failed to log event:", error);
          disableLoader();
        },
        payload
      );
    } catch (error) {
      console.error("An unexpected error occurred:", error);
      disableLoader();
    }
  };

  const options = ["Yes Please", "No Thanks"];

  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      style={{ width: "90%", maxWidth: "500px" }}
      content={
        <div className={styles.DialogContent}>
          <IoClose
            onClick={onHide}
            className={styles.EventCloseButton}
            data-testid="close-button"
          />
          <div className={styles.EventContent}>
            <div className={styles.EventHeader}>
              <h2 className={styles.EventTitle}>
                Are you looking for more work?
              </h2>
            </div>
            <p className={styles.EventDescription}>
              In 2025 you'll be able to help local families look after their
              aging parents. We will be offering the following in-home aged care
              jobs:
            </p>

            <div className={styles.EventGrid}>
              <div>
                <ul className={styles.EventList}>
                  <li>Meal preparation</li>
                  <li>Transport</li>
                  <li>Shopping</li>
                </ul>
              </div>
              <div>
                <ul className={styles.EventList}>
                  <li>Cleaning</li>
                  <li>Home maintenance</li>
                  <li>Companionship</li>
                </ul>
              </div>
            </div>

            <p className={styles.EventQuestion}>
              Interested in learning more about our in-home aged care?
            </p>

            <div className={styles.EventOptions}>
              {options.map((option) => (
                <div key={option} className={styles.EventOptionItem}>
                  <input
                    type="radio"
                    id={`option_${option}`}
                    name="careOption"
                    value={option}
                    onChange={(e) => setSelectedOption(e.target.value)}
                    checked={selectedOption === option}
                    className={styles.EventRadioInput}
                    data-testid={`radio-${option.toLowerCase()}`}
                  />
                  <label
                    htmlFor={`option_${option}`}
                    className={styles.EventRadioLabel}
                    data-testid={`label-${option.toLowerCase()}`}
                  >
                    {option}
                  </label>
                </div>
              ))}
            </div>
            <div
              style={{
                marginTop: "20px",
                display: "flex",
                flexDirection: "row",
                gap: "20px",
              }}
            >
              <button className={styles.goBackBtn} onClick={onHide}>
                Go Back
              </button>
              <button
                onClick={handleSubmit}
                disabled={!selectedOption}
                className={styles.submitBtn}
                data-testid="submit-button"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      }
    />
  );
};

export default HelperAgedCarePopup;
