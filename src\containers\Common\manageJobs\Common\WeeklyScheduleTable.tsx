import React, { useState } from "react";
import styles from "../../styles/weekly-schedule-table.module.css";
import { Divider } from "primereact/divider";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import clockStartEnd from "../../.././../assets/images/Icons/clockend.png";
import sample from "../../.././../assets/images/sample_profile.png";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { useNavigate } from "react-router-dom";
import useIsMobile from "../../../../hooks/useIsMobile";
import calender from "../../../../assets/images/Icons/calender.png";
import CustomDialog from "../../../../commonComponents/CustomDialog";
import ProviderProfile from "../../../Parent/ProviderProfile/ProviderProfile";
import { IframeBridge } from "../../../../services/IframeBridge";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
export interface WeeklyTableRow {
  dayOfWeek: number;
  day: string;
  startTime: string;
  endTime: string;
  shiftId: number;
  price: number;
  awardedAplicantId: number | null;
  applicants: Array<{
    applicantId: number;
    availabilityId: number;
    publicName: string;
    imageSrc: string;
    status: number;
  }>;
  onClick?: () => void;
  onClickInvite?: () => void;
  onClickSchedule?: () => void;
}

interface WeeklyScheduleTableProps {
  rows: WeeklyTableRow[];
  jobDate?: Date;
  onChat?: (id: Number) => void;
  onViewProfile?: (row: WeeklyTableRow) => void;
  onInviteHelpers?: (row: WeeklyTableRow) => void;
  onViewWeeklySchedule?: (row: WeeklyTableRow) => void;
}

const WeeklyScheduleTable: React.FC<WeeklyScheduleTableProps> = ({ rows, jobDate, onViewProfile, onInviteHelpers, onViewWeeklySchedule, onChat }) => {
  const navigate = useNavigate();
  const { isMobile } = useIsMobile();
  const getAwardedAplicant = (id: number) => {
    for (const row of rows) {
      for (const app of row.applicants) if (app.applicantId === id) return app;
    }
  };
  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);

  const redirectAfterHome = (id: number) => {
    IframeBridge.sendToParent({
      type: "navigateHelperProfile",
      data: {
        id: String(id),
      },
    });

    if (!inIframe) {
      if (isMobile) {
        const navigatePArams = new URLSearchParams();
        navigatePArams.set("id", id.toString());
        const clientType = utils.getCookie(CookiesConstant.clientType);

        if (clientType === "2") {
          navigate(`/business-home/provider-profile?${navigatePArams.toString()}`);
        } else if (clientType === "1") {
          navigate(`/parent-home/provider-profile?${navigatePArams.toString()}`);
        } else {
          console.warn("Unknown clientType, no navigation performed.");
        }
      } else {
        setSelectedProviderId(id);
        setShowPopup(true);
      }
    }
  };
  const handleCloseProfilePopup = () => {
    setShowPopup(false);
  };
  function formatTimeTo12Hour(time: string): string {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  }
  function formatDate(date: Date): string {
    const getOrdinalSuffix = (day: number): string => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });

    return `${day}${getOrdinalSuffix(day)} of ${month}`;
  }
  return !isMobile ? (
    <>
      <div className="grid mt-2">
        <div className="col-12">
          <div className={styles.WeeklyTableContainer}>
            {/* Header */}
            <div className="grid">
              <div className="col-12">
                <div className={`${styles.WeeklyHeader} grid`}>
                  <div className="col-3 flex align-items-center">
                    {/* Job Date */}
                    <div className={styles.WeeklyHeaderItem}>Job Day</div>
                  </div>
                  <div className="col-3 flex align-items-center">
                    {/* Job Time */}
                    <div className={styles.WeeklyHeaderItem}>Job Time</div>
                  </div>
                  <div className="col-3 flex align-items-center">
                    {/* Helper/Name */}
                    <div className={styles.WeeklyHeaderItem}>Helper/Name</div>
                  </div>
                  <div className="col-3 flex align-items-center">
                    {/* Job Status */}
                    <div className={styles.WeeklyHeaderItem}>Job Status</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Rows */}
            {rows.map((row, index) => (
              <div key={index} className="grid">
                <div className="col-12">
                  <div className={`${styles.WeeklyRow} grid`}>
                    {/* Date Column */}
                    <div className="col-3 flex align-items-center justify-content-center">
                      <div className={styles.dateCell}>
                        <p className="m-0 text-color-secondary text-xl font-bold">{row.day}</p>
                      </div>
                      <Divider layout="vertical" />
                    </div>

                    {/* Time Column */}
                    <div className="col-3 flex align-items-center justify-content-center">
                      <div className="flex align-items-center gap-2 mx-auto">
                        <div className={styles.timeGet}>
                          <img src={clockStart} alt="clockStart" width="13.5px" height="13.5px" />
                          {formatTimeTo12Hour(row.startTime)}
                        </div>
                        <p className="mx-2 text-sm font-normal text-color-secondary">to</p>
                        <div className={styles.timeGet}>
                          <img src={clockStartEnd} alt="clockEnd" width="13.5px" height="13.5px" />
                          {formatTimeTo12Hour(row.endTime)}
                        </div>
                      </div>
                      <Divider layout="vertical" className={`mx-3 ${styles.TableDivider}`} />
                    </div>

                    {/* Helper/Name Column */}
                    <div className="col-3 flex align-items-center justify-content-center">
                      {getAwardedAplicant(row.awardedAplicantId)?.publicName ? (
                        <div className="flex align-items-center gap-2 mx-auto">
                          <img src={getAwardedAplicant(row.awardedAplicantId).imageSrc || sample} alt="helper" className={styles.imagesSample} />
                          <div className="flex flex-column gap-1">
                            <h3 className={styles.helperName}>{getAwardedAplicant(row.awardedAplicantId).publicName}</h3>
                            <button onClick={() => redirectAfterHome(row.awardedAplicantId)} className={styles.viewProfile}>
                              View Profile
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-column align-items-center gap-2">
                          <button className={styles.inviteHelpers} onClick={() => onInviteHelpers && onInviteHelpers(row)}>
                            Invite Helpers
                          </button>
                          <button className={styles.viewSchedule} onClick={() => onViewWeeklySchedule && onViewWeeklySchedule(row)}>
                            View Weekly Schedule
                          </button>
                        </div>
                      )}
                      <Divider layout="vertical" className={styles.TableDividerTwo} />
                    </div>

                    {/* Status Column */}
                    <div className="col-3 flex align-items-center justify-content-center">
                      <button className={styles.approvedStatusBtn}>Job Awarded</button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <CustomDialog
        visible={showPopup}
        style={{
          width: "100%",
          maxWidth: "100%",
          height: "100%",
          maxHeight: "100%",
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          overflowY: "auto",
        }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile candidateId={selectedProviderId} onClose={handleCloseProfilePopup} />
      </CustomDialog>
    </>
  ) : (
    <div className={styles.cardContainerMobile}>
      {rows.map((row, index) => {
        const helper = getAwardedAplicant(row.awardedAplicantId);

        return (
          <div key={index} className={styles.scheduleCardMobile}>
            {/* Header with day and status */}
            <div className={styles.cardHeaderMobile}>
              <h2 className={styles.dayTitleMobile}>{row.day}</h2>
              <div className={styles.statusTextMobile}>
                <span className={styles.statusDotMobile} />
                Job Awarded!
              </div>
            </div>
            <Divider className="mb-2 mt-1" />
            {/* Date */}
            <div className={styles.dateRowMobile}>
              <img src={calender} alt="calender" width="16px" height="18px" />
              {formatDate(jobDate)}
            </div>

            {/* Time */}
            <div className={styles.timeRowMobile}>
              <img src={clockStart} alt="clockStart" width="16px" height="16px" />
              {`${formatTimeTo12Hour(row.startTime)} to ${formatTimeTo12Hour(row.endTime)}`}
            </div>
            <Divider className=" mt-2" />
            {/* Helper section with chat button */}
            {helper && (
              <div className={styles.helperSectionMobile}>
                <div className={styles.helperInfoMobile}>
                  <img src={helper.imageSrc || "/default-avatar.png"} alt={helper.publicName} className={styles.helperImageMobile} />

                  <span className={styles.ImgcheckmarkMobile}>✓</span>

                  <div className={styles.helperDetailsMobile}>
                    <h3 className={styles.helperNameMobile}>{helper.publicName}</h3>
                    <button onClick={() => redirectAfterHome(helper.applicantId)} className={styles.viewProfileMobile}>
                      View profile
                    </button>
                  </div>
                </div>
                <button onClick={() => onChat(helper.applicantId)} className={styles.chatButtonMobile}>
                  Chat
                </button>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default WeeklyScheduleTable;
