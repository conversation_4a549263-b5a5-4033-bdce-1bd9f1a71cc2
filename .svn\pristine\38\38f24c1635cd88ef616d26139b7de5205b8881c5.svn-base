import React, { useRef, useState } from "react";
import styles from "../../../../containers/Common/styles/editTimesheet.module.css";
import { FaArrowLeft } from "react-icons/fa";
import { RiArrowDropDownLine } from "react-icons/ri";
import { FiPlus } from "react-icons/fi";
import CalenderIcon from "../../../../assets/images/Icons/calender.png"
import Doller from "../../../../assets/images/Icons/Dollar1.png"

interface Shift {
    start: string;
    finish: string;
}

interface EditTimesheetProps {
    day: string,
    date: string;
    profileImage: string;
    profileName: string;
    baseRate: number;
    extraRate: number;
    initialShifts: Shift[];
    onClose: () => void;
    onSave?: (shifts: Shift[]) => void;
}

const EditTimesheet: React.FC<EditTimesheetProps> = ({
    day,
    date,
    profileImage,
    profileName,
    baseRate,
    extraRate,
    initialShifts,
    onClose,
    onSave,
}) => {
    const [errors, setErrors] = useState<{
        [key: number]: {
            start?: boolean;
            finish?: boolean;
            custom?: string;
            custom1?: string;
        };
    }>({});


    const initialShiftsRef = useRef<Shift[]>(JSON.parse(JSON.stringify(initialShifts)));
    const [shifts, setShifts] = useState<Shift[]>(initialShifts);
    const [isChanged, setIsChanged] = useState(false);
    const [openDropdown, setOpenDropdown] = useState<{ shiftIndex: number, field: 'start' | 'finish' } | null>(null);

    const handleTimeChange = (index: number, key: "start" | "finish", value: string) => {
        const updated = [...shifts];
        updated[index][key] = value;
        setShifts(updated);
        setIsChanged(true);
        setOpenDropdown(null);
    };

    const handleAddShift = () => {
        const newShift1 = { start: "", finish: "" };
        const newShift2 = { start: "", finish: "" };

        const updatedShifts = [...shifts, newShift1, newShift2];
        setShifts(updatedShifts);

        const clone1 = { ...newShift1 };
        const clone2 = { ...newShift2 };
        initialShiftsRef.current = [...initialShiftsRef.current, clone1, clone2];

        setErrors((prevErrors) => {
            const lastIndex = shifts.length;
            return {
                ...prevErrors,
                [lastIndex]: {},
                [lastIndex + 1]: {},
            };
        });

        setIsChanged(true);
    };

    const timeToMinutes = (timeStr: string): number => {
        const [time, modifier] = timeStr.split(/(am|pm)/);
        let [hours, minutes] = time.split(':').map(Number);

        if (modifier === 'pm' && hours !== 12) hours += 12;
        if (modifier === 'am' && hours === 12) hours = 0;

        return hours * 60 + minutes;
    };

    const validateShifts = (): boolean => {
        const newErrors: { [key: number]: { start?: boolean; finish?: boolean; custom?: string } } = {};

        shifts.forEach((shift, index) => {
            const shiftErrors: { start?: boolean; finish?: boolean; custom?: string } = {};

            if (!shift.start) shiftErrors.start = true;
            if (!shift.finish) shiftErrors.finish = true;

            if (shift.start && shift.finish && shift.start === shift.finish) {
                shiftErrors.custom = "Start and end times cannot be the same";
            }

            newErrors[index] = shiftErrors;
        });

        const startIndex = shifts.length >= 4 ? shifts.length - 2 : 0;

        for (let i = startIndex; i < shifts.length - 1; i++) {

            const current = shifts[i];
            const next = shifts[i + 1];

            if (current.finish && next.start) {
                const currentEnd = timeToMinutes(current.finish);
                const nextStart = timeToMinutes(next.start);

                if (nextStart <= currentEnd) {
                    if (!newErrors[i + 1]) newErrors[i + 1] = {};
                    newErrors[i + 1].custom = `Shift ${i + 2} must start after Shift ${i + 1} ends (${current.finish})`;
                }
            }
        }

        Object.keys(newErrors).forEach(index => {
            if (Object.keys(newErrors[+index]).length === 0) delete newErrors[+index];
        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSave = () => {
        if (validateShifts()) {
            onSave(shifts);
        }
    };


    const handleCancel = () => {
        const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
        setShifts(clonedInitial);
        setIsChanged(false);
        setOpenDropdown(null);
    };

    const toggleDropdown = (shiftIndex: number, field: 'start' | 'finish') => {
        if (openDropdown?.shiftIndex === shiftIndex && openDropdown?.field === field) {
            setOpenDropdown(null);
        } else {
            setOpenDropdown({ shiftIndex, field });
        }
    };

    const timeOptions = [
        "6:00am", "6:15am", "6:30am", "6:45am", "7:00am", "7:15am", "7:30am", "7:45am",
        "8:00am", "8:15am", "8:30am", "8:45am", "9:00am", "9:15am", "9:30am", "9:45am",
        "10:00am", "10:15am", "10:30am", "10:45am", "11:00am", "11:15am", "11:30am", "11:45am",
        "12:00pm", "12:15pm", "12:30pm", "12:45pm", "1:00pm", "1:15pm", "1:30pm", "1:45pm",
        "2:00pm", "2:15pm", "2:30pm", "2:45pm", "3:00pm", "3:15pm", "3:30pm", "3:45pm",
        "4:00pm", "4:15pm", "4:30pm", "4:45pm", "5:00pm", "5:15pm", "5:30pm", "5:45pm",
        "6:00pm", "6:15pm", "6:30pm", "6:45pm", "7:00pm", "7:15pm", "7:30pm", "7:45pm",
        "8:00pm", "8:15pm", "8:30pm", "8:45pm", "9:00pm"
    ];

    return (
        <div className={styles.container}>
            <div className={styles.headerWrapper}>
                <button className={styles.backBtn} onClick={onClose}>
                    <span className={styles.arrowCircle}>
                        <span className={styles.arrow}><FaArrowLeft /></span>
                    </span>
                    Go back
                </button>
            </div>

            <div className={styles.header}>
                <div className={styles.leftSection}>
                    <div className={styles.title}>Edit Timesheet</div>
                    <div className={styles.dateInfo}>
                        <img src={CalenderIcon} alt="calendar" className={styles.rowIcon} />
                        <span>{day}{date}</span>
                    </div>
                </div>
                <div className={styles.profileSection}>
                    <img src={profileImage} alt="Profile" className={styles.profileImg} />
                    <span className={styles.profileName}>{profileName}</span>
                </div>
            </div>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />


            <div className={styles.rateBlock}>
                <img src={Doller} alt="dollar" className={styles.rowIcon} />
                <div className={styles.rateText}>
                    <div>Base Rate: ${baseRate} per hour</div>
                    <div className={styles.indented}>Extra Hours Rate: ${extraRate} per hour</div>
                </div>
            </div>

            <hr style={{ border: '1px solid #F0F4F7', width: '100%' }} />

            {shifts.map((shift, index) => (
                <div key={index} className={styles.shiftBlock}>
                    <div className={styles.shiftLabel}>
                        {index % 2 === 0 ? "Before School" : "After School"}
                    </div>

                    <div className={styles.shiftRow}>
                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} 
                                 ${openDropdown?.shiftIndex === index && openDropdown?.field === 'start' ? styles.active : ''} 
                                 ${(errors[index]?.start || errors[index]?.custom) ? styles.errorBorder : ''}`}
                            >

                                <span
                                    className={`${styles.label} ${shift.start !== (initialShiftsRef.current[index]?.start || "")
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                >
                                    Start
                                </span>

                                <div className={styles.divider} />
                                <span
                                    className={`${styles.time} ${shift.start !== initialShiftsRef.current[index]?.start
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.start || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.start !== initialShiftsRef.current[index]?.start ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'start')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'start')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' ? (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px', transform: 'rotate(180deg)' }} />
                                        ) : (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px' }} />
                                        )}
                                    </span>
                                </div>
                            </div>

                            {errors[index]?.start && (
                                <div className={styles.errorMessage}>Start time is required</div>
                            )}

                            {errors[index]?.custom && (
                                <div className={styles.errorMessage}>{errors[index].custom}</div>
                            )}
                            
                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'start' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.start === time ? styles.dropdownItemSelected : ''}`}
                                            onClick={() => handleTimeChange(index, "start", time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className={styles.timeFieldCustom}>
                            <div
                                className={`${styles.customTimeInput} 
                                 ${openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' ? styles.active : ''} 
                                  ${(errors[index]?.finish || errors[index]?.custom1) ? styles.errorBorder : ''}`}
                            >

                                <span
                                    className={`${styles.label} ${shift.finish !== (initialShiftsRef.current[index]?.finish || "") ? styles.modifiedTime : ''
                                        }`}
                                >
                                    Finish
                                </span>

                                <div className={styles.divider} />

                                <span
                                    className={`${styles.time} ${shift.finish !== initialShiftsRef.current[index]?.finish ? styles.modifiedTime : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {shift.finish || 'Select time'}
                                </span>

                                <div
                                    className={`${styles.dropdownCircle} ${shift.finish !== initialShiftsRef.current[index]?.finish ||
                                        (openDropdown?.shiftIndex === index && openDropdown?.field === 'finish')
                                        ? styles.modifiedTime
                                        : ''
                                        }`}
                                    onClick={() => toggleDropdown(index, 'finish')}
                                    style={{ cursor: 'pointer' }}
                                >
                                    <span>
                                        {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' ? (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px', transform: 'rotate(180deg)' }} />
                                        ) : (
                                            <RiArrowDropDownLine style={{ fontWeight: 400, fontSize: '25px' }} />
                                        )}
                                    </span>
                                </div>
                            </div>
                            {errors[index]?.finish && (
                                <div className={styles.errorMessage}>Finish time is required</div>
                            )}

                            {errors[index]?.custom1 && (
                                <div className={styles.errorMessage}>{errors[index].custom1}</div>
                            )}

                            {openDropdown?.shiftIndex === index && openDropdown?.field === 'finish' && (
                                <div className={styles.dropdownList}>
                                    {timeOptions.map((time) => (
                                        <div
                                            key={time}
                                            className={`${styles.dropdownItem} ${shift.finish === time ? styles.dropdownItemSelected : ''
                                                }`}
                                            onClick={() => handleTimeChange(index, 'finish', time)}
                                        >
                                            {time}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            ))
            }


            {/* <button className={styles.addShiftBtn} onClick={handleAddShift}>
                <span className={styles.plusCircle}><FiPlus /></span>Add another shift
            </button> */}

            <hr style={{ border: '1px solid #F0F4F7', width: '100%', marginTop: '30px' }} />
            <div className={styles.footerButtons}>
                <button
                    className={styles.revertBtn}
                    onClick={() => {
                        if (isChanged) {
                            handleCancel();
                        } else {
                            const clonedInitial = JSON.parse(JSON.stringify(initialShiftsRef.current));
                            setShifts(clonedInitial);
                            setOpenDropdown(null);
                        }
                    }}

                >
                    {isChanged ? "Cancel Changes" : "Revert Changes"}
                </button>

                <button
                    className={`${styles.saveBtn} ${isChanged ? styles.saveBtnEnabled : ''}`}
                    disabled={!isChanged}
                    onClick={handleSave}
                >
                    Save Changes
                </button>
            </div>
        </div >
    );
};

export default EditTimesheet;