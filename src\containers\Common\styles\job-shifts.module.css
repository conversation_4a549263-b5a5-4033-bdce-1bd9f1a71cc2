.rateInput {
  appearance: none;
  border: none;
  border-bottom: 1px solid #dfdfdf;
  width: 33px;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  color: #585858;
  padding: 0;
  outline: none;
}

.rateInput:focus {
  border-bottom: 1px solid #007bff;
}

.textAreaInput {
  resize: none;
  min-height: 122px;
  border-radius: 10px;
  padding: 10px;
  color: #585858;
  font-size: 16px;
  font-weight: 300;
  border: 1px solid #585858;
}

.container {
  display: flex;
  justify-content: space-between;
  /* align-items: center; */
  padding: 10px 0;
  font-family: "Poppins", sans-serif;
  gap: 55px;
  padding-inline: 10px;
}

.timeInfo {
  display: flex;
  align-items: center;
  gap: 5px;
  flex: 2;
}

.dayLabel {
  font-weight: 700;
  color: #585858;
  font-size: 16px;
}

.time {
  font-weight: bold;
  font-size: 16px;
  color: #179D52;
}

.separator {
  font-size: 14px;
font-weight: 700;
  color: #179D52;
}

.tags {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 3;
}

.tag {
  padding: 4px 8px;
  background-color: transparent;
  border: 1px solid #DFDFDF;
  border-radius: 10px;
  font-size: 10px;
  color: #333;
  text-align: center;
  white-space: nowrap;
}

.rateSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
}

.rateInputMobile {
  width: 60px;
  text-align: center;
  font-size: 24px;
  color: #585858;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  padding: 4px;
  text-decoration: underline;

}
.weeklySummaryMobile{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
}
.jobDescriptionMobile{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
}
.textAreaInputMobile {
  resize: none;
  min-height: 278px;
  border-radius: 10px;
  padding: 10px;
  color: #585858;
  font-size: 16px;
  font-weight: 300;
  border: 1px solid #585858;
}
.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}