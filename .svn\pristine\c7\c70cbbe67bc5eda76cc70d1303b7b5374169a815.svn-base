:root {
  --primary-color: #2b9a48 !important;
  /* --font-family: "Mulish", sans-serif; */
  --font-family: "Poppins", sans-serif !important;
}

.p-inputtext:enabled:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 0 0 0.1rem green;
  border-color: #2b9a48;
  border: 1px solid white !important;
  /* background-color: #dee2e6; */
  border-radius: 10px;
}

.p-inputtext:enabled:hover {
  border-color: #2b9a48;
}

.p-dropdown:not(.p-disabled):hover {
  border-color: #2b9a48;
}

.p-dropdown:not(.p-disabled):hover {
  border-color: #2b9a48;
}

.p-dropdown:not(.p-disabled).p-focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 0 0 0.1rem #179d52;
  border-color: #179d52;
  border: 1px solid #179d52 !important;
}

.p-button-success {
  background-color: #2b9a48;
  color: #ffffff;
}

.text-muted {
  color: #6c757d;
}

.p-password .p-password-show-icon,
.p-password .p-password-hide-icon {
  line-height: 1.5;
  cursor: pointer;
  right: 1.1rem !important;
}

input[type="radio"] {
  accent-color: green;
  height: 16px;
  width: 15px;
  color: #dfdfdf;
}

.custom-card-width {
  height: 470px;
  width: 362px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.p-inputtext {
  margin: 0;
  width: 362px;
  height: 56px;
  max-width: 100%;
  color: #585858;
  border-radius: 10px;
  font: 500 16px "Poppins", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #f0f4f7;
  border-color: #f0f4f7;
}
.p-calendar {
  margin: 0;
  width: 362px;
  height: 56px;
  max-width: 100%;
  color: #585858;
  border-radius: 10px;
  font: 500 16px "Poppins", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #f0f4f7;
  border-color: #f0f4f7;
}
.p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.75) !important;
  transition-property: background-color !important;
}

/* Small Devices (up to 600px) */
@media (max-width: 600px) {
  .custom-card-width {
    width: 100%;
    height: auto;
    padding: 10px;
  }

  .p-inputtext {
    width: 100%;
    height: 48px;
    font-size: 14px;
  }
}

/* Medium Devices (600px to 1024px) */
@media (min-width: 600px) and (max-width: 1024px) {
  .custom-card-width {
    width: 80%;
    height: 400px;
  }

  .p-inputtext {
    width: 100%;
    height: 52px;
  }
}

/* Large Devices (1024px and above) */
@media (min-width: 1024px) {
  .custom-card-width {
    width: 362px;
    height: 470px;
  }

  .p-inputtext {
    width: 362px;
    height: 56px;
  }
}
.p-card {
  background: #ffffff;
  color: #585858 !important;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
  border-radius: 10px !important;
}
.p-component {
  margin: 0px;
}
.p-calendar .p-calendar-month {
  background-color: #2b9a48 !important;
}

/* Custom styles for the date input field */
.customCalendar .p-inputtext {
  border-radius: 8px !important;
  background-color: #f9f9f9;
  padding: 5px 10px;
}
.p-datepicker:not(.p-datepicker-inline) {
  background: #ffffff !important;
  border: 0 none;
  border-radius: 12px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.p-datepicker table td > span.p-highlight {
  color: #ffffff !important;
  background: #ffa500 !important;
}
.p-datepicker table td > span {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  transition: box-shadow 0.2s;
  border: 1px solid transparent;
}
.p-tag {
  background: #06b6d4;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.4rem;
  border-radius: 10px !important;
  border: 1px solid #dfdfdf !important;
}
.p-dialog .p-dialog-footer {
  text-align: start !important;
}

.p-slider .p-slider-range{
  background-color: #585858 !important;
}
.p-slider .p-slider-handle {
  height: 1.143rem;
  width: 1.143rem;
  background: #ffffff;
  border: 2px solid #585858 !important;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.p-tabview .p-tabview-nav li .p-tabview-nav-link {
  border: none;
  border-width: none;
  border-color: none;
  background: #ffffff;
  color: #6c757d;
  padding: 0px;
  font-weight: 600;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  transition: box-shadow 0.2s;
  margin: 0 0 -2px 0;
}
.p-tabview .p-tabview-nav {
  background: none;
  border:none;
  border-width: none;
}

.p-rating .p-rating-item.p-focus{
  box-shadow: none !important;
}
.custom-rating .p-rating-icon {
  color: rgb(255, 165, 0) !important;
}
.p-card .p-card-content {
  padding: 0 !important;
}
.p-tabview .p-tabview-nav .p-tabview-ink-bar {
  background-color: transparent !important;
}
.p-tabview-nav-container {
  /* position: relative; */
  margin-left: 1.5rem;
}