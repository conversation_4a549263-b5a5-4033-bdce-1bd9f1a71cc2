import { useNavigate } from 'react-router-dom';

// Type for navigation options (optional)
interface NavigationOptions {
  replace?: boolean; // Replace current history entry (default: false)
  state?: any;       // State to pass to the target route
  scroll?: boolean;   // Control scroll behavior (default: true)
}

export const useNavigateTo = (): ((path: string, options?: NavigationOptions) => void) => {
  const navigate = useNavigate();

  return (path: string, options?: NavigationOptions) => {
    navigate(path, options);
  };
};
