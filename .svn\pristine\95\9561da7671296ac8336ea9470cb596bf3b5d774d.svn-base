/* job-details.module.css */

.jobDetailsContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  padding-left: 90px;
  user-select: none;
}

.jobDetailsHeader {
  text-align: center;
  margin-bottom: 30px;
}

.jobDetailsHeaderTitle {
  font-size: 36px;
  font-weight: 700;
  color: #585858;
}

.jobDetailsHeaderDescription {
  font-size: 16px;
  font-weight: 400;
  color: #585858;
  margin-top: 10px;
}

.jobDetailsMainContent {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  max-width: 60%;
  margin-bottom: 20px;
}
.jobDetailsDateLabel {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #585858;
  margin-bottom: 10px;
}

.jobDetailsUpperSection,
.jobDetailsLowerSection {
  width: 100%;
  margin-bottom: 20px;
}

.jobDetailsSectionTitle {
  font-size: 30px;
  font-weight: 700;
  color: #585858;
  margin-bottom: 15px;
}
.customCalendar {
  background-color: transparent !important;
  display: flex !important;
  flex-direction: row !important;
  height: 56px !important;
  align-items: unset !important;
  margin-top: 10px !important;
}
.customCalendar button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}
.customCalendar input {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  background-color: transparent !important;
  outline-color: unset !important;
  height: 56px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  width: min-content !important;
}
.customCalendar input:enabled:focus {
  background-color: transparent !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-left: none !important;
}
.customTime input:enabled:focus {
  background-color: transparent !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-left: none !important;
}
.customTimeSec input:enabled:focus {
  background-color: transparent !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-left: none !important;
}
.customTime {
  background-color: transparent !important;
  display: flex !important;
  flex-direction: row !important;
  height: 56px !important;
  align-items: unset !important;
  width: 100% !important;
  width: min-content !important;
  height: 56px !important;
  margin-top: 10px !important;
  border-radius: 12px !important;
}
.customTime input {
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  background-color: transparent !important;
  width: 105px !important;
  height: 56px !important;
  padding: 0px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
}
.customTime button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  padding: 20px !important;
}
.customTimeSec {
  background-color: transparent !important;
  display: flex !important;
  flex-direction: row !important;
  height: 56px !important;
  align-items: unset !important;
  width: min-content !important;
  height: 56px !important;
  margin-top: 10px !important;
}
.customTimeSec input {
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  background-color: transparent !important;
  width: 105px !important;
  height: 56px !important;
  padding: 0px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
}
.customTimeSec button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 1px solid #585858 !important;
  border-top: 1px solid #585858 !important;
  border-bottom: 1px solid #585858 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  padding: 20px !important;
}
.greenBorder input,
.greenBorder input:enabled:focus {
  border-right: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}
.greenBorder button {
  border-left: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}
.selectedBorder input,
.selectedBorder input:enabled:focus {
  border-right: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-right-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}
.selectedBorder button {
  border-left: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  padding: 8px;
}

.cancelButton {
  background-color: transparent;
  border: 1px solid #4e4e4e;
  color: #cdcdcd;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}
.maxDivider{
 padding-inline: 10px;

}
.saveButton {
  background-color: #ff9800;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.jobDetailsSectionTitleSec {
  font-size: 24px;
  font-weight: 700;
  color: #585858;
  line-height: 36px;
  margin-bottom: 20px !important;
}
.dividerPostDetails {
  margin-block: 30px !important;
}
.activeBorder {
  border: 3px solid #179d52 !important;
}
.jobDetailsSectionInstructSec {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
}

.jobDetailsTextarea {
  height: 122px;
  padding: 10px;
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  line-height: 24px;
  border: 1px solid #585858;
  border-radius: 10px;
  resize: vertical;
  width: 100%;
  padding-left: 18px;
}

.jobDetailsFooter {
  width: 100%;
  max-width: 1200px;
  margin-top: 10px;
  padding-bottom: 35px;
}

.jobDetailsDivider {
  margin: 20px 0;
}

.jobDetailsFooterButtons {
  display: flex;
  justify-content: space-between;
}

.jobDetailsFooterButtonPrev,
.jobDetailsFooterButtonNext {
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.jobDetailsFooterButtonPrev {
  display: flex;
  background-color: transparent;
  color: #585858;
  font-size: 14px;
  font-weight: 500;
  align-items: center;
}

.jobDetailsFooterButtonNext {
  background-color: #ffa500;
  color: #ffffff;
  width: 226px;
  height: 41px;
}

.jobDetailsFooterButtonNext:disabled {
  background-color: #dfdfdf;
  color: #585858;
  font-size: 14px;
  font-weight: 400;
  cursor: not-allowed;
}
.jobDetailsDateFields {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

@media (max-width: 1050px) {
  .jobDetailsDateFields {
    flex-direction: column;
  }
}

.nextDayIndicator {
  color: #fff;
  background-color: #179d52;
  width: max-content;
  border-radius: 10px;
  padding: 3px;
  padding-inline: 5px;
  position: relative;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}

.nextDayIndicator::before {
  content: "✔";
  margin-right: 5px;
  color: #ffffff;
  font-size: 1em;
}

@media (max-width: 768px) {
  .jobDetailsMainContent {
    flex-direction: column;
    align-items: center;
  }

  .jobDetailsUpperSection,
  .jobDetailsLowerSection {
    width: 100%;
    margin-bottom: 20px;
  }

  .jobDetailsFooterButtons {
    flex-direction: column;
    align-items: center;
  }

  .jobDetailsFooterButtonPrev,
  .jobDetailsFooterButtonNext {
    width: 100%;
    margin: 5px 0;
    justify-content: center;
  }
}
.saveButton:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  opacity: 0.6;
}
.errorMessageTime {
  color: #ffffff;
  background-color: rgba(255, 99, 89, 1);
  width: max-content; /* Keep max-content for large screens */
  max-width: 100%; /* Add this to make it responsive */
  border-radius: 10px;
  padding: 3px;
  padding-inline: 5px;
  position: relative;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  word-wrap: break-word; /* Wrap long words */
  white-space: normal; /* Allow text to break into multiple lines */
  text-wrap: wrap; /* Optional for better support in modern browsers */
}

.errorMessageTime::before {
  content: "✔";
  margin-right: 5px;
  color: #ffffff;
  font-size: 1em;
}
.jobDetailsContainerMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
}
.customCalendarMobile {
  background-color: transparent !important;
  display: flex !important;
  flex-direction: row !important;
  height: 56px !important;
  align-items: unset !important;
  margin-top: 10px !important;
  width: 100% !important;
}
.customCalendarMobile button {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  color: #585858 !important;
  box-shadow: none !important;
  border-left: 2px solid #585858 !important;
  border-top: 2px solid #585858 !important;
  border-bottom: 2px solid #585858 !important;
  border-top-left-radius: 30px !important;
  border-bottom-left-radius: 30px !important;
}
.customCalendarMobile input {
  border: none !important;
  border-radius: 0 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  border-right: 2px solid #585858 !important;
  border-top: 2px solid #585858 !important;
  border-bottom: 2px solid #585858 !important;
  border-top-right-radius: 30px !important;
  border-bottom-right-radius: 30px !important;
  background-color: transparent !important;
  outline-color: unset !important;
  height: 56px !important;
  font-weight: 400 !important;
  font-size: 16px !important;

}
.customCalendarMobile input:enabled:focus {
  background-color: transparent !important;
  box-shadow: none !important;
  border-right: 2px solid #585858 !important;
  border-top: 2px solid #585858 !important;
  border-bottom: 2px solid #585858 !important;
  border-left: none !important;
  border-top-right-radius: 30px !important;
  border-bottom-right-radius: 30px !important;
}
.jobDetailsSectionTitleMobile {
  font-size: 22px;
  font-weight: 700;
  color: #585858;
  margin-bottom: 0px;
  margin-top: 0px;
  margin-left: 10px;
  padding: 15px;
  padding-top: 20px;
}
.selectedBorderMobile input,
.selectedBorderMobile input:enabled:focus {
  border-right: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-right-radius: 30px !important; /* Changed from 10px to 30px */
  border-bottom-right-radius: 30px !important; /* Changed from 10px to 30px */
}

.selectedBorderMobile button {
  border-left: 3px solid #179d52 !important;
  border-top: 3px solid #179d52 !important;
  border-bottom: 3px solid #179d52 !important;
  border-top-left-radius: 30px !important; /* Changed from 10px to 30px */
  border-bottom-left-radius: 30px !important; /* Changed from 10px to 30px */
}
.selectedBorderMobile img {
  filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg)
    brightness(90%) contrast(80%);
}

.selectedBorderMobile button img {
  filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg)
    brightness(90%) contrast(80%);
}
.selectedBorderMobile input {
  color: #179d52 !important; /* Green color for selected value */
}

/* Initial container styling */
.pickerContainer {
  border: 1px solid #dfdfdf; /* Light grey border */
  border-radius: 10px;
  width: 100%;
}

/* Highlighted container when End Time is selected */
.pickerContainerSelected {
  border: 2px solid #179d52; /* Green border */
}

/* Start and End Time div styling */
.startTimeMobile,
.endTimeMobile {
  display: flex;
  justify-content: center;
  flex-direction: row;
  gap: 120px;
  align-items: baseline;
  padding: 10px;
  width: 100%;
}
@media (max-width: 750px) {
  .startTimeMobile,
  .endTimeMobile {
    gap: 40px; /* Reduce the gap for smaller screens */
  }
}

/* Start Time: initial state with light grey bottom border */
.startTimeMobile {
  border-bottom: 1px solid #dfdfdf;
}

/* Start Time: all borders when Start Time is selected */
.selectedTime {
  border: 2px solid #179d52; /* Green border */
  border-radius: 10px;
}

/* Start Time: only bottom border when End Time is selected */
.startTimeWithEndSelected {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 2px solid #179d52; /* Green bottom border */
  border-radius: 0 0 10px 10px;
}

/* End Time: initial state with no border */
.endTimeMobile {
  border-top: none;
}

/* End Time: all borders when End Time is selected */
.selectedTimeEnd {
  border: 2px solid #179d52; /* Green border */
  border-radius: 10px;
}

/* End Time: only top border when Start Time is selected */
.endTimeWithStartSelected {
  border-left: none;
  border-right: none;
  border-bottom: none;
  border-radius: 10px 10px 0 0;
}
.checkIcon {
  width: 16px; /* Adjust size as needed */
  height: 16px;
  border-radius: 50%; /* Makes the div circular */
  background-color: #28a745; /* Green background */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 11px;
}

.checkIcon::after {
  content: "✓"; /* Unicode for checkmark */
  color: white;
  font-size: 14px; /* Adjust size as needed */
  font-weight: bold;
}
.totalDuration {
  background-color: #179d52;
  border-radius: 20px;
  padding-inline: 20px;
  padding-block: 10px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
}
.duration {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
}
.jobDetailsFooterMobile {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff; /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow for better visibility */
  padding: 10px 0; /* Padding for the button */
  text-align: center;
  z-index: 999; /* Ensure it stays on top of other elements */
  margin-top: auto;
}
.nextButtonMobile {
  padding: 10px 20px;
  width: 80%; /* Full width */
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #ffa500; /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Style for disabled button */
.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
}

/* Optional: If you want the buttons to be stacked vertically */
.MobileFooter button {
  margin-bottom: 10px; /* Adds spacing between buttons */
}
.jobDetailsDateFieldsMobile {
  gap: 20px;
  padding-inline: 20px;
  flex-direction: column !important;
}
.jobDescriptionContainerMobile {
  display: flex;
  flex-direction: column;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
  background-color: #fff;
  position: relative;
}
.jobDescriptionTitleMobile{
  color: #585858;
  font-size: 22px;
  font-weight: 700;
  margin: 0px;
}
.jobDetailsTextareaMobile {
  height: 278px;
  padding: 10px;
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  line-height: 24px;
  border: 1px solid #585858;
  border-radius: 10px;
  resize: vertical;
  width: 100%;
  padding-left: 18px;
}
.jobDetailsUpperSectionMobile{
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  /* align-items: center;
  padding-inline: 50px; */
}
.jobDetailsMainContentMobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
}
.jobDetailsMainMobile{
  padding-top: 20px;
  padding-inline: 20px;
}
.ButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}