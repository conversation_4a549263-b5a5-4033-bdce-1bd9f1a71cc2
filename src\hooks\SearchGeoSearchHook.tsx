import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store';
import { SearchFilters } from '../store/types';
import { updateSearchFilter } from '../store/slices/applicationSlice';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Service from '../services/services';
import utils from '../components/utils/util';

interface UserSearchResponse {
    results: UserResult[];
    total: number;
}

interface UserResult {
    metadata: UserMetadata;
    id: number;
    requestId:number;
    jobsCompleted: number;
    jobsCancelled: number;
    ratingsCount: number;
    ratingsAvg: number;
    firstName: string;
    publicName: string;
    distanceInKm: number;
    imageSrc: string;
    aboutMe25: string | null;
    actionRequired: boolean;
    friendStatus: number;
    suburb: string;
    state: string;
    lastSeenInDays: number;
    visibilityLimit: number;
    invitationDate: string | null;
    accountSubtype: number;
    jugglerTypes: number;
    wwcc: boolean;
    spokenLanguages: string[] | null;
    nationality: string | null;
}

interface UserMetadata {
    ageInYears: number;
    isSuperProvider: boolean;
    hasVideo: boolean;
    certificates: UserCertificates;
    ratingsCount: number;
    reviewsCount: number;
    jobsForMe: number;
    tutoringCategory: number;
    interestedInFaceToFaceTutoring: boolean;
    interestedInOnlineTutoring: boolean;
    hasSpecialNeedsExperience: boolean;
    hasDrivingLicence: boolean;
    hasOwnCar: boolean;
    languages: number[];
    qualifications: number[];
    firstAid: number[];
    primarySchoolYears: number[];
    highSchoolYears: number[];
    primarySchoolSubjects: number[];
    highSchoolSubjects: number[];
    interestedInJobTypes: number;
    responseTime: number;
    responseRate: number;
    nationality: string;
    auPairCategory: number;
    year12GraduationYear: number | null;
    canTeachStudySkills: boolean | null;
    canTeachExamPreparation: boolean | null;
    spokenLanguages: string[] | null;

}

interface UserCertificates {
    wwcc: boolean;
}

function useSearchHook(onUpdate: (data: UserSearchResponse) => void) {
    const filters = useSelector((state: RootState) => state.applicationState.filters);
    const dispatch = useDispatch<AppDispatch>();

    const updateFilters = (modifyFilters: (prevFilters: SearchFilters) => SearchFilters) => {
        const newFilters = modifyFilters(filters);
        dispatch(updateSearchFilter(newFilters));
        fetchConnections(newFilters);
    };

    const fetchConnections = utils.debounce((filters: SearchFilters) => {
        Service.getConnections(
            (data: UserSearchResponse) => onUpdate(data),
            (error: any) => {
                console.error('Error fetching connections:', error);
            },
            filters
        );
    }, 300);

    function refreshSearchResult() {
        Service.getConnections(
            (data: UserSearchResponse) => onUpdate(data),
            (error: any) => {
                console.error('Error fetching connections:', error);
            },
            filters
        );
    }

    return { updateFilters, refreshSearchResult };
}

interface GeoSearchFeature {
    properties: {
        canConnect: boolean;
        isCenterPoint: boolean;
        featureType: number;
        metadata: {
            ageInYears: number | null;
            isSuperProvider: boolean | null;
            hasVideo: boolean | null;
            certificates: {
                wwcc: boolean | null;
            };
            ratingsCount: number;
            reviewsCount: number | null;
            jobsForMe: number | null;
            tutoringCategory: number | null;
            interestedInFaceToFaceTutoring: boolean | null;
            interestedInOnlineTutoring: boolean | null;
            hasSpecialNeedsExperience: boolean | null;
            hasDrivingLicence: boolean | null;
            hasOwnCar: boolean | null;
            languages: string[] | null;
            qualifications: string[] | null;
            firstAid: number[] | null;
            primarySchoolYears: number[] | null;
            highSchoolYears: number[] | null;
            primarySchoolSubjects: string[] | null;
            highSchoolSubjects: string[] | null;
            interestedInJobTypes: number | null;
            responseTime: number | null;
            responseRate: number | null;
            nationality: string | null;
            auPairCategory: number | null;
            year12GraduationYear: number | null;
            canTeachStudySkills: boolean | null;
            canTeachExamPreparation: boolean | null;
        } | null;
        id: number;
        requestId: number;
        jobsCompleted: number;
        jobsCancelled: number;
        ratingsCount: number;
        ratingsAvg: number;
        firstName: string;
        publicName: string;
        distanceInKm: number | null;
        imageSrc: string;
        aboutMe25: string | null;
        actionRequired: boolean;
        friendStatus: number;
        suburb: string;
        state: string;
        lastSeenInDays: number;
        visibilityLimit: number;
        invitationDate: string | null;
        accountSubtype: number;
        jugglerTypes: number;
    };
    geometry: {
        coordinates: [number, number];
    };
}

interface GeoSearchResponse {
    features: GeoSearchFeature[];
}

function useGeoSearchHook(onUpdate: (data: GeoSearchFeature[]) => void) {
    const filters = useSelector((state: RootState) => state.applicationState.filters);
    const [bounds, setBounds] = useState<number[] | null>(null);
    const previousFiltersRef = useRef<SearchFilters | null>(null);
    const previousBoundsRef = useRef<number[] | null>(null);

    const fetchGeoSearch = useCallback(
        utils.debounce((filters: SearchFilters, bounds: number[]) => {
            const payload: SearchFilters = {
                ...filters,
                pageIndex: 1,
                pageSize: 1000,
                filters: [
                    ...filters.filters,
                    { field: 'geoBounds', operator: 'eq', value: bounds },
                ],
            };
            Service.geoSearch(
                (data: GeoSearchResponse) => {
                    onUpdate(data.features);
                },
                (error: any) => {
                    console.error('Error fetching connections:', error);
                },
                payload
            );
        }, 300),
        [onUpdate]
    );

    useEffect(() => {
        if (
            bounds &&
            (JSON.stringify(bounds) !== JSON.stringify(previousBoundsRef.current) ||
                JSON.stringify(filters) !== JSON.stringify(previousFiltersRef.current))
        ) {
            fetchGeoSearch(filters, bounds);
            previousBoundsRef.current = bounds;
            previousFiltersRef.current = filters;
        }
    }, [bounds, filters, fetchGeoSearch]);

    return { setBounds };
}

export { useSearchHook, useGeoSearchHook };
export type { UserSearchResponse, UserResult, GeoSearchFeature };
