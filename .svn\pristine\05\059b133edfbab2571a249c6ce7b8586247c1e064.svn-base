.horizontalNav {
    position: fixed;
    top: 0;
    background-color: #227C3C;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    width: 100%;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
}

.navContainer {
    display: flex;
    align-items: center;
    width: 100%;
 
}

.navItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 8px 16px;
    transition: all 0.2s ease;
    width: 33.33%;
    padding-bottom: 0px;
}

.navItem:hover {
    color: white;
}

.active {
    color: white;
    font-weight: 600;
}
.activeLine {
    width: 100%;
    height: 3px;
    background-color: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    transition: all 0.3s ease;
}

.navIcon {
    font-size: 1.00rem;
    margin-bottom: 4px;
}

.navText {
    font-size: 0.875rem;
    white-space: nowrap;
}
.headerGradient {
    background-color: #179D52;
    height: 55px;
    font-size: 20px;
    color: white;
    font-weight: 700;
  }
  
  .juggleLogo {
    object-fit: contain;
    width: 173px;
    height: 36px;
    margin-left: 50px;
    cursor: pointer;
  }
@media (min-width: 768px) {

    .navIcon {
        font-size: 1.25rem;
    }

    .navText {
        font-size: 1rem;
    }
}

.native {
    margin-top: var(--safe-area-top);
}