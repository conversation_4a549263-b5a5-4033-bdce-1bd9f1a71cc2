import { useState } from "react";
import { useNavigateTo } from "../../../helper/navigation";
import ForgotPasswordForm from "./ForgotPasswordForm";
import Loader from "../../../commonComponents/Loader";
import Auth from "../../../services/authService";
import "../../utils/util.css";
import useLoader from "../../../hooks/LoaderHook";

const ForgotPassword: React.FC = () => {
  const [formError, setFormError] = useState<string | null>(null);
  const navigateTo = useNavigateTo();
  const{disableLoader,enableLoader}=useLoader()

  const handleForgotPassword = (accountIdentifier: string) => {
    setFormError(null);
    enableLoader();

    Auth.forgotPassword(
      accountIdentifier,
      () => {
        disableLoader()
        redirectAfterResetPass();
      },
      () => {
        disableLoader()
        setFormError("An account with the specified details was not found. ");
      }
    );
  };

  const redirectAfterResetPass = () => {
    navigateTo("/reset-password", { replace: true });
  };

  return (
    <div className="form-container">
      <ForgotPasswordForm
        onSubmit={handleForgotPassword}
        formError={formError}
      />
    </div>
  );
};

export default ForgotPassword;
