# Time Editing Implementation for TimesheetDetailsPopup

## Overview
This document explains the implementation of AM/PM time display and editing functionality in the TimesheetDetailsPopup component with automatic hours and total calculation.

## Key Features Implemented

### 1. **AM/PM Time Display**
- ✅ Converts 24-hour format to 12-hour AM/PM format
- ✅ Displays times in user-friendly format (e.g., "9:00 AM", "5:00 PM")
- ✅ Handles edge cases (midnight, noon)

### 2. **Editable Times with Real-time Calculation**
- ✅ State management for editable start and end times
- ✅ Automatic recalculation when times change
- ✅ Updates hours worked and total price dynamically

### 3. **Enhanced Time Conversion Functions**

#### **`convertTo24Hour(timeStr: string): string`**
```typescript
// Converts "9:00 AM" → "09:00"
// Converts "5:00 PM" → "17:00"
const convertTo24Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [time, modifier] = timeStr.split(" ");
  if (!time || !modifier) return timeStr;
  let [hours, minutes] = time.split(":").map(Number);

  if (modifier.toUpperCase() === "PM" && hours < 12) {
    hours += 12;
  }
  if (modifier.toUpperCase() === "AM" && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};
```

#### **`convertTo12Hour(timeStr: string): string`**
```typescript
// Converts "09:00" → "9:00 AM"
// Converts "17:00" → "5:00 PM"
const convertTo12Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [hours, minutes] = timeStr.split(":").map(Number);
  
  if (isNaN(hours) || isNaN(minutes)) return timeStr;
  
  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours % 12 || 12;
  
  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};
```

#### **`formatTimeForDisplay(timeStr: string): string`**
```typescript
// Smart formatter that handles both formats
const formatTimeForDisplay = (timeStr: string): string => {
  if (!timeStr) return '';
  
  // If already in AM/PM format, return as is
  if (timeStr.includes('AM') || timeStr.includes('PM') || 
      timeStr.includes('am') || timeStr.includes('pm')) {
    return timeStr;
  }
  
  // If in 24-hour format, convert to 12-hour
  return convertTo12Hour(timeStr);
};
```

## Component State Management

### **TimesheetDetailsPopup State**
```typescript
const [editableStartTime, setEditableStartTime] = useState('');
const [editableEndTime, setEditableEndTime] = useState('');
const [rate, setRate] = useState(0);

// Initialize state when timesheetDetails changes
useEffect(() => {
  if (timesheetDetails) {
    const startTimeFormatted = formatTimeForDisplay(timesheetDetails.jobStartTime || '');
    const endTimeFormatted = formatTimeForDisplay(timesheetDetails.jobEndTime || '');
    
    setEditableStartTime(startTimeFormatted);
    setEditableEndTime(endTimeFormatted);
    setRate(Number(timesheetDetails.price) || 0);
  }
}, [timesheetDetails]);
```

### **Real-time Calculation**
```typescript
// Calculate hours and total based on current editable times
const startTime24 = convertTo24Hour(editableStartTime);
const endTime24 = convertTo24Hour(editableEndTime);
const hoursWorked = calculateHours(startTime24, endTime24);
const totalPrice = calculateTotalPrice(hoursWorked, rate);
```

## Enhanced AwaitingConfirmationCard

### **Time Calculation Function**
```typescript
const calculateHoursFromTimes = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const convertToMinutes = (timeStr: string): number => {
    const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})\s*(am|pm)/i);
    if (!timeMatch) return 0;

    let hours = parseInt(timeMatch[1], 10);
    const minutes = parseInt(timeMatch[2], 10);
    const period = timeMatch[3].toLowerCase();

    if (period === 'pm' && hours < 12) hours += 12;
    if (period === 'am' && hours === 12) hours = 0;

    return hours * 60 + minutes;
  };

  const startMinutes = convertToMinutes(startTime);
  const endMinutes = convertToMinutes(endTime);
  
  if (startMinutes === 0 || endMinutes === 0) return 0;
  
  const diffMinutes = endMinutes - startMinutes;
  return parseFloat((diffMinutes / 60).toFixed(2));
};
```

### **Automatic Recalculation on Edit**
```typescript
const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
  // ... existing logic ...
  
  // Recalculate hours and totals for the new/changed rows
  const recalculatedRows = newRows.map(row => {
    if (!row.isOriginal) {
      const hours = calculateHoursFromTimes(row.start, row.finish);
      const total = hours * baseRate;
      return {
        ...row,
        hours,
        rate: baseRate,
        total
      };
    }
    return row;
  });

  setTimesheetRows([
    ...previousOriginalRows,
    ...recalculatedRows
  ]);
};
```

## Usage Examples

### **Time Format Handling**
```typescript
// Input: "09:00" (24-hour) → Output: "9:00 AM" (12-hour)
// Input: "17:00" (24-hour) → Output: "5:00 PM" (12-hour)
// Input: "9:00 AM" (already 12-hour) → Output: "9:00 AM" (unchanged)

const displayTime = formatTimeForDisplay(timesheetDetails.jobStartTime);
```

### **Calculation Examples**
```typescript
// Example 1: 9:00 AM to 5:00 PM = 8 hours
startTime: "9:00 AM"
endTime: "5:00 PM"
hours: 8
total: 8 * $25 = $200

// Example 2: 12:00 AM to 12:00 PM = 12 hours
startTime: "12:00 AM" (midnight)
endTime: "12:00 PM" (noon)
hours: 12
total: 12 * $25 = $300
```

## Benefits

1. **User-Friendly Display**: Times shown in familiar AM/PM format
2. **Real-time Updates**: Hours and totals recalculate automatically
3. **Flexible Input**: Handles both 24-hour and 12-hour input formats
4. **Accurate Calculations**: Proper handling of edge cases (midnight, noon)
5. **State Persistence**: Edited times are maintained throughout the session

## Testing

Comprehensive tests cover:
- ✅ AM/PM format display
- ✅ Hours calculation accuracy
- ✅ Total price calculation
- ✅ 24-hour to 12-hour conversion
- ✅ Edge cases (midnight, noon)
- ✅ Real-time recalculation

## Future Enhancements

1. **Cross-day Support**: Handle shifts that span midnight
2. **Time Zone Support**: Handle different time zones
3. **Validation**: Add time format validation
4. **Accessibility**: Improve screen reader support
5. **Mobile Optimization**: Better touch interface for time editing

This implementation provides a robust, user-friendly time editing experience with automatic calculations and proper AM/PM formatting.
