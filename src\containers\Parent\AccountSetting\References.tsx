import { useState } from 'react';
import CustomButton from '../../../commonComponents/CustomButton';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaPlus } from 'react-icons/fa6';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import useLoader from '../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import { fetchNoRefreshSessionInfo, updateSessionInfo, updateUser } from '../../../store/tunks/sessionInfoTunk';
import { Card } from 'primereact/card';
import editIcon from "../../../assets/images/Icons/editIcon.png";
import removeIcon from "../../../assets/images/Icons/remove.png";
import { ConfirmationPopupRed, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import useIsMobile from '../../../hooks/useIsMobile';
interface ReferencesProps {
    onChange: () => void;
}

const References: React.FC<ReferencesProps> = ({ onChange }) => {
    const session = useSelector((state: RootState) => state.sessionInfo.data);
    const [showText, setShowText] = useState(false);
    const [checkedState, setCheckedState] = useState<boolean[]>(new Array(1).fill(false));
    const [showDialog, setShowDialog] = useState(false);
    const [firstName, setFirstName] = useState("");
    const [lastName, setLastName] = useState("");
    const [phoneNumber, setAccountIdentifier] = useState("");
    const [email, setAccountEmail] = useState("");
    const [accountEmailError, setAccountEmailError] = useState("");
    const [_, setSuccessMessage] = useState("");
    const [successMessageEmail, setSuccessMessageEmail] = useState("");
    const [firstNameError, setFirstNameError] = useState("");
    const [lastNameError, setLastNameError] = useState("");
    const [mobileError, setmobileError] = useState("");
    const [referenceTypeError, setReferenceTypeError] = useState("");
    const [selectedOption, setSelectedOption] = useState(null);
    const { disableLoader, enableLoader } = useLoader();
    const [selectedReferenceId, setSelectedReferenceId] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const {isMobile}=useIsMobile()
    const dispatch = useDispatch<AppDispatch>();
    const options = [
        { text: 'Au Pair', payload: '1' },
        { text: 'Other childcare employment', payload: '2' },
        { text: 'Tutoring', payload: '3' },
        { text: 'Academic', payload: '5' },
        { text: 'Sporting', payload: '6' },
        { text: 'Other - please specify', payload: '4' }
    ];

    const handleAddReference = () => {
        setShowText(true);
        setSelectedReferenceId(null);
        setFirstName("");
        setLastName("");
        setAccountEmail("");
        setAccountIdentifier("");
        setSelectedOption(null);
        setCheckedState([false]);
    };
    const handleCheckboxChange = (index: number) => {
        const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
        setCheckedState(updatedCheckedState);

        if (updatedCheckedState[index]) {
            setShowDialog(true);
            setShowText(false);
        }
    };


    const handleDropdownChange = (e) => {
        setSelectedOption(e.value);
    };
    const handleRemoveReference = (referenceId) => {
        showConfirmationPopup(
            "Remove Reference",
            "Are you sure you want to remove this reference?",
            "Remove",
            <img
                src={removeIcon}
                alt="remove icon"
                style={{ height: "15px", width: "13.33px" }}
            />,
            () => {
                const updatedReference = session["vouches"].filter(
                    (vouch) => vouch.id !== referenceId
                );

                const payload = {
                    ...session,
                    vouches: updatedReference,
                };
                enableLoader();

                dispatch(updateUser({ payload })).finally(() => {
                    setSuccessMessage("Changes successfully made!");
                    disableLoader();
                });
            }
        );
    };

    const handleSave = () => {
        if (!firstName) {
            setFirstNameError("Please enter your first name.");
            return;
        } else if (!lastName) {
            setLastNameError("Please enter your last name.");
            return;
        } else if (!phoneNumber) {
            setmobileError("Please enter your phone number.");
            return;
        } else if (!email) {
            setAccountEmailError("Please enter your email.");
            return;
        } else if (!selectedOption) {
            setReferenceTypeError("required");
            return;
        } else {
            setFirstNameError("");
        }
        enableLoader();
        const updatedReferences = session["vouches"].map((vouch) => {
            if (vouch.id === selectedReferenceId) {
                return {
                    ...vouch,
                    firstName: firstName,
                    lastName: lastName,
                    phoneNumber: phoneNumber,
                    email: email,
                    referenceType: selectedOption.payload
                };
            }
            return vouch;
        });
        const payload = {
            ...(session as object),
            vouches:
                selectedReferenceId !== null
                    ? updatedReferences
                    : [
                        ...session["vouches"],
                        {
                            firstName: firstName,
                            lastName: lastName,
                            phoneNumber: phoneNumber,
                            email: email,
                            referenceType: selectedOption.payload,
                        },
                    ],
        };
        dispatch(updateSessionInfo({ payload })).finally(() => {
            setSuccessMessage(
                selectedReferenceId !== null
                    ? "Changes successfully made!"
                    : "Changes successfully made!"
            );
            dispatch(fetchNoRefreshSessionInfo());
            // setFirstName("");
            // setLastName("");
            // setAccountEmail("");
            // setAccountIdentifier("");
            // setSelectedOption(null);
            setShowDialog(false);
            disableLoader();
        });
    };

    const handleEditReference = (vouches) => {
        setSelectedReferenceId(vouches.id);
        setFirstName(vouches.firstName);
        setLastName(vouches.lastName);
        setAccountEmail(vouches.email);
        setAccountIdentifier(vouches.phoneNumber);
        setSelectedOption(options.find((option) => Number(option.payload) === vouches.referenceType));
        setShowDialog(true);
    };
    return (
        <div className='flex flex-column p-4' style={{ color: '#585858' }}>
            <ConfirmationPopupRed confirmationProps={confirmationProps} />
            <header className={styles.utilheader}>
                <h1  style={{fontSize:isMobile && "24px"}} className="p-0 m-0">References</h1>
            </header>
            <div>
                {/* {!showProfileStrength ? (
                    <div
                        className="flex items-center justify-between mb-4"
                        style={{ width: '100%' }}
                    >
                        <div
                            className="flex gap-2 cursor-pointer align-items-center"
                            style={{
                                border: '1px solid #F1F1F1',
                                padding: '10px 25px',
                                borderRadius: '20px',
                            }}
                            onClick={() => setShowProfileStrength(true)}
                        >
                            <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
                            <p
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '400',
                                    fontSize: '14px',
                                    color: '#585858',
                                }}
                            >
                                Go Back
                            </p>
                        </div>
                        <h1
                            className="m-0 p-0"
                            style={{
                                fontWeight: '800',
                                fontSize: '24px',
                                color: '#585858',
                                textAlign: 'center',
                                flex: 1,
                            }}
                        >
                            References
                        </h1>
                        <div style={{ width: '75px' }}></div>
                    </div>
                ) : (
                    <ProfileStrength onBack={() => setShowProfileStrength(false)} />
                )} */}
            </div>
            <span>
                Adding references will enhance your Juggle St profile.
            </span>
            <span>
                Juggle Street will give your referee details to potential employers upon request.
            </span>
            {session["vouches"].map((vouch, index) => (
                <Card key={index} className={`${styles.referenceCard} mt-3`} style={{ padding: 0 }}>
                    <div><strong>Name:&nbsp;</strong>
                        {vouch.firstName} {vouch.lastName}</div>
                    <div className='mt-1'><strong>Contact Details</strong>
                        <div className='flex '>
                            <strong> Phone:&nbsp;</strong>{vouch.phoneNumber}
                        </div>
                        <div>
                            <strong>Email:&nbsp;</strong>{vouch.email}
                        </div>
                    </div>
                    <div className='flex justify-between gap-2 mt-3'>
                        <button
                            className={styles.editBtn}
                            onClick={() =>
                                handleEditReference(vouch)
                            }
                        >
                            <img
                                src={editIcon}
                                alt="Edit"
                                style={{ marginRight: "5px", width: "13px", height: "13px" }}
                            />
                            Edit
                        </button>
                        <button
                            className={styles.removeBtn}
                            onClick={() => handleRemoveReference(vouch.id)}
                        >
                            Remove
                        </button>
                    </div>
                </Card>
            ))}
            <CustomButton
                label={
                    <>
                        <FaPlus size={18} /> Add
                    </>
                }
                className={styles.customButton}
                style={{ margin: "0", width: "100px", marginTop: "1rem" }}
                onClick={handleAddReference}
                disabled={showText && !checkedState.includes(true)}
            />
            <Dialog
                header={isEditMode ? "Edit Reference" : "Add Reference"}
                visible={showDialog}
                style={{ backgroundColor: '#fff', borderRadius: '33px' }}
                footer={
                    <div>
                        <CustomButton label="Save" onClick={handleSave} style={{ width: '100px' }} />
                    </div>
                }
                onHide={() => {
                    handleAddReference();
                    setShowDialog(false);
                }}
                draggable={false}
            >
                <div className={styles.contentContainer}>
                    <div className={styles.aboutMeSection}>
                        {/* First Name Input */}
                        <div
                            className="input-container"
                            style={{ marginTop: "8px", maxWidth: "100%" }}
                        >
                            <InputText
                                id="firstName"
                                name="firstName"
                                value={firstName}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (/^[A-Za-z\s]*$/.test(value)) {
                                        setFirstName(value);
                                        if (firstNameError) {
                                            setFirstNameError("");
                                        }
                                    }
                                }}
                                placeholder=""
                                className={`input-placeholder ${firstNameError && !firstName ? "firstNameError" : ""}`}
                                style={{ borderWidth: "1px", cursor: "auto" }}
                            />
                            <label
                                htmlFor="firstName"
                                className={`label-name ${firstName ? "label-float" : ""} ${firstNameError && !firstName ? "input-error" : ""}`}
                            >
                                {firstNameError && !firstName ? firstNameError : "First Name*"}
                            </label>
                        </div>


                        {/* Last Name Input */}
                        <div
                            className="input-container"
                            style={{ marginTop: "25px", maxWidth: "100%" }}
                        >
                            <InputText
                                id="lastName"
                                name="lastName"
                                value={lastName}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (/^[A-Za-z\s]*$/.test(value)) {
                                        setLastName(value);
                                        if (lastNameError) {
                                            setLastNameError("");
                                        }
                                    }
                                }}
                                placeholder=""
                                className={`input-placeholder ${lastNameError ? "lastNameError" : ""
                                    }`}
                                style={{ borderWidth: "1px", cursor: "auto" }}
                            />
                            <label
                                htmlFor="lastName*"
                                className={`label-name ${lastName || lastNameError ? "label-float" : ""
                                    } ${lastNameError ? "input-error" : ""}`}
                            >
                                {lastNameError && !lastName ? lastNameError : "Last Name*"}
                            </label>
                        </div>

                        {/* Mobile Number Input */}
                        <div
                            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
                        >
                            <div
                                className="input-container"
                                style={{ marginTop: "25px", maxWidth: "100%" }}
                            >
                                <InputText
                                    id="mobile"
                                    name="mobile"
                                    value={phoneNumber}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (/^\d*$/.test(value) && value.length <= 15) {
                                            setAccountIdentifier(value);
                                            if (mobileError) {
                                                setmobileError("");
                                            }
                                        }
                                    }}
                                    onFocus={() => {
                                        setSuccessMessage(""); // Clear success message on focus
                                    }}
                                    placeholder=""
                                    className={`input-placeholder ${phoneNumber ? "mobileError" : ""
                                        }`}
                                    style={{ borderWidth: "1px", cursor: "auto" }}
                                />
                                <label
                                    htmlFor="mobile"
                                    className={`label-name ${mobileError || mobileError
                                        ? "label-float"
                                        : ""
                                        } ${mobileError ? "input-error" : ""}`}
                                >
                                    {mobileError && !phoneNumber
                                        ? mobileError
                                        : "Mobile*"}
                                </label>
                            </div>
                        </div>
                        <div
                            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
                        >
                            <div
                                className="input-container"
                                style={{ marginTop: "25px", maxWidth: "100%" }}
                            >
                                <InputText
                                    id="email"
                                    name="email"
                                    value={email}
                                    onChange={(e) => setAccountEmail(e.target.value)}
                                    placeholder=""
                                    className={`input-placeholder ${accountEmailError ? "emailInputError" : ""
                                        }`}
                                    style={{
                                        borderColor: accountEmailError ? "red" : "",
                                        borderWidth: "1px",
                                    }}
                                    onFocus={() => {
                                        setAccountEmailError("");
                                        setSuccessMessageEmail("");
                                    }}
                                />
                                <label
                                    htmlFor="username"
                                    className={`label-name ${email || accountEmailError ? "label-float" : ""
                                        } ${accountEmailError ? "input-error" : ""}`}
                                >
                                    {accountEmailError && !email
                                        ? accountEmailError
                                        : "Email*"}
                                </label>
                            </div>
                        </div>
                        {successMessageEmail && (
                            <div className={styles.successMobile}>
                                <div
                                    style={{
                                        backgroundColor: "#FFFFFF",
                                        borderRadius: "50%",
                                        color: "#179D52",
                                        padding: "5px",
                                        height: "16px",
                                        width: "16px",
                                        display: "inline-flex",
                                        alignItems: "center",
                                        marginRight: "3px",
                                    }}
                                >
                                    <FaCheck />
                                </div>
                                {successMessageEmail}
                            </div>
                        )}
                        <div
                            className="input-container"
                            style={{ marginTop: "25px", maxWidth: "100%" }}
                        >
                            <Dropdown
                                value={selectedOption}
                                options={options}
                                onChange={(e) => {
                                    handleDropdownChange(e);
                                    if (e.value) {
                                        setReferenceTypeError("");
                                    }
                                }}
                                optionLabel="text"
                                placeholder="Select a reference type"
                                // className={`w-full ${referenceTypeError ? "dropdown-error" : ""}`}
                                className={styles.referenceType}
                            />
                            <span className="error-message">
                                {referenceTypeError && !selectedOption && (
                                    <span style={{ color: "red", fontWeight: 500 }}>{referenceTypeError}</span>
                                )}
                            </span>
                        </div>
                    </div>
                </div>
            </Dialog>
            {showText && (
                <>
                    <p style={{ marginTop: "10px" }}>
                        By ticking the box below you acknowledge and agree with the following:
                    </p>
                    <ul style={{ marginLeft: "20px" }}>
                        <li>This Reference is independent, i.e., not a member of your family, not your partner, etc.</li>
                        <li>Information provided is true and correct.</li>
                        <li>You give Juggle Street permission to provide referee details to other registered Juggle Street users.</li>
                    </ul>
                    {checkedState.map((item, index) => (
                        <label key={index} className="flex items-center gap-2 cursor-pointer">
                            <input
                                type="checkbox"
                                className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                checked={item}
                                onChange={() => handleCheckboxChange(index)}
                                style={{ fontSize: "18px" }}
                            />
                            <span>Tick to acknowledge and agree</span>
                        </label>
                    ))}
                </>
            )}
            <div className='mt-3'>
                <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                People you work for on Juggle St will be asked to provide a 5 Star Rating and a written recommendation and these are automatically uploaded to your profile.<br />
                Providing a referee enables additional, external reference checking.
            </div>
        </div>
    )
}
export default References;