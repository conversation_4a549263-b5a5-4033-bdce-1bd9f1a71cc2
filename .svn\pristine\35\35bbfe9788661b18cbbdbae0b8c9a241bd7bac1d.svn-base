import React from 'react'

import { Jobs, MyJobSectionProps, WeeklySchedule } from '../../../Common/manageJobs/types';
import NoJobsCard from '../../../Common/manageJobs/Common/NoJobsCard';
import c from '../../../../helper/juggleStreetConstants';
import JobCardHelper from '../../../Common/manageJobs/Common/jobCardHelper';
import useIsMobile from '../../../../hooks/useIsMobile';

const UpcomingJobs: React.FC<MyJobSectionProps> = ({
  upComingJobs,
  viewJob,
}) => {
  const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };

  const formatJobDate = (jobDate: string | Date | null) => {
    if (!jobDate) {
      return null;
    }

    const dateObj = new Date(jobDate);
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthsOfYear = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Extract the day of the week
    const day = daysOfWeek[dateObj.getDay()];

    // Extract the date with suffix
    const date = dateObj.getDate();
    const twoDigitDate = date < 10 ? `0${date}` : `${date}`; // Ensure two-digit format
    const suffix =
      date % 10 === 1 && date !== 11
        ? "st"
        : date % 10 === 2 && date !== 12
          ? "nd"
          : date % 10 === 3 && date !== 13
            ? "rd"
            : "th";

    // Extract the short month
    const month = monthsOfYear[dateObj.getMonth()];
    const year = dateObj.getFullYear().toString().slice(-2);;
    // Calculate months difference from today
    const today = new Date();
    const monthsDifference =
      (dateObj.getFullYear() - today.getFullYear()) * 12 +
      (dateObj.getMonth() - today.getMonth());

    return {
      day,
      date: `${twoDigitDate}${suffix}`, // Add suffix to two-digit date
      month,
      year,
      monthsDifference,
    };
  };

  const getUnawardedJob = () => {
    const results: Jobs[] = [];
    if (upComingJobs.length === 0) {
      return results;
    }
    const isValidJobs = (job: Jobs) => {
      let allowed: boolean[] = [];
      if (job.jobStatus === 1) {
        allowed.push(true);
      }
      if (job.isRecurringJob || job.isTutoringJob) {
        if (
          (job.weeklySchedule as WeeklySchedule).weeklyScheduleEntries.some(
            (e) => e.applicantId === null
          )
        ) {
          allowed.push(true);
        } else {
          allowed.push(false);
        }
      }
      return allowed.length === 0 ? false : allowed.every((a) => a === true);
    };
    for (const job of upComingJobs) {
      if (isValidJobs(job)) {
        results.push(job);
      }
    }
    return results;
  };

  const getAwardedJobs = () => {
    return upComingJobs.filter((job) => job.jobStatus === c.jobStatus.AWARDED);
  };

  function getJobTypeDescription(job: any, country: string): string {

    switch (job.jobType) {
      case c.jobType.BABYSITTING:
        return 'Childcare - One Off Job';
      case c.jobType.ONE_OFF_ODD_JOB:
        return 'Childcare-Odd Job';
      case c.jobType.NANNYING:
        return 'Childcare – Recurring Job';
      case c.jobType.BEFORE_SCHOOL_CARE:
        return 'Before School Care';
      case c.jobType.AFTER_SCHOOL_CARE:
        return 'After School Care';
      case c.jobType.BEFORE_AFTER_SCHOOL_CARE:
        return 'Before & After School Care';
      case c.jobType.AU_PAIR:
        return 'Au Pair Job';
      case c.jobType.PRIMARY_SCHOOL_TUTORING:
        return 'Primary School Tutoring';
      case c.jobType.HIGH_SCHOOL_TUTORING:
        return country === 'au' ? 'High School Tutoring' : 'Secondary School Tutoring';
      default:
        return 'Job';
    }

  }
  const jobs = [
    { jobType: c.jobType.BABYSITTING },
    { jobType: c.jobType.ONE_OFF_ODD_JOB },
    { jobType: c.jobType.NANNYING },
    { jobType: c.jobType.BEFORE_SCHOOL_CARE },
    { jobType: c.jobType.AFTER_SCHOOL_CARE },
    { jobType: c.jobType.BEFORE_AFTER_SCHOOL_CARE },
    { jobType: c.jobType.AU_PAIR },
    { jobType: c.jobType.PRIMARY_SCHOOL_TUTORING },
    { jobType: c.jobType.HIGH_SCHOOL_TUTORING }
  ];

  const country = 'au';
  jobs.forEach(job => {
    const jobDescription = getJobTypeDescription(job, country);
    
  });

  const unawardedJObs = getUnawardedJob();
  const {isMobile}=useIsMobile();
  const awardedJobs = getAwardedJobs();
  const combinedJobs = [...awardedJobs, ...unawardedJObs];
  return (
    <div>
      {combinedJobs.length > 0 ? (
        combinedJobs.map((job, index) => {
          const jobDescription = getJobTypeDescription(job, country);
          return (
            <JobCardHelper
              key={index}
              date={formatJobDate(`${job.jobDate}`)}
              jobOption={job.paymentType === 2 ? "Cash Payment" : "Bank Transfer"}
              upcomingJob={job.jobStatus === 2 ? 'Upcoming Jobs' : ''}
              timeSlot={
                job.durationType === null
                  ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                  : `${job.duration} week duration`
              }
              invited={job.ownerPublicName}
              title={job.jobType}
              jobType={jobDescription}
              applications={job.applicantsApplied}
              onClick={() => viewJob(job.id)}
              aplicants={job.applicants}
              applicationStatus={job.applicationStatus}
              jobStatus={job.jobStatus}
              ownerImageSrc={job.ownerImageSrc}
            />
          );
        })
      ) : (
        <div className={!isMobile ? "mr-4" : "mr-0"}>
          <NoJobsCard description={"No jobs match the specified criteria"} />
        </div>
      )}
    </div>
  )
}
export default UpcomingJobs