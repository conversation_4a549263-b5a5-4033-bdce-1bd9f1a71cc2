/* Original CSS */
.headerGradient {
  background: linear-gradient(90deg, #37a950, #ffa500);
  height: 81px;
}

.juggleLogo {
  object-fit: contain;
  width: 173px;
  height: 36px;
  margin-left: 50px;
  cursor: pointer;
}

.ReferMainSection {
  flex-grow: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.containers {
  background-color: #ffffff;
  border-radius: 20px;
  padding-left: 35px;
  padding-top: 40px;
  flex-grow: 1;
  width: fit-content;
  padding-right: 140px;
}

.ReferHeader {
  font-size: 24px;
  font-weight: 700;
  color: #585858;
  margin: 0px;
}

.backBtn {
  width: max-content;
  background-color: transparent;
  border: none;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #585858;
}

.referInstruct {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  line-height: 27px;
}

.buttonContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 65px;
  width: max-content;
  background-color: transparent;
  border: 1px solid #585858;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px #00000040;
  padding-left: 20px;
  padding-block: 3px;
  padding-right: 4px;
}

.referBtn {
  width: 156px;
  height: 39px;
  background-color: #ffa500;
  border: none;
  border-radius: 10px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.ReferDivOne {
  margin-top: 10px;
}

.ReferDivTwo {
  margin-top: 10px;
  margin-left: 10px;
  font-size: 16px;
  font-weight: 500;
  line-height: 27px;
  color: #585858;
}

.referBtn:disabled {
  width: 156px;
  height: 39px;
  background-color: #dfdfdf;
  border: none;
  border-radius: 10px;
  color: #585858;
  font-size: 14px;
  font-weight: 700;
  cursor: not-allowed;
}

.termBtn {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  text-decoration: underline;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

/* Large Screens (1200px and above) */
@media screen and (max-width: 1200px) {
  .containers {
    padding-right: 70px;
    padding-left: 25px;
  }
  
  .buttonContainer {
    gap: 60px;
  }
  
  .ReferHeader {
    font-size: 22px;
  }
}

/* Medium Screens (992px and below) */
@media screen and (max-width: 992px) {
  .containers {
    padding-right: 40px;
    padding-left: 20px;
    width: 90%;
  }
  
  .buttonContainer {
    gap: 30px;
    flex-direction: column;
    padding: 10px;
  }
  
  .ReferHeader {
    font-size: 20px;
  }
  
  .juggleLogo {
    margin-left: 25px;
  }
}

/* Tablet Screens (768px and below) */
@media screen and (max-width: 768px) {
  .ReferMainSection {
    padding: 10px;
  }
  
  .containers {
    padding: 20px;
    width: 95%;
  }
  
  .ReferHeader {
    font-size: 20px;
  }
  
  .referInstruct, .ReferDivTwo {
    font-size: 14px;
    line-height: 24px;
  }
}

/* Mobile Screens (576px and below) */
@media screen and (max-width: 576px) {
  .headerGradient {
    height: 60px;
  }
  
  .juggleLogo {
    width: 130px;
    height: 27px;
    margin-left: 15px;
  }
  
  .ReferHeader {
    font-size: 20px;
  }
  
  .containers {
    padding: 15px;
    border-radius: 15px;
  }
  
  .referBtn, .referBtn:disabled {
    width: 130px;
    height: 35px;
    font-size: 12px;
  }
  
  .backBtn {
    font-size: 14px;
  }
  
  .referInstruct, .ReferDivTwo {
    font-size: 14px;
    line-height: 21px;
  }
}

/* Small Mobile Screens (360px and below) */
@media screen and (max-width: 360px) {
  .ReferHeader {
    font-size: 20px;
  }
  
  .containers {
    padding: 10px;
  }
  
  .referBtn, .referBtn:disabled {
    width: 110px;
    height: 32px;
  }
}