/* add child welcome  */

* {
  font-family: "Poppins";
}

.add-children-container {
  width: 1007px;
  display: flex;
  font-family: "Poppins";
  padding: 40px;
  padding-left: 60px;
  outline: none;
}

.add-children-container:focus {
  outline: none;
}

.headerH1 {
  text-wrap: nowrap;
}

.main-contents-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  color: #585858;
}

.main-contents-container h1 {
  font-weight: 700;
  font-size: 32px;
}

.progress-bar-style {
  height: 7px;
  border-radius: 20px;
  overflow: hidden;
}

.progress-bar-style .p-progressbar-label {
  display: none;
}

.progress-bar-style > div {
  height: 7px !important;
}

.progress-bar-style .p-progressbar-value {
  background-color: #179d52;
}

.main-contents-container .progress-info {
  font-weight: 500;
  font-size: 14px;
  margin-top: 10px;
}

.main-contents-container .progress-info > span:last-child {
  font-weight: 700;
  font-size: 20px;
  color: #179d52;
}

.main-contents-container .button-container {
  display: flex;
  justify-content: space-between;
}

.main-contents-container .button-container button {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 14px;
  cursor: pointer;
}

.button-container div[data-skip]:hover {
  box-shadow: 0 5px 4px 0 rgba(241, 241, 241, 1);
}

.button-container div[data-skip]:hover button {
  font-weight: 800;
  text-shadow: 0 5px rgba(241, 241, 241, 1);
}

.button-container div[data-next] {
  background-color: #ffa500;
}
.all-children-view .button-container div[data-next] button,
.all-children-view .button-container div[data-next] i {
  color: white;
  font-weight: 800;
}

.button-container div[data-next]:hover {
  box-shadow: 0 4px 4px 0 #00000040;
}

.main-contents-container .button-container div {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 25px;
  border-radius: 20px;
}

.main-contents-container .button-container div button {
  font-weight: 500;
}
.main-contents-container .button-container div button {
  font-weight: 400;
}

.main-contents-container .scrollable-area {
  display: flex;
  overflow-y: auto;
  flex-direction: column;
  margin-top: 20px;
  padding-inline: 20px;
  align-items: center;
  justify-content: flex-start;
  height: min-content !important;
  gap: 20px;
}

@media (min-width: 1000px) {
  .main-contents-container .scrollable-area {
    flex-direction: row;
    padding: 0;
    padding-top: 10px;
  }
}

.main-contents-container .scrollable-area > div {
  display: flex;
  flex: 0 0 50%;
  justify-content: center;
  max-width: 381px;
}

.scrollable-area .info-container {
  font-weight: 500;
  font-size: 24px;
  justify-content: start !important;
}

.info-container div {
  background-color: rgba(241, 241, 241, 0.5);
  height: min-content;
  border-radius: 33px;
  overflow: hidden;
  padding: 10px 20px;
}

.info-container div > h1 {
  font-weight: 500;
  font-size: 24px;
}

.info-container div > p {
  font-weight: 400;
  font-size: 16px;
}

.scrollable-area .add-child {
  display: flex;
  padding: 10px;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background-color: #f1f1f180;
  height: 100%;
  width: 100%;
  border-radius: 33px;
  border: 1px solid #ffa500;
}

.scrollable-area .add-child:hover {
  background-color: rgba(241, 241, 241, 0.8);
  cursor: pointer;
}
.scrollable-area .add-child:active {
  background-color: rgba(241, 241, 241, 0.3);
}

.scrollable-area .add-child > i {
  color: white;
  background-color: #ffa500;
  padding: 10px;
  border-radius: 50%;
}

.scrollable-area .add-child > p {
  font-weight: 400;
  font-size: 24px;
  color: #585858;
  margin: 0;
  line-height: 36px;
}

/* Add New Child */
.add-new-child-container {
  color: #585858;
  width: 90vw !important;
  min-height: 80vh !important;
  /* padding-inline: 20px; */
  padding: 30px;
  display: flex;
  flex-direction: column;
}

@media (min-width: 800px) {
  .add-new-child-container {
    color: #585858;
    width: 500px !important;
  }
}

.add-new-child-container .progress-bar-style {
  height: 7px;
  border-radius: 20px;
  overflow: hidden;
}

.add-new-child-container .progress-bar-style .p-progressbar-label {
  display: none;
}

.add-new-child-container .progress-bar-style > div {
  height: 7px !important;
}

.add-new-child-container .progress-bar-style .p-progressbar-value {
  background-color: #179d52;
}

.add-new-child-container .progress-info {
  font-weight: 500;
  font-size: 14px;
  margin-top: 10px;
}

.add-new-child-container .progress-info > span:last-child {
  font-weight: 700;
  font-size: 20px;
  color: #179d52;
}

.add-new-child-container h3 {
  font-weight: 600;
  font-size: 16px;
  margin: 0;
  padding: 0;
  margin-top: 10px;
}

.add-new-child-container form .add-new-child-text-area {
  resize: none;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  border: 2px solid #f0f4f7;
  outline: none;
  font-family: "Poppins";
  font-weight: 300;
  font-size: 12px;
  padding: 5px 10px;
  background-color: rgba(241, 241, 241, 0.5);
}

.add-new-child-container form .add-new-child-text-area:not(:placeholder-shown) {
  border: 2px solid #179d52;
}
.add-new-child-container form .add-new-child-text-area:focus {
  border: 2px solid #179d52;
}

/* All Children View */

.all-children-view {
  color: #585858;
  width: 90vw !important;
  min-height: 80vh !important;
  /* padding-inline: 20px; */
  padding: 35px;
  display: flex;
  flex-direction: column;
}

@media (min-width: 800px) {
  .all-children-view {
    color: #585858;
    width: 500px !important;
  }
}

.all-children-view .progress-bar-style {
  height: 7px;
  border-radius: 20px;
  overflow: hidden;
}

.all-children-view .progress-bar-style .p-progressbar-label {
  display: none;
}

.all-children-view .progress-bar-style > div {
  height: 7px !important;
}

.all-children-view .progress-bar-style .p-progressbar-value {
  background-color: #179d52;
}

.all-children-view .progress-info {
  font-weight: 500;
  font-size: 14px;
  margin-top: 10px;
}

.all-children-view .progress-info > span:last-child {
  font-weight: 700;
  font-size: 20px;
  color: #179d52;
}

.all-children-view h3 {
  font-weight: 600;
  font-size: 16px;
  margin: 0;
  padding: 0;
  margin-top: 10px;
}

.all-children-view .button-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.all-children-view .button-container button {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 14px;
  cursor: pointer;
}

.button-container div:hover {
  cursor: pointer;
}

.all-children-view .button-container div {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 25px;
  border-radius: 20px;
}

.all-children-view .button-container div button {
  font-weight: 500;
}
.all-children-view .button-container div button {
  font-weight: 400;
}
.childrenyear {
  max-width: 90px !important;
  box-shadow: none;
  text-align: center;
}
.childrenyear > .p-button {
  display: none !important;
}
.childrenyear > .p-inputtext {
  box-shadow: none !important;
  max-width: 86px !important;
  max-height: 56px !important;
  padding-inline: 23px !important;
  cursor: pointer;
}
.childrenyear > .p-inputtext:enabled:hover {
  border-radius: 10px;
  border: none;
}
.green-border {
  border-radius: 10px;
  border: 2px solid #179d52;
}

.hideScrollBar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hideScrollBar::-webkit-scrollbar {
  display: none;
}
.add-children-container-mobile {
  width: 100%;
  display: flex;
  font-family: "Poppins";
  outline: none;
  padding-inline: 15px;
  margin-top: 85px;
  padding-bottom: 70px;
}

.add-children-container-mobile {
  outline: none;
}
.scrollable-area.scrollable-area-mobile{
  overflow-y: unset;
  height: 100% !important;
}
.button-container.button-container-mobile{
  bottom: 0;
  left: 0;
  position: fixed;
  width: 100%;
  box-shadow: 0px 0px 8px 0px #00000040;
  background-color: #fff;
}
.add-new-child-container-mobile{
  color: #585858;
  width: 100% !important;
  display: flex;
  padding-inline: 15px;
  margin-top: 85px;
  padding-bottom: 70px;
  flex-direction: column;
  min-height: 100% !important;
  padding: 15px;
}
.all-children-view-mobile{
  color: #585858;
  width: 100% !important;
  min-height: 100% !important;
  padding: 15px;
  margin-top: 85px;
  padding-bottom: 70px;
  display: flex;
  flex-direction: column;
}