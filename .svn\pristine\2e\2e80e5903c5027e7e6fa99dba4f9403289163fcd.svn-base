import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import styles from "../styles/in-app-chat.module.css";
import ReceiveChat from "./ReceiveChat";
import SendChat from "./SendChat";
import Service from "../../../services/services";
import { ChatSession } from "./InAppChatTypes";
import ActivityTimeline from "./ActivityTimeline";
import useLoader from "../../../hooks/LoaderHook";
import calender from "../../../assets/images/Icons/Icon (1).png";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import useIsMobile from "../../../hooks/useIsMobile";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { updateShowChatBox } from "../../../store/slices/applicationSlice";
import HelpdeskManager from "../../../commonComponents/HelpdeskManager";

const InAppChat: React.FC = () => {
  const [searchParams] = useSearchParams();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const id = Number(searchParams.get("userId"));
  const flag = Number(searchParams.get('flag'))
  const [selectedChatId, setSelectedChatId] = useState<string | undefined>();
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [selectedParticipantId, setSelectedParticipantId] = useState<
    number | null
  >(null);
  const [error, setError] = useState<string | null>(null);
  const { disableLoader, enableLoader } = useLoader();
  const [noChatsAvailable, setNoChatsAvailable] = useState<boolean>(false);
  const [showMobileChat, setShowMobileChat] = useState(false);
  const { isMobile } = useIsMobile();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  // const handleChatSelect = (chatId: string) => {
  //   setSelectedChatId(chatId);
  // };

  const handleChatSelect = (chatId) => {
    setSelectedChatId(chatId);
    if (isMobile) {
      setShowMobileChat(true);
    }
  };

  const handleBackToList = () => {
    if (isMobile) {
      setShowMobileChat(false);
      setSelectedChatId(undefined);
    }
  };
  useEffect(() => {
    dispatch(updateShowChatBox(false));

    return () => {
      dispatch(updateShowChatBox(true));

    };
  }, []);

  const processExistingChatSessions = (data: ChatSession[]) => {
    setChatSessions(data);

    if (data.length === 0) {
      setNoChatsAvailable(true);
    } else {
      setNoChatsAvailable(false);
    }

    if (!id && data.length > 0) {
      setSelectedChatId(data[0].id);
      return;
    }

    let matchedChatId: string | undefined = undefined;
    let matchedParticipantId: number | null = null;

    data.forEach((session) => {
      session.participants.forEach((participant) => {
        if (
          participant.participantId === id &&
          participant.jugglerTypes === 2
        ) {
          matchedChatId = session.id;
          matchedParticipantId = participant.participantId;
        }
      });
    });

    if (matchedChatId && matchedParticipantId) {
      setSelectedChatId(matchedChatId);
      setSelectedParticipantId(matchedParticipantId);
    } else if (data.length > 0) {
      setSelectedChatId(data[0].id);
    }
  };
  const fetchCanChatWithHelper = () => {
    if (!id || flag === 0) return;
    Service.getChat(
      (data) => {

      },
      (error) => {
        console.error("Error fetching Can Chat With Helper data:", error);
        setError("Failed to fetch Can Chat With Helper data");
      },
      id
    );
  };

  useEffect(() => {
    fetchCanChatWithHelper();
  }, [id, flag, fetchCanChatWithHelper]);

  const initiateNewChatIfNeeded = () => {
    Service.getChatInitiate(
      (initiateData) => {


        Service.getChatSessions(
          (updatedSessions: ChatSession[]) => {
            console.log(
              "Updated Chat Sessions after initiation:",
              updatedSessions
            );

            const sessionMap = new Map<string, ChatSession>(
              updatedSessions.map((session) => [session.id, session])
            );

            const finalSessions = [
              initiateData[0],
              ...Array.from(sessionMap.values()),
            ];

            const newChat = finalSessions.find((session) =>
              session.participants.some((p) => p.participantId === id)
            );

            if (newChat) {
              setSelectedChatId(newChat.id);
              const matchedParticipant = newChat.participants.find(
                (p) => p.participantId === id
              );
              if (matchedParticipant) {
                setSelectedParticipantId(matchedParticipant.participantId);
              }
            }

            processExistingChatSessions(finalSessions);
            disableLoader();
          },
          (error) => {
            console.error("Error refetching chat sessions:", error);
            setError("Failed to refetch chat sessions");
            disableLoader();
          }
        );
      },
      (error) => {
        console.error("Error initiating chat:", error);
        setError("Failed to initiate new chat");
        disableLoader();
      },
      id
    );
  };

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        enableLoader();

        Service.getChatSessions(
          (data: ChatSession[]) => {


            if (!id) {
              processExistingChatSessions(data);
              disableLoader();
              return;
            }

            const isUserInExistingSessions = data.some((session) =>
              session.participants.some(
                (participant) =>
                  participant.participantId === id &&
                  participant.jugglerTypes === 2
              )
            );

            if (!isUserInExistingSessions) {
              initiateNewChatIfNeeded();
            } else {
              processExistingChatSessions(data);
              disableLoader();
            }
          },
          (error) => {
            console.error("Error fetching chat sessions:", error);
            setError("Failed to fetch chat sessions");
            disableLoader();
          }
        );
      } catch (err) {
        console.error("Unexpected error:", err);
        setError("An unexpected error occurred");
        disableLoader();
      }
    };

    fetchInitialData();
  }, [id]);

  useEffect(() => {



  }, [selectedChatId, selectedParticipantId, chatSessions]);

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className={`w-screen h-screen ${styles.chatContainer}`}>
      <header
        className={`w-full flex align-items-center  border-round-bottom-lg overflow-hidden ${styles.headerGradient}`}
      >
        <button
          className={styles.backHome}
          onClick={() => {
            if (isMobile && showMobileChat) {
              handleBackToList();
              return;
            }
            let path = clientType === 0 ? "/helper-home" :
              clientType === 2 ? "/business-home" : "/parent-home";
            navigate(path);
          }}
        >
          {"< "}{isMobile && showMobileChat ? "Back" : "Back home"}
        </button>

        {/* <button
          className={styles.backManage}
          onClick={() => {
            const path =
              clientType === 2
                ? "/business-home/manage-jobs"
                : "/parent-home/manage-jobs";
            navigate(path);
          }}
        >
          <img src={calender} alt="calender" width={15} height={16} />
          Manage Jobs
        </button> */}
        <button
          className={styles.backManage}
          onClick={() => {
            let path;
            if (clientType === 2) {
              path = "/business-home/manage-jobs";
            } else if (clientType === 0) {
              path = "/helper-home/myjobs";
            } else {
              path = "/parent-home/manage-jobs";
            }
            navigate(path);
          }}
        >
          <img src={calender} alt="calender" width={15} height={16} />
          Manage Jobs
        </button>
        <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
      </header>
      {noChatsAvailable ? (
        <div
          style={{
            color: "#585858",
            fontSize: "24px",
            fontWeight: "600",
            paddingLeft: "27px",
            paddingTop: "10px"
          }}
        >
          No conversations yet
        </div>
      ) : (
        <main style={{
          padding: isMobile ? "10px" : "20px",
          paddingTop: isMobile ? "0px" : "0px",
        }} className={styles.chatMain}>
          {isMobile ? (
            // Mobile Layout
            <div className="flex flex-row" style={{ flexGrow: "1" }}>
              {!showMobileChat ? (
                <div className="w-full ">
                  <ReceiveChat
                    chatSessions={chatSessions.reverse()}
                    onChatSelect={handleChatSelect}
                    selectedChatId={selectedChatId}
                  />
                </div>
              ) : (
                <div className="w-full flex">
                  <SendChat chatId={selectedChatId} />
                </div>
              )}
            </div>
          ) : (
            // Desktop Layout - Keeping original PrimeFlex structure
            <div className="flex flex-row" style={{ flexGrow: "1" }}>
              <ReceiveChat
                chatSessions={chatSessions.reverse()}
                onChatSelect={handleChatSelect}
                selectedChatId={selectedChatId}
              />
              <SendChat chatId={selectedChatId} />
              <ActivityTimeline
                chatSessions={chatSessions}
                selectedChatId={selectedChatId}
              />
            </div>
          )}
        </main>
      )}
      <HelpdeskManager />
    </div>
  );
};

export default InAppChat;
