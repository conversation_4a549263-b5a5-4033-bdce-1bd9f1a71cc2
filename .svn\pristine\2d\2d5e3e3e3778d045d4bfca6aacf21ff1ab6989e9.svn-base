import environment from "../../helper/environment";
import * as _ from "underscore";

const AU_PHONE_REG_EXP = /^04\d{8}/;
const AU_PHONE_REG_EXP_2 = /^[+]?614\d{8}/;
const NZ_PHONE_REG_EXP = /^02\d{7,9}/;
const NZ_PHONE_REG_EXP_2 = /^[+]?642\d{7,9}/;
const POST_CODE_REG_EXP = /^\d{4}$/;
type ValidationResult = string | null;

export const validateEmail = (email: string): string | null => {
  const trimmedEmail = email.trim();

  // Check if the email is empty
  if (!trimmedEmail) {
    return "Please enter your email address.";
  }

  // Check if email contains '@'
  if (!trimmedEmail.includes("@")) {
    return "Please include a '@' in the email address.";
  }

  // Use more advanced regex for validating proper email format
  const emailRegex =
    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // Check if email matches the regex pattern
  if (!emailRegex.test(trimmedEmail)) {
    return "Please enter a valid email address.";
  }

  // Additional checks if needed
  const [localPart, domain] = trimmedEmail.split("@");

  // Ensure local part is not too long (64 characters max per standard)
  if (localPart.length > 64) {
    return "Please enter a valid email address.";
  }

  // Ensure domain part is not too long (255 characters max per standard)
  if (domain.length > 255) {
    return "Please enter a valid email address.";
  }

  // Ensure domain contains a valid top-level domain
  const domainParts = domain.split(".");
  if (domainParts.length < 2 || domainParts.some(part => part.length === 0)) {
    return "Please enter a valid email address.";
  }

  // Ensure the top-level domain (TLD) has at least two characters
  const topLevelDomain = domainParts[domainParts.length - 1];
  if (topLevelDomain.length < 2) {
    return "Please enter a valid email address.";
  }

  return null; // No error, email is valid
};


export const validatePassword = (password: string): string | null => {
  const trimmedPassword = password.trim();

  // Check if the password is empty
  if (!trimmedPassword) {
    return "Please enter your password.";
  }

  // Check if the password meets the minimum length requirement
  if (trimmedPassword.length < 7) {
    return "Password must be at least 8 characters long.";
  }

  // Ensure password contains at least one uppercase letter
  // if (!/[A-Z]/.test(trimmedPassword)) {
  //   return "Password must contain at least one uppercase letter.";
  // }

  // // Ensure password contains at least one lowercase letter
  // if (!/[a-z]/.test(trimmedPassword)) {
  //   return "Password must contain at least one lowercase letter.";
  // }

  // // Ensure password contains at least one number
  // if (!/\d/.test(trimmedPassword)) {
  //   return "Password must contain at least one number.";
  // }

  // // Ensure password contains at least one special character
  // if (!/[!@#$%^&*(),.?":{}|<>]/.test(trimmedPassword)) {
  //   return "Password must contain at least one special character.";
  // }

  // Check for disallowed weak passwords (optional but recommended for security)
  // const commonPasswords = [
  //   "password", "123456", "12345678", "qwerty", "abc123", "111111", "password1"
  // ];
  // if (commonPasswords.includes(trimmedPassword.toLowerCase())) {
  //   return "This password is too common. Please choose a more secure password.";
  // }

  return null; // Password is valid
};


export const validatePhoneNumber = (phoneNumber: string): boolean => {
  let v = phoneNumber.replace(/\s/gi, "");

  if (environment.getCountry(window.location.hostname) === "au") {
    if (AU_PHONE_REG_EXP.test(v)) {
      return v.length === 10;
    }

    if (AU_PHONE_REG_EXP_2.test(v)) {
      return v.length === 12;
    }
    return false;
  }

  if (environment.getCountry(window.location.hostname) === "nz") {
    if (NZ_PHONE_REG_EXP.test(v)) {
      return v.length >= 9 && v.length <= 11;
    }

    if (NZ_PHONE_REG_EXP_2.test(v)) {
      return v.length >= 10 && v.length <= 12;
    }
    return false;
  }

  if (!validateNumber(v)) {
    return false;
  }

  return v.length <= 15;
};

export const validateNumber = (v: string): boolean => {
  return !/\D/.test(v);
};

export const validatePostCode = (postCode: string): boolean => {
  return POST_CODE_REG_EXP.test(postCode);
};

// export const validateAddress = (address: string): boolean => {
//   const trimmedAddress = address.trim();
  
//   // Check if address is not empty and has at least one space.
//   if (!trimmedAddress || trimmedAddress.indexOf(" ") === -1) {
//     return false;
//   }

//   const addressRegex = /^[a-zA-Z0-9\s.,#-]+$/;
  
//   // Check if the address is at least 5 characters long and matches the regex.
//   return trimmedAddress && addressRegex.test(trimmedAddress);
// };
export const validateAddress = (address: string): boolean => {
  const trimmedAddress = address.trim();
  
  // Check if address is not empty and has at least one space.
  if (!trimmedAddress || trimmedAddress.indexOf(" ") === -1) {
    return false;
  }

  // Updated regex to allow '/' character in the address.
  const addressRegex = /^[a-zA-Z0-9\s.,#/-]+$/;
  
  // Check if the address is at least 5 characters long and matches the regex.
  return trimmedAddress && addressRegex.test(trimmedAddress);
};

