import React, { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CustomDialog from '../Common/CustomDialog';
import BusinessIntroduction from './BusinessIntroduction';

import IdentityCheck from '../Common/IdentityCheck';
import { AppDispatch, RootState } from '../../store';
import { setCurrentProfileActivationStep } from '../../store/slices/applicationSlice';
import c from '../../helper/juggleStreetConstants';
import BuisnessPhoto from './BuisnessPhoto';

interface ProfileCompletionProps {
    isVisible: boolean;
    closeDialog: () => void;
}

function ProfileCompletionWizardBusiness({ isVisible, closeDialog }: ProfileCompletionProps) {
    const { data: sessionData } = useSelector((state: RootState) => state.sessionInfo);
    const applicationState = useSelector(
        (state: RootState) => state.applicationState.profileActivationCurrentStep
    );
    const dispatch = useDispatch<AppDispatch>();

    const profileCompleteness = sessionData['profileCompleteness'];
    const clientType = sessionData['clientType'];
    const isIndividual = clientType == c.clientType.INDIVIDUAL;
    const steps = [];

    const completeClientPhoto = () => {
        return (c.profileItems.CLIENT_PHOTO & sessionData['profileItems']) !== 0;
    };
    const completeClientAboutMe = () => {
        return (c.profileItems.CLIENT_ABOUT_ME & sessionData['profileItems']) !== 0;
    };

    const identityCheckComplete = useCallback(() => {
        return (c.profileItems.TRUST_VERIFICATION & sessionData['profileItems']) !== 0;
    }, [sessionData]);

    if (!identityCheckComplete()) {
        steps.push(IdentityCheck);
    }
    if ((!isIndividual && !completeClientAboutMe())) {
        steps.push(BusinessIntroduction);

    }
    if (!completeClientPhoto()) {
        steps.push(BuisnessPhoto);
    }


    useEffect(() => {
        dispatch(setCurrentProfileActivationStep(1));
    }, [isVisible, dispatch]);

    // Render the current step component
    const renderStepComponent = () => {
        const StepComponent = steps[applicationState - 1];
        return StepComponent ? <StepComponent /> : <CloseDialog closeDialog={closeDialog} />;
    };
    return (
        <React.Fragment>
            <CustomDialog
                visible={isVisible}
                onHide={closeDialog}
                closeClicked={() => { }}
                profileCompletion={profileCompleteness}
            >
                {renderStepComponent()}
            </CustomDialog>
        </React.Fragment>
    );
}
const CloseDialog = ({ closeDialog }: { closeDialog: () => void }) => {
    useEffect(() => {
        if (closeDialog) {
            closeDialog();
        }
    }, [closeDialog]);
    return <></>;
};

export default ProfileCompletionWizardBusiness;
