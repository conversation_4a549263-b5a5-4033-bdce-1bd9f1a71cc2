import { useEffect } from 'react';
import Service from '../services/services';
import { useApiData } from './useApiData';
import c from '../helper/juggleStreetConstants';

export interface PaymentEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  amount?: number;
  description?: string;
  originalImageUrl?: string;
}

interface PaymentApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
  amount?: number;
  description?: string;
}

const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Invoice Approval",
  [c.ApprovalStatus.READY_FOR_PAYMENT]: "Ready for Payment",
  [c.ApprovalStatus.PAYMENT_COMPLETED]: "Payment Completed",
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "Babysitting Service",
  [c.jobType.NANNYING]: "Nannying Service",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair Service",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job Service",
};

const transformPaymentItem = (item: PaymentApiItem): PaymentEntry => ({
  id: item.id,
  status: statusMap[item.status] || "Unknown Status",
  type: jobTypeMap[item.jobType] || "Unknown Service",
  date: new Date(item.jobDate).toLocaleDateString('en-AU', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  }),
  location: item.formattedAddress,
  userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
  originalImageUrl: item.originalImageUrl,
  amount: item.amount,
  description: item.description,
});

export const usePaymentData = () => {
  const {
    data: paymentData,
    isLoading,
    error,
    fetchData,
    refreshData,
    clearData
  } = useApiData<PaymentEntry>({
    transform: transformPaymentItem,
    onSuccess: (data) => {
      console.log("Payment data fetched successfully:", data);
    },
    onError: (error) => {
      console.error("Failed to fetch payment data:", error);
    }
  });

  useEffect(() => {
    fetchData((successCallback, errorCallback) => {
      Service.getinvoice(successCallback, errorCallback);
    });
  }, [fetchData]);

  // Filter data by status for different tabs
  const invoiceApprovalData = paymentData.filter(item => 
    item.status.includes("Awaiting Invoice Approval")
  );

  const makePaymentData = paymentData.filter(item => 
    item.status.includes("Ready for Payment")
  );

  const paymentHistoryData = paymentData.filter(item => 
    item.status.includes("Payment Completed")
  );

  return {
    paymentData,
    invoiceApprovalData,
    makePaymentData,
    paymentHistoryData,
    isLoading,
    error,
    refreshData,
    clearData
  };
};

export default usePaymentData;
