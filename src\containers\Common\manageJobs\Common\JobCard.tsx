import React, { useEffect, useRef, useState } from 'react';
import home from '../../../../assets/images/Icons/home.png';
import userProfile from '../../../../assets/images/Icons/user-profile.png';
import calendar from '../../../../assets/images/Icons/manage_job.png';
import clock from '../../../../assets/images/Icons/clockstart.png';
import childcare from '../../../../assets/images/Icons/my_child.png';
import styles from '../../../Common/styles/job-card.module.css';
import { Divider } from 'primereact/divider';
import c from '../../../../helper/juggleStreetConstants';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';
import useIsMobile from '../../../../hooks/useIsMobile';
interface JobCardProps {
    date?: {
        day: string;
        date: string;
        month: string;
    };
    title?: number;
    jobOption?: any;
    jobType?: string;
    timeSlot?: string;
    location?: string;
    status?: string;
    expiry?: number;
    applications?: number;
    aplicants?: Array<{
        applicantImageSrc: string;
        applicantId: number;
        applicationStatus: number;
        
    }>;
    managedBy?: number;
    onClick?: () => void;
    onAnalytics?: () => void;
    availablity?: boolean;
}
const getJobType = (jobCode: number): string => {
    if (jobCode === 256) {
        return 'Odd Job';
    } else if (jobCode === 64 || jobCode === 128) {
        return 'Tutoring';
    } else {
        return 'Childcare';
    }
};
const getJobTypeMobile = (jobCode: number): string => {
    const jobTypeMap: { [key: number]: string } = {
        0: 'Unspecified',
        1: 'Babysitting',
        2: 'Nannying',
        4: 'Before School Care',
        8: 'After School Care',
        12: 'Before & After School Care',
        16: 'Au Pair',
        32: 'Home Tutoring',
        64: 'Primary School Tutoring',
        128: 'High School Tutoring',
        256: 'One-Off Odd Job',
    };

    return jobTypeMap[jobCode] || 'Unknown';
};
const cardStyle = {
    borderRadius: '10px',
    border: '1px solid #DFDFDF',
    width: '651px',
    transition: 'box-shadow 0.3s ease',
};

const cardHoverStyle = {
    ...cardStyle,
    boxShadow: '0 4px 8px 0px rgba(0,0,0,0.25)',
};



const JobCard: React.FC<JobCardProps> = (props) => {
    const {
        date,
        title,
        jobOption,
        jobType,
        timeSlot,
        location,
        status,
        expiry,
        applications,
        aplicants,
        managedBy,
        availablity,
        onClick,
        onAnalytics
    } = { ...props };
    const { isMobile } = useIsMobile();
    const clientType = utils.getCookie(CookiesConstant.clientType);

    const [isHovered, setIsHovered] = useState(false);
    function getDisplayHelpersImg() {
        const results: string[] = [];
        if (aplicants && aplicants.length > 0) {
            aplicants
                .filter((e) =>
                    [
                        c.jobApplicationStatus.APPLIED as number,
                        c.jobApplicationStatus.SHORTLISTED_BY_CLIENT as number,
                        c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM as number,
                    ].includes(e.applicationStatus)
                )
                .forEach((e) => results.push(e.applicantImageSrc));
        }

        // Get up to 5 images (5 or fewer)
        const topResults = results.slice(0, 5);
        return topResults;
    }
    const getOrdinalSuffix = (number) => {
        if (!number) return '';
        const lastDigit = number % 10;
        const lastTwoDigits = number % 100;

        if (lastTwoDigits >= 11 && lastTwoDigits <= 13) return 'th';
        switch (lastDigit) {
            case 1:
                return 'st';
            case 2:
                return 'nd';
            case 3:
                return 'rd';
            default:
                return 'th';
        }
    };
    return !isMobile ? (
        <div className='mb-5' style={{ maxWidth: '651px', width: '100%' }}>
            {/* Date Column  */}
            <div className='flex items-stretch'>
                {/* Main Card  */}
                <div className='flex flex-column w-full'>
                    <div
                        className='flex-1 w-full'
                        style={isHovered ? cardHoverStyle : cardStyle}
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                    >
                        <div className='pl-3 pr-3'>
                            {/* Header Section - Made responsive with flex-wrap */}
                            <div className='pl-0 pr-2 pt-2 flex flex-wrap justify-content-between align-items-center gap-2'>
                                <div className='flex align-items-center gap-2 flex-wrap'>
                                    <p
                                        className='p-0 m-0 font-bold'
                                        style={{ fontSize: 'clamp(24px, 4vw, 30px)', color: '#585858' }}
                                    >
                                        {getJobType(title)}
                                    </p>
                                    <span style={{ fontSize: 'clamp(20px, 4vw, 30px)', color: "#585858" }}>-</span>
                                    <div className='flex align-items-center gap-2' style={{ minWidth: '100px' }}>
                                        <span className='font-bold' style={{ fontSize: 'clamp(24px, 4vw, 30px)', color: '#585858' }}>
                                            {date?.day}
                                        </span>
                                        <span
                                            className='font-bold ml-1'
                                            style={{ fontSize: 'clamp(24px, 4vw, 30px)', color: '#585858', position: 'relative' }}
                                        >
                                            {date?.date} 
                                            <sup
                                                style={{
                                                    fontSize: 'clamp(12px, 2vw, 14px)',
                                                    position: 'absolute',
                                                    top: '-5px',
                                                    right: '-15px',
                                                }}
                                            >
                                                {getOrdinalSuffix(date?.date)}
                                            </sup>
                                        </span>
                                         <span className='font-bold ' style={{ fontSize: 'clamp(24px, 4vw, 30px)', color: '#585858' , marginLeft:"13px" }}>
                                             {date?.month}
                                        </span>
                                    </div>
                                </div>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-3 py-2`}
                                >
                                    <span className={styles.jobType}>{jobOption}</span>
                                </div>
                            </div>

                            {/* Job Details - Made responsive with proper wrapping */}
                            <div className='flex flex-wrap gap-2 mt-2'>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                >
                                    <img
                                        alt='Calendar'
                                        src={calendar}
                                        style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                    />
                                    <span className={styles.jobType}>{jobType}</span>
                                </div>
                                <div
                                    className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                >
                                    <img
                                        alt='Clock'
                                        src={clock}
                                        style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                    />
                                    <span className={styles.jobType}>{timeSlot}</span>
                                </div>
                                {clientType !== '0' && (
                                    <div
                                        className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                                    >
                                        <img
                                            alt='Home'
                                            src={home}
                                            style={{ width: '15px', height: '15px', minWidth: '15px' }}
                                        />
                                        <span className={styles.jobType}>{location}</span>
                                    </div>
                                )}
                            </div>
                            <Divider className='mt-3' />

                            {/* Status Section - Made responsive with column layout on small screens */}
                            <div className='pr-2 py-3 border-t'>
                                <div className='flex flex-wrap justify-content-between align-items-center gap-3'>
                                    <div className='space-y-1'>
                                        {managedBy === 20 && (
                                            <div className='flex align-items-center gap-2'>
                                                <span className={`${styles.status}`}>Status:</span>
                                                <span
                                                    className={`${styles.status} font-bold`}
                                                    style={{ textDecoration: 'underline' }}
                                                >
                                                    {status}
                                                </span>
                                            </div>
                                        )}
                                        <div className='flex align-items-center gap-2'>
                                            <span className={`${styles.status}`}>Expiry:</span>
                                            <span
                                                className={`${styles.status} font-bold`}
                                                style={{ textDecoration: 'underline' }}
                                            >
                                                {expiry} days
                                            </span>
                                        </div>
                                    </div>
                                    {clientType !== '0' && (
                                        <div className='flex align-items-center gap-2 flex-wrap'>
                                            <span className={`${styles.status}`}>
                                                Applications:
                                            </span>
                                            <span
                                                className='font-bold'
                                                style={{ color: '#585858', fontSize: 'clamp(24px, 4vw, 30px)' }}
                                            >
                                                {applications}
                                            </span>
                                            <div className='relative flex'>
                                                {getDisplayHelpersImg().map((helper, index) => (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            position: 'relative',
                                                            left: `-${index * 8}px`,
                                                            zIndex: index,
                                                        }}
                                                    >
                                                        <img
                                                            src={helper}
                                                            alt='helper display'
                                                            height='33px'
                                                            width='33px'
                                                            style={{
                                                                borderRadius: '50%',
                                                                height: 'clamp(28px, 4vw, 33px)',
                                                                width: 'clamp(28px, 4vw, 33px)'
                                                            }}
                                                        />
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* View Job Button - Made responsive font size */}
                        <button
                            onClick={onClick}
                            className='w-full py-2 text-white font-bold cursor-pointer'
                            style={{
                                backgroundColor: '#FFA500',
                                textDecoration: 'underline',
                                fontSize: 'clamp(16px, 3vw, 20px)',
                                border: 'none',
                                borderBottomLeftRadius: '10px',
                                borderBottomRightRadius: '10px',
                            }}
                        >
                            {applications === 0 ? 'View Job' : 'View & Award'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    ) : (
        <div className='h-min mb-3' style={{ width: '' }}>
            {/* Date Column  */}
            <div className='flex items-stretch'>
                {/* Main Card  */}
                <div className='flex flex-column w-full'>
                    <div
                        className='flex-1'
                        // onClick={onAnalytics}
                        style={{
                            borderRadius: '20px',
                            border: '1px solid #DFDFDF',
                            width: "100%"
                        }}
                    >
                        <div>
                            <div className='flex flex-row justify-content-between align-items-center px-3 pt-3 pb-2'>
                                <div
                                    className='flex flex-row align-items-center'
                                    style={{ width: '100px' }}
                                >
                                    <span
                                        className='font-semibold'
                                        style={{ fontSize: '18px', color: '#585858' }}
                                    >
                                        {date?.day}
                                        {','}
                                    </span>
                                    <span
                                        className='font-medium ml-1'
                                        style={{ fontSize: '18px', color: '#585858' }}
                                    >
                                        {date?.date}
                                    </span>
                                    <span
                                        className='font-semibold  ml-1'
                                        style={{
                                            fontSize: '18px',
                                            color: '#585858',
                                            lineHeight: '20px',
                                        }}
                                    >
                                        {date?.month}
                                    </span>
                                </div>
                                <div className='flex align-items-center gap-2'>
                                    <span className={`${styles.statusMobile}`}>Expiry:</span>
                                    <span
                                        className={`${styles.statusMobile} font-bold`}
                                        style={{ textDecoration: 'underline' }}
                                    >
                                        {expiry} days
                                    </span>
                                </div>
                            </div>
                            <Divider />
                            {/* Header Section  */}
                            <div
                                style={{ color: "#179D52", textDecoration: "underline" }}
                                className='pl-2 pr-2 flex justify-content-between align-items-center cursor-pointer'
                                onClick={onClick}>
                                <div className='flex align-items-center gap-2 pt-2 px-2'>
                                    <span>
                                        <img
                                            alt='childcare'
                                            src={childcare}
                                            style={{
                                                width: '17px',
                                                height: '17px',
                                                color: '#585858',
                                            }}
                                            className='font-light'
                                        />
                                    </span>
                                    <p
                                        className='p-0 m-0 font-bold'
                                        style={{ fontSize: '14px', }}
                                    >
                                        {getJobTypeMobile(title)}
                                    </p>
                                </div>
                                {/* <div
                  className={`${styles.borderRadius} flex align-items-center gap-2 px-3 py-1`}
                >
                  <span className={styles.jobType}>{jobOption}</span>
                </div> */}
                            </div>

                            {/* Job Details  */}
                            <div
                                style={{ flexDirection: 'column' }}
                                className='flex flex-direction-column px-2'
                            >
                                <div className={`flex align-items-center gap-2 px-2 py-1 `}>
                                    <img
                                        alt='Calendar'
                                        src={calendar}
                                        style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                                    />
                                    <span className={styles.jobTypeMobile}>{jobType}</span>
                                </div>
                                <div className={`flex align-items-center gap-2 px-2 py-1`}>
                                    <img
                                        alt='Clock'
                                        src={clock}
                                        style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                                    />
                                    <span className={styles.jobTypeMobile}>{timeSlot}</span>
                                </div>
                                {clientType !== '0' ? (
                                    <div className={`flex align-items-center gap-2 px-2 py-1`}>
                                        <img
                                            alt='Home'
                                            src={home}
                                            style={{
                                                width: '15px',
                                                height: '15px',
                                                color: '#FFFFFF',
                                            }}
                                        />
                                        <span className={styles.jobTypeMobile}>{location}</span>
                                    </div>
                                ) : null}
                                {clientType !== '0' ? (
                                    <div
                                        style={{
                                            textDecoration: 'underline',
                                            textDecorationColor: applications > 0 ? "#179D52" : "#585858"
                                        }}
                                        className={`flex align-items-center gap-2 px-2 py-1`}
                                    >
                                        <img
                                            alt='Home'
                                            src={userProfile}
                                            style={{
                                                width: '18px',
                                                height: '15px',
                                                color: '#FFFFFF',
                                            }}
                                        />

                                        <span
                                            className='font-bold'
                                            style={{

                                                color: applications > 0 ? "#179D52" : '#585858',

                                                fontSize: '14px'
                                            }}
                                        >
                                            {applications}
                                        </span>
                                        <span
                                            style={{ fontWeight: '700' }}
                                            className={applications > 0 ? `${styles.statusMobileCount}` : `${styles.statusMobile}`}
                                        >
                                            Applications:
                                        </span>
                                        <div className='relative flex'>
                                            {getDisplayHelpersImg().map((helper, index) => (
                                                <div
                                                    key={index}
                                                    style={{
                                                        position: 'relative',
                                                        left: `-${index * 10}px`,
                                                    }}
                                                >
                                                    <img
                                                        src={helper}
                                                        alt='helper display'
                                                        height='33px'
                                                        width='33px'
                                                        style={{ borderRadius: '50%' }}
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ) : null}
                            </div>
                            <Divider className='mt-2' />

                            {/* Status Section  */}
                            <div className='pr-2 py-3 border-t px-3'>
                                <div className='flex justify-content-between align-items-center'>
                                    <div className='space-y-1'>
                                        <div className='flex align-items-center gap-2'>
                                            <span className={`${styles.statusMobile}`}>
                                         {managedBy === 1 ? "DIY Invitation :":"Juggle Assist :"}
                                            </span>
                                            <span
                                                className={`font-bold`}
                                                style={{
                                                    // textDecoration: 'underline',
                                                    fontSize: '14px',
                                                    color: '#179D52',
                                                }}
                                            >
                                                {status}
                                            </span>
                                        </div>
                                    </div>
                                    {applications === 0 && availablity === false ? (
                                        <button
                                            // onClick={onClick}
                                            onClick={onClick}
                                            className=' py-2 px-2 text-white font-bold cursor-pointer'
                                            style={{
                                                backgroundColor: '#FFA500',
                                                fontSize: '12px',
                                                border: 'none',
                                                borderRadius: '20px',
                                            }}
                                        >
                                            View Job
                                        </button>
                                    ) : (
                                        <button
                                            onClick={onAnalytics}
                                            className='py-2 px-2 text-white font-bold cursor-pointer'
                                            style={{
                                                backgroundColor: '#FFA500',
                                                fontSize: '12px',
                                                border: 'none',
                                                borderRadius: '20px',
                                                textWrap: "nowrap"
                                            }}
                                        >
                                            View & Award
                                        </button>
                                    )}
                                </div>
                            </div>

                            {/* View Job Button  */}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
export default JobCard;
