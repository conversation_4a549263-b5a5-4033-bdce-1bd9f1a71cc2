import React, { useState, useEffect } from 'react';
import { ProgressBar } from 'primereact/progressbar';
import juggleStreetLogo from '../../../../assets/images/juggle-st-transparent-card.png';
import 'primereact/resources/themes/saga-green/theme.css'; // PrimeReact theme
import 'primereact/resources/primereact.min.css'; // PrimeReact core styles
import useIsMobile from '../../../../hooks/useIsMobile';


const GeneratingInvoice = () => {
    const [value, setValue] = useState(0);
    const { isMobile } = useIsMobile();

    useEffect(() => {
        const interval = setInterval(() => {
            setValue((prev) => {
                if (prev >= 100) {
                    clearInterval(interval);
                    return 100;
                }
                return prev + 1;
            });
        }, 50);

        return () => clearInterval(interval);
    }, []);

    return (
        isMobile && (
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100vh',
                    backgroundColor: "#ffffff",
                    fontFamily: 'Arial, sans-serif',
                }}
            >
                <div style={{
                    background: "linear-gradient(90deg, #FFA500 0%, #37A950 100%)",
                    height: '68px',
                    width: '100%',
                    position: 'absolute',
                    top: 0,
                }}></div>
                <div
                    style={{
                        padding: '20px',
                        borderRadius: '10px',
                        width: '90%',
                        maxWidth: '400px',
                        textAlign: 'center',
                    }}
                >
                    <img src={juggleStreetLogo} alt="juggleStreetLogo" width="200px" height="115px" />
                    <p style={{ color: '#585858', fontSize: "18px", fontWeight: "700", marginTop: "0px" }}>
                        {value < 80 ? "Generating Invoice..." : "Generating Invoice to approve"}
                    </p>
                    <ProgressBar
                        value={value}
                        style={{ height: '8px', backgroundColor: '#E0E0E0' }}
                        showValue={false} // Hide percentage value
                        className="custom-progress-bar"
                    />
                    <style>
                        {`
                            .custom-progress-bar .p-progressbar-value {
                                background: linear-gradient(90deg, #FFA500 0%, #37A950 100%) !important;
                            }
                        `}
                    </style>
                    <p style={{ color: '#585858', marginTop: '10px', fontSize: '16px', fontWeight: "300" }}>
                        Please wait one moment
                    </p>
                </div>
            </div>
        )
    );
};

export default GeneratingInvoice;