import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../../store';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../ConfirmationPopup';
import { RxUpdate } from 'react-icons/rx';
import { updateProfileActivationEnabled } from '../../../../store/slices/applicationSlice';
import { IframeBridge } from '../../../../services/IframeBridge';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';

const Chat: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);

  useEffect(() => {
    if (sessionInfo.data?.["profileCompleteness"] <= 99) {
      showConfirmationPopup(
        "Complete",
        `Your account must be 100% complete before proceeding.`,
        "Complete",
        <RxUpdate style={{ fontSize: "20px" }} />,
        () => {
          dispatch(updateProfileActivationEnabled(true));
        }
      );
      return; 
    }
    if (!inIframe) {
      const clientType = Number(utils.getCookie(CookiesConstant.clientType));
      const mainChatPath = clientType === 2 ? "/business-home/inAppChat" : "/parent-home/inAppChat";
      navigate(mainChatPath, { replace: true });
    }
    IframeBridge.sendToParent({
      type: "navigateConversations",
    });
  }, [dispatch, inIframe, navigate, sessionInfo.data, showConfirmationPopup]);

  return (
    <>
      <ConfirmationPopupGreen confirmationProps={confirmationProps} />
       {/* Show a loading spinner or a message while checks are performed */}
      {/* <div style={{ padding: '20px', textAlign: 'center', color: '#4b5563', backgroundColor: '#f3f4f6', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
        <i className="pi pi-spin pi-spinner" style={{ fontSize: '3.5rem', color: '#16a34a', marginBottom: '20px' }}></i>
        <h2 style={{ marginTop: '0px', color: '#1f2937', fontSize: '1.5rem' }}>Accessing Chat...</h2>
        <p style={{ fontSize: '0.9rem' }}>Please wait a moment.</p>
      </div> */}
    </>
  );
};

export default Chat;