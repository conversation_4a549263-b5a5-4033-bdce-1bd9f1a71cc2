import React from 'react';
import styles from '../commonStyle/header.module.css';
import logoImage from '../assets/images/juggle_white.png'; // Adjust the path to your image
import { Link } from 'react-router-dom';

interface HeaderProps {
    altText?: string;
}

const Header: React.FC<HeaderProps> = ({ altText = 'jugglestreet' }) => {
    return (
        <header className={styles.headerContainer}>
            <section className={styles.headerContent}>
                <Link to="/login">
                    {' '}
                    {/* Replace with your login page route */}
                    <img
                        loading="lazy"
                        src={logoImage}
                        alt={altText}
                        className={styles.headerImage}
                    />
                </Link>
            </section>
        </header>
    );
};

export default Header;
