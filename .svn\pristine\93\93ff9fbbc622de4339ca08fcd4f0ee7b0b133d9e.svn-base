import React, { useState } from "react";
import styles from "../styles/wwcc-details.module.css";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { ProgressBar } from "primereact/progressbar";
import add_round_duotone from "../../../assets/images/Icons/add_round_duotone.png";
import { IoClose } from "react-icons/io5";
import calender from "../../../assets/images/Icons/calender.png";
import editIcon from "../../../assets/images/Icons/editIcon.png";
import CustomButton from "../../../commonComponents/CustomButton";
import Service from "../../../services/services";
import useLoader from "../../../hooks/LoaderHook";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import removeIcon from "../../../assets/images/Icons/remove.png";
import { ConfirmationPopupRed, useConfirmationPopup } from "../../Common/ConfirmationPopup";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";

interface WWCCEntry {
  expiryDate: string;
  certificateNumber: string;
  certificateStatus: number;
  certificateTypeId: number;
  category: number;
}

interface FormData {
  firstName: string;
  middleName: string;
  lastName: string;
  wwccNumber: string;
  expirationDate: Date | null;
}
interface WWCCDisplayEntry extends WWCCEntry {
  firstName: string;
  middleName: string;
  lastName: string;
}
const CERTIFICATE_STATUS = {
  1: "Pending Verification",
  2: "active",
  3: "invalid",
  4: "expired",
} as const;

type CertificateStatusType = keyof typeof CERTIFICATE_STATUS;

const WWCCForm: React.FC<{
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  onSave: () => void;
}> = ({ formData, setFormData, onSave }) => {
  const isFormValid = () => {
    return (
      formData.firstName.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.wwccNumber.trim() !== "" &&
      formData.expirationDate !== null
    );
  };
  const{isMobile}=useIsMobile()
  const handleNameChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof FormData
  ) => {
    const value = e.target.value;

    if (value === "" || /^[A-Za-z\s]+$/.test(value)) {
      handleNameInput(e, field);
      setFormData((prevState) => ({
        ...prevState,
        [field]: value,
      }));
    }
  };

  const handleNameInput = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof FormData
  ) => {
    const value = e.target.value;
    if (value === "" || /^[A-Za-z\s]+$/.test(value)) {
      setFormData((prevState) => ({
        ...prevState,
        [field]: value,
      }));
    }
  };

  const handleWWCCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if (value === "" || /^\d+$/.test(value)) {
      handleWWCCInput(e);
      setFormData((prevState) => ({
        ...prevState,
        wwccNumber: value,
      }));
    }
  };

  const handleWWCCInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d+$/.test(value)) {
      setFormData((prevState) => ({
        ...prevState,
        wwccNumber: value,
      }));
    }
  };

  return (
    <div>
      <div className={styles.wwccSettingsInputContainer}>
        <header className={styles.wwccHeaderSecond}> Add WWCC </header>
        <p
          style={{
            color: "#585858",
            fontSize: "14px",
            fontWeight: "600",
            marginTop: "3px",
          }}
        >
          Full name as it appears on WWCC
        </p>
        <div style={{ position: "relative", maxWidth:!isMobile ? "362px" : "100%" }}>
          <InputText
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={(e) => handleNameChange(e, "firstName")}
            placeholder=" "
            className="input-placeholder"
          />
          <label
            htmlFor="firstName"
            className={`label-name ${formData.firstName ? "label-float" : ""}`}
          >
            First Name*
          </label>
        </div>
      </div>

      <div className={styles.wwccSettingsInputContainer}>
        <div style={{ position: "relative", maxWidth:!isMobile ? "362px" : "100%" }}>
          <InputText
            id="middleName"
            name="middleName"
            value={formData.middleName}
            onChange={(e) => handleNameChange(e, "middleName")}
            placeholder=" "
            className="input-placeholder"
          />
          <label
            htmlFor="middleName"
            className={`label-name ${formData.middleName ? "label-float" : ""}`}
          >
            Middle Name
          </label>
        </div>
      </div>

      <div className={styles.wwccSettingsInputContainer}>
        <div style={{ position: "relative",  maxWidth:!isMobile ? "362px" : "100%" }}>
          <InputText
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={(e) => handleNameChange(e, "lastName")}
            placeholder=" "
            className="input-placeholder"
          />
          <label
            htmlFor="lastName"
            className={`label-name ${formData.lastName ? "label-float" : ""}`}
          >
            Last Name*
          </label>
        </div>
      </div>

      <div className={styles.wwccSettingsInputContainer}>
        <div style={{ position: "relative",  maxWidth:!isMobile ? "362px" : "100%" }}>
          <InputText
            id="wwccNumber"
            name="wwccNumber"
            value={formData.wwccNumber}
            onChange={handleWWCCChange}
            placeholder=" "
            className="input-placeholder"
          />
          <label
            htmlFor="wwccNumber"
            className={`label-name ${formData.wwccNumber ? "label-float" : ""}`}
          >
            WWCC Number*
          </label>
        </div>
      </div>

      <div className={styles.wwccSettingsInputContainer}>
        <div>
          {" "}
          <p className={styles.calendarDiv}>
            <img src={calender} alt="calender" height={"18px"} width={"16px"} />
            Expiration Date
          </p>
        </div>
        <Calendar
          value={formData.expirationDate}
          dateFormat="DD, dth 'of' MM"
          placeholder="Tap to Select"
          minDate={new Date()}
          onChange={(e) =>
            setFormData((prevState) => ({
              ...prevState,
              expirationDate: e.value,
            }))
          }
          className={isMobile &&  "w-full"}
        />
      </div>

      <div>
        <button
          onClick={onSave}
          className={styles.wwccSaveButton}
          disabled={!isFormValid()}
          style={{
            cursor: isFormValid() ? "pointer" : "not-allowed",
          }}
        >
          Save
        </button>
      </div>
      <p
        style={{
          fontSize: "16px",
          fontWeight: "400",
          color: "#585858",
          textWrap: "wrap",
          width:!isMobile ? "365px" : "100%",
        }}
      >
        NOTE - All helpers 18 years and older are required to have a WWCC to use
        Juggle Street.
      </p>
    </div>
  );
};

const WWCCDetails: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentEditIndex, setCurrentEditIndex] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const { enableLoader, disableLoader } = useLoader();
  const {isMobile}=useIsMobile()
  const [formData, setFormData] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    wwccNumber: "",
    expirationDate: null,
  });

  const [entries, setEntries] = useState<WWCCDisplayEntry[]>([]);

  const transformFormDataToEntry = (data: FormData): WWCCEntry => {
    return {
      expiryDate:
        data.expirationDate?.toISOString() || new Date().toISOString(),
      certificateNumber: data.wwccNumber,
      certificateStatus: 1,
      certificateTypeId: 1,
      category: 1,
    };
  };

  const handleSave = () => {
    const newDisplayEntry: WWCCDisplayEntry = {
      ...transformFormDataToEntry(formData),
      firstName: formData.firstName,
      middleName: formData.middleName,
      lastName: formData.lastName,
    };

    if (currentEditIndex !== null) {
      setEntries((prevEntries) => {
        const updatedEntries = [...prevEntries];

        newDisplayEntry.certificateStatus =
          updatedEntries[currentEditIndex].certificateStatus;
        updatedEntries[currentEditIndex] = newDisplayEntry;
        return updatedEntries;
      });
    } else {
      setEntries((prevEntries) => [...prevEntries, newDisplayEntry]);
    }

    setFormData({
      firstName: "",
      middleName: "",
      lastName: "",
      wwccNumber: "",
      expirationDate: null,
    });
    setCurrentEditIndex(null);
    setIsVisible(false);
  };

  const handleEdit = (index: number) => {
    const entry = entries[index];
    setFormData({
      firstName: entry.firstName,
      middleName: entry.middleName,
      lastName: entry.lastName,
      wwccNumber: entry.certificateNumber,
      expirationDate: new Date(entry.expiryDate),
    });
    setCurrentEditIndex(index);
    setIsVisible(true);
  };

  const handleDelete = (index: number) => {
    showConfirmationPopup(
      "Remove Entry",
      "Are you sure you want to remove this entry?",
      "Remove",
      <img
        src={removeIcon}
        alt="remove icon"
        style={{ height: "15px", width: "13.33px" }}
      />,
      () => {
        const updatedEntries = entries.filter((_, i) => i !== index);
        setEntries(updatedEntries);
      }
    );
  };


  const handlePrev = () => {
    dispatch(decrementProfileActivationStep());
  };

  const handleNext = async () => {
    try {
      const apiEntries = entries.map((entry) => ({
        expiryDate: entry.expiryDate,
        certificateNumber: entry.certificateNumber,
        certificateStatus: entry.certificateStatus,
        certificateTypeId: entry.certificateTypeId,
        category: entry.category,
      }));
      enableLoader();
      const payload = {
        ...sessionInfo.data,
        certificates: apiEntries,
      };
      

      await Service.updateSessionInfo(
        payload,
        (newSession) => {
          setEntries(entries);
          dispatch(createNewSessionInfo(newSession));
          disableLoader();
          // dispatch(incrementProfileActivationStep());
        },
        (error) => {
          disableLoader();
          dispatch(createNewSessionInfo());
          console.error("Error updating WWCC details:", error);
        }
      );
    } catch (error) {
      console.error("Error in handleNext:", error);
    }
  };
  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  return (
    <div className={!isMobile ? `${styles.wwccContainer}` : `${styles.wwccContainerMobile}`}>
      <ConfirmationPopupRed confirmationProps={confirmationProps} />

      <ProfileCompletenessHeader
        title="WWCC Details"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div style={{paddingInline:isMobile && "15px", marginTop:isMobile && "30px"}}>
        {entries.map((entry, index) => (
          <div key={index} className={styles.wwccEntry}>
            <div className={styles.wwccSettingsInputContainer}>
              <div style={{ position: "relative",  maxWidth:!isMobile ? "362px" : "100%" }}>
                <InputText
                  id={`wwccNumber-${index}`}
                  name="wwccNumber"
                  readOnly
                  value={entry.certificateNumber}
                  onChange={(e) => {
                    const updatedEntries = [...entries];
                    updatedEntries[index].certificateNumber = e.target.value;
                    setEntries(updatedEntries);
                  }}
                  placeholder=" "
                  className="input-placeholder"
                />
                <label
                  htmlFor={`wwccNumber-${index}`}
                  className={`label-name ${entry.certificateNumber ? "label-float" : ""
                    }`}
                >
                  WWCC Number*
                </label>
              </div>
            </div>
            <div className={styles.wwccSettingsInputContainer}>
              <div style={{ position: "relative", maxWidth:!isMobile ? "362px" : "100%" }}>
                <InputText
                  id="expirationDate"
                  name="expirationDate"
                  readOnly
                  value={
                    entry.expiryDate
                      ? new Date(entry.expiryDate).toISOString().split("T")[0] // Ensure ISO format
                      : ""
                  }
                  onChange={(e) => {
                    const updatedEntries = [...entries];
                    updatedEntries[index].expiryDate = e.target.value; // Store as a string
                    setEntries(updatedEntries);
                  }}
                  placeholder=" "
                  className="input-placeholder"
                 
                />

                <label
                  htmlFor="expirationDate"
                  className={`label-name ${entry.expiryDate ? "label-float" : ""
                    }`}
                >
                  Expiration Date*
                </label>
              </div>
            </div>
            <div className={styles.wwccSettingsInputContainer}>
              <div style={{ position: "relative",  maxWidth:!isMobile ? "362px" : "100%"}}>
                <InputText
                  id={`Status-${index}`}
                  readOnly
                  name="Status"
                  value={
                    CERTIFICATE_STATUS[
                    entry.certificateStatus as CertificateStatusType
                    ] || "Unknown"
                  }
                  onChange={(e) => { }}
                  placeholder=" "
                  className="input-placeholder"
                />
                <label
                  htmlFor={`Status-${index}`}
                  className={`label-name ${entry.certificateStatus ? "label-float" : ""
                    }`}
                >
                  Status
                </label>
              </div>
            </div>

            <div className={styles.buttonContainer}>
              <button
                className={styles.editBtn}
                onClick={() => handleEdit(index)}
              >
                <img
                  src={editIcon}
                  alt="Edit WWCC"
                  style={{ marginRight: "5px", width: "13px", height: "13px" }}
                />
                Edit WWCC
              </button>

              <button
                className={styles.removeBtn}
                onClick={() => handleDelete(index)}
              >
                Remove
              </button>
            </div>
          </div>
        ))}
      </div>
      <div style={{paddingInline:isMobile && "15px"}}>
        <p style={{ fontSize: "16px", fontWeight: "400", color: "#585858" }}>
          NOTE - All helpers 18 years and older are required to have a WWCC to
          use Juggle Street.
        </p>
        {entries.length === 0 && (
          <button
            className={styles.wwccAddButton}
            onClick={() => setIsVisible(true)}
          >
            <img
              src={add_round_duotone}
              alt="add_round_duotone"
              width={33}
              height={34}
            />
            Add WWCC Details
          </button>
        )}
      </div>
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left" />
              Previous
            </>
          }
          onClick={handlePrev}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
             marginTop : isMobile && "5px"
          }}
          disabled={isLoading}
        />
        <div style={{ flexGrow: 1 }} />
        <CustomButton
          className={styles.hoverClass}
          data-skip={entries.length > 0 ? "false" : "true"}
          onClick={entries.length > 0 ? handleNext : handleSkip}
          label={
            <>
              {entries.length > 0 ? "Next" : "Skip"}
              <i className="pi pi-angle-right" style={{ marginLeft: "8px" }} />
            </>
          }
          style={
            entries.length > 0
              ? {
                backgroundColor: "#FFA500",
                color: "#fff",
                width: "156px",
                height: "39px",
                fontWeight: "800",
                fontSize: "14px",
                borderRadius: "8px",
                border: "2px solid transparent",
                boxShadow: "0px 4px 12px #00000",
                transition:
                  "background-color 0.3s ease, box-shadow 0.3s ease",
                 margin : isMobile && "10px"
              }
              : {
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontWeight: "400",
                fontSize: "14px",
                borderRadius: "10px",
                border: "1px solid #F0F4F7",
                margin : isMobile && "10px"
              }
          }
          disabled={isLoading}
        />
      </footer>
      <Dialog
        visible={isVisible}
        onHide={() => {
          setIsVisible(false);
          setCurrentEditIndex(null);
        }}
        content={
          <div className={styles.wwccDialogContent}>
            <IoClose
              onClick={() => {
                setIsVisible(false);
                setCurrentEditIndex(null);
              }}
              className={styles.CloseBtn}
            />
            <WWCCForm
              formData={formData}
              setFormData={setFormData}
              onSave={handleSave}
            />
          </div>
        }
      />
    </div>
  );
};

export default WWCCDetails;
