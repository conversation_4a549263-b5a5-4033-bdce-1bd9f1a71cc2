
  
  .imageContainer {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid orange;
  }
  
  .placeholder {
    width: 60%;
    height: 60%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .previewImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
  
  .uploadButton {
    margin-top: 10px;
    display: inline-block;
    background-color: #fff;
    border: 1px solid #ddd;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 14px;
  }
  
  .uploadButton input[type="file"] {
    display: none;
  }
  
  .instructionText {
    margin-top: 8px;
    font-size: 12px;
  }
  
  .instructionText a {
    color: blue;
    text-decoration: underline;
  }
  
  /* Media queries for responsiveness */
  @media (max-width: 600px) {
    .imageContainer {
      width: 120px;
      height: 120px;
    }
  
    .uploadButton {
      font-size: 12px;
      padding: 6px 12px;
    }
  
    .instructionText {
      font-size: 10px;
    }
  }
  
  @media (min-width: 601px) and (max-width: 1200px) {
    .imageContainer {
      width: 140px;
      height: 140px;
    }
  
    .uploadButton {
      font-size: 13px;
      padding: 7px 14px;
    }
  
    .instructionText {
      font-size: 11px;
    }
  }
  .circleImage {
    border-radius: 50%;
    object-fit: cover;
    width: 100%; /* Adjust this as necessary for your design */
    height: 100%; /* Ensures the image stays within a square container */
  }
  
  .cropSection {
    width: 200px; /* Adjust for the crop size you want */
    height: 200px;
    position: relative;
  }
  .circleImage {
    border-radius: 50%;
    width: 100px; /* adjust the width as needed */
    height: 100px; /* adjust the height as needed */
    object-fit: cover;
}


.upload-img{
  background-color: 'white';
  color: '#585858';
  box-shadow:
      '0 4px 4px 0 rgba(0, 0, 0, 0.25)';
  font-size: '13px';
  font-weight: '600';
  padding: '6px';
  padding-left: '20px';
  padding-right: '20px';
  margin-left: '20%';
  position:'relative';
  top: '50px';
  border-radius: '8px';
  display: 'flex';
  justify-content: 'center';
  align-items: 'center';
  width:'152px';
  text-wrap:'nowrap';
}
.upload-img:hover{
  border-bottom: 2px solid #585858;
}