import React, { useState } from 'react';
import styles from "../Common/styles/parent-home.module.css";
import '../Common/styles/welcome-component.css'; // Assuming this contains .progress-container and .progress-label styles
import juggleLogo from "../../assets/images/juggle_white.png";
import ArrowIcon from "../../assets/images/arrow.png"; // Adjust path as needed
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import Service from '../../services/services';
import HomeHeaderHelper from './Components/HomeHeaderHelper';

const HelperNewHome: React.FC = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [showButton, setShowButton] = useState(true); // State to toggle button/message
    // Define profileCompletion based on sessionInfo
    const profileCompletion = sessionInfo.loading
        ? 70
        : sessionInfo.data?.["profileCompleteness"] || 0;

    // Define reverseWhen (e.g., flip arrow at 50%)
    const reverseWhen = 50;


    return (
        <>        <HomeHeaderHelper />
        <div className={`${styles.stylehelpercontainer} w-full`}>
              
            <div className={styles.curveContainer}>
                <div className="flex justify-content-center items-center align-items-center">
                    <img src={juggleLogo} alt="juggleLogo" width={265} height={53} />
                </div>
                <div className={styles.curveDivFirst}>
                    <div style={{
                        border: !showButton ? "3px solid #179D52" : "none",
                    }} className={styles.curveDiv}>
                        <div className="w-9">
                            <h2
                                style={{
                                    fontSize: "60px",
                                    color: "#179D52",
                                    textAlign: "center",
                                    margin: "0"
                                }}
                            >
                                Welcome to Juggle Street, {sessionInfo.data?.["firstName"]}!
                            </h2>
                        </div>
                        <div className={styles.smallDiv}>
                            <p
                                style={{
                                    textAlign: "center",
                                    fontSize: "18px",
                                    color: "#585858", // Fixed typo from #58585
                                    fontWeight: "500"
                                }}
                                className="w-9"
                            >
                                Thank you for registering, please click Continue below and we’ll send you an SMS to open Juggle Street on your mobile.
                            </p>
                        </div>
                        <div className={styles.smallDiv}>
                            {showButton ? (
                               <button
                               className={styles.continueButton}
                               onClick={() => {
                                   Service.sendMobileLink(
                                       () => setShowButton(false), // Success callback
                                       (error) => console.error("Failed to send mobile link:", error) // Failure callback
                                   );
                               }}
                           >
                               Continue on Mobile
                           </button>
                            ) : (
                                <div
                                    style={{
                                        textAlign: "center",
                                        fontSize: "10px",
                                        backgroundColor: "#179D52",
                                        fontWeight: "800",
                                        display: "flex",
                                        color: "#fff",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        borderRadius: "20px",
                                        paddingInline: "10px",
                                        paddingBlock: "5px",

                                    }}
                                >
                                    <span
                                        style={{
                                            display: "inline-flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            width: "20px",
                                            height: "20px",
                                            borderRadius: "50%",
                                            backgroundColor: "#FFFFFF",
                                            marginRight: "10px",
                                            border: "2px solid #179D52",
                                        }}
                                    >
                                        <span
                                            style={{
                                                color: "#179D52",
                                                fontSize: "14px",
                                                lineHeight: "30px",
                                            }}
                                        >
                                            ✓
                                        </span>
                                    </span>
                                    We've sent an SMS to open Juggle Street on your mobile
                                </div>
                            )}
                        </div>
                        <div
                            className="progress-container"
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '75%', // Adjusted to match previous example
                                padding: '0',
                                marginBlock: "25px"
                            }}
                        >
                            <div
                                style={{
                                    width: '100%',
                                    height: '3px',
                                    backgroundColor: '#D9D9D9',
                                    position: 'relative',
                                }}
                            >
                                <div
                                    style={{
                                        width: `${profileCompletion}%`,
                                        height: '3px',
                                        backgroundColor: '#179D52',
                                        top: 0,
                                        left: 0,
                                    }}
                                />
                            </div>
                            <div
                                className="progress-label"
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    flexDirection: profileCompletion >= reverseWhen ? 'row-reverse' : 'row',
                                    marginTop: '-5px',
                                    textWrap: 'nowrap',
                                    fontWeight: '700',
                                    color: '#179D52',
                                }}
                            >
                                <div
                                    style={{
                                        width: `${profileCompletion >= reverseWhen
                                            ? 100 - profileCompletion - 1.5
                                            : profileCompletion - 1.5
                                            }%`,
                                    }}
                                />
                                <img
                                    src={ArrowIcon}
                                    alt="arrow" // Fixed typo from "attor"
                                    style={{
                                        marginRight: '5px',
                                        transform: profileCompletion >= reverseWhen
                                            ? 'rotateY(180deg)'
                                            : 'rotateY(0deg)',
                                    }}
                                />
                                <p>Your profile is {profileCompletion}% complete</p>
                            </div>
                        </div>
                        <div style={{ height: '20px' }} /> {/* Optional spacing */}
                    </div>
                </div>
            </div>
        </div>
        </>

    );
};

export default HelperNewHome;