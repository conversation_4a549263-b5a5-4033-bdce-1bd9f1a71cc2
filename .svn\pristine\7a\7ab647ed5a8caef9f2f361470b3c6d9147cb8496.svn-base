import { TabPanel, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useState } from "react";
const TimeSheet: React.FC<{
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;

}> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        Awaiting your <br /> Confirmation
      </div>
      }
      >
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Awaiting your Confirmation Timesheets</div>
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        Parent-adjusted <br /> Timesheets
      </div>
      }
      >
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Parent-adjusted Timesheets</div>
      </TabPanel>
      <TabPanel header={<div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.4' }}>
        Awaiting Parent <br />Approval </div>}>
        <div style={{ padding: '20px', textAlign: 'center', color: lightGrayText, backgroundColor: screenBg, height: '100%' }}>Content for Awaiting Parent Approval</div>
      </TabPanel>
    </TabView>
  );
};

export default TimeSheet;