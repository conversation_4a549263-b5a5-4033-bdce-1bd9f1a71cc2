import React, { CSSProperties, ReactNode, useState } from 'react';
import { FaRegHeart } from 'react-icons/fa6';
import { FiPlayCircle } from 'react-icons/fi';
import { RiCloseLargeLine } from 'react-icons/ri';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { GrHide } from 'react-icons/gr';
import useLoader from '../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import { updateProfileActivationEnabled } from '../../../store/slices/applicationSlice';
import { RxUpdate } from 'react-icons/rx';
import c from '../../../helper/juggleStreetConstants';
import Service from '../../../services/services';
import { UserResult } from '../../../hooks/SearchGeoSearchHook';
import HelperActionCard from '../../Common/HelperActionCard';
import { useLocation, useNavigate } from 'react-router-dom';
import utils from '../../../components/utils/util';
import CookiesConstant from '../../../helper/cookiesConst';
import styles from '../../Helper/styles/home.module.css';

import '../../Common/styles/helper-card.css';
import useIsMobile from '../../../hooks/useIsMobile';
interface Items {
    icon: React.JSX.Element;
    description: React.JSX.Element;
}
interface helperhomeProps extends UserResult {
    refresh: () => void;
}
interface ClientCardProps {
    helperName: string;
    businessTag?: ReactNode;
    imgSrc: string;
    mainDivStyles?: CSSProperties;
    titleFontSize?: number;
    items: Items[];
    isSuperHelper?: boolean;
    hasProfileVideo?: boolean;
    friendStatus: number;
}
function ClientCard({
    helperName,
    imgSrc,
    mainDivStyles,
    items,
    titleFontSize,
    isSuperHelper,
    hasProfileVideo,
    friendStatus,
    refresh,
    id,
    requestId,
}: ClientCardProps & helperhomeProps) {
    const navigate = useNavigate();
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const [dialogVisible, setDialogVisible] = useState(false);
    const { enableLoader, disableLoader } = useLoader();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const { isMobile } = useIsMobile();
    const location = useLocation();

    const redirectAfterHome = () => {
        const searchParams = new URLSearchParams();
        searchParams.set('id', id.toString());
        // if (requestId) {
        //     searchParams.set('requestId', requestId.toString());
        // }
        const clientType = utils.getCookie(CookiesConstant.clientType);

        // Conditionally navigate based on clientType
        if (clientType === '2') {
            navigate(`/business-home/provider-profile?${searchParams.toString()}`);
        } else if (clientType === '1') {
            navigate(`/parent-home/provider-profile?${searchParams.toString()}`);
        } else {
            navigate(`/helper-home/client-profile?${searchParams.toString()}`);
        }
    };

    const handleXClicked = () => {
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                helperName,
                `Your profile must be 100% complete before you can connect with ${helperName}`,
                'Complete',
                <RxUpdate />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        if (friendStatus === 4) {
            setDialogVisible(true);
        }
    };

    const handleFavClicked = () => {
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                helperName,
                `Your profile must be 100% complete before you can connect with ${helperName}`,
                'Complete',
                <RxUpdate />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        if (friendStatus === 4) {
            setDialogVisible(true);
        }
    };

    const handleAddToFavorites = () => {
        setDialogVisible(false);
        // enableLoader();
        let newStatus = friendStatus;
        if (friendStatus === c.friendStatus.NONE) {
            newStatus = c.friendStatus.PENDING;
        } else if (friendStatus === c.friendStatus.PENDING) {
            newStatus = c.friendStatus.APPROVED;
        }
        const payload = {
            friendId: id,
            invitationMessage: '',
            status: newStatus,
        };
        Service.addFriend(
            payload,
            () => {

                setDialogVisible(false); // Close dialog on success
                disableLoader();
                refresh();
            },
            (error) => {
                console.error('Failed to add friend:', error);
                // Handle failure case
            }
        );
    };
    const handleHide = () => {
        showConfirmationPopup(
            helperName,
            `Are you sure you want to Hide ${helperName}?`,
            'Hide',
            <GrHide style={{ fontSize: '20px' }} />,
            () => {

                executeHideAction();
            }
        );
        return;
    };

    const executeHideAction = () => {
        setDialogVisible(false);
        let newStatus = c.friendStatus.HIDDEN;
        const payload = {
            friendId: id,
            invitationMessage: '',
            status: newStatus,
        };
        Service.addFriend(
            payload,
            () => {

                setDialogVisible(false);
                disableLoader();
                refresh();
            },
            (error) => {
                console.error('Failed to hide profile:', error);
            }
        );
    };
    const handleClosePopup = () => {
        setDialogVisible(false);
    };
    const handleAccept = () => {
        setDialogVisible(false);
        enableLoader();
        let newStatus = friendStatus;
        if (friendStatus === c.friendStatus.NONE) {
            newStatus = c.friendStatus.PENDING;
        } else if (friendStatus === c.friendStatus.PENDING) {
            newStatus = c.friendStatus.APPROVED;
        }
        const payload = {
            friendId: id,
            invitationMessage: '',
            status: newStatus,
        };
        Service.acceptRequest(
            payload,
            () => {

                setDialogVisible(false);
                disableLoader();
                refresh();
            },
            (error) => {
                console.error('Failed to add friend:', error);
                // Handle failure case
            },
            Number(requestId)
        );
    };
    const canAccept = () => {
        friendStatus === c.friendStatus.PENDING && this.UserResult.actionRequired
    }
    return !isMobile ? (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            <div
                className={`${styles.ClientCard} flex card flex-wrap`}
                style={{
                    ...mainDivStyles,
                    width: '100%',
                    height: '116px',
                }}
            >
                <div className='img'>
                    <img
                        src={imgSrc}
                        alt='Helper Image'
                        className='cursor-pointer'
                        style={{
                            width: '116px',
                            height: '116px',
                        }}
                        onClick={() => redirectAfterHome()}
                    />
                </div>
                {isSuperHelper && (
                    <div
                        className='absolute w-full flex justify-content-start pl-5'
                        style={{ bottom: '5px' }}
                    >
                        <div
                            className='text-white font-bold'
                            style={{
                                fontSize: '0.6rem',
                                backgroundColor: '#444444',
                                padding: '5px 10px',
                                paddingInline: '10px',
                                borderRadius: '30px',
                                userSelect: 'none',
                            }}
                        >
                            Super Helper
                        </div>
                    </div>
                )}
                {hasProfileVideo && (
                    <div className='absolute flex' style={{ top: '5px', left: '8px' }}>
                        <div
                            className='text-white font-bold'
                            style={{
                                fontSize: '1.6rem',
                                borderRadius: '30px',
                                userSelect: 'none',
                            }}
                        >
                            <FiPlayCircle />
                        </div>
                    </div>
                )}
                <div className='flex-grow-1 flex flex-column p-1 px-2' style={{ color: '#585858' }}>
                    <div className='flex justify-content-between align-items-center'>
                        <h4
                            className='m-0 font-bold'
                            style={{
                                fontSize: `${titleFontSize || 18}px`,
                            }}
                        >
                            {helperName}
                        </h4>
                        <div className='flex text-md gap-2'>
                            {friendStatus === 4 ? (
                                <>
                                    <FaRegHeart
                                        className='cursor-pointer'
                                        onClick={() => handleFavClicked()}
                                    />
                                    <RiCloseLargeLine
                                        className='cursor-pointer'
                                        onClick={() => handleXClicked()}
                                    />
                                </>
                            ) : (
                                <FaRegHeart
                                    className='cursor-pointer'
                                    style={{ color: 'red' }} // Show red heart when friendStatus is not 4
                                    onClick={() => handleFavClicked()}
                                />
                            )}
                        </div>
                    </div>

                    <div className='flex-grow-1 flex flex-column'>
                        {items.map((item, index) => (
                            <div
                                key={index}
                                className='flex align-items-center gap-2 text-sm font-semibold'
                            >
                                {item.icon}
                                {item.description}
                            </div>
                        ))}

                    </div>
                    {/* <span style={{
                    fontSize:'12px'
                }} >
                    Connection<br />
                    Invitation
                </span> */}
                    <>
                        {canAccept && location.pathname === '/helper-home/public/connections/invitations' && (
                            <button
                                className={styles['responsive-button']}
                                style={{
                                    backgroundColor: 'rgb(23, 157, 82)',
                                    borderRadius: '1rem',
                                    color: 'white',
                                    margin: '1rem',
                                    border: 'none',
                                    cursor: 'pointer',
                                }}
                                onFocus={(e) => (e.target.style.outline = 'solid rgb(23, 157, 82)')}
                                onBlur={(e) => (e.target.style.outline = 'none')}
                                onClick={() => handleAccept()}
                            >
                                ACCEPT
                            </button>)}</>
                    {friendStatus === 4 && (
                        <HelperActionCard
                            visible={dialogVisible}
                            onHide={handleHide}
                            onAddToFavorites={handleAddToFavorites}
                            name={helperName}
                            imageSrc={imgSrc}
                            onClose={handleClosePopup}
                        />
                    )}
                </div>
            </div>
        </>
    ) : (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            <div
                className={`${styles.ClientCard} flex card flex-wrap flex-column`}
                style={{
                    ...mainDivStyles,
                    width: '100%',
                    height: '100%',
                    alignItems: "center",
                    minHeight: "275px"
                }}
            >
                <div className='img'>
                    <img
                        src={imgSrc}
                        alt='Helper Image'
                        className='cursor-pointer'
                        style={{
                            width: '180px',
                            height: '180px',
                        }}
                        onClick={() => redirectAfterHome()}
                    />
                </div>
                {isSuperHelper && (
                    <div
                        className='absolute w-full flex justify-content-start pl-5'
                        style={{ bottom: '5px' }}
                    >
                        <div
                            className='text-white font-bold'
                            style={{
                                fontSize: '0.6rem',
                                backgroundColor: '#444444',
                                padding: '5px 10px',
                                paddingInline: '10px',
                                borderRadius: '30px',
                                userSelect: 'none',
                            }}
                        >
                            Super Helper
                        </div>
                    </div>
                )}
                {hasProfileVideo && (
                    <div className='absolute flex' style={{ top: '5px', left: '8px' }}>
                        <div
                            className='text-white font-bold'
                            style={{
                                fontSize: '1.6rem',
                                borderRadius: '30px',
                                userSelect: 'none',
                            }}
                        >
                            <FiPlayCircle />
                        </div>
                    </div>
                )}
                <div className='flex flex-column ' style={{ color: '#585858', width: "100%", paddingInline: "10px" }}>
                    <div className='flex justify-content-between align-items-center'>
                        <h4
                            className='m-0 font-bold'
                            style={{
                                fontSize: `${titleFontSize || 14}px`,
                            }}
                        >
                            {helperName}
                        </h4>
                        <div className='flex text-md gap-2'>
                            {friendStatus === 4 ? (
                                <>
                                    <FaRegHeart
                                        className='cursor-pointer'
                                        onClick={() => handleFavClicked()}
                                    />
                                    <RiCloseLargeLine
                                        className='cursor-pointer'
                                        onClick={() => handleXClicked()}
                                    />
                                </>
                            ) : (
                                <FaRegHeart
                                    className='cursor-pointer'
                                    style={{ color: 'red' }} // Show red heart when friendStatus is not 4
                                    onClick={() => handleFavClicked()}
                                />
                            )}
                        </div>
                    </div>

                    <div className='flex-grow-1 flex flex-column gap-1'>
                        {items.map((item, index) => (
                            <div
                                key={index}
                                className='flex align-items-center gap-2 text-sm font-semibold'
                            >
                                {item.icon}
                                {item.description}
                            </div>
                        ))}

                    </div>
                    {/* <span style={{
                    fontSize:'12px'
                }} >
                    Connection<br />
                    Invitation
                </span> */}
                    <>
                        {canAccept && location.pathname === '/helper-home/public/connections/invitations' && (
                            <button
                                className={styles['responsive-button']}
                                style={{
                                    backgroundColor: 'rgb(23, 157, 82)',
                                    borderRadius: '1rem',
                                    color: 'white',
                                    margin: '1rem',
                                    border: 'none',
                                    cursor: 'pointer',
                                }}
                                onFocus={(e) => (e.target.style.outline = 'solid rgb(23, 157, 82)')}
                                onBlur={(e) => (e.target.style.outline = 'none')}
                                onClick={() => handleAccept()}
                            >
                                ACCEPT
                            </button>)}</>
                    {friendStatus === 4 && (
                        <HelperActionCard
                            visible={dialogVisible}
                            onHide={handleHide}
                            onAddToFavorites={handleAddToFavorites}
                            name={helperName}
                            imageSrc={imgSrc}
                            onClose={handleClosePopup}
                        />
                    )}
                </div>
            </div>
        </>
    )
}

export default ClientCard;
