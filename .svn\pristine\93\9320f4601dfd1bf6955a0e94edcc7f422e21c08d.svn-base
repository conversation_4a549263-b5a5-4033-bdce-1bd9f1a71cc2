import { createAsyncThunk } from "@reduxjs/toolkit";
import Auth from "../../services/authService";
import utils from "../../components/utils/util";
import { SessionInfoType } from "../types";
import Service from "../../services/services";
import CookiesConstant from "../../helper/cookiesConst";

type ErrorResponse = {
  message: string;
  name?: string;
  stack?: string;
};

export const fetchSessionInfo = createAsyncThunk<SessionInfoType, void, { rejectValue: ErrorResponse }>(
  "sessionInfo/check_session",
  async (_, { rejectWithValue }) => {
    try {
      const token = utils.getCookie(CookiesConstant.accessToken);
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Auth.checkSession(token, resolve, reject);
      });
      const expires = new Date();
      expires.setFullYear(new Date().getFullYear() + 1);
      utils.setCookie(CookiesConstant.clientType, response["client"]["clientType"], expires);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);
export const fetchNoRefreshSessionInfo = createAsyncThunk<SessionInfoType, void, { rejectValue: ErrorResponse }>(
  "sessionInfo/check_session_no_refresh",
  async (_, { rejectWithValue }) => {
    try {
      const token = utils.getCookie("jugglestreet-access-token");
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Auth.checkSession(token, resolve, reject);
      });
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);

export const updateSessionInfo = createAsyncThunk<SessionInfoType, { payload: object }, { rejectValue: ErrorResponse }>(
  "sessionInfo/updateSessionInfo",
  async ({ payload }, { rejectWithValue }) => {
    try {
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Service.updateSessionInfo(payload, resolve, reject);
      });
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);

export const updateUser = createAsyncThunk<SessionInfoType, { payload: object }, { rejectValue: ErrorResponse }>(
  "sessionInfo/updateUser",
  async ({ payload }, { rejectWithValue }) => {
    try {
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Service.updateUser(payload, resolve, reject);
      });
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);

export const refreshAccount = createAsyncThunk<SessionInfoType, void, { rejectValue: ErrorResponse }>(
  "sessionInfo/refreshAccount",
  async (_, { rejectWithValue }) => {
    try {
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Service.refreshAccount(resolve, reject);
      });
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);

export const updateProfileVisibility = createAsyncThunk<SessionInfoType, { payload: object }, { rejectValue: ErrorResponse }>(
  "sessionInfo/updateProfileVisibility",
  async ({ payload }, { rejectWithValue }) => {
    try {
      const response: SessionInfoType = await new Promise((resolve, reject) => {
        Service.updateProfileVisibility(payload, resolve, reject);
      });
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue({
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
      return rejectWithValue({ message: "An unknown error occurred" });
    }
  }
);
