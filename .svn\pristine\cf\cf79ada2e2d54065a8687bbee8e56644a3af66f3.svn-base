import  { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom';
import SidePannel from './SidePannel';
import environment from '../../../helper/environment';
import HomeHeaderHelper from './HomeHeaderHelper';
import useLoader from '../../../hooks/LoaderHook';
import c from '../../../helper/juggleStreetConstants';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import useIsMobile from '../../../hooks/useIsMobile';
const HelperHowItWorks = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [frameSrc, setFrameSrc] = useState<string>('');
    const [searchParams] = useSearchParams();
    const { disableLoader, enableLoader } = useLoader();
    const{isMobile}=useIsMobile();
    useEffect(() => {
        enableLoader();
        const link = `https://${environment.getMarketingRoot(window.location.hostname)}/helpers?hideLayout=1&loggedIn=1`
        setFrameSrc(link);
    }, []);
    const handleIframeLoad = () => {
        disableLoader();
      };
      const activeIndex = sessionInfo?.data?.['accountStatus'] === c.accountStatus?.APPROVED ? 6 : 2;
    return (

        <div style={{ width: '100%', height: '100vh' }}>
            <SidePannel activeindex={activeIndex} />
            <HomeHeaderHelper />
            <iframe
                src={frameSrc}
                frameBorder="0"
                style={{ width:!isMobile ? 'calc(100% - 250px)' : "100%", height: '100%', position: 'absolute', right: 0 }}
                onLoad={handleIframeLoad}
            />
        </div>
    );
}
export default HelperHowItWorks