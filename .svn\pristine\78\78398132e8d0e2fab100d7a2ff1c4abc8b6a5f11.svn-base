import { Crisp } from "crisp-sdk-web";

import env from "../helper/environment";

var intercomManager = {
  configure() {
    Crisp.setSafeMode(true);
    Crisp.configure(env.getHelpDeskId(), {
      autoload: true,
    });
  },

  boot(userInfo) {
    if ((<any>window).app.adminMode) return;

    Crisp.user.setEmail(userInfo.email, userInfo.helpDeskHash);
    const name = userInfo.firstName + " " + userInfo.lastName;
    Crisp.user.setNickname(name);
  },

  hide() {
    if ((<any>window).app.adminMode) return;

    Crisp.chat.close();
  },

  show() {
    if ((<any>window).app.adminMode) return;

    Crisp.chat.open();
  },

  shutdown() {
    if ((<any>window).app.adminMode) return;

    Crisp.session.reset();
  },
};

export default intercomManager;
