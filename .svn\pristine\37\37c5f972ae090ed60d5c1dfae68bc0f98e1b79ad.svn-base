import env from './environment';
import { HttpLink } from './httpLink';

const httpLink = new HttpLink();
const ClientAPIRequest = {
    baseURL: () => env.getServiceURL(window.location.hostname),
    makeRequest: function (url, method, data, callback, failureCallback: any = this.handleError) {
        if (url.indexOf('http') !== 0) {
            url = this.baseURL() + url;
        }

        const config = {
            url,
            method,
            data,
        };
        return httpLink.request(config, callback, failureCallback);
    },
};

export default ClientAPIRequest;
