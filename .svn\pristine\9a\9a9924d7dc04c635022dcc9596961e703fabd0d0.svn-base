import { useState, useEffect, useRef } from 'react';
import '../Common/styles/custom-homepage-card.css';
import JuggleMaps from './JuggleMaps';
import { AppDispatch, RootState } from '../../store';
import { useDispatch, useSelector } from 'react-redux';
import ProfileCompleteCongoLoader from '../Common/ProfileCompleteCongoLoader';
import CustomHomepageCard from '../Common/CustomHomepageCard';
import LeftHandUserPanel from '../Common/LeftHandUserPanel';
import WelcomeComponent from '../Common/WelcomeComponent';
import HelpdeskManager from '../../commonComponents/HelpdeskManager';
import { LuPackageCheck, LuSmilePlus } from 'react-icons/lu';
import { HiOutlineBookOpen } from 'react-icons/hi';
import { UserSearchResponse, useSearchHook } from '../../hooks/SearchGeoSearchHook';
import useLoader from '../../hooks/LoaderHook';
import HomeHeader from '../Common/HomeHeader';
import { FiMap } from 'react-icons/fi';
import { OverlayPanel } from 'primereact/overlaypanel';
import { Divider } from 'primereact/divider';
import AllFilters, {
    defaultFiltersOddJobs,
    FiltersInHomeTutorsHighSchool,
    FiltersInHomeTutorsPrimarySchool,
    FiltersInOnlineTutorsHighSchool,
    FiltersInOnlineTutorsPrimarySchool,
} from '../Common/AllFilters';
import menuIcon from '../../assets/images/Icons/Filter_alt.png';
import juggleLogo from '../../assets/images/juggle_white.png';
import styles from '../Common/styles/parent-home.module.css';
import {
    JobCategory,
    JobsCompletedFilter,
    JobTypeFilters,
    ReviewFilter,
    TutoringJobTypeFilter,
    useFilterHook,
} from '../../hooks/useFilterHook';
import AgedCarePopup from './AgedCarePopup ';
import {
    toggleSideBar,
    updatehasRequestedEmployeeBenefits,
    updateinterestInHomeAgedCareResponse,
    updateProfileActivationEnabled,
} from '../../store/slices/applicationSlice';
import c from '../../helper/juggleStreetConstants';
import utils from '../../components/utils/util';
import EmployeeBenefits from '../Common/EmployeeBenefits';
import useIsMobile from '../../hooks/useIsMobile';
import { useNavigate } from 'react-router-dom';
import { RxUpdate } from 'react-icons/rx';
import CookiesConstant from '../../helper/cookiesConst';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../Common/ConfirmationPopup';
import ShimmerHelperCard from '../Common/ShimmerHelperCard';
import { IframeBridge } from '../../services/IframeBridge';

const ParentHome = () => {
    const [profileCardDivWidth, setProfileCardDivWidth] = useState<number>(0);
    const [profileCardCol, setProfileCardCol] = useState<
        'col-12' | 'col-6' | 'col-4' | 'col' | 'col-3' | 'col-2'
    >('col');
    const [showProfileCompletenessDialog, setShowProfileCompletenessDialog] = useState(true);
    const { enableLoader, disableLoader } = useLoader();
    const [useSearchResponse, setUseSearchResponse] = useState<UserSearchResponse>(null);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const applicationState = useSelector((state: RootState) => state.applicationState);
    const { inIframe } = useSelector((state: RootState) => state.applicationState);

    const dispatch = useDispatch<AppDispatch>();
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const hidePopup = () => dispatch(updateinterestInHomeAgedCareResponse(false));
    const [showEmployeeBenefitsDialog, setShowEmployeeBenefitsDialog] = useState<boolean>(true);
    const hidePopupEmployee = () => {
        setShowEmployeeBenefitsDialog(false);
        dispatch(updatehasRequestedEmployeeBenefits(false));
    };
    const { sideBarIsOpened: sidebarIsOpen, shouldShowProfileActivation } = useSelector(
        (state: RootState) => state.applicationState
    );
    const [numberOfRows, setNumberOfRows] = useState<number>(0);
    const profileCardDivRef = useRef<HTMLDivElement>(null);
    const [showBackToMapView, setShowBackToMapView] = useState<boolean>(false);
    const op1 = useRef<OverlayPanel>(null);
    const op2 = useRef<OverlayPanel>(null);
    const op3 = useRef<OverlayPanel>(null);
    const isFirstRender = useRef(true);
    const { defaultFilters } = useSelector((state: RootState) => state.applicationState);

    const [showAllFilters, setShowAllFilters] = useState<boolean>(false);
    const [isAbove1024, setIsAbove1024] = useState(window.innerWidth > 1024); // New state
    const { isMobile } = useIsMobile();
    const [filtersApplied, setFiltersApplied] = useState<boolean>(false);
    const navigate = useNavigate();
    const resetFiltersRef = useRef(null);
    const { updateFilters, refreshSearchResult } = useSearchHook((data) => {
        setUseSearchResponse((prev) => {
            if (!data || !data.results) {
                disableLoader();
                return prev;
            }

            if (data.results.length === 0) {
                disableLoader();
                return prev;
            }

            const prevResultsMap = new Map((prev?.results || []).map((item) => [item.id, item]));
            let resultsChanged = false;

            const newResults = data.results.map((newItem) => {
                const existingItem = prevResultsMap.get(newItem.id);

                if (!existingItem) {
                    resultsChanged = true;
                    return newItem;
                }

                if (JSON.stringify(existingItem) !== JSON.stringify(newItem)) {
                    resultsChanged = true;
                    return newItem;
                }

                return existingItem;
            });

            if (!resultsChanged) {
                disableLoader();
                return prev;
            }

            const finalResults = [...(prev?.results || [])];
            newResults.forEach((newItem) => {
                const existingIndex = finalResults.findIndex((item) => item.id === newItem.id);
                if (existingIndex === -1) {
                    finalResults.push(newItem);
                } else {
                    finalResults[existingIndex] = newItem;
                }
            });

            return {
                ...prev,
                results: finalResults,
                total: data?.total || prev?.total || 0,
            };
        });
        disableLoader();
    });

    const shouldShowEmployeeBenefits = (): boolean => {
        // Preliminary checks from initiateEmployeeBenefitsRequest
        if (
            (window as any).app?.adminMode ||
            window.location.pathname.includes('employee-benefits')
        ) {
            return false;
        }

        const status = sessionInfo.data?.['employeeBenefitsReferralStatus'];
        if (
            status === c.employeeBenefitsReferralStatus.INITIATED ||
            status === c.employeeBenefitsReferralStatus.CANCELLED ||
            status === c.employeeBenefitsReferralStatus.REQUESTED
        ) {
            return false;
        }

        const completedJobs =
            sessionInfo.data?.['client']['jobsCompleted'] >= 1 ||
            sessionInfo.data?.['provider']['jobsCompleted'] >= 1;
        const promptNow = completedJobs;

        if (!promptNow) {
            return false;
        }

        let notify = false;
        if (status === c.employeeBenefitsReferralStatus.NONE) {
            notify = true;
        } else if (status === c.employeeBenefitsReferralStatus.DELAYED) {
            const dayDiff = utils.calculateDateDifference(
                new Date(),
                new Date(sessionInfo.data?.['employeeBenefitsActionDate']),
                'days'
            );
            notify = dayDiff >= 14;
        }
        return notify;
    };
    const conditions = shouldShowEmployeeBenefits();

    const {
        selectedJob,
        jobTypeFilters,
        reviewFilter,
        jobsCompletedFilter,
        isApplyButtonDisabled,
        getJobTypeOptions,
        handleJobSelect,
        handleJobTypeChange,
        handleReviewFilterChange,
        handleJobsCompletedFilterChange,
        handleApplyJobTypeFilters,
    } = useFilterHook({
        defaultFilters,
        onFilterChange: (filters) =>
            updateFilters((prev) => ({
                ...prev,
                pageIndex: 1,
                filters: prev.filters.map((data) => {
                    const newFilter = filters[data.field];
                    return newFilter || data;
                }),
            })),
        enableLoader,
        setSearchResponse: setUseSearchResponse,
    });

    const getSelectedFilterValues = (category: JobCategory, filters: JobTypeFilters) => {
        const options = getJobTypeOptions(category);
        if (category === 'Tutoring') {
            const tutoringFilters = filters as TutoringJobTypeFilter;
            const selectedOption = options.find(
                (opt) => opt.key === tutoringFilters.selectedOption
            );
            return selectedOption ? [selectedOption.value] : [];
        }
        return Object.entries(filters)
            .filter(([_, value]) => value)
            .map(([key]) => {
                const option = options.find((opt) => opt.key === key);
                return option ? option.value : null;
            })
            .filter((value) => value !== null);
    };
    useEffect(() => {
        if (isFirstRender.current) {
            const initialFilters = getInitialJobTypeFilters('Childcare');
            const initialValues = getSelectedFilterValues('Childcare', initialFilters);

            updateFilters((prev) => ({
                ...prev,
                pageIndex: 1,
                filters: prev.filters.map((data) => {
                    if (data.field === 'jobTypes') {
                        return {
                            field: 'jobTypes',
                            operator: 'eq',
                            value: initialValues,
                        };
                    }
                    return data;
                }),
            }));

            isFirstRender.current = false;
        }
    }, []);
    // const handleCloseSidebar = () => {
    //     if (sidebarIsOpen) {
    //       dispatch(toggleSideBar());
    //     }
    //   };

    function getInitialJobTypeFilters(category: JobCategory): JobTypeFilters {
        switch (category) {
            case 'Childcare':
                return {
                    babysitting: true,
                    nannying: true,
                    beforeSchool: true,
                    afterSchool: true,
                };
            case 'Tutoring':
                return {
                    selectedOption: 'primarySchool',
                };
            case 'Odd Jobs':
                return {
                    laundry: true,
                    errands: true,
                    outdoorChores: true,
                    elderlyHelp: true,
                    otherOddJobs: true,
                };
        }
    }

    const handlePagination = () => {
        enableLoader();
        updateFilters((prev) => {
            return {
                ...prev,
                pageIndex: prev.pageIndex + 1,
            };
        });
    };

    useEffect(() => {
        if (sessionInfo.loading) {
            enableLoader();
        } else {
            disableLoader();
        }
    }, [sessionInfo.loading]);

    useEffect(() => {
        setUseSearchResponse(null);
        updateFilters(() => defaultFilters);
    }, [window.location]);

    const reviews = [
        { value: 'All', label: 'All' },
        { value: 'Over-4-stars', label: 'Over 4 stars' },
        { value: 'Over-4-stars-with-reviews', label: 'Over 4 stars with reviews' },
    ];

    const Jobscompleted = [
        { value: 'All', label: 'All' },
        { value: '1-or-more-jobs', label: '1 or more-jobs' },
        { value: '5-or-more-jobs', label: '5 or more jobs' },
    ];

    const handleBackToMapView = () => {
        if (profileCardDivRef.current && showBackToMapView) {
            profileCardDivRef.current.style.height = '50%';
            profileCardDivRef.current.scrollTo({ top: 0, behavior: 'smooth' });
            setShowBackToMapView(false);
        }
    };

    const closeJobType = () => {
        if (op1.current) {
            op1.current.hide();
        }
    };

    const closeReviews = () => {
        if (op2.current) {
            op2.current.hide();
        }
    };

    const closeJobsCompleted = () => {
        if (op3.current) {
            op3.current.hide();
        }
    };

    useEffect(() => {
        if (!profileCardDivRef.current) return;
        const resizeObserver = new ResizeObserver((entries) => {
            for (let entry of entries) {
                if (entry.target === profileCardDivRef.current) {
                    setProfileCardDivWidth(entry.contentRect.width);
                }
            }
        });
        resizeObserver.observe(profileCardDivRef.current);
        return () => {
            if (profileCardDivRef.current) {
                resizeObserver.unobserve(profileCardDivRef.current);
            }
        };
    }, []);

    useEffect(() => {
        if (profileCardDivWidth <= 850) {
            setProfileCardCol('col-12');
        } else if (profileCardDivWidth <= 950) {
            setProfileCardCol('col-6');
        } else if (profileCardDivWidth <= 1300) {
            setProfileCardCol('col-6');
        } else if (profileCardDivWidth >= 1500) {
            setProfileCardCol('col-3');
        } else {
            setProfileCardCol('col-4');
        }
    }, [profileCardDivWidth]);

    useEffect(() => {
        if (isMobile) {
            if (profileCardDivWidth <= 649) {
                setProfileCardCol('col-6'); // 2 in a row
            } else if (profileCardDivWidth <= 950) {
                setProfileCardCol('col-4'); // 3 in a row
            } else if (profileCardDivWidth <= 1300) {
                setProfileCardCol('col-3'); // 4 in a row
            } else if (profileCardDivWidth > 1300) {
                setProfileCardCol('col-2'); // 5 in a row
            }
        }
    }, [profileCardDivWidth]);
    useEffect(() => {
        if (profileCardDivRef.current) {
            const profileCards =
                profileCardDivRef.current.querySelectorAll<HTMLElement>('.profile-card2');
            const rowPositions = new Set<number>();
            profileCards.forEach((card) => {
                rowPositions.add(card.offsetTop);
            });
            setNumberOfRows(rowPositions.size);
        }
    }, [useSearchResponse, isMobile]);

    const handleScroll = () => {
        if (profileCardDivRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = profileCardDivRef.current;
            if (scrollTop + clientHeight >= scrollHeight - 2000) {
                setShowBackToMapView(true);
                profileCardDivRef.current.style.height = !isMobile ? '80vh' : 'auto';
            }
        }
    };

    const renderHeader = () => (
        <header
            className={`w-full flex align-items-center justify-content-center  overflow-hidden ${styles.headerGradient}`}
        >
            <img className={styles.juggleLogo} src={juggleLogo} alt="juggle logo" />
        </header>
    );
    const HorizontalNavigation = {
        Item: (index: number, icon: React.ReactNode, label: string, onClick: () => void) => {
            const isPostJob = label === 'Profiles'; // Always active for Post Job
            return (
                <div className={styles.horizontalnavitemcontainer}>
                    <div
                        className={`${styles.horizontalNavItem} ${
                            isPostJob ? styles.activeTab : ''
                        }`}
                        onClick={onClick}
                    >
                        {icon}
                        <span className={styles.navLabel}>{label}</span>
                    </div>
                    {/* Active line below only for Post Job */}
                    {isPostJob && <div className={styles.activeLine}></div>}
                </div>
            );
        },
    };

    useEffect(() => {
        const container = profileCardDivRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, [numberOfRows, isMobile, useSearchResponse]);

    useEffect(() => {
        const handleResize = () => setIsAbove1024(window.innerWidth > 1024);
        window.addEventListener('resize', handleResize);
        handleResize(); // Set initial value
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    const defaultCategory: JobCategory = selectedJob;
    return !isMobile ? (
        <div className="" style={{ height: '100vh', width: '100vw' }}>
            <HomeHeader />
            <JuggleMaps
                height={`calc(100% - ${showBackToMapView ? '80vh' : '50%'})`}
                width="calc(100% - 255px)"
                position="absolute"
                top={0}
                right={0}
            />
            {shouldShowProfileActivation && sessionInfo.data['profileCompleteness'] === 100 && (
                <ProfileCompleteCongoLoader />
            )}
            <AllFilters
                enable={showAllFilters}
                availableHelpers={useSearchResponse}
                selectedJobCategory={selectedJob}
                onClose={(payload, actionType) => {
                    setShowAllFilters(false);
                    if (payload === null) return;
                    enableLoader();
                    setUseSearchResponse(null);
                    updateFilters(() => payload);
                    if (actionType === 'reset') {
                        setFiltersApplied(false);
                    } else {
                        setFiltersApplied(true);
                    }
                }}
                onResetRef={(resetFn) => {
                    resetFiltersRef.current = resetFn;
                }}
            />
            {showBackToMapView && (
                <div
                    className="flex cursor-pointer absolute "
                    onClick={handleBackToMapView}
                    style={{
                        left: '60%',
                        transform: 'translateX(-50%)',
                        bottom: '82vh',
                        zIndex: '900',
                    }}
                >
                    <div
                        className="flex align-items-center pl-3 pr-3"
                        style={{
                            border: '2px solid rgba(88, 88, 88, 0.3)',
                            borderRadius: '20px',
                            height: '44px',
                            backgroundColor: '#585858',
                        }}
                    >
                        <FiMap style={{ color: '#FFFFFF' }} />
                        &nbsp;&nbsp;
                        <p className="font-bold" style={{ color: '#FFFFFF', fontSize: '12px' }}>
                            Back to map view
                        </p>
                    </div>
                </div>
            )}
            <div
                className="profile-card-container "
                style={{
                    width: sidebarIsOpen ? 'calc(100% - 246px)' : 'calc(100% - 83px)',
                    minHeight: 'auto',
                    overflowY: 'auto',
                    height: '50%',
                }}
                ref={profileCardDivRef}
            >
                <div
                    className="buttons-container flex justify-content-between flex-column"
                    style={{
                        position: 'fixed',
                        backgroundColor: '#FFFFFF',
                        width: 'calc(100% - 336px)',
                        paddingBlock: '10px',
                        zIndex: '20',
                        right: '40px',
                        // borderBottom: "2px solid rgba(88,88,88,0.07)",
                    }}
                >
                    <div
                        className="flex jobs justify-content-end align-items-center cursor-pointer relative"
                        style={{
                            boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.25)',
                            textWrap: 'nowrap',
                        }}
                    >
                        <div
                            className={`job-option ${selectedJob === 'Childcare' ? 'active' : ''}`}
                            onClick={() => handleJobSelect('Childcare')}
                        >
                            <p>
                                <LuSmilePlus /> &nbsp;Childcare
                            </p>
                        </div>
                        <div
                            className={`job-option ${selectedJob === 'Tutoring' ? 'active' : ''}`}
                            onClick={() => handleJobSelect('Tutoring')}
                        >
                            <p>
                                <HiOutlineBookOpen /> &nbsp;Tutoring
                            </p>
                        </div>
                        <div
                            className={`job-option ${selectedJob === 'Odd Jobs' ? 'active' : ''}`}
                            onClick={() => handleJobSelect('Odd Jobs')}
                        >
                            <p>
                                <LuPackageCheck /> &nbsp;Odd Jobs
                            </p>
                        </div>
                    </div>
                    <div
                        className={`${styles.jobsSmall} flex jobs align-items-center cursor-pointer relative`}
                        style={{
                            textWrap: 'nowrap',
                        }}
                    >
                        <div className="flex justify-content-evenly mt-2 gap-2 pr-2">
                            <div className="flex">
                                {getJobTypeOptions(selectedJob).map(({ key, label }) => (
                                    <div
                                        key={key}
                                        className="mb-2 flex justify-content-start align-items-center"
                                    >
                                        {selectedJob === 'Tutoring' ? (
                                            <input
                                                type="radio"
                                                id={key}
                                                name="tutoring-type"
                                                checked={
                                                    (jobTypeFilters as TutoringJobTypeFilter)
                                                        .selectedOption === key
                                                }
                                                onChange={() => {
                                                    handleJobTypeChange(key);
                                                    // handleApplyJobTypeFilters();
                                                }}
                                                className="custom-radio cursor-pointer"
                                            />
                                        ) : (
                                            <input
                                                type="checkbox"
                                                id={key}
                                                checked={(jobTypeFilters as any)[key]}
                                                onChange={() => {
                                                    handleJobTypeChange(key);
                                                    // handleApplyJobTypeFilters();
                                                }}
                                                className="custom-checkbox"
                                            />
                                        )}
                                        <label
                                            htmlFor={key}
                                            className="m-1 cursor-pointer"
                                            style={{ fontSize: '12px' }}
                                        >
                                            {label}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="flex cursor-pointer mt-1 gap-3">
                            <div
                                className="home-all-filter-btn pl-2 pr-2"
                                style={{
                                    border: '2px solid rgba(88, 88, 88, 0.3)',
                                    borderRadius: '15px',
                                    height: '43px',
                                }}
                                onClick={() => {
                                    setShowAllFilters(true);
                                }}
                            >
                                <img
                                    alt="Doller"
                                    src={menuIcon}
                                    style={{ width: '20px', color: '#FFFFFF' }}
                                />
                                &nbsp;
                                <p
                                    className="font-medium p-0 m-0"
                                    style={{
                                        color: '#585858',
                                        textWrap: 'nowrap',
                                        fontSize: '16px',
                                    }}
                                >
                                    All Filters
                                </p>
                            </div>

                            {(filtersApplied || selectedJob !== defaultCategory) && (
                                <div
                                    className="home-all-filter-btn pl-2 pr-2"
                                    style={{
                                        border: '2px solid rgba(88, 88, 88, 0.3)',
                                        borderRadius: '15px',
                                        height: '43px',
                                    }}
                                    // onClick={() => {
                                    //     enableLoader();

                                    //     // Reset filters in child component if needed
                                    //     if (resetFiltersRef.current) {
                                    //         resetFiltersRef.current();
                                    //     }

                                    //     const defaultCategory = "Tutoring" ; // or "Tutoring", "Odd Jobs", etc.
                                    //     const initialFilters = getInitialJobTypeFilters(defaultCategory);
                                    //     const initialValues = getSelectedFilterValues(defaultCategory, initialFilters);

                                    //     updateFilters((prev) => ({
                                    //         ...prev,
                                    //         pageIndex: 1,
                                    //         filters: prev.filters.map((filter) => {
                                    //             if (filter.field === "jobTypes") {
                                    //                 return {
                                    //                     ...filter,
                                    //                     value: initialValues,
                                    //                 };
                                    //             }
                                    //             if (filter.field === "jobDeliveryMethod" && defaultCategory === "Tutoring") {
                                    //                 const tutoring = (initialFilters as { selectedOption: string }).selectedOption;
                                    //                 const jobDeliveryMethodValue =
                                    //                     tutoring === "primarySchool" ? 1 : tutoring === "highSchool" ? 2 : null;

                                    //                 return {
                                    //                     ...filter,
                                    //                     value: jobDeliveryMethodValue,
                                    //                 };
                                    //             }
                                    //             return filter;
                                    //         }),
                                    //     }));

                                    //     setUseSearchResponse(null);
                                    //     setFiltersApplied(false);

                                    //     setTimeout(() => {
                                    //         disableLoader();
                                    //     }, 1000);
                                    // }}
                                    onClick={() => {
                                        enableLoader();

                                        if (resetFiltersRef.current) {
                                            resetFiltersRef.current();
                                        }
                                        if (selectedJob === 'Odd Jobs') {
                                            // Avoid redundant API calls for Odd Jobs
                                            handleJobSelect('Odd Jobs');

                                            setUseSearchResponse(null);
                                            updateFilters(() => defaultFiltersOddJobs);
                                            setFiltersApplied(false);
                                            setTimeout(() => {
                                                disableLoader();
                                            }, 1000);
                                            return;
                                        }
                                        if (selectedJob === 'Childcare') {
                                            enableLoader();

                                            // Reset filters for Childcare
                                            handleJobSelect('Childcare');
                                            updateFilters(() => defaultFilters);

                                            setUseSearchResponse(null); // Clear search results
                                            setFiltersApplied(false); // Mark filters as cleared

                                            setTimeout(() => {
                                                disableLoader();
                                            }, 1000);

                                            return;
                                        }
                                        if (selectedJob === 'Tutoring') {
                                            // Determine the selected option (primarySchool or highSchool)
                                            const selectedOption = (
                                                jobTypeFilters as TutoringJobTypeFilter
                                            ).selectedOption;

                                            // Reset to primarySchool if the current option is highSchool
                                            if (selectedOption === 'highSchool') {
                                                handleJobTypeChange('primarySchool'); // Reset to primarySchool
                                            }

                                            // Determine the jobDeliveryMethod (1 for In-Home, 2 for Online)
                                            const jobDeliveryMethod = defaultFilters.filters.find(
                                                (filter) => filter.field === 'jobDeliveryMethod'
                                            )?.value;

                                            // Pass the correct filter based on the selected option and jobDeliveryMethod
                                            if (
                                                selectedOption === 'primarySchool' ||
                                                selectedOption === 'highSchool'
                                            ) {
                                                if (jobDeliveryMethod === 2) {
                                                    // Online Primary School
                                                    updateFilters(
                                                        () => FiltersInOnlineTutorsPrimarySchool
                                                    );
                                                } else {
                                                    // In-Home Primary School
                                                    updateFilters(
                                                        () => FiltersInHomeTutorsPrimarySchool
                                                    );
                                                }
                                            }

                                            setUseSearchResponse(null); // Clear search results
                                            setFiltersApplied(false); // Mark filters as cleared

                                            setTimeout(() => {
                                                disableLoader();
                                            }, 1000);

                                            return; // Exit early to avoid redundant logic
                                        }
                                        const defaultCategory: JobCategory = selectedJob; // or another dynamic value
                                        const initialFilters =
                                            getInitialJobTypeFilters(defaultCategory);
                                        const initialValues = getSelectedFilterValues(
                                            defaultCategory,
                                            initialFilters
                                        );

                                        handleJobSelect(defaultCategory);
                                        updateFilters((prev) => ({
                                            ...prev,
                                            pageIndex: 1,
                                            filters: prev.filters.map((filter) => {
                                                if (filter.field === 'jobTypes') {
                                                    return {
                                                        ...filter,
                                                        value: initialValues,
                                                    };
                                                }
                                                if (
                                                    filter.field === 'jobDeliveryMethod' &&
                                                    defaultCategory === 'Tutoring'
                                                ) {
                                                    const tutoring = (
                                                        initialFilters as { selectedOption: string }
                                                    ).selectedOption;
                                                    const jobDeliveryMethodValue =
                                                        tutoring === 'primarySchool'
                                                            ? 1
                                                            : tutoring === 'highSchool'
                                                            ? 2
                                                            : null;

                                                    return {
                                                        ...filter,
                                                        value: jobDeliveryMethodValue,
                                                    };
                                                }

                                                return filter;
                                            }),
                                        }));

                                        setUseSearchResponse(null);
                                        setFiltersApplied(false);

                                        setTimeout(() => {
                                            disableLoader();
                                        }, 1000);
                                    }}
                                >
                                    <img
                                        alt="Doller"
                                        src={menuIcon}
                                        style={{ width: '20px', color: '#FFFFFF' }}
                                    />
                                    &nbsp;
                                    <p
                                        className="font-medium p-0 m-0"
                                        style={{
                                            color: '#585858',
                                            textWrap: 'nowrap',
                                            fontSize: '16px',
                                        }}
                                    >
                                        Clear Filters
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="" style={{ backgroundColor: '#FFFFFF' }}>
                        <p
                            className="p-0 m-0 pl-3"
                            style={{
                                color: '#787777',
                                textWrap: 'nowrap',
                                fontSize: '18px',
                                fontWeight: '400',
                            }}
                        >
                            <span>
                                <b
                                    style={{
                                        height: '100px',
                                        fontSize: '24px',
                                        color: '#585858',
                                        fontWeight: '700',
                                    }}
                                >
                                    {useSearchResponse !== null
                                        ? useSearchResponse.results?.length
                                        : 0}
                                    {' of '}
                                    {useSearchResponse !== null ? useSearchResponse.total : 0}{' '}
                                </b>
                            </span>
                            {selectedJob === 'Tutoring'
                                ? 'Candidates Near Me'
                                : 'Candidates Near Me'}
                        </p>
                    </div>
                    <Divider className="mt-2" />
                </div>
                <div
                    className={`${styles.containerSmall} container`}
                    style={{ backgroundColor: '#FFFFFF', marginTop: '169px' }}
                >
                    <div className="grid flex-grow-1">
                        {useSearchResponse !== null &&
                            useSearchResponse.results.map((profile, index) => (
                                <div key={index} className={`${profileCardCol} profile-card2`}>
                                    <CustomHomepageCard
                                        {...profile}
                                        refresh={() => {
                                            enableLoader();
                                            setUseSearchResponse(null);
                                            setTimeout(() => {
                                                refreshSearchResult();
                                            }, 1000);
                                        }}
                                    />
                                </div>
                            ))}
                    </div>
                    <div className="flex justify-content-center w-full mb-4 mt-3">
                        <div
                            className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
                            onClick={handlePagination}
                            style={{
                                border: '1px solid #000000',
                                borderRadius: '5px',
                                fontSize: '14px',
                                color: '#585858',
                                height: '42px',
                            }}
                        >
                            See More Helpers
                        </div>
                    </div>
                </div>
            </div>
            {sessionInfo.data?.['interestInHomeAgedCareResponse'] == null &&
                sessionInfo?.data?.['client']?.['jobsPlaced'] !== 0 &&
                applicationState?.interestInHomeAgedCareResponse && (
                    <AgedCarePopup
                        visible={
                            sessionInfo.data?.['interestInHomeAgedCareResponse'] == null &&
                            sessionInfo?.data?.['client']?.['jobsPlaced'] !== 0 &&
                            applicationState?.interestInHomeAgedCareResponse
                        }
                        onHide={hidePopup}
                    />
                )}

            {conditions && showEmployeeBenefitsDialog && (
                <EmployeeBenefits
                    isDialogVisible={conditions && showEmployeeBenefitsDialog}
                    onHide={hidePopupEmployee}
                />
            )}

            {!sessionInfo.loading && Number(sessionInfo?.data['profileCompleteness']) < 100 && (
                <WelcomeComponent
                    dialogBoxState={showProfileCompletenessDialog}
                    reverseWhen={65}
                    profileCompletion={Number(sessionInfo.data['profileCompleteness'])}
                    userName={sessionInfo.data['firstName']}
                    closeClicked={() => setShowProfileCompletenessDialog(false)}
                    onExploreClicked={() => setShowProfileCompletenessDialog(false)}
                />
            )}

            {}
            <LeftHandUserPanel activeindex={0} />
            <HelpdeskManager />
        </div>
    ) : (
        <>
            {renderHeader()}
            <div
                className=""
                style={{
                    height: '100vh',
                    width:
                        isAbove1024 && sidebarIsOpen && isMobile ? 'calc(100vw - 288px)' : '100vw',
                    marginLeft: isAbove1024 && isMobile && sidebarIsOpen ? '288px' : '0',
                    position: isAbove1024 && isMobile ? 'relative' : undefined,
                }}
            >
                <HomeHeader />
                <ConfirmationPopupGreen confirmationProps={confirmationProps} />
                <div>
                    {/* <JuggleMaps
            height={`calc(100% - ${showBackToMapView ? '80vh' : '350px'})`}
            width='calc(100% - 255px)'
            position='absolute'
            top={0}
            right={0}
        /> */}

                    <div className={styles.tabContainer}>
                        {[
                            {
                                item: ({ index, itemStyles }) =>
                                    HorizontalNavigation.Item(
                                        index,
                                        <i
                                            style={{ fontSize: '20px' }}
                                            className={`pi pi-users ${itemStyles.navIcon}`}
                                        ></i>,
                                        'Profiles',
                                        () => {
                                            const path =
                                                clientType === 2
                                                    ? '/business-home'
                                                    : '/parent-home';
                                            navigate(path);
                                        }
                                    ),
                            },
                            // Conditionally render "My Jobs" if jobPlace > 0, otherwise render "Post Job"
                            sessionInfo.data?.['client']?.['jobsPlaced'] > 0
                                ? {
                                      item: ({ index, itemStyles }) =>
                                          HorizontalNavigation.Item(
                                              index,
                                              <i
                                                  className={`pi pi-list ${itemStyles.navIcon}`}
                                              ></i>,
                                              'My Jobs',
                                              () => {
                                                  const path =
                                                      clientType === 2
                                                          ? '/business-home/manage-jobs?jobId=-1&activeTab=0'
                                                          : '/parent-home/manage-jobs?jobId=-1&activeTab=0';
                                                  navigate(path);
                                              }
                                          ),
                                  }
                                : {
                                      item: ({ index, itemStyles }) =>
                                          HorizontalNavigation.Item(
                                              index,
                                              <i
                                                  className={`pi pi-plus ${itemStyles.navIcon}`}
                                              ></i>,
                                              'Post Job',
                                              () => {
                                                  if (
                                                      sessionInfo.data['profileCompleteness'] <= 99
                                                  ) {
                                                      showConfirmationPopup(
                                                          'Complete',
                                                          `Your account must be 100% complete before proceeding.`,
                                                          'Complete',
                                                          <RxUpdate style={{ fontSize: '20px' }} />,
                                                          () => {
                                                              dispatch(
                                                                  updateProfileActivationEnabled(
                                                                      true
                                                                  )
                                                              );
                                                          }
                                                      );
                                                      return; // Prevent navigation
                                                  }
                                                  const path =
                                                      clientType === 2
                                                          ? '/business-home/post-job'
                                                          : '/parent-home/post-job';
                                                  navigate(path);
                                              }
                                          ),
                                  },
                            // {
                            //     item: ({ index, itemStyles }) =>
                            //         HorizontalNavigation.Item(
                            //             index,
                            //             <i className={`pi pi-comments ${itemStyles.navIcon}`}></i>,
                            //             "Chat",
                            //             () => {
                            //                 if (sessionInfo.data["profileCompleteness"] <= 99) {
                            //                     showConfirmationPopup(
                            //                         "Complete",
                            //                         `Your account must be 100% complete before proceeding.`,
                            //                         "Complete",
                            //                         <RxUpdate style={{ fontSize: "20px" }} />,
                            //                         () => {
                            //                             dispatch(updateProfileActivationEnabled(true));
                            //                         }
                            //                     );
                            //                     return; // Prevent navigation
                            //                 }

                            //                 const path =
                            //                     clientType === 2
                            //                         ? "/business-home/inAppChat"
                            //                         : "/parent-home/inAppChat";
                            //                 navigate(path);
                            //             }
                            //         ),
                            // },
                            {
                                item: ({ index, itemStyles }) =>
                                    HorizontalNavigation.Item(
                                        index,
                                        <i className={`pi pi-comments ${itemStyles.navIcon}`}></i>,
                                        'Payments',
                                        () => {
                                            // if (sessionInfo.data["profileCompleteness"] <= 99) {
                                            //     showConfirmationPopup(
                                            //         "Complete",
                                            //         `Your account must be 100% complete before proceeding.`,
                                            //         "Complete",
                                            //         <RxUpdate style={{ fontSize: "20px" }} />,
                                            //         () => {
                                            //             dispatch(updateProfileActivationEnabled(true));
                                            //         }
                                            //     );
                                            //     return; // Prevent navigation
                                            // }

                                            if (!inIframe) {
                                                const path =
                                                    clientType === 2
                                                        ? '/business-home/timesheet/helper-confirm'
                                                        : '/parent-home/timesheet/helper-confirm';
                                                navigate(path);
                                            }
                                            if (clientType === 0) {
                                                IframeBridge.sendToParent({
                                                    type: 'helperInAppPaymentsNavigate',
                                                });
                                            } else {
                                                IframeBridge.sendToParent({
                                                    type: 'inAppPaymentsNavigate',
                                                });
                                            }
                                        }
                                    ),
                            },
                        ].map((tab, index) => (
                            <div key={index} className={styles.tabItem}>
                                {tab.item({ index, itemStyles: styles })}
                            </div>
                        ))}
                    </div>

                    {shouldShowProfileActivation &&
                        sessionInfo?.data['profileCompleteness'] === 100 && (
                            <ProfileCompleteCongoLoader />
                        )}
                    <AllFilters
                        enable={showAllFilters}
                        availableHelpers={useSearchResponse}
                        selectedJobCategory={selectedJob}
                        onClose={(payload, actionType) => {
                            setShowAllFilters(false);
                            if (payload === null) return;
                            enableLoader();
                            setUseSearchResponse(null);
                            if (actionType === 'reset') {
                                setFiltersApplied(false);
                            } else {
                                setFiltersApplied(true);
                            }
                        }}
                        onResetRef={(resetFn) => {
                            resetFiltersRef.current = resetFn;
                        }}
                    />
                    {/* {showBackToMapView && (
            <div
                className='flex cursor-pointer absolute '
                onClick={handleBackToMapView}
                style={{
                    left: '60%',
                    transform: 'translateX(-50%)',
                    bottom: '82vh',
                    zIndex: '900',
                }}
            >
                <div
                    className='flex align-items-center pl-3 pr-3'
                    style={{
                        border: '2px solid rgba(88, 88, 88, 0.3)',
                        borderRadius: '20px',
                        height: '44px',
                        backgroundColor: '#585858',
                    }}
                >
                    <FiMap style={{ color: '#FFFFFF' }} />
                    &nbsp;&nbsp;
                    <p className='font-bold' style={{ color: '#FFFFFF', fontSize: '12px' }}>
                        Back to map view
                    </p>
                </div>
            </div>
        )} */}
                    <div className={styles.profileCardContainer} ref={profileCardDivRef}>
                        <div
                            className="buttons-container flex justify-content-between"
                            style={{
                                backgroundColor: '#FFFFFF',
                                flexDirection: 'column',
                                paddingBlock: '7px',
                            }}
                        >
                            <div
                                className={`${styles.jobmobile}`}
                                style={{
                                    boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.25)',
                                    textWrap: 'nowrap',
                                }}
                            >
                                <div
                                    className={`job-option ${
                                        selectedJob === 'Childcare' ? 'active' : ''
                                    }`}
                                    onClick={() => handleJobSelect('Childcare')}
                                >
                                    <p>
                                        <LuSmilePlus /> &nbsp;Childcare
                                    </p>
                                </div>
                                <div
                                    className={`job-option ${
                                        selectedJob === 'Tutoring' ? 'active' : ''
                                    }`}
                                    onClick={() => handleJobSelect('Tutoring')}
                                >
                                    <p>
                                        <HiOutlineBookOpen /> &nbsp;Tutoring
                                    </p>
                                </div>
                                <div
                                    className={`job-option ${
                                        selectedJob === 'Odd Jobs' ? 'active' : ''
                                    }`}
                                    onClick={() => handleJobSelect('Odd Jobs')}
                                >
                                    <p>
                                        <LuPackageCheck /> &nbsp;Odd Jobs
                                    </p>
                                </div>
                            </div>
                            <div
                                style={{ flexDirection: 'column', marginTop: '5px', gap: '5px' }}
                                className="flex justify-content-between  flex-row"
                            >
                                <p
                                    style={{
                                        color: '#787777',
                                        textWrap: 'nowrap',
                                        fontSize: '10px',
                                        fontWeight: '400',
                                        marginInline: '5px',
                                    }}
                                >
                                    <span>
                                        <b
                                            style={{
                                                height: '100px',
                                                fontSize: '16px',
                                                color: '#585858',
                                                fontWeight: '700',
                                                marginInline: '5px',
                                            }}
                                        >
                                            {useSearchResponse !== null
                                                ? useSearchResponse.results.length
                                                : 0}
                                            {' of '}
                                            {useSearchResponse !== null
                                                ? useSearchResponse.total
                                                : 0}{' '}
                                        </b>
                                    </span>
                                    {selectedJob === 'Tutoring'
                                        ? 'Candidates Near Me'
                                        : 'Candidates Near Me'}
                                </p>
                                <div className="flex cursor-pointer mt-1 gap-3">
                                    {/* Combined All Filters/Clear Filters Button */}
                                    <div
                                        className="home-all-filter-btn pl-2 pr-2"
                                        style={{
                                            border: '1px solid rgba(88, 88, 88, 0.3)',
                                            borderRadius: '15px',
                                            height: '30px',
                                        }}
                                        onClick={() => {
                                            if (filtersApplied) {
                                                enableLoader();
                                                if (resetFiltersRef.current) {
                                                    resetFiltersRef.current(); // Reset child filters
                                                }
                                                setUseSearchResponse(null); // Reset search response
                                                updateFilters(() => defaultFilters); // Update filters to default
                                                setFiltersApplied(false); // Mark filters as cleared
                                                setTimeout(() => {
                                                    disableLoader();
                                                }, 1000);
                                            } else {
                                                setShowAllFilters(true);
                                            }
                                        }}
                                    >
                                        <img
                                            alt="Filter"
                                            src={menuIcon}
                                            style={{ width: '20px', color: '#FFFFFF' }}
                                        />
                                        &nbsp;
                                        <p
                                            className="font-medium"
                                            style={{
                                                color: '#585858',
                                                textWrap: 'nowrap',
                                                fontSize: '12px',
                                            }}
                                        >
                                            {filtersApplied ? 'Clear Filters' : 'All Filters'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className={`${!isMobile && 'xl:mt-7'}`}
                            style={{ backgroundColor: '#FFFFFF' }}
                        ></div>
                        <div
                            className="container "
                            style={{ backgroundColor: '#FFFFFF', margin: '0px' }}
                        >
                            <div className="grid flex-grow-1">
                                {useSearchResponse !== null
                                    ? useSearchResponse.results.map((profile, index) => (
                                          <div
                                              key={index}
                                              className={`${profileCardCol} profile-card2`}
                                          >
                                              <CustomHomepageCard
                                                  {...profile}
                                                  refresh={() => {
                                                      enableLoader();
                                                      setUseSearchResponse(null);
                                                      setTimeout(() => {
                                                          refreshSearchResult();
                                                      }, 1000);
                                                  }}
                                              />
                                          </div>
                                      ))
                                    : Array.from({ length: 4 }).map((_, index) => (
                                          <div
                                              key={index}
                                              className={`${profileCardCol} profile-card2`}
                                          >
                                              <ShimmerHelperCard />
                                          </div>
                                      ))}
                            </div>
                            <div className="flex justify-content-center w-full mb-4 mt-3">
                                <div
                                    className="cursor-pointer text-center w-full font-bold flex justify-content-center align-items-center"
                                    onClick={handlePagination}
                                    style={{
                                        border: '1px solid #000000',
                                        borderRadius: '5px',
                                        fontSize: '14px',
                                        color: '#585858',
                                        height: '42px',
                                    }}
                                >
                                    See More Helpers
                                </div>
                            </div>
                        </div>
                    </div>
                    {sessionInfo.data?.['interestInHomeAgedCareResponse'] == null &&
                        sessionInfo?.data?.['client']?.['jobsPlaced'] !== 0 &&
                        applicationState?.interestInHomeAgedCareResponse && (
                            <AgedCarePopup
                                visible={
                                    sessionInfo.data?.['interestInHomeAgedCareResponse'] == null &&
                                    sessionInfo?.data?.['client']?.['jobsPlaced'] !== 0 &&
                                    applicationState?.interestInHomeAgedCareResponse
                                }
                                onHide={hidePopup}
                            />
                        )}

                    {conditions && showEmployeeBenefitsDialog && (
                        <EmployeeBenefits
                            isDialogVisible={conditions && showEmployeeBenefitsDialog}
                            onHide={hidePopupEmployee}
                        />
                    )}

                    {!sessionInfo.loading &&
                        Number(sessionInfo?.data['profileCompleteness']) < 100 && (
                            <WelcomeComponent
                                dialogBoxState={showProfileCompletenessDialog}
                                reverseWhen={65}
                                profileCompletion={Number(sessionInfo?.data['profileCompleteness'])}
                                userName={sessionInfo.data['firstName']}
                                closeClicked={() => setShowProfileCompletenessDialog(false)}
                                onExploreClicked={() => setShowProfileCompletenessDialog(false)}
                            />
                        )}
                    {}
                    <LeftHandUserPanel activeindex={0} />
                    <HelpdeskManager />
                </div>
            </div>
        </>
    );
};

export default ParentHome;
