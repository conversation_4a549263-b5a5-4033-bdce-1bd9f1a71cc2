import { useNavigate, useSearchParams } from "react-router-dom";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import LeftHandUserPanelParent from "../LeftHandUserPanel";
import HomeHeaderParent from "../HomeHeader";
import HomeHeaderBusiness from "../../Business/HomeHeaderBuisness";
import React, { useEffect, useRef, useState } from "react";
import AwardedJobs from "./Tabs/AwardedJobs";
import JobHistory from "./Tabs/JobHistory";
import RateHelpers from "./Tabs/RateHelpers";
import NoteSearch from "../../../assets/images/Icons/note-search-mobile.png";
import NoteTodo from "../../../assets/images/Icons/note-todo-mobile.png";
import NoteWrite from "../../../assets/images/Icons/note-write-mobile.png";
import StarGray from "../../../assets/images/Icons/star-gray-mobile.png";
import { JobLoadingStates, Jobs, ManageJobSectionProps, WeeklySchedule } from "./types";
import Service from "../../../services/services";
import useLoader from "../../../hooks/LoaderHook";
import ManageJobsCalender from "./ManageJobsCalender";
import UnawardedJobs from "./Tabs/UnAwardedJobs";
import JobSummaryUnAwarded from "./summary/JobSummaryUnawarded";
import { Badge } from "primereact/badge";
import { FaCheck } from "react-icons/fa";
import JobSummaryAwarded from "./summary/JobSummaryAwarded";
import HelpdeskManager from "../../../commonComponents/HelpdeskManager";
import styles from "../styles/manage-jobs.module.css";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../HorizontalNavigationMobile";
import { RxUpdate } from "react-icons/rx";
import { ConfirmationPopupGreen, useConfirmationPopup } from "../ConfirmationPopup";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { updateProfileActivationEnabled, updateShowChatBox } from "../../../store/slices/applicationSlice";
import RateHelperModal from "./modals/RateHelperModal";
import JobHistorySummary from "./summary/JobHistorySummary";
import { IframeBridge } from "../../../services/IframeBridge";
import JobAnalyticsUnawarded from "./summary/JobAnalyticsUnawarded";
import c from "../../../helper/juggleStreetConstants";
import { BsFillChatLeftFill } from "react-icons/bs";

interface PendingStats {
  pendingInvitations: number;
  pendingJobs: number;
  pendingChatMessages: number;
  unratedJobs: number;
}

const TabFactory: Record<
  number,
  {
    tab: React.FC<ManageJobSectionProps>;
    tabName: string;
    tabIcon: React.ReactNode;
  }
> = {
  0: {
    tabName: "Unawarded Jobs",
    tab: UnawardedJobs,
    tabIcon: <img src={NoteSearch} alt="NoteSearch" width="13.5px" height="14.4px" />,
  },
  1: {
    tabName: "Awarded Jobs",
    tab: AwardedJobs,
    tabIcon: <img src={NoteTodo} alt="NoteTodo" width="13.35px" height="14.4px" />,
  },
  2: {
    tabName: "Job History",
    tab: JobHistory,
    tabIcon: <img src={NoteWrite} alt="NoteWrite" width="14.46px" height="14.46px" />,
  },
  3: {
    tabName: "Rate Helpers",
    tab: RateHelpers,
    tabIcon: <img src={StarGray} alt="StarGray" width="17px" height="15.0px" />,
  },
};

const ManageJobs = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [upComingJobs, setUpComingJobs] = useState<Jobs[]>([]);
  const [jobHistory, setJobHistory] = useState<Jobs[]>([]);
  const [unRatedJobs, setUnRatedJobs] = useState<Jobs[]>([]);
  const [pendingStats, setPendingStats] = useState<PendingStats | null>(null);
  const [enableRatePopup, setEnableRatePopup] = useState<boolean>(false);
  // const { sideBarIsOpened: sidebarIsOpen, shouldShowProfileActivation } =
  //     useSelector((state: RootState) => state.applicationState);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { enableLoader, disableLoader } = useLoader();
  const [isLoading, setIsLoading] = useState<JobLoadingStates>(JobLoadingStates.initial); // Add local loading state
  const { isMobile } = useIsMobile();
  const jobId = searchParams.get("jobId");
  const activeTab = searchParams.get("activeTab");
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const tabNameIcon = Object.values(TabFactory).map((v) => ({
    tabName: v.tabName,
    tabIcon: v.tabIcon,
  }));
  const ActiveTab = TabFactory[activeTabIndex].tab;

  const fetchJobs = async () => {
    enableLoader();
    try {
      setIsLoading(JobLoadingStates.loading); // Set loading to true
      const [upcomingJobs, completedJobs, unratedJobs, pendingStat] = await Promise.all([
        new Promise<Jobs[]>((resolve, reject) => {
          Service.getUpComingJobs(
            (data: Jobs[]) => resolve(data),
            (error) => reject(error)
          );
        }),
        new Promise<Jobs[]>((resolve, reject) => {
          Service.getCompletedJobs(
            (data: Jobs[]) => resolve(data),
            (error) => reject(error)
          );
        }),
        new Promise<Jobs[]>((resolve, reject) => {
          Service.getUnratedJobs(
            (data: Jobs[]) => resolve(data),
            (error) => reject(error)
          );
        }),
        new Promise<PendingStats>((resolve, reject) => {
          Service.requestPendingStats(
            (data: PendingStats) => resolve(data),
            (error) => reject(error)
          );
        }),
      ]);
      setUpComingJobs(upcomingJobs);
      setJobHistory(completedJobs);
      setUnRatedJobs(unratedJobs);
      setPendingStats(pendingStat);
      setIsLoading(JobLoadingStates.finished); // Set loading to false
    } catch (error) {
      setIsLoading(JobLoadingStates.error); // Set loading to false
      console.warn("Error fetching jobs:", error);
    } finally {
      disableLoader();
    }
  };

  const showRatePopup = () => {
    setEnableRatePopup(true);
  };

  useEffect(() => {
    if (pendingStats === null) return;
    if (pendingStats.unratedJobs > 0 && activeTab !== "3" && unRatedJobs.length > 0 && (jobId === null || jobId === undefined || jobId === "-1")) {
      showRatePopup();
    }
  }, [pendingStats]);

  useEffect(() => {
    if (activeTab) {
      const activeTabIndex = Number(activeTab);
      if (activeTabIndex >= 0 && activeTabIndex < Object.keys(TabFactory).length) {
        setActiveTabIndex(activeTabIndex);
      } else {
        setActiveTabIndex(0);
      }
    }
  }, [activeTab]);

  const handleTabChange = (index: number) => {
    setActiveTabIndex(index);
    const updatedParams = new URLSearchParams(searchParams);
    updatedParams.set("jobId", "-1");
    updatedParams.set("activeTab", String(index));
    updatedParams.set("rateJobId", "");
    updatedParams.set("applicantId", "");
    navigate(
      { search: updatedParams.toString() },
      {
        replace: true,
      }
    );
    IframeBridge.sendToParent({
      type: "goBackToManageJob",
      data: {
        activeTab: index,

      },
    });
  };
  const handleViewJobIdChange = (newJobId: number, showAnalytics = false) => {
    if (!inIframe) {
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.set("jobId", String(newJobId));
      updatedParams.set("activeTab", activeTab);
      if (showAnalytics) {
        updatedParams.set("analytics", "1");
      } else {
        updatedParams.delete("analytics");
      }
      navigate({ search: updatedParams.toString() });
    }

    IframeBridge.sendToParent({
      type: "viewJob",
      data: {
        jobId: newJobId,
        activeTab: activeTab,
        analytics: showAnalytics ? "1" : "0", // Always include analytics parameter
      },
    });
  };

  useEffect(() => {
    fetchJobs();
  }, [jobId, activeTab]);

  useEffect(() => {
    const id = requestAnimationFrame(() => {
      IframeBridge.sendToParent({
        type: "canProcced",
        data: null,
      });
      disableLoader();
    });
    return () => cancelAnimationFrame(id);
  }, []);
  useEffect(() => {
    const id = requestAnimationFrame(() => {
      IframeBridge.sendToParent({
        type: "inAppPaymentsLoaded",
        data: null,
      });
      disableLoader();
    });
    return () => cancelAnimationFrame(id);
  }, []);
  const unAwardedCount = upComingJobs.filter(job => job.jobStatus === 1).length;
  const awardedCount = upComingJobs.filter((val) => val.jobStatus === 2).length;
  const renderHeader = () => (
    <>
      <header className={`w-full flex align-items-center justify-content-center border-round-bottom-lg overflow-hidden ${styles.headerGradient}`}>
        Jobs
        <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
      </header>
    </>
  );
  useEffect(() => {
    if (isMobile) {
      dispatch(updateShowChatBox(false));

      return () => {
        dispatch(updateShowChatBox(true));
      };
    }
  }, [isMobile, dispatch]);
  return !isMobile ? (
    <div className="w-screen h-screen">
      <>
        <RateHelperModal visible={enableRatePopup} job={unRatedJobs[0]} onClose={() => setEnableRatePopup(false)} />
        {clientType === 1 ? <HomeHeaderParent /> : <HomeHeaderBusiness />}
        {<LeftHandUserPanelParent activeindex={activeTabIndex === 3 ? 3 : 1} />}
        <HelpdeskManager />
      </>
      <div
        className="overflow-hidden h-full absolute right-0 pt-3 flex flex-column"
        style={{
          width: "calc(100% - 240px)",
        }}
      >
        <div
          className="flex mb-5"
          style={{
            height: "23%",
            width: "100%",
            borderTop: "2px solid #179D52",
            borderBottom: "2px solid #179D52",
          }}
        >
          <ManageJobsCalender upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} />
        </div>
        <div
          className="flex flex-column w-full p-1"
          style={{
            borderBottom: "1px solid #D6D6D6",
          }}
        >
          <div className="flex w-full gap-2 md:gap-4 lg:gap-6 px-4 sm:px-6 md:px-8 lg:px-20">
            {tabNameIcon.map((tni, index) => (
              <div key={index} className="flex flex-column gap-0 align-items-center cursor-pointer relative" onClick={() => handleTabChange(index)}>
                <div className="flex gap-1 sm:gap-2 align-items-center cursor-pointer">
                  <span style={{ opacity: activeTabIndex === index ? 1 : 0.5 }}>{tni.tabIcon}</span>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: activeTabIndex === index ? "700" : "500",
                      fontSize: "clamp(12px, 3vw, 16px)",
                      color: activeTabIndex === index ? "#585858" : " #58585880",
                      textAlign: "center",
                    }}
                  >
                    {tni.tabName}
                  </p>
                  <Badge
                    value={unAwardedCount}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "15px",
                      minWidth: "15px",
                      maxHeight: "15px",
                      maxWidth: "15px",
                      fontSize: "50%",
                      display: index === 0 && unAwardedCount > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      right: "-12px",
                      top: "-5px",
                    }}
                  />
                  <Badge
                    value={awardedCount}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "15px",
                      minWidth: "15px",
                      maxHeight: "15px",
                      maxWidth: "15px",
                      fontSize: "50%",
                      display: index === 1 && awardedCount > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      right: "-12px",
                      top: "-5px",
                    }}
                  />
                  <Badge
                    value={jobHistory.length}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "15px",
                      minWidth: "15px",
                      maxHeight: "15px",
                      maxWidth: "15px",
                      fontSize: "50%",
                      display: index === 2 && jobHistory.length > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      right: "-16px",
                      top: "-2px",
                    }}
                  />
                  <Badge
                    value={unRatedJobs.length}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "15px",
                      minWidth: "15px",
                      maxHeight: "15px",
                      maxWidth: "15px",
                      fontSize: "50%",
                      display: index === 3 && unRatedJobs.length > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      right: "-12px",
                      top: "-5px",
                    }}
                  />
                </div>
                {activeTabIndex === index && (
                  <div
                    className="relative"
                    style={{
                      bottom: "-4px",
                      width: "100%",
                      height: "2px",
                      backgroundColor: "#FFA500",
                      borderRadius: "20px",
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        <div
          className="flex-grow-1 flex overflow-y-auto"
          style={{
            paddingLeft: "63px",
          }}
        >
          <div
            className="p-1 pt-4 flex flex-column flex-grow-1 "
            style={{
              height: "100%",
            }}
          >
            {Number(jobId || -1) === -1 ? (
              <ActiveTab
                upComingJobs={upComingJobs}
                jobHistory={jobHistory}
                unRatedJobs={unRatedJobs}
                viewJob={(j) => handleViewJobIdChange(j)}
                refresh={() => fetchJobs()}
              />
            ) : activeTabIndex === 0 ? (
              <JobSummaryUnAwarded upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
            ) : activeTabIndex === 1 ? (
              <JobSummaryAwarded upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
            ) : activeTabIndex === 2 ? (
              <JobHistorySummary upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div
      style={{
        // height: '100vh', width: sidebarIsOpen ? "calc(100vw - 288px)" : "100vw",
        // marginLeft: sidebarIsOpen ? "288px" : "0",
        height: "100vh",
        width: "100vw",
        marginLeft: "0",
      }}
    >
      <>
        <RateHelperModal visible={enableRatePopup} job={unRatedJobs[0]} onClose={() => setEnableRatePopup(false)} />
        <ConfirmationPopupGreen confirmationProps={confirmationProps} />
        {clientType === 1 ? <HomeHeaderParent /> : <HomeHeaderBusiness />}
        {/* {<LeftHandUserPanelParent activeindex={activeTabIndex === 3 ? 3 : 1} />} */}
        <HelpdeskManager />
      </>
      <HorizontalNavigation
        title="Jobs"
        style={{
          width: "100vw",
          marginLeft: "0",
        }}
        wrapperStyle={{
          position: "relative",
        }}
        tabs={[
          {
            item: ({ index, activeIndex, itemStyles }) =>
              HorizontalNavigation.Item(index, activeIndex, <i className={`pi pi-list ${itemStyles.navIcon}`}></i>, "My Jobs", () => {
                if (!inIframe) {
                  const path = clientType === 2 ? "/business-home/manage-jobs?jobId=-1&activeTab=0" : "/parent-home/manage-jobs?jobId=-1&activeTab=0";
                  navigate(path);
                }
              }),
          },
          {
            item: ({ index, activeIndex, itemStyles }) =>
              HorizontalNavigation.Item(index, activeIndex, <i className={`pi pi-plus ${itemStyles.navIcon}`}></i>, "Post Job", () => {
                if (sessionInfo.data["profileCompleteness"] <= 99) {
                  showConfirmationPopup(
                    "Complete",
                    `Your account must be 100% complete before proceeding.`,
                    "Complete",
                    <RxUpdate style={{ fontSize: "20px" }} />,
                    () => {
                      dispatch(updateProfileActivationEnabled(true));
                    }
                  );
                  return; // Prevent navigation
                }

                // Navigate to Post Job
                if (!inIframe) {
                  const path = clientType === 2 ? "/business-home/job/post/job-type" : "/parent-home/job/post/job-type";
                  navigate(path);
                }
                IframeBridge.sendToParent({
                  type: "navigateToPostJob",
                });
              }),
          },
          // {
          //   item: ({ index, activeIndex, itemStyles }) =>
          //     HorizontalNavigation.Item(index, activeIndex, <BsFillChatLeftFill fontSize={"18px"} />, "Chat", () => {
          //       // Check profile completeness before navigating
          //       if (sessionInfo.data["profileCompleteness"] <= 99) {
          //         showConfirmationPopup(
          //           "Complete",
          //           `Your account must be 100% complete before proceeding.`,
          //           "Complete",
          //           <RxUpdate style={{ fontSize: "20px" }} />,
          //           () => {
          //             dispatch(updateProfileActivationEnabled(true));
          //           }
          //         );
          //         return; // Prevent navigation
          //       }
          //       IframeBridge.sendToParent({
          //         type: "navigateConversations",
          //       });
          //       // Navigate to Chat
          //       if (!inIframe) {
          //         const path = clientType === 2 ? "/business-home/inAppChat" : "/parent-home/conversations";
          //         navigate(path);
          //       }
          //     }),
          // },
          {
            item: ({ index, activeIndex, itemStyles }) =>
              HorizontalNavigation.Item(index, activeIndex, <BsFillChatLeftFill fontSize={"18px"} />, "Payments", () => {
                // Check profile completeness before navigating
                // if (sessionInfo.data["profileCompleteness"] <= 99) {
                //   showConfirmationPopup(
                //     "Complete",
                //     `Your account must be 100% complete before proceeding.`,
                //     "Complete",
                //     <RxUpdate style={{ fontSize: "20px" }} />,
                //     () => {
                //       dispatch(updateProfileActivationEnabled(true));
                //     }
                //   );
                //   return; // Prevent navigation
                // }

                // Navigate to Chat
                if (!inIframe) {
                  const path = clientType === 2 ? "/business-home/timesheet" : "/parent-home/timesheet";
                  navigate(path);
                }
                if (clientType === c.clientType.UNSPECIFIED) {
                  IframeBridge.sendToParent({
                    type: "helperInAppPaymentsNavigate",
                  });
                } else {
                  IframeBridge.sendToParent({
                    type: "inAppPaymentsNavigate",
                  });
                }
              }),
          },
        ]}
        onBackClick={() => {
          if (!inIframe) {
            const path = clientType === 2 ? "/business-home" : "/parent-home";
            navigate(path);
          }
          IframeBridge.sendToParent({
            type: "goBackFromManageJob",
          });
        }}
      />

      <div className="flex flex-column">
        <div style={{ borderBottom: "1px solid #D6D6D6" }}>
          <div style={{ justifyContent: "space-around" }} className="flex w-full mt-4 gap-2 px-2 justify-content-space-around">
            {tabNameIcon.map((tni, index) => (
              <div key={index} className="flex flex-column gap-1 align-items-center cursor-pointer relative" onClick={() => handleTabChange(index)}>
                <div style={{ flexDirection: "column" }} className="flex gap-1 align-items-center cursor-pointer">
                  <span style={{ opacity: activeTabIndex === index ? 1 : 0.5 }}>{tni.tabIcon}</span>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: activeTabIndex === index ? "700" : "500",
                      fontSize: "10px",
                      color: activeTabIndex === index ? "#585858" : "#58585880",
                      textAlign: "center",
                    }}
                  >
                    {tni.tabName}
                  </p>
                  <Badge
                    value={unAwardedCount}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "13px",
                      minWidth: "13px",
                      maxHeight: "13px",
                      maxWidth: "13px",
                      fontSize: "40%",
                      display: index === 0 && unAwardedCount > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      left: "50px",
                      bottom: "40px",
                    }}
                  />
                  <Badge
                    value={awardedCount}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "13px",
                      minWidth: "13px",
                      maxHeight: "13px",
                      maxWidth: "13px",
                      fontSize: "50%",
                      display: index === 1 && awardedCount > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      left: "45px",
                      bottom: "39px",
                    }}
                  />
                  {/* <div
                    style={{
                      backgroundColor: "#179D52",
                      minHeight: "13px",
                      minWidth: "13px",
                      maxHeight: "13px",
                      maxWidth: "13px",
                      display: index === 1 && awardedCount > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: "50%",
                      boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                      position: "absolute",
                      left: "45px",
                      bottom: "39px",
                    }}
                  >
                    <FaCheck color="#FFFFFF" fontSize="40%" />
                  </div> */}
                  <Badge
                    value={jobHistory.length}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "13px",
                      minWidth: "13px",
                      maxHeight: "13px",
                      maxWidth: "13px",
                      fontSize: "40%",
                      display: index === 2 && jobHistory.length > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      left: "35px",
                      bottom: "39px",
                    }}
                  />
                  <Badge
                    value={unRatedJobs.length}
                    style={{
                      backgroundColor: "#FF6359",
                      minHeight: "13px",
                      minWidth: "13px",
                      maxHeight: "13px",
                      maxWidth: "13px",
                      fontSize: "60%",
                      display: index === 3 && unRatedJobs.length > 0 ? "flex" : "none",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      left: "39px",
                      bottom: "37px",
                    }}
                  />
                </div>
                {activeTabIndex === index && (
                  <div
                    className="relative"
                    style={{
                      bottom: "-2px",
                      width: "100%",
                      height: "3px",
                      backgroundColor: "#FFA500",
                      borderRadius: "20px",
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        <div className="flex-grow-1 flex w-full px-3">
          <div
            className="pt-3 flex flex-column flex-grow-1  "
            style={{
              height: "100%",
              marginBottom: "calc(var(--safe-area-bottom) + 55px)",
              width: "100%",
            }}
          >
            {Number(jobId || -1) === -1 ? (
              <ActiveTab
                upComingJobs={upComingJobs}
                jobHistory={jobHistory}
                unRatedJobs={unRatedJobs}
                viewJob={(j, showAnalytics = false) => handleViewJobIdChange(j, showAnalytics)}
                refresh={() => fetchJobs()}
                isLoading={isLoading} // Pass isLoading
              />
            ) : activeTabIndex === 0 ? (
              parseInt(searchParams.get("analytics") ?? "0") === 1 ? (
                <JobAnalyticsUnawarded upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
              ) : (
                <JobSummaryUnAwarded upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
              )
            ) : activeTabIndex === 1 ? (
              <JobSummaryAwarded upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
            ) : activeTabIndex === 2 ? (
              <JobHistorySummary upComingJobs={upComingJobs} jobHistory={jobHistory} unRatedJobs={unRatedJobs} refresh={() => fetchJobs()} />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageJobs;
