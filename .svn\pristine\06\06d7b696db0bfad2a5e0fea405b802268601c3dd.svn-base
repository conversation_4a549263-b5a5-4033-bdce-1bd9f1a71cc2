import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import utils from '../../components/utils/util';
import CookiesConstant from '../../helper/cookiesConst';
import ProfileCompletionWizard from '../Parent/ProfileCompletenessWizard/ProfileCompletionWizard';
import { updateProfileActivationEnabled } from '../../store/slices/applicationSlice';
import ProfileCompletionWizardBusiness from '../Business/ProfileCompletionWizardBuisness';
import ProfileCompletionWizardHelper from './ProfileCompletionWizardHelper';

function ProfileActivation() {
    const clientType = utils.getCookie(CookiesConstant.clientType);
    const { data, loading } = useSelector((state: RootState) => state.sessionInfo);
    const { profileActivationEnabled } = useSelector((state: RootState) => state.applicationState);
    const dispatch = useDispatch<AppDispatch>();

    function renderProfileActivation(which: number) {
        if (!profileActivationEnabled) {
            return null;
        }

        if (loading) {
            return null;
        }
        if (data === null) {
            return null;
        }

        if (data['profileCompleteness'] === 100) {
            return null;
        }

        switch (String(which)) {
            case '0':
                return (
                    <ProfileCompletionWizardHelper
                        isVisible={profileActivationEnabled}
                        closeDialog={() => dispatch(updateProfileActivationEnabled(false))}
                    />
                );
            case '1':
                return (
                    <ProfileCompletionWizard
                        isVisible={profileActivationEnabled}
                        closeDialog={() => dispatch(updateProfileActivationEnabled(false))}
                    />
                );
            case '2':
                return (
                    <ProfileCompletionWizardBusiness
                        isVisible={profileActivationEnabled}
                        closeDialog={() => dispatch(updateProfileActivationEnabled(false))}
                    />
                );
            default:
                return null;
        }
    }

    return renderProfileActivation(clientType);
}

export default ProfileActivation;
