.containerPostJob {
  display: flex;
  flex-direction: column;
  height: min-content;
  justify-content: center;
  align-items: center;
  padding-top: 115px;
}
.headerPostJob {
  background-color: #ffff;
  padding: 15px;
  text-align: center;
  color: #585858;
  font-size: 30px;
  font-weight: 700;
  line-height: 45px;
  margin-bottom: 0px;
  padding-bottom: 0px;
  margin-top: 0px;
}

.instructionPostJob {
  color: #585858;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .headerPostJob {
    font-size: 24px;
    line-height: 36px;
  }

  .instructionPostJob {
    font-size: 14px;
    line-height: 20px;
  }
}

@media (max-width: 480px) {
  .headerPostJob {
    font-size: 20px;
    line-height: 30px;
  }

  .instructionPostJob {
    font-size: 12px;
    line-height: 18px;
  }
}

.instructionPostJob .breakLine {
  display: inline;
}

@media (max-width: 768px) {
  .instructionPostJob .breakLine {
    display: none;
  }
}

.mainContentPostJobs {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
}

.buttonContainer {
  margin-top: 10px;
  display: flex;
  gap: 20px;
  width: 100%;
}

.buttonPostJob {
  padding: 15px 20px;
  font-size: 16px;
  border: 3px solid #dfdfdf;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  background-color: #ffff;
  color: white;
  width: 314px;
  height: 154px;
}
.buttonPostJobHead {
  font-size: 22px;
  font-weight: 700;
  line-height: 33px;
  text-align: center;
  color: #585858;
  margin-top: 0px;
  margin-bottom: 5px;
}
.instructionPostJobText {
  font-size: 14px;
  line-height: 21px;
  font-weight: 300;
  color: #585858;
  margin-top: 0px;
}
.selectedButton {
  border: 3px solid #179d52;
}

.selectedText {
  color: #179d52;
}

.footerPostJob {
  background-color: #ffff;
  color: white;
  text-align: center;
  width: 100%;
  margin-top: auto;
}

.footerButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 10px;
}

.footerButtonPrev {
  background-color: transparent;
  color: #585858;
  width: 156px;
  height: 39px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.footerButtonPrev i {
  margin-right: 8px;
}

.footerButton {
  font-size: 14px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #ffa500;
  color: #ffff;
  width: 226px;
  height: 41px;
  border-radius: 10px;
  font-weight: 700;
}

.footerButton i {
  margin-left: 8px;
}

.footerButton:hover {
  background-color: #f0f4f7;
}

.dividerPosJob {
  width: 100%;
}

@media (max-width: 668px) {
  .footerButtons {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .buttonContainer {
    flex-direction: column;
    gap: 15px;
  }

  .button {
    width: 100%;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .button {
    padding: 10px 20px;
  }
}
.containerPostJobMobile {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  padding-top: 15px;
  background-color: #fff;
  overflow: hidden;
  overflow-y: scroll;
}
.mainContentPostJobsMobile {
  display: flex;
  justify-content: center;
  width: 100%;
  flex-grow: 1;
}
.headerPostJobMobile {
  background-color: #ffff;
  color: #585858;
  font-size: 22px;
  font-weight: 700;
  line-height: 45px;
  margin-bottom: 0px;
  padding-bottom: 0px;
  margin-top: 0px;
  /* max-width: 347px; */
}
.instructionPostJobMobile {
  color: #585858;
  font-size: 14px;
  font-weight: 300;
  margin-top: 5px;
}
.buttonPostJobMobile.selectedButtonMobile {
  border: 3px solid #179d52;
  box-shadow: 0px 4px 4px 0px #00000040;
}
/* Class for the footer container at the bottom */
.MobileFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff; /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow for better visibility */
  padding: 10px 0; /* Padding for the button */
  text-align: center;
  z-index: 999; /* Ensure it stays on top of other elements */
  margin-top: auto;
}

/* Style for the Previous button */
.footerButtonPrevMobile {
  padding: 10px 20px;
  width: 100%; /* Full width */
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #ffa500; /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Style for the Next button */
.nextButtonMobile {
  padding: 10px 20px;
  width: 80%; /* Full width */
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #ffa500; /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Style for disabled button */
.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
}

/* Optional: If you want the buttons to be stacked vertically */
.MobileFooter button {
  margin-bottom: 10px; /* Adds spacing between buttons */
}
.buttonPostJobMobile {
  padding: 15px 20px;
  font-size: 16px;
  border: 3px solid #dfdfdf;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  background-color: #ffff;
  color: white;
  height: 154px;
}
.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}
