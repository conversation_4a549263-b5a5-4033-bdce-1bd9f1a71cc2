.childCareContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0px;
  justify-content: center;
  flex-direction: column;
  padding-right: 50px;
}

.childCareBoxOne {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding-left: 25px;
  padding-top: 12px;
  color: #585858;
  padding-right: 20px;
  padding-bottom: 30px;
}

.childCareBoxTwo {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 25px;
  padding-top: 15px;
}
.childCareBoxThree {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 25px;
  padding-top: 15px;
}
.childCareBoxFour {
  width: 100%;
  height: 150px;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding: 20px;
  display: flex;
  padding: 20px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #333;
}
.childCare {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  line-height: 30px;
  margin: 0px;
}
.childCareExperience {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
  margin-top: 10px;
}
.row {
  display: flex;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  align-items: center;
  gap: 8px;
  text-wrap: nowrap;
}

.tick::after {
  content: "✔"; /* Tick mark */
  color: green;
  font-size: 16px;
  font-weight: bold;
}
.Nationality {
  display: flex;
  width: min-content;
  background-color: #f1f1f1;
  border: none;
  border-radius: 20px;
  padding: 10px 18px 10px 18px;
  gap: 6px;
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  color: #585858;

}
.checks {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
  margin: 0px;
}
.ChecksRow {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: #585858;
}
.qualificationContainer{
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
  padding-bottom: 25px;
}
.qualificationContainerTutor{
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
}
.qualificationContainerTutorSec{
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
  padding-right: 20px;
}

.tabHeader {
  margin-right: 1rem;
}
.tabHeader {
  background-color: rgb(255, 255, 255);
  border-radius: 20px;
  padding: 10px 20px;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 4px 4px 0px;
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.tabHeaderSelected {
  border: 2px solid rgb(255, 165, 0);
}
.childCareContainerMobile {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}
.childCareBoxOneMobile {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding-left: 20px;
  padding-top: 12px;
  color: #585858;
  padding-right: 20px;
  padding-bottom: 16px;
}
.childCareBoxTwoMobile {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 16px;
  padding-top: 12px;
}
.childCareBoxThreeMobile {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 16px;
  padding-top: 12px;
}
.qualificationContainerMobile{
  width: 100%;
  height: min-content;
  padding-left: 20px;
  padding-top: 12px;
  padding-bottom: 16px;
}
.qualificationContainerTutorMobile{
  width: 100%;
  height: min-content;
  padding-left: 20px;
  padding-top: 12px
}
.inputbox {
  display: flex;
  flex-direction: row;
  align-items: center;
  /* width: 1000px; */
  height: 56px;
  /* max-width: 100%; */
  min-width: 50%;
  color: #585858;
  border-radius: 10px;
  font-weight: 500;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  background-color: #f0f4f7;
  border-color: #f0f4f7;
  margin-top: 1rem;
}
.responsive-container {
  width: calc(100% - 290px);
  position: absolute;
  /* right: 0; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  margin-top: 4rem;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .responsive-container {
    width: 100%;
    position: relative;
    margin-top: 2rem;
  }
}