import React, { useState } from "react";
import calendar from "../../../../assets/images/Icons/manage_job.png";
import clock from "../../../../assets/images/Icons/clockstart.png";
import childcare from "../../../../assets/images/Icons/my_child.png";
import userProfile from '../../../../assets/images/Icons/user-profile.png';
import styles from "../../../Common/styles/job-card.module.css";
import { Divider } from "primereact/divider";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import useIsMobile from "../../../../hooks/useIsMobile";
interface JobCardProps {
  date?: {
    day: string;
    date: string;
    month: string;
    year?: string;
  };
  title?: number;
  jobOption?: any;
  upcomingJob?: string;
  jobType?: string;
  timeSlot?: string;
  location?: string;
  invited?: string;
  applications?: number;
  jobStatus?: number;
  aplicants?: Array<{
    applicantImageSrc: string;
    applicantId: number;
    applicationStatus: number;
  }>;
  ownerImageSrc: string;
  applicationStatus?: number;
  onClick?: () => void;
}
const getJobType = (jobCode: number): string => {
  if (jobCode === 256) {
    return "Odd Job";
  } else if (jobCode === 64 || jobCode === 128) {
    return "Tutoring";
  } else {
    return "Childcare";
  }
};
const cardStyle = {
  borderRadius: "10px",
  border: "1px solid #DFDFDF",
  width: "692px",
  transition: "box-shadow 0.3s ease",
};
const cardStyleMobile = {
  borderRadius: "20px",
  border: "1px solid #DFDFDF",
  width: "100%",
  transition: "box-shadow 0.3s ease",
};

const cardHoverStyle = {
  ...cardStyle,
  boxShadow: "0 4px 4px 4px rgba(0,0,0,0.25)",
};
const cardHoverStyleMobile = {
  ...cardStyleMobile,
};
const JobCardHelper: React.FC<JobCardProps> = (props) => {
  const {
    date,
    title,
    jobOption,
    upcomingJob,
    jobType,
    timeSlot,
    invited,
    applications,
    aplicants,
    applicationStatus,
    jobStatus,
    ownerImageSrc,
    onClick,
  } = { ...props };

  const clientType = utils.getCookie(CookiesConstant.clientType);
  const [isHovered, setIsHovered] = useState(false);
  const { isMobile } = useIsMobile();

  return !isMobile ? (
    <div className="h-min mb-5" style={{ width: "" }}>
      {/* Date Column  */}
      <div className="flex items-stretch">
        <div
          className="flex flex-column align-items-center"
          style={{ width: "100px" }}
        >
          <span
            className="font-semibold"
            style={{ fontSize: "18px", color: "#585858" }}
          >
            {date?.day}
          </span>
          <span
            className="font-medium"
            style={{ fontSize: "30px", color: "#585858" }}
          >
            {date?.date}
          </span>
          <span
            className="font-semibold"
            style={{ fontSize: "24px", color: "#585858", lineHeight: "20px" }}
          >
            {date?.month}
          </span>
          {/* <div className="w-2 h-2 bg-gray-700 rounded-full mt-2"></div> */}
          <div
            className=""
            style={{
              width: "1px",
              height: "50px",
              backgroundColor: "#585858",
            }}
          ></div>
          {/* Rounded black circle */}
          <div
            className=""
            style={{
              width: "10px",
              height: "10px",
              backgroundColor: "#585858",
              borderRadius: "50%",
            }}
          ></div>
        </div>

        {/* Main Card  */}
        <div className="flex flex-column">
          <div
            className="flex-1"
            style={isHovered ? cardHoverStyle : cardStyle}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="pl-3 pr-3">
              {/* Header Section  */}
              <div className="pl-3 pr-2 flex justify-content-between align-items-center ">
                <div className="flex align-items-center gap-2 pt-3 ">
                  <span className="" role="img" aria-label="childcare">
                    <img
                      alt="Clock"
                      src={childcare}
                      style={{
                        width: "27px",
                        height: "27px",
                        color: "#585858",
                      }}
                      className="font-light"
                    />
                  </span>
                  <p
                    className="p-0 m-0 font-bold"
                    style={{ fontSize: "30px", color: "#585858" }}
                  >
                    {getJobType(title)}
                  </p>
                </div>
                <div className="flex gap-2">
                  <div
                    className={`${styles.borderRadius} flex align-items-center gap-2 px-3 py-2`}
                  >
                    <span className={styles.jobType}>{jobOption}</span>
                  </div>
                  {jobStatus === c.jobStatus.AWARDED ? (
                    <div
                      className={`${styles.upcomingJob} flex align-items-center gap-2 px-3 py-2`}
                    >
                      <span className={""}>{upcomingJob}</span>
                    </div>
                  ) : null}
                </div>
              </div>

              {/* Job Details  */}
              <div className="flex flex-wrap gap-2 ">
                <div
                  className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2 `}
                >
                  <img
                    alt="Calendar"
                    src={calendar}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobType}>{jobType}</span>
                </div>
                <div
                  className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                >
                  <img
                    alt="calendar"
                    src={calendar}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobType}>
                    {" "}
                    Starting on {date?.day} {date?.date} {date?.month}{" "}
                    {date?.year}
                  </span>
                </div>
                <div
                  className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-2`}
                >
                  <img
                    alt="Clock"
                    src={clock}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobType}>{timeSlot}</span>
                </div>
              </div>
              <Divider className="mt-3" />

              {/* Status Section  */}
              <div className="pr-2 py-3 border-t">
                <div className="flex justify-content-between align-items-center">
                  <div className="space-y-1">
                    <div className="flex align-items-center gap-2">
                      <span className={`${styles.status}`}>Invite:</span>
                      <div className="relative flex">
                        <img
                          src={ownerImageSrc}
                          alt="helper display"
                          height="33px"
                          width="33px"
                          style={{ borderRadius: "50%" }}
                        />
                      </div>
                      <span
                        className={`${styles.status} font-bold`}
                        style={{ textDecoration: "underline" }}
                      >
                        {invited} has invited you!
                      </span>
                    </div>
                  </div>
                  
                </div>
              </div>

              {/* View Job Button  */}
            </div>
            {applicationStatus === c.jobApplicationStatus.PENDING ||
              applicationStatus === c.jobApplicationStatus.VIEWED ? (
              <button
                onClick={onClick}
                className="w-full py-2 text-white font-bold cursor-pointer"
                style={{
                  backgroundColor: "#ff6666",
                  textDecoration: "underline",
                  fontSize: "20px",
                  border: "none",
                  borderBottomLeftRadius: "10px",
                  borderBottomRightRadius: "10px",
                }}
              >
                Response required
              </button>
            ) : applicationStatus === c.jobApplicationStatus.APPLIED ||
              applicationStatus ===
              c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM ||
              applicationStatus ===
              c.jobApplicationStatus.EXCLUDED_BY_CLIENT ? (
              <button
                onClick={onClick}
                className="w-full py-2 text-white font-bold cursor-pointer"
                style={{
                  backgroundColor: "#4caf50",
                  textDecoration: "underline",
                  fontSize: "20px",
                  border: "none",
                  borderBottomLeftRadius: "10px",
                  borderBottomRightRadius: "10px",
                }}
              >
                Pending response
              </button>
            ) : (
              <button
                onClick={onClick}
                className="w-full py-2 text-white font-bold cursor-pointer"
                style={{
                  backgroundColor: "#FFA500",
                  textDecoration: "underline",
                  fontSize: "20px",
                  border: "none",
                  borderBottomLeftRadius: "10px",
                  borderBottomRightRadius: "10px",
                }}
              >
                View Job
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div className="h-min mb-3" style={{ width: "" }}>
      {/* Date Column  */}
      <div className="flex items-stretch justify-content-center">
        {/* Main Card  */}
        <div className="flex flex-column w-full">
          <div
            className="flex-1"
            style={isHovered ? cardHoverStyleMobile : cardStyleMobile}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={onClick}
          >
            <div className="flex flex-row px-3 pt-4 pb-3 justify-content-between">
              <div
                className="flex flex-row align-items-center gap-2 px-2"
                style={{ width: "100px" }}
              >
                <span
                  className="font-semibold"
                  style={{ fontSize: "20px", color: "#585858" }}
                >
                  {date?.day}
                </span>
                <span
                  className="font-medium"
                  style={{ fontSize: "20px", color: "#585858" }}
                >
                  {date?.date}
                </span>
                <span
                  className="font-semibold"
                  style={{
                    fontSize: "20px",
                    color: "#585858",
                    lineHeight: "20px",
                  }}
                >
                  {date?.month}
                </span>
              </div>
              <div className="flex gap-2">
                <div
                  className={`${styles.borderRadius} flex align-items-center gap-2 px-2 py-1`}
                >
                  <span style={{ fontSize: "14px", fontWeight: "700" }} className={styles.jobTypeMobile}>{jobOption}</span>
                </div>

              </div>

            </div>
            <Divider />
            <div className="pt-2">
              {/* Header Section  */}
              <div className=" flex justify-content-between align-items-center ">
                <div className="flex align-items-center gap-2 px-4">
                  <span className="" role="img" aria-label="childcare">
                    <img
                      alt="Clock"
                      src={childcare}
                      style={{ width: '15px', height: '15px', color: '#FFFFFF' }}
                      className="font-light"
                    />
                  </span>
                  <p
                    className="p-0 m-0 font-bold"
                    style={{ fontSize: "14px", color: "#585858" }}
                  >
                    {getJobType(title)}
                  </p>
                </div>

              </div>

              {/* Job Details  */}
              <div className="flex flex-wrap gap-2 flex-column px-4 ">
                <div
                  className={` flex align-items-center gap-2`}
                >
                  <img
                    alt="Calendar"
                    src={calendar}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobTypeMobile}>{jobType}</span>
                </div>
                <div
                  className={`flex align-items-center gap-2`}
                >
                  <img
                    alt="calendar"
                    src={calendar}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobTypeMobile}>
                    {" "}
                    Starting on {date?.day} {date?.date} {date?.month}{" "}
                    {date?.year}
                  </span>
                </div>
                <div
                  className={` flex align-items-center gap-2`}
                >
                  <img
                    alt="Clock"
                    src={clock}
                    style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
                  />
                  <span className={styles.jobTypeMobile}>{timeSlot}</span>
                </div>
                <div>

                  <div className="flex align-items-center gap-2">
                    <img
                      alt="userProfile"
                      src={userProfile}
                      style={{ width: "17px", height: "15px", color: "#FFFFFF" }}
                    />
                    <div className="space-y-1">
                      <div className="flex align-items-center gap-2">
                        <span className={`${styles.statusMobile}`}>Invite:</span>
                       
                        <span
                          className={`${styles.statusMobile} font-bold`}
                          style={{ textDecoration: "underline" }}
                        >
                          {invited} has invited you!
                        </span>
                        <img
                          src={ownerImageSrc}
                          alt="helper display"
                          height="33px"
                          width="33px"
                          style={{ borderRadius: "50%" }}
                        />
                      </div>
                    </div>

                  </div>
                </div>
              </div>
              <Divider className="mt-3" />
              <div className="flex flex-row justify-content-between  px-4 py-3">

                <div style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center"
                }}>
                  {jobStatus === c.jobStatus.AWARDED ? (
                    <div
                      style={{ fontSize: "14px", fontWeight: "700", color: "#179D52", textDecoration: "underline" }}
                      className={` flex align-items-center gap-2`}
                    >
                      <span className={""}>{upcomingJob}</span>
                    </div>
                  ) : null}
                </div>
                {applicationStatus === c.jobApplicationStatus.PENDING ||
                  applicationStatus === c.jobApplicationStatus.VIEWED ? (
                  <button
                    onClick={onClick}
                    className="  text-white font-bold cursor-pointer py-2 px-3"
                    style={{
                      backgroundColor: "#ff6666",
                      fontSize: "12px",
                      border: "none",
                      borderRadius: "20px"
                    }}
                  >
                    Response required
                  </button>
                ) : applicationStatus === c.jobApplicationStatus.APPLIED ||
                  applicationStatus ===
                  c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM ||
                  applicationStatus ===
                  c.jobApplicationStatus.EXCLUDED_BY_CLIENT ? (
                  <button
                    onClick={onClick}
                    className="  text-white font-bold cursor-pointer py-2 px-3 "
                    style={{
                      backgroundColor: "#4caf50",
                      fontSize: "12px",
                      border: "none",
                      borderRadius: "20px"
                    }}
                  >
                    Pending response
                  </button>
                ) : (
                  <button
                    onClick={onClick}
                    className=" text-white font-bold cursor-pointer py-2 px-3 "
                    style={{
                      backgroundColor: "#FFA500",
                      fontSize: "12px",
                      border: "none",
                      borderRadius: "20px"
                    }}
                  >
                    View Job
                  </button>
                )}
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};
export default JobCardHelper;
