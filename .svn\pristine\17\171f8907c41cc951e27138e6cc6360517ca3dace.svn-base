.container {
  width: 100%;
  height: 100%;
}

.jobPosting {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #585858;
  height: 100%;
}

.jobAddress {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #585858;
  height: auto;
  padding-top: 24px;
  width: 100%;
  max-width: 1200px;
}
.addressOptions {
  display: flex;
  margin-bottom: 30px;
  line-height: 20px;
  padding-left: 2%;
  padding-right: 2%;
  justify-content: center;
}
.headerH2 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 24px;
}

.content {
  display: flex;
  width: 52%;
  /* max-width: 592px;  */
  gap: 20px;
  text-wrap: nowrap;
}

.jobOptions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: auto;
}
.subOption {
  font-size: 16px;
  font-weight: 600px;
  color: #585858;
}
.subtext {
  font-size: 14px;
  font-weight: 300;
  color: #585858;
}
.subSubOptions {
  display: flex;
  flex-direction: column;
}
.subOptions {
  display: grid; /* Or flex */
  gap: 10px;
}
.jobOption {
  display: flex;
  align-items: center;
  /* gap: 20px; */
  padding: 19px 32px;
  border: 1px solid rgba(223, 223, 223, 1);
  border-radius: 10px;
  cursor: pointer;
  /* transition: all 0.3s ease; */
  background-color: #fff;
  font-size: 22px;
  font-weight: 600;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  user-select: none;
  height: 80px;
}

.jobOption.selected {
  border: 3px solid #179d52;
  box-shadow: 0px 4px 4px 0 rgba(0, 0, 0 0.25);
  background-color: #ffffff;
  width: 200px;
  color: #179d52;
  border-radius: 10px;
  font-weight: 700;
  font-size: 22px;
}

.jobIcon {
  width: 27px;
  height: 26px;
  border: 2px;
}
.additionalOptions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radioGroup {
  display: flex;
  gap: 20px;
}

.radioButtonLabel {
  margin-left: 27px;
  font-size: 16px;
  font-weight: 600;
  margin-top: -23px;
  line-height: 30px;
}

.subtext {
  color: #777;
  margin-top: -4px;
  padding-left: 28px;
  font-size: 14px;
  /* text-wrap: wrap; */
}
@media (max-width: 1284px) {
  .subtext {
    white-space: normal; /* Enables text wrapping */
  }
}
.nextButton {
  padding: 8px 30px;
  border: none;
  border-radius: 10px;
  background-color: #ffa500;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  margin-top: 5%;
  width: 100%;
  height: 41px;
}

.nextButton:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
  width: 100%;
}

@media (max-width: 768px) {
  .content {
    flex-direction: column;
  }

  .additionalOptions {
    margin-top: 20px;
  }

  .nextButton {
    width: 100%;
  }
}

/* .jobLabel {
  margin-inline: auto;
} */
.label {
  display: flex;
  align-items: center;
}

.addressOption.selected {
  font-size: 16px;
  font-weight: 600;
  color: #585858;
}
.addressOptions {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}
.input[type="radio"] {
  accent-color: #179d52;
  height: 16px;
  width: 15px;
  color: #dfdfdf;
  margin-right: 8px;
}
.Mobilecontainer {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
  overflow-y: scroll;
  position: relative;
}
.headerH2Mobile {
  font-size: 22px;
  font-weight: 700;
  color: #585858;
  line-height: 33px;
  padding-left: 10px;
}
.jobPostingMobile{
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #585858;
    height: 100%;
}
.jobOptionMobile {
  display: flex;
  align-items: center;
  padding: 13px 32px;
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  cursor: pointer;
  background-color: #fff;
  font-size: 22px;
  font-weight: 600;
  user-select: none;
  width: 100%;
  max-width: 95%;
  flex-direction: column;
}
.jobOptionMobile.selected {
  border: 3px solid #179d52;
  box-shadow: 0px 4px 4px 0 rgba(0, 0, 0 0.25);
  background-color: #ffffff;
  color: #179d52;
  border-radius: 10px;
  font-weight: 700;
  padding: 13px 32px;
  font-size: 22px;
  flex-direction: column;
}
.contentMobile {
  display: flex;
  width: 100%;
  gap: 20px;
  text-wrap: nowrap;
  flex-direction: column;
  align-items: center;
}
.jobIconMobile {
  width: 27px;
  height: 26px;
  border: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.borderDivMobile {
  border-radius: 50%;
  border: 1px solid #dfdfdf;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 10px;
  padding-block: 9px;
}
.borderDivMobile.selected {
  border-radius: 50%;
  border: 2px solid #179d52;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 10px;
  padding-block: 9px;
}
.subOptionsMobile {
  display: grid; /* Or flex */
  gap: 30px;
  margin-left: 10px;
}
.subOptionsBtnsMobile {
  height: 50px;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  justify-content: center;
}
.subSubOptionsMobile {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.subSubOptionsRecurring {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}
.undoButtonMobile {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  margin-top: 10px;
  width: min-content;
  border: none;
  color: #585858;
  font-size: 14px;
  font-weight: 400;
  text-decoration: underline;
  background-color: transparent;
  cursor: pointer;
}
.dropdownAdressMobile{
  width: 348px;
  height: 50px;
  background-color: transparent;
  border-radius: 10px;
  border: 1px solid #DFDFDF;
}
.dropdownAdressMobile > span{
  background-color: transparent;
}
.dropdownAdressMobile > .p-dropdown:not(.p-disabled).p-focus {
  border: 2px solid  #179d52 !important;
}
.adressTagMobile{
  font-size: 22px;
  color: #179d52;
  font-weight: 700;
  margin-top: 10px;
  margin-bottom: 0px;
}
/* Mobile Layout Styles for Job Address */
.jobAddressMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #585858;
  height: auto;
  width: 100%;
  max-width: 1200px;
}
.addressOptionsMobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding-inline: 20px;
}

.customDropdownMobile {
  position: relative;
  width: 100%;
}

.dropdownButtonMobile {
  width: 100%;
  padding: 12px;
  font-size: 12px;
  font-weight: 700;
  border: 1px solid #DFDFDF;
  border-radius: 8px;
  background-color: #fff;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.dropdownButtonMobile.dropdownSelected {
  width: 100%;
  padding: 12px;
  font-size: 12px;
  font-weight: 700;
  border:2px solid #179D52; 
  border-radius: 8px;
  color: #179D52;
  background-color: #fff;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}
.dropdownSelected {
  border:2px solid #179D52; /* Green border when selected */
}
.dropdownListMobile {
  position: absolute;
  width: 100%;
  top: 100%;
  left: 0;
  background-color: #fff;
  border-radius: 10px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0px 0px 4px 0px #00000040;
  z-index: 10;
}

.dropdownItemMobile {
  padding: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
/* Style for the fixed footer container */
.fixedFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff; /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow for better visibility */
  padding: 10px 0; /* Padding for the button */
  text-align: center;
  z-index: 999; /* Ensure it stays on top of other elements */
  margin-top: auto;
}

/* Style for the Save button */
.nextButtonMobile {
  padding: 10px 20px;
  width: 311px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #FFA500; /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
  width: 100%;
  padding: 10px 20px;
  width: 311px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.dialogContentMobile{
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow-y: scroll;
  max-height: 80%;
  max-height: calc(100vh - 150px);
  border-top-right-radius: 30px;
  border-top-left-radius: 30px;
}
.dialogFooter {
  position: sticky; /* Keeps the footer at the bottom of the dialog */
  bottom: 0;
  background: #fff; /* Matches the dialog background */
  padding: 10px 20px;
  border-top: 1px solid #ddd; /* Optional: adds a top border for separation */
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
}

.CloseBtn {
  position: absolute;
  display: flex;
  justify-content: end;
  width: 25px;
  height: 27px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  cursor: pointer;
  right: 23px;
  top: 14px;
}
.saveAddressMobile{
  font-size: 14px;
  background-color: #ffa500;
  color: white;
  padding: 10px;
  border: none;
  border-radius: 8px;
  width: 311px;
  height: 45px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: 700;
}
.saveAddressMobile:disabled {
  background-color: #DFDFDF;
  color: #FFFFFF;
  cursor: not-allowed;
}
.addAddressContainerMobile {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 25px;
  box-sizing: border-box;
}
.dropdownContainerCountryMobile {
  width: 118px;
  margin-top: 18px;
}
.dropdownContainerCountryMobile{
  border-radius: 10px !important;
  border: none !important;
  background-color: #f0f4f7 !important;
  width: 259px !important;
}
.jobOptionsMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  height: auto;
  width: 100%;
}