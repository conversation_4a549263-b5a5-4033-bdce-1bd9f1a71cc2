import React, { CSSProperties, useState } from 'react';
import styles from '../../styles/odd-jobs.module.css';
import earth from '../../../../assets/images/Icons/earth.png';
import tickIcon from '../../../../assets/images/Icons/check-green.png';
import tickBlack from '../../../../assets/images/Icons/check-star.png';
import { ExtendedProfileTabProps } from '../types';
import { Divider } from 'primereact/divider';
import degree from '../../../../assets/images/Icons/degree.png';
import star from '../../../../assets/images/Icons/star.png';
import rank from '../../../../assets/images/Icons/rank.png';
import c from '../../../../helper/juggleStreetConstants';
import useIsMobile from '../../../../hooks/useIsMobile';
import { PiFirstAidKit } from 'react-icons/pi';

interface ChecksProps {
    date1: string;
    date2: string;
}

interface Qualification {
    optionId: number;
    text: string;
    selected: boolean;
    canSelectCategory: boolean;
    childCategory: number;
    children: Qualification[];
}
const LimitedText = ({
    text,
    limit,
    disableLimit,
    style,
    className,
}: {
    text: string;
    limit: number;
    disableLimit: boolean;
    style?: CSSProperties;
    className?: string;
}) => {
    const displayText = disableLimit || text.length <= limit ? text : text.slice(0, limit);
    return (
        <span className={className} style={{ ...style }}>
            {displayText}
        </span>
    );
};
const FirstAid = ({ data }: { data: string[] }) => {
    const{isMobile}=useIsMobile()
    return (
        <div className='px-4 pt-2 mt-2 mb-4'>
            <h1
                className='m-0 p-0'
                style={{
                    fontWeight: '700',
                    fontSize: '20px',
                    color: '#585858',
                }}
            >
               First Aid Accreditations
            </h1>
            {data.map((val, index) => (
                <div key={index} className='flex gap-2 mt-2 mx-2 align-items-center'>
                   <PiFirstAidKit />
                    <p
                        className='m-0 p-0'
                        style={{
                            fontWeight: '400',
                            fontSize:!isMobile ? '16px' : '14px',
                            color: '#585858',
                        }}
                    >
                        {val}
                    </p>
                </div>
            ))}
        </div>
    );
};
const Checks: React.FC<ChecksProps> = ({ date1, date2 }) => {
    const {isMobile}=useIsMobile();
    return (
        <>
        <div className='px-4'>
            <h1
                className='m-0 p-0'
                style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                }}
            >
                Checks
            </h1>
            <div className={!isMobile ? `${'flex gap-2 mt-2 mx-2'}` : `${'flex gap-2 mt-2 '}`}>
                {!isMobile ? (
                     <img src={tickBlack} alt='check' height='23px' width='23px' />
                ):(
                    <img src={tickBlack} alt='check' height='18px' width='18px' />
                )}
                <p
                    className='m-0 p-0'
                    style={{
                        fontWeight: '600',
                        fontSize:!isMobile ? '16px' : '14px',
                        color: '#585858',
                    }}
                >
                    Working With Children Check
                </p>
            </div>
            <p
                className='m-0 p-0 mt-2 mb-3'
                style={{
                    fontWeight: '700',
                    fontSize: '12px',
                    color: '#179D52',
                }}
            >
                {`Verified on: ${date1} | Expires on: ${date2}`}
            </p>
          
        </div>
        <Divider />
        </>
    );
};

const ChildcareQualification: React.FC<{ helper: any }> = ({ helper }) => {
    const {isMobile}=useIsMobile();
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
                marginTop: '10px',
            }}
        >
            {helper.qualifications
                .filter((val) => val.selected === true)
                .map((qualification: Qualification) => (
                    <div
                        key={qualification.optionId}
                        style={{
                            fontWeight: '600',
                            fontSize:!isMobile ? '16px' : '14px',
                            color: '#585858',
                            display: 'flex',
                            gap: '10px',
                        }}
                    >
                                 {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
                        <span>{qualification.text}</span>
                        {qualification.children && qualification.children.length > 0 && (
                            <div style={{ marginLeft: '1rem' }}>
                                {qualification.children.map((child: Qualification) => (
                                    <div
                                        key={child.optionId}
                                        style={{
                                            fontWeight: '600',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    >
                                        {child.text}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ))}
        </div>
    );
};
const TutoringQualification: React.FC<{ helper: any }> = ({ helper }) => {
    const {isMobile}=useIsMobile()
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
                marginTop: '10px',
            }}
        >
            {helper.tutoringQualifications
                .filter((val) => val.selected === true)
                .map((tutoringQualifications: Qualification) => (
                    <div
                        key={tutoringQualifications.optionId}
                        style={{
                            fontWeight: '600',
                            fontSize: '16px',
                            color: '#585858',
                            display: 'flex',
                            gap: '10px',
                        }}
                    >
                       {!isMobile ? (
              <img src={degree} alt="degree" height="23px" width="23px" />
            ) : (
              <img src={degree} alt="degree" height="19.82px" width="18.62px" />
            )}
                        <span>{tutoringQualifications.text}</span>
                        {tutoringQualifications.children &&
                            tutoringQualifications.children.length > 0 && (
                                <div style={{ marginLeft: '1rem' }}>
                                    {tutoringQualifications.children.map((child: Qualification) => (
                                        <div
                                            key={child.optionId}
                                            style={{
                                                fontWeight: '600',
                                                fontSize: '16px',
                                                color: '#585858',
                                            }}
                                        >
                                            {child.text}
                                        </div>
                                    ))}
                                </div>
                            )}
                    </div>
                ))}
        </div>
    );
};
const ReviewAndRatingHead = ({
    rating,
    ratingCount,
    isSuperHelper,
}: {
    rating: number;
    ratingCount: number;
    isSuperHelper: boolean;
}) => {
    return (
        <div className='mb-4'>
            <div className='flex justify-content-between align-items-center'>
                <div>
                    <h1
                        className='m-0 p-0'
                        style={{
                            fontWeight: '700',
                            fontSize: '20px',
                            color: '#585858',
                        }}
                    >
                        Reviews
                    </h1>
                    <div className='flex gap-1'>
                        <img src={star} alt='star' width='19.82px' height='18.62px' />
                        <p
                            className='m-0 p-0'
                            style={{
                                fontWeight: '300',
                                fontSize: '14px',
                                color: '#585858',
                            }}
                        >
                            {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
                        </p>
                    </div>
                </div>
                {isSuperHelper && (
                    <div className='flex gap-2 align-items-center'>
                        <img src={rank} alt='star' width='19.82px' height='18.62px' />
                        <p
                            className='m-0 p-0'
                            style={{
                                fontWeight: '700',
                                fontSize: '18px',
                                color: '#585858',
                            }}
                        >
                            Super Helper
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};
const ReviewAndRatingList = ({
    ratings,
}: {
    ratings: Array<{
        clientFirstName: string;
        clientLastName: string;
        feedback: string;
        ratingDate: string;
        ratingAvg: number;
        clientImageUrl: string;
    }>;
}) => {
    return (
        <div className='flex flex-column  pt-2 mt-2 mb-4'>
            {ratings.map((rating, index) => (
                <React.Fragment key={index}>
                    <Divider />
                    <div className='flex gap-2 my-2'>
                        <div
                            style={{
                                height: '38px',
                                width: '38px',
                                background: 'gray',
                                borderRadius: '50%',
                                overflow: 'hidden',
                                 minWidth:"38px"
                            }}
                        >
                            <img
                                src={rating.clientImageUrl}
                                alt='client Image'
                                width='100%'
                                height='100%'
                            />
                        </div>
                        <div className='flex-grow-1 flex flex-column gap-2'>
                            <div className='flex'>
                                <div className='flex-grow-1 flex flex-column'>
                                    <p
                                        className='m-0 p-0'
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '16px',
                                            color: '#585858',
                                        }}
                                    >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                                    <p
                                        className='m-0 p-0'
                                        style={{
                                            fontWeight: '400',
                                            fontSize: '12px',
                                            color: '#585858',
                                        }}
                                    >
                                        {new Date(rating.ratingDate).toLocaleDateString('en-GB')}
                                    </p>
                                </div>
                                <div style={{ display: 'flex', flexDirection: 'row', gap: '5px' }}>
                                    <img
                                        src={star}
                                        alt='star'
                                        width='19.82px'
                                        height='18.62px'
                                        style={{ marginTop: '2px' }}
                                    />
                                    <p
                                        className='m-0 p-0'
                                        style={{
                                            fontWeight: '600',
                                            fontSize: '18px',
                                            color: '#585858',
                                        }}
                                    >
                                        {`${rating.ratingAvg.toFixed(1)}`}
                                    </p>
                                </div>
                            </div>
                            <p
                                className='m-0 p-0'
                                style={{
                                    fontWeight: '400',
                                    fontSize: '14px',
                                    color: '#585858',
                                }}
                            >
                                {rating.feedback}
                            </p>
                        </div>
                    </div>
                </React.Fragment>
            ))}
        </div>
    );
};

const OddJobsTab: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
    const [textState, toggleTextState] = useState<boolean>(false);
    const displayLimit = 300;
    const text = helper.providerMyExperience || '';
    const {isMobile}=useIsMobile();
    const nat = c.countriesIso.find(
        (country) =>
            country.alpha2.toLowerCase() === helper?.nationality.toLowerCase() ||
            country.value.toLowerCase() === helper?.nationality.toLowerCase()
    );
    return (
        <div className={ !isMobile  ? `${styles.childCareContainer}` : `${styles.childCareContainerMobile}`}>
            <div className={styles.childCareBoxOne}>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '390px' }}>
                    <h1
                        style={{
                            fontSize: '16px',
                            fontWeight: '700',
                            color: '#585858',
                            margin: '0px',
                        }}
                    >
                        Services
                    </h1>
                </div>

                <div
                    style={{
                        display: 'flex',
                        flexDirection:!isMobile ? 'row' : 'column',
                        gap:!isMobile ? '60px' : '10px',
                        paddingLeft: '10px',
                        marginTop: '10px',
                        textWrap: 'nowrap',
                    }}
                >
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <div
                            className={styles.checkboxOption}
                            style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                        >
                            <input
                                readOnly
                                type='checkbox'
                                checked={helper.oddJobPreferences.laundry === true}
                                value='Laundry'
                                className={styles.checkboxInput}
                            />
                            <label className={!isMobile ? `${styles.checkboxLabel}` : `${styles.checkboxLabelMobile}`}>Laundry</label>
                        </div>

                        <div
                            className={styles.checkboxOption}
                            style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                        >
                            <input
                                readOnly
                                type='checkbox'
                                checked={helper.oddJobPreferences.errands === true}
                                value='Errand running'
                                className={styles.checkboxInput}
                            />
                            <label className={!isMobile ? `${styles.checkboxLabel}` : `${styles.checkboxLabelMobile}`}>Errand running</label>
                        </div>
                        <div
                            className={styles.checkboxOption}
                            style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                        >
                            <input
                                readOnly
                                type='checkbox'
                                checked={helper.oddJobPreferences.otherOddJobs === true}
                                value='Other odd jobs'
                                className={styles.checkboxInput}
                            />
                            <label  className={!isMobile ? `${styles.checkboxLabel}` : `${styles.checkboxLabelMobile}`}>Other odd jobs</label>
                        </div>
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <div
                            className={styles.checkboxOption}
                            style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                        >
                            <input
                                readOnly
                                type='checkbox'
                                checked={helper.oddJobPreferences.outdoorChores === true}
                                value='Outdoor chores'
                                className={styles.checkboxInput}
                            />
                            <label className={!isMobile ? `${styles.checkboxLabel}` : `${styles.checkboxLabelMobile}`}>Outdoor chores</label>
                        </div>

                        <div
                            className={styles.checkboxOption}
                            style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                        >
                            <input
                                readOnly
                                type='checkbox'
                                checked={helper.oddJobPreferences.elderlyHelp === true}
                                value='>Help for the elderly'
                                className={styles.checkboxInput}
                            />
                            <label  className={!isMobile ? `${styles.checkboxLabel}` : `${styles.checkboxLabelMobile}`}>Help for the elderly</label>
                        </div>
                    </div>

                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: !isMobile ? 'center' : 'end',
                            marginLeft: '15px',
                            gap: '5px',
                        }}
                    >
                        <h1
                            style={{
                                fontSize: '16px',
                                fontWeight: '700',
                                color: '#585858',
                                margin: '0px',
                            }}
                        >
                            Nationality
                        </h1>
                        <button className={styles.Nationality}>
                            <img src={earth} alt='' />
                            {nat?.label || 'Unknown'}
                        </button>
                    </div>
                </div>
            </div>
            {(helper?.firstAid.filter((x) => x.selected === true).length ?? 0) > 0 && (
                      <div className={styles.childCareBoxTwo}>
                    <FirstAid
                       data={helper.firstAid
                        .filter((val) => val.selected === true)
                        .map((val) => val.text)}
                    />
                     </div>
                )}
            <div className={styles.childCareBoxTwo}>
                {(helper?.certificates?.length ?? 0) > 0 && (
                    <Checks
                        date1={new Date(helper.certificates[0].verificationDate).toLocaleDateString(
                            'en-GB'
                        )}
                        date2={new Date(helper.certificates[0].expiryDate).toLocaleDateString(
                            'en-GB'
                        )}
                    />
                )}

                {helper?.qualifications.filter((x) => x.selected === true).length > 0 && (
                    <div className={styles.qualificationContainer}>
                        <h1
                            className='m-0 p-0'
                            style={{
                                fontWeight: '400',
                                fontSize: '16px',
                                color: '#585858',
                            }}
                        >
                            Childcare Qualification
                        </h1>
                        <div>
                            <ChildcareQualification helper={helper} />
                        </div>
                    </div>
                )}
                {helper?.tutoringQualifications.filter((x) => x.selected === true).length > 0 && (
                    <>
                        <Divider />
                        <div className={styles.qualificationContainerTutor}>
                            <h1
                                className='m-0 p-0'
                                style={{
                                    fontWeight: '400',
                                    fontSize: '16px',
                                    color: '#585858',
                                }}
                            >
                                Tutoring Qualification
                            </h1>
                            <div>
                                <TutoringQualification helper={helper} />
                            </div>
                        </div>
                    </>
                )}
            </div>
            {helper?.hasVouches && (
      <div className={styles.childCareBoxThree}>
      <div className="px-4 pt-2 mt-2 mb-4">
      <div className="flex justify-content-between align-items-center">
        <div className="flex flex-column gap-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: !isMobile ? "20px" : "16px",
              color: "#585858",
            }}
          >
           References
          </h1>
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "16px",
              color: "#585858",
            }}
          >
           Available on request. Please contact Customer Service to obtain referee details.
          </h1>
        </div>
       
      </div>
      </div>
      </div>
            )}
            {(helper?.ratingsExtended.length ?? 0) > 0 && (
            <div className={styles.childCareBoxThree}>
                <div className={styles.qualificationContainerTutorSec}>
                    <div>
                      
                            <>
                                <ReviewAndRatingHead
                                    rating={helper?.providerRatingsAvg ?? 0}
                                    ratingCount={helper?.providerRatingsCount ?? 0}
                                    isSuperHelper={helper?.isSuperProvider ?? false}
                                />
                                {(helper?.ratingsExtended.length ?? 0) > 0 && (
                                    <ReviewAndRatingList ratings={helper.ratingsExtended} />
                                )}
                            </>
              
                    </div>
                </div>
            </div>
                      )}
        </div>
    );
};

export default OddJobsTab;
