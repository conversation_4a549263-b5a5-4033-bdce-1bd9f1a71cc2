import React, { createContext, CSSProperties, useContext, useEffect, useRef, useState } from "react";

// import ClockStartImg from '../../../assets/images/Icons/clockstart.png';
import ClockStartImg from "../../../../../assets/images/Icons/clockstart.png";
import ClockEndImg from "../../../../../assets/images/Icons/clockend.png";
import RemoveMinusImg from "../../../../../assets/images/Icons/remove-minus.png";
import AddShiftImg from "../../../../../assets/images/Icons/add.png";
import PencilImg from "../../../../../assets/images/Icons/pencil-green.png";
import RemoveImg from "../../../../../assets/images/Icons/remove.png";
import ClockGreenImg from "../../../../../assets/images/Icons/clock-Icon-start-green.png";
import CalenderImg from "../../../../../assets/images/Icons/Icon (1).png";
import CalenderGreenImg from "../../../../../assets/images/Icons/calendar-green.png";
import { FaChevronDown } from "react-icons/fa6";
import { Calendar } from "primereact/calendar";
import { IoMdClose } from "react-icons/io";
import { IoChevronDown } from "react-icons/io5";
import { JobManagementScreens, PayloadTemplate } from "../../provider/JobManagerProvider";
import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
// import { GoBack, Next, RadioButton } from '../../Buttons';
import useIsMobile from "../../../../../hooks/useIsMobile";
import { GoBack, Next, RadioButton } from "../Buttons";

type ScheduleContext = {
  unFilledTime: boolean;
  toggleUnFilledTime: (state: boolean) => void;
};

const scheduleContext = createContext<ScheduleContext>({
  unFilledTime: false,
  toggleUnFilledTime: () => {},
});

const shortDays = ["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"];

const styles: {
  LTB: {
    active: CSSProperties;
    disable: CSSProperties;
  };
  TB: {
    active: CSSProperties;
    disable: CSSProperties;
  };
  RTB: {
    active: CSSProperties;
    disable: CSSProperties;
  };
} = {
  LTB: {
    active: {
      borderLeft: "2px solid #179D52",
      borderTop: "2px solid #179D52",
      borderBottom: "2px solid #179D52",
      borderTopLeftRadius: "10px",
      borderBottomLeftRadius: "10px",
      color: "#179D52",
    },
    disable: {
      borderLeft: "1px solid #DFDFDF",
      borderTop: "1px solid #DFDFDF",
      borderBottom: "1px solid #DFDFDF",
      borderTopLeftRadius: "10px",
      borderBottomLeftRadius: "10px",
      color: "#DFDFDF",
    },
  },
  TB: {
    active: {
      borderTop: "2px solid #179D52",
      borderBottom: "2px solid #179D52",

      color: "#179D52",
    },
    disable: {
      borderTop: "1px solid #DFDFDF",
      borderBottom: "1px solid #DFDFDF",

      color: "#DFDFDF",
    },
  },
  RTB: {
    active: {
      borderRight: "2px solid #179D52",
      borderTop: "2px solid #179D52",
      borderBottom: "2px solid #179D52",

      borderTopRightRadius: "10px",
      borderBottomRightRadius: "10px",
      color: "#179D52",
    },
    disable: {
      borderRight: "1px solid #DFDFDF",
      borderTop: "1px solid #DFDFDF",
      borderBottom: "1px solid #DFDFDF",
      borderTopRightRadius: "10px",
      borderBottomRightRadius: "10px",
      color: "#DFDFDF",
    },
  },
};

function isTutoringJob(jobType: number) {
  return [64, 128].includes(jobType);
}

function isShiftFilled(shiftEntries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"], checkAllEntries: boolean = false): boolean {
  let result = false;

  if (shiftEntries?.length === 0) return result;

  if (checkAllEntries) {
    result = shiftEntries?.every((se) => se.jobStartTime && se.jobEndTime && se.shifts?.every((s) => s.jobStartTime && s.jobEndTime));
  } else {
    const se1 = shiftEntries && shiftEntries[0];
    if (se1 && se1.jobStartTime && se1.jobEndTime && se1.shifts?.every((s) => s.jobStartTime && s.jobEndTime)) {
      result = true;
    }
  }

  return result;
}

function getJobName(jobType, subJobType = 0): { job: string; shift: string[] } {
  const mappings = {
    jobType: {
      1: { job: "Ad-hoc Childcare", shift: ["Morning", "Afternoon"] },
      2: { job: "Recurring Childcare", shift: ["Morning", "Afternoon"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["After School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
      64: {
        job: "Primary School Tutoring",
        shift: ["Tutoring Shift", "Second Shift"],
      },
      128: {
        job: "High School Tutoring",
        shift: ["Tutoring Shift", "Second Shift"],
      },
      256: { job: "Odd Job", shift: ["Morning", "Afternoon"] },
    },
    subJobType: {
      1: { job: "Laundry", shift: ["Morning", "Afternoon"] },
      2: { job: "Errand Running", shift: ["Morning", "Afternoon"] },
      4: { job: "Outdoor Chores", shift: ["Morning", "Afternoon"] },
      8: { job: "Help for the Elderly", shift: ["Morning", "Afternoon"] },
      16: { job: "Other Odd Jobs", shift: ["Morning", "Afternoon"] },
    },
    recurringChildcare: {
      2: { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["After School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
    },
  };

  if (jobType === 2) {
    return subJobType === 0
      ? { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] }
      : mappings.recurringChildcare[jobType] || {
          job: "Recurring Childcare",
          shift: ["Morning", "Afternoon"],
        };
  }

  if (jobType === 256 && subJobType > 0) {
    return (
      mappings.subJobType[subJobType] || {
        job: "Odd Job",
        shift: ["Morning", "Afternoon"],
      }
    );
  }

  return (
    mappings.jobType[jobType] || {
      job: "Unknown Job",
      shift: ["Morning", "Afternoon"],
    }
  );
}

function isNextDay(startTime: Date | string | null, endTime: Date | string | null): boolean {
  if (!startTime || !endTime) {
    return false;
  }

  const parseTime = (time: Date | string) => {
    if (typeof time === "string") {
      const [hours, minutes] = time.split(":").map(Number);
      return { hours, minutes };
    } else if (time instanceof Date) {
      return { hours: time.getHours(), minutes: time.getMinutes(), day: time.getDate() };
    }
    throw new Error("Invalid time format");
  };

  const start = parseTime(startTime);
  const end = parseTime(endTime);

  if (startTime instanceof Date && endTime instanceof Date) {
    return endTime.getDate() !== startTime.getDate();
  }

  if (
    end.hours < start.hours ||
    (end.hours === start.hours && end.minutes < start.minutes) ||
    (end.hours === start.hours && end.minutes === start.minutes)
  ) {
    return true;
  }

  return false;
}

function convertTimeToDate(time: string | null, shift?: "1st" | "2nd", timeType?: "start" | "end", jobType?: number): Date {
  const now = new Date();

  if (!time) {
    let hours = 0;
    let minutes = 0;

    if (jobType === 8) {
      // After School Care
      if (timeType === "start") {
        hours = 15; // 3:00 PM
      } else if (timeType === "end") {
        hours = 18; // 6:00 PM
      }
    } else if (jobType === 4) {
      // Before School Care
      if (timeType === "start") {
        hours = 6; // 6:00 AM
      } else if (timeType === "end") {
        hours = 9; // 9:00 AM
      }
    } else if (jobType === 2) {
      // nannyin  Care
      if (shift === "1st") {
        if (timeType === "start") {
          hours = 6; // 6:00 AM
        } else if (timeType === "end") {
          hours = 18; // 6:00 pM
        }
      } else {
        if (timeType === "start") {
          hours = 15; // 6:00 AM
        } else if (timeType === "end") {
          hours = 18; // 6:00 pM
        }
      }
    } else if (jobType === 128 || jobType === 64) {
      //Tutoring
      if (shift === "1st") {
        if (timeType === "start") {
          hours = 16; // 6:00 pm
        } else if (timeType === "end") {
          hours = 17; // 7:00 pM
        }
      } else {
        if (timeType === "start") {
          hours = 19; // 7:00 AM
        } else if (timeType === "end") {
          hours = 20; // 8:00 pM
        }
      }
    } else {
      // Other job types
      if (shift === "2nd") {
        if (timeType === "start") {
          hours = 15; // 3:00 PM
        } else if (timeType === "end") {
          hours = 18; // 6:00 PM
        }
      } else {
        if (timeType === "start") {
          hours = 6; // 6:00 AM
        } else if (timeType === "end") {
          hours = 9; // 9:00 AM
        }
      }
    }

    now.setHours(hours, minutes, 0, 0);
    return now;
  }

  const timeParts = time.split(":").map(Number);
  const hours = timeParts[0];
  const minutes = timeParts[1];
  const seconds = timeParts.length > 2 ? timeParts[2] : 0;

  now.setHours(hours, minutes, seconds, 0);

  return now;
}

function formatDateToTimeString(date: Date): string {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

function convertTo12HourFormat(time: string): string {
  if (!time) {
    throw new Error("Time string cannot be null or empty");
  }

  const [hourString, minuteString, secondString] = time.split(":");
  const hours = parseInt(hourString, 10);
  const minutes = parseInt(minuteString, 10);

  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    throw new Error(`Invalid time format: ${time}`);
  }

  const period = hours >= 12 ? "PM" : "AM";
  const adjustedHours = hours % 12 || 12;

  const formattedMinutes = minuteString.padStart(2, "0");
  return `${adjustedHours}:${formattedMinutes} ${period}`;
}

function addTimeToDate(date: Date, increment: "halfHour" | "oneHour"): Date {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error("Invalid date object provided.");
  }

  const updatedDate = new Date(date);

  if (increment === "halfHour") {
    updatedDate.setMinutes(updatedDate.getMinutes() + 30);
  } else if (increment === "oneHour") {
    updatedDate.setHours(updatedDate.getHours() + 1);
  }

  return updatedDate;
}

function calculateTimeDifference(time1: string, time2: string): number {
  const [startHours, startMinutes] = time1.split(":").map(Number);
  const [endHours, endMinutes] = time2.split(":").map(Number);

  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;

  let differenceInMinutes = endTotalMinutes - startTotalMinutes;
  if (differenceInMinutes < 0) {
    differenceInMinutes += 24 * 60;
  }

  const decimalHours = differenceInMinutes / 60;

  return Number(decimalHours.toFixed(2));
}

function getMondayDate(date: Date): Date {
  const givenDate = new Date(date);
  const day = givenDate.getDay();
  const diff = (day === 0 ? -6 : 1) - day;
  givenDate.setDate(givenDate.getDate() + diff);
  return givenDate;
}

function convertDate(input: Date | string): Date | string {
  if (input instanceof Date) {
    const year = input.getFullYear();
    const month = String(input.getMonth() + 1).padStart(2, "0");
    const day = String(input.getDate()).padStart(2, "0");
    const hours = "00";
    const minutes = "00";
    const seconds = "00";

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  } else if (typeof input === "string") {
    return new Date(input);
  } else {
    return null;
  }
}

function isDateLessThanToday(date: Date): boolean {
  const today = new Date();

  date?.setHours(0, 0, 0, 0);
  today?.setHours(0, 0, 0, 0);

  return date < today;
}

interface DaysOfWeek {
  dayOfWeek: number;
  isRequired: boolean;
}

// function useShiftEntries(dows, currentPayload: Partial<PayloadTemplate>) {
//   const [shiftEntries, setShiftEntries] = useState<PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]>(
//     currentPayload?.weeklySchedule?.weeklyScheduleEntries
//   );

//   const updateEntries = (entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]) => {
//     setShiftEntries(entries);
//   };

//   useEffect(() => {
//     const newShiftEntries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = [];
//     dows
//       ?.filter((dow) => dow.isRequired === true)
//       .forEach((dow) => {
//         const existingEntry: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0] = shiftEntries.find(
//           (se: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]) => se.dayOfWeek === dow.dayOfWeek
//         );
//         if (!!existingEntry) {
//           const newShift: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]["shifts"] = [];
//           existingEntry.shifts.forEach((s) => {
//             newShift.push(s);
//           });
//           newShiftEntries.push({
//             ...existingEntry,
//             shifts: newShift,
//           });
//         } else {
//           const newShift: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]["shifts"] = [];
//           newShift.push({
//             hourlyPrice: null,
//             isRequired: true,
//             jobStartTime: "",
//             jobEndTime: "",
//             price: null,
//             shiftType: 1,
//           });
//           if (!isTutoringJob(currentPayload.jobType) && ![4, 8].includes(currentPayload.jobType)) {
//             newShift.push({
//               hourlyPrice: null,
//               isRequired: true,
//               jobStartTime: "",
//               jobEndTime: "",
//               price: null,
//               shiftType: 2,
//             });
//           }
//           newShiftEntries.push({
//             dayOfWeek: dow.dayOfWeek,
//             isRequired: dow.isRequired,
//             hourlyPrice: null,
//             jobStartTime: "",
//             jobEndTime: "",
//             price: null,
//             shifts: newShift,
//           });
//         }
//       });
//     setShiftEntries(newShiftEntries);
//   }, [dows, currentPayload]);

//   return { shiftEntries, updateEntries };
// }
function useShiftEntries(dows, currentPayload: Partial<PayloadTemplate>) {
  const [shiftEntries, setShiftEntries] = useState<PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]>(
    currentPayload?.weeklySchedule?.weeklyScheduleEntries || []
  );

  const updateEntries = (entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]) => {
    setShiftEntries(entries);
  };

  // Existing useEffect for initializing shiftEntries based on dows
  useEffect(() => {
    const newShiftEntries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = [];
    dows
      ?.filter((dow) => dow.isRequired === true)
      .forEach((dow) => {
        const existingEntry: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0] = shiftEntries.find(
          (se: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]) => se.dayOfWeek === dow.dayOfWeek
        );
        if (!!existingEntry) {
          const newShift: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]["shifts"] = [];
          existingEntry.shifts.forEach((s) => {
            newShift.push(s);
          });
          newShiftEntries.push({
            ...existingEntry,
            shifts: newShift,
          });
        } else {
          const newShift: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]["shifts"] = [];
          newShift.push({
            hourlyPrice: null,
            isRequired: true,
            jobStartTime: "",
            jobEndTime: "",
            price: null,
            shiftType: 1,
          });
          if (!isTutoringJob(currentPayload.jobType) && ![4, 8].includes(currentPayload.jobType)) {
            newShift.push({
              hourlyPrice: null,
              isRequired: true,
              jobStartTime: "",
              jobEndTime: "",
              price: null,
              shiftType: 2,
            });
          }
          newShiftEntries.push({
            dayOfWeek: dow.dayOfWeek,
            isRequired: dow.isRequired,
            hourlyPrice: null,
            jobStartTime: "",
            jobEndTime: "",
            price: null,
            shifts: newShift,
          });
        }
      });
    setShiftEntries(newShiftEntries);
  }, [dows, currentPayload]);

  // Add this new useEffect to sync shiftEntries with payload changes
  useEffect(() => {
    if (currentPayload?.weeklySchedule?.weeklyScheduleEntries) {
      setShiftEntries(currentPayload.weeklySchedule.weeklyScheduleEntries);
    }
  }, [currentPayload?.weeklySchedule?.weeklyScheduleEntries]);

  return { shiftEntries, updateEntries };
}
interface DropdownProps {
  options: string[];
  selectedOption: string;
  onSelect: (option: string) => void;
}

export const CustomDropdown: React.FC<DropdownProps> = ({ options, selectedOption, onSelect }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dropdownPosition, setDropdownPosition] = useState<"top" | "bottom">("bottom");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLUListElement>(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleOptionClick = (option: string) => {
    onSelect(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && dropdownListRef.current && dropdownRef.current) {
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const dropdownListHeight = dropdownListRef.current.offsetHeight;
      const spaceBelow = window.innerHeight - dropdownRect.bottom;
      const spaceAbove = dropdownRect.top;

      if (spaceBelow < dropdownListHeight && spaceAbove > dropdownListHeight) {
        setDropdownPosition("top");
      } else {
        setDropdownPosition("bottom");
      }
    }
  }, [isOpen]);

  return (
    <div
      ref={dropdownRef}
      style={{
        position: "relative",
        width: "min-content",
        height: "56px",
        textWrap: "nowrap",
        cursor: "pointer",
      }}
    >
      <div
        onClick={toggleDropdown}
        className="flex gap-2 w-full justify-content-center align-items-center h-full px-3"
        style={{
          border: options.includes(selectedOption) ? "3px solid #179D52" : "1px solid #DFDFDF",
          borderRadius: "10px",
        }}
      >
        <img src={options.includes(selectedOption) ? ClockGreenImg : ClockStartImg} alt="Clock" height="18px" width="18px" />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: options.includes(selectedOption) ? "700" : "400",
            fontSize: "16px",
            color: options.includes(selectedOption) ? "#179D52" : "#585858",
          }}
        >
          {selectedOption !== "undefined" ? selectedOption : "0"}
        </p>
        <IoChevronDown color={options.includes(selectedOption) ? "#179D52" : "#585858"} />
      </div>
      {isOpen && (
        <ul
          ref={dropdownListRef}
          style={{
            position: "absolute",
            top: dropdownPosition === "bottom" ? "101%" : undefined,
            bottom: dropdownPosition === "top" ? "102%" : undefined,
            left: 0,
            width: "100%",
            border: "1px solid #DFDFDF",
            backgroundColor: "#fff",
            listStyle: "none",
            margin: 0,
            padding: 0,
            zIndex: 1000,
            maxHeight: "150px",
            overflow: "auto",
            borderRadius: "10px",
          }}
        >
          {options.map((option) => (
            <li
              key={option}
              className="flex justify-content-center align-items-center"
              onClick={() => handleOptionClick(option)}
              style={{
                padding: "8px",
                cursor: "pointer",
                backgroundColor: "#fff",
              }}
              onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#f0f0f0")}
              onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#fff")}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
export const CustomDropdownRoaster: React.FC<DropdownProps> = ({ options, selectedOption, onSelect }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dropdownPosition, setDropdownPosition] = useState<"top" | "bottom">("bottom");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLUListElement>(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleOptionClick = (option: string) => {
    onSelect(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && dropdownListRef.current && dropdownRef.current) {
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const dropdownListHeight = dropdownListRef.current.offsetHeight;
      const spaceBelow = window.innerHeight - dropdownRect.bottom;
      const spaceAbove = dropdownRect.top;

      if (spaceBelow < dropdownListHeight && spaceAbove > dropdownListHeight) {
        setDropdownPosition("top");
      } else {
        setDropdownPosition("bottom");
      }
    }
  }, [isOpen]);

  return (
    <div
      ref={dropdownRef}
      style={{
        position: "relative",
        width: "min-content",
        // marginLeft: "45px",
        textWrap: "nowrap",
        cursor: "pointer",
      }}
    >
      <div
        onClick={toggleDropdown}
        className="flex gap-2 w-full justify-content-center align-items-center px-3"
        style={{
          border: options.includes(selectedOption) ? "2px solid #179D52" : "1px solid #DFDFDF",
          borderRadius: "15px",
          height: "35px",
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            fontWeight: options.includes(selectedOption) ? "700" : "400",
            fontSize: "16px",
            color: options.includes(selectedOption) ? "#179D52" : "#585858",
          }}
        >
          {selectedOption !== "undefined" ? selectedOption : "0"}
        </p>
        <IoChevronDown color={options.includes(selectedOption) ? "#179D52" : "#585858"} />
      </div>
      {isOpen && (
        <ul
          ref={dropdownListRef}
          style={{
            position: "absolute",
            top: dropdownPosition === "bottom" ? "101%" : undefined,
            bottom: dropdownPosition === "top" ? "102%" : undefined,
            left: 0,
            width: "100%",
            border: "1px solid #DFDFDF",
            backgroundColor: "#fff",
            listStyle: "none",
            margin: 0,
            padding: 0,
            zIndex: 1000,
            maxHeight: "150px",
            overflow: "auto",
            borderRadius: "10px",
          }}
        >
          {options.map((option) => (
            <li
              key={option}
              className="flex justify-content-center align-items-center"
              onClick={() => handleOptionClick(option)}
              style={{
                padding: "8px",
                cursor: "pointer",
                backgroundColor: "#fff",
              }}
              onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#f0f0f0")}
              onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#fff")}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
export const CustomDropdownRoasterMobile: React.FC<DropdownProps> = ({ options, selectedOption, onSelect }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dropdownPosition, setDropdownPosition] = useState<"top" | "bottom">("bottom");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLUListElement>(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleOptionClick = (option: string) => {
    onSelect(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && dropdownListRef.current && dropdownRef.current) {
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const dropdownListHeight = dropdownListRef.current.offsetHeight;
      const spaceBelow = window.innerHeight - dropdownRect.bottom;
      const spaceAbove = dropdownRect.top;

      if (spaceBelow < dropdownListHeight && spaceAbove > dropdownListHeight) {
        setDropdownPosition("top");
      } else {
        setDropdownPosition("bottom");
      }
    }
  }, [isOpen]);

  return (
    <div
      ref={dropdownRef}
      style={{
        position: "relative",
        width: "min-content",
        // marginLeft: "45px",
        textWrap: "nowrap",
        cursor: "pointer",
      }}
    >
      <div
        onClick={toggleDropdown}
        className="flex gap-2  justify-content-center align-items-center px-3"
        style={{
          border: options.includes(selectedOption) ? "2px solid #179D52" : "1px solid #DFDFDF",
          borderRadius: "10px",
          height: "46px",
          width: "340px",
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            fontWeight: options.includes(selectedOption) ? "700" : "400",
            fontSize: "16px",
            color: options.includes(selectedOption) ? "#179D52" : "#585858",
          }}
        >
          {selectedOption !== "undefined" ? selectedOption : "0"}
        </p>
        <IoChevronDown color={options.includes(selectedOption) ? "#179D52" : "#585858"} />
      </div>
      {isOpen && (
        <ul
          ref={dropdownListRef}
          style={{
            position: "absolute",
            top: dropdownPosition === "bottom" ? "101%" : undefined,
            bottom: dropdownPosition === "top" ? "102%" : undefined,
            left: 0,
            width: "100%",
            border: "1px solid #DFDFDF",
            backgroundColor: "#fff",
            listStyle: "none",
            margin: 0,
            padding: 0,
            zIndex: 1000,
            maxHeight: "150px",
            overflow: "auto",
            borderRadius: "10px",
          }}
        >
          {options.map((option) => (
            <li
              key={option}
              className="flex justify-content-center align-items-center"
              onClick={() => handleOptionClick(option)}
              style={{
                padding: "8px",
                cursor: "pointer",
                backgroundColor: "#fff",
              }}
              onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#f0f0f0")}
              onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#fff")}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
const ShortDaysContainer: React.FC<{
  dow: DaysOfWeek;
  onClick: (dayNumber: number) => void;
}> = ({ dow, onClick }) => {
  const [hovered, setHovered] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const daysStyles: CSSProperties = {
    border: "1px solid #FF6359",
  };

  const days = ["Sunday's", "Monday's", "Tuesday's", "Wednesday's", "Thursday's", "Friday's", "Saturday's"];

  return (
    <>
      <Dialog
        visible={visible}
        onHide={() => setVisible(false)}
        style={{
          width: "100vw",
          height: "100vh",
          maxWidth: "none",
          maxHeight: "none",
        }}
        content={
          <div
            className="h-min flex flex-column py-3 px-4 m-auto"
            style={{
              maxWidth: "610px",
              width: "90%",
              backgroundColor: "#FFFFFF",
              borderRadius: "33px",
            }}
          >
            <div className="flex justify-content-between align-items-center">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "32px",
                  color: "#585858",
                }}
              >
                Remove {shortDays[dow.dayOfWeek]}?
              </h1>
              <div className="flex gap-2 align-items-center">
                <button
                  className="cursor-pointer"
                  style={{
                    border: "none",
                    backgroundColor: "#FFFFFF",
                    color: "#585858",
                    fontWeight: "300",
                    fontSize: "16px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setVisible(false);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="cursor-pointer flex gap-2"
                  style={{
                    border: "1px solid #FF6359",
                    borderRadius: "10px",
                    backgroundColor: "rgba(255, 99, 89, 0.3)",
                    color: "#FF6359",
                    fontWeight: "500",
                    fontSize: "12px",
                    padding: "8px 14px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setVisible(false);
                    onClick(dow.dayOfWeek);
                  }}
                >
                  <img src={RemoveImg} alt="remove" width="13.33px" height="15px" />
                  Remove
                </button>
              </div>
            </div>
            <Divider className="my-2" />
            <p
              className="m-0 p-0 py-3"
              style={{
                fontWeight: "500",
                fontSize: "20px",
                color: "#585858",
              }}
            >
              Are you sure you want to remove {days[dow.dayOfWeek]} shift?
            </p>
          </div>
        }
      />
      <div
        className="py-2 px-4 cursor-pointer flex justify-content-center align-items-center"
        style={{
          border: dow.isRequired ? "3px solid #179D52" : "1px solid #DFDFDF",
          borderRadius: "10px",
          width: "100px",
          height: "56px",
          ...(dow.isRequired && hovered ? daysStyles : {}),
        }}
        onClick={(e) => {
          e.preventDefault();
          if (dow.isRequired) {
            setVisible(true);
            return;
          }
          onClick(dow.dayOfWeek);
        }}
        onMouseEnter={(e) => {
          e.preventDefault();
          setHovered(true);
        }}
        onMouseLeave={(e) => {
          e.preventDefault();
          setHovered(false);
        }}
      >
        <p
          className="m-0 p-0"
          style={{
            fontWeight: dow.isRequired ? (hovered ? "400" : "700") : "500",
            fontSize: "16px",
            color: dow.isRequired ? (hovered ? "#FF6359" : "#179D52") : "#585858",
          }}
        >
          {shortDays[dow.dayOfWeek]}
        </p>
      </div>
    </>
  );
};

const DaysSelectionManager: React.FC<{
  dows: Array<DaysOfWeek>;
  onlyWeekDays: boolean;
  onClick: (dayNumber: number) => void;
}> = ({ dows, onlyWeekDays, onClick }) => {
  return (
    <div className="flex flex-column w-min mt-2">
      <div className="flex gap-2">
        {dows?.slice(0, onlyWeekDays ? 5 : 7).map((dow, i) => (
          <ShortDaysContainer key={i} dow={dow} onClick={onClick} />
        ))}
      </div>
      <Divider className="my-2" />
    </div>
  );
};

const TimePicker: React.FC<{
  shift: "1st" | "2nd";
  text: "Start Time" | "End Time";
  enabled: boolean;
  imgSrc: string;
  value: string;
  startFrom?: string;
  dayIndex: number;
  jobType?: number;
  onChange: (value: string) => void;
}> = ({ text, enabled, imgSrc, value, startFrom, onChange, dayIndex, shift, jobType }) => {
  const [containerReady, setContainerReady] = useState(false);
  const [time, setTime] = useState<Date>(convertTimeToDate(value, shift, text === "Start Time" ? "start" : "end", jobType));
  const ref = useRef<Calendar>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const contextSchedule = useContext(scheduleContext);

  useEffect(() => {
    if (startFrom && text !== "End Time") {
      setTime(convertTimeToDate(startFrom, shift, text === "Start Time" ? "start" : "end", jobType));
    }
  }, [startFrom]);

  useEffect(() => {
    if (containerRef.current) {
      setContainerReady(true);
    }
  }, [containerRef.current]);

  return (
    <div className="relative w-full flex flex-column" ref={containerRef}>
      <div
        className=" flex p-2 align-items-center"
        style={{
          border: contextSchedule.unFilledTime && !value && dayIndex == 0 ? "1px solid #FF6359" : "1px solid #DFDFDF",
          borderRadius: "10px",
          cursor: enabled ? "pointer" : "default",
        }}
        onClick={(e) => {
          e.preventDefault();
          if (!enabled) return;
          ref.current.show();
        }}
      >
        <img src={imgSrc} alt={text} width="13.5px" height="13.5px" />
        <p
          className="m-0 p-0 mx-auto px-2"
          style={{
            fontWeight: !!value ? "700" : "400",
            fontSize: "14px",
            color: enabled ? "#585858" : "#BBBBBB",
          }}
        >
          {!!value ? convertTo12HourFormat(value) : text}
        </p>
        <FaChevronDown color={enabled ? "#585858" : "#BBBBBB"} />
      </div>
      {containerReady && (
        <Calendar
          ref={ref}
          value={time}
          onChange={(v) => {
            setTime(v.value);
          }}
          stepMinute={15}
          appendTo={containerRef.current}
          timeOnly
          style={{
            display: "none",
          }}
          hourFormat="12"
          footerTemplate={() => (
            <div className="flex gap-2">
              <button
                className="flex-grow-1 cursor-pointer"
                style={{
                  backgroundColor: "#ffffff",
                  fontWeight: "500",
                  fontSize: "16px",
                  color: "#CDCDCD",
                  border: "1px solid #4E4E4E",
                  borderRadius: "12px",
                  height: "36px",
                  pointerEvents: enabled ? "auto" : "none",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  ref.current.hide();
                  if (contextSchedule.toggleUnFilledTime) {
                    contextSchedule.toggleUnFilledTime(false);
                  }
                }}
              >
                Cancel
              </button>
              <button
                className="flex-grow-1 cursor-pointer"
                style={{
                  backgroundColor: "#FFA500",
                  fontWeight: "500",
                  fontSize: "16px",
                  color: "#FFFFFF",
                  border: "none",
                  borderRadius: "12px",
                  height: "36px",
                  pointerEvents: enabled ? "auto" : "none",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  ref.current.hide();
                  onChange(formatDateToTimeString(time));
                }}
              >
                Save
              </button>
            </div>
          )}
        />
      )}
    </div>
  );
};

const ActiveShift: React.FC<{
  shift: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]["shifts"][0];
  index: number;
  enabled: boolean;
  secondShiftEnabled?: boolean;
  jobType: number;
  jobSubType: number;
  dayIndex: number;
  borderEnabled?: boolean;
  onTimeChange: (startTime: string, endTime: string) => void;
}> = ({ enabled, secondShiftEnabled, jobType, jobSubType, index, onTimeChange, shift, dayIndex, borderEnabled }) => {
  const [difference, setDifference] = useState<number>(0);
  const [startTime, setStartTime] = useState<string>(null);
  const [endTime, setEndTime] = useState<string>(null);
  const job = getJobName(jobType, jobSubType);

  useEffect(() => {
    setStartTime(shift.jobStartTime);
    setEndTime(shift.jobEndTime);
  }, [shift]);

  useEffect(() => {
    if (!!startTime && !!endTime) {
      setDifference(calculateTimeDifference(startTime, endTime));
      onTimeChange(startTime, endTime);
    }
  }, [startTime, endTime]);

  return (
    <div
      className="h-full flex-grow-1 flex-1 flex align-items-center justify-content-around"
      style={{
        ...(secondShiftEnabled ? (enabled ? styles.TB.active : styles.TB.disable) : enabled ? styles.RTB.active : styles.RTB.disable),
        // borderRight:shiftEntry && shiftEntry.shifts.length !== 0 && shiftEntry.shifts.find(s => s.shiftType === 1) ? "" :"2px solid #179D52" ,
        borderLeft: index === 1 && borderEnabled ? "2px solid #179D52" : "",
      }}
    >
      <p
        className="m-0 p-0 ml-3"
        style={{
          fontWeight: "600",
          fontSize: "14px",
          color: enabled ? "#585858" : "#BBBBBB",
        }}
      >
        {job.shift[index]}
      </p>
      <div className="flex flex-column gap-2">
        <TimePicker
          text="Start Time"
          enabled={enabled}
          imgSrc={ClockStartImg}
          value={startTime}
          dayIndex={dayIndex}
          onChange={(v) => setStartTime(v)}
          shift={index === 0 ? "1st" : "2nd"}
          jobType={jobType}
        />
        <TimePicker
          text="End Time"
          enabled={enabled}
          imgSrc={ClockEndImg}
          value={endTime}
          startFrom={startTime}
          dayIndex={dayIndex}
          onChange={(v) => setEndTime(v)}
          shift={index === 0 ? "1st" : "2nd"}
          jobType={jobType}
        />
        {isNextDay(startTime, endTime) && (
          <p
            className="m-0 p-0 text-center"
            style={{
              fontSize: "12px",
            }}
          >
            on the following day
          </p>
        )}
      </div>
      <div className="flex flex-column align-items-center">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "600",
            fontSize: "30px",
            color: enabled ? "#585858" : "#DFDFDF",
          }}
        >
          {difference}
        </p>
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "12px",
            color: enabled ? "#585858" : "#DFDFDF",
          }}
        >
          Total Hours
        </p>
      </div>
    </div>
  );
};
const NoneShift: React.FC<{
  tutoringJob: boolean;
  enabled: boolean;
  textShow?: String;
  onAddShiftClicked: () => void;
}> = ({ enabled, tutoringJob, onAddShiftClicked, textShow }) => {
  return (
    <div
      className="flex-grow-1 flex-1 ml-2 flex align-items-center"
      style={{
        border: tutoringJob ? "2px solid #DFDFDF" : "",
        borderRadius: "10px",
        justifyContent: tutoringJob ? "start" : "center",
      }}
    >
      <div
        className={`flex gap-2 align-items-center px-3 py-2 ${enabled && "cursor-pointer"}`}
        style={{
          border: tutoringJob ? "1px solid #DFDFDF" : "",
          boxShadow: tutoringJob ? "" : "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
          borderRadius: "10px",
        }}
        onClick={(e) => {
          e.preventDefault();
          if (enabled) {
            onAddShiftClicked();
          }
        }}
      >
        <img src={AddShiftImg} alt="add shift img" width="12px" height="13.5px" />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "500",
            fontSize: "14px",
            color: enabled ? "#585858" : "#DFDFDF",
          }}
        >
          {textShow}
        </p>
      </div>
    </div>
  );
};

const DayContainer: React.FC<{
  dow: DaysOfWeek;
  shiftEntry: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0];
  enabled: boolean;
  jobType: number;
  jobSubType: number;
  dayIndex: number;
  onEntryChange: (entry: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]) => void;
  onAddShiftClicked: (dayNumber: number, addIndex: number) => void;
}> = ({ dow, enabled, shiftEntry, jobType, jobSubType, dayIndex, onEntryChange, onAddShiftClicked }) => {
  const [startTime1, setStartTime1] = useState<string>(null);
  const [endTime1, setEndTime1] = useState<string>(null);
  const [startTime2, setStartTime2] = useState<string>(null);
  const [endTime2, setEndTime2] = useState<string>(null);

  const contextSchedule = useContext(scheduleContext);

  return (
    <div
      className={`flex flex-column h-min w-full ${!enabled ? "cursor-pointer" : ""}`}
      onClick={(e) => {
        e.preventDefault();
        if (!enabled && contextSchedule.toggleUnFilledTime) {
          contextSchedule.toggleUnFilledTime(true);
        }
      }}
    >
      <div
        className="flex flex-grow-1"
        style={{
          height: "130px",
        }}
      >
        <div
          className="flex-grow-1 flex align-items-center"
          style={{
            maxWidth: "120px",
            ...(enabled ? styles.LTB.active : styles.LTB.disable),
            borderRight: shiftEntry && shiftEntry.shifts.length !== 0 && shiftEntry.shifts.find((s) => s.shiftType === 1) ? "" : "2px solid #179D52",
          }}
        >
          <p
            className="m-0 p-0 mx-auto"
            style={{
              fontWeight: "700",
              fontSize: "30px",
            }}
          >
            {shortDays[dow.dayOfWeek]}
          </p>
          <div
            style={{
              width: "1px",
              height: "64px",
              backgroundColor: "#DFDFDF",
            }}
          />
        </div>
        {shiftEntry && shiftEntry.shifts.length !== 0 && shiftEntry.shifts.find((s) => s.shiftType === 1) ? (
          <ActiveShift
            shift={shiftEntry.shifts.find((s) => s.shiftType === 1)}
            index={0}
            enabled={enabled}
            secondShiftEnabled={shiftEntry.shifts.length > 1}
            jobType={jobType}
            jobSubType={jobSubType}
            dayIndex={dayIndex}
            onTimeChange={(s, e) => {
              setStartTime1(s);
              setEndTime1(e);

              const updatedShifts = shiftEntry.shifts.map((shift) => (shift.shiftType === 1 ? { ...shift, jobStartTime: s, jobEndTime: e } : shift));

              const updatedEntry = {
                ...shiftEntry,
                jobStartTime: s ?? startTime2,
                jobEndTime: e ?? endTime2,
                shifts: updatedShifts,
              };

              onEntryChange(updatedEntry);
            }}
            borderEnabled={shiftEntry?.shifts.some((s) => s.shiftType !== 2)}
          />
        ) : (
          <NoneShift
            tutoringJob={isTutoringJob(jobSubType)}
            enabled={[4, 8].includes(jobType) ? false : enabled}
            onAddShiftClicked={() => onAddShiftClicked(dow.dayOfWeek, 0)}
            textShow="Add a first shift"
          />
        )}
        {shiftEntry && shiftEntry.shifts.length !== 0 && shiftEntry.shifts.find((s) => s.shiftType === 2) ? (
          <ActiveShift
            shift={shiftEntry.shifts.find((s) => s.shiftType === 2)}
            index={1}
            enabled={enabled}
            jobType={jobType}
            jobSubType={jobSubType}
            dayIndex={dayIndex}
            onTimeChange={(s, e) => {
              setStartTime2(s);
              setEndTime2(e);

              const updatedShifts = shiftEntry.shifts.map((shift) => (shift.shiftType === 2 ? { ...shift, jobStartTime: s, jobEndTime: e } : shift));

              const updatedEntry = {
                ...shiftEntry,
                jobStartTime: startTime1 ?? s,
                jobEndTime: endTime1 ?? e,
                shifts: updatedShifts,
              };

              onEntryChange(updatedEntry);
            }}
            borderEnabled={!shiftEntry?.shifts.find((s) => s.shiftType == 1)}
          />
        ) : (
          <NoneShift
            tutoringJob={isTutoringJob(jobSubType)}
            enabled={[4, 8].includes(jobType) ? false : enabled}
            onAddShiftClicked={() => onAddShiftClicked(dow.dayOfWeek, 1)}
            textShow="Add a second shift"
          />
        )}
      </div>
    </div>
  );
};

const SchedulePopUp: React.FC<{
  visible: boolean;
  defautprice?: number;
  setState: (state: boolean) => void;
  dows: DaysOfWeek[];
  shiftEntries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"];
  jobType: number;
  jobSubType: number;
  onEntriesChange: (entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]) => void;
  setDows: React.Dispatch<React.SetStateAction<DaysOfWeek[]>>; // Explicitly type setDows
}> = ({ visible, setState, dows, shiftEntries, jobType, jobSubType, onEntriesChange, setDows, defautprice }) => {
  const [allDaysEnabled, setAllDaysEnabled] = useState<boolean>(false);

  useEffect(() => {}, [shiftEntries]);

  function fillAllShifts() {
    const firstShift = shiftEntries[0];
    onEntriesChange(
      shiftEntries.map((se) => ({
        ...firstShift,
        dayOfWeek: se.dayOfWeek,
        hourlyPrice: se.hourlyPrice,
        price: se.price,
        shifts: firstShift.shifts.map((s, i) => ({
          ...s,
          hourlyPrice: se.shifts[i]?.hourlyPrice || null,
          price: se.shifts[i]?.price || null,
        })),
      }))
    );
  }

  function handleRemoveShift(dayNumber: number, shiftIndex: number) {
    const shiftTypeToRemove = shiftIndex + 1;

    // Update shift entries
    const updatedEntries = shiftEntries.map((prev) =>
      prev.dayOfWeek === dayNumber
        ? {
            ...prev,
            shifts: prev.shifts.filter((s) => s.shiftType !== shiftTypeToRemove),
          }
        : prev
    );

    // Check if the day has no shifts left
    const dayEntry = updatedEntries.find((entry) => entry.dayOfWeek === dayNumber);
    if (dayEntry && dayEntry.shifts.length === 0) {
      // Update daysOfWeek to mark the day as not required
      setDows((prevDows: DaysOfWeek[]) => prevDows.map((dow) => (dow.dayOfWeek === dayNumber ? { ...dow, isRequired: false } : dow)));
    }

    // Update shift entries
    onEntriesChange(updatedEntries);
  }
  function handleAddShift(dayNumber: number, addIndex: number) {
    const newShift = {
      hourlyPrice: defautprice?.toString() || null,
      isRequired: true,
      jobStartTime: "",
      jobEndTime: "",
      price: null,
      shiftType: addIndex + 1,
    };

    onEntriesChange(
      shiftEntries.map((prev) =>
        prev.dayOfWeek === dayNumber
          ? {
              ...prev,
              shifts: [...prev.shifts.slice(0, addIndex), newShift, ...prev.shifts.slice(addIndex)],
            }
          : prev
      )
    );
  }

  useEffect(() => {
    setAllDaysEnabled(isShiftFilled(shiftEntries, true));
  }, [dows, visible]);
  return (
    <Dialog
      visible={visible}
      onHide={() => setState(false)}
      style={{
        width: "100vw",
        height: "100vh",
        maxWidth: "none",
        maxHeight: "none",
      }}
      content={
        <div
          className="m-auto flex flex-column overflow-hidden"
          style={{
            maxWidth: "948px",
            maxHeight: "893px",
            width: "90%",
            height: "90%",
            backgroundColor: "#FFFFFF",
            borderRadius: "25px",
          }}
        >
          <div
            className="w-full flex align-items-center px-5"
            style={{
              minHeight: "89px",
              backgroundColor: "#179D52",
              boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
            }}
          >
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "30px",
                color: "#FFFFFF",
              }}
            >
              Complete your weekly schedule
            </h1>
          </div>
          <div className="flex-grow-1 flex flex-column overflow-auto px-5 gap-3 pt-5 pb-8">
            {dows
              ?.filter((dow) => dow.isRequired === true)
              .map((dow, i) => (
                <React.Fragment key={i}>
                  <DayContainer
                    dow={dow}
                    shiftEntry={shiftEntries.find((se) => se.dayOfWeek === dow.dayOfWeek)}
                    enabled={i === 0 || allDaysEnabled}
                    jobType={jobType}
                    jobSubType={jobSubType}
                    dayIndex={i}
                    onEntryChange={(e) => {
                      onEntriesChange(shiftEntries.map((p) => (p.dayOfWeek === e.dayOfWeek ? { ...e } : p)));
                    }}
                    onAddShiftClicked={handleAddShift}
                  />
                  {(!isShiftFilled(shiftEntries) || allDaysEnabled) && shiftEntries && shiftEntries.find((se) => se.dayOfWeek === dow.dayOfWeek) && (
                    <div className="flex align-items-center">
                      {shiftEntries?.length > 0 &&
                        shiftEntries.some((s) => s.shifts.some((ss) => ss.shiftType === 1) && s.dayOfWeek == dow.dayOfWeek) && (
                          <div
                            style={{ paddingLeft: "75px" }}
                            className="flex-grow-1 flex-1 h-min flex justify-content-center align-items-center gap-2 cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              handleRemoveShift(dow.dayOfWeek, 0);
                            }}
                          >
                            <img src={RemoveMinusImg} alt="Remove Minus" width="13.5px" height="13.5px" />
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "600",
                                fontSize: "12px",
                                color: "#FF6359",
                              }}
                            >
                              Remove shift
                            </p>
                          </div>
                        )}
                      <div className="flex-grow-1 flex-1 h-min" />
                      {shiftEntries?.length > 0 &&
                        shiftEntries.some((s) => s.shifts.some((ss) => ss.shiftType === 2) && s.dayOfWeek == dow.dayOfWeek) && (
                          <div
                            style={{ paddingRight: "35px" }}
                            className="flex-grow-1 flex-1 h-min flex justify-content-center align-items-center gap-2 cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              handleRemoveShift(dow.dayOfWeek, 1);
                            }}
                          >
                            <img src={RemoveMinusImg} alt="Remove Minus" width="13.5px" height="13.5px" />
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "600",
                                fontSize: "12px",
                                color: "#FF6359",
                              }}
                            >
                              Remove shift
                            </p>
                          </div>
                        )}
                    </div>
                  )}
                  {!allDaysEnabled && i === 0 && isShiftFilled(shiftEntries) && (
                    <div className="h-min w-full flex gap-3">
                      <button
                        className="flex-1 cursor-pointer"
                        style={{
                          height: "46px",
                          border: "1px solid #585858",
                          borderRadius: "5px",
                          backgroundColor: "#FFFFFF",
                          fontWeight: "700",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          setAllDaysEnabled(true);
                        }}
                      >
                        Complete day-by-day
                      </button>
                      <button
                        className="flex-1 cursor-pointer"
                        style={{
                          height: "46px",
                          border: "1px solid #FFA500",
                          borderRadius: "5px",
                          backgroundColor: "#FFA500",
                          fontWeight: "700",
                          fontSize: "16px",
                          color: "#FFFFFF",
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          setAllDaysEnabled(true);
                          fillAllShifts();
                        }}
                      >
                        Auto-fill weekly schedule
                      </button>
                    </div>
                  )}
                </React.Fragment>
              ))}
          </div>
          <div className="flex flex-column px-5 gap-3 mb-3">
            <Divider />
            <div className="flex-grow-1 flex justify-content-between">
              <GoBack
                onClick={(e) => {
                  e.preventDefault();
                  setState(false);
                }}
              />
              <Next
                disabled={!isShiftFilled(shiftEntries, true)}
                onClick={(e) => {
                  e.preventDefault();
                  setState(false);
                }}
              />
            </div>
          </div>
        </div>
      }
    />
  );
};

const DateSelectionManager: React.FC<{
  date: Date | null;
  onChange: (date: Date) => void;
}> = ({ date, onChange }) => {
  const [selectedDate, setSelectedDate] = useState<Date>(date);
  const [commencingDate, setCommencingDate] = useState<Date>(!!date ? getMondayDate(date) : null);
  const [visible, setVisible] = useState<boolean>(false);

  function formatDate(date: Date): string {
    const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const monthsOfYear = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

    const dayOfWeek = daysOfWeek[date.getDay()];
    const dayOfMonth = date.getDate();
    const month = monthsOfYear[date.getMonth()];
    const year = date.getFullYear();

    return `${dayOfWeek}, ${dayOfMonth} of ${month}, ${year}`;
  }
  return (
    <>
      <Dialog
        visible={visible}
        onHide={() => setVisible(false)}
        style={{
          width: "100vw",
          height: "100vh",
          maxWidth: "none",
          maxHeight: "none",
        }}
        content={
          <div className="m-auto flex flex-column gap-3">
            <div
              className="relative"
              style={{
                textWrap: "nowrap",
              }}
            >
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "30px",
                  color: "#FFFFFF",
                }}
              >
                When do you want your weekly schedule to start?
              </h1>
              <div
                className="absolute flex justify-content-center align-items-center cursor-pointer"
                style={{
                  top: "-40px",
                  right: "-40px",
                  width: "30px",
                  height: "32px",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  setVisible(false);
                }}
              >
                <IoMdClose />
              </div>
            </div>
            <Calendar
              className="h-min mx-auto"
              panelClassName="w-full"
              panelStyle={{
                width: "465.972px",
                borderRadius: "16px",
              }}
              style={{ width: "465.972px", borderRadius: "16px" }}
              value={selectedDate}
              onChange={(e) => {
                setSelectedDate(e.value);
                onChange(e.value);
                setCommencingDate(getMondayDate(e.value));
                setVisible(false);
              }}
              minDate={new Date()}
              inline
            />
          </div>
        }
      />
      <div className="my-2 flex flex-column">
        <div
          className="flex justify-content-center align-items-center gap-2 cursor-pointer"
          style={{
            border: !!commencingDate ? "3px solid #179D52" : "3px solid #FFA500",
            height: "46px",
            maxWidth: "700px",
            backgroundColor: !!commencingDate ? "#FFFFFF" : "#FFA500",
            borderRadius: "10px",
            boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
          }}
          onClick={(e) => {
            e.preventDefault();
            setVisible(true);
          }}
        >
          <img src={!!commencingDate ? CalenderGreenImg : CalenderImg} alt="Calender" width="18px" height="18px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: !!commencingDate ? "700" : "600",
              fontSize: "16px",
              color: !!commencingDate ? "#179D52" : "#FFFFFF",
            }}
          >
            {!!commencingDate ? `Week commencing from ${formatDate(commencingDate)}` : "Select Start Date"}
          </p>
        </div>
      </div>
    </>
  );
};

interface PostJobPropss {
  payload: Partial<PayloadTemplate>;
  next: (to: JobManagementScreens, replace?: boolean) => void;
  prev: (to: JobManagementScreens, replace?: boolean) => void;
  setpayload: (payload: Partial<PayloadTemplate>) => void;
}
const DaysAndSchedule: React.FC<PostJobPropss> = ({ next, payload, prev, setpayload }) => {
  const [hasUnfilledTimes, setHasUnfilledTimes] = useState<boolean>(false);
  const [dows, setDows] = useState<Array<DaysOfWeek>>(payload.daysOfWeek);
  // const [completionMethod, setCompletionMethod] = useState<number | null>(
  //   payload.jobRosterType !== undefined ? payload.jobRosterType : 0
  // );
  // const [showCompletionDropdown, setShowCompletionDropdown] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState<string>(payload.duration !== null ? String(payload.duration) : "0");
  const [selectedDate, setSelectedDate] = useState<Date>(
    (() => {
      if (payload.jobDate === null) {
        return null;
      } else {
        if (isDateLessThanToday(convertDate(payload.jobDate) as Date)) {
          return null;
        } else {
          return convertDate(payload.jobDate) as Date;
        }
      }
    })()
  );
  const [dateFlexible, setDateFlexible] = useState<number>(payload.isJobFlexible !== undefined ? (payload.isJobFlexible ? 0 : 1) : -1);

  const [popUpVisible, setPopUpVisible] = useState<{
    SchedulePopUp: boolean;
  }>({
    SchedulePopUp: false,
  });

  const { shiftEntries, updateEntries } = useShiftEntries(dows, payload);

  const { isMobile } = useIsMobile();

  function handleDaysClicked(dayNumber: number) {
    setDows((prev) => prev.map((dow) => (dow.dayOfWeek === dayNumber ? { ...dow, isRequired: !dow.isRequired } : dow)));
  }

  function handleInputShiftState(): boolean {
    return dows?.some((dow) => dow.isRequired === true);
  }

  // if (isMobile) {
  //     return (
  //         <DaysAndScheduleMobile
  //             payload={payload}
  //             next={next}
  //             prev={prev}
  //             setpayload={setpayload}
  //         />
  //     );
  // }

  return (
    <scheduleContext.Provider
      value={{
        unFilledTime: hasUnfilledTimes,
        toggleUnFilledTime: (state) => {
          setHasUnfilledTimes(state);
        },
      }}
    >
      <SchedulePopUp
        visible={popUpVisible.SchedulePopUp}
        setState={(state) => {
          setPopUpVisible((prev) => ({
            ...prev,
            SchedulePopUp: state,
          }));
        }}
        dows={dows}
        shiftEntries={shiftEntries}
        jobType={payload.jobType}
        jobSubType={payload.jobSubType}
        onEntriesChange={(e) => {
          updateEntries(e);
        }}
        setDows={setDows} // Pass setDows
        defautprice={payload.price}
      />
      <div className="h-full  flex flex flex-column">
        <div className="flex-grow-1 w-full flex flex-column my-3 overflow-auto p-8" style={{}}>
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "24px",
              color: "#585858",
            }}
          >
            Select the days required
          </h1>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Candidates will respond with their availability for each of the days you select
          </p>
          <DaysSelectionManager dows={dows} onlyWeekDays={String(payload.jobType) === "12"} onClick={handleDaysClicked} />
          <div className="flex flex-column w-min">
            <button
              className="flex justify-content-center align-items-center gap-2"
              style={{
                width: "629px",
                height: "46px",
                border: isShiftFilled(shiftEntries, true) ? "3px solid #179D52" : handleInputShiftState() ? "3px solid #FFA500" : "3px solid #DFDFDF",
                borderRadius: "5px",
                fontWeight: "700",
                fontSize: "16px",
                color: isShiftFilled(shiftEntries, true) ? "#179D52" : "#FFFFFF",
                backgroundColor: isShiftFilled(shiftEntries, true) ? "#FFFFFF" : handleInputShiftState() ? "#FFA500" : "#DFDFDF",
                cursor: handleInputShiftState() ? "pointer" : "not-allowed",
              }}
              onClick={(e) => {
                e.preventDefault();
                if (handleInputShiftState()) {
                  setPopUpVisible((prev) => ({
                    ...prev,
                    SchedulePopUp: true,
                  }));
                }
              }}
            >
              {isShiftFilled(shiftEntries, true) && <img src={PencilImg} alt="Pencil" width="15.1px" height="15.1px" />}
              {isShiftFilled(shiftEntries, true) ? "Edit Shifts" : "Input Shifts"}
            </button>
            {isShiftFilled(shiftEntries, true) && <Divider className="my-3" />}
          </div>
          {isShiftFilled(shiftEntries, true) && (
            <div className="flex flex-column">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                How many weeks is this job expected to run for?
              </h1>
              <div className="flex gap-2 my-2 align-items-center">
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "400",
                    fontSize: "16px",
                    color: "#585858",
                  }}
                >
                  Expected Job Duration
                </p>
                <CustomDropdown
                  options={Array.from({ length: 52 }, (_, i) => String(i + 1))}
                  selectedOption={selectedDuration}
                  onSelect={(e) => setSelectedDuration(e)}
                />
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "400",
                    fontSize: "16px",
                    color: "#585858",
                  }}
                >
                  Weeks
                </p>
                {/* {completionMethod !== 0 && (
                  <div className="flex gap-2 my-2 align-items-center ml-5">
                    <CustomDropdownRoaster
                      options={["Auto-fill Weeks", "Week-by-week"]}
                      selectedOption={completionMethod === 1 ? "Auto-fill Weeks" : "Week-by-week"}
                      onSelect={(option) => {
                        setCompletionMethod(option === "Auto-fill Weeks" ? 1 : 2);
                      }}
                    />
                  </div>
                )} */}
              </div>

              <div
                className="my-2"
                style={{
                  width: "100%",
                  height: "1px",
                  maxWidth: "630px",
                  backgroundColor: "#e5e7eb",
                }}
              />
            </div>
          )}
          {/* {selectedDuration !== "0" && isShiftFilled(shiftEntries, true) && (
            <div className="flex flex-column mt-2">
              {completionMethod === 0 && (
                <h1 className="m-0 p-0" style={{ fontWeight: "700", fontSize: "24px", color: "#585858" }}>
                  How would you like to complete your roster?
                </h1>
              )}

              {completionMethod === 0 && (
                <div className="flex gap-3 my-2">
                  <button
                    className="cursor-pointer"
                    style={{
                      height: "46px",
                      border: "1px solid #585858",
                      borderRadius: "5px",
                      backgroundColor: "#FFFFFF",
                      fontWeight: "700",
                      fontSize: "16px",
                      color: "#585858",
                      width: "310px",
                    }}
                    onClick={() => setCompletionMethod(1)}
                  >
                    Auto-fill
                  </button>
                  <button
                    className="cursor-pointer"
                    style={{
                      height: "46px",
                      border: "none",
                      borderRadius: "5px",
                      backgroundColor: "#FFA500",
                      fontWeight: "700",
                      fontSize: "16px",
                      color: "#FFFFFF",
                      width: "310px"
                    }}
                    onClick={() => setCompletionMethod(2)}
                  >
                    Week-by-week
                  </button>
                </div>
              )}
            </div>
          )} */}
          {selectedDuration !== "0" && isShiftFilled(shiftEntries, true) && (
            <div
              className="flex flex-column mt-2"
              style={{
                textWrap: "nowrap",
              }}
            >
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                When do you want your weekly schedule to start?
              </h1>
              <DateSelectionManager date={selectedDate} onChange={(d) => setSelectedDate(d)} />
              <div
                className="my-3"
                style={{
                  width: "700px",
                  height: "1px",
                  maxWidth: "630px",
                  backgroundColor: "#e5e7eb",
                }}
              />
            </div>
          )}
          {!!selectedDate && isShiftFilled(shiftEntries, true) && selectedDuration !== "0" && (
            <div
              className="flex my-3 align-items-center justify-content-between"
              style={{
                maxWidth: "600px",
              }}
            >
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                Is this start date flexible?
              </h1>
              <div className="flex gap-3">
                {["Yes", "No"].map((v, i) => (
                  <div
                    key={i}
                    className="flex gap-2 justify-content-center align-items-center cursor-pointer"
                    style={{
                      border: dateFlexible === i ? "3px solid #179D52" : "1px solid #DFDFDF",
                      borderRadius: "10px",
                      width: "91px",
                      height: "52px",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      setDateFlexible(i);
                    }}
                  >
                    <RadioButton selected={dateFlexible === i} />
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: dateFlexible === i ? "700" : "500",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {v}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        <Divider className="w-full" />
        <div className="h-min flex justify-content-between my-3 pl-6" style={{ width: "80%" }}>
          <GoBack
            onClick={(e) => {
              e.preventDefault();

              setpayload({
                ...payload,
                jobDate: convertDate(selectedDate) as string,
                daysOfWeek: dows,
                duration: selectedDuration,
                // jobRosterType: completionMethod,
                durationType: 1,
                weeklySchedule: {
                  ...payload.weeklySchedule,
                  weeklyScheduleEntries: shiftEntries,
                },
              });
              if ([2, 4, 8, 12].includes(payload.jobType)) {
                prev("job-posting"); // Navigate to "day-and-schedule" if condition is true
              } else {
                prev("tutoring-subjects");
              }
              // Replace "previous-step" with the actual previous step key
            }}
          />
          <Next
            disabled={
              !isShiftFilled(shiftEntries, true) ||
              !selectedDate ||
              selectedDuration === "0" ||
              selectedDuration === "undefined" ||
              dateFlexible === -1
              // completionMethod === null
            }
            onClick={(e) => {
              e.preventDefault();

              setpayload({
                ...payload,
                jobDate: convertDate(selectedDate) as string,
                daysOfWeek: dows,
                duration: selectedDuration,
                // jobRosterType: completionMethod,
                isJobFlexible: dateFlexible === 0,
                durationType: 1,
                weeklySchedule: {
                  ...payload.weeklySchedule,
                  weeklyScheduleEntries: shiftEntries,
                },
              });

              //next("pricing-payments-step2"); // Replace "next-step" with the actual next step key
              if ([2, 4, 8, 12].includes(payload.jobType)) {
                next("jobpricing-step1");
              } else {
                next("pricing-payments-step1");
              }
            }}
          />
        </div>
      </div>
    </scheduleContext.Provider>
  );
};

export default DaysAndSchedule;
{
  /* <NoneShift
tutoringJob={isTutoringJob(jobSubType)}
enabled={[4, 8].includes(jobType) ? false : enabled}
onAddShiftClicked={() => onAddShiftClicked(dow.dayOfWeek)}
/> */
}
