import React from "react";
import styles from "../../styles/childcare-job-posted.module.css";
import juggleLogo from "../../../../assets/images/juggle_white.png";
import childcare from "../../../../assets/images/childcare-completed-logo.png";
import { Divider } from "primereact/divider";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import useIsMobile from "../../../../hooks/useIsMobile";
import { AppDispatch, RootState } from "../../../../store";
import { useHashQueryParam } from "../../../../hooks/useHashQueryParam";
import c from "../../../../helper/juggleStreetConstants";
import { IframeBridge } from "../../../../services/IframeBridge";
import { toggleSideBar } from "../../../../store/slices/applicationSlice";
import { refreshAccount } from "../../../../store/tunks/sessionInfoTunk";

export interface ChildCareJobPostedProps {
  manageBy: number;
  jobType: number;
}
const ChildcareJobPosted: React.FC<ChildCareJobPostedProps> = ({ manageBy, jobType }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const clientType = utils.getCookie(CookiesConstant.clientType);

  const { isMobile } = useIsMobile();
  const { sideBarIsOpened: isOpen, inIframe } = useSelector((state: RootState) => state.applicationState);
  const dispatch = useDispatch<AppDispatch>();
  const jobId = useHashQueryParam("jobId");

  const activeTab = searchParams.has("activeTab") && searchParams.get("activeTab") !== "undefined" ? Number(searchParams.get("activeTab")) : null;

  const show = Number(searchParams.get("show"));

  const renderHeader = () => (
    <header className={`w-full flex align-items-center justify-content-between border-round-bottom-lg overflow-hidden ${styles.headerGradient}`}>
      <img className={styles.juggleLogo} src={juggleLogo} alt="juggle logo" />
      <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
    </header>
  );
  return (
    <div>
      <div>
        {renderHeader()}
        <main className={styles.mainSection}>
          <div className={styles.leftSideSection}>
            <div className="text-center">
              <h2 className={styles.textStyle1}>Job Posted!</h2>
            </div>
            <div>
              <p className={styles.leftPara}>•This job can be active for up to {jobType === 1 || jobType === 256 ? "14" : "30"} days.</p>
              <Divider />
              <p className={styles.leftPara}>•During this time you will be able to chat online with invited candidates</p>
              <Divider />
              {manageBy == c.managedBy.USER ? (
                <p className={styles.leftPara}>
                  • If you don't find the right person quickly, you can change the job details and invite more candidates to apply.
                </p>
              ) : (
                <p className={styles.leftPara}>• If you don’t find the right person, you can edit the criteria to widen your search</p>
              )}
              <Divider />
              <p className={styles.leftPara}>•When a candidate applies to your job, they will be waiting to hear from you!</p>
              <Divider />
              <p className={styles.leftPara}>•Make sure to confirm your chosen candidates by clicking the AWARD button</p>
              <Divider />
            </div>
            <button
              className={styles.manageMentBtn}
              onClick={async (e) => {
                e.preventDefault();

                // IframeBridge.sendToParent({
                //   type: "viewJob",
                //   data: {
                //     id: String(jobId),
                //   },
                // });

                 IframeBridge.sendToParent({
      type: "goBack-postjob",
    });
                if (!inIframe) {
                  if (jobId !== null && !isNaN(Number(jobId)) && activeTab !== null && !isNaN(activeTab)) {
                    const newParams = new URLSearchParams();
                    newParams.set("jobId", String(jobId));
                    newParams.set("activeTab", String(activeTab));

                    navigate({
                      pathname: `/${Number(clientType) === 1 ? "parent-home" : "business-home"}/manage-jobs`,
                      search: newParams.toString(),
                    });
                    if (isMobile && isOpen) {
                      dispatch(toggleSideBar());
                    }
                    await dispatch(refreshAccount());
                    return;
                  }

                  navigate(`/${Number(clientType) === 1 ? "parent-home" : "business-home"}/manage-jobs`);
                  if (isMobile && isOpen) {
                    dispatch(toggleSideBar());
                  }
                  await dispatch(refreshAccount());
                }
              }}
            >
              Head to Job Management
            </button>
          </div>
          <div className={styles.rightSection}>
            <div className="text-center">
              <div>
                <img src={childcare} alt="childcare" width={636} height={621} />
              </div>
              {/* Add application information content here */}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ChildcareJobPosted;
