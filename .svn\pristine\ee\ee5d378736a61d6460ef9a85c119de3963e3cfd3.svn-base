.congratscontainer {
  width: 668px;
  position: relative; /* Ensure confetti is positioned correctly */
  overflow: hidden; /* Prevent confetti from overflowing */
  overflow-y: hidden;
  padding: 40px;
}

.headerContainer {
  width: 668px;
}

.congratsheader {
  display: flex;
  height: 50px;
  align-items: center;
}

.imgcofe {
}

.congratsheader img {
  max-width: 272px;
  width: 100%;
  height: auto;
}
.congotitle {
  font-size: 32px;
  font-weight: 700;
  color: #585858;
  margin: 20px;
}
.congratspara {
  max-width: 660px;
  width: 100%;
  height: 230px;
  margin: 0px;
  margin-left: 20px;
  font-size: 26px;
  color: #585858;
  font-weight: 400;
  line-height: 39px;
}
@media (max-width: 768px) {
  .congotitle {
    font-size: 24px;
    margin: 10px;
  }

  .congratspara {
    font-size: 20px;
    line-height: 30px;
    margin-left: 10px;
  }

  .congratsheader img {
    max-width: 180px; /* Reduce image size on smaller screens */
  }
}

@media (max-width: 480px) {
  .congotitle {
    font-size: 25px;
    margin: 8px;
  }
  .congratsheader img {
    max-width: 150px; /* Further reduce image size */
  }
}
.congralationButton{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  height: min-content;
  background-color: #fff;
  box-shadow: 0px 0px 8px 0px #00000040;
}
