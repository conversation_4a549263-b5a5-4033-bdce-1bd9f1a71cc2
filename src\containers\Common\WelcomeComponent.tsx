import React from 'react';
import { Dialog } from 'primereact/dialog';
import './styles/welcome-component.css';
import ArrowIcon from '../../assets/images/arrow.png';
import CustomButton from '../../commonComponents/CustomButton';
import CookiesConstant from '../../helper/cookiesConst';
import utils from '../../components/utils/util';

interface WelcomeComponentProps {
    profileCompletion: number;
    reverseWhen: number;
    userName: string;
    dialogBoxState: boolean;
    closeClicked: () => void;
    onExploreClicked: () => void;
}

const WelcomeComponent: React.FC<WelcomeComponentProps> = ({
    profileCompletion,
    reverseWhen,
    userName,
    dialogBoxState,
    closeClicked,
    onExploreClicked,
}) => {
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    return (
        <Dialog
            modal
            visible={dialogBoxState}
            onHide={() => { }}
            content={() => (
                <div className="custom-dialog">
                    <div className="custom-gradient-bg-1" />
                    <div className="custom-gradient-bg-2" />
                    <div className="custom-gradient-bg-3" />
                    <div className="custom-dialog-content">
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: '100%',
                                position: 'relative',
                                paddingInline: '20px',
                            }}
                        >
                            <img
                                src="https://cdn.builder.io/api/v1/image/assets/TEMP/4045842db830bca0fc6bf211bd30a87fc5460fa35bca4419649d77bcbcad3f01?placeholderIfAbsent=true&apiKey=9b028915150f4a5e85e469d00e64d9ec"
                                alt="Juggle Street Logo"
                                height={'89px'}
                                width={'150px'}
                            />
                            <div
                                style={{
                                    color: '#585858',
                                    cursor: 'pointer',
                                }}
                                onClick={closeClicked}
                            >
                                <i className="pi pi-times" />
                            </div>
                        </div>
                        <div
                            className="progress-container"
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '100%',
                                padding: '0',
                                margin: '0',
                            }}
                        >
                            <div
                                style={{
                                    width: '100%',
                                    height: '3px',
                                    backgroundColor: '#D9D9D9',
                                    position: 'relative',
                                }}
                            >
                                <div
                                    style={{
                                        width: `${profileCompletion}%`,
                                        height: '3px',
                                        backgroundColor: '#179D52',
                                        top: 0,
                                        left: 0,
                                    }}
                                />
                            </div>
                            <div
                                className="progress-label"
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    flexDirection: `${profileCompletion >= reverseWhen ? 'row-reverse' : 'row'
                                        }`,
                                    marginTop: '-5px',
                                    textWrap: 'nowrap',
                                    fontWeight: '700',
                                    color: '#179D52',
                                }}
                            >
                                <div
                                    style={{
                                        width: `${profileCompletion >= reverseWhen
                                            ? 100 - profileCompletion - 1.5
                                            : profileCompletion - 1.5
                                            }%`,
                                    }}
                                />
                                <img
                                    src={ArrowIcon}
                                    alt="attor"
                                    style={{
                                        marginRight: '5px',
                                        transform: `${profileCompletion >= reverseWhen
                                            ? 'rotateY(180deg)'
                                            : 'rotateY(0deg)'
                                            }`,
                                    }}
                                />
                                <p>Your profile is {profileCompletion}% complete</p>
                            </div>
                        </div>
                        <div
                            style={{
                                flexGrow: 1,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                textAlign: 'center',
                                flexDirection: 'column',
                                marginTop: '2em',
                            }}
                        >
                            <div
                                className="custom-welcom-text"
                                style={{
                                    display: 'flex',
                                    color: '#585858',
                                    gap: '10px',
                                    alignItems: 'center',
                                }}
                            >
                                <h1
                                    style={{
                                        fontWeight: '600',
                                        fontSize: '32px',
                                        color: '#179D52',
                                        margin: '0',
                                    }}
                                >
                                    Hi {userName},
                                </h1>
                                <span
                                    style={{
                                        fontWeight: '500',
                                        fontSize: '32px',
                                    }}
                                >
                                    welcome to Juggle Street
                                </span>
                            </div>
                            <p
                                style={{
                                    paddingInline: '10px',
                                    color: '#585858',
                                    fontWeight: '500',

                                }}
                            >
                                {/* Your profile is now {profileCompletion}% complete, Before you look
                                for childcare <br /> workers and post jobs, you need to
                                complete  <br /> your {clientType==2 ? "business profile." : "profile"}  */}
                                {clientType === 0 ? (
                                    <>
                                        Your profile is now {profileCompletion}% complete. <br />
                                        We just need a few more details before your profile will become active.
                                    </>
                                ) : clientType === 1 ? (
                                    <>
                                        Your profile is now {profileCompletion}% complete. <br />
                                        Before you can chat with helpers and post jobs, you need to <br />
                                        complete your profile.
                                    </>
                                ) : (
                                    <>
                                        Your profile is now {profileCompletion}% complete. Before you look
                                        for childcare <br /> workers and post jobs, you need to
                                        complete <br /> your profile.
                                    </>
                                )}
                            </p>
                            <CustomButton
                                className="hover:shadow-3"
                                label="Explore Juggle Street"
                                onClick={onExploreClicked}
                                style={{
                                    width: '306px',
                                    margin: '0',
                                    marginTop: '1em',
                                }}
                            />
                        </div>
                        <div style={{ height: '100px' }} />
                    </div>
                </div>
            )}
        />
    );
};

export default WelcomeComponent;
