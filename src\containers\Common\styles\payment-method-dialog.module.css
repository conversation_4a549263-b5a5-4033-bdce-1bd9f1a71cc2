.paymentDialog {
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin: 0 auto;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  background-color: #fff;
      min-height: 500px;
    overflow-y: auto;
}

.dialogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  color: #585858;
  font-size: 14px;
  cursor: pointer;
}

.approvalStatus {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #585858;
  font-size: 14px;
}

.approvalText {
  font-weight: 400;     
  font-size: 12px;
}

.tickWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tickWrapper svg {
  width: 30px;
  height: 30px;
}

.outerCircle {
  fill: rgba(23, 157, 82, 0.2);
  stroke: none;
}

.innerCircle {
  fill: #179D52;
  stroke: none;
  animation: heartbeat 2s ease-in-out infinite;
  transform-origin: center;
}

.checkmark {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: drawCheckmark 1s ease-in-out 0.5s forwards;
}

/* Animations */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes drawCheckmark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.dialogTitle {
  font-size: 20px;
  font-weight: 600;
  color: #585858;
  margin: 15px 20px;
}

.paymentMethods {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.paymentOption {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #585858;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
}

.selectedOption {
  border: 2px solid #179D52;
}

.methodDetails {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.methodHeader {
  display: flex;
  align-items: center;
  gap: 10px;
}

.methodIcon {
  width: 40px;
  height: 30px;
  object-fit: contain;
}

.methodNameContainer {
  display: flex;
  flex-direction: column;
}

.methodName {
  font-weight: 600;
  font-size: 16px;
  color: #585858;
}

.methodOwner {
  font-size: 14px;
  color: #585858 ;
}

.methodNumber {
  font-size: 14px;
  color: #666;
}

.noFeeTag {
  background-color: #179D52;
  color: #fff;
  padding: 3px 8px;
  border-radius: 30px;
  font-size: 10px;
  position: absolute;
  top: -12px;
  right: 10px;
}

.feeTag {
  background-color: #DFDFDF;
  color: #585858;
  padding: 3px 8px;
  border-radius: 30px;
  font-size: 10px;
  border: 1px solid #5858584D;
  position: absolute;
  top: -12px;
  right: 10px;
}

.addCard {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: underline;
  gap: 10px;
  padding: 15px;
  color: #585858;
  cursor: pointer;
  font-weight: 500;
}

.paymentFooter {
  margin-top: 20px;
  padding: 0 20px 20px;
}

.paymentFooter p {
  font-size: 12px;
  color: #585858;
  font-weight: 500;
  margin-bottom: 15px;
  line-height: 1.4;
}

.footerLink {
  color: #FFA500;
  text-decoration: none;
}

.payButton {
  background-color: #FFA500 !important;
  border: none !important;
  color: white !important;
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 16px;
}

.payButton:hover {
  background-color: #FF8C00 !important;
}

@media (max-width: 480px) {
  .paymentDialog {
    max-width: 100%;
  }
}