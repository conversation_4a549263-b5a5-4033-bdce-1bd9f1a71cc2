import { PropsWithChildren, useEffect, useState } from "react";
import Loader from "../../commonComponents/Loader";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import { store } from "../../store";
import { updateIsAdminMode } from "../../store/slices/applicationSlice";
import { fetchSessionInfo } from "../../store/tunks/sessionInfoTunk";
import environment from "../../helper/environment";

const AdminInitializer = ({ children }: PropsWithChildren) => {
  const [allowed, setAllowed] = useState(false);
  const [loading, setLoading] = useState(true);

  const check = async () => {
    const params = new URLSearchParams(window.location.search);

    if (params.has("admin_mode") && params.has("access_token")) {
      const expires = new Date();
      expires.setFullYear(new Date().getFullYear() + 1);

      utils.setCookie(CookiesConstant.accessToken, params.get("access_token"), expires);
      utils.setCookie(CookiesConstant.adminMode, params.get("admin_mode"), expires);

      store.dispatch(updateIsAdminMode(true));
      await store.dispatch(fetchSessionInfo());
      setAllowed(true);

      const newUrl = window.location.pathname + window.location.hash;
      window.history.replaceState({}, document.title, newUrl);
    } else {
      setAllowed(true);
    }

    setLoading(false);
  };

  const userAgent = () => {
    const ua = navigator.userAgent;
    const isMobile = /iphone|ipod|android.*mobile|windows phone|blackberry|bb10/i.test(ua);
    const isTablet = /ipad|android(?!.*mobile)|tablet/i.test(ua);
    const mode = environment.getCurrentEnvironment(window.location.hostname);

    const host = window.location.hostname;
    if (["m.", "m2."].some((h) => host.startsWith(h))) return;

    if ((isMobile || isTablet) && mode === "production") {
      const currentUrl = window.location.hostname.replace("web.", "");
      const newUrl = `https://m.${currentUrl}`;

      window.location.href = newUrl;
    }
  };

  useEffect(() => {
    userAgent();
    check();
  }, [check, userAgent]);

  if (loading) {
    return <Loader />;
  }

  return allowed ? children : null;
};

export default AdminInitializer;
