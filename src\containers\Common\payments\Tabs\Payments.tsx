import React, { useState } from 'react';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import '../TimesheetScreen.css';
import { TabView, TabPanel } from 'primereact/tabview';
import { Badge } from 'primereact/badge';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';
import c from '../../../../helper/juggleStreetConstants';
import TimeSheetCard from "../Common/TimeSheetCard";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import ProfileImage from "../../../../assets/images/Icons/my_child.png";

interface PaymentEntry {
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  amount?: number;
  description?: string;
}

const Payments: React.FC = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [selectedEntry, setSelectedEntry] = useState<PaymentEntry | null>(null);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isHelper = clientType === c.clientType.UNSPECIFIED;

  // Sample data using the same structure as timesheet data
  const invoiceApprovalData: PaymentEntry[] = [
    {
      status: "Awaiting Invoice Approval",
      type: "Babysitting Service",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Sarah M",
      amount: 150,
      description: "3 hours of babysitting"
    },
  ];

  const makePaymentData: PaymentEntry[] = [
    {
      status: "Ready for Payment",
      type: "Tutoring Session",
      date: "20th of January, 2024",
      location: "22 Queen Street, Brisbane",
      userName: "Emily R",
      amount: 175,
      description: "Math tutoring session"
    }
  ];

  const paymentHistoryData: PaymentEntry[] = [
    {
      status: "Payment Completed",
      type: "Garden Maintenance",
      date: "10th of January, 2024",
      location: "12 Garden Street, Brisbane",
      userName: "Mike T",
      amount: 100,
      description: "Weekly garden care"
    }
  ];

  const headers = {
    firstTab: isParent ? "Invoice Approval" : "Pending Approval",
    secondTab: isParent ? "Make Payment" : "Awaiting Payment",
    thirdTab: isParent ? "Payment History" : "Payment Receipts",
  };

  // Get counts for badges
  const counts = {
    firstTab: invoiceApprovalData.length,
    secondTab: makePaymentData.length,
    thirdTab: paymentHistoryData.length,
  };

  const handleReview = (entry: PaymentEntry) => {
    setSelectedEntry(entry);
  };

  const closePopup = () => {
    setSelectedEntry(null);
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.2',
        position: 'relative',
      }}
    >
      <span style={{ textAlign: 'center' }}>{title}</span>
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            height:'15px',

            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable payment list component
  const PaymentList = ({ data }: { data: PaymentEntry[] }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <div style={{ 
          padding: '20px', 
          textAlign: 'center', 
          color: '#666'
        }}>
          No payment records available
        </div>
      )}
    </>
  );

  // Popup overlay component (same as timesheet)
  const PopupOverlay = () => (
    selectedEntry && (
      <div className="overlay-popup">
        <div className="slide-up-card">
          <AwaitingConfirmationCard
            profileName={selectedEntry.userName}
            profileImage={ProfileImage}
            jobType={selectedEntry.type}
            jobDate={selectedEntry.date}
            jobAddress={selectedEntry.location}
            baseRate={25}
            extraHoursRate={35}
            initialTimesheetRows={[
              { start: "6:00am", finish: "9:00am", hours: 3, rate: 30, total: 90 },
              { start: "3:30pm", finish: "6:30pm", hours: 3, rate: 45, total: 135 }
            ]}
            onSubmit={closePopup}
            onGoBack={closePopup}
          />
        </div>
      </div>
    )
  );

  return (
    <>
      <TabView 
        activeIndex={activeTabIndex} 
        onTabChange={(e) => setActiveTabIndex(e.index)} 
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.firstTab} count={counts.firstTab} />}>
          <PaymentList data={invoiceApprovalData} />
        </TabPanel>

        <TabPanel header={<TabHeader title={headers.secondTab} count={counts.secondTab} />}>
          <PaymentList data={makePaymentData} />
        </TabPanel>

        <TabPanel header={<TabHeader title={headers.thirdTab} count={counts.thirdTab} />}>
          <PaymentList data={paymentHistoryData} />
        </TabPanel>
      </TabView>

      <PopupOverlay />
    </>
  );
};

export default Payments;