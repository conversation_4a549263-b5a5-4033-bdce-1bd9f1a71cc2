import { useSelector } from "react-redux";
import utils from "../../../../../components/utils/util";
import CookiesConstant from "../../../../../helper/cookiesConst";
import environment from "../../../../../helper/environment";
import c from "../../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import { RootState } from "../../../../../store";
import { useEffect, useState } from "react";
import NumberInput from "../../../../../commonComponents/NumberInput";
import { Divider } from "primereact/divider";
import Pencil from "../../../../../assets/images/Icons/pencil.png";
import { IoClose } from "react-icons/io5";
import { Dialog } from "primereact/dialog";
import styles from "../../../styles/job-pricing.module.css";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import clockForword from "../../../../../assets/images/Icons/clock-forward.png";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import { GoBack, Next } from "../Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

function Step3() {
  const { isMobile } = useIsMobile();
  return isMobile ? <Step3Mobile /> : <Step3Web />;
}
export default Step3;

const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const country = environment.getCountry(window.location.hostname);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [price, setPrice] = useState(payload.price ? String(payload.price) : "");
  const [errorMessage1, setErrorMessage1] = useState("");
  const [errorMessage2, setErrorMessage2] = useState("");
  const [priceEditDisabled, setPriceEditDisabled] = useState(true);
  const [extraPriceEditDisabled, setExtraPriceEditDisabled] = useState(true);
  const [extraPayPopup, setExtraPayPopup] = useState(false);
  const [guidePopup, setGuidePopup] = useState(false);
  const [extraPaySelection, setExtraPaySelection] = useState(payload.willPayOvertime === null ? -1 : payload.willPayOvertime ? 0 : 1);
  const [extraPrice, setExtraPrice] = useState(payload.overtimeRate ? String(payload.overtimeRate) : "");
  const displayPriceGuide = sessionInfo.data?.["client"]["clientType"] == c.clientType.INDIVIDUAL || sessionInfo.data?.["isJobManagerAccount"];
  const notBusiness = sessionInfo.data?.["client"]["clientType"] == c.clientType.INDIVIDUAL ;
  const getJobHours = () => {
    const jobStart = payload.jobStartTime;
    const jobEnd = payload.jobEndTime;

    const timeStringToMinutes = (timeString) => {
      if (!timeString || typeof timeString !== "string") {
        console.error("Invalid time string:", timeString);
        return 0; // Return 0 minutes if the input is invalid
      }

      const [hours, minutes, seconds] = timeString.split(":").map(Number);
      return hours * 60 + minutes + (seconds || 0) / 60;
    };
    const startMinutes = timeStringToMinutes(jobStart);
    const endMinutes = timeStringToMinutes(jobEnd);

    let differenceInMinutes;

    if (startMinutes === endMinutes) {
      differenceInMinutes = 24 * 60;
    } else if (endMinutes > startMinutes) {
      differenceInMinutes = endMinutes - startMinutes;
    } else {
      differenceInMinutes = 24 * 60 - (startMinutes - endMinutes);
    }

    return differenceInMinutes / 60;
  };

  const minPrice = getJobHours() >= 3 ? 25 : 35;

  const isLowRate = () => {
    if (!price || Number(price) === 0 || payload.paymentType === 1) {
      return false;
    }
    const jobPrice = parseFloat(price);
    if (payload.paymentType === 2) {
      return jobPrice < minPrice;
    }
    const hours = getJobHours();
    return jobPrice / hours < minPrice;
  };

  useEffect(() => {
    if (Number(price) === 0 && !priceEditDisabled) {
      setErrorMessage1("Please enter the amount you are willing to pay for the job.");
    } else {
      setErrorMessage1("");
    }

    if (Number(extraPrice) === 0 && !extraPriceEditDisabled) {
      setErrorMessage2("Please enter the amount you are willing to pay for the job.");
    } else {
      setErrorMessage2("");
    }
  }, [price, extraPrice]);

  return {
    clientType,
    payload,
    next,
    prev,
    setpayload,
    country,
    isLowRate,
    price,
    setPrice,
    getJobHours,
    displayPriceGuide,
    errorMessage2,
    setErrorMessage2,
    errorMessage1,
    setErrorMessage1,
    priceEditDisabled,
    setPriceEditDisabled,
    extraPriceEditDisabled,
    setExtraPriceEditDisabled,
    extraPrice,
    setExtraPrice,
    extraPayPopup,
    setExtraPayPopup,
    extraPaySelection,
    setExtraPaySelection,
    minPrice,
    guidePopup,
    setGuidePopup,
    notBusiness,
  };
};

const Step3Web = () => {
  const {
    clientType,
    payload,
    next,
    prev,
    setpayload,
    country,
    isLowRate,
    price,
    minPrice,
    setPrice,
    getJobHours,
    displayPriceGuide,
    errorMessage1,
    extraPrice,
    setExtraPrice,
    setExtraPayPopup,
    extraPaySelection,
    setExtraPaySelection,
    errorMessage2,
    extraPayPopup,
    notBusiness,
  } = useJobTypeHook();
  return (
    <div className="w-full h-full overflow-hidden overflow-y-auto relative">
      <Dialog
        visible={extraPayPopup}
        focusOnShow={false}
        onHide={() => {
          if (Number(extraPrice) < Number(price)) {
            setExtraPrice(price);
          }
          setExtraPayPopup(false);
        }}
        content={
          <div
            className="flex justify-content-center align-items-center"
            style={{
              maxWidth: "533px",
              width: "80vw",
              maxHeight: "258px",
              height: "80vh",
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "33px",
              position: "relative",
              overflow: "visible",
              userSelect: "none",
            }}
          >
            <div
              style={{
                position: "absolute",
                height: "30px",
                width: "30px",
                borderRadius: "50%",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                top: "-10px",
                right: "10px",
                backgroundColor: "#FFFFFF",
                fontSize: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.preventDefault();
                if (Number(extraPrice) < Number(price)) {
                  setExtraPrice(price);
                }
                setExtraPayPopup(false);
              }}
            >
              <IoClose />
            </div>
            <div
              className="w-full h-full flex flex-column justify-content-end"
              style={{
                borderRadius: "33px",
                overflow: "clip",
                padding: "10px",
              }}
            >
              <div className="flex justify-content-center align-items-center">
                <div className="w-min flex flex-column align-items-center">
                  <NumberInput
                    value={String(extraPrice?.length > 0 ? extraPrice : price)}
                    onChange={(value) => {
                      if (value === "") {
                        setExtraPrice("");
                      } else if (!isNaN(Number(value))) {
                        setExtraPrice(value);
                      }
                    }}
                  />
                  <p
                    className="m-0 p-0 relative mr-6"
                    style={{
                      fontWeight: 400,
                      fontSize: "20px",
                      color: "#585858",
                    }}
                  >
                    Per hour
                  </p>
                </div>
              </div>
              {errorMessage2 !== "" && (
                <p
                  className="m-0"
                  style={{
                    backgroundColor: "#FF6359",
                    borderRadius: "8px",
                    fontSize: "12px",
                    color: "#ffffff",
                    fontWeight: "600",
                    padding: "2px 10px",
                    alignSelf: "center",
                    textAlign: "center",
                  }}
                >
                  {errorMessage2}
                </p>
              )}
              <Divider className="my-4" />
              <Next
                disabled={Number(extraPrice) < Number(price)}
                className="align-self-end mb-2"
                onClick={(e) => {
                  e.preventDefault();
                  if (Number(extraPrice) < Number(price)) {
                    setExtraPrice(price);
                  }
                  setExtraPayPopup(false);
                }}
              />
            </div>
          </div>
        }
      />
      <div
        className="flex align-items-center flex-column mx-auto"
        style={{
          height: "100%",
          width: "80%",
          paddingTop: "30px",
        }}
      >
        <div
          style={{
            maxWidth: "757px",
            width: "100%",
          }}
        >
          <div className="w-full h-full flex flex-column align-items-center">
            <div
              className="flex"
              style={{
                width: "100%",
                border: "1px solid #179D52",
                borderRadius: "15px",
                minHeight: "145px",
                backgroundColor: "rgba(240, 244, 247, 0.3)",
                padding: "15px 30px",
              }}
            >
            {notBusiness ? (
              <>
                            <div style={{ width: "45%" }}>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: 700,
                    fontSize: "30px",
                    color: "#585858",
                    textWrap: "wrap",
                  }}
                >
                  Job Price Guide
                </p>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  <span className="inline-block text-left">
                    <span
                      className="inline-block mr-1"
                      style={{
                        fontWeight: "700",
                      }}
                    >
                      Note:
                    </span>
                    Jobs priced below the Price Guide can be difficult to fill.
                  </span>
                </p>
              </div>
              <div className="flex-grow-1 flex flex-column justify-content-around">
                <div className="w-full flex">
                  <div className="w-6 flex flex-column pl-6">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "20px",
                        color: "#585858",
                      }}
                    >
                      Shift Duration
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      2 Hours
                    </p>
                  </div>
                  <div className="w-6 flex flex-column pl-6">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "20px",
                        color: "#585858",
                      }}
                    >
                      Base rate
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Min $35+ p/h
                    </p>
                  </div>
                </div>
                <div
                  style={{
                    width: "88%",
                    backgroundColor: "#F1F1F1",
                    height: "1px",
                    alignSelf: "end",
                  }}
                />
                <div className="w-full flex">
                  <div className="w-6 flex flex-column pl-6">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      3+ Hours
                    </p>
                  </div>
                  <div className="w-6 flex flex-column pl-6">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Av. $25-$35+ p/h
                    </p>
                  </div>
                </div>
              </div>
              </>
            ):(
              <div style={{color: "#585858", fontSize:"18px"}}>
                For details of the childcare wages stipulated in the Children Services Award
                please refer to the government’s Fair Work Commission website.
              </div>
            )}
            </div>
            <div
              className="w-full flex justify-content-between"
              style={{
                padding: "0 30px",
              }}
            >
              <div
                className="flex flex-column"
                style={{
                  marginTop: "13px",
                }}
              >
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: 700,
                    fontSize: "30px",
                    color: "#585858",
                  }}
                >
                  Now Set Your Price
                </p>
                {clientType === "2" && !payload["isJobManagerAccount"] && (
                  <p
                    className="p-0 m-0 mb-2"
                    style={{
                      color: "#585858",
                      fontWeight: 400,
                      fontSize: "14px",
                    }}
                  >
                    {country == "au" && <small>* Excludes Superannuation and other statutory entitlements. </small>}
                    {country == "nz" && <small>* Excludes any government statutory entitlements.</small>}
                  </p>
                )}
                <p
                  className="1 m-0 p-0"
                  style={{
                    color: "#585858",
                    fontWeight: 400,
                    fontSize: "14px",
                  }}
                >
                  {/* <span style={{ fontWeight: 700 }}>Note:</span> Jobs priced
                  below the Price Guide can be difficult to fill. */}
                  {displayPriceGuide && isLowRate() && (
                    <p
                      className="m-0 my-2 p-0"
                      style={{
                        color: "#585858",
                        fontWeight: 500,
                        fontSize: "14px",
                        width: "100%",
                      }}
                    >
                      <span style={{ color: "red" }}>*</span>Jobs priced below the Price Guide can be difficult to fill.
                    </p>
                  )}
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 600,
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {(() => {
                      let amount = 0;

                      if (payload.paymentType === 2) {
                        amount = Number(price) * Number(getJobHours());
                      } else {
                        amount = Number(price);
                      }

                      const total = isNaN(amount) ? "00" : amount.toFixed(2);
                      const hours = isNaN(getJobHours()) ? "0" : getJobHours().toFixed(2);

                      return `Total = $${total} for ${hours} Hour Job`;
                    })()}
                  </p>
                </p>
                {errorMessage1 !== "" && (
                  <p
                    className="m-0"
                    style={{
                      backgroundColor: "#FF6359",
                      borderRadius: "8px",
                      fontSize: "12px",
                      color: "#ffffff",
                      fontWeight: "600",
                      padding: "2px 10px",
                      textAlign: "center",
                    }}
                  >
                    {errorMessage1}
                  </p>
                )}
              </div>
              <div className="flex-grow-1 flex justify-content-end align-items-center">
                <div className="w-min flex flex-column align-items-center relative">
                  <NumberInput
                    value={price}
                    onChange={(e) => {
                      setPrice(e);
                      setExtraPrice(e);
                    }}
                  />
                  {displayPriceGuide && isLowRate() && (
                    <span
                      style={{
                        color: "red",
                        fontSize: "20px",
                        fontWeight: 500,
                        margin: "0 0 0 4px", // Adds a little spacing after the input
                        position: "absolute",
                        right: "12px",
                      }}
                    >
                      *
                    </span>
                  )}
                  {payload.paymentType === 2 && (
                    <p
                      className="m-0 p-0 relative mr-5"
                      style={{
                        fontWeight: 400,
                        fontSize: "20px",
                        color: "#585858",
                      }}
                    >
                      Per hour
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div
              className="mt-4 mb-5"
              style={{
                backgroundColor: "#F1F1F1",
                height: "1px",
                width: "93%",
              }}
            />
            <div
              className="w-full flex justify-content-between align-items-center"
              style={{
                padding: "0 30px",
              }}
            >
              <div
                className="flex flex-column"
                style={{
                  width: "65%",
                  fontWeight: 700,
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                <p className="m-0 p-0">If the job exceeds the hours outlined,</p>
                <p className="m-0 p-0">will you increase the hourly rate for the extra hours?</p>
              </div>
              <div className="flex gap-3 ml-auto mr-5">
                {["Yes", "No"].map((value, index) => (
                  <div
                    key={index}
                    className="flex align-items-center gap-1 cursor-pointer"
                    onClick={() => {
                      // if (price < minPrice) {
                      //     alert(`Minimum Price for this Job is ${minPrice}`);
                      //     return;
                      // }
                      if (index === 0) {
                        setExtraPayPopup(true);
                      }
                      setExtraPaySelection(index);
                    }}
                  >
                    <div
                      className="flex justify-content-center align-items-center cursor-pointer"
                      style={{
                        height: "18px",
                        width: "18px",
                        borderRadius: "50%",
                        padding: "1px",
                        border: `1px solid ${extraPaySelection === index ? "#179d52" : "#DFDFDF"}`,
                      }}
                      onClick={() => {
                        if (Number(price) >= minPrice) {
                          setExtraPaySelection(index);
                        }
                      }}
                    >
                      {extraPaySelection === index && (
                        <div
                          style={{
                            height: "100%",
                            width: "100%",
                            borderRadius: "50%",
                            backgroundColor: "#179D52",
                          }}
                        />
                      )}
                    </div>
                    <label
                      className="cursor-pointer"
                      onClick={() => {
                        if (Number(price) >= minPrice) {
                          setExtraPaySelection(index);
                        }
                      }}
                      style={{
                        fontWeight: 400,
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {value}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div
              className="mt-4 mb-5"
              style={{
                backgroundColor: "#F1F1F1",
                height: "1px",
                width: "93%",
              }}
            />
            {extraPaySelection !== -1 && (
              <div
                className="w-full flex justify-content-between "
                style={{
                  padding: "0 30px",
                }}
              >
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: 700,
                    fontSize: "30px",
                    color: "#585858",
                  }}
                >
                  Job Summary
                </p>
                <div className="flex gap-4 lg:gap-4">
                  <div className="flex flex-column gap-1 text-center">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 500,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Job Cost
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "30px",
                        color: "#585858",
                      }}
                    >
                      $
                      {(() => {
                        let amount = 0;

                        if (payload.paymentType === 2) {
                          amount = Number(price) * Number(getJobHours());
                        } else {
                          amount = Number(price);
                        }

                        return isNaN(amount) ? "00" : amount.toFixed(2);
                      })()}
                    </p>
                  </div>
                  <div className="flex flex-column gap-1 text-center">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 500,
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Job length
                    </p>
                    <span
                      className="m-0 p-0 "
                      style={{
                        fontWeight: 700,
                        fontSize: "30px",
                        color: "#585858",
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      {getJobHours().toFixed(2)}
                      <span style={{ fontSize: "16px", fontWeight: "500" }}> Hours</span>
                    </span>
                  </div>
                  {extraPaySelection === 0 && (
                    <div className="flex flex-column gap-1 text-center">
                      <div className="flex gap-2">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 500,
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          Extra hours rate
                        </p>
                        <div
                          className="flex justify-content-center align-items-center"
                          style={{
                            minWidth: "26px",
                            // height: '27px',
                            backgroundColor: "#FFA500",
                            borderRadius: "50%",
                            boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                            cursor: "pointer",
                          }}
                          onClick={(e) => {
                            e.preventDefault();
                            setExtraPayPopup(true);
                          }}
                        >
                          <img src={Pencil} alt="pencil" />
                        </div>
                      </div>
                      <p
                        className="p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "30px",
                          color: "#585858",
                          margin: "0",
                          marginBottom: "-3px",
                        }}
                      >
                        ${extraPrice}
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        Per hour
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div
        className=" flex flex-column sticky justify-content-center align-items-center bottom-0 bg-white"
        style={{
          width: "100%",
        }}
      >
        <Divider className="" />
        <div className="flex justify-content-between align-content-center py-3" style={{ width: "80%" }}>
          <GoBack
            onClick={() => {
              setpayload({
                ...payload,
                willPayOvertime: extraPaySelection === 0,
                price: Number(price),
                overtimeRate: extraPaySelection === 0 ? Number(extraPrice) : null,
                applicantFilters: payload.applicantFilters
                  ? [
                      ...payload.applicantFilters.filter((af) => af.field !== "hourlyRate"),
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ]
                  : [
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ],
              });

              prev("jobpricing-step2");
            }}
          />
          <Next
            disabled={extraPaySelection === -1 || Number(price) < 1}
            onClick={() => {
              setpayload({
                ...payload,
                willPayOvertime: extraPaySelection === 0,
                price: Number(price),
                overtimeRate: extraPaySelection === 0 ? Number(extraPrice) : null,
                applicantFilters:
                  payload.applicantFilters === undefined
                    ? [
                        {
                          field: "hourlyRate",
                          operator: "eq",
                          value: Number(price),
                        },
                      ]
                    : [
                        ...payload.applicantFilters.filter((af) => af.field !== "hourlyRate"),
                        {
                          field: "hourlyRate",
                          operator: "eq",
                          value: Number(price),
                        },
                      ],
              });
              if (payload.managedBy === c.managedBy.SYSTEM) {
                next("candidate-matching");
              } else {
                next("candidate-selection");
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

const Step3Mobile = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    isLowRate,
    price,
    setPrice,
    getJobHours,
    displayPriceGuide,
    errorMessage1,
    priceEditDisabled,
    extraPrice,
    extraPaySelection,
    guidePopup,
    setPriceEditDisabled,
    setGuidePopup,
    notBusiness
  } = useJobTypeHook();
  return (
    <div className={styles.JobpricingMain}>
      {/* <Dialog
      visible={extraPayPopup}
      onHide={() => setExtraPayPopup(false)}
      content={
        <div
          className="flex justify-content-center align-items-center"
          style={{
            maxWidth: "533px",
            width: "80vw",
            maxHeight: "258px",
            height: "80vh",
            backgroundColor: "#FFFFFF",
            border: "1px solid #F0F4F7",
            borderRadius: "33px",
            position: "relative",
            overflow: "visible",
            userSelect: "none",
          }}
        >
          <div
            style={{
              position: "absolute",
              height: "30px",
              width: "30px",
              borderRadius: "50%",
              boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              top: "-10px",
              right: "10px",
              backgroundColor: "#FFFFFF",
              fontSize: "20px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
            }}
            onClick={(e) => {
              e.preventDefault();
              // if (extraPrice < minExtraPrice) {
              //     alert(`Minimum Extra for this Job is ${minExtraPrice}`);
              //     return;
              // }
              setExtraPayPopup(false);
            }}
          >
            <IoClose />
          </div>
          <div
            className="w-full h-full flex flex-column justify-content-end"
            style={{
              borderRadius: "33px",
              overflow: "clip",
              padding: "10px",
            }}
          >
            <div className="flex justify-content-center align-items-center">
              <div className="w-min flex flex-column align-items-center">
                <input
                  type="text"
                  name="price"
                  id="price"
                  placeholder={`$${extraPrice}`}
                  value={!extraPriceEditDisabled ? `$${extraPrice}` : ""}
                  className={`${styles.priceInput}`}
                  onClick={() => setExtraPrice(0)}
                  onChange={(e) => {
                    const val = e.target.value;
                    const numericValue = val.replace(/[^0-9]/g, "");
                    if (numericValue) {
                      setExtraPrice(Number(numericValue));
                    } else if (numericValue === "") {
                      setExtraPrice(0);
                    }
                  }}
                  style={{
                    border: "none",
                    width: "150px",
                    height: "79px",
                  }}
                  disabled={extraPriceEditDisabled}
                />
                <p
                  className="m-0 p-0 relative"
                  style={{
                    fontWeight: 400,
                    fontSize: "20px",
                    color: "#585858",
                  }}
                >
                  Per hour
                </p>
              </div>
              <div
                className="flex justify-content-center align-items-center"
                style={{
                  width: "26px",
                  height: "27px",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                  cursor: "pointer",
                }}
                onClick={() =>
                  setExtraPriceEditDisabled(!extraPriceEditDisabled)
                }
              >
                <img src={PencilBlack} alt="pencil" />
              </div>
            </div>
            <Divider className="my-4" />
            <Next
              className="align-self-end mb-2"
              onClick={(e) => {
                e.preventDefault();

                // if (extraPrice < minExtraPrice) {
                //     alert(`Minimum Extra for this Job is ${minExtraPrice}`);
                //     return;
                // }
                setExtraPayPopup(false);
              }}
            />
          </div>
        </div>
      }
    /> */}
      <Dialog
        visible={guidePopup}
        onHide={() => setGuidePopup(false)}
        content={
          <div
            className="flex justify-content-center align-items-center"
            style={{
              width: "100%",
              height: "100%",
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "33px",
              position: "relative",
              overflow: "visible",
              userSelect: "none",
              paddingInline: "30px",
              paddingBlock: "20px",
            }}
          >
            <div
              style={{
                position: "absolute",
                height: "30px",
                width: "30px",
                borderRadius: "50%",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                top: "-10px",
                right: "10px",
                backgroundColor: "#FFFFFF",
                fontSize: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.preventDefault();
                // if (extraPrice < minExtraPrice) {
                //     alert(`Minimum Extra for this Job is ${minExtraPrice}`);
                //     return;
                // }
                setGuidePopup(false);
              }}
            >
              <IoClose />
            </div>
            <div className={styles.priceGuide}>
              <div>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: 700,
                    fontSize: "22px",
                    color: "#444444",
                    textWrap: "wrap",
                  }}
                >
                  Job Price Guide
                </p>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  <span className="inline-block text-left">
                    <span
                      className="inline-block mr-1"
                      style={{
                        fontWeight: "700",
                      }}
                    >
                      Note:
                    </span>
                    Jobs priced below the Price Guide can be difficult to fill.
                  </span>
                </p>
              </div>
              <div className={styles.shiftDivMobile}>
                <div className={styles.shiftOne}>
                  <div
                    style={{
                      backgroundColor: "#1F9EAB",
                      borderRadius: "50%",
                    }}
                  >
                    <p
                      style={{
                        fontWeight: 700,
                        fontSize: "12px",
                        color: "#fff",
                        paddingInline: "15px",
                      }}
                    >
                      <span
                        style={{
                          fontSize: "16px",
                          fontWeight: 700,
                          color: "#fff",
                        }}
                      >
                        2
                      </span>{" "}
                      hr
                    </p>
                  </div>
                  <div className="mr-3">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "12px",
                        color: "#585858",
                      }}
                    >
                      Base rate
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "15px",
                        color: "#585858",
                      }}
                    >
                      Min $35+ p/h
                    </p>
                  </div>
                </div>

                <div className={styles.shiftDivTwoMobile}>
                  <div
                    style={{
                      backgroundColor: "#8577DB",
                      borderRadius: "50%",
                      marginLeft: "8px",
                    }}
                  >
                    <p
                      style={{
                        fontWeight: 700,
                        fontSize: "12px",
                        color: "#fff",
                        paddingInline: "8px",
                        paddingBlock: "5px",
                      }}
                    >
                      <span
                        style={{
                          fontSize: "16px",
                          fontWeight: 700,
                          color: "#fff",
                        }}
                      >
                        3+
                      </span>{" "}
                      hrs
                    </p>
                  </div>
                  <div className="mr-3 flex flex-column justify-content-center align-items-center">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 400,
                        fontSize: "12px",
                        color: "#585858",
                      }}
                    >
                      Base rate
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "15px",
                        color: "#585858",
                      }}
                    >
                      Av. $25-$35+ p/h
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-3">
                <button className={styles.gotitBtn} onClick={() => setGuidePopup(false)}>
                  Got it
                </button>
              </div>
            </div>
          </div>
        }
      />
      <div
        className="flex align-items-center flex-column"
        style={{
          height: "100%",
          width: "100%",
          paddingTop: "20px",
        }}
      >
        <div
          style={{
            maxWidth: "757px",
            width: "100%",
          }}
        >
          <div className="w-full h-full flex flex-column align-items-center">
            <div
              className="w-full flex justify-content-between"
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                paddingInline: "25px",
              }}
            >
              <div className="flex flex-column align-items-center">
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: 700,
                    fontSize: "30px",
                    color: "#585858",
                  }}
                >
                  {payload.paymentType === 1 ? "Fixed Price" : "Base Rate"}
                </p>
                <p
                  className="1 m-0 p-0"
                  style={{
                    color: "#585858",
                    fontWeight: 400,
                    fontSize: "14px",
                  }}
                >
                  {/* <span style={{ fontWeight: 700 }}>Note:</span> Jobs priced
                  below the Price Guide can be difficult to fill. */}
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 600,
                      fontSize: "12px",
                      color: "#585858",
                    }}
                  >
                    {(() => {
                      let amount = 0;

                      if (payload.paymentType === 2) {
                        amount = Number(price) * Number(getJobHours());
                      } else {
                        amount = Number(price);
                      }

                      const total = isNaN(amount) ? "00" : amount.toFixed(2);
                      const hours = isNaN(getJobHours()) ? "0" : getJobHours().toFixed(2);

                      return `Total price $${total} for ${hours} Hour Job`;
                    })()}
                  </p>
                </p>
                {errorMessage1 !== "" && (
                  <p
                    className="m-0"
                    style={{
                      backgroundColor: "#FF6359",
                      borderRadius: "8px",
                      fontSize: "12px",
                      color: "#ffffff",
                      fontWeight: "600",
                      padding: "2px 10px",
                      alignSelf: "center",
                      textAlign: "center",
                    }}
                  >
                    {errorMessage1}
                  </p>
                )}
              </div>
              <div className="flex-grow-1 flex justify-content-end align-items-center">
                <div className="w-min flex flex-column align-items-center relative">
                  <NumberInput
                    value={price}
                    onChange={(e) => {
                      setPrice(e);
                    }}
                    onFocus={() => setPriceEditDisabled(false)} // Add this
                    onBlur={() => setPriceEditDisabled(true)} // Add this
                  />
                  {displayPriceGuide && isLowRate() && (
                    <span
                      style={{
                        color: "red",
                        fontSize: "20px",
                        fontWeight: 500,
                        margin: "0 0 0 4px", // Adds a little spacing after the input
                        position: "absolute",
                        right: "10px",
                      }}
                    >
                      *
                    </span>
                  )}
                  {payload.paymentType === 2 && (
                    <p
                      className="m-0 p-0 relative mr-6"
                      style={{
                        fontWeight: 400,
                        fontSize: "20px",
                        color: "#585858",
                      }}
                    >
                      Per hour
                    </p>
                  )}
                </div>
              </div>
            </div>
            {!priceEditDisabled && (
              <div>
                <p
                  style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    color: "#FFA500",
                    textDecoration: "underline",
                  }}
                  onClick={() => setGuidePopup(true)}
                >
                  Learn more about pricing
                </p>
              </div>
            )}
            {!priceEditDisabled && (
              <>
                <Divider />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    gap: "20px",
                    marginTop: "30px",
                  }}
                >
                  <div className={styles.jobLength}>
                    <img src={clockForword} alt="clockForword" width="20px" height="18px" />
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "16px",
                        color: "#fff",
                      }}
                    >
                      {getJobHours().toFixed(2)} hrs
                    </p>
                  </div>
                  <div className={styles.jobCost}>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "16px",
                        color: "#fff",
                      }}
                    >
                      ${payload.paymentType === 2 ? ((Number(price) * getJobHours()) as number).toFixed(2) : Number(price).toFixed(2)} Job Total
                    </p>
                  </div>
                </div>
              </>
            )}
            <div className="px-2">
              {displayPriceGuide && isLowRate() && (
                <p
                  className="m-0 mt-3 p-0"
                  style={{
                    color: "#585858",
                    fontWeight: 300,
                    fontSize: "14px",
                    textAlign: "center",
                  }}
                >
                  <span style={{ color: "red" }}> *</span>Jobs priced below the Price Guide can be difficult to fill.
                </p>
              )}
            </div>
            <Divider className="mt-5" />
            {priceEditDisabled && notBusiness &&   (
              <div className={styles.priceGuide}>
                <div>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 700,
                      fontSize: "22px",
                      color: "#444444",
                      textWrap: "wrap",
                    }}
                  >
                    Job Price Guide
                  </p>
                  {/* <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    <span className="inline-block text-left">
                      <span
                        className="inline-block mr-1"
                        style={{
                          fontWeight: "700",
                        }}
                      >
                        Note:
                      </span>
                      Jobs priced below the Price Guide can be difficult to fill.
                    </span>
                  </p> */}
                </div>
                <div className={styles.shiftDivMobile}>
                  <div className={styles.shiftOne}>
                    <div
                      style={{
                        backgroundColor: "#1F9EAB",
                        borderRadius: "50%",
                      }}
                    >
                      <p
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#fff",
                          paddingInline: "15px",
                        }}
                      >
                        <span
                          style={{
                            fontSize: "20px",
                            fontWeight: 700,
                            color: "#fff",
                          }}
                        >
                          2
                        </span>{" "}
                        hr
                      </p>
                    </div>
                    <div className="mr-3">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        Base rate
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "20px",
                          color: "#585858",
                        }}
                      >
                        Min $35+ p/h
                      </p>
                    </div>
                  </div>

                  <div className={styles.shiftDivTwoMobile}>
                    <div
                      style={{
                        backgroundColor: "#8577DB",
                        borderRadius: "50%",
                        marginLeft: "8px",
                      }}
                    >
                      <p
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#fff",
                          paddingInline: "8px",
                          paddingBlock: "5px",
                        }}
                      >
                        <span
                          style={{
                            fontSize: "20px",
                            fontWeight: 700,
                            color: "#fff",
                          }}
                        >
                          3+
                        </span>{" "}
                        hrs
                      </p>
                    </div>
                    <div className="mr-3 flex flex-column justify-content-center align-items-center">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        Base rate
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "20px",
                          color: "#585858",
                        }}
                      >
                        Av. $25-$35+ p/h
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

              {priceEditDisabled && !notBusiness &&   (
              <div className={styles.priceGuide}>
                 <div style={{color: "#585858", fontSize:"14px", textAlign:"center"}}>
                For details of the childcare wages stipulated in the Children Services Award
                please refer to the government’s Fair Work Commission website.
              </div>
              
              </div>
            )}
            <div
              className="w-full flex justify-content-between align-items-center"
              style={{
                padding: "0 30px",
              }}
            ></div>

            {extraPaySelection !== 1 && (
              <div
                className="w-full flex justify-content-between "
                style={{
                  padding: "0 30px",
                }}
              >
                <div className="flex gap-4 lg:gap-4"></div>
              </div>
            )}
          </div>
        </div>

        <div
          className="flex flex-column mt-6"
          style={{
            width: "100%",
          }}
        >
          <BackButtonPortal id="back-button-portal">
            <div
              onClick={() => {
                setpayload({
                  ...payload,
                  willPayOvertime: extraPaySelection === 0,
                  price: Number(price),
                  overtimeRate: extraPaySelection === 0 ? Number(extraPrice) : null,
                  applicantFilters:
                    payload.applicantFilters === null
                      ? [
                          {
                            field: "hourlyRate",
                            operator: "eq",
                            value: price,
                          },
                        ]
                      : [
                          ...payload.applicantFilters.filter((af) => af.field !== "hourlyRate"),
                          {
                            field: "hourlyRate",
                            operator: "eq",
                            value: price,
                          },
                        ],
                });
                prev("jobpricing-step2"); // Navigate to the previous step
              }}
            >
              <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
            </div>
          </BackButtonPortal>
        </div>
        <CustomFooterButton
          label="Next"
          isDisabled={Number(price) < 1}
          onClick={() => {
            // Save the current state and navigate to overtime route
            setpayload({
              ...payload,
              price: Number(price),
              // other payload updates...
            });
            next("overtime-section-mobile"); // Navigate to the next step
          }}
        />
      </div>
    </div>
  );
};
