import { createSlice } from "@reduxjs/toolkit";
import { ApplicationStateType } from "../types";

const initialState: ApplicationStateType = {
  isAdminMode: false,
  inIframe: false,
  isMobile: false,
  sideBarIsOpened: window.innerWidth > 768,
  profileActivationCurrentStep: 1,
  shouldShowProfileActivation: false,
  crispChatWindow: false,
  defaultFilters: {
    filters: [
      {
        field: "neighbourhood",
        value: null,
        operator: "eq",
      },
      {
        field: "ageGroups",
        value: [1, 2, 3, 4],
        operator: "eq",
      },
      {
        field: "jobTypes",
        value: [1, 2, 4, 8],
        operator: "eq",
      },
      {
        field: "experience",
        value: null,
        operator: "eq",
      },
      {
        field: "ratings",
        value: [],
        operator: "eq",
      },
      {
        field: "activity",
        value: null,
        operator: "eq",
      },
      {
        field: "distance",
        value: 1,
        operator: "eq",
      },
      {
        field: "driving",
        value: [],
        operator: "eq",
      },
      {
        field: "otherSkills",
        value: [],
        operator: "eq",
      },
      {
        field: "tutoringCategory",
        value: [],
        operator: "eq",
      },
      {
        field: "auPairCategory",
        value: null,
        operator: "eq",
      },
      {
        field: "nationality",
        value: null,
        operator: "eq",
      },
      {
        field: "jobDeliveryMethod",
        value: null,
        operator: "eq",
      },
      {
        field: "jobSubTypes",
        value: [],
        operator: "eq",
      },
    ],
    pageIndex: 1,
    pageSize: 50,
    sortBy: "experience",
  },
  filters: {
    filters: [
      {
        field: "neighbourhood",
        value: 0,
        operator: "eq",
      },
      {
        field: "ageGroups",
        value: [1, 2, 3, 4],
        operator: "eq",
      },
      {
        field: "jobTypes",
        value: [1, 2, 4, 8],
        operator: "eq",
      },
      {
        field: "experience",
        value: 0,
        operator: "eq",
      },
      {
        field: "ratings",
        value: [],
        operator: "eq",
      },
      {
        field: "activity",
        value: 0,
        operator: "eq",
      },
      {
        field: "distance",
        value: 2,
        operator: "eq",
      },
      {
        field: "driving",
        value: [],
        operator: "eq",
      },
      {
        field: "otherSkills",
        value: [],
        operator: "eq",
      },
      {
        field: "tutoringCategory",
        value: [],
        operator: "eq",
      },
      {
        field: "auPairCategory",
        value: null,
        operator: "eq",
      },
      {
        field: "nationality",
        value: null,
        operator: "eq",
      },
      {
        field: "jobDeliveryMethod",
        value: null,
        operator: "eq",
      },
      {
        field: "jobSubTypes",
        value: [],
        operator: "eq",
      },
    ],
    pageIndex: 1,
    pageSize: 50,
    sortBy: "experience",
  },

  selectedMarkerSignal: -1,
  showChatBox: true,
  enableAccountAndSettings: false,
  accountAndSettingsActiveTab: -1,
  enableAccountAndSettingsBuisness: false,
  accountAndSettingsActiveTabBuisness: -1,
  loaderEnabled: false,
  profileActivationEnabled: false,
  membershipActivationEnabled: false,
  interestInHomeAgedCareResponse: false,
  hasRequestedEmployeeBenefits: false,
};

const ApplicationState = createSlice({
  name: "applicationState",
  initialState,
  reducers: {
    updateChatWindowState: (state, action) => {
      state.crispChatWindow = action.payload;
    },
    toggleSideBar: (state) => {
      state.sideBarIsOpened = !state.sideBarIsOpened;
    },
    incrementProfileActivationStep: (state) => {
      state.profileActivationCurrentStep += 1;
    },
    setProfileActivationStep: (state, action) => {
      state.profileActivationCurrentStep = action.payload;
    },
    decrementProfileActivationStep: (state) => {
      if (state.profileActivationCurrentStep > 1) {
        state.profileActivationCurrentStep -= 1;
      }
    },
    setCurrentProfileActivationStep: (state, action) => {
      state.profileActivationCurrentStep = action.payload;
    },
    updateShowProfileActivation: (state, action) => {
      state.shouldShowProfileActivation = action.payload;
    },
    setSelectedMarkerSignal: (state, action) => {
      state.selectedMarkerSignal = action.payload;
    },
    updateShowChatBox: (state, action) => {
      state.showChatBox = action.payload;
    },
    updateSearchFilter: (state, action) => {
      state.filters = action.payload;
    },
    updateShowAccountAndSettings: (state, action) => {
      state.enableAccountAndSettings = action.payload;
    },
    updateAccountAndSettingsActiveTab: (state, action) => {
      state.accountAndSettingsActiveTab = action.payload;
    },
    updateShowAccountAndSettingsBuisness: (state, action) => {
      state.enableAccountAndSettingsBuisness = action.payload;
    },
    updateAccountAndSettingsActiveTabBuisness: (state, action) => {
      state.accountAndSettingsActiveTabBuisness = action.payload;
    },
    updateLoaderEnabled: (state, action) => {
      state.loaderEnabled = action.payload;
    },
    updateProfileActivationEnabled: (state, action) => {
      state.profileActivationEnabled = action.payload;
    },
    updateMembershipActivationEnabled: (state, action) => {
      state.membershipActivationEnabled = action.payload;
    },
    updateinterestInHomeAgedCareResponse: (state, action) => {
      state.interestInHomeAgedCareResponse = action.payload;
    },
    updatehasRequestedEmployeeBenefits: (state, action) => {
      state.hasRequestedEmployeeBenefits = action.payload;
    },
    updateIsMobile: (state, action) => {
      state.isMobile = action.payload;
    },
    updateInIFrame: (state, action) => {
      state.inIframe = action.payload;
    },
    updateIsAdminMode: (state, action) => {
      state.isAdminMode = action.payload;
    },
  },
});

export const {
  toggleSideBar,
  incrementProfileActivationStep,
  decrementProfileActivationStep,
  setProfileActivationStep,
  setCurrentProfileActivationStep,
  updateShowProfileActivation,
  setSelectedMarkerSignal,
  updateShowChatBox,
  updateSearchFilter,
  updateShowAccountAndSettings,
  updateAccountAndSettingsActiveTab,
  updateShowAccountAndSettingsBuisness,
  updateAccountAndSettingsActiveTabBuisness,
  updateLoaderEnabled,
  updateProfileActivationEnabled,
  updateMembershipActivationEnabled,
  updateinterestInHomeAgedCareResponse,
  updatehasRequestedEmployeeBenefits,
  updateChatWindowState,
  updateIsMobile,
  updateInIFrame,
  updateIsAdminMode,
} = ApplicationState.actions;
export default ApplicationState.reducer;
