import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useState } from "react";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import { useNavigate } from 'react-router-dom';
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import ProfileImage from "../../../../assets/images/Icons/my_child.png";
import { Badge } from "primereact/badge";

interface TimesheetEntry {
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
}

interface TimeSheetProps {
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;
}

const TimeSheet: React.FC<TimeSheetProps> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [selectedEntry, setSelectedEntry] = useState<TimesheetEntry | null>(null);
  const navigate = useNavigate();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;

  // Sample data - replace with actual data from props or API
  const timesheetData: TimesheetEntry[] = [
    {
      status: "Awaiting Your Approval",
      type: "One Off Job",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Craig S",
    },
  ];

  const helperAdjustedData: TimesheetEntry[] = [
    {
      status: "Awaiting Your Approval",
      type: "One Off Job",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Craig S",
    },
  ];

  const awaitingApprovalData: TimesheetEntry[] = [
    {
      status: "Finalized",
      type: "Regular Job",
      date: "18th of January, 2024",
      location: "15 Main Street, Brisbane",
      userName: "Sarah M",
    },
    {
      status: "Finalized",
      type: "One Off Job",
      date: "20th of January, 2024",
      location: "22 Queen Street, Brisbane",
      userName: "John D",
    },
  ];

  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };

  const handleReview = (entry: TimesheetEntry) => {
    setSelectedEntry(entry);
  };

  const closePopup = () => {
    setSelectedEntry(null);
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.4',
        position: 'relative',
      }}
    >
      <span
        dangerouslySetInnerHTML={{ __html: title }}
        style={{ textAlign: 'center' }}
      />
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            display: "flex",
            height:'15px',
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable timesheet list component
  const TimesheetList = ({ data }: { data: TimesheetEntry[] }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: lightGrayText,
          backgroundColor: screenBg
        }}>
          No timesheets available
        </div>
      )}
    </>
  );

  // Popup overlay component
  const PopupOverlay = () => (
    selectedEntry && (
      <div className="overlay-popup">
        <div className="slide-up-card">
          <AwaitingConfirmationCard
            profileName={selectedEntry.userName}
            profileImage={ProfileImage}
            jobType={selectedEntry.type}
            jobDate={selectedEntry.date}
            jobAddress={selectedEntry.location}
            baseRate={25}
            extraHoursRate={35}
            initialTimesheetRows={[
              { start: "6:00am", finish: "9:00am", hours: 3, rate: 30, total: 90 },
              { start: "3:30pm", finish: "6:30pm", hours: 3, rate: 45, total: 135 }
            ]}
            onSubmit={closePopup}
            onGoBack={closePopup}
          />
        </div>
      </div>
    )
  );

  return (
    <>
      <TabView
        activeIndex={activeTabIndex}
        onTabChange={(e) => setActiveTabIndex(e.index)}
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.awaitingConfirmation} count={timesheetData.length} />}>
          <TimesheetList data={timesheetData} />
        </TabPanel>

        <TabPanel header={<TabHeader title={headers.adjustedTimesheets} count={helperAdjustedData.length} />}>
          <TimesheetList data={helperAdjustedData} />
        </TabPanel>

        <TabPanel header={<TabHeader title={headers.awaitingApproval} count={awaitingApprovalData.length} />}>
          <TimesheetList data={awaitingApprovalData} />
        </TabPanel>
      </TabView>

      <PopupOverlay />
    </>
  );
};

export default TimeSheet;