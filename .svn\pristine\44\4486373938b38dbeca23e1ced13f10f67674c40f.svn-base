import React, { useState } from "react";
import { Dialog } from "primereact/dialog";
import { <PERSON><PERSON> } from "primereact/button";
import styles from "./styles/employee-benefits.module.css";
import EmployeeBenefits from "./EmployeeBenefits";
import Service from "../../services/services";
import c from "../../helper/juggleStreetConstants";
import environment from "../../helper/environment";
import useLoader from "../../hooks/LoaderHook";
import { refreshAccount } from "../../store/tunks/sessionInfoTunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";

interface EmployeeBenefitsRequestProps {
  visible: boolean;
  onHide: () => void;
}

interface AppEventPayload {
  status: string;
  // Add any other required payload fields here
}

const EmployeeBenefitsRequest: React.FC<EmployeeBenefitsRequestProps> = ({
  visible,
  onHide,
}) => {
  const [loading, setLoading] = useState(false);
  const [showFullForm, setShowFullForm] = useState(false);
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>(); 
  const getEventType = (action: 'yes' | 'no' | 'later') => {
    const eventTypes = {
      'yes': c.appEventType.requestedEmployeeBenefits,
      'no': c.appEventType.cancelledEmployeeBenefits,
      'later': c.appEventType.delayedEmployeeBenefits
    };
    return eventTypes[action];
  };

  const handleSubmit = async (action: 'yes' | 'no' | 'later') => {
    enableLoader();
    
    const payload: AppEventPayload = {
      status: c.appEventType.initiatedEmployeeBenefits.toString()
      // Add any other required fields here
    };
  
    try {
      const logEventPayload = {
        eventType: getEventType(action),
        platform: "desktop-web",
        appVersion: environment.getAppVersion(),
        distVersion: environment.getAppVersion(),
        country: environment.getCountry(window.location.hostname),
      };

      Service.logAppEvent(
        () => {
          // Success callback for logEvent
          if (action === 'yes') {
            setShowFullForm(true);
          }
          dispatch(refreshAccount());
          disableLoader();
        },
        (error) => {
          // Failure callback for logEvent
          console.error("Failed to log event:", error);
          disableLoader();
        },
        logEventPayload
      );
    } catch (error) {
      setLoading(false);
      console.error("Error submitting:", error);
      disableLoader();
    }
  };

  const handleYes = () => {
    handleSubmit('yes');
  };
  
  const handleNo = () => {
    handleSubmit('no');
    onHide();
  };
  
  const handleLater = () => {
    handleSubmit('later');
    onHide();
  };
  
  const handleFormClose = () => {
    setShowFullForm(false);
    onHide();
  };

  // If showing full form, render the EmployeeBenefits component
  if (showFullForm && visible) {
    return <EmployeeBenefits isDialogVisible={true} onHide={handleFormClose} />;
  }

  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      className="w-full max-w-lg"
      modal
      closeOnEscape={!loading}
      closable={!loading}
      content={
        <div className={styles.dialogContent}>
          <div className="flex flex-column py-3 px-5">
            <h1 style={{fontSize:"24px",color:"#585858",fontWeight:"700"}}>Employee Benefits Program</h1>
            <div style={{fontSize:"16px",color:"#585858",fontWeight:"500",paddingBottom:"15px"}}>
              <p>
                Our Employee Benefit Program allows businesses to pay for Juggle
                Street memberships on behalf of their employees.
              </p>
              <p>
                Are you happy to introduce Juggle Street to your company?
              </p>
            </div>
            <div className="flex justify-end gap-2">
              <button
                onClick={handleNo}
                className={styles.benefitsLater}
                disabled={loading}
              >No Thanks</button>

              <button
                onClick={handleLater}
                className={styles.benefitsLater}
                disabled={loading}
              >Later</button>

              <button
                onClick={handleYes}
                className={styles.benefitsSubmit}
                disabled={loading}
              >Yes</button>
            </div>
          </div>
        </div>
      }
    />
  );
};

export default EmployeeBenefitsRequest;