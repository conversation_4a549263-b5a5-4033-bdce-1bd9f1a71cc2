import React, { CSSProperties, useEffect, useState } from "react";
import { ExtendedProfileTabProps } from "../types";
import { Divider } from "primereact/divider";
import check from "../../../../assets/images/Icons/check-green.png";
import earth from "../../../../assets/images/Icons/earth.png";
import checkStar from "../../../../assets/images/Icons/check-star.png";
import degree from "../../../../assets/images/Icons/degree.png";
import star from "../../../../assets/images/Icons/star.png";
import rank from "../../../../assets/images/Icons/rank.png";
import c from "../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../hooks/useIsMobile";
import { PiFirstAidKit } from "react-icons/pi";
import { TbLicense } from "react-icons/tb";
import { IoLanguageOutline } from "react-icons/io5";

const LimitedText = ({
  text,
  limit,
  disableLimit,
  style,
  className,
}: {
  text: string;
  limit: number;
  disableLimit: boolean;
  style?: CSSProperties;
  className?: string;
}) => {
  const displayText =
    disableLimit || text.length <= limit ? text : text.slice(0, limit);
  return (
    <span className={className} style={{ ...style }}>
      {displayText}
    </span>
  );
};

const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{
        border: "1px solid #F1F1F1",
        borderRadius: "30px",
        width: !isMobile ? "90%" : "100%",
        maxWidth: !isMobile ? "90%" : "100%",
        marginTop:isMobile && "15px"
      }}
    >
      {children}
    </div>
  );
};
const AboutSection = ({
  text,
  childcare,
  tutoring,
  oddJobs,
  nationality,
  interestedInJobType,
}: {
  text: string;
  childcare: boolean;
  tutoring: boolean;
  oddJobs: boolean;
  nationality: string;
  interestedInJobType?: any;
}) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  const { isMobile } = useIsMobile();

  function interestedInTutoring() {
    const interestedInJobTypes = interestedInJobType;

    return (
      interestedInJobTypes & c.jobType.PRIMARY_SCHOOL_TUTORING ||
      interestedInJobTypes & c.jobType.HIGH_SCHOOL_TUTORING
    );
  }
  return (
    <div className="px-4 pt-2 pb-6">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: !isMobile ? "20px" : "16px",
          color: "#585858",
        }}
      >
        About
      </h1>
      <div className="py-2 px-3">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: "#585858",
            textWrap: "wrap",
            maxWidth: "612px",
          }}
        >
          <span
            className="m-0 p-0 inline-block text-left"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
              textWrap: "wrap",
              width: "100%",
            }}
          >
            <LimitedText
              className="m-0 p-0"
              text={text}
              limit={displayLimit}
              disableLimit={textState}
              style={{
                fontWeight: "400",
                fontSize: !isMobile ? "16px" : "14px",
                color: "#585858",
                textWrap: "wrap",
                width: "100%",
              }}
            />
            {text.length >= displayLimit && (
              <span
                className="cursor-pointer hover:text-gray-300"
                style={{
                  fontWeight: "400",
                  fontSize: "12px",
                  color: "#585858",
                  textWrap: "wrap",
                  width: "100%",
                }}
                onClick={() => toggleTextState((prev) => !prev)}
              >
                {" "}
                {textState ? "Show Less." : "Read More..."}
              </span>
            )}
          </span>
        </p>
      </div>
      <div className="flex mt-3">
        <div className="flex-grow-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Services
          </h1>
          <div className={!isMobile ? `${"flex py-2 px-4"}` : `${"flex "}`}>
            <div
              className={
                !isMobile
                  ? `${"h-min flex-grow-1 flex gap-2"}`
                  : `${"h-min flex-grow-1 flex gap-2 flex-column"}`
              }
            >
              {[
                { label: "Childcare", shouldAdd: childcare },
                {
                  label: interestedInTutoring() ? "Home Tutor" : "",
                  shouldAdd: tutoring,
                },
                { label: "Odd Jobs", shouldAdd: oddJobs },
              ].map((data, index) => {
                if (!data.shouldAdd) return null;
                return (
                  <div key={index} className="flex gap-1 align-items-center">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: !isMobile ? "14px" : "12px",
                        color: "#585858",
                      }}
                    >
                      {data.label}
                    </p>
                    <img src={check} alt="check" width="14px" height="13.07" />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className="flex flex-column align-items-center">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Nationality
          </h1>
          <div
            className="flex gap-2 justify-content-center align-items-center"
            style={{
              width: !isMobile ? "145.28px" : "max-content",
              height: "42.28px",
              borderRadius: "20px",
              backgroundColor: "#F1F1F1",
              paddingInline: isMobile && "10px",
            }}
          >
            <img src={earth} alt="earth" height="14.4px" width="14.4px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "12px",
                color: "#585858",
              }}
            >
              {nationality}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const SpecialNeedExperience = ({ text }: { text: string }) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 pb-6">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: !isMobile ? "20px" : "16px",
          color: "#585858",
        }}
      >
        Special Needs Experience
      </h1>
      <div className="py-2 px-3">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: "#585858",
            textWrap: "wrap",
            maxWidth: "612px",
          }}
        >
          <span
            className="m-0 p-0 inline-block text-left"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
              textWrap: "wrap",
              width: "100%",
            }}
          >
            <LimitedText
              className="m-0 p-0"
              text={text}
              limit={displayLimit}
              disableLimit={textState}
              style={{
                fontWeight: "400",
                fontSize: !isMobile ? "16px" : "14px",
                color: "#585858",
                textWrap: "wrap",
                width: "100%",
              }}
            />
            {text.length >= displayLimit && (
              <span
                className="cursor-pointer hover:text-gray-300"
                style={{
                  fontWeight: "400",
                  fontSize: "12px",
                  color: "#585858",
                  textWrap: "wrap",
                  width: "100%",
                }}
                onClick={() => toggleTextState((prev) => !prev)}
              >
                {" "}
                {textState ? "Show Less." : "Read More..."}
              </span>
            )}
          </span>
        </p>
      </div>
    </div>
  );
};
const Checks = ({ date1, date2 }: { date1: string; date2: string }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Checks
      </h1>
      <div className="flex gap-2 mt-2 mx-2">
        {!isMobile ? (
          <img src={checkStar} alt="check" height="23px" width="23px" />
        ) : (
          <img src={checkStar} alt="check" height="18px" width="18px" />
        )}
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "600",
            fontSize: !isMobile ? "16px" : "14px",
            color: "#585858",
          }}
        >
          Working With Children Check
        </p>
      </div>
      <p
        className="m-0 p-0 mt-2 mb-3"
        style={{
          fontWeight: "700",
          fontSize: "12px",
          color: "#179D52",
        }}
      >
        {`Verified on: ${date1} | Expires on: ${date2}`}
      </p>
    </div>
  );
};

const ChildcareQualifications = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Childcare Qualifications
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "600",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const FirstAid = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "20px",
          color: "#585858",
        }}
      >
        First Aid Accreditations
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2 align-items-center">
          <PiFirstAidKit />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const Transportation = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "20px",
          color: "#585858",
        }}
      >
        Transportation
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2 align-items-center">
          <TbLicense />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const AdditionalLanguages = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "20px",
          color: "#585858",
        }}
      >
        Additional Languages
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2 align-items-center">
          <IoLanguageOutline />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const TutoringQualifications = ({ data }: { data: string[] }) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: "400",
          fontSize: "16px",
          color: "#585858",
        }}
      >
        Tutoring Qualifications
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "600",
              fontSize: !isMobile ? "16px" : "14px",
              color: "#585858",
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};

const ReviewAndRatingHead = ({
  rating,
  ratingCount,
  isSuperHelper,
}: {
  rating: number;
  ratingCount: number;
  isSuperHelper: boolean;
}) => {
  const { isMobile } = useIsMobile();
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <div className="flex justify-content-between align-items-center">
        <div className="flex flex-column gap-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: !isMobile ? "20px" : "16px",
              color: "#585858",
            }}
          >
            Reviews
          </h1>
          <div className="flex gap-1">
            <img src={star} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "300",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
            </p>
          </div>
        </div>
        {isSuperHelper && (
          <div className="flex gap-2 align-items-center">
            <img src={rank} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "18px",
                color: "#585858",
              }}
            >
              Super Helper
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const ReviewAndRatingList = ({
  ratings,
}: {
  ratings: Array<{
    clientFirstName: string;
    clientLastName: string;
    feedback: string;
    ratingDate: string;
    ratingAvg: number;
    clientImageUrl: string;
  }>;
}) => {
  return (
    <div className="flex flex-column px-4 pt-2 mt-2 mb-4">
      {ratings.map((rating, index) => (
        <React.Fragment key={index}>
          <Divider />
          <div className="flex gap-2 my-2">
            <div
              style={{
                height: "38px",
                width: "38px",
                background: "gray",
                borderRadius: "50%",
                overflow: "hidden",
                minWidth:"38px"
              }}
            >
              <img
                src={rating.clientImageUrl}
                alt="client Image"
                width="100%"
                height="100%"
              />
            </div>
            <div className="flex-grow-1 flex flex-column gap-2">
              <div className="flex">
                <div className="flex-grow-1 flex flex-column">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "12px",
                      color: "#585858",
                    }}
                  >
                    {new Date(rating.ratingDate).toLocaleDateString("en-GB")}
                  </p>
                </div>
                <div className="flex gap-1">
                  <img src={star} alt="star" width="19.82px" height="18.62px" />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {`${rating.ratingAvg.toFixed(1)}`}
                  </p>
                </div>
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                {rating.feedback}
              </p>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

const ProfileTab: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
  const nat = c.countriesIso.find(
    (country) =>
      country.alpha2.toLowerCase() === helper?.nationality?.toLowerCase() ||
      country.value.toLowerCase() === helper?.nationality?.toLowerCase()
  );
  const { isMobile } = useIsMobile();
  return (
    <div
      style={{ width: isMobile && "100%" }}
      className={`${!isMobile ? "flex flex-column gap-4" : "flex flex-column gap-1" }`}
    >
      <Wrapper>
        <AboutSection
          text={helper?.aboutMe ?? ""}
          childcare={helper?.interestedInChildcareJobs ?? true}
          tutoring={helper?.interestedInTutoringJobs ?? true}
          oddJobs={helper?.interestedInOddJobs ?? true}
          nationality={nat?.label || "Unknown"}
          interestedInJobType={helper?.interestedInJobTypes}
        />
      </Wrapper>
      {(helper?.providerMyExperience2?.length ?? 0) > 0 && (
        <Wrapper>
          <SpecialNeedExperience text={helper?.providerMyExperience2 ?? ""} />
        </Wrapper>
      )}

      {(helper?.firstAid.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <>
          <Wrapper>
            {(helper?.certificates.length ?? 0) > 0}
            <FirstAid
              data={helper.firstAid
                .filter((val) => val.selected === true)
                .map((val) => val.text)}
            />
          </Wrapper>
        </>
      )}

      <Wrapper>
        {(helper?.certificates.length ?? 0) > 0 && (
          <>
            <Checks
              date1={new Date(
                helper.certificates[0].verificationDate
              ).toLocaleDateString("en-GB")}
              date2={new Date(
                helper.certificates[0].expiryDate
              ).toLocaleDateString("en-GB")}
            />
          </>
        )}
        {(helper?.qualifications.filter((x) => x.selected === true).length ??
          0) > 0 && (
          <>
            {(helper?.certificates.length ?? 0) > 0 && <Divider />}
            <ChildcareQualifications
              data={helper.qualifications
                .filter((val) => val.selected === true)
                .map((val) => val.text)}
            />
          </>
        )}
        {(helper?.tutoringQualifications.filter((x) => x.selected === true)
          .length ?? 0) > 0 && (
          <>
            {(helper?.qualifications.length ?? 0) > 0 && <Divider />}
            <TutoringQualifications
              data={helper.tutoringQualifications
                .filter((val) => val.selected === true)
                .map((val) => val.text)}
            />
          </>
        )}
      </Wrapper>

      {(helper?.transport.filter((x) => x.selected === true).length ?? 0) >
        0 && (
        <>
          <Wrapper>
            <Transportation
              data={helper.transport
                .filter((val) => val.selected === true)
                .map((val) => val.text)}
            />
          </Wrapper>
        </>
      )}
      {(helper?.spokenLanguages.filter((x) => x.selected === true).length ??
        0) > 0 && (
        <>
          <Wrapper>
            {(helper?.spokenLanguages.length ?? 0) > 0}
            <AdditionalLanguages
              data={helper.spokenLanguages
                .filter((val) => val.selected === true)
                .map((val) => val.text)}
            />
          </Wrapper>
        </>
      )}
       {helper?.hasVouches && (
      <Wrapper>
        <>
         
            <div className="px-4 pt-2 mt-2 mb-4">
              <div className="flex justify-content-between align-items-center">
                <div className="flex flex-column gap-1">
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: !isMobile ? "20px" : "16px",
                      color: "#585858",
                    }}
                  >
                    References
                  </h1>
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: !isMobile ? "16px" : "16px",
                      color: "#585858",
                    }}
                  >
                    Available on request. Please contact Customer Service to
                    obtain referee details.
                  </h1>
                </div>
              </div>
            </div>
       
        </>
      </Wrapper>
         )}
      {(helper?.ratingsExtended.length ?? 0) > 0 && (
        <Wrapper>
          <>
            <ReviewAndRatingHead
              rating={helper?.providerRatingsAvg ?? 0}
              ratingCount={helper?.providerRatingsCount ?? 0}
              isSuperHelper={helper?.isSuperProvider ?? false}
            />

            {(helper?.ratingsExtended.length ?? 0) > 0 && (
              <ReviewAndRatingList ratings={helper.ratingsExtended} />
            )}
          </>
        </Wrapper>
      )}
    </div>
  );
};

export default ProfileTab;
