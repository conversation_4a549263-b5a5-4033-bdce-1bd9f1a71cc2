import { PropsWithChildren, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { appInsights } from '../services/AppInsights';

const BaseLayout = (props: PropsWithChildren) => {
    const location = useLocation();

    useEffect(() => {
        appInsights?.trackPageView({ uri: location.pathname + location.search });
    }, [location]);
    return props.children;
};
export default BaseLayout;
