import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TimesheetDetailsPopup from '../../../containers/Common/payments/Common/TimesheetDetailsPopup';
import { TimesheetDetails } from '../../../hooks/useTimesheetDetails';

// Mock the hooks and services
jest.mock('../../../hooks/LoaderHook', () => ({
  __esModule: true,
  default: () => ({
    enableLoader: jest.fn(),
    disableLoader: jest.fn(),
  }),
}));

jest.mock('../../../services/services', () => ({
  __esModule: true,
  default: {
    postUpdateHistory: jest.fn(),
  },
}));

// Mock the AwaitingConfirmationCard component
jest.mock('../../../containers/Common/payments/Common/AwaitingConfirmationCard', () => {
  return function MockAwaitingConfirmationCard({ 
    initialTimesheetRows, 
    onSubmit, 
    onGoBack, 
    profileName 
  }: any) {
    const row = initialTimesheetRows[0];
    return (
      <div data-testid="awaiting-confirmation-card">
        <div>Profile: {profileName}</div>
        <div data-testid="start-time">Start: {row.start}</div>
        <div data-testid="end-time">End: {row.finish}</div>
        <div data-testid="hours">Hours: {row.hours}</div>
        <div data-testid="total">Total: ${row.total}</div>
        <button onClick={onSubmit} data-testid="submit-btn">Submit</button>
        <button onClick={onGoBack} data-testid="go-back-btn">Go Back</button>
      </div>
    );
  };
});

const mockTimesheetEntry = {
  id: 1,
  status: 'Awaiting Your Approval',
  type: 'One Off Job',
  date: '15 January 2024',
  location: '123 Test Street, Test City',
  userName: 'John D',
  originalImageUrl: 'test-image.jpg',
};

const mockTimesheetDetails: TimesheetDetails = {
  id: 1,
  firstName: 'John',
  lastName: 'D',
  originalImageUrl: 'test-image.jpg',
  jobType: 'One Off Job',
  jobDate: '2024-01-15',
  formattedAddress: '123 Test Street, Test City',
  price: 25,
  overtimeRate: 30,
  jobStartTime: '9:00 AM', // AM/PM format
  jobEndTime: '5:00 PM',   // AM/PM format
  status: 'pending',
  estimatedJobValue: 200,
  estimatedJobHours: 8,
  timesheetId: 1,
  jobId: 1,
  applicantId: 1,
  userId: 1,
};

describe('TimesheetDetailsPopup - Enhanced Time Handling', () => {
  const mockOnClose = jest.fn();
  const mockOnApprovalSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('displays times in AM/PM format', () => {
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 9:00 AM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 5:00 PM');
  });

  it('calculates hours correctly from AM/PM times', () => {
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // 9:00 AM to 5:00 PM = 8 hours
    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 8');
  });

  it('calculates total price correctly', () => {
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // 8 hours * $25/hour = $200
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $200');
  });

  it('handles 24-hour format conversion correctly', () => {
    const timesheetWith24Hour = {
      ...mockTimesheetDetails,
      jobStartTime: '09:00', // 24-hour format
      jobEndTime: '17:00',   // 24-hour format
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={timesheetWith24Hour}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // Should convert to AM/PM format
    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 9:00 AM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 5:00 PM');
  });

  it('handles midnight and noon times correctly', () => {
    const timesheetWithSpecialTimes = {
      ...mockTimesheetDetails,
      jobStartTime: '12:00 AM', // Midnight
      jobEndTime: '12:00 PM',   // Noon
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={timesheetWithSpecialTimes}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // 12:00 AM to 12:00 PM = 12 hours
    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 12');
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $300'); // 12 * 25
  });

  it('handles cross-day times correctly', () => {
    const timesheetWithCrossDay = {
      ...mockTimesheetDetails,
      jobStartTime: '11:00 PM',
      jobEndTime: '7:00 AM',
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={timesheetWithCrossDay}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // This should handle cross-day calculation
    // Note: The current implementation might need enhancement for cross-day scenarios
    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 11:00 PM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 7:00 AM');
  });

  it('updates calculations when times change', async () => {
    // This test would require the component to expose time editing functionality
    // For now, it verifies the initial state is correct
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 8');
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $200');
  });
});
