import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import TimesheetDetailsPopup from '../TimesheetDetailsPopup';
import { TimesheetDetails } from '../../../hooks/useTimesheetDetails';

// Mock the hooks and services
jest.mock('../../../hooks/LoaderHook', () => ({
  __esModule: true,
  default: () => ({
    enableLoader: jest.fn(),
    disableLoader: jest.fn(),
  }),
}));

jest.mock('../../../services/services', () => ({
  __esModule: true,
  default: {
    postUpdateHistory: jest.fn(),
  },
}));

// Mock the AwaitingConfirmationCard component to show the data it receives
jest.mock('../../../containers/Common/payments/Common/AwaitingConfirmationCard', () => {
  return function MockAwaitingConfirmationCard({ 
    initialTimesheetRows, 
    baseRate,
    profileName,
    onTimesheetRowsChange
  }: any) {
    const row = initialTimesheetRows[0];
    
    // Simulate time editing
    const handleEdit = () => {
      const updatedRows = [{
        ...row,
        start: '10:00 AM',
        finish: '6:00 PM',
        hours: 8,
        total: 8 * baseRate,
        isOriginal: false
      }];
      onTimesheetRowsChange?.(updatedRows);
    };
    
    return (
      <div data-testid="awaiting-confirmation-card">
        <div data-testid="profile-name">Profile: {profileName}</div>
        <div data-testid="base-rate">Rate: ${baseRate}</div>
        <div data-testid="start-time">Start: {row.start}</div>
        <div data-testid="end-time">End: {row.finish}</div>
        <div data-testid="hours">Hours: {row.hours}</div>
        <div data-testid="rate">Row Rate: ${row.rate}</div>
        <div data-testid="total">Total: ${row.total}</div>
        <button onClick={handleEdit} data-testid="edit-time-btn">Edit Time</button>
      </div>
    );
  };
});

const mockTimesheetEntry = {
  id: 1,
  status: 'Awaiting Your Approval',
  type: 'One Off Job',
  date: '15 January 2024',
  location: '123 Test Street, Test City',
  userName: 'John D',
  originalImageUrl: 'test-image.jpg',
};

describe('TimesheetDetailsPopup - Enhanced Features', () => {
  const mockOnClose = jest.fn();
  const mockOnApprovalSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows AM/PM times immediately on first load', () => {
    const mockTimesheetDetails: TimesheetDetails = {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      originalImageUrl: 'test-image.jpg',
      jobType: 'Babysitting',
      jobDate: '2024-01-15',
      formattedAddress: '123 Test Street, Test City',
      price: 25,
      overtimeRate: 30,
      jobStartTime: '9:00 AM',
      jobEndTime: '5:00 PM',
      status: 'pending',
      estimatedJobValue: 200,
      estimatedJobHours: 8,
      timesheetId: 1,
      jobId: 1,
      applicantId: 1,
      userId: 1,
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // Check that AM/PM times are displayed immediately
    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 9:00 AM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 5:00 PM');
    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 8');
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $200');
  });

  it('converts 24-hour format to AM/PM on first load', () => {
    const mockTimesheetDetails: TimesheetDetails = {
      id: 1,
      firstName: 'Jane',
      lastName: 'Smith',
      originalImageUrl: 'test-image.jpg',
      jobType: 'Tutoring',
      jobDate: '2024-01-16',
      formattedAddress: '456 Main Street',
      price: 30,
      overtimeRate: 35,
      jobStartTime: '14:00', // 24-hour format
      jobEndTime: '18:00',   // 24-hour format
      status: 'pending',
      estimatedJobValue: 120,
      estimatedJobHours: 4,
      timesheetId: 2,
      jobId: 2,
      applicantId: 2,
      userId: 2,
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // Check that 24-hour times are converted to AM/PM
    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 2:00 PM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 6:00 PM');
    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 4');
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $120');
  });

  it('handles midnight and noon correctly', () => {
    const mockTimesheetDetails: TimesheetDetails = {
      id: 3,
      firstName: 'Bob',
      lastName: 'Wilson',
      originalImageUrl: 'test-image.jpg',
      jobType: 'Night Shift',
      jobDate: '2024-01-17',
      formattedAddress: '789 Night Street',
      price: 35,
      overtimeRate: 40,
      jobStartTime: '00:00', // Midnight
      jobEndTime: '12:00',   // Noon
      status: 'pending',
      estimatedJobValue: 420,
      estimatedJobHours: 12,
      timesheetId: 3,
      jobId: 3,
      applicantId: 3,
      userId: 3,
    };

    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // Check midnight and noon conversion
    expect(screen.getByTestId('start-time')).toHaveTextContent('Start: 12:00 AM');
    expect(screen.getByTestId('end-time')).toHaveTextContent('End: 12:00 PM');
    expect(screen.getByTestId('hours')).toHaveTextContent('Hours: 12');
    expect(screen.getByTestId('total')).toHaveTextContent('Total: $420');
  });
});
