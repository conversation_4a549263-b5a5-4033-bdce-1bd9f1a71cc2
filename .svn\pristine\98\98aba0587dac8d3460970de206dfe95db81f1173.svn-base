import { useSelector } from "react-redux";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { RootState } from "../../../../store";
import useLoader from "../../../../hooks/LoaderHook";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import c from "../../../../helper/juggleStreetConstants";
import Service from "../../../../services/services";
import styles from "../../styles/review-and-post.module.css";
import { Dialog } from "primereact/dialog";
import { Tag } from "primereact/tag";
import calendar from "../../../../assets/images/Icons/Icon (1).png";
import clock from "../../../../assets/images/Icons/Vector.png";
import home from "../../../../assets/images/Icons/home-05.png";
import doller from "../../../../assets/images/Icons/Dollar.png";
import { Divider } from "primereact/divider";
import ProviderProfilePopup from "../../../Parent/ProviderProfile/ProviderProfilePopup";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import { GoBack } from "./Buttons";
import ProfileCard from "./JobSummary/InvitedCandidatesCard";
import { Toast } from "primereact/toast";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

function ReviewAndPost() {
  const { isMobile } = useIsMobile();
  return isMobile ? <ReviewAndPostMobile /> : <ReviewAndPostWeb />;
}

export default ReviewAndPost;
const useJobTypeHook = () => {
  const { payload, prev, setpayload } = useJobManager();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();
  const { isMobile } = useIsMobile();
  const toast = useRef(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const truncateText = (text, wordLimit) => {
    if (!text || typeof text !== "string") return ""; // Return empty string if text is null, undefined, or not a string
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };
  const wordLimit = 20;
  const navigate = useNavigate();
  const jobDate = payload.jobDate;
  const formattedDate = jobDate
    ? new Intl.DateTimeFormat("en-GB", {
        weekday: "short", // "Mon"
        day: "2-digit", // "31"
        month: "short", // "Mar"
        year: "2-digit", // "25"
      }).format(new Date(jobDate))
    : "Date not specified";

  const formattedDateMobile = jobDate
    ? new Intl.DateTimeFormat("en-GB", {
        weekday: "short", // "Mon"
        day: "2-digit", // "31"
        month: "short", // "Mar"
        year: "2-digit", // "25"
      }).format(new Date(jobDate))
    : "Date not specified";
  const startTime = payload.jobStartTime;
  const endTime = payload.jobEndTime;
  const clientType = utils.getCookie(CookiesConstant.clientType);

  const formatTime = (time: string | null | undefined) => {
    if (!time) {
      return ""; // or handle this case as needed, e.g., return a default time or throw an error
    }
    const [hours, minutes] = time.split(":");
    if (hours === undefined || minutes === undefined) {
      return ""; // handle the case where time format is incorrect
    }

    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));

    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    })
      .format(date)
      .toLocaleLowerCase();
  };
  const distanceMapping = {
    0: "Within 2.5km",
    1: "Within 5km",
    2: "Within 10km",
  };

  const ageMapping = {
    1: "16-17",
    2: "18-24",
    3: "25-44",
    4: "45+",
  };

  const otherSkillsMapping = {
    4: "Special Needs",
    9: "Driving Licence",
  };

  const tutoringCategoryMapping = {
    1: "Newbie",
    2: "Apprentice",
    3: "Experienced",
    4: "Professional",
  };

  const filteredApplicantFilters = payload?.applicantFilters?.filter(
    (filter) => filter.field === "age" || filter.field === "distance" || filter.field === "otherSkills" || filter.field === "tutoringCategory"
  );
  const [address, setAddress] = useState(null);
  const formattedStartTime = formatTime(startTime);
  const formattedEndTime = formatTime(endTime);
  const overtimeRate = payload.overtimeRate;
  const specialInstructions = payload.specialInstructions ?? "";
  const price = payload.price;
  const [visible, setVisible] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [showPopup, setShowPopup] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [applicantId, setApplicantId] = useState(null);

  const customHeader = !isMobile ? (
    <div style={{ color: "#585858", fontSize: "24px", fontWeight: "bold" }}>Important Information</div>
  ) : (
    <div style={{ color: "#585858", fontSize: "18px", fontWeight: "bold" }}>Important Information</div>
  );
  useEffect(() => {
    if (sessionInfo.loading) return;
    const temp = sessionInfo.data["addresses"].find((val) => val["id"] === payload.addressId);
    if (temp === undefined) return;
    setAddress(temp);
  }, [sessionInfo]);
  useEffect(() => {
    if (errorMessage && isMobile && toast.current) {
      toast.current.clear();
      toast.current.show({
        severity: "error",
        summary: null, // Remove the title
        detail: errorMessage,
        life: 3000,
        className: styles.customToast, // Reference module class
        contentClassName: styles.customToastContent,
      });
      setErrorMessage("");
    }
  }, [errorMessage, isMobile]);
  const footerContent = (
    <div>
      <button
        onClick={() => {
          setVisible(false);
          handlePostJobSubmit();
        }}
        autoFocus
        className={`${styles.okGotItBtn}`}
      >
        Ok,Got it
      </button>
    </div>
  );

  const handleJobError = (error: any) => {
    disableLoader();
    const errorMsg = error.response?.data?.message || error.message || "An unexpected error occurred while posting the job.";
    setErrorMessage(errorMsg);
  };

  const handlePostJobSubmit = () => {
    enableLoader();
    const omittedPayload = utils.omitProperties(payload, [
      "metadata",
      "jobFinishType",
      "jobStartType",
      "duration",
      "durationToStart",
      "durationType",
      "isPriceNegotiable",
      "jobStartDurationType",
      "selectedCandidates",
    ]);
    if (payload.actionType === c.jobActionType.EDIT_JOB) {
      Service.jobEditClient(
        (res) => {
          disableLoader();
          const newPram = new URLSearchParams();
          newPram.set("jobId", String(payload["id"]));
          newPram.set("managedBy", String(payload["managedBy"]));
          newPram.set("jobType", String(payload["jobType"]));
          newPram.set("activeTab", "0");
          newPram.set("show", [64, 128].includes(payload.jobType) ? "1" : "0");
          sessionStorage.removeItem("jobManagement");
          navigate({
            pathname: `/${Number(clientType) === 1 ? "parent-home" : "business-home"}/post-job/complete`,
            search: newPram.toString(),
          });
        },

        handleJobError,
        omittedPayload,
        payload["id"]
      );
    } else {
      Service.jobClient(
        omittedPayload,
        (res) => {
          disableLoader();
          const newPram = new URLSearchParams();
          newPram.set("jobId", String(res.id));
          newPram.set("managedBy", String(payload["managedBy"]));
          newPram.set("jobType", String(payload["jobType"]));
          newPram.set("show", [64, 128].includes(payload.jobType) ? "1" : "0");
          sessionStorage.removeItem("jobManagement");
          navigate({
            pathname: `/${Number(clientType) === 1 ? "parent-home" : "business-home"}/post-job/complete`,
            search: newPram.toString(),
          });
        },
        handleJobError
      );
    }
  };

  const getJobHours = () => {
    const jobStart = payload.jobStartTime;
    const jobEnd = payload.jobEndTime;
    const timeStringToMinutes = (timeString) => {
      if (!timeString || typeof timeString !== "string") return 0;
      const [hours, minutes, seconds] = timeString.split(":").map(Number);
      return hours * 60 + minutes + (seconds || 0) / 60;
    };
    const startMinutes = timeStringToMinutes(jobStart);
    const endMinutes = timeStringToMinutes(jobEnd);
    const differenceInMinutes =
      endMinutes === startMinutes
        ? 24 * 60 // Handle exact 24-hour case
        : endMinutes >= startMinutes
        ? endMinutes - startMinutes
        : 24 * 60 - (startMinutes - endMinutes);
    return differenceInMinutes / 60;
  };

  const handleImageClick = (candidate, id) => {
    setSelectedCandidate(candidate);
    setApplicantId(id);
    setShowPopup(true);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setSelectedCandidate(null);
    setApplicantId(null);
  };

  const getJobDetails = (jobclientProp) => {
    const jobToUse = jobclientProp || { jobType: 0 };
    switch (jobToUse.jobType) {
      case 256:
        return {
          label: "Odd Job",
        };
      case 64:
        return {
          label: "Primary School",
        };
      case 128:
        return {
          label: "High School",
        };
      default:
        return {
          label: "Childcare",
        };
    }
  };
  return {
    payload,
    prev,
    setpayload,
    isExpanded,
    setIsExpanded,
    truncateText,
    wordLimit,
    address,
    formattedStartTime,
    formattedEndTime,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    tutoringCategoryMapping,
    filteredApplicantFilters,
    customHeader,
    footerContent,
    formattedDate,
    isMobile,
    jobDate,
    clientType,
    disableLoader,
    enableLoader,
    overtimeRate,
    specialInstructions,
    price,
    visible,
    setVisible,
    errorMessage,
    setErrorMessage,
    handlePostJobSubmit,
    getJobHours,
    showPopup,
    setShowPopup,
    selectedCandidate,
    setSelectedCandidate,
    handleImageClick,
    handleClosePopup,
    applicantId,
    setApplicantId,
    toast,
    getJobDetails,
    formattedDateMobile,
  };
};

const ReviewAndPostWeb = () => {
  const {
    prev,
    setpayload,
    isExpanded,
    setIsExpanded,
    truncateText,
    wordLimit,
    address,
    formattedStartTime,
    formattedEndTime,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    tutoringCategoryMapping,
    filteredApplicantFilters,
    customHeader,
    footerContent,
    formattedDate,
    payload,
    overtimeRate,
    specialInstructions,
    price,
    visible,
    setVisible,
    errorMessage,
    getJobHours,
    showPopup,
    handleImageClick,
    handleClosePopup,
    applicantId,
  } = useJobTypeHook();
  return (
    <div>
      <div className={`h-full overflow-hidden overflow-y-auto absolute`} style={{ width: "70%" }}>
        <div className={styles.reviewPost}>
          <h2 className={`${styles.headerH2} p-0 m-0 mb-3`}>Your Job is ready to go!</h2>
          <div className={`${styles.jobSummary} `}>
            <h3 className="font-bold" style={{ color: "#585858", fontSize: "30px" }}>
              Job Summary
            </h3>
            <div className={styles.tagButton}>
              <Tag style={{ background: "#2F9ACD" }}>
                <div className={styles.tagData}>
                  <img alt="Calendar" src={calendar} style={{ width: "18px", color: "#FFFFFF" }} />
                  {formattedDate}
                </div>
              </Tag>
              <Tag style={{ background: "#8577DB" }}>
                <div className={styles.tagData}>
                  <img alt="Clock" src={clock} style={{ width: "18px", color: "#FFFFFF" }} />
                  {formattedStartTime}-{formattedEndTime}
                </div>
              </Tag>
              <Tag style={{ background: "#179D52" }}>
                <div className={styles.tagData}>
                  <img alt="Home" src={home} style={{ width: "18px", color: "#FFFFFF" }} />
                  {address !== null && utils.cleanAddress(address["formattedAddress"])}
                </div>
              </Tag>
              {!!overtimeRate && (
                <Tag style={{ background: "#77DBC9" }}>
                  <div className={styles.tagData}>
                    <img alt="Doller" src={doller} style={{ width: "10px", color: "#FFFFFF" }} />
                    Hourly Rate: ${price} Per Hour
                  </div>
                </Tag>
              )}
            </div>
            <div>
              {filteredApplicantFilters?.length > 0 && (
                <div>
                  <h4 className={`${styles.headerH4} mb-1`}>Candidate Criteria</h4>
                  <div>
                    {filteredApplicantFilters.map((filter, index) => {
                      let displayValue = "";
                      let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                      if (filter.field === "distance") {
                        displayValue = distanceMapping[filter.value];
                      } else if (filter.field === "age") {
                        if (filter.value === 0) {
                          displayValue = Object.values(ageMapping).join(", ");
                        } else {
                          displayValue = (filter.value as number[]).map((val) => ageMapping[val]).join(", ");
                        }
                      } else if (filter.field === "otherSkills") {
                        displayValue = (filter.value as number[]).map((val) => otherSkillsMapping[val]).join(", ");
                        displayField = "Other";
                      } else if (filter.field === "tutoringCategory") {
                        displayValue = (filter.value as number[]).map((val) => tutoringCategoryMapping[val]).join(", ");
                        displayField = "Experience";
                      }
                      return (
                        <div key={index} className={styles.criteriaItem}>
                          <span className={styles.criteriaLabel}>{displayField}:&nbsp;</span>
                          <span className={styles.criteriaValue}>{displayValue}</span>
                        </div>
                      );
                    })}
                  </div>
                  <Divider className="mt-4" />
                </div>
              )}
            </div>
            <div>
              <h4 className={styles.headerH4}>Job Description</h4>
              <p className={styles.yourJobDescriptionMobile}>{isExpanded ? specialInstructions : truncateText(specialInstructions, wordLimit)}</p>
              {specialInstructions.split(" ").length > wordLimit && (
                <button className={styles.seeMoreButton} onClick={() => setIsExpanded(!isExpanded)}>
                  {isExpanded ? "See Less" : "See More"}
                </button>
              )}
            </div>

            <Divider className="mt-4" />
            <div className={styles.jobPrice}>
              <p style={{ textAlign: "end" }}>
                Job Price ={" "}
                <strong className={styles.jobPriceDoller}>
                  ${payload.paymentType === 2 ? ((price * getJobHours()) as number).toFixed(2) : price?.toFixed(2)}
                </strong>{" "}
              </p>
              {!!overtimeRate && (
                <p className={`${styles.jobPriceLable} mt-0`}>
                  Overtime =<strong className={styles.jobOvertime}>${overtimeRate} Per Hour</strong>
                </p>
              )}
            </div>
          </div>

          <div style={{ width: "75%" }}>
            {payload.managedBy === 1 && (
              <div>
                <h3 className="font-bold p-0 m-0 mt-3 mb-3" style={{ color: "#585858", fontSize: "30px" }}>
                  Invited Candidates <strong style={{ color: "#197D52" }}> ({payload?.["selectedCandidates"]?.length || 0})</strong>
                </h3>

                <div className="flex flex-wrap gap-2">
                  {payload?.["selectedCandidates"]?.map((profile) => {
                    const applicant = payload.applicants.find((app) => app.applicantId === profile.id);
                    const applicantId = applicant ? applicant.applicantId : null;

                    return (
                      <ProfileCard
                        key={profile.id}
                        name={profile.publicName}
                        location={profile.suburb || "Location not available"}
                        jobsCompleted={profile.jobsCompleted}
                        imageUrl={profile.imageSrc || "default_image.jpg"}
                        isSelected={true} // Always true as per requirement
                        onClick={() => handleImageClick(profile, applicantId)} // Pass the applicantId here
                      />
                    );
                  })}
                  <Dialog
                    visible={showPopup}
                    style={{ width: "auto" }}
                    // footer={<Button label="Close" icon="pi pi-times" onClick={handleClosePopup} />}
                    onHide={handleClosePopup}
                    draggable={false}
                  >
                    <ProviderProfilePopup candidateId={applicantId} requestId={0} />
                  </Dialog>
                </div>
              </div>
            )}
          </div>
          {/* <button className={styles.postJobBtn}>Post Job</button> */}
          <Dialog
            header={customHeader}
            visible={visible}
            style={{ width: "50vw", color: "#585858" }}
            onHide={() => {
              if (!visible) return;
              setVisible(false);
            }}
            footer={footerContent}
            draggable={false}
          >
            <p className="m-0 p-0">
              <>
                {(() => {
                  const jobTypeOneOff = [1, 256];
                  if (payload.managedBy === 1) {
                    if (jobTypeOneOff.includes(payload.jobType)) {
                      return (
                        <div style={{ color: "#585858", fontSize: "16px" }}>
                          This job can be active for up to 14 days. During this time you will be able to chat online with invited candidates. If you
                          don't find the right person quickly, you can change the job details and invite more candidates to apply. When a candidate
                          applies for your job, they will be waiting to hear from you! Make sure to confirm your chosen candidate by clicking the
                          AWARD button.
                        </div>
                      );
                    }
                    return (
                      <div style={{ color: "#585858", fontSize: "16px" }}>
                        This recurring job post will be active for 30 days, during this time you will be able to chat online with invited candidates.
                        If you don’t find the right person right away you can invite more candidates to apply, change the job details and repost the
                        job. Remember, when a candidate applies for your job they will be waiting to hear from you!
                      </div>
                    );
                  } else {
                    if (jobTypeOneOff.includes(payload.jobType)) {
                      return (
                        <div style={{ color: "#585858", fontSize: "16px" }}>
                          This job can be active for up to 14 days. The Juggle Assist algorithm will now invite candidates who meet your criteria and
                          invite them to your job. You will be notified each time a candidate applies for this job – these applicants will appear in
                          your job shortlist
                        </div>
                      );
                    }
                    return (
                      <div style={{ color: "#585858", fontSize: "16px" }}>
                        The Juggle Assist algorithm will now invite candidates who meet your criteria and invite them to your job. You will be
                        notified each time a candidate applies for this job – these applicants will appear in your job shortlist.
                      </div>
                    );
                  }
                })()}
              </>
            </p>
          </Dialog>
          <button className={`${styles.postJobBtn} mt-5`} onClick={() => setVisible(true)}>
            Post Job
          </button>
          {/* <p className={styles.goBack} onClick={() => prevClicked(currentPayload)}>
                    <span className='pi pi-angle-left'></span>Go Back
                </p> */}
          <GoBack
            onClick={() => {
              setpayload({
                ...payload,
              });
              if (payload.managedBy === 20) {
                prev("candidate-matching");
              } else {
                prev("candidate-selection");
              }
            }}
          />
          {errorMessage && <div className={styles.errorMessage}>{errorMessage}</div>}
        </div>
      </div>
    </div>
  );
};
const ReviewAndPostMobile = () => {
  const {
    prev,
    setpayload,
    isExpanded,
    setIsExpanded,
    truncateText,
    wordLimit,
    address,
    formattedStartTime,
    formattedEndTime,
    customHeader,
    footerContent,
    payload,
    overtimeRate,
    specialInstructions,
    price,
    visible,
    setVisible,
    formattedDateMobile,
    filteredApplicantFilters,
    distanceMapping,
    ageMapping,
    otherSkillsMapping,
    tutoringCategoryMapping,
    getJobHours,
    toast,
    getJobDetails,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        height: "100%",
      }}
    >
      <Toast ref={toast} position="bottom-center" /> {/* Add Toast here */}
      <div className={styles.containerMobile}>
        <div className={styles.reviewPostMobile}>
          <h2 className={styles.headerH2Mobile}>Job Summary</h2>
          <div className={styles.jobDetailsPlain}>
            <div>
              {![64, 128].includes(payload.jobType) && ![2, 4, 8, 16].includes(payload.jobType) && (
                <div>
                  <div style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }}>
                    {`${getJobDetails(payload).label} - ${
                      [64, 128].includes(payload.jobType) ? "Tutoring Job" : [2, 4, 8, 16].includes(payload.jobType) ? "Recurring Job" : "One Off Job"
                    }`}
                  </div>
                </div>
              )}
            </div>
            <div className={styles.jobDetailItem}>
              <span className={styles.detailValue}>{formattedDateMobile}</span>
              {","}
              <span className={styles.detailValue}>
                {formattedStartTime}-{formattedEndTime}
              </span>
            </div>

            <div className="flex flex-column">
              <p className={styles.addressTag}>{payload.addressLabel}</p>

              <span className={styles.detailValue}>{address !== null && utils.cleanAddress(address["formattedAddress"])}</span>
            </div>

            <div>
              <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="m-0 p-0 mt-2">
                Payment
              </p>
            </div>
            {payload.paymentType !== 1 && (
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Price:</span>
                <span className={styles.detailValue}>${price} p/h</span>
              </div>
            )}
            {!!overtimeRate && payload.paymentType !== 1 && (
              <div className={styles.jobDetailItem}>
                <span className={styles.detailValue}>Extra hours rate:</span>
                <span className={styles.detailValue}>${overtimeRate} per hour</span>
              </div>
            )}
            <div className={styles.jobDetailItem}>
              <span className={styles.detailValue}>Payment Method: {payload.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer"} </span>
            </div>
            {payload.paymentType !== 1 ? (
              <div>
                <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="m-0 p-0 mt-2">
                  Total Job
                </p>
                <span>
                  ${((price * getJobHours()) as number).toFixed(2)} at ${payload.price} per hour
                </span>
              </div>
            ) : (
              <div>
                <p style={{ fontSize: "16px", color: "#585858", fontWeight: "700" }} className="m-0 p-0 mt-2">
                  Total Job
                </p>
                <span>{`$${price} fixed price for ${getJobHours()} hours`}</span>
              </div>
            )}
            <div>
              {filteredApplicantFilters?.length > 0 && (
                <div>
                  <h4 className={`${styles.headerH4} mb-1`}>Candidate Criteria</h4>
                  <div>
                    {filteredApplicantFilters.map((filter, index) => {
                      let displayValue = "";
                      let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                      if (filter.field === "distance") {
                        displayValue = distanceMapping[filter.value];
                      } else if (filter.field === "age") {
                        if (filter.value === 0) {
                          displayValue = Object.values(ageMapping).join(", ");
                        } else {
                          displayValue = (filter.value as number[]).map((val) => ageMapping[val]).join(", ");
                        }
                      } else if (filter.field === "otherSkills") {
                        displayValue = (filter.value as number[]).map((val) => otherSkillsMapping[val]).join(", ");
                        displayField = "Other";
                      } else if (filter.field === "tutoringCategory") {
                        displayValue = (filter.value as number[]).map((val) => tutoringCategoryMapping[val]).join(", ");
                        displayField = "Experience";
                      }
                      return (
                        <div key={index} className={styles.criteriaItem}>
                          <span className={styles.criteriaLabel}>{displayField}:&nbsp;</span>
                          <span className={styles.criteriaValue}>{displayValue}</span>
                        </div>
                      );
                    })}
                  </div>
                  <Divider className="mt-4" />
                </div>
              )}
            </div>
          </div>

          <div style={{ backgroundColor: "#BBBBBB33", borderRadius: "20px", paddingInline: "20px", paddingBlock: "10px" }}>
            <h4 className="font-bold p-0 m-0 ">Job Description</h4>
            <p style={{ marginTop: "5px" }} className={styles.yourJobDescriptionMobile}>
              {isExpanded ? specialInstructions : truncateText(specialInstructions, wordLimit)}
            </p>
            {specialInstructions.split(" ").length > wordLimit && (
              <button className={styles.seeMoreButton} onClick={() => setIsExpanded(!isExpanded)}>
                {isExpanded ? "See Less" : "See More"}
              </button>
            )}
          </div>
          <Divider className="mt-4" />

          <div style={{ width: "100%", maxHeight: "385px" }}>
            {payload.managedBy === 1 && (
              <div>
                <h3 className="font-bold" style={{ color: "#585858", fontSize: "24px" }}>
                  Invited Candidates <strong style={{ color: "#197D52" }}> ({payload?.["selectedCandidates"]?.length || 0})</strong>
                </h3>

                <div className="flex  flex-wrap gap-2">
                  {payload?.["selectedCandidates"]?.map((profile) => (
                    <ProfileCard
                      key={profile.id}
                      name={profile.publicName}
                      location={profile.suburb || "Location not available"}
                      jobsCompleted={profile.jobsCompleted}
                      imageUrl={profile.imageSrc || "default_image.jpg"}
                      isSelected={true} // Always true as per requirement
                    />
                  ))}
                </div>
                {/* {errorMessage && <div className={styles.errorMessage}>{errorMessage}</div>} */}
              </div>
            )}
          </div>
          {/* <button className={styles.postJobBtn}>Post Job</button> */}
          <Dialog
            header={customHeader}
            visible={visible}
            style={{ width: "100%", color: "#585858", padding: "20px" }}
            onHide={() => {
              if (!visible) return;
              setVisible(false);
            }}
            footer={footerContent}
            draggable={false}
          >
            <p className="m-0 p-0">
              <>
                {(() => {
                  const jobTypeOneOff = [1, 256];
                  if (payload.managedBy === 1) {
                    if (jobTypeOneOff.includes(payload.jobType)) {
                      return (
                        <div style={{ color: "#585858", fontSize: "14px" }}>
                          This job can be active for up to 14 days. During this time you will be able to chat online with invited candidates. If you
                          don't find the right person quickly, you can change the job details and invite more candidates to apply. When a candidate
                          applies for your job, they will be waiting to hear from you! Make sure to confirm your chosen candidate by clicking the
                          AWARD button.
                        </div>
                      );
                    }
                    return (
                      <div style={{ color: "#585858", fontSize: "14px" }}>
                        This recurring job post will be active for 30 days, during this time you will be able to chat online with invited candidates.
                        If you don’t find the right person right away you can invite more candidates to apply, change the job details and repost the
                        job. Remember, when a candidate applies for your job they will be waiting to hear from you!
                      </div>
                    );
                  } else {
                    if (jobTypeOneOff.includes(payload.jobType)) {
                      return (
                        <div style={{ color: "#585858", fontSize: "14px" }}>
                          This job can be active for up to 14 days. The Juggle Assist algorithm will now invite candidates who meet your criteria and
                          invite them to your job. You will be notified each time a candidate applies for this job – these applicants will appear in
                          your job shortlist
                        </div>
                      );
                    }
                    return (
                      <div style={{ color: "#585858", fontSize: "14px" }}>
                        The Juggle Assist algorithm will now invite candidates who meet your criteria and invite them to your job. You will be
                        notified each time a candidate applies for this job – these applicants will appear in your job shortlist.
                      </div>
                    );
                  }
                })()}
              </>
            </p>
          </Dialog>

          {/* <p
            className={styles.goBack}
            onClick={() => prevClicked(currentPayload)}
          >
            <span className="pi pi-angle-left"></span>Go Back
          </p> */}
        </div>

        <div className={styles.fixedFooter}>
          <div className={styles.jobPriceMobile}>
            {/* <div
                        style={{
                            margin: '0px',
                            fontSize: '20px',
                            color: '#585858',
                            fontWeight: '700',
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'baseline',
                            gap: '5px',
                            textDecoration: 'underline',
                        }}
                    >
                        <strong className={styles.jobPriceDoller}>
                            ${((price * getJobHours()) as number).toFixed(2)}
                        </strong>{' '}
                        <span
                            style={{
                                fontSize: '16px',
                                fontWeight: '300',
                                color: '#585858',
                            }}
                        >
                            {' '}
                            total
                        </span>
                    </div> */}
            {/* {!!overtimeRate && (
                        <div
                            className={`${styles.jobPriceLable} mt-0`}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                gap: '5px',
                                textDecoration: 'underline',
                                alignItems: 'baseline',
                            }}
                        >
                            <strong
                                className={styles.jobOvertime}
                                style={{
                                    fontSize: '20px',
                                    fontWeight: '700',
                                    color: '#585858',
                                }}
                            >
                                ${overtimeRate} P/H
                            </strong>
                            <span
                                style={{
                                    fontSize: '16px',
                                    fontWeight: '300',
                                    color: '#585858',
                                    marginTop: '2px',
                                }}
                            >
                                overtime
                            </span>
                        </div>
                    )} */}
          </div>

          <BackButtonPortal id="back-button-portal">
            <div
              onClick={() => {
                setpayload(payload);
                if (payload.managedBy === 1) {
                  prev("candidate-selection");
                } else {
                  prev("candidate-matching");
                }
              }}
              style={{ width: "35px", height: "25px" }} // Force width & height in inline style
            >
              <img
                src={SideArrow}
                alt="cross"
                width={13}
                height={20}
                className="cursor-pointer"
                style={{ display: "block", objectFit: "contain" }} // Important for iOS
              />
            </div>
          </BackButtonPortal>

          <button className={`${styles.nextButtonMobile} `} onClick={() => setVisible(true)}>
            Post Job
          </button>
        </div>
      </div>
    </div>
  );
};
