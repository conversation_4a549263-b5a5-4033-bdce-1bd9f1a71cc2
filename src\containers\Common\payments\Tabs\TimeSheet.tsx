import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import { Badge } from "primereact/badge";
import { useTimesheetData, TimesheetEntry } from "../../../../hooks/useTimesheetData";
import { useTimesheetDetails } from "../../../../hooks/useTimesheetDetails";
import TimesheetDetailsPopup from "../../../../components/timesheet/TimesheetDetailsPopup";

interface TimeSheetProps {
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;
}

const TimeSheet: React.FC<TimeSheetProps> = ({ lightGrayText, screenBg }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Map URL paths to tab indices
  const tabRoutes = {
    'awaiting-confirmation': 0,
    'adjusted-timesheets': 1,
    'awaiting-approval': 2
  };

  const routeToTab = {
    0: 'awaiting-confirmation',
    1: 'adjusted-timesheets',
    2: 'awaiting-approval'
  };

  // Get current tab index from URL
  const getCurrentTabIndex = () => {
    const currentPath = location.pathname.split('/').pop();
    return tabRoutes[currentPath as keyof typeof tabRoutes] ?? 0;
  };

  const [activeTabIndex, setActiveTabIndex] = useState(getCurrentTabIndex());
  const [selectedEntry, setSelectedEntry] = useState<TimesheetEntry | null>(null);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;

  // Use custom hooks
  const { timesheetData, refreshData } = useTimesheetData();
  const { timesheetDetails, fetchTimesheetDetails, clearTimesheetDetails } = useTimesheetDetails();
  
  // Update tab index when URL changes
  useEffect(() => {
    setActiveTabIndex(getCurrentTabIndex());
  }, [location.pathname]);

  console.log('timesheetDetails', timesheetDetails)

  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };

  // Handle tab change with navigation
  const handleTabChange = (e: { index: number }) => {
    console.log('Tab change triggered:', e.index); // Debug log
    const newRoute = routeToTab[e.index as keyof typeof routeToTab];
    console.log('Navigating to:', `/parent-home/timesheet/${newRoute}`); // Debug log
    navigate(`/parent-home/timesheet/${newRoute}`);
  };

  const handleReview = (entry: TimesheetEntry) => {
    setSelectedEntry(entry);
    fetchTimesheetDetails(entry.id);
    // The timesheet rows will be extracted from timesheetDetails.weeklyScheduleEntries
  };

  const closePopup = () => {
    setSelectedEntry(null);
    clearTimesheetDetails();
    // Timesheet rows are now part of timesheetDetails, so they'll be cleared automatically
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.4',
        position: 'relative',
      }}
    >
      <span
        dangerouslySetInnerHTML={{ __html: title }}
        style={{ textAlign: 'center' }}
      />
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            display: "flex",
            height: '15px',
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable timesheet list component
  const TimesheetList = ({ data }: { data: TimesheetEntry[] }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            userImage={entry.originalImageUrl}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: lightGrayText,
          backgroundColor: screenBg
        }}>
          No timesheets available
        </div>
      )}
    </>
  );

  const handleApprovalSuccess = () => {
    // Refresh data after successful approval
    if (timesheetDetails?.id) {
      fetchTimesheetDetails(timesheetDetails.id);
    }
  };

  return (
    <>
      <TabView
        activeIndex={activeTabIndex}
        onTabChange={handleTabChange}
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.awaitingConfirmation} count={timesheetData.length} />}>
          <TimesheetList data={timesheetData} />
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.adjustedTimesheets} count={2} />}>
          <div>Adjusted Timesheets Content</div>
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.awaitingApproval} count={1} />}>
          <div>Awaiting Approval Content</div>
        </TabPanel>
      </TabView>
      <TimesheetDetailsPopup
        selectedEntry={selectedEntry}
        timesheetDetails={timesheetDetails}
        timesheetRows={timesheetDetails?.weeklyScheduleEntries || []} // Use extracted timesheet rows
        onClose={closePopup}
        onApprovalSuccess={handleApprovalSuccess}
      />
    </>
  );
};

export default TimeSheet;