import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import { Badge } from "primereact/badge";
import { useTimesheetData, TimesheetEntry } from "../../../../hooks/useTimesheetData";
import { useTimesheetDetails } from "../../../../hooks/useTimesheetDetails";
import TimesheetDetailsPopup from "../../../../components/timesheet/TimesheetDetailsPopup";

interface TimeSheetProps {
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;
}

const TimeSheet: React.FC<TimeSheetProps> = ({ lightGrayText, screenBg }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Map URL paths to tab indices
  const tabRoutes = {
    'awaiting-confirmation': 0,
    'adjusted-timesheets': 1,
    'awaiting-approval': 2
  };

  const routeToTab = {
    0: 'awaiting-confirmation',
    1: 'adjusted-timesheets',
    2: 'awaiting-approval'
  };

  // Get current tab index from URL
  const getCurrentTabIndex = () => {
    const currentPath = location.pathname.split('/').pop();
    return tabRoutes[currentPath as keyof typeof tabRoutes] ?? 0;
  };

  const [activeTabIndex, setActiveTabIndex] = useState(getCurrentTabIndex());
  const [selectedEntry, setSelectedEntry] = useState<TimesheetEntry | null>(null);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;

  // Use custom hooks
  const { timesheetData, refreshData } = useTimesheetData();
  const { timesheetDetails, fetchTimesheetDetails, clearTimesheetDetails } = useTimesheetDetails();
  
  // Update tab index when URL changes
  useEffect(() => {
    setActiveTabIndex(getCurrentTabIndex());
  }, [location.pathname]);

  console.log('timesheetDetails', timesheetDetails)

  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };

  // Handle tab change with navigation
  const handleTabChange = (e: { index: number }) => {
    console.log('Tab change triggered:', e.index); // Debug log
    const newRoute = routeToTab[e.index as keyof typeof routeToTab];
    console.log('Navigating to:', `/parent-home/timesheet/${newRoute}`); // Debug log
    navigate(`/parent-home/timesheet/${newRoute}`);
  };

  // State for multiple timesheet rows
  const [timesheetRows, setTimesheetRows] = useState<any[]>([]);

  const handleReview = (entry: TimesheetEntry) => {
    setSelectedEntry(entry);
    fetchTimesheetDetails(entry.id);
    // Also fetch multiple timesheet rows for this entry
    fetchTimesheetRows(entry.id);
  };

  // Function to fetch multiple timesheet rows
  const fetchTimesheetRows = async (timesheetId: number) => {
    try {
      console.log('Fetching timesheet rows for ID:', timesheetId);

      // Option 1: If you have a separate API endpoint for multiple timesheet rows
      // Uncomment and modify this if you have such an API:
      /*
      Service.getTimesheetRows(
        (response: any) => {
          console.log('Multiple timesheet rows response:', response);
          const rows = Array.isArray(response) ? response : response?.data || [];
          setTimesheetRows(rows);
        },
        (error: any) => {
          console.error('Error fetching timesheet rows:', error);
          setTimesheetRows([]);
        },
        timesheetId
      );
      */

      // Option 2: Create multiple rows from sample data (for demonstration)
      // Replace this with your actual data source
      const sampleRows = [
        {
          start: "09:00",      // Morning shift
          finish: "12:00",
          hours: 3,
          rate: 25,
          total: 75,
          id: 1
        },
        {
          start: "13:00",      // Afternoon shift
          finish: "17:00",
          hours: 4,
          rate: 25,
          total: 100,
          id: 2
        },
        {
          start: "18:00",      // Evening shift
          finish: "21:00",
          hours: 3,
          rate: 30,           // Higher evening rate
          total: 90,
          id: 3
        }
      ];

      setTimesheetRows(sampleRows);
      console.log('Multiple timesheet rows set:', sampleRows);

    } catch (error) {
      console.error('Error fetching timesheet rows:', error);
      setTimesheetRows([]);
    }
  };

  const closePopup = () => {
    setSelectedEntry(null);
    clearTimesheetDetails();
    setTimesheetRows([]); // Clear timesheet rows when closing popup
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.4',
        position: 'relative',
      }}
    >
      <span
        dangerouslySetInnerHTML={{ __html: title }}
        style={{ textAlign: 'center' }}
      />
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            display: "flex",
            height: '15px',
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable timesheet list component
  const TimesheetList = ({ data }: { data: TimesheetEntry[] }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            userImage={entry.originalImageUrl}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: lightGrayText,
          backgroundColor: screenBg
        }}>
          No timesheets available
        </div>
      )}
    </>
  );

  const handleApprovalSuccess = () => {
    // Refresh data after successful approval
    if (timesheetDetails?.id) {
      fetchTimesheetDetails(timesheetDetails.id);
    }
  };

  return (
    <>
      <TabView
        activeIndex={activeTabIndex}
        onTabChange={handleTabChange}
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.awaitingConfirmation} count={timesheetData.length} />}>
          <TimesheetList data={timesheetData} />
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.adjustedTimesheets} count={2} />}>
          <div>Adjusted Timesheets Content</div>
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.awaitingApproval} count={1} />}>
          <div>Awaiting Approval Content</div>
        </TabPanel>
      </TabView>
      <TimesheetDetailsPopup
        selectedEntry={selectedEntry}
        timesheetDetails={timesheetDetails}
        timesheetRows={timesheetRows} // Pass the multiple timesheet rows
        onClose={closePopup}
        onApprovalSuccess={handleApprovalSuccess}
      />
    </>
  );
};

export default TimeSheet;