import { <PERSON>b<PERSON><PERSON><PERSON>, Tab<PERSON>iew } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useEffect, useState } from "react";
import AwaitingConfirmationCard from "../Common/AwaitingConfirmationCard";
import { useNavigate } from 'react-router-dom';
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import ProfileImage from "../../../../assets/images/Icons/my_child.png";
import { Badge } from "primereact/badge";
import Service from "../../../../services/services";
import useLoader from "../../../../hooks/LoaderHook";
import environment from "../../../../helper/environment";

interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  onReview?: () => void;
}

interface TimeSheetProps {
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;
}
interface TimesheetDetails {
  id?: number;
  firstName?: string;
  lastName?: string;
  originalImageUrl?: string;
  jobType?: string;
  jobDate?: string;
  formattedAddress?: string;
  price?: number;
  overtimeRate?: number;
  jobEndTime?: string;
  jobStartTime?: any;
  status?: string;
  estimatedJobValue?: number;
  estimatedJobHours?: number;
}
const TimeSheet: React.FC<TimeSheetProps> = ({ lightGrayText, screenBg }) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [selectedEntry, setSelectedEntry] = useState<TimesheetEntry | null>(null);
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const { disableLoader, enableLoader } = useLoader();
  // Sample data - replace with actual data from props or API
  const [timesheetData, setTimesheetData] = useState<TimesheetEntry[]>([]);
  const [timesheetDetails, setTimesheetDetails] = useState<TimesheetDetails | null>(null);
  console.log('timesheetDetails', timesheetDetails)
  const headers = {
    awaitingConfirmation: isParent ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: isParent ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: isParent ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };
  const statusMap: { [key: number]: string } = {
    [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
  };
  const jobTypeMap: { [key: number]: string } = {
    [c.jobType.UNSPECIFIED]: "Unspecified",
    [c.jobType.BABYSITTING]: "One Of Job",
    [c.jobType.NANNYING]: "Recurring Job",
    [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
    [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
    [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
    [c.jobType.AU_PAIR]: "Au Pair",
    [c.jobType.HOME_TUTORING]: "Home Tutoring",
    [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
    [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
    [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
  };
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };
  function calculateHours(startTime: string, endTime: string): number {
    if (!startTime || !endTime) return 0;

    const start = new Date(`1970-01-01T${startTime}`);
    const end = new Date(`1970-01-01T${endTime}`);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      console.warn("Invalid time format", { startTime, endTime });
      return 0;
    }

    const diffMs = end.getTime() - start.getTime();
    const hours = diffMs / (1000 * 60 * 60);
    return parseFloat(hours.toFixed(2));
  }

  function calculateTotalPrice(hours: number, rate: number): number {
    if (isNaN(hours) || isNaN(rate)) return 0;
    return parseFloat((hours * rate).toFixed(2));
  }
  function convertTo24Hour(timeStr: string): string {
    const [time, modifier] = timeStr.split(" ");
    if (!time || !modifier) return timeStr; // fallback
    let [hours, minutes] = time.split(":").map(Number);

    if (modifier === "PM" && hours < 12) {
      hours += 12;
    }
    if (modifier === "AM" && hours === 12) {
      hours = 0;
    }

    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
  }

  useEffect(() => {
    enableLoader();
    Service.getTimeSheet(
      (response: any[]) => {
        console.log("API Response:", response);
        const mappedData: TimesheetEntry[] = response.map((item) => ({
          id: item.id,
          status: statusMap[item.status] || "Unknown",
          type: jobTypeMap[item.jobType] || "Unknown",
          date: new Date(item.jobDate).toLocaleDateString('en-AU', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
          location: item.formattedAddress,
          userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
          originalImageUrl: item.originalImageUrl,
        }));

        setTimesheetData(mappedData);
        disableLoader();
      },
      (error: any) => {
        console.error('Error fetching data:', error);
        disableLoader();
      },);
  }, []);

  const handleReview = (entry: TimesheetEntry) => {
    setSelectedEntry(entry);
    fetchTimesheetDetails(entry.id);
  };

  const fetchTimesheetDetails = (timesheetId: number) => {
    console.log("Fetching timesheet details for ID:", timesheetId);
    enableLoader();

    Service.getTimeSheetDetails(
      (response: any) => {
        console.log("Raw Timesheet API Response:", response);
        const actualData = Array.isArray(response) ? response[0] : response?.data || response;
        //  const startTime = utils.createDateFromTimeString(actualData.jobStartTime);
        console.log('actualData', actualData)
        const details: TimesheetDetails = {
          id: actualData.id,
          firstName: actualData.firstName,
          lastName: `${actualData.lastName?.charAt(0) || ''}`,
          originalImageUrl: actualData.originalImageUrl,
          jobType: jobTypeMap[actualData.jobType],
          jobDate: actualData.jobDate,
          formattedAddress: actualData.formattedAddress,
          price: actualData.price,
          overtimeRate: actualData.overtimeRate,
          jobStartTime: formatTime(actualData.jobStartTime),
          jobEndTime: formatTime(actualData.jobEndTime),
          status: actualData.status,
          estimatedJobValue: actualData.estimatedJobValue,
          estimatedJobHours: actualData.estimatedJobHours,
        };

        console.log("Parsed Timesheet Details:", details);
        setTimesheetDetails(details);
        disableLoader();
      },
      (error: any) => {
        console.error("Error fetching timesheet details:", error);
        disableLoader();
      },
      timesheetId
    );
  };

  const closePopup = () => {
    setSelectedEntry(null);
    setTimesheetDetails(null);
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.4',
        position: 'relative',
      }}
    >
      <span
        dangerouslySetInnerHTML={{ __html: title }}
        style={{ textAlign: 'center' }}
      />
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            display: "flex",
            height: '15px',
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable timesheet list component
  const TimesheetList = ({ data }: { data: TimesheetEntry[] }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            userImage={entry.originalImageUrl}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: lightGrayText,
          backgroundColor: screenBg
        }}>
          No timesheets available
        </div>
      )}
    </>
  );


  // Popup overlay component
  const PopupOverlay = () => {
    if (!selectedEntry || !timesheetDetails) return null;

    const [startTime, setStartTime] = useState(convertTo24Hour(timesheetDetails.jobStartTime || ""));
    const [endTime, setEndTime] = useState(convertTo24Hour(timesheetDetails.jobEndTime || ""));
    const rate = Number(timesheetDetails.price) || 0;

    const hoursWorked = calculateHours(startTime, endTime);
    const totalPrice = calculateTotalPrice(hoursWorked, rate);
    console.log('hoursWorked', hoursWorked)
    return (
      <div className="overlay-popup">
        <div className="slide-up-card">
          <AwaitingConfirmationCard

            profileName={`${timesheetDetails.firstName} ${timesheetDetails.lastName}`}
            profileImage={timesheetDetails.originalImageUrl}
            jobType={timesheetDetails.jobType}
            jobDate={timesheetDetails.jobDate}
            jobAddress={timesheetDetails.formattedAddress}
            baseRate={timesheetDetails.price}
            extraHoursRate={timesheetDetails.overtimeRate}
            initialTimesheetRows={[
              {
                start: startTime,
                finish: endTime,
                hours: hoursWorked,
                rate,
                total: totalPrice,
              },
            ]}

            onSubmit={closePopup}
            onGoBack={closePopup}
          />
        </div>
      </div>
    );
  };


  return (
    <>
      <TabView
        activeIndex={activeTabIndex}
        onTabChange={(e) => setActiveTabIndex(e.index)}
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.awaitingConfirmation} count={timesheetData.length} />}>
          <TimesheetList data={timesheetData} />
        </TabPanel>

        <TabPanel header={<TabHeader title={headers.adjustedTimesheets} count={2} />}>
          <div>hi2</div>
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.awaitingApproval} count={1} />}>
          <div>hi3</div>
        </TabPanel>
      </TabView>

      <PopupOverlay />
    </>
  );
};

export default TimeSheet;