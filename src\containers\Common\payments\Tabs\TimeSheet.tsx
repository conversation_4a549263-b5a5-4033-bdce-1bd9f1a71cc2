import { Tab<PERSON><PERSON><PERSON>, TabView } from "primereact/tabview";
import '../TimesheetScreen.css'
import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import TimeSheetCard from "../Common/TimeSheetCard";
import { Badge } from "primereact/badge";
import { useTimesheetData, TimesheetEntry } from "../../../../hooks/useTimesheetData";
import { useTimesheetDetails } from "../../../../hooks/useTimesheetDetails";
import TimesheetDetailsPopup from "../Common/TimesheetDetailsPopup";
import useAdjustedTimesheetDetails from "../../../../hooks/useAdjustedTimesheetDetails";
import useAwaitingApprovalDetails from "../../../../hooks/useAwaitingApprovalDetails";
import NoJobsCard from "../../manageJobs/Common/NoJobsCard";

interface TimeSheetProps {
  activeTabIndex?: number;
  onTabChange?: (e: { index: number }) => void;
  accentOrange?: string;
  lightGrayText?: string;
  mediumGrayText?: string;
  cardBg?: string;
  screenBg?: string;
}

const TimeSheet: React.FC<TimeSheetProps> = ({ }) => {
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isBusiness = clientType === c.clientType.BUSINESS;
  const showApprove = isParent || isBusiness;
  const isHelper = clientType === c.clientType.UNSPECIFIED;
  const navigate = useNavigate();
  const location = useLocation();

  // Map URL paths to tab indices
  const tabRoutes = showApprove
    ? {
      'helper-confirm': 0,
      'helper-adjusted': 1,
      'finalized-timesheets': 2
    }
    : {
      'awaiting-confirmation': 0,
      'parent-adjusted': 1,
      'awaiting-approval': 2
    };

  const routeToTab = Object.fromEntries(
    Object.entries(tabRoutes).map(([key, value]) => [value, key])
  );

  // Get current tab index from URL
  const getCurrentTabIndex = () => {
    const currentPath = location.pathname.split('/').pop();
    return tabRoutes[currentPath as keyof typeof tabRoutes] ?? 0;
  };

  const [activeTabIndex, setActiveTabIndex] = useState(getCurrentTabIndex());
  const [selectedEntry, setSelectedEntry] = useState<TimesheetEntry | null>(null);
  // Use custom hooks
  const { timesheetData } = useTimesheetData(); // Keep for awaiting-confirmation tab
  const {
    adjustedTimesheetData,
    refreshData: refreshAdjustedData
  } = useAdjustedTimesheetDetails(); // Separate hook for adjusted timesheets
  const {
    awaitingApprovalData,
    isLoading: approvalLoading,
    refreshData: refreshApprovalData
  } = useAwaitingApprovalDetails(); // Separate hook for awaiting approval
  const { timesheetDetails, fetchTimesheetDetails, clearTimesheetDetails } = useTimesheetDetails();

  // Update tab index when URL changes
  useEffect(() => {
    setActiveTabIndex(getCurrentTabIndex());
  }, [location.pathname]);

  console.log('timesheetDetails', timesheetDetails)

  const headers = {
    awaitingConfirmation: showApprove ? "Helper Confirm" : "Awaiting your <br /> Confirmation",
    adjustedTimesheets: showApprove ? "Helper-adjusted" : "Parent-adjusted <br /> Timesheets",
    awaitingApproval: showApprove ? "Finalized Timesheets" : "Awaiting Parent <br /> Approval",
  };

  // Handle tab change with navigation
  const handleTabChange = (e: { index: number }) => {
    console.log('Tab change triggered:', e.index);

    const newRoute = routeToTab[e.index as keyof typeof routeToTab];

    if (isParent) {
      console.log('Navigating as Parent to:', `/parent-home/timesheet/${newRoute}`);
      navigate(`/parent-home/timesheet/${newRoute}`);
    } else if (isBusiness) {
      console.log('Navigating as Business to:', `/business-home/timesheet/${newRoute}`);
      navigate(`/business-home/timesheet/${newRoute}`);
    }
    else if (isHelper) {
      console.log('Navigating as Business to:', `/helper-home/timesheet/${newRoute}`);
      navigate(`/helper-home/timesheet/${newRoute}`);
    }
  };


  const handleReview = (entry: TimesheetEntry) => {
    setSelectedEntry(entry);
    fetchTimesheetDetails(entry.id);
    // The timesheet rows will be extracted from timesheetDetails.weeklyScheduleEntries
  };

  const closePopup = () => {
    setSelectedEntry(null);
    clearTimesheetDetails();
    // Timesheet rows are now part of timesheetDetails, so they'll be cleared automatically
  };

  // Reusable tab header component
  const TabHeader = ({ title, count }: { title: string; count: number }) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        fontSize: '10px',
        lineHeight: '1.4',
        position: 'relative',
      }}
    >
      <span
        dangerouslySetInnerHTML={{ __html: title }}
        style={{ textAlign: 'center' }}
      />
      {count > 0 && (
        <Badge
          value={count}
          style={{
            backgroundColor: "#FF6359",
            minHeight: "5px",
            minWidth: "15px",
            fontSize: "10px",
            display: "flex",
            height: '15px',
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      )}
    </div>
  );

  // Reusable timesheet list component
  const TimesheetList = ({
    data,
    emptyMessage
  }: {
    data: TimesheetEntry[];
    emptyMessage: string;
  }) => (
    <>
      {data.length > 0 ? (
        data.map((entry, index) => (
          <TimeSheetCard
            key={index}
            status={entry.status}
            type={entry.type}
            date={entry.date}
            location={entry.location}
            userName={entry.userName}
            userImage={entry.originalImageUrl}
            onReview={() => handleReview(entry)}
          />
        ))
      ) : (
        <NoJobsCard description={emptyMessage} />
      )}
    </>
  );

  const handleApprovalSuccess = () => {
    // Refresh data after successful approval
    if (timesheetDetails?.id) {
      fetchTimesheetDetails(timesheetDetails.id);
    }
  };

  return (
    <>
      <TabView
        activeIndex={activeTabIndex}
        onTabChange={handleTabChange}
        className="custom-tabview"
      >
        <TabPanel header={<TabHeader title={headers.awaitingConfirmation} count={
          timesheetData.filter(entry =>
            showApprove
              ? entry.statusCode === c.ApprovalStatus.AWAITING_APPROVAL
              : entry.statusCode === c.ApprovalStatus.AWAITING_CONFIRMATION
          ).length
        } />}>
          <TimesheetList
            data={timesheetData.filter(entry =>
              showApprove
                ? entry.statusCode === c.ApprovalStatus.AWAITING_APPROVAL
                : entry.statusCode ===  c.ApprovalStatus.AWAITING_CONFIRMATION
            )}
            emptyMessage="No timesheets awaiting confirmation"
          />
        </TabPanel>

        {/* <TabPanel header={<TabHeader title={headers.adjustedTimesheets} count={adjustedTimesheetData.length} />}>
          <TimesheetList
            data={adjustedTimesheetData}
            emptyMessage="No adjusted timesheets available"
          />
        </TabPanel>
        <TabPanel header={<TabHeader title={headers.awaitingApproval} count={awaitingApprovalData.length} />}>
          <TimesheetList
            data={awaitingApprovalData}
            emptyMessage="No timesheets awaiting approval"
          />
        </TabPanel> */}
        <TabPanel
          header={
            <TabHeader
              title={headers.adjustedTimesheets}
              count={
                adjustedTimesheetData.filter(entry =>
                  showApprove
                    ? entry.status === "Awaiting Your Approval"
                    : entry.status === "Awaiting Confirmation"
                ).length
              }
            />
          }
        >
          <TimesheetList
            data={adjustedTimesheetData.filter(entry =>
              showApprove
                ? entry.status === "Awaiting Your Approval"
                : entry.status === "Awaiting Confirmation"
            )}
            emptyMessage="No adjusted timesheets available"
          />
        </TabPanel>

        <TabPanel
  header={
    <TabHeader
      title={headers.awaitingApproval}
      count={
        awaitingApprovalData.filter(entry =>
          showApprove
            ? entry.statusCode === c.ApprovalStatus.FINALIZED
            : entry.statusCode === c.ApprovalStatus.AWAITING_APPROVAL
        ).length
      }
    />
  }
>
  <TimesheetList
    data={awaitingApprovalData.filter(entry =>
      showApprove
        ? entry.statusCode === c.ApprovalStatus.FINALIZED
        : entry.statusCode === c.ApprovalStatus.AWAITING_APPROVAL
    )}
    emptyMessage="No timesheets awaiting approval"
  />
</TabPanel>
      </TabView>
      <TimesheetDetailsPopup
        selectedEntry={selectedEntry}
        timesheetDetails={timesheetDetails}
        timesheetRows={timesheetDetails?.weeklyScheduleEntries || []} // Use extracted timesheet rows
        onClose={closePopup}
        onApprovalSuccess={handleApprovalSuccess}
      />
    </>
  );
};

export default TimeSheet;