/* CancelJobPopup.module.css */
.popupContainer {
    width: 610px;
    border-radius: 20px;
    padding: 20px;
    padding-left: 25px;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    border: 1px solid #F0F4F7;
    color: #585858;
}

.popupHeader {
    display: flex;
    justify-content: space-between;
}

.popupHeading {
    margin: 0;
    padding: 0;
    font-size: 32px;
    font-weight: 700;
}

.popupMessage {
    margin: 0;
    padding: 0;
    margin-top: 0.5rem;
    padding-block: 0.75rem;
    color: #585858;
    font-size: 1rem;
    font-weight: 500;
}

.popupActions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.cancelButton {
    margin: 0;
    padding: 0;
    width: 118px;
    height: 42px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 500;
    box-shadow: 0px 0px 4px 0px #00000040;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: underline;

}

.confirmButton {
    gap: 0.5rem;
    width: max-content;
    padding-inline: 5px;
    height: 43px;
    cursor: pointer;
    background-color: rgba(255, 99, 89, 0.3);
    border: 1px solid #FF6359;
    color: #FF6359;
    font-weight: 700;
    font-size: 16px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirmButton p {
    margin: 0;
    padding: 0;
}
.popupContainerMobile {
    width: 100%;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    border-top: 2px solid #FF6359;
    border-right: 2px solid #FF6359;
    border-left: 2px solid #FF6359;
    color: #585858;
    position: fixed;
    bottom: 0;
    left: 0;
    align-items: baseline;
}
.popupHeadingMobile {
    margin-top: 10px;
    padding: 0;
    font-size: 22px;
    font-weight: 700;
}
.popupMessageMobile {
    margin: 0;
    padding: 0;
    margin-top: 0.5rem;
    padding-block: 0.75rem;
    color: #585858;
    font-size: 14px;
    font-weight: 500;
}
.confirmButtonMobile {
    gap: 0.5rem;
    width: 100%;
    height: 43px;
    cursor: pointer;
    background: #FF63594D;
    border: 1px solid #FF6359;
    color: #FF6359;
    font-weight: 700;
    font-size: 14px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirmButtonMobile p {
    margin: 0;
    padding: 0;
}