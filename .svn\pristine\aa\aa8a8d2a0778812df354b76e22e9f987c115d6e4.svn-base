import { Card } from 'primereact/card';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import editIcon from "../../../assets/images/Icons/editIcon.png";
import c from '../../../helper/juggleStreetConstants';
import { useEffect, useState } from 'react';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import { Calendar } from 'primereact/calendar';
import { InputText } from 'primereact/inputtext';
import calender from "../../../assets/images/Icons/calender.png";
import { Dialog } from 'primereact/dialog';
import { IoClose } from 'react-icons/io5';
import useIsMobile from '../../../hooks/useIsMobile';

interface Qualification {
    canSelectCategory: boolean;
    childCategory: number;  
    children: null;
    optionId: number;
    selected: boolean;
    text: string;
}
interface Tutoring {
    canSelectCategory: boolean;
    childCategory: number;
    children: null;
    optionId: number;
    selected: boolean;
    text: string;
}
interface FirstAid {
    canSelectCategory: boolean;
    childCategory: number;
    children: null;
    optionId: number;
    selected: boolean;
    text: string;
}
interface FormData {
    firstName: string;
    middleName: string;
    lastName: string;
    wwccNumber: string;
    expirationDate: Date | null;
}
const CERTIFICATE_STATUS = {
    1: "Pending Verification",
    2: "active",
    3: "invalid",
    4: "expired",
} as const;

const WWCCForm: React.FC<{
    formData: FormData;
    setFormData: React.Dispatch<React.SetStateAction<FormData>>;
    onSave: () => void;
}> = ({ formData, setFormData, onSave }) => {
    const isFormValid = () => {
        return (
            formData.firstName?.trim() !== "" &&
            formData.lastName?.trim() !== "" &&
            formData.wwccNumber?.trim() !== "" &&
            formData?.expirationDate !== null
        );
    };
    const handleNameChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        field: keyof FormData
    ) => {
        const value = e.target.value;

        if (value === "" || /^[A-Za-z\s]+$/.test(value)) {
            setFormData((prevState) => ({
                ...prevState,
                [field]: value,
            }));
        }
    };
    const{isMobile}=useIsMobile();


    const handleWWCCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        if (value === "" || /^\d+$/.test(value)) {
            handleWWCCInput(e);
            setFormData((prevState) => ({
                ...prevState,
                wwccNumber: value,
            }));
        }
    };

    const handleWWCCInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (value === "" || /^\d+$/.test(value)) {
            setFormData((prevState) => ({
                ...prevState,
                wwccNumber: value,
            }));
        }
    };

    return (
        <div>
            <div>
                <header className={styles.wwccHeader}> Add WWCC </header>
                <p
                    style={{
                        color: "#585858",
                        fontSize: "14px",
                        fontWeight: "600",
                        marginTop: "3px",
                    }}
                >
                    Full name as it appears on WWCC
                </p>
                <div style={{ position: "relative", maxWidth:!isMobile ? "362px" : "100%", marginBottom: "20px" }}>
                    <InputText
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleNameChange(e, "firstName")}
                        placeholder=" "
                        className="input-placeholder"
                    />
                    <label
                        htmlFor="firstName"
                        className={`label-name ${formData.firstName ? "label-float" : ""}`}
                    >
                        First Name*
                    </label>
                </div>
            </div>

            <div className={styles.wwccSettingsInputContainer}>
                <div style={{ position: "relative",  maxWidth:!isMobile ? "362px" : "100%", marginBottom: "20px" }}>
                    <InputText
                        id="middleName"
                        name="middleName"
                        value={formData.middleName}
                        onChange={(e) => handleNameChange(e, "middleName")}
                        placeholder=" "
                        className="input-placeholder"
                    />
                    <label
                        htmlFor="middleName"
                        className={`label-name ${formData.middleName ? "label-float" : ""}`}
                    >
                        Middle Name
                    </label>
                </div>
            </div>

            <div className={styles.wwccSettingsInputContainer}>
                <div style={{ position: "relative",maxWidth:!isMobile ? "362px" : "100%", marginBottom: "20px" }}>
                    <InputText
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleNameChange(e, "lastName")}
                        placeholder=" "
                        className="input-placeholder"
                    />
                    <label
                        htmlFor="lastName"
                        className={`label-name ${formData.lastName ? "label-float" : ""}`}
                    >
                        Last Name*
                    </label>
                </div>
            </div>

            <div className={styles.wwccSettingsInputContainer}>
                <div style={{ position: "relative", maxWidth:!isMobile ? "362px" : "100%", marginBottom: "20px" }}>
                    <InputText
                        id="wwccNumber"
                        name="wwccNumber"
                        value={formData.wwccNumber}
                        onChange={handleWWCCChange}
                        placeholder=" "
                        className="input-placeholder"
                    />
                    <label
                        htmlFor="wwccNumber"
                        className={`label-name ${formData.wwccNumber ? "label-float" : ""}`}
                    >
                        WWCC Number*
                    </label>
                </div>
            </div>

            <div className={styles.wwccSettingsInputContainer} style={{ marginBottom: "20px" }}>
                <div>
                    {" "}
                    <p className={styles.calendarDiv}>
                        <img src={calender} alt="calender" height={"18px"} width={"16px"} />
                        Expiration Date
                    </p>
                </div>
                <Calendar
                    value={new Date(formData.expirationDate)}
                    dateFormat="DD, dth 'of' MM"
                    placeholder="Tap to Select"
                    minDate={new Date()}
                    onChange={(e) =>
                        setFormData((prevState) => ({
                            ...prevState,
                            expirationDate: e.value,
                        }))
                    }
                />
            </div>
            <div>
                <button
                    onClick={onSave}
                    className={`${styles.wwccSaveButton} ${!isFormValid() ? styles.wwccSaveButtonDisabled : ''}`}
                    disabled={!isFormValid()}
                    style={{
                        cursor: isFormValid() ? "pointer" : "not-allowed",
                    }}
                >
                    Save
                </button>
            </div>
            <p
                style={{
                    fontSize: "16px",
                    fontWeight: "400",
                    color: "#585858",
                    textWrap: "wrap",
                    width:!isMobile ? "365px" : "100%",
                }}
            >
                NOTE - It is your responsibility to have the correct WWCC (paid or volunteer). Check your state guidelines.
            </p>
        </div>
    );
};

const Certificates = () => {
    const [isVisible, setIsVisible] = useState(false);
    const [_, setCurrentEditIndex] = useState<number | null>(null);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo) as {
        data: { qualifications: Qualification[], tutoring: Tutoring[], firstAid: FirstAid },
        loading: boolean,
    };
    const session = useSelector((state: RootState) => state.sessionInfo.data);
    const dispatch = useDispatch<AppDispatch>();
    const [certificateRadioState, setcertificateRadioState] = useState(null);
    const [qualification, setQualification] = useState<Qualification[]>([]);
    const [tutoringRadioState, settutoringRadioState] = useState(null);
    const [tutoring, setTutoring] = useState<Tutoring[]>([]);
    const [checkedState, setCheckedState] = useState<boolean[]>([]);
    const [tutoringcheckedState, settutoringCheckedState] = useState<boolean[]>([]);
    const [firstAid, setFirstAid] = useState<FirstAid[]>([]);
    const [firstAidcheckedState, setfirstAidCheckedState] = useState<boolean[]>([]);
    const [firstAidRadioState, setfirstAidRadioState] = useState(null);
    const { disableLoader, enableLoader } = useLoader();
    const [showWWCCForm, setShowWWCCForm] = useState(false);
    const {isMobile}=useIsMobile()
    const [selectedCertificate, setSelectedCertificate] = useState(null);
    const [certificateError, setcertificateError] = useState<boolean>(false);
    const [formData, setFormData] = useState({
        firstName: "",
        middleName: "",
        lastName: "",
        wwccNumber: "",
        expirationDate: null,
        status: -1
    });
    useEffect(() => {
        if (sessionInfo.loading || !sessionInfo.data) return;
        const certificates = session["certificates"].find((certificate) =>
            certificate.category === 1);
        if (!certificates) return;
        setFormData({
            firstName: session["officialFirstName"],
            lastName: session["officialLastName"],
            middleName: session["officialMiddleName"],
            wwccNumber: certificates.certificateNumber,
            expirationDate: certificates.expiryDate,
            status: certificates.certificateStatus
        })
    }, [sessionInfo]);

    const handleEditCertificate = (certificate) => {
        setIsVisible(true);
        setCurrentEditIndex(certificate);
    };
    const handlecertificateRadioChange = (value: string) => {
        setcertificateRadioState(value);
    };
    const handletutoringRadioChange = (value: string) => {
        settutoringRadioState(value);
    };
    const handlefirstAidRadioChange = (value: string) => {
        setfirstAidRadioState(value);
    };
    useEffect(() => {
        const qualifications = session["provider"]["qualifications"] || [];
        setQualification(qualifications);
        setCheckedState(qualifications.map((qua) => qua.selected));
        if (qualifications.some((qua) => qua.selected)) {
            setcertificateRadioState('yes');
        }
    }, [sessionInfo.data.qualifications]);
    useEffect(() => {
        const tutoring = session["provider"]["tutoringQualifications"] || [];
        setTutoring(tutoring);
        settutoringCheckedState(tutoring.map((tutoring) => tutoring.selected));
        if (tutoring.some((tutoring) => tutoring.selected)) {
            settutoringRadioState('yes');
        }
    }, [sessionInfo.data.tutoring]);
    useEffect(() => {
        const firstAid = session["provider"]["firstAid"] || [];
        setFirstAid(firstAid);
        setfirstAidCheckedState(firstAid.map((firstAid) => firstAid.selected));
        if (firstAid.some((firstAid) => firstAid.selected)) {
            setfirstAidRadioState('yes');
        }
    }, [sessionInfo.data.firstAid]);
    const getStatusText = (certificateStatus) => {
        switch (certificateStatus) {
            case c.certificateStatus.active:
                return 'Verified';
            case c.certificateStatus.pendingVerification:
                return 'Verification Pending';
            case c.certificateStatus.invalid:
                return 'Invalid';
            case c.certificateStatus.expired:
                return 'Expired';
            default:
                return 'Unknown Status';
        }
    };
    const handleCheckboxChange = (index: number) => {
        const updatedCheckedState = checkedState.map((item, idx) => (idx === index ? !item : item));
        setCheckedState(updatedCheckedState);

        const updatedQualification = qualification.map((qua, idx) => ({
            ...qua,
            selected: updatedCheckedState[idx],
        }));
        setQualification(updatedQualification);
    };
    const tutoringCheckboxChange = (index: number) => {
        const updatedtutoringCheckedState = tutoringcheckedState.map((item, idx) => (idx === index ? !item : item));
        settutoringCheckedState(updatedtutoringCheckedState);

        const updatedTutoring = tutoring.map((tutoring, idx) => ({
            ...tutoring,
            selected: updatedtutoringCheckedState[idx],
        }));
        setTutoring(updatedTutoring);
    };
    const firstAidCheckboxChange = (index: number) => {
        const updatedfirstAidCheckedState = firstAidcheckedState.map((item, idx) => (idx === index ? !item : item));
        setfirstAidCheckedState(updatedfirstAidCheckedState);

        const updatedfirstAid = firstAid.map((firstAid, idx) => ({
            ...firstAid,
            selected: updatedfirstAidCheckedState[idx],
        }));
        setFirstAid(updatedfirstAid);
    };
    const handleWWCCSave = () => {
        setIsVisible(false);
        setCurrentEditIndex(null);
    }
    const handleSave = () => {
        let hasError = false;
        if (formData.firstName === "") {
            setcertificateError(true);
            hasError = true;
        } else {
            setcertificateError(false);
        }
        if (hasError) return;
        enableLoader();
        const certificates = [
            ...session["certificates"].filter((certificate) => certificate.category !== 1),
            {
                certificateNumber: formData.wwccNumber,
                category: 1,
                expiryDate: formData.expirationDate,
                certificateStatus: formData.status,
                statusText: getStatusText(formData.status),
            }
        ]
        const payload = {
            ...session,
            certificates: certificates,
            officialFirstName: formData.firstName,
            officialMiddleName: formData.middleName,
            officialLastName: formData.lastName,
            provider: {
                qualifications: qualification,
                tutoringQualifications: tutoring,
                firstAid: firstAid,
            },
        };
        dispatch(updateSessionInfo({ payload })).finally(() => {
            disableLoader();
        });
    };
    const handleAddCertificate = () => {
        setIsVisible(true);
        setSelectedCertificate(null);
        setFormData({
            firstName: '',
            middleName: '',
            lastName: '',
            wwccNumber: '',
            expirationDate: null,
            status: -1
        });
        setShowWWCCForm(true);
    };
    return (
        <div className='flex flex-column p-4' style={{ color: '#585858' }}>
            <div>
            </div>
            <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
                <header className={styles.utilheader}>
                    <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">My Certificate</h1>
                </header>
                <CustomButton
                    label="Save"
                    onClick={handleSave}
                    className={myfamilystyles.customButton}
                    style={{ margin: '0', width: '150px' }}
                />
            </div>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '18px', color: certificateError ? 'red' : '#179d52' }}
                >
                    Working With Children Check
                </h1>
            </div>
            {formData.firstName !== "" ? (
                <Card className={`${styles.referenceCard} mt-3`} style={{ padding: 0 }}>
                    <div><strong>Number:&nbsp;</strong>
                        {formData.wwccNumber}</div>
                    <div className='mt-1'><strong>Expiry Date:&nbsp;</strong>
                        {new Date(formData.expirationDate).toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                        })
                        }</div>
                    <div className='mt-1'><strong>Status:&nbsp;</strong>
                        {getStatusText(formData.status)}</div>
                    <div className='flex justify-between gap-2'>
                        <button
                            className={styles.editBtn}
                            onClick={() => handleEditCertificate(formData)}
                        >
                            <img
                                src={editIcon}
                                alt="Edit"
                                style={{ marginRight: "5px", width: "13px", height: "13px" }}
                            />
                            Edit
                        </button>
                    </div>
                </Card>
            ) : (
                <div className='flex justify-center mt-3'>
                    <CustomButton
                        label="Add"
                        onClick={handleAddCertificate}
                        className={myfamilystyles.customButton}
                        style={{ margin: '0', width: '150px' }}
                    />
                </div>
            )}
            <div className='mt-3'>
                <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                All helpers 18 years and older are required to have a WWCC to use Juggle Street.
            </div>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#179d52' }}
                >
                    Childcare Qualifications
                </h1>
                <div className="flex justify-content-start items-center gap-2">
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="childminder-radio"
                            value="yes"
                            checked={certificateRadioState === 'yes'}
                            onChange={() => handlecertificateRadioChange('yes')}
                            className="cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="childminder-radio"
                            value="no"
                            checked={certificateRadioState === 'no'}
                            onChange={() => handlecertificateRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
            </div>
            <div>
                {certificateRadioState === 'yes' && (
                    <>
                        <div className="flex flex-column justify-content-center">
                            {qualification.map((qualification, index) => (
                                <label key={qualification.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        checked={checkedState[index]}
                                        onChange={() => handleCheckboxChange(index)}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <div className="flex flex-column items-center">
                                        <div className="txt-clr" style={{ fontSize: '16px' }}>
                                            {qualification.text}
                                        </div>
                                    </div>
                                </label>
                            ))}
                        </div>
                    </>
                )}
            </div>
            <div className='mt-3'>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#179d52' }}
                >
                    Tutoring Qualifications
                </h1>
                <div className="flex justify-content-start items-center gap-2">
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="tutoring-radio"
                            value="yes"
                            checked={tutoringRadioState === 'yes'}
                            onChange={() => handletutoringRadioChange('yes')}
                            className="cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="tutoring-radio"
                            value="no"
                            checked={tutoringRadioState === 'no'}
                            onChange={() => handletutoringRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
            </div>

            <div>
                {tutoringRadioState === 'yes' && (
                    <>
                        <div className="flex flex-column justify-content-center">
                            {tutoring.map((tutoring, index) => (
                                <label key={tutoring.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        checked={tutoringcheckedState[index]}
                                        onChange={() => tutoringCheckboxChange(index)}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <div className="flex flex-column items-center">
                                        <div className="txt-clr" style={{ fontSize: '16px' }}>
                                            {tutoring.text}
                                        </div>
                                    </div>
                                </label>
                            ))}
                        </div>
                    </>
                )}
            </div>
            <div className='mt-3'>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#179d52' }}
                >
                    Do you have First Aid training?
                </h1>
                <div className="flex justify-content-start items-center gap-2">
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="firstAid-radio"
                            value="yes"
                            checked={firstAidRadioState === 'yes'}
                            onChange={() => handlefirstAidRadioChange('yes')}
                            className="cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="firstAid-radio"
                            value="no"
                            checked={firstAidRadioState === 'no'}
                            onChange={() => handlefirstAidRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
            </div>

            <div>
                {firstAidRadioState === 'yes' && (
                    <>
                        <div className="flex flex-column justify-content-center">
                            {firstAid.map((firstAid, index) => (
                                <label key={firstAid.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                        checked={firstAidcheckedState[index]}
                                        onChange={() => firstAidCheckboxChange(index)}
                                        style={{ fontSize: '18px' }}
                                    />
                                    <div className="flex flex-column items-center">
                                        <div className="txt-clr" style={{ fontSize: '16px' }}>
                                            {firstAid.text}
                                        </div>
                                    </div>
                                </label>
                            ))}
                        </div>
                    </>
                )}
            </div>
            <Dialog
                visible={isVisible}
                onHide={() => {
                    setIsVisible(false);
                    setCurrentEditIndex(null);
                }}
                content={
                    <div className={styles.wwccDialogContent}>
                        <IoClose
                            onClick={() => {
                                setIsVisible(false);
                                setCurrentEditIndex(null);
                            }}
                            className={styles.CloseBtn}
                        />
                        <WWCCForm
                            formData={formData}
                            setFormData={setFormData}
                            onSave={handleWWCCSave}
                        />
                    </div>
                }
            />
        </div>
    );
};

export default Certificates;
