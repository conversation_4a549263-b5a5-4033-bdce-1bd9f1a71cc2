import React, { CSSProperties, useState } from 'react'
import styles from '../../styles/child-careTab.module.css';
import { ExtendedProfileTabProps } from '../../ProviderProfile/types';
import star from '../../../../assets/images/Icons/star.png';
import rank from '../../../../assets/images/Icons/rank.png';
import { Divider } from 'primereact/divider';
import checkStar from '../../../../assets/images/Icons/check-star.png';
import degree from '../../../../assets/images/Icons/degree.png';
import earth from '../../../../assets/images/Icons/earth.png';
import check from '../../../../assets/images/Icons/check-green.png';
import c from '../../../../helper/juggleStreetConstants';
const LimitedText = ({
  text,
  limit,
  disableLimit,
  style,
  className,
}: {
  text: string;
  limit: number;
  disableLimit: boolean;
  style?: CSSProperties;
  className?: string;
}) => {
  const displayText = disableLimit || text.length <= limit ? text : text.slice(0, limit);
  return (
    <span className={className} style={{ ...style }}>
      {displayText}
    </span>
  );
};
const AboutSection = ({
  text,
  childcare,
  tutoring,
  oddJobs,
  auPair,
  nationality,
}: {
  text: string;
  childcare: boolean;
  tutoring: boolean;
  oddJobs: boolean;
  auPair: boolean;
  nationality: string;
}) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  return (
    <div className="px-4 pt-2 pb-6">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '700',
          fontSize: '20px',
          color: '#585858',
        }}
      >
        About
      </h1>
      <div className="py-2 px-3">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: '400',
            fontSize: '16px',
            color: '#585858',
            textWrap: 'wrap',
            maxWidth: '612px',
          }}
        >
          <span
            className="m-0 p-0 inline-block text-left"
            style={{
              fontWeight: '400',
              fontSize: '16px',
              color: '#585858',
              textWrap: 'wrap',
              width: '100%',
            }}
          >
            <LimitedText
              className="m-0 p-0"
              text={text}
              limit={displayLimit}
              disableLimit={textState}
              style={{
                fontWeight: '400',
                fontSize: '16px',
                color: '#585858',
                textWrap: 'wrap',
                width: '100%',
              }}
            />
            {text.length >= displayLimit && (
              <span
                className="cursor-pointer hover:text-gray-300"
                style={{
                  fontWeight: '400',
                  fontSize: '12px',
                  color: '#585858',
                  textWrap: 'wrap',
                  width: '100%',
                }}
                onClick={() => toggleTextState((prev) => !prev)}
              >
                {' '}
                {textState ? 'Show Less.' : 'Read More...'}
              </span>
            )}
          </span>
        </p>
      </div>
      <div className="flex mt-3">
        <div className="flex-grow-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: '700',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            Services
          </h1>
          <div className="flex py-2 px-4">
            <div className="h-min flex-grow-1 flex gap-2">
              {[
                { label: 'Childcare', shouldAdd: childcare },
                { label: 'Tutoring', shouldAdd: tutoring },
                { label: 'Odd Jobs', shouldAdd: oddJobs },
                { label: 'Au Pair', shouldAdd: auPair },
              ].map((data, index) => {
                if (!data.shouldAdd) return null;
                return (
                  <div key={index} className="flex gap-1 align-items-center">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: '700',
                        fontSize: '14px',
                        color: '#585858',
                      }}
                    >
                      {data.label}
                    </p>
                    <img src={check} alt="check" width="14px" height="13.07" />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className="flex flex-column align-items-center">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: '700',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            Nationality
          </h1>
          <div
            className="flex gap-2 justify-content-center align-items-center"
            style={{
              width: '145.28px',
              height: '42.28px',
              borderRadius: '20px',
              backgroundColor: '#F1F1F1',
            }}
          >
            <img src={earth} alt="earth" height="14.4px" width="14.4px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: '700',
                fontSize: '12px',
                color: '#585858',
              }}
            >
              {nationality || 'Not specified'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
const ChildcareQualifications = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        Childcare Qualifications
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const FirstAidAccreditations = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        First Aid Accreditations
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const TutoringQualification = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        Tutoring Qualification
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const Transportation = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        Transportation
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const AdditionalLanguages = ({ data }: { data: string[] }) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        Additional Languages
      </h1>
      {data.map((val, index) => (
        <div key={index} className="flex gap-2 mt-2 mx-2">
          <img src={degree} alt="degree" width="19.82px" height="18.62px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#585858',
            }}
          >
            {val}
          </p>
        </div>
      ))}
    </div>
  );
};
const Checks = ({ date1, date2 }: { date1: string; date2: string }) => {
  return (
    <div className="px-4 pt-2">
      <h1
        className="m-0 p-0"
        style={{
          fontWeight: '400',
          fontSize: '16px',
          color: '#585858',
        }}
      >
        Checks
      </h1>
      <div className="flex gap-2 mt-2 mx-2">
        <img src={checkStar} alt="check" height="23px" width="23px" />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: '600',
            fontSize: '16px',
            color: '#585858',
          }}
        >
          Working With Children Check
        </p>
      </div>
      <p
        className="m-0 p-0 mt-2 mb-3"
        style={{
          fontWeight: '700',
          fontSize: '12px',
          color: '#179D52',
        }}
      >
        {`Verified on: ${date1} | Expires on: ${date2}`}
      </p>
    </div>
  );
};
const ReviewAndRatingHead = ({
  rating,
  ratingCount,
  isSuperHelper,
}: {
  rating: number;
  ratingCount: number;
  isSuperHelper: boolean;
}) => {
  return (
    <div className="px-4 pt-2 mt-2 mb-4">
      <div className="flex justify-content-between align-items-center">
        <div className="flex flex-column gap-1">
          <h1
            className="m-0 p-0"
            style={{
              fontWeight: '700',
              fontSize: '20px',
              color: '#585858',
            }}
          >
            Reviews
          </h1>
          <div className="flex gap-1">
            <img src={star} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: '300',
                fontSize: '14px',
                color: '#585858',
              }}
            >
              {`${rating.toFixed(1)} Avg Rating (${ratingCount} ratings)`}
            </p>
          </div>
        </div>
        {isSuperHelper && (
          <div className="flex gap-2 align-items-center">
            <img src={rank} alt="star" width="19.82px" height="18.62px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: '700',
                fontSize: '18px',
                color: '#585858',
              }}
            >
              Super Helper
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
const ReviewAndRatingList = ({
  ratings,
}: {
  ratings: Array<{
    clientFirstName: string;
    clientLastName: string;
    feedback: string;
    ratingDate: string;
    ratingAvg: number;
    clientImageUrl: string;
  }>;
}) => {
  return (
    <div className="flex flex-column px-4 pt-2 mt-2 mb-4">
      {ratings.map((rating, index) => (
        <React.Fragment key={index}>
          <Divider />
          <div className="flex gap-2 my-2">
            <div
              style={{
                height: '38px',
                width: '38px',
                background: 'gray',
                borderRadius: '50%',
                overflow: 'hidden',
              }}
            >
              <img
                src={rating.clientImageUrl}
                alt="client Image"
                width="100%"
                height="100%"
              />
            </div>
            <div className="flex-grow-1 flex flex-column gap-2">
              <div className="flex">
                <div className="flex-grow-1 flex flex-column">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: '400',
                      fontSize: '16px',
                      color: '#585858',
                    }}
                  >{`${rating.clientFirstName} ${rating.clientLastName}`}</p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: '400',
                      fontSize: '12px',
                      color: '#585858',
                    }}
                  >
                    {new Date(rating.ratingDate).toLocaleDateString('en-GB')}
                  </p>
                </div>
                <div className="flex gap-1">
                  <img src={star} alt="star" width="19.82px" height="18.62px" />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: '300',
                      fontSize: '14px',
                      color: '#585858',
                    }}
                  >
                    {`${rating.ratingAvg.toFixed(1)}`}
                  </p>
                </div>
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: '400',
                  fontSize: '14px',
                  color: '#585858',
                }}
              >
                {rating.feedback}
              </p>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};
const Profile: React.FC<ExtendedProfileTabProps> = ({ helper }) => {
  const [textState, toggleTextState] = useState<boolean>(false);
  const displayLimit = 300;
  const text1 = helper?.providerMyExperience2 || '';
  const selectedTutoringQualifications = helper?.tutoringQualifications.filter((tutoringQualifications) => tutoringQualifications.selected) ?? [];
  const selectedTransport = helper?.transport.filter((transport) => transport.selected) ?? [];
  const selectedAdditionalLanguages = helper?.spokenLanguages.filter((spokenLanguages) => spokenLanguages.selected) ?? [];
  const selectedfirstAid = helper?.firstAid.filter((firstAid) => firstAid.selected) ?? [];
  const selectedQualifications = helper?.qualifications.filter((qualification) => qualification.selected) ?? [];
  const selectedCountryOfCitizenship = c.countriesIso.find((country) => country.value.toLowerCase() === helper?.nationality?.toLowerCase() || country.alpha2.toLowerCase() === helper?.nationality?.toLowerCase())
  const hasData = (helper?.certificates.length ?? 0) > 0 ||
    (helper?.firstAid.length ?? 0) > 0 && selectedfirstAid.length > 0 ||
    (helper?.qualifications.length ?? 0) > 0 && selectedQualifications.length > 0 ||
    (helper?.tutoringQualifications.length ?? 0) > 0 && selectedTutoringQualifications.length > 0 ||
    (helper?.transport.length ?? 0) > 0 && selectedTransport.length > 0 ||
    (helper?.spokenLanguages.length ?? 0) > 0 && selectedAdditionalLanguages.length > 0;
  return (
    <div>
      <div className={styles.childCareContainer}>
        <div className={styles.childCareBoxOne}>
          <AboutSection
            text={helper?.aboutMe ?? ''}
            childcare={helper?.interestedInChildcareJobs ?? true}
            tutoring={helper?.interestedInTutoringJobs ?? true}
            oddJobs={helper?.interestedInOddJobs ?? true}
            auPair={helper?.interestedInAuPairJobs ?? true}
            nationality={selectedCountryOfCitizenship?.label}
          />
        </div>
      </div>
      <br />
      {helper?.providerMyExperience2 && (
        <div className={styles.childCareContainer}>
          <div className={styles.childCareBoxOne}>
            <h1 className={styles.childCare}>Special Needs Experience</h1>
            <div className={styles.childCareExperience}>
              {/* {helper.providerMyExperience} */}
              <div className="py-2 px-3">
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                  }}
                >
                  <span
                    className="m-0 p-0 inline-block text-left"
                    style={{
                      fontWeight: '400',
                      fontSize: '16px',
                      color: '#585858',
                    }}
                  >
                    <LimitedText
                      className="m-0 p-0"
                      text={text1}
                      limit={displayLimit}
                      disableLimit={textState}
                      style={{
                        fontWeight: '400',
                        fontSize: '16px',
                        color: '#585858',
                      }}
                    />
                    {text1.length >= displayLimit && (
                      <span
                        className="cursor-pointer hover:text-gray-300"
                        style={{
                          fontWeight: '400',
                          fontSize: '12px',
                          color: '#585858',
                        }}
                        onClick={() => toggleTextState((prev) => !prev)}
                      >
                        {' '}
                        {textState ? 'Show Less.' : 'Read More...'}
                      </span>
                    )}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      <br />
      {hasData && (
        <div className={styles.childCareContainer}>
          <div className={styles.childCareBoxOne}>
            {(helper?.certificates.length ?? 0) > 0 && (
              <>
                <Checks
                  date1={new Date(
                    helper.certificates[0].verificationDate
                  ).toLocaleDateString('en-GB')}
                  date2={new Date(helper.certificates[0].expiryDate).toLocaleDateString(
                    'en-GB'
                  )}
                />
                {(helper?.firstAid.length ?? 0) > 0 && selectedfirstAid.length > 0 && <Divider />}
              </>
            )}
            {(helper?.firstAid.length ?? 0) > 0 && selectedfirstAid.length > 0 && (
              <>
                <FirstAidAccreditations
                  data={selectedfirstAid.map((val) => val.text)}
                />
                {(helper?.qualifications.length ?? 0) > 0 && <Divider />}
              </>
            )}
            {(helper?.qualifications.length ?? 0) > 0 && selectedQualifications.length > 0 && (
              <>
                <ChildcareQualifications
                  data={selectedQualifications.map((val) => val.text)}
                />
                {(helper?.tutoringQualifications.length ?? 0) > 0 && <Divider />}
              </>
            )}
            {(helper?.tutoringQualifications.length ?? 0) > 0 && selectedTutoringQualifications.length > 0 && (
              <>
                <TutoringQualification
                  data={selectedTutoringQualifications.map((val) => val.text)}
                />
                {(helper?.transport.length ?? 0) > 0 && <Divider />}
              </>
            )}
            {(helper?.transport.length ?? 0) > 0 && selectedTransport.length > 0 && (
              <>
                <Transportation
                  data={selectedTransport.map((val) => val.text)}
                />
                {(helper?.spokenLanguages.length ?? 0) > 0 && <Divider />}
              </>
            )}
            {(helper?.spokenLanguages.length ?? 0) > 0 && selectedAdditionalLanguages.length > 0 && (
              <>
                <AdditionalLanguages
                  data={selectedAdditionalLanguages.map((val) => val.text)}
                />
              </>
            )}
          </div>
        </div>
      )}
      <br />
      {(helper?.hasVouches) && (
        <>
          <div className={styles.childCareContainer}>
            <div className={styles.childCareBoxOne}>
              <div>
                <h1
                  className="m-0 p-0"
                  style={{
                    fontWeight: '700',
                    fontSize: '20px',
                    color: '#585858',
                  }}
                >
                  References
                </h1>
                <p>Available on request. Please contact Customer Service to obtain referee details.</p>
              </div>
            </div>
          </div>
        </>
      )}
      <br />
      {(helper?.providerReviewsCount ?? 0) > 0 && (
        <div className={styles.childCareContainer}>
          <div className={styles.childCareBoxOne}>
            <>
              <ReviewAndRatingHead
                rating={helper?.providerRatingsAvg ?? 0}
                ratingCount={helper?.providerRatingsCount ?? 0}
                isSuperHelper={helper?.isSuperProvider ?? false}
              />
              {(helper?.ratingsExtended.length ?? 0) > 0 && (
                <ReviewAndRatingList ratings={helper.ratingsExtended} />
              )}
            </>
          </div>
        </div>
      )}
    </div>
  )
}
export default Profile