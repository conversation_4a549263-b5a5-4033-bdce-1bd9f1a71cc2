import { useSelector } from "react-redux";
import { RootState } from "../store";

const AdminNav = () => {
  const isAdmin = useSelector((state: RootState) => state.applicationState.isAdminMode);
  return isAdmin ? (
    <div className="fixed z-5 top-0 left-0 w-full flex justify-content-center align-items-center bg-black-alpha-70 text-white">Admin Mode</div>
  ) : (
    <></>
  );
};

export default AdminNav;
