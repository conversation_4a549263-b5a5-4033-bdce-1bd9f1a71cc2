import { useNavigate, useSearchParams } from "react-router-dom";
import c from "../../../../helper/juggleStreetConstants";
import { MyJobSectionProps } from "../../../Common/manageJobs/types";
import NoJobsCard from "../../../Common/manageJobs/Common/NoJobsCard";
import JobHistoryCard from "../../../Common/manageJobs/Common/JobHistoryCard";
import useIsMobile from "../../../../hooks/useIsMobile";
import utils from "../../../../components/utils/util";

const CompletedJobs: React.FC<MyJobSectionProps> = ({ jobHistory }) => {
    const convertTo12HourFormat = (timeSlot: string) => {
        const [start, end] = timeSlot.split("-");
        return `${formatTime(start)} - ${formatTime(end)}`;
    };
    const { isMobile } = useIsMobile()
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const handleViewJobIdChange = (newJobId: number, index: number) => {
        const updatedParams = new URLSearchParams(searchParams);
        updatedParams.set("rateJobId", String(newJobId));
        updatedParams.set("activeTab", String(index));
        navigate({ search: updatedParams.toString() });
    };

    const formatTime = (time: string) => {
        const [hours, minutes] = time.split(":").map(Number);
        const period = hours >= 12 ? "pm" : "am";
        const formattedHours = hours % 12 || 12;
        return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
    };

    const completedJobsCount = jobHistory.length;
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const suffixes = ["st", "nd", "rd", "th"];
        const day = date.getDate();
        const daySuffix =
            day % 10 === 1 && day !== 11
                ? suffixes[0]
                : day % 10 === 2 && day !== 12
                    ? suffixes[1]
                    : day % 10 === 3 && day !== 13
                        ? suffixes[2]
                        : suffixes[3];

        // Format the date
        const dayOfWeek = new Intl.DateTimeFormat("en-GB", {
            weekday: "short",
        }).format(date);
        const month = new Intl.DateTimeFormat("en-GB", { month: "long" }).format(
            date
        );
        return `${dayOfWeek} ${day}${daySuffix} of ${month}`;
    };
    const getTutoringLabel = (jobType: number) => {
        switch (jobType) {
            case 64:  // PRIMARY_SCHOOL_TUTORING
                return "Primary School Tutoring";
            case 128: // HIGH_SCHOOL_TUTORING
                return "High School Tutoring";
            default:
                return "";
        }
    };


    return !isMobile ? (
        <div>
            {jobHistory.length === 0 ? (
                // Show NoJobsCard when no jobs exist
                <div className="mr-4">
                    <NoJobsCard
                        description={"No job history matches the specified criteria."}
                    />
                </div>
            ) : (
                // Show the header and job cards when jobs exist
                <>
                    <div
                        className="p-3"
                        style={{
                            border: "1px solid #DFDFDF",
                            width: "1111px",
                            borderRadius: "10px",
                        }}
                    >
                        <h1
                            className="p-0 m-0 font-bold"
                            style={{ fontSize: "30px", color: "#585858" }}
                        >
                            Completed Jobs{" "}
                            <span style={{ color: "#179D52" }}>({completedJobsCount})</span>
                        </h1>

                        {jobHistory
                            // filter((job) => job.jobStatus === c.jobStatus.COMPLETED)
                            .map((job, index) => {
                                // const ratingProvided = (job.jobStatus === c.jobStatus.COMPLETED && job.isRatedByClient) ||
                                //     (!job.isRatedByProvider && job.jobStatus === c.jobStatus.COMPLETED && job.awardedApplicantId === job.id);
                                const ratingProvided = job.jobStatus === c.jobStatus.COMPLETED && job.ratingProvided
                                // const ratingProvided = (job.jobType === c.jobType.BABYSITTING || job.jobType === c.jobType.AU_PAIR) && !job.isRatedByProvider

                                return (
                                    <JobHistoryCard
                                        key={index}
                                        name={
                                            job.ownerFirstName && job.ownerLastInitial
                                                ? `${job.ownerFirstName} ${job.ownerLastInitial}`
                                                : "No Name"
                                        }
                                        jobTitle={
                                            job.jobType === 64 || job.jobType === 128
                                                ? "Tutoring Job"
                                                : job.jobType === 1 || job.jobType === 256
                                                    ? "One-off Job"
                                                    : "Recurring Job"
                                        }
                                        time={
                                            job.durationType === null
                                                ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                                                : `${job.duration} week duration`
                                        }
                                        date={
                                            job.jobDate
                                                ? formatDate(job.jobDate)
                                                : "No Date Available"
                                        }
                                        address={
                                            job.formattedAddress
                                                ? `${utils.cleanAddress(job.formattedAddress)}`
                                                : "No Address Available"
                                        }
                                        ratingProvided={ratingProvided}
                                        jobStatus={job.jobStatus}
                                        avatarUrl={job.ownerImageSrc || null}
                                        onClick={() => handleViewJobIdChange(job.id, 1)}
                                        onClickChat={() => {
                                            const params = new URLSearchParams()
                                            params.set('userId', `${job.jobOwnerId}`)
                                            params.set('flag', '0')
                                            navigate({
                                                pathname: '/helper-home/inAppChat',
                                                search: params.toString()
                                            });

                                        }}
                                    />
                                );
                            })}
                    </div>
                </>
            )}
        </div>
    ) : (
        <div>
            {jobHistory.length === 0 ? (
                // Show NoJobsCard when no jobs exist
                <div >
                    <NoJobsCard
                        description={"No job history matches the specified criteria."}
                    />
                </div>
            ) : (
                // Show the header and job cards when jobs exist
                <>
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <h1
                            className="p-0 m-0 font-bold"
                            style={{ fontSize: "18px", color: "#585858" }}
                        >
                            Completed Jobs{" "}
                            <span style={{ color: "#179D52" }}>({completedJobsCount})</span>
                        </h1>

                        {jobHistory
                            // filter((job) => job.jobStatus === c.jobStatus.COMPLETED)
                            .map((job, index) => {
                                // const ratingProvided = (job.jobStatus === c.jobStatus.COMPLETED && job.isRatedByClient) ||
                                //     (!job.isRatedByProvider && job.jobStatus === c.jobStatus.COMPLETED && job.awardedApplicantId === job.id);
                                const ratingProvided = job.jobStatus === c.jobStatus.COMPLETED && job.ratingProvided
                                // const ratingProvided = (job.jobType === c.jobType.BABYSITTING || job.jobType === c.jobType.AU_PAIR) && !job.isRatedByProvider

                                return (
                                    <JobHistoryCard
                                        key={index}
                                        name={
                                            job.ownerFirstName && job.ownerLastInitial
                                                ? `${job.ownerFirstName} ${job.ownerLastInitial}`
                                                : "No Name"
                                        }
                                        jobTitle={
                                            job.jobType === 64 || job.jobType === 128
                                                ? "Tutoring Job"
                                                : job.jobType === 1 || job.jobType === 256
                                                    ? "One-off Job"
                                                    : "Recurring Job"
                                        }
                                        time={
                                            job.durationType === null
                                                ? convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`)
                                                : `${job.duration} week duration`
                                        }
                                        date={
                                            job.jobDate
                                                ? formatDate(job.jobDate)
                                                : "No Date Available"
                                        }
                                        address={
                                            job.formattedAddress
                                                ? `${utils.cleanAddress(job.formattedAddress)}`
                                                : "No Address Available"
                                        }
                                        ratingProvided={ratingProvided}
                                        jobStatus={job.jobStatus}
                                        avatarUrl={job.ownerImageSrc || null}
                                        onClick={() => handleViewJobIdChange(job.id, 1)}
                                        onClickChat={() => {
                                            const params = new URLSearchParams()
                                            params.set('userId', `${job.jobOwnerId}`)
                                            params.set('flag', '0')
                                            navigate({
                                                pathname: '/helper-home/inAppChat',
                                                search: params.toString()
                                            });

                                        }}
                                    />
                                );
                            })}
                    </div>
                </>
            )}
        </div>
    )
};

export default CompletedJobs;
