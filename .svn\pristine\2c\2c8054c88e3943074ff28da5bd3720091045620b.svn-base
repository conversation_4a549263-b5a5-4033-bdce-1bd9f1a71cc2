import { useSelector } from 'react-redux';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { RootState } from '../../../store';
import c from '../../../helper/juggleStreetConstants';
import { Rating } from 'primereact/rating';
const ResponseStats = () => {
    const session = useSelector((state: RootState) => state.sessionInfo.data) as { responseRate: number, responseTime: number,validApplicationsCount: number };

    const responseTimeTypes = c.responseTimeTypes;

    const getResponseTime = () => {
        if (!session || session.validApplicationsCount === 0) return 0;
        if (session.responseRate < 75 || session.responseTime === c.responseTimes.within7Days) {
            return 1000;
        }

        if (session.responseTime === c.responseTimes.withinAnHour) {
            return c.responseTimes.withinADay;
        }

        return session.responseTime;
    };

    const responseTime = getResponseTime();

    const responseStars = (() => {
        switch (responseTime) {
            case 1:
                return 5;
            case 24:
                return 5;
            case 48:
                return 4;
            case 72:
                return 3;
            case 168:
                return 2;
            case 1000:
                return 1;
            default:
                return 1;
        }
    })();

    const isActiveResponseTime = (value: number) => responseTime === value;

    return (
        <div className={styles.utilcontainerhelper} style={{ color: '#585858' }}>
            <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
                <header className={styles.utilheader}>
                    <h1 className="p-0 m-0">Response Time</h1>
                </header>
            </div>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '18px', color: '#179d52' }}
                >
                    Your Response Time
                </h1>
                <p>The best way to improve your chances of winning a job is to respond quickly to all job invites.</p>
                <p>Your Response Rate is calculated on how often and how quickly you respond.</p>
                <p>Your current Response Rate:</p>
                {responseTime !== 0 && (
                    <Rating value={responseStars} max={5} cancel={false} className="custom-rating" />
                )}
                <table className='mt-3'>
                    <tbody>
                        {responseTimeTypes.map((response, index) => (
                            <tr key={index}>
                                <td>
                                    <input
                                        type="radio"
                                        name="responseTime"
                                        checked={isActiveResponseTime(response.value)}
                                        onChange={() => { }}
                                        style={{ cursor: 'pointer' }}
                                    />
                                </td>
                                <td
                                    className={
                                        isActiveResponseTime(response.value) ? 'text-primary' : ''
                                    }
                                >
                                    {response.label}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '18px', color: '#179d52' }}
                >
                    Important Info
                </h1>
                <p>Parents search for helpers using Response Time filters - helpers with good response rates receive more job invitations.</p>
                <p>Response Times are measured over the past 30 days.</p>
            </div>
        </div>
    )
}

export default ResponseStats