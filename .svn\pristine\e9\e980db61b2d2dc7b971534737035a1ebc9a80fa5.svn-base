import { useSearchParams } from 'react-router-dom';
import TutoringJobPosted from './TutoringJobPosted';
import ChildcareJobPosted from './ChildcareJobPosted';

const JobSuccess = () => {
    const [searchParams] = useSearchParams();
    const show = Number(searchParams.get('show'));
    const manageBy = Number(searchParams.get('managedBy'));
    const jobType = Number(searchParams.get('jobType'));
    if (show === 1) {
        return <TutoringJobPosted manageBy={manageBy} jobType={jobType} />;
    }
    return <ChildcareJobPosted  manageBy={manageBy}  jobType={jobType}/>;
};

export default JobSuccess;
