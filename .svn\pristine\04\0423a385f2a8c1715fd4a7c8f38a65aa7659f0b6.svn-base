/* TimesheetScreen.css (Updated) */

:root {
  --primary-green: #179D52;
  --accent-orange: #f97316;
  --light-gray-text: #585858;
  --medium-gray-text: #4b5563;
  --dark-gray-text: #1f2937;
  --card-bg: #ffffff;
  --screen-bg: #ffffff; /* Can be same as card-bg or different */
  --light-border-color: #e5e7eb; /* Used for the inactive tab border */
  --white-color: #ffffff;
  --notification-badge-bg: #ef4444;
  --status-text-green: #22c55e;
  --bottom-nav-inactive-text: rgba(255,255,255,0.7);
  --bottom-nav-border: rgba(255,255,255,0.2);
}


.phone-screen-container {
  max-width: 100%;
  /* min-height: 800px; */
  height: 100vh; /* Make it take full viewport height if not constrained by parent */
  margin: 0Px auto;
  background-color: var(--screen-bg);
  /* border-radius: 20px; */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Mock Status Bar */
.mock-status-bar {
  background-color: var(--phone-outer-bg);
  /* padding: 5px 15px; */
  color: var(--white-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}
.mock-status-bar-icon {
  margin-right: 5px;
}
.mock-status-bar-icon:last-child {
  margin-right: 0;
}


/* App Header */
.app-header {
  background-color: var(--primary-green);
  color: var(--white-color);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
  flex-shrink: 0;
}
.header-back-icon {
  margin-right: 15px;
  cursor: pointer;
  font-size: 1.5rem;
}

/* Main Content Area */
.main-content-area {
  flex-grow: 1;
  background-color: var(--screen-bg);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Custom TabView Styles (for Timesheet & Payments Tabs) */
.custom-tabview .p-tabview-nav {
  background-color: var(--card-bg); /* white */
  /* border-bottom: 3px solid gray; */
  border-bottom: 3px solid #e5e7eb;
  display: flex;
  justify-content: space-around;
}
.custom-tabview .p-tabview-nav li {
  flex: 1;
  text-align: center;
}
.custom-tabview .p-tabview-nav li .p-tabview-nav-link {
  border: none !important;
  border-bottom: 3px solid transparent !important;
  color: #585858;
  font-weight: 500;
  padding: 0.8rem 0.2rem;
  font-size: 0.75rem;
  line-height: 1.2; /* This will be fine for single line too */
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column; /* Helps center even single line text if wrapped in a div */
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 50px;
}
.custom-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background-color: white !important; /* white */
  color: var(--medium-gray-text) !important;
  border-bottom-color: var(--accent-orange) !important;
  font-weight: bold;
}
.custom-tabview .p-tabview-panels {
  padding: 0;
  background-color: var(--screen-bg);
  flex-grow: 1; /* Allow panels to grow */
}
.custom-tabview { /* Ensure TabView itself can grow */
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

/* Tab Header with Badge */
.tab-header-with-badge {
  position: relative;
  padding-right: 10px; /* Space for the badge */
  /* For multi-line text, add <br/> in the JSX */
}
.notification-badge {
  position: absolute;
  top: 0px;
  right: -5px; /* Adjust if needed for single line headers */
  background-color: var(--notification-badge-bg); /* Red */
  color: var(--white-color);
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 0.65rem;
  font-weight: bold;
  line-height: 1;
  min-width: 18px;
  text-align: center;
}

/* Card Styles (reusable for Timesheet and Payments) */
.content-tab-card-area { /* Renamed from timesheet-tab-content for generality */
  padding: 15px;
  background-color: var(--screen-bg);
}
.info-card { /* Renamed from timesheet-card for generality */
  background-color: var(--card-bg);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.card-header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}
.card-status-info {
  /* flex-grow: 1; */
}
.card-status-info .status-line {
  margin: 0 0 10px 0;
  font-weight: bold;
  font-size: 0.9rem;
  color: var(--medium-gray-text);
}
.card-status-info .status-value { /* Default green status */
  color: var(--status-text-green);
  font-weight: normal;
}
/* NEW/UPDATED Status value for pending parent approval (reddish) */
.status-value--pending-approval {
  color: var(--notification-badge-bg); /* Uses badge red */
  font-weight: normal;
}
.card-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: var(--light-gray-text);
  font-size: 0.85rem;
}
.card-detail-item:last-child {
  margin-bottom: 0;
}
.detail-item-icon {
  margin-right: 8px;
  color: var(--medium-gray-text);
}
.card-avatar-section {
  text-align: center;
  flex-shrink: 0;
  margin-left: 10px;
}
.avatar-name {
  margin: 5px 0 0;
  font-size: 0.8rem;
  color: var(--medium-gray-text);
}
.action-button.p-button { /* Generic class for orange buttons */
  background-color: var(--accent-orange);
  border-color: var(--accent-orange);
  color: var(--white-color);
  width: 100%;
  border-radius: 8px;
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 0.9rem;
}
.action-button.p-button:hover,
.action-button.p-button:focus {
  background-color: #fb923c; /* Slightly lighter orange for hover/focus */
  border-color: #fb923c;
}

/* Placeholder Views (for empty tabs or generic content) */
.placeholder-view {
  padding: 20px;
  text-align: center;
  color: var(--medium-gray-text);
  background-color: var(--screen-bg);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
}
.placeholder-icon {
  font-size: 3.5rem;
  color: var(--primary-green);
  margin-bottom: 20px;
}
.placeholder-title {
  margin-top: 0px;
  color: var(--dark-gray-text);
  font-size: 1.5rem;
}
.placeholder-text {
  font-size: 0.9rem;
}

/* Tab Panel Content for simple text tabs */
.simple-tab-panel-content {
  padding: 20px;
  text-align: center;
  color: var(--light-gray-text);
  background-color: var(--screen-bg);
  height: 100%;
}


/* Bottom Navigation */
.bottom-navigation {
  background-color: var(--primary-green);
  color: var(--white-color);
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  border-top: 1px solid var(--bottom-nav-border);
  flex-shrink: 0;
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  color: var(--bottom-nav-inactive-text);
  border-bottom: 3px solid transparent;
  transition: color 0.2s, border-bottom-color 0.2s;
  flex: 1;
  text-align: center;
}
.nav-item.nav-item--active {
  color: var(--white-color);
  border-bottom-color: var(--white-color);
}
.nav-item-icon {
  font-size: 1.4rem;
  margin-bottom: 3px;
}
.nav-item-label {
  font-size: 0.75rem;
}
.nav-item.nav-item--active .nav-item-label {
  font-weight: bold;
}