import React, { useState, useRef, useCallback, useEffect } from "react";
import ReactCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import styles from "../../Common/styles/identity-check.module.css";
import "../styles/add-family-photo.css";
import { ProgressBar } from "primereact/progressbar";
import "primeflex/primeflex.css";
import { useDispatch, useSelector } from "react-redux";
import Userimage from "../../../assets/images/Group_light.png";
import { AppDispatch, RootState } from "../../../store";
import { decrementProfileActivationStep, incrementProfileActivationStep } from "../../../store/slices/applicationSlice";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
// import Loader from '../../../commonComponents/Loader';
import CustomButton from "../../../commonComponents/CustomButton";
import useLoader from "../../../hooks/LoaderHook";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../../Common/HorizontalNavigationMobile";

const AddFamilyPhoto = () => {
  const [currentStep] = useState(1);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch<AppDispatch>();
  const [isImageUploaded, setIsImageUploaded] = useState(false);
  const { enableLoader, disableLoader } = useLoader();
  const [, setCroppedImageUrl] = useState<string | null>(null);
  const { isMobile } = useIsMobile();
  const handlePreviousStep = () => {
    dispatch(decrementProfileActivationStep());
  };
  const applicationSate = useSelector<RootState>((state) => state.applicationState.profileActivationCurrentStep);

  useEffect(() => {
    if (sessionInfo.data?.["defaultImage"]?.["scale1ImageUrl"]) {
      setSelectedImage(sessionInfo.data["defaultImage"]["scale1ImageUrl"]);
      setIsImageUploaded(true);
    }
  }, [sessionInfo.data]);

  const handleCancel = () => {
    setSelectedImage(null);
    setCroppedImageUrl(null);
    setCompletedCrop(undefined);
    setCrop(undefined);
    setIsImageUploaded(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        4 / 4,
        width,
        height
      ),
      width,
      height
    )
    setCrop(crop)
  }

  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop) => {
      const canvas = document.createElement("canvas");
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      const pixelCrop = {
        x: Math.round(crop.x * scaleX),
        y: Math.round(crop.y * scaleY),
        width: Math.round(crop.width * scaleX),
        height: Math.round(crop.height * scaleY),
        unit: 'px' as const // Ensure the unit is explicitly set to "px"
      };

      canvas.width = pixelCrop.width;
      canvas.height = pixelCrop.height;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(
          image,
          pixelCrop.x,
          pixelCrop.y,
          pixelCrop.width,
          pixelCrop.height,
          0,
          0,
          pixelCrop.width,
          pixelCrop.height
        );
      }

      return {
        croppedImageUrl: canvas.toDataURL("image/jpeg"),
        x: pixelCrop.x,
        y: pixelCrop.y,
        height: pixelCrop.height,
        width: pixelCrop.width,
        unit: pixelCrop.unit // Include the unit property in the return object
      };
    },
    []
  );

  const handleSavePhoto = useCallback(() => {
    if (imgRef.current && completedCrop) {
      enableLoader();
      const { croppedImageUrl, x, y, height, width, unit } = getCroppedImg(imgRef.current, completedCrop);
      setCroppedImageUrl(croppedImageUrl);
      setSelectedImage(croppedImageUrl); // Set the cropped image as the selected image
      const payload = {
        id: sessionInfo.data["defaultImage"]["id"],
        cropDimensions: { x, y, width, height, unit },
        isCropped: true,
        scale1ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"], // Set the cropped image URL as scale1ImageUrl
        scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
        scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
      };
      dispatch(
        updateUser({
          payload: {
            ...sessionInfo.data,
            defaultImage: payload,
          },
        })
      )
        .then(() => {
          // Ensure the component re-renders with the new image
          setIsImageUploaded(false);
          setSelectedImage(null);
          setCroppedImageUrl(null);
          setCompletedCrop(undefined);
          setCrop(undefined);
        })
        .catch((error) => {
          console.error("Error updating user:", error);
        })
        .finally(() => {
          disableLoader();
        });
    }
  }, [completedCrop, getCroppedImg, sessionInfo.data, dispatch, disableLoader]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      enableLoader();
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {

        setIsImageUploaded(true);
        setSelectedImage(reader.result as string);
        const binaryData = e.target?.result as ArrayBuffer;
        const payload = {
          id: sessionInfo.data["defaultImage"]["id"],
          imageBinary: binaryData,
          isCropped: true,
          scale1ImageUrl: reader.result as string, // Set the uploaded image as scale1ImageUrl
          scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
          scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
        };
        dispatch(
          updateUser({
            payload: {
              ...sessionInfo.data,
              defaultImage: payload,
            },
          })
        )
          .then(() => { })
          .catch((error) => {
            console.error("Error updating user:", error);
          })
          .finally(() => {
            disableLoader();
          });
      };
      reader.readAsDataURL(file);
    }
  };
  return (
    <div className={!isMobile ? `${styles.profileActivationContainer}` : `${styles.profileActivationContainerMobile}`}>
      <header className={styles.profileActivationHeader}>
        {!isMobile ? (
          <h1 className={styles.headerTitle} style={{ textWrap: "nowrap" }}>
            Add Family Photo
          </h1>
        ) : (
          <HorizontalNavigation
            onBackClick={handlePreviousStep}
            title="Add Family Photo"
          />
        )}
        <ProgressBar
          value={
            sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]
          }
          className={styles.idprogressbar}
        />
        <p
          style={{
            fontFamily: "Poppins",
            fontWeight: 500,
            fontSize: "14px",
            color: "#585858",
          }}
        >
          Your profile is{" "}
          <span
            style={{ color: "#179D52", fontSize: "20px", fontWeight: "700" }}
          >
            {sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]}
            % complete.
          </span>
        </p>
      </header>
      <main style={{ gap: isMobile && "0px", padding: isMobile && "0px" }} className={styles.profileActivationContent}>
        {currentStep === 1 && (
          <>
            <div className={styles.contentLeft}>
              <h2 className={styles.headerTitle}>
                {selectedImage ? "Crop image" : "Family photo"}
              </h2>
              <p className="" style={{ color: "#585858" }}>
                Families and helpers are required to have a profile photo.
                <br />
                <br />
                Your photo is only visible to other Juggle Street users. It
                cannot be found by somebody browsing the web.
              </p>
            </div>
            <div className={styles.contentRight}>
              <div className={styles.container}>
                <div
                  className=""
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    id="image-upload"
                    style={{ display: "none" }}
                  />
                  {!isImageUploaded ? (
                    <label
                      htmlFor="image-upload"
                      className="flex  align-items-center flex-wrap"
                    >
                      <div
                        className="cursor-pointer"
                        style={{
                          width: "150px",
                          height: "150px",
                          borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                          border: "1px solid #FFA500",
                        }}
                      >
                        {!isImageUploaded && !sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
                          <img
                            src={Userimage}
                            alt=""
                            className="cursor-pointer mt-4 ml-4 "
                            style={{
                              width: "95px",
                              height: "99px",
                              // borderRadius: "50%",
                              backgroundColor: "#F0F4F7",
                            }}
                          />
                        ) : (
                          <img
                            src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? Userimage}
                            alt=""
                            className="cursor-pointer"
                            style={{
                              width: "148px",
                              height: "148px",
                              borderRadius: "50%",
                              backgroundColor: "#F0F4F7",
                            }}
                          />
                        )}
                      </div>
                      <div
                        className="ml-3 mt-1 pl-3 pr-3 p-2 cursor-pointer"
                        style={{
                          backgroundColor: "#1F9EAB",
                          color: "#FFFFFF",
                          fontSize: "12px",
                          fontWeight: "600",
                          borderRadius: "8px",
                        }}
                      >
                        <span className="pi pi-camera"></span>
                        &nbsp;&nbsp;Upload Photo
                      </div>
                      <p className="text-center cursor-pointer" style={{ color: "#585858", fontSize: '12px' }}  >
                        Please choose a photo that represents your whole family.
                      </p>
                    </label>

                  ) : (
                    <ReactCrop
                      crop={crop}
                      onChange={(_, percentCrop) => setCrop(percentCrop)}
                      onComplete={(c) => setCompletedCrop(c)}
                      aspect={1}
                      // circularCrop
                      // minWidth={30}
                      // minHeight={30}
                      style={{ objectFit: 'contain' }}
                    >
                      <img
                        ref={imgRef}
                        src={selectedImage}
                        alt="Selected"
                        crossOrigin="anonymous"
                        onLoad={onImageLoad}
                        style={{
                          backgroundColor: "#F0F4F7",
                          cursor: "pointer",
                        }}
                      />
                    </ReactCrop>
                  )}
                </div>
                {isImageUploaded && (
                  <div className={styles.resultSection}>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        marginTop: "10px",
                        zIndex: !isMobile ? 999 : 0,
                        position: 'relative'
                      }}
                    >
                      <CustomButton
                        label="Cancel"
                        onClick={handleCancel}
                        style={{
                          marginRight: "10px",
                          backgroundColor: "transparent",
                          color: "#585858",
                          height: "39px",
                          fontSize: "14px",
                          fontWeight: "500",
                          boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                        }}
                      />
                      <CustomButton
                        label="Save photo"
                        onClick={handleSavePhoto}
                        style={{ backgroundColor: "#1F9EAB" }}
                      />
                    </div>
                  </div>

                )}

              </div>
            </div>
          </>
        )}
      </main>
      {!isMobile ? (
        <footer className={styles.buttonContainer}>
          {applicationSate !== 1 &&
            <CustomButton
              label={
                <>
                  <i
                    className="pi pi-angle-left"
                    style={{ marginRight: "8px" }}
                  ></i>
                  Previous
                </>
              }
              onClick={handlePreviousStep}
              style={{
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontSize: "14px",
                fontWeight: "500",
              }}
            />
          }
          <div style={{ flexGrow: 1 }} />
          <CustomButton
            label={
              <>
                Skip
                <i
                  className="pi pi-angle-right"
                  style={{ marginRight: "8px" }}
                ></i>
              </>
            }
            onClick={() => dispatch(incrementProfileActivationStep())}
            style={{
              backgroundColor: "transparent",
              color: "#585858",
              width: "156px",
              height: "39px",
              fontSize: "14px",
              border: "1px solid #F0F4F7",
              fontWeight: "500",
            }}
            className={styles.customButtonHover}
          />
        </footer>
      ) : (
        <footer className={styles.buttonContainerMobile}>
          <CustomButton
            label={
              <>
                <i
                  className="pi pi-angle-left"
                  style={{ marginRight: "8px" }}
                ></i>
                Previous
              </>
            }
            onClick={handlePreviousStep}
            style={{
              backgroundColor: "transparent",
              color: "#585858",
              width: "156px",
              height: "39px",
              fontSize: "14px",
              fontWeight: "500",
              margin: "5px"
            }}
          />
          <div style={{ flexGrow: 1 }} />
        </footer>
      )}
    </div>
  );
};

export default AddFamilyPhoto;
