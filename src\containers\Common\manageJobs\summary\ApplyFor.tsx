import { useEffect, useRef, useState } from "react";
import CustomButton from "../../../../commonComponents/CustomButton";
import { Jobs } from "../types";
import { Helper } from "../../../Parent/ProviderProfile/types";
import { useNavigate, useSearchParams } from "react-router-dom";
import Service from "../../../../services/services";
import AwardShiftsToParent from "../Common/AwardShiftsToParent";
import c from "../../../../helper/juggleStreetConstants";
import { Dialog } from "primereact/dialog";
import useLoader from "../../../../hooks/LoaderHook";
import { CancelJobPopup, useCancelJobPopup } from "../Common/CancelJobPopup";
// import remove from '../../../../assets/images/Icons/remove.png';
import remove from '../../../../assets/images/Icons/remove.png';
import { RxCross2 } from "react-icons/rx";
import { FcCheckmark } from "react-icons/fc";
import AwardShiftsToHelper from "../Common/AwardShiftsToHelper";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import { updateActiveTabIndex } from "../../../../store/slices/accountSettingSlice";
import { updateAccountAndSettingsActiveTab, updateShowAccountAndSettings } from "../../../../store/slices/applicationSlice";
import useIsMobile from "../../../../hooks/useIsMobile";

interface TimeSlot {
    dayOfWeek: number;
    day: string;
    startTime: string;
    endTime: string;
    shiftId: number;
    price: number;
    awardedAplicantId: number | null;
    applicants: Array<{
        applicantId: number;
        availabilityId: number;
        publicName: string;
        imageSrc: string;
        status: number;
        applicantRatingsCount: number;
        completedJobsCount: number;
        applicantRatingsAvg: number;
        responseRate: number;
    }>;
}
interface AvailableApplicants {
    applicantId: number;
    publicName: string;
    imageSrc: string;
    isSuperHelper: boolean;
    applicantRatingsCount: number;
    applicantRatingsAvg: number;
    responseRate: number;
    id: number;
    applicationStatus: number;
}
interface WeeklyScheduleProps {
    timeSlots: TimeSlot[];
    className?: string;
    availabilityStatusEnum: any; // Pass the enum from constants
    totalInvited: number;
    totalViewed: number;
    job: Jobs;
    availableApplicants: AvailableApplicants[];
}
interface UnavailableReason {
    label: string;
    value: string;
}

const unavailableReasons: UnavailableReason[] = [
    { label: 'Hourly rate too low', value: '3' },
    { label: 'Job too short', value: '6' },
    { label: 'Calendar clash', value: '1' },
    { label: 'Too far away', value: '5' },
    { label: 'Other', value: '4' },
];
const Items: UnavailableReason[] = [
    { label: 'I need taking home', value: '1' },
    { label: 'Walking', value: '2' },
    { label: 'Public transport', value: '3' },
    { label: 'Getting picked up', value: '4' },
    { label: 'Driving myself', value: '5' },
    { label: 'Uber/Taxi', value: '6' },
];

interface ErrorPopup {
    title: string;
    description: string;
    onConfirm: () => void;
    update?: () => void;
}

const ApplyFor: React.FC<WeeklyScheduleProps> = ({
    job,
}) => {
    const [isvisible, setIsvisible] = useState(false);
    const [selectedHelper, setSelectedHelper] = useState<Helper | null>(null);
    const [selectedShifts, setSelectedShifts] = useState<number[]>([]);
    const [clickedShiftId, setClickedShiftId] = useState<number | null>(null);
    const [searchParams] = useSearchParams();
    const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
    const jobIdString = searchParams.get('jobId');
    const jobId = jobIdString ? parseInt(jobIdString) : null;
    const [providerData, setProviderData] = useState<Jobs | null>(null);
    const [areAllSelected, setAreAllSelected] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedReason, setSelectedReason] = useState<string | null>(null);
    const [selectedItem, setSelectedItem] = useState<string | null>(null);
    const { disableLoader, enableLoader } = useLoader();
    const navigate = useNavigate();
    const { isMobile } = useIsMobile()
    const [commonPopup, setCommonPopup] = useState<{ title: string, description: string, onConfirm: () => void, update?: () => void } | null>(null);
    const handleDialogClose = () => {
        setIsvisible(false);
        setSelectedHelper(null);
        setSelectedShifts([]);
        setClickedShiftId(null);
        setAreAllSelected(false);
    };

    const [errorPopup, setErrorPopup] = useState<ErrorPopup | null>(null);

    const handleShiftSelect = (shiftId: number) => {
        setClickedShiftId(shiftId);
        setSelectedShifts((prev) => {
            if (prev.includes(shiftId)) {
                return prev.filter((id) => id !== shiftId);
            } else {
                return [...prev, shiftId];
            }
        });
    };

    const handleSelectAll = () => {
        if (!providerData?.['applicantAvailability']) return;

        const requiredShifts = providerData?.['applicantAvailability']
            .filter(shift => shift.isRequired)
            .map(shift => shift.id);




        if (areAllSelected) {
            // Unselect all
            setSelectedShifts([]);
            setAreAllSelected(false);
        } else {
            // Select all
            setSelectedShifts(requiredShifts);
            setAreAllSelected(true);
        }


    };

    const transformShiftsData = (data: any) => {
        if (!data?.applicantAvailability) return [];
        return data.applicantAvailability.map(shift => ({
            day: getDayName(shift.dayOfWeek),
            startTime: shift.jobStartTime,
            endTime: shift.jobEndTime,
            shiftId: shift.id,
            price: shift.price,
            overtimerate: shift.price, // Example calculation, adjust as needed
            isRequired: shift.isRequired,
            awardedAplicantImage: null // Add if you have this data
        }));
    };

    const getDayName = (dayOfWeek: number) => {
        const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        return days[dayOfWeek];
    };

    const formatTimeTo12Hour = (time: string): string => {
        if (!time) return "";
        const [hours, minutes] = time.split(":").map(Number);
        const period = hours >= 12 ? "pm" : "am";
        const formattedHours = hours % 12 || 12;
        return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
    };

    const renderShiftInfo = (shift: any, isMobile?: boolean) => {
        const startTime = formatTimeTo12Hour(shift.jobStartTime);
        const endTime = formatTimeTo12Hour(shift.jobEndTime);
        const day = getDayName(shift.dayOfWeek);
        return !isMobile ? (
            <div
                key={shift.id}
                style={{
                    color: '#585858',
                    fontSize: '16px',
                    marginBottom: '8px'
                }}
            >
                {shift.availabilityStatus === c.applicantAvailabilityStatus.AVAILABLE ? <FcCheckmark /> : <RxCross2 />} {day}, {startTime} to {endTime} for ${shift.price} (${shift.price} per hour)
            </div>
        ) : (
            <div
                key={shift.id}
                style={{
                    color: '#585858',
                    fontSize: '14px',
                    marginBottom: '8px'
                }}
            >
                {shift.availabilityStatus === c.applicantAvailabilityStatus.AVAILABLE ? <FcCheckmark /> : <RxCross2 />} {day}, {startTime} to {endTime} for ${shift.price} (${shift.price} per hour)
            </div>
        )
    };

    useEffect(() => {
        Service.jobProviderDetails(
            (response: Jobs) => {
                setProviderData(response);
            },
            (error: any) => {
                console.error('Error fetching data:', error);
            },
            jobId
        );
    }, [jobId]);

    const selectedItemRef = useRef(selectedItem);
    useEffect(() => {
        selectedItemRef.current = selectedItem;
    }, [selectedItem]);

    const handleApplyClick = () => {
        if (job.jobType === c.jobType.BABYSITTING || job.jobType === c.jobType.ONE_OFF_ODD_JOB) {
            // Show transportation dialog first
            setErrorPopup({
                title: "Getting Home",
                description: "How are you getting home after this job?",
                onConfirm: () => {
                    // Use the ref to get the latest value
                    const currentSelectedItem = selectedItemRef.current;
                    if (currentSelectedItem) {
                        handleApplyForRecurringJob(currentSelectedItem);
                    }
                }
            });
        } else if (job.id && job.id > 0) {
            setIsvisible(true);
        }
    };

    const handleApply = () => {
        const payload = {
            ...job,
            applicationStatus: c.jobApplicationStatus.APPLIED,
            applicantAvailability: job.applicantAvailability.map(a => ({
                ...a,
                availabilityStatus: selectedShifts.includes(a.id) ? c.applicantAvailabilityStatus.AVAILABLE : c.applicantAvailabilityStatus.NOT_AVAILABLE
            }))
        };

        // enableLoader();
        // Service.jobProviderDetailsUpdate(
        //     (response: any) => {
        //         setProviderData(response);
        //         const newSearchParams = new URLSearchParams();
        //         newSearchParams.set('jobId', '-1');
        //         newSearchParams.set('activeTab', '0');
        //         navigate({ search: newSearchParams.toString() });
        //         disableLoader();
        //     },
        //     (error: any) => {
        //         console.error('Error updating job status:', error);
        //         disableLoader();
        //     },
        //     jobId,
        //     payload
        // );
    };

    const handleApplyForRecurringJob = (transportation: string) => {
        const payload = {
            ...job,
            applicationStatus: c.jobApplicationStatus.APPLIED,
            gettingHome: Number(transportation),
            applicantAvailability: job.applicantAvailability.map(a => ({
                ...a,
                availabilityStatus: selectedShifts.includes(a.id)
                    ? c.applicantAvailabilityStatus.AVAILABLE
                    : c.applicantAvailabilityStatus.NOT_AVAILABLE
            }))
        };

        enableLoader();
        Service.jobProviderDetailsUpdate(
            (response: any) => {
                setProviderData(response);
                const newSearchParams = new URLSearchParams();
                newSearchParams.set('jobId', '-1');
                newSearchParams.set('activeTab', '0');
                navigate({ search: newSearchParams.toString() });
                disableLoader();
            },
            (error: any) => {
                disableLoader();
                // Show the exact error message from the API response
                const errorMessage = error.response?.data?.message || error.message || "An error occurred";

                setErrorPopup({
                    title: "Error",
                    description: errorMessage, // Use the actual error message from response
                    onConfirm: () => setErrorPopup(null)
                });
            },
            jobId,
            payload
        );
    };

    const handleDeclineClick = () => {
        setIsDialogOpen(true);
    };

    const handleDialogSubmit = () => {
        submitUnavailable(parseInt(selectedReason, 10));
        setIsDialogOpen(false);
    };

    const submitUnavailable = (unavailableReason: number) => {
        const payload = {
            ...job,
            unavailableReason: unavailableReason,
            applicationStatus: c.jobApplicationStatus.NOT_AVAILABLE
        }
        enableLoader();
        Service.jobProviderDetailsUpdate(
            (response: any) => {
                setProviderData(response);
                const newSearchParams = new URLSearchParams();
                newSearchParams.set('jobId', '-1');
                newSearchParams.set('activeTab', '0');
                navigate({ search: newSearchParams.toString() });
                disableLoader();
            },
            (error: any) => {
                console.error('Error updating job status:', error);
                disableLoader();
            },
            jobId,
            payload
        );
    };

    const handleWithdrawApplication = () => {
        const payload = {
            ...job,
            applicationStatus: c.jobApplicationStatus.WITHRDAWN
        }

        enableLoader();
        Service.jobProviderDetailsUpdate(
            (response: any) => {

                setProviderData(response);
                const newSearchParams = new URLSearchParams();
                newSearchParams.set('jobId', '-1');
                newSearchParams.set('activeTab', '0');
                navigate({ search: newSearchParams.toString() });
                disableLoader();
            },
            (error: any) => {
                console.error('Error updating job status:', error);
                disableLoader();
            },
            jobId,
            payload
        );
    };

    const handleCancelJob = () => {
        const payload = {
            ...job,
            jobStatus: c.jobStatus.CANCELLED,
            applicationStatus: c.jobApplicationStatus.CANCELLED
        }
        enableLoader();
        Service.jobProviderDetailsUpdate(
            (response: any) => {

                setProviderData(response);
                const newSearchParams = new URLSearchParams();
                newSearchParams.set('jobId', '-1');
                newSearchParams.set('activeTab', '-1');
                navigate({ search: newSearchParams.toString() });
                disableLoader();
            },
            (error: any) => {
                console.error('Error updating job status:', error);
                disableLoader();
            },
            jobId,
            payload
        );
    };

    const handleCancel = () => {
        showCancelJobPopup(
            'Confirm Cancellation', // Heading
            'Cancelling a job will be shown on your Jobs Cancelled section of your profile, are you sure you want to continue?', // Message
            'Cancel', // Cancel Text
            'Ok', // Confirm Text
            <img src={remove} alt="confirm" style={{ height: '20px', width: '20px' }} />, // Confirm Icon
            handleCancelJob // Confirm callback
        );
    }

    const handleWithdrawApplicationCancel = () => {
        showCancelJobPopup(
            'Confirmation', // Heading
            'You are about to withdraw your job application and you will not be able to re-apply for this job. The parent will be notified of your withdrawal, but this will not affect your job cancellation counter. Click OK to continue.', // Message
            'Cancel', // Cancel Text
            'Ok', // Confirm Text
            <img src={remove} alt="confirm" style={{ height: '20px', width: '20px' }} />, // Confirm Icon
            handleWithdrawApplication // Confirm callback
        );
    };

    const handleChat = () => {
        const params = new URLSearchParams();
        params.set('userId', `${job.jobOwnerId}`);
        params.set('flag', '0');
        navigate({
            pathname: '/helper-home/inAppChat',
            search: params.toString()
        });

    };

    const footerContent = (
        <div className="flex">
            <CustomButton
                label="Cancel"
                style={{
                    margin: '0 1rem 0 0',
                    width: '150px',
                    backgroundColor: '#585858'
                }}
                onClick={() => setIsDialogOpen(false)}
            />
            <CustomButton
                label="Submit"
                style={{
                    margin: '0',
                    width: '150px'
                }}
                onClick={handleDialogSubmit}
                disabled={!selectedReason} // Disable the button if no reason is selected
            />
        </div>
    );

    const commonPopupFooter = (
        <div className="flex">
            <CustomButton
                label="Cancel"
                style={{
                    margin: '0 1rem 0 0',
                    width: '150px',
                    backgroundColor: '#585858'
                }}
                onClick={() => setCommonPopup(null)}
            />
            <CustomButton
                label="Ok"
                style={{
                    margin: '0',
                    width: '150px'
                }}
                onClick={() => {
                    commonPopup?.onConfirm();
                    setCommonPopup(null);
                }}
                disabled={!selectedItem} // Disable the button if no item is selected
            />
        </div>
    );
    const renderErrorPopupFooter = () => {
        if (!errorPopup) return null;

        return (
            <div className="flex">
                <CustomButton
                    label="Cancel"
                    style={{
                        margin: '0 1rem 0 0',
                        width: '150px',
                        backgroundColor: '#585858'
                    }}
                    onClick={() => setErrorPopup(null)}
                />
                <CustomButton
                    label={errorPopup.update ? "Update" : "Ok"}
                    style={{
                        margin: '0',
                        width: '150px'
                    }}
                    onClick={() => {
                        if (errorPopup.title === "Getting Home" && selectedItem) {
                            // Capture the current value at the time of click
                            const transportOption = selectedItem;
                            // Pass it directly to the handler
                            handleApplyForRecurringJob(transportOption);
                        } else if (errorPopup.update) {
                            errorPopup.update();
                        } else {
                            errorPopup.onConfirm();
                        }
                        setErrorPopup(null);
                    }}
                    disabled={errorPopup.title === "Getting Home" && !selectedItem}
                />
            </div>
        );
    };

    return !isMobile ? (
        <>
            <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
            <div
                className=''
                style={{
                    width: '952px',
                    borderRadius: '20px',
                    paddingLeft: '30px',
                    paddingRight: '20px',
                    marginRight: '2%',
                    border: '1px solid #dfdfdf',
                    marginTop: '2%',
                }}
            >
                <div className='flex flex-column mb-2'>
                    {job.jobType === c.jobType.BABYSITTING || job.jobType === c.jobType.ONE_OFF_ODD_JOB ? (
                        <h3 className='font-bold p-0 m-0 mt-2' style={{ color: '#585858', fontSize: '30px' }}>
                            Apply For Job
                        </h3>
                    ) : job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.PENDING ||
                            job.applicationStatus === c.jobApplicationStatus.VIEWED) ? (
                        <h3 className='font-bold p-0 m-0 mt-2' style={{ color: '#585858', fontSize: '30px' }}>
                            Apply for one or more days below.
                        </h3>
                    ) : (
                        <h3 className='font-bold p-0 m-0 mt-2' style={{ color: '#585858', fontSize: '30px' }}>
                            Your Application
                        </h3>
                    )}

                    {providerData?.['applicantAvailability'] && (
                        <div style={{ marginTop: '16px', marginBottom: '24px' }}>
                            {providerData['applicantAvailability']
                                .filter(shift => shift.isRequired)
                                .map(shift => renderShiftInfo(shift))}
                        </div>
                    )}
                </div>

                <div className="flex gap-2 mb-5">
                    {job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.PENDING ||
                            job.applicationStatus === c.jobApplicationStatus.VIEWED) ? (
                        <>
                            <CustomButton
                                label="Apply"
                                style={{ margin: '0', width: '150px' }}
                                onClick={handleApplyClick}
                            />
                            <CustomButton
                                label="Decline"
                                style={{ margin: '0', width: '150px', backgroundColor: '#585858' }}
                                onClick={handleDeclineClick}
                            />
                            {isDialogOpen && (
                                <Dialog
                                    visible={isDialogOpen}
                                    style={{ width: '40vw' }}
                                    onHide={() => setIsDialogOpen(false)}
                                    header={<span style={{ color: '#585858' }}>Tell Us Why</span>}
                                    footer={footerContent}
                                    modal
                                    closeOnEscape
                                    dismissableMask
                                    draggable={false}
                                >
                                    <p className="p-0 m-0 mb-2" style={{ color: '585858' }}>Please tell us the main reason you declined this job.</p>
                                    {unavailableReasons.map((reason) => (
                                        <div key={reason.value} className="flex align-items-center">
                                            <input
                                                type="radio"
                                                id={reason.value}
                                                name="unavailableReason"
                                                value={reason.value}
                                                onChange={(e) => setSelectedReason(e.target.value)}
                                            />
                                            <label htmlFor={reason.value} className="cursor-pointer ml-1">{reason.label}</label>
                                        </div>
                                    ))}
                                </Dialog>
                            )}
                        </>
                    ) : null}

                    <CustomButton
                        label="Chat"
                        style={{ margin: '0', width: '150px', backgroundColor: 'white', color: 'black', border: '1px solid black' }}
                        onClick={handleChat}
                    />

                    {job.jobStatus === c.jobStatus.AWARDED && (
                        <CustomButton
                            label="Cancel Job"
                            style={{ margin: '0', width: '150px', backgroundColor: '#ff6666' }}
                            onClick={handleCancel}
                        />
                    )}

                    {job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.APPLIED ||
                            job.applicationStatus === c.jobApplicationStatus.SHORTLISTED_BY_CLIENT ||
                            job.applicationStatus === c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM) && (
                            <CustomButton
                                label="Withdraw Application"
                                style={{ margin: '0', width: '150px', backgroundColor: 'white', color: 'black', border: '1px solid black' }}
                                onClick={handleWithdrawApplicationCancel}
                            />
                        )}
                </div>
                {isvisible && (
                    <AwardShiftsToHelper
                        isOpen={isvisible}
                        onClose={handleDialogClose}
                        title="Apply for one or more days below."
                        shifts={transformShiftsData(providerData)}
                        onShiftSelect={handleShiftSelect}
                        onSelectAll={handleSelectAll}
                        selectedShifts={selectedShifts}
                        timeSlots={transformShiftsData(providerData)}
                        job={job}
                        onApply={handleApply}
                        handleApplyForRecurringJob={() => handleApplyForRecurringJob(selectedItem || '')}
                    />

                )}
                {commonPopup && (
                    <Dialog
                        visible={!!commonPopup}
                        style={{ width: '40vw' }}
                        onHide={() => setCommonPopup(null)}
                        header={<span style={{ color: '#585858' }}>{commonPopup.title}</span>}
                        footer={commonPopupFooter}
                        modal
                        closeOnEscape
                        dismissableMask
                        draggable={false}
                    >
                        <p className="p-0 m-0 mb-2" style={{ color: '585858' }}>{commonPopup.description}</p>
                        {Items.map((reason) => (
                            <div key={reason.value} className="flex align-items-center">
                                <input
                                    type="radio"
                                    id={reason.value}
                                    name="transportation"
                                    value={reason.value}
                                    onChange={(e) => {

                                        setSelectedItem(e.target.value);
                                    }}
                                />
                                <label htmlFor={reason.value} className="cursor-pointer ml-1">{reason.label}</label>
                            </div>
                        ))}
                    </Dialog>
                )}
                {errorPopup && (
                    <Dialog
                        visible={!!errorPopup}
                        style={{ width: '40vw' }}
                        onHide={() => setErrorPopup(null)}
                        header={<span style={{ color: '#585858' }}>{errorPopup.title}</span>}
                        footer={renderErrorPopupFooter()}
                        modal
                        closeOnEscape
                        dismissableMask
                        draggable={false}
                    >
                        <p className="p-0 m-0 mb-2" style={{ color: '585858' }}>
                            {errorPopup.description}
                        </p>
                        {errorPopup.title === "Getting Home" && (
                            <div>
                                {Items.map((reason) => (
                                    <div key={reason.value} className="flex align-items-center">
                                        <input
                                            type="radio"
                                            id={reason.value}
                                            name="transportation"
                                            value={reason.value}
                                            onChange={(e) => setSelectedItem(e.target.value)}
                                        />
                                        <label htmlFor={reason.value} className="cursor-pointer ml-1">
                                            {reason.label}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        )}
                    </Dialog>
                )}
            </div>
        </>
    ) : (
        <>
            <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
            <div
                className=''
                style={{
                    width: '100%',
                    borderRadius: '20px',
                    marginTop: '22px',
                    boxShadow: "0px 0px 4px 0px #00000040",
                    padding:"15px"

                }}
            >
                <div className='flex flex-column mb-2'>
                    {job.jobType === c.jobType.BABYSITTING || job.jobType === c.jobType.ONE_OFF_ODD_JOB ? (
                        <h3 className='font-bold p-0 m-0 ' style={{ color: '#585858', fontSize: '22px' }}>
                            Apply For Job
                        </h3>
                    ) : job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.PENDING ||
                            job.applicationStatus === c.jobApplicationStatus.VIEWED) ? (
                        <h3 className='font-bold p-0 m-0 ' style={{ color: '#585858', fontSize: '22px' }}>
                            Apply for one or more days below.
                        </h3>
                    ) : (
                        <h3 className='font-bold p-0 m-0 ' style={{ color: '#585858', fontSize: '22px' }}>
                            Your Application
                        </h3>
                    )}

                    {providerData?.['applicantAvailability'] && (
                        <div style={{ marginTop: '10px', marginBottom: '24px' }}>
                            {providerData['applicantAvailability']
                                .filter(shift => shift.isRequired)
                                .map(shift => renderShiftInfo(shift, isMobile))}
                        </div>
                    )}
                </div>
                <div className="flex gap-2 mb-3 ">
                    {job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.PENDING ||
                            job.applicationStatus === c.jobApplicationStatus.VIEWED) ? (
                        <>
                            <CustomButton
                                label="Apply"
                                style={{ margin: '0', width: 'max-content', backgroundColor: '#179D52', color: '#fff', paddingInline: "23px", borderRadius: "20px", border: "1px solid #179D52" }}
                                onClick={handleApplyClick}
                            />
                            <CustomButton
                                label="Decline"
                                style={{ margin: '0', width: 'max-content', backgroundColor: '#fff', color: '#FF6359', paddingInline: "23px", borderRadius: "20px", border: "1px solid #FF6359" }}
                                onClick={handleDeclineClick}
                            />
                            {isDialogOpen && (
                                <Dialog
                                    visible={isDialogOpen}
                                    style={{
                                        width: "100%", position: "fixed", bottom: "0", left: "0",
                                        borderTopLeftRadius: "20px", borderTopRightRadius: "20px"
                                    }}
                                    onHide={() => setIsDialogOpen(false)}
                                    header={<span style={{ color: '#585858' }}>Tell Us Why</span>}
                                    footer={footerContent}
                                    modal
                                    closeOnEscape
                                    dismissableMask
                                    draggable={false}
                                >
                                    <p className="p-0 m-0 mb-2" style={{ color: '585858', fontWeight: "500" }}>Please tell us the main reason you declined this job.</p>
                                    {unavailableReasons.map((reason) => (
                                        <div key={reason.value} className="flex align-items-center">
                                            <input
                                                type="radio"
                                                id={reason.value}
                                                name="unavailableReason"
                                                value={reason.value}
                                                onChange={(e) => setSelectedReason(e.target.value)}
                                            />
                                            <label htmlFor={reason.value} className="cursor-pointer ml-1" style={{ color: '585858', fontWeight: "400" }}>{reason.label}</label>
                                        </div>
                                    ))}
                                </Dialog>
                            )}
                        </>
                    ) : null}

                    <CustomButton
                        label="Chat"
                        style={{ margin: '0', width: 'max-content', backgroundColor: '#FFA500', color: '#fff', paddingInline: "30px", borderRadius: "20px" }}
                        onClick={handleChat}
                    />

                    {job.jobStatus === c.jobStatus.AWARDED && (
                        <CustomButton
                            label="Cancel Job"
                            style={{ margin: '0', width: 'max-content', backgroundColor: '#ff6666', color: '#fff', paddingInline: "25px", borderRadius: "20px" }}
                            onClick={handleCancel}
                        />
                    )}

                    {job.jobStatus === c.jobStatus.PENDING &&
                        (job.applicationStatus === c.jobApplicationStatus.APPLIED ||
                            job.applicationStatus === c.jobApplicationStatus.SHORTLISTED_BY_CLIENT ||
                            job.applicationStatus === c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM) && (
                            <CustomButton
                                label="Withdraw Application"
                                style={{ margin: '0', width: 'max-content', backgroundColor: '#fff', color: '#585858', paddingInline: "23px", borderRadius: "20px", border: "1px solid #585858" }}
                                onClick={handleWithdrawApplicationCancel}
                            />
                        )}
                </div>
                {isvisible && (
                    <AwardShiftsToHelper
                        isOpen={isvisible}
                        onClose={handleDialogClose}
                        title="Apply for one or more days below."
                        shifts={transformShiftsData(providerData)}
                        onShiftSelect={handleShiftSelect}
                        onSelectAll={handleSelectAll}
                        selectedShifts={selectedShifts}
                        timeSlots={transformShiftsData(providerData)}
                        job={job}
                        onApply={handleApply}
                        handleApplyForRecurringJob={() => handleApplyForRecurringJob(selectedItem || '')}
                    />

                )}
                {commonPopup && (
                    <Dialog
                        visible={!!commonPopup}
                        style={{
                            width: "100%", position: "fixed", bottom: "0", left: "0",
                            borderTopLeftRadius: "20px", borderTopRightRadius: "20px"
                        }}
                        onHide={() => setCommonPopup(null)}
                        header={<span style={{ color: '#585858' }}>{commonPopup.title}</span>}
                        footer={commonPopupFooter}
                        modal
                        closeOnEscape
                        dismissableMask
                        draggable={false}
                    >
                        <p className="p-0 m-0 mb-2" style={{ color: '585858' }}>{commonPopup.description}</p>
                        {Items.map((reason) => (
                            <div key={reason.value} className="flex align-items-center">
                                <input
                                    type="radio"
                                    id={reason.value}
                                    name="transportation"
                                    value={reason.value}
                                    onChange={(e) => {

                                        setSelectedItem(e.target.value);
                                    }}
                                />
                                <label htmlFor={reason.value} className="cursor-pointer ml-1">{reason.label}</label>
                            </div>
                        ))}
                    </Dialog>
                )}
                {errorPopup && (
                    <Dialog
                        visible={!!errorPopup}
                        style={{
                            width: "100%", position: "fixed", bottom: "0", left: "0",
                            borderTopLeftRadius: "20px", borderTopRightRadius: "20px"
                        }}
                        onHide={() => setErrorPopup(null)}
                        header={<span style={{ color: '#585858' }}>{errorPopup.title}</span>}
                        footer={renderErrorPopupFooter()}
                        modal
                        closeOnEscape
                        dismissableMask
                        draggable={false}
                    >
                        <p className="p-0 m-0 mb-2" style={{ color: '585858' }}>
                            {errorPopup.description}
                        </p>
                        {errorPopup.title === "Getting Home" && (
                            <div>
                                {Items.map((reason) => (
                                    <div key={reason.value} className="flex align-items-center">
                                        <input
                                            type="radio"
                                            id={reason.value}
                                            name="transportation"
                                            value={reason.value}
                                            onChange={(e) => setSelectedItem(e.target.value)}
                                        />
                                        <label htmlFor={reason.value} className="cursor-pointer ml-1">
                                            {reason.label}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        )}
                    </Dialog>
                )}
            </div>
           
        </>
    )
};

export default ApplyFor;
