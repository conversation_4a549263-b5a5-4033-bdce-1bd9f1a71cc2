import React, { useState } from "react";
import styles from "./styles/job-referral.module.css";
import juggleLogo from "../../assets/images/juggle_white.png";
import rightArrow from "../../assets/images/Icons/chevron-left.png";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { useNavigate } from "react-router-dom";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import environment from "../../helper/environment";
import { Divider } from "primereact/divider";

const JobReferral: React.FC = () => {
  const [copied, setCopied] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const navigate = useNavigate();

  const getShareText = () => {
    try {
      const userInfo = sessionInfo.data["country"];
      const referralCode = sessionInfo.data["referralCode"];
      const referralLink = `https://juggle.to/${userInfo}/r/${referralCode}`;
      return `Hi, I love using Juggle St and I think you will too. If you become a member, we will both get a bonus! ${referralLink}`;
    } catch (error) {
      console.error("Error generating referral link:", error);
      return "Error generating referral link";
    }
  };

  const getReferralLink = () => {
    try {
      const userInfo = sessionInfo.data["country"];
      const referralCode = sessionInfo.data?.["referralCode"];
      return `https://juggle.to/${userInfo}/r/${referralCode}`;
    } catch (error) {
      console.error("Error generating referral link:", error);
      return "Unable to generate referral link";
    }
  };

  const handleCopyLink = () => {
    try {
      const shareText = getShareText();

      navigator.clipboard
        .writeText(shareText)
        .then(() => {
          setCopied(true);
          setIsDisabled(true);

          setTimeout(() => {
            setCopied(false);
            setIsDisabled(false);
          }, 500);
        })
        .catch((err) => {
          console.error("Failed to copy text: ", err);
          setIsDisabled(false);
        });
    } catch (error) {
      console.error("Error copying referral link:", error);
      setIsDisabled(false);
    }
  };
    const handleNagivateToTerms = () => {
      window.location.href =
        "https://www." +
        environment.getDomainName(window.location.hostname) +
        "/referral-terms";
    };

  // const renderHeader = () => (
  //   <header
  //     className={`w-full flex align-items-center justify-content-between border-round-bottom-lg overflow-hidden ${styles.headerGradient}`}
  //   >
  //     <img className={styles.juggleLogo} src={juggleLogo} alt="juggle logo"  onClick={handleBackHomeClick}/>
  //     <div className="h-full w-max select-none flex align-items-center justify-content-around gap-3 px-3 cursor-pointer hover:opacity-80"></div>
  //   </header>
  // );
  const handleBackHomeClick = () => {
    if (clientType === "0") {
      navigate("/helper-home");
    } else {
      navigate("/parent-home");
    }
  };
  const referralLink = getReferralLink();

  return (
    <div className=" w-full ">
      {/* {renderHeader()} */}
      <main className={styles.ReferMainSection}>
        <div
          style={{ display: "flex", flexDirection: "column", flexGrow: "1" }}
        >
          {/* <button
            className={styles.backBtn}
            onClick={handleBackHomeClick}
          >
            <img src={rightArrow} alt="rightArrow" width="8px" height="12px" />
            Back home
          </button> */}

          <div >
            <div >
            <h2 className={styles.ReferHeader}>
            Refer a Friend
              </h2>
              <Divider className="my-3"/>
              <h2 className={styles.ReferHeader}>
                Earn $10 for each friend you refer
              </h2>
              <p className={styles.referInstruct}>
                Spread the word and receive $10 for each friend who becomes a
                Juggle Street member. Your friend will also get $10.
              </p>
              <div className={styles.ReferDivOne}>
                <div className={styles.buttonContainer}>
                  <div
                    style={{
                      fontSize: "15px",
                      fontWeight: "300",
                      color: "#585858",
                    }}
                  >
                    {referralLink}
                  </div>
                  <button
                    onClick={handleCopyLink}
                    className={styles.referBtn}
                    disabled={isDisabled}
                  >
                    {copied ? "Link copied!" : "Copy link"}
                  </button>
                </div>
                <p
                  style={{
                    fontSize: "15px",
                    fontWeight: "300",
                    color: "#585858",
                  }}
                >
                  Copy or share this link with friends
                </p>
              </div>
              <div className={styles.ReferDivTwo}>
                <p>
                  • Your referral bonus is a $10 certificate to spend with one
                  of many national retailers or donate to charity.
                </p>
                <p>
                  • Your friends receive a $10 discount off their Juggle St
                  membership.
                </p>
                <p>
                  • Once your friend has completed their membership your digital
                  gift certificate will be emailed to you.
                </p>
              </div>
              <button
                className={styles.termBtn}
                onClick={handleNagivateToTerms}
              >
               See referral terms here
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default JobReferral;
