import { useState, useEffect } from 'react';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';

export interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  statusCode: number; // Add status code for filtering
}

interface TimesheetApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
  2: "Adjusted Timesheet", // Status 2 for adjusted-timesheets
  3: "Awaiting Approval",  // Status 3 for awaiting-approval
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

// Remove static data - we'll use real API call

export const useFilteredTimesheetData = () => {
  const [allTimesheetData, setAllTimesheetData] = useState<TimesheetEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  const fetchTimesheetData = async (): Promise<void> => {
    console.log('🔄 Starting timesheet data fetch from API...');
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getTimeSheetDetails(
          (response: TimesheetApiItem[]) => {
            console.log("API Response getTimeSheetDetails:", response);

            if (!Array.isArray(response)) {
              console.warn("Expected array response, got:", typeof response);
              setAllTimesheetData([]);
              resolve();
              return;
            }

            const mappedData: TimesheetEntry[] = response.map((item) => ({
              id: item.id,
              status: statusMap[item.status] || "Unknown",
              statusCode: item.status, // Keep original status code for filtering
              type: jobTypeMap[item.jobType] || "Unknown",
              date: new Date(item.jobDate).toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }),
              location: item.formattedAddress,
              userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
              originalImageUrl: item.originalImageUrl,
            }));

            console.log("Mapped timesheet data from API:", mappedData);
            console.log("Status breakdown:", {
              status1: mappedData.filter(item => item.statusCode === 1).length,
              status2: mappedData.filter(item => item.statusCode === 2).length,
              status3: mappedData.filter(item => item.statusCode === 3).length,
            });

            setAllTimesheetData(mappedData);
            console.log('✅ Timesheet data loaded successfully from API, count:', mappedData.length);
            resolve();
          },
          (error: any) => {
            console.error('Error fetching timesheet data from API:', error);
            setError(error?.message || 'Failed to fetch timesheet data');
            reject(error);
          }
        );
      });

    } catch (err) {
      console.error('Fetch timesheet data failed:', err);
      setError('Failed to fetch timesheet data');
    } finally {
      console.log('🔄 Finishing timesheet data fetch...');
      setIsLoading(false);
      disableLoader();
      console.log('✅ Loader disabled, isLoading set to false');
    }
  };

  useEffect(() => {
    fetchTimesheetData();
  }, []);

  const refreshData = () => {
    fetchTimesheetData();
  };

  // Filter data by status
  const getTimesheetsByStatus = (statusCode: number): TimesheetEntry[] => {
    return allTimesheetData.filter(item => item.statusCode === statusCode);
  };

  // Get data for specific tabs
  const adjustedTimesheetData = getTimesheetsByStatus(2); // Status 2
  const awaitingApprovalData = getTimesheetsByStatus(3); // Status 3

  return {
    allTimesheetData,
    adjustedTimesheetData,
    awaitingApprovalData,
    isLoading,
    error,
    refreshData,
    getTimesheetsByStatus
  };
};

export default useFilteredTimesheetData;
