# Static Timesheet Implementation Guide

## 🎯 **What Was Implemented**

I've successfully implemented static timesheet data for the two routes with proper status filtering and NoJobsCard integration:

### ✅ **1. Static Data Hook (`useStaticTimesheetData.ts`)**
- **Static timesheet data** based on your API response structure
- **Status filtering**: Status 2 for adjusted-timesheets, Status 3 for awaiting-approval
- **Proper badge counts** for each tab
- **NoJobsCard integration** for empty states

### ✅ **2. Enhanced TimeSheet Component**
- **Route-specific data display** based on status codes
- **Proper empty state handling** with descriptive messages
- **Dynamic badge counts** reflecting actual data

### ✅ **3. Status-Based Filtering**
- **Status 2**: Adjusted Timesheets (`/parent-home/timesheet/adjusted-timesheets`)
- **Status 3**: Awaiting App<PERSON>al (`/parent-home/timesheet/awaiting-approval`)
- **Status 1**: Awaiting Confirmation (existing functionality)

## 🔧 **Implementation Details**

### **1. Static Data Structure**
```typescript
const staticTimesheetData: TimesheetApiItem[] = [
  {
    id: 1,
    status: 2, // Adjusted timesheet
    jobType: 2,
    jobDate: "2025-06-24T00:00:00",
    formattedAddress: "Marrickville, Nsw, 2204, Arncliffe NSW 2205",
    firstName: "Natasha",
    lastName: "Kelley",
    originalImageUrl: "https://jsstagingaue.blob.core.windows.net/images/..."
  },
  {
    id: 2,
    status: 2, // Adjusted timesheet
    // ... more data
  },
  {
    id: 3,
    status: 3, // Awaiting approval
    // ... more data
  },
  {
    id: 4,
    status: 3, // Awaiting approval
    // ... more data
  }
];
```

### **2. Status Mapping**
```typescript
const statusMap: { [key: number]: string } = {
  [c.ApprovalStatus.AWAITING_APPROVAL]: "Awaiting Your Approval",
  2: "Adjusted Timesheet", // Status 2 for adjusted-timesheets
  3: "Awaiting Approval",  // Status 3 for awaiting-approval
};
```

### **3. Hook Usage**
```typescript
const { 
  adjustedTimesheetData,    // Status 2 entries
  awaitingApprovalData,     // Status 3 entries
  isLoading: staticLoading,
  refreshData: refreshStaticData 
} = useStaticTimesheetData();
```

### **4. Enhanced TimesheetList Component**
```typescript
const TimesheetList = ({ 
  data, 
  emptyMessage 
}: { 
  data: TimesheetEntry[]; 
  emptyMessage: string;
}) => (
  <>
    {data.length > 0 ? (
      data.map((entry, index) => (
        <TimeSheetCard
          key={index}
          status={entry.status}
          type={entry.type}
          date={entry.date}
          location={entry.location}
          userName={entry.userName}
          userImage={entry.originalImageUrl}
          onReview={() => handleReview(entry)}
        />
      ))
    ) : (
      <NoJobsCard description={emptyMessage} />
    )}
  </>
);
```

## 📊 **Tab Configuration**

### **Tab 1: Awaiting Confirmation**
- **Route**: `/parent-home/timesheet/awaiting-confirmation`
- **Data Source**: `useTimesheetData()` (existing API call)
- **Status**: Status 1 (existing)
- **Badge Count**: `timesheetData.length`
- **Empty Message**: "No timesheets awaiting confirmation"

### **Tab 2: Adjusted Timesheets**
- **Route**: `/parent-home/timesheet/adjusted-timesheets`
- **Data Source**: `useStaticTimesheetData()` filtered by status 2
- **Status**: Status 2
- **Badge Count**: `adjustedTimesheetData.length` (currently 2)
- **Empty Message**: "No adjusted timesheets available"

### **Tab 3: Awaiting Approval**
- **Route**: `/parent-home/timesheet/awaiting-approval`
- **Data Source**: `useStaticTimesheetData()` filtered by status 3
- **Status**: Status 3
- **Badge Count**: `awaitingApprovalData.length` (currently 2)
- **Empty Message**: "No timesheets awaiting approval"

## 🎯 **Current Data Display**

Based on the static data, here's what each tab will show:

### **Adjusted Timesheets Tab (Status 2):**
```
┌─────────────────────────────────────────────────────────┐
│ 📋 Adjusted Timesheet                                  │
│ Natasha K                                               │
│ One Of Job • 24 June 2025                             │
│ Marrickville, Nsw, 2204, Arncliffe NSW 2205          │
│                                        [Review] Button │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ 📋 Adjusted Timesheet                                  │
│ John S                                                  │
│ Unspecified • 25 June 2025                            │
│ Sydney, NSW, 2000                                      │
│                                        [Review] Button │
└─────────────────────────────────────────────────────────┘

Badge Count: 2
```

### **Awaiting Approval Tab (Status 3):**
```
┌─────────────────────────────────────────────────────────┐
│ 📋 Awaiting Approval                                   │
│ Sarah J                                                 │
│ Before School Care • 26 June 2025                     │
│ Melbourne, VIC, 3000                                   │
│                                        [Review] Button │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ 📋 Awaiting Approval                                   │
│ Mike W                                                  │
│ One Of Job • 27 June 2025                             │
│ Brisbane, QLD, 4000                                    │
│                                        [Review] Button │
└─────────────────────────────────────────────────────────┘

Badge Count: 2
```

### **Empty State (NoJobsCard):**
If no data matches the status criteria:
```
┌─────────────────────────────────────────────────────────┐
│                    📄 No Jobs                          │
│                                                         │
│           No adjusted timesheets available             │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 🔄 **How to Customize**

### **1. Add More Static Data:**
```typescript
// In useStaticTimesheetData.ts, add more entries to staticTimesheetData
{
  id: 5,
  status: 2, // or 3
  jobType: 1,
  jobDate: "2025-06-28T00:00:00",
  formattedAddress: "Your Address",
  firstName: "Your Name",
  lastName: "Last",
  originalImageUrl: "Your Image URL"
}
```

### **2. Change Empty Messages:**
```typescript
// In TimeSheet.tsx, update the emptyMessage props
<TimesheetList 
  data={adjustedTimesheetData} 
  emptyMessage="Your custom message for adjusted timesheets" 
/>
```

### **3. Replace with Real API Call:**
```typescript
// In useStaticTimesheetData.ts, replace static data with API call
Service.getTimeSheetDetails(
  (response: any) => {
    // Process response and filter by status
    const filteredData = response.filter(item => item.status === targetStatus);
    setAllTimesheetData(mappedData);
  },
  (error: any) => {
    console.error('Error:', error);
  }
);
```

## 🧪 **Testing**

### **1. Navigate to Routes:**
- `/parent-home/timesheet/adjusted-timesheets` → Should show 2 cards with status "Adjusted Timesheet"
- `/parent-home/timesheet/awaiting-approval` → Should show 2 cards with status "Awaiting Approval"

### **2. Verify Badge Counts:**
- **Adjusted Timesheets tab**: Badge shows "2"
- **Awaiting Approval tab**: Badge shows "2"
- **Awaiting Confirmation tab**: Badge shows actual count from API

### **3. Test Empty States:**
- Modify static data to have no status 2 entries → Should show NoJobsCard
- Modify static data to have no status 3 entries → Should show NoJobsCard

## 🎯 **Benefits**

### **✅ Status-Based Filtering:**
- Each tab shows only relevant timesheet entries
- Proper status mapping for user-friendly display
- Dynamic badge counts reflecting actual data

### **✅ Consistent UI:**
- Same TimeSheetCard component across all tabs
- Consistent NoJobsCard for empty states
- Proper loading states and error handling

### **✅ Maintainable Code:**
- Reusable TimesheetList component
- Centralized static data management
- Easy to replace with real API calls

## 🚀 **Summary**

Your timesheet tabs now:
- ✅ **Use static data** with proper status filtering
- ✅ **Show TimeSheetCard** for status 2 and 3 entries
- ✅ **Display NoJobsCard** when no data matches criteria
- ✅ **Show correct badge counts** for each tab
- ✅ **Handle empty states** with descriptive messages

The implementation is ready and working with static data that can easily be replaced with real API calls! 🎉
