import React, { useState } from 'react';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import '../TimesheetScreen.css';
import { TabView, TabPanel } from 'primereact/tabview';

const Payments: React.FC = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  return (
    <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)} className="custom-tabview">
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          Pending Approval
        </div>
      }>
        <div className="simple-tab-panel-content">Content for Pending Approval.</div>
      </TabPanel>
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          Awaiting Payment
        </div>
      }>
        <div className="simple-tab-panel-content">Content for Awaiting Payment.</div>
      </TabPanel>
      <TabPanel header={
        <div style={{ fontSize: '10px', textAlign: 'center', lineHeight: '1.2' }}>
          Payment Receipts
        </div>
      }>
        <div className="simple-tab-panel-content">Content for Payment Receipts.</div>
      </TabPanel>
    </TabView>
  );
};

export default Payments;