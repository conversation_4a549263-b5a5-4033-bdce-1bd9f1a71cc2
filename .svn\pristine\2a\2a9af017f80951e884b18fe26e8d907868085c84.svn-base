import React, { useState } from "react";
import styles from "../../styles/weekly-schedule-table.module.css";
import { Divider } from "primereact/divider";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import clockStartEnd from "../../.././../assets/images/Icons/clockend.png";
import overtime from "../../.././../assets/images/Icons/payments.png";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { useNavigate } from "react-router-dom";
import useIsMobile from "../../../../hooks/useIsMobile";
import calender from "../../../../assets/images/Icons/calender.png";
import CustomDialog from "../../../../commonComponents/CustomDialog";
import ProviderProfile from "../../../Parent/ProviderProfile/ProviderProfile";
export interface WeeklyTableRow {
  dayOfWeek: number;
  day: string;
  startTime: string;
  endTime: string;
  shiftId: number;
  price: number;
  awardedAplicantId: number | null;
  applicants: Array<{
    applicantId: number;
    availabilityId: number;
    publicName: string;
    imageSrc: string;
    status: number;
  }>;
  onClick?: () => void;
  onClickInvite?: () => void;
  onClickSchedule?: () => void;
  jobType?: number; // Added
  subJobType?: number; // Added
}

interface WeeklyScheduleTableProps {
  rows: WeeklyTableRow[];
  jobDate?: Date;
  onChat?: (id: Number) => void;
  onViewProfile?: (row: WeeklyTableRow) => void;
  onInviteHelpers?: (row: WeeklyTableRow) => void;
  onViewWeeklySchedule?: (row: WeeklyTableRow) => void;
}

const JobHistoryTable: React.FC<WeeklyScheduleTableProps> = ({
  rows,
  jobDate,
  onViewProfile,
  onInviteHelpers,
  onViewWeeklySchedule,
  onChat,
}) => {
  const navigate = useNavigate();
  const { isMobile } = useIsMobile();
  const getAwardedAplicant = (id: number) => {
    for (const row of rows) {
      for (const app of row.applicants) if (app.applicantId === id) return app;
    }
  };
  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);

  const redirectAfterHome = (id: number) => {
    if (isMobile) {
      const navigatePArams = new URLSearchParams();
      navigatePArams.set("id", id.toString());
      const clientType = utils.getCookie(CookiesConstant.clientType);

      if (clientType === "2") {
        navigate(
          `/business-home/provider-profile?${navigatePArams.toString()}`
        );
      } else if (clientType === "1") {
        navigate(`/parent-home/provider-profile?${navigatePArams.toString()}`);
      } else {
        console.warn("Unknown clientType, no navigation performed.");
      }
    } else {
      setSelectedProviderId(id);
      setShowPopup(true);
    }
  };
  const handleCloseProfilePopup = () => {
    setShowPopup(false);
  };
  function formatTimeTo12Hour(time: string): string {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  }
  function formatDate(date: Date): string {
    const getOrdinalSuffix = (day: number): string => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });

    return `${day}${getOrdinalSuffix(day)} of ${month}`;
  }
  const calculateHours = (startTime: string, endTime: string): number => {
    const [startHours, startMinutes] = startTime.split(":").map(Number);
    const [endHours, endMinutes] = endTime.split(":").map(Number);

    // Convert to minutes for easier calculation
    const startInMinutes = startHours * 60 + startMinutes;
    const endInMinutes = endHours * 60 + endMinutes;

    // Calculate total minutes difference
    let totalMinutes = endInMinutes - startInMinutes;

    // If end time is less than start time, assume it crosses midnight
    if (totalMinutes < 0) {
      totalMinutes += 24 * 60; // Add 24 hours in minutes
    }

    // Convert to hours
    const totalHours = totalMinutes / 60;
    return totalHours;
  };

  function getJobName(jobType, subJobType = 0): { job: string; shift: string[] } {
    const mappings = {
      jobType: {
        1: { job: 'Ad-hoc Childcare', shift: ['Morning', 'Afternoon'] },
        2: { job: 'Recurring Childcare', shift: ['Morning', 'Afternoon'] },
        4: {
          job: 'Before School Care',
          shift: ['Before School', 'Second Shift'],
        },
        8: {
          job: 'After School Care',
          shift: ['Before School', 'Second Shift'],
        },
        12: {
          job: 'Before & After School Care',
          shift: ['Before School', 'After School'],
        },
        64: {
          job: 'Primary School Tutoring',
          shift: ['Before School', 'After School'],
        },
        128: {
          job: 'High School Tutoring',
          shift: ['Before School', 'After School'],
        },
        256: { job: 'Odd Job', shift: ['Morning', 'Afternoon'] },
      },
      subJobType: {
        1: { job: 'Laundry', shift: ['Morning', 'Afternoon'] },
        2: { job: 'Errand Running', shift: ['Morning', 'Afternoon'] },
        4: { job: 'Outdoor Chores', shift: ['Morning', 'Afternoon'] },
        8: { job: 'Help for the Elderly', shift: ['Morning', 'Afternoon'] },
        16: { job: 'Other Odd Jobs', shift: ['Morning', 'Afternoon'] },
      },
      recurringChildcare: {
        2: { job: 'Daytime Nanny', shift: ['Nanny Shift', 'Second Shift'] },
        4: {
          job: 'Before School Care',
          shift: ['Before School', 'Second Shift'],
        },
        8: {
          job: 'After School Care',
          shift: ['After School', 'Second Shift'],
        },
        12: {
          job: 'Before & After School Care',
          shift: ['Before School', 'After School'],
        },
      },
    };

    if (jobType === 2) {
      return subJobType === 0
        ? { job: 'Daytime Nanny', shift: ['Nanny Shift', 'Second Shift'] }
        : mappings.recurringChildcare[jobType] || {
          job: 'Recurring Childcare',
          shift: ['Morning', 'Afternoon'],
        };
    }

    if (jobType === 256 && subJobType > 0) {
      return (
        mappings.subJobType[subJobType] || {
          job: 'Odd Job',
          shift: ['Morning', 'Afternoon'],
        }
      );
    }

    return (
      mappings.jobType[jobType] || {
        job: 'Unknown Job',
        shift: ['Morning', 'Afternoon'],
      }
    );
  }
  return !isMobile ? (
    <>
      <div className="grid">
        <div className="col-12">
          <div className={styles.WeeklyTableContainer}>
            {/* Header */}
            {/* <div className="grid">
              <div className="col-12">
                <div className={` grid`}>
                  <div className="col-3 flex align-items-center">
         
                    <div className={styles.WeeklyHeaderItem}>Job Day</div>
                  </div>
                  <div className="col-3 flex align-items-center">
         
                    <div className={styles.WeeklyHeaderItem}>Job Time</div>
                  </div>
                  <div className="col-3 flex align-items-center">
             
                    <div className={styles.WeeklyHeaderItem}>Hours</div>
                  </div>
                  <div className="col-3 flex align-items-center">
              
                    <div className={styles.WeeklyHeaderItem}>Rate</div>
                  </div>
                </div>
              </div>
            </div> */}

            {/* Rows */}
            {rows.map((row, index) => {
              const isLastShiftOfDay = index === rows.length - 1 || rows[index + 1]?.day !== row.day;
              const jobInfo = getJobName(row.jobType, row.subJobType);
              const isFirstShiftOfDay = index === 0 || rows[index - 1]?.day !== row.day;
              const shiftName = isFirstShiftOfDay ? jobInfo.shift[0] : jobInfo.shift[1];
              return (
                <div key={index} className="grid">
                  <div className="col-12">
                    <div className={`${styles.WeeklyRowHistroy} grid`}>
                      {/* Date Column */}
                      <div className="col-2 flex align-items-center">
                        <div className={`flex flex-row gap-1 ${styles.dateCellHistroy}`}>
                          <p className="m-0 text-color-secondary text-l font-bold">
                            {row.day}
                          </p>
                          <p style={{ fontSize: "12px", textWrap: "nowrap", color: "#585858" }} className="p-0 m-0 ">
                            {shiftName ? `${shiftName}` : ""}
                          </p>
                        </div>
                        {/* <Divider layout="vertical" /> */}
                      </div>

                      {/* Time Column */}
                      <div className="col-3 flex align-items-center justify-content-center">
                        <div className="flex align-items-center gap-2 mx-auto">
                          <div className={`font-bold ${styles.timeGetHistroy}`}>
                            <img
                              src={clockStart}
                              alt="clockStart"
                              width="13.5px"
                              height="13.5px"
                            />
                            {formatTimeTo12Hour(row.startTime)}
                          </div>
                          <p className="mx-2 text-sm font-normal text-color-secondary">
                            to
                          </p>
                          <div className={`font-bold ${styles.timeGetHistroy}`}>
                            <img
                              src={clockStartEnd}
                              alt="clockEnd"
                              width="13.5px"
                              height="13.5px"
                            />
                            {formatTimeTo12Hour(row.endTime)}
                          </div>
                        </div>
                        {/* <Divider
                        layout="vertical"
                        className={styles.TableDivider}
                      /> */}
                      </div>

                      {/* Helper/Name Column */}
                      <div className="col-3 flex align-items-center justify-content-center gap-5">
                        <div className="flex flex-column align-items-center">
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "700",
                            }}
                            className="p-0 m-0"
                          >
                            Hours
                          </p>
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "300",
                            }}
                            className="p-0 m-0"
                          >
                            {row.startTime && row.endTime
                              ? `${calculateHours(row.startTime, row.endTime)}`
                              : "N/A"}
                          </p>
                        </div>

                        <div className="flex flex-column align-items-center">
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "700",
                            }}
                            className="p-0 m-0"
                          >
                            Rate
                          </p>
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "300",
                            }}
                            className="p-0 m-0"
                          >
                            {row.startTime && row.endTime && row.price
                              ? (() => {
                                const hours = calculateHours(row.startTime, row.endTime);
                                return hours > 0
                                  ? `$${(row.price / hours).toFixed(2)}`
                                  : "N/A";
                              })()
                              : "N/A"}
                          </p>
                        </div>


                        <div className="flex flex-column align-items-center">
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "700",
                            }}
                            className="p-0 m-0"
                          >
                            Total
                          </p>
                          <p
                            style={{
                              fontSize: "14px",
                              color: "#585858",
                              fontWeight: "300",
                            }}
                            className="p-0 m-0"
                          >
                            {`$${row.price}`}
                          </p>
                        </div>
                      </div>

                      {/* Status Column */}
                      {/* <div className="col-3 flex align-items-center justify-content-center">
                      <button className={styles.approvedStatusBtn}>
                        Job Awarded
                      </button>
                    </div> */}
                    </div>
                    {isLastShiftOfDay && <Divider className={styles.HistroyDivider} />}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <CustomDialog
        visible={showPopup}
        style={{
          width: "100%",
          maxWidth: "100%",
          height: "100%",
          maxHeight: "100%",
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          overflowY: "auto",
        }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile
          candidateId={selectedProviderId}
          onClose={handleCloseProfilePopup}
        />
      </CustomDialog>
    </>
  ) : (
    <div className={styles.cardContainerMobile}>
      {rows.map((row, index) => {
        return (
          <div key={index} className={styles.scheduleCardMobile}>
            {/* Header with day and status */}
            <div className={styles.cardHeaderMobile}>
              <h2 className={styles.dayTitleMobile}>{row.day}</h2>
              <div className={styles.statusTextMobile}>
                <span className={styles.statusDotMobile} />
                Job Awarded!
              </div>
            </div>
            <Divider className="mb-2 mt-1" />
            {/* Date */}
            <div className={styles.dateRowMobile}>
              <img src={calender} alt="calender" width="16px" height="18px" />
              {formatDate(jobDate)}
            </div>

            {/* Time */}
            <div className={styles.timeRowMobile}>
              <img
                src={clockStart}
                alt="clockStart"
                width="16px"
                height="16px"
              />
              {`${formatTimeTo12Hour(row.startTime)} to ${formatTimeTo12Hour(
                row.endTime
              )}`}
            </div>

            <div className={styles.timeRowMobile}>
              <img
                src={overtime}
                alt="clockStart"
                width="16px"
                height="16px"
              />
              <p
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "300",
                }}
                className="p-0 m-0"
              >
                Rate
              </p>
              {row.startTime && row.endTime && row.price
                ? (() => {
                  const hours = calculateHours(row.startTime, row.endTime);
                  return hours > 0
                    ? `$${(row.price / hours).toFixed(2)}`
                    : "N/A";
                })()
                : "N/A"}
            </div>
            <div className={styles.timeRowMobile}>

              <img
                src={clockStart}
                alt="clockStart"
                width="16px"
                height="16px"
              />
              <p
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "300",
                }}
                className="p-0 m-0"
              >
                Total
              </p>
              {`$${row.price}`}
            </div>

            {/* <Divider className=" mt-2" /> */}
            {/* Helper section with chat button */}
            {/* {helper && (
              <div className={styles.helperSectionMobile}>
                <div className={styles.helperInfoMobile}>
                  <img
                    src={helper.imageSrc || "/default-avatar.png"}
                    alt={helper.publicName}
                    className={styles.helperImageMobile}
                  />

                  <span className={styles.ImgcheckmarkMobile}>✓</span>

                  <div className={styles.helperDetailsMobile}>
                    <h3 className={styles.helperNameMobile}>
                      {helper.publicName}
                    </h3>
                    <button
                      // onClick={() => redirectAfterHome(helper.applicantId)}
                      className={styles.viewProfileMobile}
                    >
                      View profile
                    </button>
                  </div>
                </div>
                <button
                  onClick={() => onChat(helper.applicantId)}
                  className={styles.chatButtonMobile}
                >
                  Chat
                </button>
              </div>
            )} */}
          </div>
        );
      })}
    </div>
  );
};

export default JobHistoryTable;
