/* TimeSheet Card Styles */
.card {
  background: #F0F4F7B2;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s ease;
  overflow: hidden;
  width: 100%;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.header {
  display: flex;
  flex-direction: row;
  gap: 4px;
  margin-bottom: 8px;
  align-items: center;
}

.status {
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
  background: transparent;
  padding: 0;
  border-radius: 0;
  width: fit-content;
  text-decoration: underline;
  border: none;
}

.infoRow {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.infoRow img {
  flex-shrink: 0;
  opacity: 0.8;
}

.spantag {
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  line-height: 1.4;
  word-break: break-word;
}

.user {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  min-width: 80px;
  text-align: center;
}

.avatar {
  font-size: 8px;
  color: #6c757d;
  background: #e9ecef;
  border-radius: 50%;
  padding: 4px;
  height: 49px;
  width: 45px;
}

.userName {
  font-size: 14px;
  font-weight: 600;
  color: #585858;
  text-decoration: underline;
  text-align: center;
  word-break: break-word;
  line-height: 1.2;
}

.button {
  background: #FFA500;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 8px 32px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 16px 0;
  min-width: 200px;
  width: 90%;
}


/* Responsive Design */
/* @media (max-width: 768px) {

  
  .cardContent {
    gap: 10px;
  }
  
  .infoRow {
    gap: 6px;
    margin-bottom: 4px;
  }
  
  .spantag {
    font-size: 13px;
  }
  
  .user {
    min-width: 70px;
    gap: 6px;
  }
  
  .avatar {
    font-size: 40px;
  }
  
  .userName {
    font-size: 13px;
  }
  
  .button {
    padding: 10px 20px;
    font-size: 13px;
    min-width: 120px;
  }
} */

/* @media (max-width: 480px) {

  .header {
    margin-bottom: 6px;
  }
  
  .status {
    font-size: 13px;
    padding: 3px 6px;
  }
  
  .cardContent {
    gap: 8px;
  }
  
  .infoRow {
    gap: 5px;
    margin-bottom: 3px;
  }
  
  .spantag {
    font-size: 12px;
  }
  
  .user {
    min-width: 60px;
    gap: 4px;
  }
  
  .avatar {
    font-size: 36px;
  }
  
  .userName {
    font-size: 12px;
  }
  
  .button {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 100px;
    margin: 12px 0;
  }
} */

/* Status variants */
.status.pending {
  color: #FF8C00;
  background: rgba(255, 140, 0, 0.1);
  border-color: rgba(255, 140, 0, 0.2);
}

.status.approved {
  color: #179D52;
  background: rgba(23, 157, 82, 0.1);
  border-color: rgba(23, 157, 82, 0.2);
}

.status.declined {
  color: #DC3545;
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
}

/* Animation for card entrance */
.card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Divider styling */
.card :global(.p-divider) {
  margin: 0;
  border-color: #f0f0f0;
}

.card:focus-within {
  box-shadow: 0 4px 16px rgba(23, 157, 82, 0.15);
}
