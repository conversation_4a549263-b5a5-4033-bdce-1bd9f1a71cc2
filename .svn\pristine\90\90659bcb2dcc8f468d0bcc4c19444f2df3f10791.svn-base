import { useEffect, useMemo, useState } from "react";
import c from "../../../../../helper/juggleStreetConstants";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../store";
import { Schedule } from "./Interface";
import { Divider } from "primereact/divider";
import ClockForwardMobile from "../../../../../assets/images/Icons/clock-end-mobile.png";
import ClockStartMobile from "../../../../../assets/images/Icons/clock-forwardbig.png";
import NumberInput from "../../../../../commonComponents/NumberInput";
import styles from "../../../../Common/styles/job-pricing.module.css";
// import ClockForward from "../../../../../assets/images/Icons/clock-forward.png";
import ClockForward from "../../../../../assets/images/Icons/clock-forward.png";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import Dollar from "../../../../../assets/images/Icons/Dollar.png";
import JobShifts from "./JobShifts";
import { GoBack, Next } from "../Buttons";
import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

function PricingPaymentsStep1() {
  const { isMobile } = useIsMobile();
  return isMobile ? <PricingPaymentsStep1Mobile /> : <PricingPaymentsStep1Web />;
}

export default PricingPaymentsStep1;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  const { isMobile } = useIsMobile();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  const [price, setPrice] = useState<string>("");
  const [showAll, setShowAll] = useState<boolean>(false);
  const [shiftsFilled, setShiftsFilled] = useState<boolean>(false);
  const [priceEditDisabled, setPriceEditDisabled] = useState<boolean>(true);
  const [guidePopup, setGuidePopup] = useState<boolean>(false);
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [message, setMessage] = useState<string>("");
  const notBusiness = sessionInfo.data?.["client"]["clientType"] == c.clientType.INDIVIDUAL;
  function getTimeDifferenceInHourMinuteFormat(start: string, end: string): number {
    const [startHours, startMinutes] = start.split(":").map(Number);
    const [endHours, endMinutes] = end.split(":").map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    let differenceInMinutes = endTotalMinutes - startTotalMinutes;
    if (differenceInMinutes < 0) {
      differenceInMinutes += 24 * 60;
    }

    const hours = Math.floor(differenceInMinutes / 60);
    const minutes = differenceInMinutes % 60;

    return parseFloat((hours + minutes / 60).toFixed(2));
  }

  useEffect(() => {
    setPrice(payload.price ? payload.price.toString() : "");
    setShowAll(payload.price !== null);
    setPriceEditDisabled(payload.price === null);

    const updatedSchedules =
      payload.weeklySchedule?.["weeklyScheduleEntries"]?.map((entry) => ({
        ...entry,
        hourlyPrice: Number(entry.hourlyPrice),
        shifts: entry.shifts.map((shift) => ({
          ...shift,
          hourlyPrice: Number(shift.hourlyPrice),
        })),
      })) || [];

    setSchedules(updatedSchedules);
  }, [payload]);

  const metadata = useMemo(() => {
    let totalCost = 0;
    let totalHours = 0;

    const updatedSchedules = schedules.map((schedule) => {
      const updatedShifts = schedule.shifts.map((shift) => {
        const duration = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
        const jobHourlyPrice = shift.hourlyPrice || Number(price);
        const jobPrice = Number(jobHourlyPrice) * duration;

        totalCost += jobPrice;
        totalHours += duration;

        return {
          ...shift,
          price: jobPrice,
          hourlyPrice: jobHourlyPrice,
        };
      });

      return { ...schedule, shifts: updatedShifts };
    });

    return {
      cost: parseFloat(totalCost.toFixed(2)),
      hours: parseFloat(totalHours.toFixed(2)),
      schedules: updatedSchedules,
    };
  }, [schedules, price]);

  useEffect(() => {
    let disable = false;
    let message = "";

    for (const sc of schedules) {
      for (const sh of sc.shifts) {
        if (sh.hourlyPrice === 0 || sh.hourlyPrice === null) {
          disable = true;
          message = "Please Enter Valid Price";
          break;
        }

        if (![64, 128].includes(payload.jobType) && sh.hourlyPrice < 1) {
          disable = true;
          message = "Minimum hourly rate is $1.";
          break;
        }
      }
      if (disable) break;
    }

    setMessage(message);
    setShiftsFilled(disable);
  }, [schedules, payload.jobType]);

  const handleKeyDown = (e: any) => {
    if (["Enter", "Done", "Go", "Next"].includes(e.key) || e.keyCode === 13) {
      setPriceEditDisabled((prev) => !prev);
      e.target.blur();
    }
  };

  const displayLowRateNotice = () => {
    const isRecurring =
      payload.jobType === c.jobType.NANNYING ||
      payload.jobType === c.jobType.BEFORE_SCHOOL_CARE ||
      payload.jobType === c.jobType.AFTER_SCHOOL_CARE ||
      payload.jobType === c.jobType.BEFORE_AFTER_SCHOOL_CARE;

    const basePrice = Number(price);
    if (!basePrice) return false;
    const hourlyPrice = parseFloat(basePrice.toString());
    const isLowRate = hourlyPrice > 0 && hourlyPrice < 25;

    if (isRecurring && isLowRate) {
      return true;
    }

    for (const schedule of schedules) {
      for (const shift of schedule.shifts) {
        const shiftPrice = shift.hourlyPrice || basePrice;
        if (!shiftPrice) continue;

        const parsedShiftPrice = parseFloat(shiftPrice.toString());
        const duration = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);
        const isShiftLowRate = parsedShiftPrice > 0 && parsedShiftPrice < 25;

        if (isRecurring && isShiftLowRate && duration < 2) {
          return true;
        }
      }
    }

    return false;
  };

  const displayPriceGuide = sessionInfo.data?.["client"]["clientType"] === c.clientType.INDIVIDUAL || sessionInfo.data?.["isJobManagerAccount"];
  const getDurationInHours = (start, end) => {
    const [startHour, startMin] = start.split(":").map(Number);
    const [endHour, endMin] = end.split(":").map(Number);

    const startTotalMinutes = startHour * 60 + startMin;
    const endTotalMinutes = endHour * 60 + endMin;

    return (endTotalMinutes - startTotalMinutes) / 60;
  };

  const displayTutoringLowRateNotice = (): boolean => {
    // Only for tutoring jobs
    if (![64, 128].includes(payload.jobType)) return false;

    const basePrice = Number(price);
    if (!basePrice) return false;

    for (const schedule of schedules) {
      for (const shift of schedule.shifts) {
        const shiftPrice = shift.hourlyPrice ?? basePrice;
        if (!shiftPrice) continue;

        const parsedShiftPrice = Number(shiftPrice);
        const duration = getTimeDifferenceInHourMinuteFormat(shift.jobStartTime, shift.jobEndTime);

        // Determine minimum rates based on duration
        let minRate;
        if (duration <= 1) {
          // 1-hour lesson minimums:
          // High School Student: $40
          // High School Graduate: $60
          // Certified Teacher: $80
          minRate = 40; // Using the lowest minimum (HS Student)
        } else {
          // 2+ hours lesson minimums:
          // High School Student: $30
          // High School Graduate: $50
          // Certified Teacher: $60
          minRate = 30; // Using the lowest minimum (HS Student)
        }

        if (parsedShiftPrice < minRate) {
          return true; // Show notice if price is below minimum
        }
      }
    }

    return false;
  };

  return {
    payload,
    next,
    prev,
    setpayload,
    getTimeDifferenceInHourMinuteFormat,
    displayLowRateNotice,
    displayPriceGuide,
    guidePopup,
    message,
    metadata,
    handleKeyDown,
    shiftsFilled,
    setShowAll,
    showAll,
    isMobile,
    setPrice,
    price,
    setSchedules,
    setGuidePopup,
    schedules,
    setPriceEditDisabled,
    priceEditDisabled,
    notBusiness,
    displayTutoringLowRateNotice,
  };
};

const PricingPaymentsStep1Web = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    displayLowRateNotice,
    displayPriceGuide,
    message,
    metadata,
    shiftsFilled,
    setShowAll,
    showAll,
    setPrice,
    price,
    setSchedules,
    schedules,
    displayTutoringLowRateNotice,
    notBusiness
  } = useJobTypeHook();
  return (
    <div className=" h-full flex flex-column align-items-center oveflow-hidden overflow-y-auto relative">
      <div className="w-full flex-grow-1 flex justify-content-center">
        <div
          className="flex flex-column"
          style={{
            width: "70%",
            paddingTop: "25px",
          }}
        >
          {![64, 128].includes(payload.jobType) ? (
            <div
              className="flex"
              style={{
                width: "100%",
                border: "1px solid #179D52",
                borderRadius: "33px",
                minHeight: "145px",
                backgroundColor: "rgba(240, 244, 247, 0.3)",
                padding: "15px 30px",
              }}
            >
              {notBusiness ? (
                <>
                  <div style={{ width: "45%" }}>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: 700,
                        fontSize: "30px",
                        color: "#585858",
                        textWrap: "wrap",
                      }}
                    >
                      Job Price Guide
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      <span className="inline-block text-left">
                        <span
                          className="inline-block mr-1"
                          style={{
                            fontWeight: "700",
                          }}
                        >
                          Note:
                        </span>
                        Jobs priced below the Price Guide can be difficult to fill.
                      </span>
                    </p>
                  </div>
                  <div className="flex-grow-1 flex flex-column justify-content-around">
                    <div className="w-full flex">
                      <div className="w-6 flex flex-column pl-6">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 700,
                            fontSize: "20px",
                            color: "#585858",
                          }}
                        >
                          Shift Duration
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 400,
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          2 Hours
                        </p>
                      </div>
                      <div className="w-6 flex flex-column pl-6">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 700,
                            fontSize: "20px",
                            color: "#585858",
                          }}
                        >
                          Base rate
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 400,
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          Min $35+ p/h
                        </p>
                      </div>
                    </div>
                    <div
                      style={{
                        width: "88%",
                        backgroundColor: "#F1F1F1",
                        height: "1px",
                        alignSelf: "end",
                      }}
                    />
                    <div className="w-full flex">
                      <div className="w-6 flex flex-column pl-6">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 400,
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          3+ Hours
                        </p>
                      </div>
                      <div className="w-6 flex flex-column pl-6">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 400,
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          Av. $25-$35+ p/h
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div style={{ color: "#585858", fontSize: "18px" }}>
                  For details of the childcare wages stipulated in the Children Services Award
                  please refer to the government’s Fair Work Commission website.
                </div>
              )}
            </div>
          ) : (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "minmax(200px, 30%) 1fr",
                gap: "20px",
                width: "100%",
                border: "1px solid #585858",
                borderRadius: "15px",
                minHeight: "145px",
                backgroundColor: "rgba(240, 244, 247, 0.3)",
                padding: "15px 30px 15px 15px",
                boxSizing: "border-box",
                alignItems: "baseline",
              }}
            >
              {/* Left Column - Price Guide */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                }}
              >
                <p
                  style={{
                    margin: 0,
                    padding: 0,
                    fontWeight: 700,
                    fontSize: "20px",
                    color: "#585858",
                    wordWrap: "break-word",
                  }}
                >
                  Tutoring Price Guide
                </p>
                <p
                  style={{
                    margin: 0,
                    padding: 0,
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#585858",
                    wordWrap: "break-word",
                  }}
                >
                  <span style={{ display: "inline-block", textAlign: "left" }}>
                    <span style={{ fontWeight: "700", marginRight: "4px" }}>Note:</span>
                    When selecting the hourly rate please consider overall cost of job. i.e take into account travel time, prep time and any follow up
                    work required by the tutor
                  </span>
                </p>
              </div>

              {/* Right Column - Pricing Table */}
              <div
                style={{
                  display: "grid",
                  gridTemplateRows: "auto 1px auto 1px auto",
                  gap: "10px",
                  alignItems: "center",
                }}
              >
                {/* Header Row */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(3, 1fr)",
                    gap: "10px",
                  }}
                >
                  <div>
                    <p style={{ margin: 0, padding: 0, fontWeight: 700, fontSize: "clamp(16px, 1.5vw, 20px)", color: "#585858" }}>Tutor Profile</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 700, fontSize: "clamp(16px, 1.5vw, 20px)", color: "#585858" }}>1 Hour Lesson</p>
                    <p style={{ margin: "4px 0 0", padding: 0, fontWeight: 400, fontSize: "14px", color: "#585858" }}>min. per hour</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 700, fontSize: "clamp(16px, 1.5vw, 20px)", color: "#585858" }}>2+ Hours Lesson</p>
                    <p style={{ margin: "4px 0 0", padding: 0, fontWeight: 400, fontSize: "14px", color: "#585858" }}>min. per hour</p>
                  </div>
                </div>

                {/* Divider */}
                <div style={{ width: "100%", backgroundColor: "#F1F1F1", height: "1px" }} />

                {/* High School Student Row */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(3, 1fr)",
                    gap: "10px",
                  }}
                >
                  <div>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>High School Student</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$40</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$30</p>
                  </div>
                </div>

                {/* Divider */}
                <div style={{ width: "100%", backgroundColor: "#F1F1F1", height: "1px" }} />

                {/* High School Graduate Row */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(3, 1fr)",
                    gap: "10px",
                  }}
                >
                  <div>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>High School Graduate</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$60</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$50</p>
                  </div>
                </div>
                <div style={{ width: "100%", backgroundColor: "#F1F1F1", height: "1px" }} />
                {/* Certified Teacher Row */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(3, 1fr)",
                    gap: "10px",
                  }}
                >
                  <div>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>Certified Teacher</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$80</p>
                  </div>
                  <div style={{ textAlign: "center" }}>
                    <p style={{ margin: 0, padding: 0, fontWeight: 400, fontSize: "16px", color: "#585858" }}>$60</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* <Divider className="mt-5 mb-6 w-11 align-self-center" /> */}
          <div className="flex justify-content-between w-11 align-self-center mt-3">
            <div className="flex flex-column" style={{ width: "" }}>
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "30px",
                  color: "#585858",
                }}
              >
                Base rate
              </h1>
              {shiftsFilled && (
                <div className=" flex gap-1 my-2 justify-content-between">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "16px",
                      color: "#FF6359",
                    }}
                  >
                    {message}
                  </p>
                </div>
              )}
              {displayPriceGuide && displayTutoringLowRateNotice() && (
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                    textAlign: "justify",
                    width: "100%",
                  }}
                >
                  <span className="inline-block text-left">
                    <span style={{ color: "red" }}>*</span>Jobs priced below the Price Guide can be difficult to fill.
                  </span>
                </p>
              )}
              {/* <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  <span className="inline-block text-left">
                    <span
                      className="inline-block mr-1"
                      style={{
                        fontWeight: "700",
                      }}
                    >
                      Note:
                    </span>
                    Jobs priced below the Price Guide can be difficult to fill.
                  </span>
                </p> */}
              {displayPriceGuide && displayLowRateNotice() && (
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                    textAlign: "justify",
                    width: "100%",
                  }}
                >
                  <span className="inline-block text-left">
                    <span style={{ color: "red" }}>*</span>Jobs priced below the Price Guide can be difficult to fill.
                  </span>
                </p>
              )}
            </div>
            <div className="flex align-items-center">
              <div className="w-min flex flex-column align-items-center relative">
                <NumberInput
                  value={price}
                  onChange={(v) => {
                    if (Number(v) > 0) {
                      setShowAll(true);
                    }
                    setPrice(v);
                  }}
                />
                {displayPriceGuide && (displayLowRateNotice() || displayTutoringLowRateNotice()) && (
                  <span
                    style={{
                      color: "red",
                      fontSize: "20px",
                      fontWeight: 500,
                      margin: "0 0 0 4px", // Adds a little spacing after the input
                      position: "absolute",
                      right: "12px",
                    }}
                  >
                    *
                  </span>
                )}
                {/* <input
                    type="text"
                    name="price"
                    id="price"
                    placeholder={`$${price}`}
                    value={!priceEditDisabled ? `$${price}` : ""}
                    className={`${styles.priceInput}`}
                    onClick={() => {
                      setShowAll(true);
                      setPriceEditDisabled(false);
                      setPrice("");
                    }}
                    onKeyDown={handleKeyDown}
                    onChange={(e) => {
                      const val = e.target.value;
                      const numericValue = val.replace(/[^0-9]/g, "");
                      if (numericValue) {
                        setPrice(numericValue);
                      } else if (numericValue === "") {
                        setPrice("00");
                      }
                    }}
                    style={{
                      border: "none",
                      width: "150px",
                      height: "79px",
                    }}
                  /> */}

                <p
                  className="m-0 p-0 relative mr-6"
                  style={{
                    fontWeight: 400,
                    fontSize: "20px",
                    color: "#585858",
                  }}
                >
                  Per hour
                </p>
              </div>
              {/* <div
                  className="flex justify-content-center align-items-center"
                  style={{
                    width: "26px",
                    height: "27px",
                    backgroundColor: "#FFA500",
                    borderRadius: "50%",
                    boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    setShowAll(true);
                    setPriceEditDisabled(!priceEditDisabled);
                  }}
                >
                  <img src={Pencil} alt="pencil" />
                </div> */}
            </div>
          </div>

          {showAll && (
            <>
              {/* <div className="w-11 align-self-center flex gap-1 mb-2">
                {[
                  {
                    label: `${metadata.hours} Hours`,
                    icon: <img src={ClockForward} alt="Clock" />,
                    backgroundColor: "#2F9ACD",
                  },
                  {
                    label: `${isNaN(metadata.cost) ? 0 : metadata.cost} job cost `,
                    icon: <img src={Dollar} alt="Dollar" height="15px" width="8px" />,
                    backgroundColor: "#77DBC9",
                  },
                ].map((value, index) => (
                  <div
                    key={index}
                    className="flex justify-content-center align-items-center gap-2"
                    style={{
                      backgroundColor: value.backgroundColor,
                      borderRadius: "10px",
                      padding: "10px",
                      border: "1px solid #DFDFDF",
                    }}
                  >
                    {value.icon}
                    <p
                      className="p-0 m-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "14px",
                        color: "#FFFFFF",
                      }}
                    >
                      {value.label}
                    </p>
                  </div>
                ))}
              </div> */}
              <div className="w-11 align-self-center">
                {schedules.map((schedule, index) => (
                  <JobShifts
                    schedule={schedule}
                    price={Number(price)}
                    onSchedulePriceChange={(i, s) => {
                      setSchedules((prev) => {
                        const updatedSchedules = [...prev];
                        const shift = [...updatedSchedules[index].shifts];
                        shift[i] = { ...s };
                        updatedSchedules[index] = {
                          ...updatedSchedules[index],
                          hourlyPrice: s.hourlyPrice,
                          price: s.price,
                          shifts: shift,
                        };
                        return updatedSchedules;
                      });
                    }}
                    key={index}
                  />
                ))}

                <div className="grid justify-content-end mt-2">
                  <div className="grid gap-3 grid-nogutter pr-0 justify-content-end">
                    <div className="mr-2">
                      <p
                        className="m-0"
                        style={{
                          color: "rgb(88, 88, 88)",
                          fontSize: "16px",
                          fontWeight: "700",
                        }}
                      >
                        Weekly Total
                      </p>
                    </div>
                    <div style={{ minWidth: "44px" }} className="">
                      <p
                        className="m-0"
                        style={{
                          color: "rgb(88, 88, 88)",
                          fontSize: "16px",
                          fontWeight: "700",
                          textAlign: "center",
                        }}
                      >
                        {metadata.hours}
                      </p>
                    </div>
                    <div style={{ width: "38px" }} />
                    <div className="">
                      <p
                        className="m-0"
                        style={{
                          color: "rgb(88, 88, 88)",
                          fontSize: "16px",
                          fontWeight: "700",
                          textAlign: "right",
                        }}
                      >
                        ${isNaN(metadata.cost) ? "0.00" : Number(metadata.cost).toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <div
        className="flex flex-column sticky justify-content-center align-items-center bottom-0 bg-white"
        style={{
          width: "100%",
        }}
      >
        <Divider className="m-0" />
        <div className="flex justify-content-between align-items-center py-3 " style={{ width: "80%" }}>
          <GoBack
            onClick={() => {
              setpayload({
                ...payload,
                weeklySchedule: {
                  ...payload.weeklySchedule,
                  weeklyScheduleEntries: schedules.map((schedule) => ({
                    ...schedule,
                    hourlyPrice: schedule.hourlyPrice.toString(), // Convert hourlyPrice to a string
                    shifts: schedule.shifts.map((shift) => ({
                      ...shift,
                      hourlyPrice: shift.hourlyPrice.toString(), // Convert hourlyPrice to a string
                    })),
                  })),
                },
                price: Number(price),
                applicantFilters:
                  payload.applicantFilters === null
                    ? [
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ]
                    : [
                      ...payload.applicantFilters.filter((val) => val.field !== "hourlyRate"),
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ],
              });
              if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
                prev("day-and-schedule"); // Navigate to "candidate-selection" for specific job types
              } else {
                prev("jobpricing-step1");
              }
            }}
          />
          <Next
            disabled={shiftsFilled}
            onClick={() => {
              setpayload({
                ...payload,
                weeklySchedule: {
                  ...payload.weeklySchedule,
                  weeklyScheduleEntries: schedules.map((schedule) => ({
                    ...schedule,
                    hourlyPrice: schedule.hourlyPrice.toString(), // Convert hourlyPrice to a string
                    shifts: schedule.shifts.map((shift) => ({
                      ...shift,
                      hourlyPrice: shift.hourlyPrice.toString(), // Convert hourlyPrice to a string
                    })),
                  })),
                },
                price: Number(price),
                applicantFilters:
                  payload.applicantFilters === null
                    ? [
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ]
                    : [
                      ...payload.applicantFilters.filter((val) => val.field !== "hourlyRate"),
                      {
                        field: "hourlyRate",
                        operator: "eq",
                        value: Number(price),
                      },
                    ],
              });

              if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
                next("pricing-payments-step2"); // Navigate to "pricing-payments-step2" for specific job types
              } else {
                next("candidate-selection"); // Navigate to "candidate-selection" otherwise
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};
const PricingPaymentsStep1Mobile = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    displayLowRateNotice,
    displayPriceGuide,
    guidePopup,
    message,
    metadata,
    shiftsFilled,
    setShowAll,
    showAll,
    setPrice,
    price,
    setSchedules,
    priceEditDisabled,
    setGuidePopup,
    schedules,
    notBusiness,
    displayTutoringLowRateNotice,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        position: "relative",
        overflow: "auto",
      }}
    >
      <Dialog
        visible={guidePopup}
        onHide={() => setGuidePopup(false)}
        content={
          <div
            className="flex justify-content-center align-items-center"
            style={{
              width: "100%",
              height: "100%",
              backgroundColor: "#FFFFFF",
              border: "1px solid #F0F4F7",
              borderRadius: "33px",
              position: "relative",
              overflow: "visible",
              userSelect: "none",
              paddingInline: "30px",
              paddingBlock: "20px",
            }}
          >
            <div
              style={{
                position: "absolute",
                height: "30px",
                width: "30px",
                borderRadius: "50%",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                top: "-10px",
                right: "10px",
                backgroundColor: "#FFFFFF",
                fontSize: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.preventDefault();
                // if (extraPrice < minExtraPrice) {
                //     alert(`Minimum Extra for this Job is ${minExtraPrice}`);
                //     return;
                // }
                setGuidePopup(false);
              }}
            >
              <IoClose />
            </div>
            {![64, 128].includes(payload.jobType) ? (
              <div className={styles.priceGuide}>
                <div>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 700,
                      fontSize: "22px",
                      color: "#444444",
                      textWrap: "wrap",
                    }}
                  >
                    Job Price Guide
                  </p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    <span className="inline-block text-left">
                      <span
                        className="inline-block mr-1"
                        style={{
                          fontWeight: "700",
                        }}
                      >
                        Note:
                      </span>
                      Jobs priced below the Price Guide can be difficult to fill.
                    </span>
                  </p>
                </div>
                <div className={styles.shiftDivMobile}>
                  <div className={styles.shiftOneSecond}>
                    <div
                      style={{
                        backgroundColor: "#1F9EAB",
                        borderRadius: "50%",
                      }}
                    >
                      <p
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#fff",
                          paddingInline: "15px",
                        }}
                      >
                        <span
                          style={{
                            fontSize: "14px",
                            fontWeight: 700,
                            color: "#fff",
                          }}
                        >
                          2
                        </span>{" "}
                        hr
                      </p>
                    </div>
                    <div className="mr-3">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        Base rate
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        Min $35+ p/h
                      </p>
                    </div>
                  </div>

                  <div className={styles.shiftDivTwoMobileSecond}>
                    <div
                      style={{
                        backgroundColor: "#8577DB",
                        borderRadius: "50%",
                        marginLeft: "10px",
                      }}
                    >
                      <p
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#fff",
                          paddingInline: "8px",
                          paddingBlock: "5px",
                        }}
                      >
                        <span
                          style={{
                            fontSize: "14px",
                            fontWeight: 700,
                            color: "#fff",
                          }}
                        >
                          3+
                        </span>{" "}
                        hrs
                      </p>
                    </div>
                    <div className=" flex flex-column justify-content-center">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        Base rate
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        Av. $25-$35+ p/h
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-column">
                <div className="flex flex-column" style={{ alignItems: "center" }}>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 700,
                      fontSize: "20px",
                      color: "#444444",
                      textWrap: "wrap",
                    }}
                  >
                    Job Price Guide
                  </p>

                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: 400,
                      fontSize: "12px",
                      color: "#585858",
                      textWrap: "wrap",
                    }}
                  >
                    <span className="m-0 p-0 text-center inline-block">
                      <span
                        className="inline-block "
                        style={{
                          fontWeight: "700",
                        }}
                      >
                        Note:
                      </span>
                      When selecting the hourly rate please consider overall cost of job. i.e take into account travel time, prep time and any follow
                      up work required by the tutor
                    </span>
                  </p>
                </div>
                <div style={{ marginTop: "10px" }}>
                  <div className={styles.priceGuideTutoring}>
                    <div>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        Tutor Profile
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        High School Student
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        High School Graduate
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        Certified Teacher
                      </p>
                    </div>
                    <div className="flex flex-column">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        1 Hour Lesson
                        <br />
                        (min. per hour)
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $40
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $60
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $80
                      </p>
                    </div>
                    <div className="flex flex-column">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 700,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        2+ Hours Lesson
                        <br />
                        (min. per hour)
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $30
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $50
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: 400,
                          fontSize: "12px",
                          color: "#585858",
                        }}
                      >
                        $60
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        }
      />
      <div className={styles.JobpricingMain}>
        <div className="w-full h-full flex flex-column align-items-center">
          <div className="w-full flex-grow-1 flex ">
            <div
              className="flex flex-column"
              style={{
                paddingTop: "25px",
              }}
            >
              <div className="flex flex-column justify-content-between ">
                <div className="flex flex-column" style={{ paddingInline: "20px" }}>
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "30px",
                      color: "#585858",
                    }}
                  >
                    Base Rate
                  </h1>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {/* <span className="inline-block text-left">
                      <span
                        className="inline-block mr-1"
                        style={{
                          fontWeight: "700",
                        }}
                      >
                        Note:
                      </span>
                      Jobs priced below the Price Guide can be difficult to
                      fill.
                    </span> */}
                  </p>
                </div>
                <div className="flex align-items-center justify-content-center">
                  <div className="w-min flex flex-column align-items-center relative">
                    <NumberInput
                      value={price}
                      onChange={(v) => {
                        if (Number(v) > 0) {
                          setShowAll(true);
                        }
                        setPrice(v);
                      }}
                    />

                    {displayPriceGuide && (displayLowRateNotice() || displayTutoringLowRateNotice()) && (
                      <span
                        style={{
                          color: "red",
                          fontSize: "20px",
                          fontWeight: 500,
                          margin: "0 0 0 4px", // Adds a little spacing after the input
                          position: "absolute",
                          right: "54px",
                        }}
                      >
                        *
                      </span>
                    )}
                    {/* <input
                          type="text"
                          name="price"
                          id="price"
                          placeholder={`$${price}`}
                          value={!priceEditDisabled ? `$${price}` : ""}
                          className={`${styles.priceInput}`}
                          onClick={() => {
                            setShowAll(true);
                            setPriceEditDisabled(false);
                            setPrice("");
                          }}
                          onChange={(e) => {
                            const val = e.target.value;
                            const numericValue = val.replace(/[^0-9]/g, "");
                            if (numericValue) {
                              setPrice(numericValue);
                            } else if (numericValue === "") {
                              setPrice("00");
                            }
                          }}
                          onKeyDown={handleKeyDown}
                          style={{
                            border: "none",
                            width: "175px",
                            height: "79px",
                          }}
                        /> */}

                    <p
                      className="m-0 p-0 relative mr-6"
                      style={{
                        fontWeight: 400,
                        fontSize: "20px",
                        color: "#585858",
                      }}
                    >
                      Per hour
                    </p>
                  </div>

                  {/* <div
                        className="flex justify-content-center align-items-center"
                        style={{
                          width: "26px",
                          height: "27px",
                          backgroundColor: "#FFA500",
                          borderRadius: "50%",
                          boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                          cursor: "pointer",
                        }}
                        onClick={() => {
                          setShowAll(true);
                          setPriceEditDisabled(!priceEditDisabled);
                        }}
                      >
                        <img src={Pencil} alt="pencil" />
                      </div> */}
                </div>
                {!priceEditDisabled && notBusiness && (
                  <div>
                    <p
                      style={{
                        fontSize: "14px",
                        fontWeight: "600",
                        color: "#FFA500",
                        textDecoration: "underline",
                        textAlign: "center",
                      }}
                      onClick={() => setGuidePopup(true)}
                    >
                      Learn more about pricing
                    </p>
                  </div>
                )}

                {!priceEditDisabled && !notBusiness && (
                  <div style={{ color: "#585858", fontSize: "12px", textAlign: "center" ,marginTop:"10px"}}>
                    For details of the childcare wages stipulated in the Children Services Award
                    please refer to the government’s Fair Work Commission website.
                  </div>
                )}
              </div>
              <div className="px-2">
                {displayPriceGuide && (displayLowRateNotice() || displayTutoringLowRateNotice()) && (
                  <p
                    className="m-0 mt-3 p-0"
                    style={{
                      color: "#585858",
                      fontWeight: 300,
                      fontSize: "14px",
                      textAlign: "center",
                    }}
                  >
                    <span style={{ color: "red" }}> *</span>Jobs priced below the Price Guide can be difficult to fill.
                  </p>
                )}
              </div>
              {/* <div className="align-self-center flex gap-1 mt-3 mb-3">
                {[
                  {
                    label: `${metadata.hours} hrs`,
                    icon: <img src={ClockStartMobile} alt="Clock" width="20px" height="18.5px" />,
                    backgroundColor: "#2F9ACD",
                  },
                  {
                    label: `${payload.duration || "0"} weeks`, // Add your duration value here
                    icon: <img src={ClockForwardMobile} alt="Duration" width="18px" height="16.5px" />, // You can use the same or different clock icon
                    backgroundColor: "#8577DB", // Added a new color to distinguish it
                  },
                  {
                    label: `${metadata.cost} job cost`,
                    icon: <img src={Dollar} alt="Dollar" height="15px" width="8px" />,
                    backgroundColor: "#77DBC9",
                  },
                ].map((value, index) => (
                  <div
                    key={index}
                    className="flex justify-content-center align-items-center gap-2"
                    style={{
                      backgroundColor: value.backgroundColor,
                      borderRadius: "10px",
                      padding: "10px",
                      border: "1px solid #DFDFDF",
                    }}
                  >
                    {value.icon}
                    <p
                      className="p-0 m-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "14px",
                        color: "#FFFFFF",
                      }}
                    >
                      {value.label}
                    </p>
                  </div>
                ))}
              </div> */}
              {!showAll && (
                <>
                  {![64, 128].includes(payload.jobType) ? (
                    <div className={styles.priceGuide}>
                      <div>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 700,
                            fontSize: "22px",
                            color: "#444444",
                            textWrap: "wrap",
                          }}
                        >
                          Job Price Guide
                        </p>
                      </div>
                      <div className={styles.shiftDivMobile}>
                        <div className={styles.shiftOne}>
                          <div
                            style={{
                              backgroundColor: "#1F9EAB",
                              borderRadius: "50%",
                            }}
                          >
                            <p
                              style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                color: "#fff",
                                paddingInline: "15px",
                              }}
                            >
                              <span
                                style={{
                                  fontSize: "20px",
                                  fontWeight: 700,
                                  color: "#fff",
                                }}
                              >
                                2
                              </span>{" "}
                              hr
                            </p>
                          </div>
                          <div className="mr-3">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "16px",
                                color: "#585858",
                              }}
                            >
                              Base rate
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 700,
                                fontSize: "20px",
                                color: "#585858",
                              }}
                            >
                              Min $35+ p/h
                            </p>
                          </div>
                        </div>

                        <div className={styles.shiftDivTwoMobile}>
                          <div
                            style={{
                              backgroundColor: "#8577DB",
                              borderRadius: "50%",
                            }}
                          >
                            <p
                              style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                color: "#fff",
                                paddingInline: "8px",
                                paddingBlock: "5px",
                              }}
                            >
                              <span
                                style={{
                                  fontSize: "20px",
                                  fontWeight: 700,
                                  color: "#fff",
                                }}
                              >
                                3+
                              </span>{" "}
                              hrs
                            </p>
                          </div>
                          <div className="mr-3 flex flex-column justify-content-center align-items-center">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "16px",
                                color: "#585858",
                              }}
                            >
                              Base rate
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 700,
                                fontSize: "20px",
                                color: "#585858",
                              }}
                            >
                              Av. $25-$35+ p/h
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="flex flex-column justify-content-center align-item-center"
                      style={{
                        padding: "15px 20px",
                      }}
                    >
                      <div className="flex flex-column" style={{ alignItems: "center" }}>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 700,
                            fontSize: "22px",
                            color: "#444444",
                            textWrap: "wrap",
                          }}
                        >
                          Job Price Guide
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: 400,
                            fontSize: "14px",
                            color: "#585858",
                            textWrap: "wrap",
                          }}
                        >
                          <span className="m-0 p-0 text-center inline-block">
                            <span
                              className="inline-block "
                              style={{
                                fontWeight: "700",
                              }}
                            >
                              Note:
                            </span>
                            When selecting the hourly rate please consider overall cost of job. i.e take into account travel time, prep time and any
                            follow up work required by the tutor
                          </span>
                        </p>
                      </div>
                      <div style={{ marginTop: "10px" }}>
                        <div className={styles.priceGuideTutoring}>
                          <div>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                color: "#585858",
                              }}
                            >
                              Tutor Profile
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              High School Student
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              High School Graduate
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              Certified Teacher
                            </p>
                          </div>
                          <div className="flex flex-column">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                color: "#585858",
                              }}
                            >
                              1 Hour Lesson
                              <br />
                              (min. per hour)
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $40
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $60
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $80
                            </p>
                          </div>
                          <div className="flex flex-column">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                color: "#585858",
                              }}
                            >
                              2+ Hours Lesson
                              <br />
                              (min. per hour)
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $30
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $50
                            </p>
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: 400,
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              $60
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
              {showAll && (
                <>
                  {/* <Divider className="mt-2  w-full align-self-center" />   */}
                  {/* <div className={styles.ratecontainer}>
                    <div>
                      {" "}
                      <h1
                        style={{
                          fontSize: "22px",
                          color: "#585858",
                          fontWeight: "700",
                          margin: "0px",
                        }}
                      >
                        Adjust Hourly Rate
                      </h1>
                    </div>
                    <div className='flex align-items-center justify-content-center gap-2'>
                      {" "}
                      <p
                        style={{
                          fontSize: "16px",
                          color: "#585858",
                          fontWeight: "700",
                        }}
                      >
                     $
                      </p>
                    </div>
                  </div> */}
                  {/* <div className="flex align-items-center flex-" style={{ paddingInline: "10px" }}>
                    <div className="flex justify-content-start align-items-center gap-2" style={{ width: "80%" }}>
                      <p className="p-0 font-bold" style={{ color: '#585858' }}> Adjust Hourly Rate</p>
                    </div>
                    <div className="flex justify-content-start align-items-center" >
                      <p className="p-0 font-bold" style={{ color: '#585858' }} >$</p>
                    </div>
                  </div> */}

                  <div className={styles.container} style={{ display: "flex", justifyContent: "space-between", padding: "10px", gap: "50px" }}>
                    <div style={{ minWidth: "207px" }}>
                      <p className="p-0 font-bold" style={{ color: "#585858", fontSize: "20px" }}>
                        Adjust Hourly Rate
                      </p>
                    </div>
                  </div>
                  <div className="w-full align-self-center">
                    {schedules.map((schedule, index) => (
                      <JobShifts
                        schedule={schedule}
                        price={Number(price)}
                        onSchedulePriceChange={(i, s) => {
                          setSchedules((prev) => {
                            const updatedSchedules = [...prev];
                            const shift = [...updatedSchedules[index].shifts];
                            shift[i] = { ...s };
                            updatedSchedules[index] = {
                              ...updatedSchedules[index],
                              hourlyPrice: s.hourlyPrice,
                              price: s.price,
                              shifts: shift,
                            };
                            return updatedSchedules;
                          });
                        }}
                        key={index}
                        jobType={payload.jobType}
                        subJobType={payload.jobSubType}
                      />
                    ))}
                  </div>
                </>
              )}
              <div className={styles.container} style={{ display: "flex", justifyContent: "space-between", padding: "10px", gap: "50px" }}>
                <div style={{ minWidth: "207px" }}>
                  <p className="p-0 font-bold" style={{ color: "#585858", fontSize: "16px", textAlign: "end", marginRight: "8px" }}>
                    Weekly Total
                  </p>
                </div>
                <div className={styles.rateSection}>
                  <p className="p-0 font-bold" style={{ color: "#585858", fontSize: "16px" }}>
                    ${isNaN(metadata.cost) ? "0.00" : Number(metadata.cost).toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <BackButtonPortal id="back-button-portal">
        <div
          onClick={() => {
            setpayload({
              ...payload,
              weeklySchedule: {
                ...payload.weeklySchedule,
                weeklyScheduleEntries: schedules.map((schedule) => ({
                  ...schedule,
                  hourlyPrice: schedule.hourlyPrice.toString(), // Convert hourlyPrice to a string
                  shifts: schedule.shifts.map((shift) => ({
                    ...shift,
                    hourlyPrice: shift.hourlyPrice.toString(), // Convert hourlyPrice to a string
                  })),
                })),
              },
              price: Number(price),
              applicantFilters:
                payload.applicantFilters === null
                  ? [
                    {
                      field: "hourlyRate",
                      operator: "eq",
                      value: Number(price),
                    },
                  ]
                  : [
                    ...payload.applicantFilters.filter((val) => val.field !== "hourlyRate"),
                    {
                      field: "hourlyRate",
                      operator: "eq",
                      value: Number(price),
                    },
                  ],
            });
            if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
              prev("day-and-schedule"); // Navigate to "candidate-selection" for specific job types
            } else {
              prev("jobpricing-step1");
            }
          }}
        >
          <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
        </div>
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        isDisabled={shiftsFilled}
        onClick={() => {
          setpayload({
            ...payload,
            weeklySchedule: {
              ...payload.weeklySchedule,
              weeklyScheduleEntries: schedules.map((schedule) => ({
                ...schedule,
                hourlyPrice: schedule.hourlyPrice.toString(), // Convert hourlyPrice to a string
                shifts: schedule.shifts.map((shift) => ({
                  ...shift,
                  hourlyPrice: shift.hourlyPrice.toString(), // Convert hourlyPrice to a string
                })),
              })),
            },
            price: Number(price),
            applicantFilters:
              payload.applicantFilters === null
                ? [
                  {
                    field: "hourlyRate",
                    operator: "eq",
                    value: Number(price),
                  },
                ]
                : [
                  ...payload.applicantFilters.filter((val) => val.field !== "hourlyRate"),
                  {
                    field: "hourlyRate",
                    operator: "eq",
                    value: Number(price),
                  },
                ],
          });

          if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
            next("pricing-payments-step2"); // Navigate to "pricing-payments-step2" for specific job types
          } else {
            next("candidate-selection"); // Navigate to "candidate-selection" otherwise
          }
        }}
      />
    </div>
  );
};
