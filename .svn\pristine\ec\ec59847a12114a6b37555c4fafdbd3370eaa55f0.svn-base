import React, { useCallback, useEffect, useRef, useState } from "react";
import { InputTextarea } from "primereact/inputtextarea";
import { AppDispatch, RootState } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import OutlineButton from "../../../commonComponents/OutlineButton";
import myfamilystyles from "../styles/my-family.module.css";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import ReactCrop, {
  centerCrop,
  Crop,
  makeAspectCrop,
  PixelCrop,
} from "react-image-crop";
import CustomButton from "../../../commonComponents/CustomButton";
import Userimage from "../../../assets/images/Group_light.png";
import { Divider } from "primereact/divider";
import useLoader from "../../../hooks/LoaderHook";
import useIsMobile from "../../../hooks/useIsMobile";

interface Benefit {
  canSelectCategory: boolean;
  childCategory: number;
  children: null;
  optionId: number;
  selected: boolean;
  text: string;
}

interface Pet {
  canSelectCategory: boolean;
  childCategory: number;
  children: null;
  optionId: number;
  selected: boolean;
  text: string;
}
const MyFamily: React.FC = ({ }) => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { enableLoader, disableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isImageUploaded, setIsImageUploaded] = useState(false);
  const { isMobile } = useIsMobile();
  const [inputValue, setInputValue] = useState(
    sessionInfo.data["aboutMe"] || ""
  );
  const [expectationValue, setExpectationValue] = useState(
    sessionInfo.data["client"]["jobDescription"] || ""
  );
  const [houseRules, setHouseRules] = useState(
    sessionInfo.data["client"]["jobRules"] || ""
  );
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [crop, setCrop] = useState<Crop>()
  const imgRef = useRef<HTMLImageElement>(null);
  const [benefits, setBenefits] = useState<Benefit[]>(
    sessionInfo.data["client"]["benefitsForProviders"] || []
  );
  const [pets, setPets] = useState<Pet[]>(
    sessionInfo.data["client"]["pets"] || []
  );
  const [errors, setErrors] = useState({
    inputValueError: false,
    expectationValueError: false,
    houseRulesError: false,
  });



  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);

    if (e.target.value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        inputValueError: false,
      }));
    }
  };

  const handleExpectationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setExpectationValue(e.target.value);


  };

  const handleHouseRulesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setHouseRules(e.target.value);

    if (e.target.value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        houseRulesError: false,
      }));
    }
  };

  const toggleBenefit = (optionId: number) => {
    setBenefits((prevBenefits) =>
      prevBenefits.map((benefit) =>
        benefit.optionId === optionId
          ? { ...benefit, selected: !benefit.selected }
          : benefit
      )
    );
  };

  const togglePet = (optionId: number) => {
    setPets((prevPets) =>
      prevPets.map((pet) =>
        pet.optionId === optionId ? { ...pet, selected: !pet.selected } : pet
      )
    );
  };

  const handleSave = () => {
    let isValid = true;
    const newErrors = { ...errors };

    // Sequential validation
    if (!inputValue) {
      newErrors.inputValueError = true;
      isValid = false;
    }

    setErrors(newErrors);

    if (!isValid) return;

    // Save logic
    enableLoader();
    const payload = {
      ...sessionInfo.data,
      aboutMe: inputValue,
      client: {
        ...sessionInfo.data["client"],
        jobDescription: expectationValue,
        jobRules: houseRules,
        benefitsForProviders: benefits,
        pets: pets,
      },
    };
    dispatch(updateUser({ payload })).finally(() => {
      disableLoader();
    });
  };

  useEffect(() => {
    if (sessionInfo.data?.["defaultImage"]?.["scale1ImageUrl"]) {
      setSelectedImage(sessionInfo.data["defaultImage"]["scale1ImageUrl"]);
    }
  }, [sessionInfo.data]);

  const handleCancel = () => {
    setSelectedImage(null);
    setCroppedImageUrl(null);
    setCompletedCrop(undefined);
    setIsImageUploaded(false);
    setCrop(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        4 / 4,
        width,
        height
      ),
      width,
      height
    )
    setCrop(crop)
  }

  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop) => {
      const canvas = document.createElement("canvas");
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      const pixelCrop = {
        x: Math.round(crop.x * scaleX),
        y: Math.round(crop.y * scaleY),
        width: Math.round(crop.width * scaleX),
        height: Math.round(crop.height * scaleY),
        unit: 'px' as const // Ensure the unit is explicitly set to "px"
      };

      canvas.width = pixelCrop.width;
      canvas.height = pixelCrop.height;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(
          image,
          pixelCrop.x,
          pixelCrop.y,
          pixelCrop.width,
          pixelCrop.height,
          0,
          0,
          pixelCrop.width,
          pixelCrop.height
        );
      }

      return {
        croppedImageUrl: canvas.toDataURL("image/jpeg"),
        x: pixelCrop.x,
        y: pixelCrop.y,
        height: pixelCrop.height,
        width: pixelCrop.width,
        unit: pixelCrop.unit // Include the unit property in the return object
      };
    },
    []
  );

  const handleSavePhoto = useCallback(() => {
    if (imgRef.current && completedCrop) {
      enableLoader();
      const { croppedImageUrl, x, y, height, width, unit } = getCroppedImg(imgRef.current, completedCrop);
      setCroppedImageUrl(croppedImageUrl);
      setSelectedImage(croppedImageUrl); // Set the cropped image as the selected image
      const payload = {
        id: sessionInfo.data["defaultImage"]["id"],
        cropDimensions: { x, y, width, height, unit },
        isCropped: true,
        scale1ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"], // Set the cropped image URL as scale1ImageUrl
        scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
        scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
      };
      dispatch(
        updateUser({
          payload: {
            ...sessionInfo.data,
            defaultImage: payload,
          },
        })
      )
        .then(() => {
          // Ensure the component re-renders with the new image
          setIsImageUploaded(false);
          setSelectedImage(null);
          setCroppedImageUrl(null);
          setCompletedCrop(undefined);
          setCrop(undefined);
        })
        .catch((error) => {
          console.error("Error updating user:", error);
        })
        .finally(() => {
          disableLoader();
        });
    }
  }, [completedCrop, getCroppedImg, sessionInfo.data, dispatch, disableLoader]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      enableLoader();
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        setIsImageUploaded(true);
        setSelectedImage(reader.result as string);
        const binaryData = e.target?.result as ArrayBuffer;
        const payload = {
          id: sessionInfo.data["defaultImage"]["id"],
          imageBinary: binaryData,
          isCropped: true,
          scale1ImageUrl: reader.result as string, // Set the uploaded image as scale1ImageUrl
          scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
          scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
        };
        dispatch(
          updateUser({
            payload: {
              ...sessionInfo.data,
              defaultImage: payload,
            },
          })
        )
          .then(() => { })
          .catch((error) => {
            console.error("Error updating user:", error);
          })
          .finally(() => {
            disableLoader();
          });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="flex">
      <div className="p-2 pl-3" style={{ width: !isMobile ? "90%" : "100%" }}>
        <div>
          <div
            className="flex align-items-center justify-content-between mb-2 mt-1
                     flex-wrap"
          >
            <p
              className="font-semibold m-0"
              style={{ color: "#585858", fontSize: "24px" }}
            >
              My Family
            </p>
            <CustomButton
              label={"Save"}
              className={`${myfamilystyles.customButton}`}
              style={{ margin: "0", width: "150px" }}
              onClick={handleSave}
            ></CustomButton>
          </div>
          <Divider className=" mb-5" />
          <p
            className="font-semibold "
            style={{
              color: "#585858",
              fontSize: "18px",
              lineHeight: "0px",
            }}
          >
            Family photo
          </p>
          <p
            className={myfamilystyles.inputTextareafamily}
            style={{ color: "#585858", fontWeight: "400", fontSize: "14px" }}
          >
            All families and helpers are required to have a profile photo.
            Please choose one that represents your whole family.
          </p>
          <div className="">
            <div className="">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                id="image-upload"
                style={{ display: "none" }}
              />
              {!isImageUploaded ? (
                <label
                  htmlFor="image-upload"
                  className="flex  align-items-center flex-wrap"
                >
                  <div
                    className="cursor-pointer"
                    style={{
                      width: "150px",
                      height: "150px",
                      borderRadius: "50%",
                      backgroundColor: "#F0F4F7",
                      border: "1px solid #FFA500",
                    }}
                  >
                    {!isImageUploaded && !sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
                      <img
                        src={Userimage}
                        alt=""
                        className="cursor-pointer mt-4 ml-4 "
                        style={{
                          width: "95px",
                          height: "99px",
                          // borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                        }}
                      />
                    ) : (
                      <img
                        src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? Userimage}
                        alt=""
                        className="cursor-pointer"
                        style={{
                          width: "148px",
                          height: "148px",
                          borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                        }}
                      />
                    )}
                  </div>
                  <div
                    className="ml-3 mt-1 pl-3 pr-3 p-2 cursor-pointer"
                    style={{
                      backgroundColor: "#1F9EAB",
                      color: "#FFFFFF",
                      fontSize: "12px",
                      fontWeight: "600",
                      borderRadius: "8px",
                    }}
                  >
                    <span className="pi pi-camera"></span>
                    &nbsp;&nbsp;Upload Photo
                  </div>
                </label>
              ) : (
                <ReactCrop
                  crop={crop}
                  onChange={c => setCrop(c)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={1}

                  style={{ objectFit: "contain" }}
                >
                  <img
                    ref={imgRef}
                    src={selectedImage}
                    alt="Selected"
                    crossOrigin="anonymous" // Set crossOrigin attribute
                    onLoad={onImageLoad}
                    className="cursor-pointer"
                    style={{
                      // borderRadius: "50%",
                      backgroundColor: "#F0F4F7",
                    }}
                  />
                </ReactCrop>
              )}
            </div>
            {isImageUploaded && (
              <div className="flex flex-wrap justify-content-center flex-column">
                <div style={{ display: "flex" }}>
                  <CustomButton
                    label="Cancel"
                    onClick={handleCancel}
                    style={{
                      marginRight: "10px",
                      backgroundColor: "transparent",
                      color: "#585858",
                      height: "30px",
                      fontSize: "14px",
                      fontWeight: "500",
                      boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                      width: "150px",
                    }}
                  />
                  <CustomButton
                    label="Save photo"
                    onClick={handleSavePhoto}
                    style={{
                      backgroundColor: "#1F9EAB",
                      width: "150px",
                      height: "30px",
                    }}
                  />
                </div>
              </div>
            )}
          </div>
          <Divider className="mt-5 mb-5" />
        </div>
        <div>
          <p
            className="font-semibold"
            style={{
              color: "#585858",

              fontSize: "18px",
              lineHeight: "0px",
            }}
          >
            Family Introduction
          </p>
          <p
            className={myfamilystyles.inputTextareafamily}
            style={{ color: "#585858", fontWeight: "400", fontSize: "14px" }}
          >
            Please describe your family, but do not include any info that will
            need keeping up-to-date, such as the age of your children.
          </p>
          <div style={{ overflow: "auto", fontWeight: "500" }}>
            <InputTextarea
              autoResize
              value={inputValue}
              required
              onChange={handleInputChange}
              style={{ fontWeight: 500 }}
              rows={3}
              cols={30}
              className={`${myfamilystyles.inputTextareafamily} w-full p-3 ${errors.inputValueError ? "border-red-500" : ""
                }`}
              placeholder="Example - Hi there, we are a busy family of four. We are looking for local babysitters to help us, as my partner and I both often travel for work."
            />
            {errors.inputValueError && (
              <p className="text-red-500 p-0 m-0 text-sm">required</p>
            )}
          </div>
          <div>
            <p
              className="font-normal"
              style={{ fontSize: "14px", color: "#585858" }}
            >
              Do NOT include job details here, you do this when you post a Job.
            </p>
          </div>{" "}
        </div>
        <Divider className="mt-5 mb-5" />
        <div>
          <p
            className="font-semibold"
            style={{
              color: "#585858",

              fontSize: "18px",
              lineHeight: "0px",
            }}
          >
            Expectations & House rules
          </p>
          <div style={{ minHeight: "0px", overflow: "auto" }}>
            <InputTextarea
              autoResize
              value={expectationValue}
              required
              style={{ fontWeight: 500 }}
              onChange={handleExpectationChange}
              rows={3}
              cols={30}
              className={`${myfamilystyles.inputTextareafamily} w-full p-3 `}
              placeholder="Outline your general expectations for anybody looking after your children."
            />

          </div>
          <div style={{ minHeight: "", overflow: "auto" }}>
            <InputTextarea
              autoResize
              value={houseRules}
              required
              style={{ fontWeight: 500 }}
              onChange={handleHouseRulesChange}
              rows={3}
              cols={30}
              className={`${myfamilystyles.inputTextareafamily
                } w-full p-3 mt-3 `}
              placeholder="Tell helpers about any house rules they need to know."
            />
            {/* {errors.houseRulesError && (
              <p className="text-red-500 p-0 m-0 text-sm">required</p>
            )} */}
          </div>
        </div>
        <Divider className="mt-5 mb-5" />
        <div className={`${myfamilystyles.inputTextareafamily} mb-5`}>
          <p
            className="font-semibold"
            style={{
              color: "#585858",
              fontSize: "18px",
              lineHeight: "0px",
            }}
          >
            Perks for helpers
          </p>
          <div className="flex justify-content-around flex-wrap">
            {benefits.map((benefit: Benefit) => (
              <OutlineButton
                key={benefit.optionId}
                onClick={() => toggleBenefit(benefit.optionId)}
                className={`font-semibold ${benefit.selected ? "selected" : ""
                  }`}
                style={{
                  fontSize: "14px",
                  border: benefit.selected ? "2px solid #179D52" : "none",
                  paddingBlock: "15px",
                  fontWeight: benefit.selected ? "700" : "500",
                  backgroundColor: benefit.selected ? "#F0F4F7" : "#FFFFFF",
                }}
              >
                {benefit.text}
              </OutlineButton>
            ))}
          </div>
        </div>
        <div className={`${myfamilystyles.inputTextareafamily}`}>
          <p
            className="font-semibold"
            style={{
              color: "#585858",
              fontSize: "18px",
              lineHeight: "0px",
            }}
          >
            Pets
          </p>
          <div className="flex justify-content-around flex-wrap mt-4">
            {pets.map((pet: Pet) => (
              <OutlineButton
                key={pet.optionId}
                onClick={() => togglePet(pet.optionId)}
                className={`${pet.selected ? "selected" : ""}`}
                style={{
                  fontSize: "14px",
                  border: pet.selected ? "2px solid #179D52" : "none",
                  paddingBlock: "15px",
                  fontWeight: pet.selected ? "700" : "500",
                  backgroundColor: pet.selected ? "#F0F4F7" : "#FFFFFF",
                }}
              >
                {pet.text}
              </OutlineButton>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyFamily;
