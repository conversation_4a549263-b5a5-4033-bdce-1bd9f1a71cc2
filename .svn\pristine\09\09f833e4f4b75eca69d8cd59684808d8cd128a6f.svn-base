import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';
import CookiesConstant from '../helper/cookiesConst';
import utils from '../components/utils/util';

export interface AwaitingApprovalEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  statusCode: number;
}

interface AwaitingApprovalApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const statusMap: { [key: number]: string } = {
  3: "Awaiting Approval", // Status 3 for awaiting-approval
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

export const useAwaitingApprovalDetails = () => {
  const location = useLocation();
  const [awaitingApprovalData, setAwaitingApprovalData] = useState<AwaitingApprovalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();
 const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isBusiness = clientType === c.clientType.BUSINESS;
  // Check if current route is awaiting-approval
  const isAwaitingApprovalRoute = location.pathname.includes('/finalized-timesheets') ||
    (isParent && location.pathname === '/parent-home/timesheet') ||
    (isBusiness && location.pathname === '/business-home/timesheet');

  const fetchAwaitingApprovalData = async (): Promise<void> => {
    console.log('🔄 Starting awaiting approval data fetch from API...');
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getTimeSheet(
          (response: AwaitingApprovalApiItem[]) => {
            console.log("API Response getTimeSheet for awaiting approval:", response);
            
            if (!Array.isArray(response)) {
              console.warn("Expected array response, got:", typeof response);
              setAwaitingApprovalData([]);
              resolve();
              return;
            }

            // Filter only status 3 entries (Awaiting Approval)
            const filteredData = response.filter((item: AwaitingApprovalApiItem) => item.status === 3);
            console.log("Filtered awaiting approval data (status 3):", filteredData);

            const mappedData: AwaitingApprovalEntry[] = filteredData.map((item: AwaitingApprovalApiItem) => ({
              id: item.id,
              status: statusMap[item.status] || "Awaiting Approval",
              statusCode: item.status,
              type: jobTypeMap[item.jobType] || "Unknown",
              date: new Date(item.jobDate).toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }),
              location: item.formattedAddress,
              userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
              originalImageUrl: item.originalImageUrl,
            }));

            console.log("Mapped awaiting approval data:", mappedData);
            console.log("Awaiting approval count:", mappedData.length);
            
            setAwaitingApprovalData(mappedData);
            console.log('✅ Awaiting approval data loaded successfully, count:', mappedData.length);
            resolve();
          },
          (error: any) => {
            console.error('Error fetching awaiting approval data from API:', error);
            setError(error?.message || 'Failed to fetch awaiting approval data');
            reject(error);
          }
        );
      });
      
    } catch (err) {
      console.error('Fetch awaiting approval data failed:', err);
      setError('Failed to fetch awaiting approval data');
    } finally {
      console.log('🔄 Finishing awaiting approval data fetch...');
      setIsLoading(false);
      disableLoader();
      console.log('✅ Loader disabled for awaiting approval');
    }
  };

  useEffect(() => {
    // Only fetch data when on the awaiting-approval route
    if (isAwaitingApprovalRoute) {
      console.log('🔄 Route changed to awaiting-approval, fetching data...');
      fetchAwaitingApprovalData();
    } else {
      console.log('📍 Not on awaiting-approval route, skipping API call');
    }
  }, [location.pathname]); // Trigger when route changes

  const refreshData = () => {
    fetchAwaitingApprovalData();
  };

  const clearAwaitingApprovalData = () => {
    setAwaitingApprovalData([]);
    setError(null);
  };

  return {
    awaitingApprovalData,
    isLoading,
    error,
    refreshData,
    clearAwaitingApprovalData
  };
};

export default useAwaitingApprovalDetails;
