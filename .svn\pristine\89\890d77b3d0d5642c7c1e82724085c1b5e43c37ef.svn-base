import styles from "../commonStyle/loader.module.css";
import loaderImg from "../assets/images/js-loading-animation.gif";
import { store } from "../store";

const Loader = ({ inIframe = false }: { inIframe?: boolean }) => {
  const { loaderEnabled } = store.getState().applicationState;

  if (!loaderEnabled && !inIframe) return null;

  return (
    <div className={styles.loaderOverlay}>
      <div className={styles.loader}>
        <img src={loaderImg} alt="Loading..." />
      </div>
    </div>
  );
};

export default Loader;
