import React, { useState, useEffect } from 'react';
import AwaitingConfirmationCard from './AwaitingConfirmationCard';
import { TimesheetDetails } from '../../../../hooks/useTimesheetDetails';
import Service from '../../../../services/services';
import useLoader from '../../../../hooks/LoaderHook';
import c from '../../../../helper/juggleStreetConstants';
import CookiesConstant from '../../../../helper/cookiesConst';
import utils from '../../../../components/utils/util';
import { useNavigate } from 'react-router-dom';

// ... (Interfaces: TimesheetEntry, TimesheetRow, etc. are unchanged)
interface TimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
}

interface TimesheetRow {
  start: string;
  finish: string;
  hours: number;
  rate: number;
  total: number;
  isOriginal?: boolean;
  editVersion?: number;
  id?: number;
}

interface TimesheetDetailsPopupProps {
  selectedEntry: TimesheetEntry | null;
  timesheetDetails: TimesheetDetails | null;
  timesheetRows?: TimesheetRow[]; // Array of timesheet entries
  onClose: () => void;
  onApprovalSuccess?: () => void;
}


// ... (Utility functions: convertTo24Hour, formatTimeForDisplay, etc. are unchanged)
const convertTo24Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [time, modifier] = timeStr.split(" ");
  if (!time || !modifier) return timeStr; // fallback
  let [hours, minutes] = time.split(":").map(Number);

  if (modifier.toUpperCase() === "PM" && hours < 12) {
    hours += 12;
  }
  if (modifier.toUpperCase() === "AM" && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

const convertTo12Hour = (timeStr: string): string => {
  if (!timeStr) return '';
  const [hours, minutes] = timeStr.split(":").map(Number);

  if (isNaN(hours) || isNaN(minutes)) return timeStr;

  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours % 12 || 12;

  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};

const formatTimeForDisplay = (timeStr: string): string => {
  if (!timeStr) return '';
  // If already in AM/PM format, return as is
  if (timeStr.includes('AM') || timeStr.includes('PM') || timeStr.includes('am') || timeStr.includes('pm')) {
    return timeStr;
  }
  // If in 24-hour format, convert to 12-hour
  return convertTo12Hour(timeStr);
};

const calculateHours = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;
  const start = new Date(`1970-01-01T${startTime}`);
  const end = new Date(`1970-01-01T${endTime}`);
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.warn("Invalid time format", { startTime, endTime });
    return 0;
  }
  const diffMs = end.getTime() - start.getTime();
  const hours = diffMs / (1000 * 60 * 60);
  return parseFloat(hours.toFixed(2));
};

const calculateTotalPrice = (hours: number, rate: number): number => {
  if (isNaN(hours) || isNaN(rate)) return 0;
  return parseFloat((hours * rate).toFixed(2));
};

const TimesheetDetailsPopup: React.FC<TimesheetDetailsPopupProps> = ({
  selectedEntry,
  timesheetDetails,
  timesheetRows = [],
  onClose,
  onApprovalSuccess
}) => {
  if (!selectedEntry || !timesheetDetails) return null;

  const { enableLoader, disableLoader } = useLoader();
  const navigate = useNavigate();
  const [isEdited, setIsEdited] = useState(false);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // ... (all helper functions and state for timesheet rows are fine)
  // Helper function to get initial formatted times
  const getInitialFormattedTime = (timeStr: string) => {
    return formatTimeForDisplay(timeStr || '');
  };

  // Function to process timesheet rows and convert times to AM/PM format
  const processTimesheetRows = (rows: TimesheetRow[]): TimesheetRow[] => {
    return rows.map((row, index) => ({
      ...row,
      start: getInitialFormattedTime(row.start),
      finish: getInitialFormattedTime(row.finish),
      isOriginal: false,
      editVersion: 0,
      id: row.id || index
    }));
  };

  // Create initial timesheet rows - either from props or from timesheetDetails
  const getInitialTimesheetRows = (): TimesheetRow[] => {
    if (timesheetRows && timesheetRows.length > 0) {
      // Use provided timesheet rows array
      return processTimesheetRows(timesheetRows);
    } else {
      // Fallback to single row from timesheetDetails (backward compatibility)
      const startTime = getInitialFormattedTime(timesheetDetails.jobStartTime || '');
      const endTime = getInitialFormattedTime(timesheetDetails.jobEndTime || '');
      const rate = Number(timesheetDetails.price) || 0;
      const startTime24 = convertTo24Hour(startTime);
      const endTime24 = convertTo24Hour(endTime);
      const hours = calculateHours(startTime24, endTime24);
      const total = calculateTotalPrice(hours, rate);

      return [{
        start: startTime,
        finish: endTime,
        hours: hours,
        rate: rate,
        total: total,
        isOriginal: false,
        editVersion: 0,
        id: 1
      }];
    }
  };

  // State for managing multiple timesheet rows

  const [currentTimesheetRows, setCurrentTimesheetRows] = useState<TimesheetRow[]>(() =>
    getInitialTimesheetRows()
  );
  // const [currentTimesheetRows, setCurrentTimesheetRows] = useState<TimesheetRow[]>([]);
  // Update state when timesheetDetails or timesheetRows change
  useEffect(() => {
    if (timesheetDetails || (timesheetRows && timesheetRows.length > 0)) {
      const newRows = getInitialTimesheetRows();
      setCurrentTimesheetRows(newRows);
    }
  }, [timesheetDetails, timesheetRows]);

  // Handle time changes from AwaitingConfirmationCard
  const handleTimesheetRowsChange = (updatedRows: TimesheetRow[]) => {
    if (updatedRows && updatedRows.length > 0) {
      setCurrentTimesheetRows(updatedRows);
    }
  };

  // Helper to generate the JSON payload for recurring jobs
  const generateWeeklyScheduleJson = (rows: TimesheetRow[], jobDate: string): string => {
    const dateObj = new Date(jobDate);
    const dayOfWeek = dateObj.getDay(); // 0=Sun, 1=Mon, etc.
    const activeRows = rows.filter(row => !row.isOriginal);

    if (activeRows.length === 0) return '[]';

    const scheduleForDay = {
      dayOfWeek: dayOfWeek,
      shifts: activeRows.map(row => ({
        jobStartTime: formatTimeToHHMMSS(row.start),
        jobEndTime: formatTimeToHHMMSS(row.finish),
        isRequired: true,
        price: row.total,
        shiftType: 1,
      })),
    };
    return JSON.stringify([scheduleForDay]);
  };
  const formatTimeToHHMMSS = (timeStr: string): string => {
    if (!timeStr) return "00:00:00";

    const match = timeStr.trim().toLowerCase().match(/(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/);
    if (!match) {
      console.warn("Invalid time format:", timeStr);
      return "00:00:00";
    }

    let [_, hourStr, minuteStr = "00", ampm] = match;
    let hour = parseInt(hourStr, 10);
    const minute = parseInt(minuteStr, 10);

    if (ampm === "pm" && hour < 12) hour += 12;
    if (ampm === "am" && hour === 12) hour = 0;

    const hours = hour.toString().padStart(2, '0');
    const minutes = minute.toString().padStart(2, '0');

    return `${hours}:${minutes}:00`;
  };


  const handleApprove = async (rowsToSubmit: TimesheetRow[]) => {
  debugger
  // 1. Initial validation
  if (!timesheetDetails || !timesheetDetails.timesheetId) {
    console.error("Missing timesheet details or timesheet ID");
    return;
  }

  // 2. Set loading states
  setIsSubmitting(true);
  enableLoader();

  try {
    // 3. The CORE LOGIC: Decide which API to call based on the isEdited state
    if (isEdited) {
      // Declare `activeRows` here, at the top of the scope
      const activeRows = currentTimesheetRows.filter(row => !row.isOriginal);
      if (activeRows.length === 0) {
        console.error("Cannot submit, no active timesheet rows found.");
        setIsSubmitting(false);
        disableLoader();
        return;
      }

      // Calculate totals from the current (edited) timesheet rows
      const totalHours = currentTimesheetRows
        .filter(row => !row.isOriginal)
        .reduce((sum, row) => sum + (row.hours || 0), 0);
      
      const calculateTotalAmount = (rows: TimesheetRow[]): number => {
        return rows
          .filter(row => !row.isOriginal)
          .reduce((sum, row) => sum + (row.total || 0), 0);
      }
      
      const totalAmount = calculateTotalAmount(currentTimesheetRows);
      const baseRate = Number(timesheetDetails.price) || 0;

      // Job type mapping
      const jobTypeLabelToIdMap: Record<string, number> = {
        "Unspecified": c.jobType.UNSPECIFIED,
        "One Of Job": c.jobType.BABYSITTING,
        "Recurring Job": c.jobType.NANNYING,
        "Before School Care": c.jobType.BEFORE_SCHOOL_CARE,
        "After School Care": c.jobType.AFTER_SCHOOL_CARE,
        "Before & After School Care": c.jobType.BEFORE_AFTER_SCHOOL_CARE,
        "Au Pair": c.jobType.AU_PAIR,
        "Home Tutoring": c.jobType.HOME_TUTORING,
        "Primary School Tutoring": c.jobType.PRIMARY_SCHOOL_TUTORING,
        "High School Tutoring": c.jobType.HIGH_SCHOOL_TUTORING,
        "Odd Job": c.jobType.ONE_OFF_ODD_JOB,
      };

      // Job type sets - ensuring they are mutually exclusive
      const ODD_AND_ONE_OFF_JOBS = new Set<number>([
        c.jobType.ONE_OFF_ODD_JOB,
        c.jobType.BABYSITTING,
        ...Object.values(c.oddJobType),
      ]);

      const JOB_TYPES_WITH_ROWS = new Set<number>([
        c.jobType.NANNYING,
        c.jobType.BEFORE_SCHOOL_CARE,
        c.jobType.AFTER_SCHOOL_CARE,
        c.jobType.BEFORE_AFTER_SCHOOL_CARE,
        c.jobType.HOME_TUTORING,
        c.jobType.PRIMARY_SCHOOL_TUTORING,
        c.jobType.HIGH_SCHOOL_TUTORING,
      ]);

      const rawJobType = timesheetDetails.jobType;
      const currentJobType: number =
        typeof rawJobType === "number" ? rawJobType : jobTypeLabelToIdMap[rawJobType] ?? 0;

      // Debug logs to see which category the job type belongs to
      console.log("Current job type:", currentJobType);
      console.log("Is in ODD_AND_ONE_OFF_JOBS:", ODD_AND_ONE_OFF_JOBS.has(currentJobType));
      console.log("Is in JOB_TYPES_WITH_ROWS:", JOB_TYPES_WITH_ROWS.has(currentJobType));

      // Construct the base payload for the update
      const updatePayload: any = {
        timeSheetId: timesheetDetails.timesheetId,
        jobId: timesheetDetails.jobId,
        applicantId: timesheetDetails.applicantId,
        newEstimatedJobHours: totalHours,
        newEstimatedJobValue: totalAmount,
        newPrice: baseRate,
      };

      // FIXED CONDITIONAL LOGIC: Check JOB_TYPES_WITH_ROWS first (if it has priority)
      if (JOB_TYPES_WITH_ROWS.has(currentJobType)) {
        console.log("Job is a Recurring Job. Adding weekly schedule JSON.");
        
        // For recurring jobs, we send the schedule ID and the new JSON schedule
        updatePayload.weeklyScheduleId = timesheetDetails.weeklyScheduleId || 0;
        updatePayload.weeklyScheduleJson = generateWeeklyScheduleJson(
          rowsToSubmit,
          timesheetDetails.jobDate
        );
        
        // Start/End time might still be required by the API, send them if needed
        const firstShift = activeRows[0];
        if (firstShift) {
          updatePayload.newJobStartTime = "00:00:00";
          updatePayload.newJobEndTime = "00:00:00";
        }
      }
      else if (ODD_AND_ONE_OFF_JOBS.has(currentJobType)) {
        console.log("Job is an Odd/One-Off Job. Adding start/end times.");
        
        const firstShift = activeRows[0];
        
        // Add actual start/end times for odd and one-off jobs
        if (firstShift && firstShift.start && firstShift.finish) {
          updatePayload.newJobStartTime = formatTimeToHHMMSS(firstShift.start);
          updatePayload.newJobEndTime = formatTimeToHHMMSS(firstShift.finish);
        } else {
          console.warn("Missing start/finish time in firstShift:", firstShift);
        }
        
        updatePayload.weeklyScheduleId = 0;
        updatePayload.weeklyScheduleJson = null;
      }
      else {
        // Handle any other job types that don't fit into the above categories
        console.log("Job type doesn't match any specific category. Using default values.");
        updatePayload.weeklyScheduleId = 0;
        updatePayload.weeklyScheduleJson = null;
        updatePayload.newJobStartTime = "00:00:00";
        updatePayload.newJobEndTime = "00:00:00";
      }

      console.log("Submitting EDITED Timesheet. Payload:", updatePayload);

      // Call the update API
      await new Promise<void>((resolve, reject) => {
        Service.postUpdateHistory(
          (response: any) => {
            console.log("Timesheet update successful:", response);
            resolve();
          },
          (error: any) => {
            console.error("Error updating timesheet:", error);
            reject(error);
          },
          updatePayload
        );
      });

      // Success actions for an update
      onApprovalSuccess?.();
      onClose(); // Assuming you close the modal/view after a successful edit

    } else {
      // --- LOGIC FOR APPROVING AN UNEDITED TIMESHEET (postApprove/Confirm) ---

      const clientType = Number(utils.getCookie(CookiesConstant.clientType));
      const isParent = clientType === c.clientType.INDIVIDUAL;
      const isBusiness = clientType === c.clientType.BUSINESS;
      const showApprove = isParent || isBusiness;

      const apiCallFunction = showApprove
        ? Service.postApproveTimeSheet
        : Service.postConfirmTimeSheet;

      const successLogMessage = showApprove
        ? "Timesheet approved:"
        : "Timesheet confirmed:";

      // Construct the simple payload for approve/confirm
      const approvePayload = {
        timeSheetId: timesheetDetails.timesheetId
      };

      console.log("Submitting UNEDITED Timesheet for approval/confirmation.");

      // Call the approve/confirm API
      await new Promise<void>((resolve, reject) => {
        apiCallFunction(
          (response: any) => {
            console.log(successLogMessage, response);
            resolve();
          },
          (error: any) => {
            console.error("Error submitting timesheet:", error);
            reject(error);
          },
          approvePayload
        );
      });

      // Success actions for approve/confirm
      setIsConfirmed(true); // Update state to show confirmation
      onApprovalSuccess?.();
    }

  } catch (error) {
    // 4. Generic error handling for either API call
    console.error('Submission failed:', error);
    // Optionally show an error message to the user here
  } finally {
    // 5. Always reset loading states
    setIsSubmitting(false);
    disableLoader();
  }
};
  // <-- NEW: This function handles the final navigation step
  const handleNavigationAndClose = () => {
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const isParent = clientType === c.clientType.INDIVIDUAL;
    const isBusiness = clientType === c.clientType.BUSINESS;
    const isHelper = clientType === c.clientType.UNSPECIFIED;
    const showApprove = isParent || isBusiness;

    const basePath = isParent
      ? '/parent-home/timesheet'
      : isBusiness
        ? '/business-home/timesheet'
        : isHelper
          ? '/helper-home/timesheet'
          : '/';

    const isAdjustedTimesheet = selectedEntry?.status === 'Parent Adjusted' ||
      selectedEntry?.status === 'Helper Adjusted';

    const subRoute = isAdjustedTimesheet
      ? 'adjusted-timesheets'
      : showApprove
        ? 'finalized-timesheets'
        : 'awaiting-approval';

    const targetPath = `${basePath}/${subRoute}`;
    console.log('targetPath', targetPath)
    navigate(targetPath);
    onClose(); // Finally, close the popup
  };

  return (
    <div className="overlay-popup">
      <div className="slide-up-card">
        <AwaitingConfirmationCard
          profileName={`${timesheetDetails.firstName}' ${timesheetDetails.lastName}`}
          profileImage={timesheetDetails.originalImageUrl}
          jobType={timesheetDetails.jobType}
          jobDate={timesheetDetails.jobDate}
          jobAddress={timesheetDetails.formattedAddress}
          baseRate={Number(timesheetDetails.price) || 0}
          extraHoursRate={timesheetDetails.overtimeRate}
          initialTimesheetRows={currentTimesheetRows}
          onTimesheetRowsChange={handleTimesheetRowsChange}
          statusText={selectedEntry?.status || ''}
          // --- PROP CHANGES ARE HERE ---
          isEdited={isEdited}
          setIsEdited={setIsEdited}
          isConfirmed={isConfirmed} // <-- Pass the confirmed state down
          isSubmitting={isSubmitting} // <-- Pass the submitting state down
          onSubmit={async () => {
            await handleApprove(currentTimesheetRows);
            handleNavigationAndClose();
          }}// <-- This triggers the API call

          // <-- DYNAMIC PROP: The "Go back" button's action depends on the state
          // Before confirming, it closes the popup.
          // After confirming, it becomes the "Next" button and navigates away.
          onGoBack={isConfirmed ? handleNavigationAndClose : onClose}
        />
      </div>
    </div>
  );
};

export default TimesheetDetailsPopup;