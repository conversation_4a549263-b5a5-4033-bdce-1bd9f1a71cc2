*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

.container {
  background-color: #fff;
  width: 100%;
  min-height: 100vh;
  height: auto;
  overflow-y: auto;
}

.headerWrapper {
  width: 100%;
  padding: 21px 16px;
  border-radius: 20px 20px 0 0;
  background-color: #179d52;
}

.backBtn {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.arrowCircle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 12px;
  color: #fff;
}
 .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
}

.leftSection {
  display: flex;
  flex-direction: column;
}

.title {
  font-weight: 700;
  font-size: 22px;
}

.dateInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 700;
  margin-top: 4px;
}

.profileSection {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.subHeading {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.row {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: underline;
}

.profileImg {
  border-radius: 50%;
  width: 49px;
  height: 45px;
}

.profileName {
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  text-decoration: underline;
}

.rowIcon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  color: #585858;
  margin-top: 2px;
}
.rateBlock {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding-left: 14px;
}

.rateText {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  font-weight: 400;
}

.indented {
  margin-left: 4px; 
  color: #555;
}


.shiftBlock {
  margin-top: 7px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
  width: 100%;
  padding: 16px;
}

.shiftLabel {
  font-weight: 700;
  font-size: 14px;
  margin-bottom: 8px;
}

.shiftRow {
  display: flex;
  gap: 20px;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

.timeFieldCustom {
  flex: 1;
  min-width: 140px;
  position: relative;
}

.shiftRow {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
}

.timeFieldCustom {
  flex: 1 1 45%;
  min-width: 130px; 
}


.timeDropdown {
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
}

.customTimeInput {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 10px 12px;
  background-color: white;
  font-size: 14px;
  font-weight: 500;
  transition: border-color 0.2s;
}

.customTimeInput.active {
  border: 2px solid #179d52;
}

.label {
  color: #333;
}

.divider {
  width: 1px;
  height: 20px;
  background-color: #ccc;
  margin: 0 3px;
}

.time {
  color: #333;
  flex: 1;
}

.dropdownCircle {
  background-color: #f2f2f2;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  display: flex;
}

.dropdownCircle .material-icons {
  font-size: 16px;
  color: #555;
}

.dropdownList {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 2px solid #179d52;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 2px;
}

.dropdownItem {
  padding: 10px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.dropdownItem:hover {
  background-color: #f5f5f5;
}

.dropdownItemSelected {
  background-color: #179d52 !important;
  color: white !important;
  font-weight: 600;
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
  border: 1px solid #179d52 !important;
}

.dropdownItemSelected:hover {
  background-color: #148a47 !important;
  border: 1px solid #148a47 !important;
}

.dropdownItem:first-child {
  border-radius: 12px 12px 0 0;
}

.dropdownItem:last-child {
  border-radius: 0 0 12px 12px;
}

.addShiftBtn {
  background: none;
  border: none;
  color: #585858;
  font-weight: 500;
  margin: 20px auto;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.plusCircle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f0f4f7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  color: #585858;
  border: 1px solid #585858;
}

.footerButtons {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
  padding: 10px;
}

.revertBtn {
  border: none;
  background: none;
  color: grey;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.saveBtn {
  background-color: #d0d0d0;
  color: #fff;
  font-weight: bold;
  border: none;
  padding: 10px 24px;
  border-radius: 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.saveBtn:disabled {
  background-color: #d0d0d0;
  cursor: not-allowed;
}

.saveBtn:enabled,
.saveBtnEnabled {
  background-color: #FFA500 !important;
  color: #333 !important;
}

.saveBtnEnabled:hover {
  background-color: #FFA500 !important;
}

.modifiedTime {
  color: #179D52;
  font-weight: 600;
  font-size: 14px;
}

.dropdownCircle.modifiedTime {
  background-color: #179D52;
  color: #FFFFFF;
  height: 26px;
  width: 26px;
}

@media (max-width: 768px) {
  .header {
    padding: 12px;
  }

  .subHeading {
    flex-direction: row;
    align-items: flex-start;
  }

  .shiftRow {
    flex-direction: row;
    align-items: stretch;
  }

  .footerButtons {
    flex-direction: row;
    align-items: stretch;
  }

  .saveBtn,
  .revertBtn {
    width: 100%;
    text-align: center;
  }
}