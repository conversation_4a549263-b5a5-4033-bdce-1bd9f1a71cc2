import { useDispatch, useSelector } from "react-redux";
import styles from "../styles/MyAddresses.module.css";
import { AppDispatch, RootState } from "../../../store";
import { Divider } from "primereact/divider";
import defaultIcon from "../../../assets/images/addressIcon.png";
import setIcon from "../../../assets/images/setAddressIcon.png";
import { useEffect, useState } from "react";
import CustomDialog from "../../Common/CustomDialog";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import { FaCheck } from "react-icons/fa6";
import {
  ConfirmationPopupRed,
  useConfirmationPopup,
} from "../../Common/ConfirmationPopup";
import Service from "../../../services/services";
import { AutoComplete } from "primereact/autocomplete";
import useLoader from "../../../hooks/LoaderHook";
import editIcon from "../../../assets/images/Icons/editIcon.png";
import removeIcon from "../../../assets/images/Icons/remove.png";
import Auth from "../../../services/authService";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../../Common/HorizontalNavigationMobile";
interface SuburbData {
  name: string;
  state: string;
}
const MyAddresses = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const [dialogVisible, setDialogVisible] = useState(false);
  const [addressLine1, setAddressLine1] = useState(
    sessionInfo.data["addressLine1"] || ""
  );
  const [addressLine2, setAddressLine2] = useState(
    sessionInfo.data["addressLine2"] || ""
  );
  const [suburb, setSuburb] = useState(sessionInfo.data["suburb"] || "");
  const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<any[]>(
    []
  );
  const [postCode, setPostCode] = useState(sessionInfo.data["suburb"] || "");
  const [country, setCountry] = useState(sessionInfo.data["country"] || "au");
  const [state, setState] = useState(sessionInfo.data["state"] || "");
  const [nickname, setNickname] = useState(
    sessionInfo.data["addressLabel"] || ""
  );
  const { disableLoader, enableLoader } = useLoader();
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [nzAuto, setNzAuto] = useState(true);
  const [addressSuggestions, setAddressSuggestions] = useState([]);
  const [nzAddress, setNzAddress] = useState("");
  const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
  const [isFormValid, setIsFormValid] = useState(false);
  const {isMobile}=useIsMobile();
  const handleDialogOpen = () => {
    setIsEditMode(false);
    setDialogVisible(true);
  };

  const handleDialogClose = () => {
    setDialogVisible(false);
    setAddressLine1("");
    setAddressLine2("");
    setCountry("au");
    setCountry(sessionInfo.data["country"] || "au");
    setNickname("");
    setPostCode(null);
    setState("");
    setSuburb("");
    setSelectedAddressId(null);
    setNzAuto(true);
    setNzAddress("");
  };

  const addresses = sessionInfo.loading ? [] : sessionInfo.data["addresses"];
  const defaultAddress = addresses.length > 0 ? addresses[0] : null;

  const isFormComplete = () => {
    return addressLine1 && suburb && postCode && country && state ;
  };

  const handleNext = () => {
    if (isFormComplete()) {
      const updatedAddresses = sessionInfo.data["addresses"].map((address) => {
        if (address.id === selectedAddressId) {
          return {
            ...address,
            addressLabel: nickname,
            addressLine1: addressLine1,
            addressLine2: addressLine2,
            country: country,
            postCode: postCode,
            state: state,
            suburb: suburb,
          };
        }
        return address;
      });

      const payload = {
        ...(sessionInfo.data as object),
        addresses:
          selectedAddressId !== null
            ? updatedAddresses
            : [
                ...sessionInfo.data["addresses"],
                {
                  addressLabel: nickname,
                  addressLine1: addressLine1,
                  addressLine2: addressLine2,
                  autoCompleteSuggestions: [],
                  autoCompleteValue: "",
                  country: country,
                  isPrimaryAddress: false,
                  postCode: postCode,
                  showDetailedAddress: false,
                  state: state,
                  suburb: suburb,
                },
              ],
      };

      enableLoader();
      dispatch(updateUser({ payload })).finally(() => {
        setSuccessMessage(
          selectedAddressId !== null
            ? "Changes successfully made!"
            : "Changes successfully made!"
        );

        setAddressLine1("");
        setAddressLine2("");
        setCountry("au");
        setNickname("");
        setPostCode(null);
        setState("");
        setSuburb("");
        setSelectedAddressId(null);
        disableLoader();
        setDialogVisible(false);
        setNzAuto(true);
        setNzAddress("");
      });
    }
  };

  const handleEditAddress = (address) => {
    setSelectedAddressId(address.id);
    setAddressLine1(address.addressLine1);
    setAddressLine2(address.addressLine2);
    setCountry(address.country);
    setNickname(address.addressLabel);
    setPostCode(address.postCode);
    setState(address.state);
    setSuburb(address.suburb);
    setIsEditMode(true);
    setDialogVisible(true);
    setNzAuto(false);
  };

  const handleRemoveAddress = (addressId) => {
    showConfirmationPopup(
      "Remove Address",
      "Are you sure you want to remove this address?",
      "Remove",
      <img
        src={removeIcon}
        alt="remove icon"
        style={{ height: "15px", width: "13.33px" }}
      />,
      () => {
        const updatedAddresses = addresses.filter(
          (address) => address.id !== addressId
        );

        const payload = {
          ...sessionInfo.data,
          addresses: updatedAddresses,
        };
        enableLoader();

        dispatch(updateUser({ payload })).finally(() => {
          setSuccessMessage("Changes successfully made!");
          disableLoader();
        });
      }
    );
  };
  const handleSetDefaultAddress = (addressId) => {
    const updatedAddresses = addresses.map((address) => {
      return {
        ...address,
        isPrimaryAddress: address.id === addressId,
      };
    });

    const payload = {
      ...sessionInfo.data,
      addresses: updatedAddresses,
    };

    enableLoader();
    dispatch(updateUser({ payload })).finally(() => {
      setSuccessMessage("Changes successfully made!");
      disableLoader();
    });
  };
  const onSuggestionsFetchRequested = async (value) => {
    if (value.length < 6) {
      setAddressSuggestions([]);
      return;
    }

    try {
      Service.addressSearch(
        value,
        (res) => {
          setAddressSuggestions(res);
        },
        (err) => {
          console.error("Error fetching address suggestions:", err);
        }
      );
    } catch (error) {
      console.error("Error during API call:", error);
    }
  };
  const selectAddress = (address) => {
    enableLoader();

    Service.geocodeAddress(
      address,
      (response) => {
        disableLoader();

        setAddressLine1(
          `${response.streetNumber ? response.streetNumber : ""}${
            response.streetName ? " " + response.streetName : ""
          }`
        );
        setPostCode(response.postCode);
        setSuburb(response.suburb);
        setState(response.state);
        setNzAuto(false);
      },
      (error) => {
        disableLoader();
        console.error("Error geocoding address:", error);
      }
    );
  };
  useEffect(() => {
    const isValid =
      addressLine1 &&
      suburb &&
      (country !== "nz" ? state : true) &&
      postCode &&
      nickname;
    setIsFormValid(isValid);
  }, [addressLine1, addressLine2, suburb, state, postCode, nickname, country]);

  const formatAddressString = (address) => {
    if (!address) {
      return "Default Address not available";
    }

    if (!address.addressLine2 || address.addressLine2.trim() === "") {
      return address.addressLine1;
    }

    return `${address.addressLine1}, ${address.addressLine2}`;
  };
  const searchAddress = (event: { query: string }) => {
    Auth.suburbSearch(
      event.query,
      (res) => {
        const suggestions = res.map((item) => ({
          label: `${item.name}, ${(item.state as string).toUpperCase()}`,
          value: item,
        }));
        setAutoCompleteSuggestions(suggestions);
      },
      () => {}
    );
  };
  return (
    <div style={{paddingTop: isMobile && "0px"}} className={styles.addressContainer}>
      <ConfirmationPopupRed confirmationProps={confirmationProps} />
      <header className="flex flex-column md:flex-row">
        <h2 style={{margin: isMobile && "0px"}} className={styles.addressHeader}>My Addresses</h2>
        {successMessage && (
          <div className={styles.successMessageAddress}>
            <div
              style={{
                backgroundColor: "#FFFFFF",
                borderRadius: "50%",
                color: "#179D52",
                padding: "5px",
                height: "16px",
                width: "16px",
                display: "inline-flex",
                alignItems: "center",
                marginRight: "3px",
              }}
            >
              <FaCheck />
            </div>
            {successMessage}
          </div>
        )}
      </header>
      <Divider className={styles.addressDivider} />

      <div className={styles.content}>
        <div  className={styles.addressSection1}>
          <div className={styles.defaultAddressContainer}>
            <img
              src={defaultIcon}
              alt="Default Address Icon"
              className={styles.addressIcon}
            />
            <div>
              <p className={styles.defaultAddress}>
                {formatAddressString(defaultAddress)}
              </p>
              <p className={styles.defaultHeader}>Default address</p>
            </div>
            <button
              className={styles.editBtn}
              onClick={() =>
                handleEditAddress(sessionInfo.data["addresses"][0])
              }
            >
              <img
                src={editIcon}
                alt="Edit"
                style={{ marginRight: "5px", width: "13px", height: "13px" }}
              />
              Edit Address
            </button>
          </div>
        </div>
        <Divider className={styles.addressDivider} />

        {addresses.slice(1).map((address, index) => (
          <div key={index} className={styles.addressSectionUpdate}>
            <div className={styles.defaultAddressContainer}>
              <img
                src={setIcon}
                alt="Default Address Icon"
                className={styles.setAddressIcon}
              />
              <div>
                <p
                  className={styles.defaultAddress}
                  style={{ marginTop: "0px" }}
                >
                  {address.addressLine1}
                  {address.addressLine2 ? `, ${address.addressLine2}` : ""}
                </p>

                <button
                  className={styles.defaultBtn}
                  onClick={() => handleSetDefaultAddress(address.id)}
                >
                  Set as Default
                </button>
              </div>
              <button
                className={styles.editBtn}
                onClick={() => handleEditAddress(address)}
              >
                <img
                  src={editIcon}
                  alt="Edit Address"
                  style={{ marginRight: "5px", width: "13px", height: "13px" }}
                />
                Edit Address
              </button>

              <button
                className={styles.removeBtn}
                onClick={() => handleRemoveAddress(address.id)}
              >
                Remove
              </button>
            </div>

            {index < addresses.length - 2 && (
              <Divider className={styles.addressDividerUpdate} />
            )}
          </div>
        ))}
        <Divider className={styles.addressDivider} />
        <div className={styles.addressItem}>
          <button className={styles.AddBtn} onClick={handleDialogOpen}>
            <img
              src={defaultIcon}
              alt="Default Address Icon"
              style={{ width: "18px", height: "18px" }}
            />
            Add Address
          </button>
          <CustomDialog
            visible={dialogVisible}
            onHide={handleDialogClose}
            closeClicked={handleDialogClose}
            profileCompletion={0}
          >
            <div className={styles.addAddressContainer}>
              <header>
                {!isMobile  ? (
                  <h2 className={styles.addAddressHeader}>
                  {isEditMode ? "Edit Address" : "Add Address"}
                </h2>

                ):(<h2 style={{paddingBottom:isMobile && "0px"}} className={styles.addAddressHeader}>
                  {isEditMode ? (
                    <HorizontalNavigation 
                     title="Edit Address"
                     onBackClick={handleDialogClose}
                    />
                  ):(
                    <HorizontalNavigation  
                     title="Add Address"
                     onBackClick={handleDialogClose}
                    />
                  )}
                </h2>)}
                
                <Divider className={styles.addaddressDivider} />
              </header>

              <div className={styles.flexContainer}>
                <div className={styles.addAddressDiv}>
                  {!isEditMode ? (
                    <div className={styles.dropdownContainerCountry}>
                      <p className={styles.addressHead}>Country</p>
                      <Dropdown
                        id="Country"
                        name="Country"
                        options={[
                          { label: "Australia", value: "au" },
                          { label: "New Zealand", value: "nz" },
                        ]}
                        placeholder="Select Country"
                        value={country}
                        onChange={(e) => {
                          setCountry(e.target.value);
                          setAddressLine1("");
                          setAddressLine2("");
                          setNickname("");
                          setPostCode(null);
                          setState("");
                          setSuburb("");
                          setSelectedAddressId(null);
                        }}
                        className="input-placeholder"
                      />
                    </div>
                  ) : (
                    <div className={styles.dropdownContainerCountry}>
                      <p className={styles.addressHead}>Country</p>
                      <Dropdown
                        disabled
                        id="Country"
                        name="Country"
                        options={[
                          { label: "Australia", value: "au" },
                          { label: "New Zealand", value: "nz" },
                        ]}
                        placeholder="Select Country"
                        value={country}
                        onChange={(e) => {
                          setCountry(e.target.value);
                          setAddressLine1("");
                          setAddressLine2("");
                          setNickname("");
                          setPostCode(null);
                          setState("");
                          setSuburb("");
                          setSelectedAddressId(null);
                        }}
                        className="input-placeholder"
                      />
                    </div>
                  )}

                  {country === "nz" && nzAuto ? (
                    <div className={styles.nzAddressContainer}>
                      <p className={styles.addressHead}>Address</p>

                      <div
                        className="input-container"
                        style={{ marginTop: "11px", width: "100%" }}
                      >
                        <AutoComplete
                          value={nzAddress}
                          suggestions={addressSuggestions}
                          completeMethod={(e) =>
                            onSuggestionsFetchRequested(e.query)
                          }
                          className={styles.nzAddressDropdown}
                          placeholder="Start typing your address"
                          onChange={(e) => {
                            const inputValue = e.value;

                            // Validate input to ensure it's not numeric
                            if (/^\d+$/.test(inputValue)) {
                              return; // Ignore numerical input
                            }
                            setNzAddress(inputValue);
                          }}
                          onSelect={(e) => selectAddress(e.value)}
                          style={{ width: "100%" }}
                        />
                      </div>

                      <button
                        className={styles.switchToBtn}
                        onClick={() => {
                          setNzAuto(false);
                        }}
                      >
                        Type Address Manually
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className={styles.addAddressDiv}>
                        <p className={styles.addressHead}>Address</p>
                        <div
                          className="input-container"
                          style={{ marginTop: "18px", width: "100%" }}
                        >
                          <InputText
                            id="addressLine1"
                            name="addressLine1"
                            placeholder=""
                            style={{ width: "100%" }}
                            className="input-placeholder"
                            value={addressLine1}
                            onChange={(e) => {
                              const inputValue = e.target.value;
                              const filteredValue = inputValue.trimStart();
                              setAddressLine1(filteredValue);
                            }}
                          />
                          <label
                            htmlFor="addressLine1"
                            className={`label-name ${
                              addressLine1 ? "label-float" : ""
                            }`}
                          >
                            Address Line 1
                          </label>
                        </div>
                        <div
                          className="input-container"
                          style={{ marginTop: "25px", width: "100%" }}
                        >
                          <InputText
                            id="addressLine2"
                            name="addressLine2"
                            style={{ width: "100%" }}
                            placeholder=""
                            className="input-placeholder"
                            value={addressLine2}
                            onChange={(e) => {
                              const inputValue = e.target.value;
                              const filteredValue = inputValue.trimStart();
                              setAddressLine2(filteredValue);
                            }}
                          />
                          <label
                            htmlFor="addressLine2"
                            className={`label-name ${
                              addressLine2 ? "label-float" : ""
                            }`}
                          >
                            Address Line 2
                          </label>
                        </div>
                      </div>

                      <div
                        className={`${styles.addAddressSecond} flex flex-column md:flex-row`}
                      >
                        <div
                          className="input-container"
                          style={{
                            marginTop: "18px",
                            maxWidth: "259px",
                            height: "56px",
                          }}
                        >
                          <p className={styles.addressHead}>Suburb</p>
                          <AutoComplete
                            id="Suburb"
                            name="Suburb"
                            placeholder=""
                            className={`${styles.autoComplete} input-placeholder`}
                            suggestions={autoCompleteSuggestions}
                            completeMethod={searchAddress}
                            value={suburb}
                            field="label"
                            onChange={(e) => {
                              const inputValue = e.value;
                              const filteredValue =
                                typeof inputValue === "string"
                                  ? inputValue.trimStart()
                                  : inputValue.label;

                              // Validate input to ensure it's not numeric
                              if (/^\d+$/.test(filteredValue)) {
                                return; // Ignore numerical input
                              }
                              setSuburb(filteredValue);
                            }}
                            onSelect={(e) => {
                              const selectedSuburb = e.value.value;

                              setSuburb(selectedSuburb.name);
                              setState(selectedSuburb.state.toLowerCase());
                              setPostCode(selectedSuburb.postCode);
                            }}
                          />
                        </div>
                        {country !== "nz" ? (
                          <div className={styles.dropdownContainer}>
                            <p className={styles.addressHead}>State</p>

                            <Dropdown
                              id="State"
                              name="State"
                              options={[
                                { label: "NSW", value: "nsw" },
                                { label: "ACT", value: "act" },
                                { label: "VIC", value: "vic" },
                                { label: "SA", value: "sa" },
                                { label: "QLD", value: "qld" },
                                { label: "WA", value: "wa" },
                              ]}
                              placeholder=""
                              className="input-placeholder"
                              value={state}
                              onChange={(e) => setState(e.target.value)}
                            />
                          </div>
                        ) : (
                          <div className={styles.dropdownContainer}>
                            <p className={styles.addressHead}>City</p>
                            <InputText
                              id="city"
                              name="city"
                              placeholder=""
                              className="input-placeholder"
                              value={state}
                              onChange={(e) =>
                                setState(e.target.value.replace(/\s+/g, ""))
                              }
                            />
                          </div>
                        )}

                        <div
                          className="input-container"
                          style={{
                            marginTop: "18px",
                            maxWidth: "134px",
                            height: "56px",
                          }}
                        >
                          <p className={styles.addressHead}>Post code</p>
                          <InputText
                            id="postCode"
                            name="postCode"
                            placeholder=""
                            className="input-placeholder"
                            value={postCode === null ? "" : postCode}
                            onChange={(e) => {
                              const inputValue = e.target.value;
                              const filteredValue = inputValue.replace(
                                /\D/g,
                                ""
                              );
                              setPostCode(filteredValue);
                            }}
                          />
                        </div>
                      </div>

                      <div className={styles.addAddressDiv}>
                        <div
                          className={`input-container ${styles.nicknameCont}`}
                          style={{ marginTop:isMobile ? "65px" : "18px", width: "100%" }}
                        >
                          <p className={styles.addressHead}>Nickname</p>
                          <InputText
                            style={{ width: "100%" }}
                            id="Nickname"
                            name="Nickname"
                            placeholder="For example, Home, Melb Work, Holiday House"
                            className="input-placeholder"
                            value={nickname}
                            onChange={(e) => {
                              const inputValue = e.target.value;
                              const filteredValue = inputValue.trimStart();
                              setNickname(filteredValue);
                            }}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <footer className={styles.dialogFooter}>
                <br />
                <button
                  onClick={handleNext}
                  className={styles.saveAddress}
                  disabled={!isFormComplete()}
                >
                  Save
                </button>
              </footer>
            </div>
          </CustomDialog>
        </div>
        <Divider className={styles.addressDivider} />
        <div className={styles.addressItem}></div>
      </div>
    </div>
  );
};

export default MyAddresses;
