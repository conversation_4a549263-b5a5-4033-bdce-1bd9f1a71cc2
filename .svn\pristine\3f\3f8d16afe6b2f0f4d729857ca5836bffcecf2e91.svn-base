import React, { useEffect, useRef, useState } from "react";
import { Avatar } from "primereact/avatar";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "../../store";
import { removeSession } from "../../store/slices/sessionInfoSlice";
import {
  updateAccountAndSettingsActiveTab,
  updateAccountAndSettingsActiveTabBuisness,
  updateChatWindowState,
  updateShowAccountAndSettings,
  updateShowAccountAndSettingsBuisness,
  updateShowProfileActivation,
} from "../../store/slices/applicationSlice";

import style from "../Common/styles/home-header.module.css";
import { FiGift, FiUser, FiUsers } from "react-icons/fi";
import aboutMeIcon from "../../assets/images/Icons/about_me.png";
import familyIcon from "../../assets/images/Icons/my_family.png";
import childrenIcon from "../../assets/images/Icons/my_child.png";
import addressIcon from "../../assets/images/Icons/my_addresses.png";
import paymentsIcon from "../../assets/images/Icons/payments.png";
import familyMembershipIcon from "../../assets/images/Icons/family_membership.png";
import settingsIcon from "../../assets/images/Icons/settings.png";
import logoutIcon from "../../assets/images/Icons/logout.png";
import chatIcon from "../../assets/images/Icons/chat.png";
import giftIcon from "../../assets/images/Icons/refer.png";
import { RxHamburgerMenu } from "react-icons/rx";
import CustomDialog from "../Common/CustomDialog";
import AccountLayoutBuisness from "./AccountSettingBuisness/AccountLayoutBuisness";
import menu from "../../assets/images/Icons/menu.png";
import { updateActiveTabIndex } from "../../store/slices/accountSettingSlice";
import useIsMobile from "../../hooks/useIsMobile";
import utils from "../../components/utils/util";
function HomeHeaderBuisness() {
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const [showMenu, setShowMenu] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { enableAccountAndSettingsBuisness } = useSelector((state: RootState) => state.applicationState);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const options = [
    {
      option: "My Account",
      options: [
        {
          icon: <img src={aboutMeIcon} alt="Account holder" width="14.4px" height="14.47px" />,
          optionName: "Account holder",
          func: () => {
            setShowMenu(false);
            dispatch(updateAccountAndSettingsActiveTabBuisness(0));
            dispatch(updateActiveTabIndex(29));
            dispatch(updateShowAccountAndSettingsBuisness(true));
          },
        },
        {
          icon: <img src={familyIcon} alt="My Business" width="14.4px" height="12.92px" />,
          optionName: "My Business",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(30));
            dispatch(updateAccountAndSettingsActiveTabBuisness(1));
            dispatch(updateShowAccountAndSettingsBuisness(true));
          },
        },
        ...(sessionInfo.data?.["client"]?.["clientCategory"] === 3
          ? [
              {
                icon: <img src={addressIcon} alt="My Addresses" width="14.4px" height="12.92px" />,
                optionName: "My Addresses",
                func: () => {
                  setShowMenu(false);
                  dispatch(updateActiveTabIndex(36));
                  dispatch(updateAccountAndSettingsActiveTabBuisness(2));
                  dispatch(updateShowAccountAndSettingsBuisness(true));
                },
              },
            ]
          : []),
        // {
        //     icon: (
        //         <img src={childrenIcon} alt="My Children" width="16.5px" height="15.82px" />
        //     ),
        //     optionName: 'My Children',
        //     func: () => {
        //         setShowMenu(false);
        //         dispatch(updateAccountAndSettingsActiveTabBuisness(2));
        //         dispatch(updateShowAccountAndSettingsBuisness(true));
        //     },
        // },
        // {
        //     icon: (
        //         <img
        //             src={addressIcon}
        //             alt="My Addresses"
        //             width="14.32px"
        //             height="14.32px"
        //         />
        //     ),
        //     optionName: 'My Addresses',
        //     func: () => {
        //         setShowMenu(false);
        //         dispatch(updateAccountAndSettingsActiveTabBuisness(3));
        //         dispatch(updateShowAccountAndSettingsBuisness(true));
        //     },
        // },
      ],
    },
    {
      option: "Billing",
      options: [
        {
          icon: <img src={paymentsIcon} alt="Payments" width="13.5px" height="15.07px" />,
          optionName: "Payments",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(31));
            dispatch(updateAccountAndSettingsActiveTabBuisness(5));
            dispatch(updateShowAccountAndSettingsBuisness(true));
          },
        },
        {
          icon: <img src={familyMembershipIcon} alt="Pricing" width="9.33px" height="16.07px" />,
          optionName: "Pricing",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(32));
            dispatch(updateAccountAndSettingsActiveTabBuisness(6));
            dispatch(updateShowAccountAndSettingsBuisness(true));
          },
        },
      ],
    },
    {
      option: "Settings",
      options: [
        {
          icon: <img src={settingsIcon} alt="General Settings" width="16px" height="16.08px" />,
          optionName: "General Settings",
          func: () => {
            setShowMenu(false);
            dispatch(updateActiveTabIndex(33));
            dispatch(updateAccountAndSettingsActiveTabBuisness(7));
            dispatch(updateShowAccountAndSettingsBuisness(true));
          },
        },
        {
          icon: <img src={logoutIcon} alt="Log out" width="16px" height="16.08px" />,
          optionName: "Log out",
          func: () => {
            setShowMenu(false);
            try {
              dispatch(removeSession());
              dispatch(updateShowProfileActivation(false));
              utils.obliterateEverything();
            } catch (_e) {
            } finally {
              navigate("/");
            }
          },
        },
      ],
    },
    {
      option: "Support",
      options: [
        {
          icon: <img src={chatIcon} alt="Log out" width="16px" height="16.08px" />,
          optionName: "Contact Customer Service",
          func: () => {
            setShowMenu(false);
            dispatch(updateChatWindowState(true));
          },
        },
      ],
    },
  ];

  function checkValue<T>(val: any, defaultValue: T): T {
    if (val !== null && val !== undefined) {
      return val;
    }
    return defaultValue;
  }

  const firstName = checkValue(sessionInfo?.data?.["firstName"], "");
  const lastName = checkValue(sessionInfo?.data?.["lastName"], "");

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const { isMobile } = useIsMobile();
  return (
    <div className="fixed z-3 pt-2 md:pt-4 pr-2 md:pr-4 w-full flex justify-content-end pointer-events-none">
      {!isMobile && (
        <div
          ref={buttonRef}
          className="flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl shadow-3 gap-1 md:gap-2 align-items-center w-min pointer-events-auto hover:shadow-4"
          onClick={() => setShowMenu(!showMenu)}
        >
          <img
            // className="cursor-pointer"
            src={menu}
            alt="hamburger"
            height={"15px"}
            width={"20.69"}
          />
          <div
            className="border-circle p-1 md:p-2 flex justify-content-center align-items-center pointer-events-none select-none"
            style={{
              height: "24px",
              width: "24px",
              backgroundColor: "#9F9F9F",
              fontSize: "10px",
              fontWeight: "600",
              color: "white",
              overflow: "hidden",
            }}
          >
            {sessionInfo.data && sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
              <img
                src={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
                alt="Profile"
                className="profilePhoto"
                style={{
                  height: "30.71px",
                  width: "30.65px",
                  borderRadius: "50%",
                  objectFit: "contain",
                }}
              />
            ) : (
              `${firstName[0]}.${lastName[0]}`
            )}
          </div>
        </div>
      )}
      {showMenu && (
        <div
          ref={menuRef}
          className="fixed bg-white shadow-3 p-2 pt-3 pb-4 overflow-y-auto overflow-x-hidden w-64 md:w-80 border-round-2xl pointer-events-auto"
          style={{ top: "70px", maxHeight: "80vh" }}
        >
          <div className="flex flex-column justify-content-center align-items-center">
            <div className="w-full flex justify-content-center align-content-center gap-2 md:gap-3 mb-2 mt-2">
              <Avatar
                className={`${style.imageStyle}`}
                label={`${firstName.charAt(0)}.${lastName.charAt(0)}`}
                image={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
                shape="circle"
                size="large"
                style={{
                  fontSize: "16px",
                  fontWeight: "600",
                  backgroundColor: "#9F9F9F",
                  color: "#FFFFFF",
                }}
              />
              <h3 style={{ color: "#585858" }} className="text-base md:text-lg font-semibold m-0 mt-2">
                {`${firstName} ${lastName}`}
              </h3>
            </div>
            {options.map((option, index) => (
              <div key={index} className="w-full flex flex-column mb-2 pl-3 md:pl-5">
                <h3 style={{ color: "#585858" }} className="text-base md:text-lg font-semibold m-0 mt-2">
                  {option.option}
                </h3>
                <div
                  className="pl-2 md:pl-3 mt-1 md:mt-2"
                  style={{
                    borderLeft: "2px solid #F1F1F1",
                  }}
                >
                  {option.options.map((value, optionIndex) => (
                    <div key={optionIndex} className="flex justify-content-start align-items-center gap-2 md:gap-3">
                      <div style={{ width: "15px" }}>{value.icon}</div>
                      <p
                        className={`m-0 w-min cursor-pointer text-sm md:text-base ${style.noTextWrap}`}
                        style={{
                          paddingBlock: "4px",
                          color: "#585858",
                          fontWeight: "500",
                        }}
                        onClick={value.func}
                      >
                        {value.optionName}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            {/* <button
                            className="border-round-xl px-2 md:px-3 py-1 md:py-2 mb-3 flex justify-content-center align-items-center gap-1 md:gap-2 cursor-pointer text-xs md:text-sm"
                            style={{
                                border: '1px solid #585858',
                                backgroundColor: 'transparent',
                                color: '#585858',
                                fontWeight: '500',
                            }}
                        >
                            <img src={giftIcon} alt="Gift Icon" width="14.4px" height="14.48px" />{' '}
                            Get $10 for each friend you refer
                        </button> */}
          </div>
        </div>
      )}

      <CustomDialog
        visible={enableAccountAndSettingsBuisness}
        onHide={() => {
          dispatch(updateShowAccountAndSettingsBuisness(false));
        }}
        closeClicked={() => {
          dispatch(updateShowAccountAndSettingsBuisness(false));
        }}
        profileCompletion={0}
      >
        <AccountLayoutBuisness visible={enableAccountAndSettingsBuisness} />
      </CustomDialog>
    </div>
  );
}

export default HomeHeaderBuisness;
