.p-dialog {
  border-radius: 20px;
  border: none;
}

.custom-dialog {
  background-color: #ffffff;
  height: auto;
  max-height: 80vh;
  width: 728px;
  min-height: 505px;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

.custom-dialog > .custom-gradient-bg-1,
.custom-dialog > .custom-gradient-bg-2,
.custom-dialog > .custom-gradient-bg-3 {
  position: absolute;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  border-radius: 20px;
}

.custom-gradient-bg-1 {
  transform: rotate(-3deg);
  height: 90%;
  width: 97%;
  top: 0;
  left: -20px;
  z-index: -1;
  background: linear-gradient(90deg, #37a950, #ffa500);
}

.custom-gradient-bg-2 {
  transform: rotate(-3deg);
  height: 90%;
  width: 95%;
  bottom: -10px;
  right: 0px;
  z-index: -1;
  background: linear-gradient(360deg, #37a950, #ffa500);
}

.custom-gradient-bg-3 {
  transform: rotate(1deg);
  height: 90%;
  width: 95%;
  bottom: 5px;
  right: -10px;
  background-color: #37a950;
  z-index: -1;
}

.custom-dialog > .custom-dialog-content {
  height: 100%;
  width: 100%;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.custom-dialog-content img {
  max-width: 100%;
  height: auto;
}

.progress-container {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.progress-container .progress-label {
  display: flex;
  align-items: center;
  margin-top: 5px;
  width: 100%;
  justify-content: flex-end;
}

.progress-container .progress-label img {
  margin-right: 5px;
}

@media (max-width: 600px) {
  .custom-dialog {
    width: 80vw;
  }

  .custom-dialog-content {
    flex-direction: column;
  }

  .custom-welcom-text {
    flex-direction: column;
  }
}
