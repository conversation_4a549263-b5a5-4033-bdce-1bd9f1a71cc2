.container {
  width: 100%;
  height: 100%;
}

.p-dialog {
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  max-height: 90%;
  transform: scale(1);
  position: relative;
  width: 72% !important;
}

.reviewPost {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #585858;
  padding-top: 38px;
  height: auto;
  padding-bottom: 30px;
}

.criteriaValue {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  margin-bottom: 0px;
}

.criteriaLabel {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
}

.okGotItBtn {
  height: 38px;
  width: 100px;
  font-size: 12px;
  font-weight: 700;
  background-color: #ffa500;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.headerH3 {
  font-size: 30px;
  font-weight: 700;
  color: #585858;
}

.headerH4 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: -7px;
}

.jobSummary {
  border: 1px solid #dfdfdf;
  width: 75%;
  /* height: 400px; */
  border-radius: 20px;
  /* padding: 15px 40px; */
  padding-left: 30px;
  padding-right: 20px;
  /* margin-right: 2%; */
}

.tagButton {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tagData {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  border-radius: 10px;
  padding: 3px;
  border-radius: 10px !important;
}

.yourJobDescription {
  font-size: 16px;
  font-weight: 300;
  color: #585858;
}

.jobPrice {
  float: right;
}

.jobPriceDoller {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  height: 33px;
}

.jobOvertime {
  font-size: 16px;
  font-weight: 700;
  color: #585858;
  height: 33px;
}

.postJobBtn {
  width: 60%;
  height: 46px;
  border-radius: 20px;
  background-color: #ffa500;
  border: none;
  margin-top: 1.5%;
  color: #ffffff;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
}

.postJobBtn:hover {
  box-shadow: 0px 4px 4px 0px #00000040;
}

.goBack {
  margin-top: 2%;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  cursor: pointer;
  align-items: center;
  display: flex;
}

.errorMessage {
  color: red;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: 500;
  padding-inline: 15px;
  /* display: none; */
  padding-bottom: 20px;
}

.containerMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
  position: relative;
}

.reviewPostMobile {
  display: flex;
  flex-direction: column;
  color: #585858;
  padding-inline: 25px;
  width: 100%;
}

.jobSummaryMobile {
  width: 100%;
}

.headerH4Mobile {
  font-size: 16px;
  font-weight: 700;
  color: #179D52;
}

.yourJobDescriptionMobile {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
}

.seeMoreButton {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

.fixedFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  /* Optional shadow for better visibility */
  padding: 10px 0;
  /* Padding for the button */
  text-align: center;
  z-index: 999;
  /* Ensure it stays on top of other elements */
  margin-top: auto;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
}

/* Style for the Save button */
.nextButtonMobile {
  padding: 10px 20px;
  width: 191px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #FFA500;
  /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
  width: 100%;
  padding: 10px 20px;
  width: 311px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}
.addressTag{
  margin: 0;
  margin-top: 5px;
  padding: 0;
  color: #179D52;
  font-weight: 700;
}
.customToastContent {
  padding: 12px 16px !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
  /* color: #fff !important; */
}

.customToast .p-toast-message-close {
    display: none !important;
}

/* Remove default icon */
.customToast .p-toast-message-icon {
    display: none !important;
}