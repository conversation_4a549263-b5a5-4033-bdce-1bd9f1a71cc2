/* SidePanel.module.css */
.accountSidePanel {
  flex: 1; /* Take up available space */

  padding: 20px; /* Space inside left pane */
  background-color: #fafafa;
  padding-top: 0px;
  height: 100%;
}

/* Header styles */
.header {
  padding: 15px; /* Optional padding for header */
  padding-bottom: 0px;
  padding-top: 0px;
}

/* User profile styles */
.userProfile {
  display: flex;
  align-items: center;
  transition: all 0.3s ease; /* Smooth transition */
}

.userProfile > img {
  border-radius: 50%; /* Circular profile image */
  height: 45px; /* Adjust as needed */
  width: 48px; /* Adjust as needed */
}

/* Content styles */
.accountSidePanelcontent {
  flex-grow: 1; /* Grow to fill available space */
  display: flex;
  flex-direction: column; /* Stack content divs vertically */
  justify-content: space-between; /* Equal gaps */
  padding: 15px; /* Padding inside the content area */
}

.profileName {
  font-size: 20px;
  font-weight: 600;
  line-height: 30px;
  color: #585858;
}

/* Style for individual content divs */
.accountSidePanelcontentDiv {
  padding: 10px;
  border-radius: 5px;
  padding-top: 0px;
  padding-bottom: 0px;
}
.myaccounttitle {
  font-size: 20px;
  font-weight: 600;
  color: #585858;
  margin-bottom: 0px;
  margin-top: 2px;
}
.verticalTabContainer {
  display: flex;
  flex-direction: row; /* Align icons and tabs in a row */
  width: 100%;
  height: min-content;
  margin-inline: auto;
}

.icons {
  display: flex;
  flex-direction: column;
  width: 40px;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
  padding-top: 13px;
  font-size: 18px;
}

.iconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  width: 40px;
  text-align: center;
  cursor: pointer;
}

.tabs {
  display: flex;
  flex-direction: column;
  width: calc(100% - 40px); /* Adjust width according to the icon container */
  padding: 10px 0;
  border-left: none; /* No border for this section */
}

.tabItem {
  width: max-content;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  line-height: 24px;
  color: #585858;
}

.textContainer {
  flex-grow: 1;
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #585858;
  padding-top: 18px;
  padding-left: 14px;
  height: 38px;
}

.tabItem .spaceDiv{
  position: relative;
  height: 100%;
  left: 0;
  top: 0;
  width: 4px;
  background-color: #F1F1F1;

}
.tabItem .spaceDiv[data-index="first"]{
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.tabItem .spaceDiv[data-index="last"] {
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
.activeTab .spaceDiv::after{
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #ffa500;
  border-radius: 20px;
}

.activeTabColor {
  color: #ffa500; 
  font-weight: 700;
  font-size: 14px;
}

.activeTabColor .iconContainer img {
  filter: brightness(0) saturate(100%) invert(55%) sepia(74%) saturate(2878%) hue-rotate(358deg) brightness(102%) contrast(105%);
}

.paneldropdown {
  width: 100%; /* Full width for the dropdown */
}
.tabItem > div {
  border: none !important;
  box-shadow: none !important;
  max-width: 100%;
}
.tabItem > div[class="p-focus"] {
  border: none !important;
  box-shadow: none !important;
  max-width: 100%;
}
.needbtn {
  font-weight: 400;
  padding: 8px 16px;
  font-size: 14px;
  background-color: transparent;
  color: #585858;
  border: none;
  border-radius: 4px;
  cursor: pointer; /* Change cursor to pointer on hover */
}
.referbtn {
  font-size: 12px !important;
  background-color: transparent !important;
  border: 1px solid #585858 !important;
  color: #585858 !important;
  border-radius: 10px !important;
  width: max-content !important;
  height: 38px !important;
  padding: 10px !important;
  font-weight: 500 !important;
  line-height: 18px !important ;
  margin-top: 10px !important;
  box-shadow: none !important;
  margin-left: 7px !important;
  display: flex;
  justify-content: center;
  align-items: normal;
  gap: 9px;
  transition: all 0.1s ease-in-out;
}
.referbtn:hover{
  color: #FFA500 !important;
  font-weight: 700 !important;
  border: 2px solid #FFA500 !important;
  box-shadow: 0px 4px 4px 0px #********;
}
.defaultIcon {
  display: block;
  transition: opacity 0.1s ease-in-out;
}

.hoverIcon {
  display: none;
  transition: opacity 0.1s ease-in-out;
}

.referbtn:hover .defaultIcon {
  display: none;
}

.referbtn:hover .hoverIcon {
  display: block;
}
.accountSidePanelfooter {
  color: #585858;
  text-align: center;
  font-size: 12px;
  line-height: 18px;
  font-weight: 500;
}

/* Media query for smaller screens */
@media (max-width: 868px) {
  .accountSidePanel {
    position: absolute; /* Allow overlay on small screens */
    top: 0;
    right: 0; /* Align to the right */
    height: 100%; /* Full height */
    z-index: 1000; /* Ensure it's on top */
    transition: transform 0.3s ease; /* Smooth transition */
    transform: translateX(100%); /* Initially hide off-screen */
  }
}
