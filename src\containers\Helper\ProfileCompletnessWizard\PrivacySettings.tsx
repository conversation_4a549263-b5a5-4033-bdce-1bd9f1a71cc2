import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import CustomButton from "../../../commonComponents/CustomButton";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import useLoader from "../../../hooks/LoaderHook";
import Service from "../../../services/services";
import { updateSessionInfo } from "../../../store/tunks/sessionInfoTunk";
import { IoEye } from "react-icons/io5";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";
const PrivacySettings = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [privacySetting, setPrivacySetting] = useState("");
  const [isPublicProfileAccessible, setIsPublicProfileAccessible] = useState<boolean | null>(null);
  const { disableLoader, enableLoader } = useLoader();
  const {isMobile}=useIsMobile()
  const handlePrivacyChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setPrivacySetting(value);
    setIsPublicProfileAccessible(value === 'public');
  };

  const handleprev = () => {
    dispatch(decrementProfileActivationStep());
  };
  useEffect(() => {
    if (sessionInfo.data) {
      const isPublic = sessionInfo.data?.['isPublicProfilePhotoAccessible'];
      setPrivacySetting(isPublic ? "public" : "private");
      setIsPublicProfileAccessible(isPublic);

    }
  }, [sessionInfo]);

  const privacyOptions = [
    {
      value: "private",
      label: "Private",
      description:
        "Your profile information is only visible to other Juggle Street users.",
    },
    {
      value: "public",
      label: "Public",
      description:
        "Your general profile info and photo are visible to the public. This does NOT include personal details such as phone number, email, and home address.",
    },
  ];

  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  const handleContinue = async () => {
    enableLoader();
    const payload = {
      isPublicProfileAccessible: isPublicProfileAccessible,
      isPublicProfilePhotoAccessible: isPublicProfileAccessible
    };
    const payload2 = {
      ...sessionInfo.data, // Include existing sessionInfo data
      isPublicProfileAccessible: isPublicProfileAccessible,
      isPublicProfilePhotoAccessible: isPublicProfileAccessible,
    };
    Service.updateProfileVisibility(payload,
      () => {
        dispatch(updateSessionInfo({ payload: payload2 }));
        // dispatch(incrementProfileActivationStep());
        disableLoader();
      },
      (error) => {
        disableLoader();
        dispatch(incrementProfileActivationStep());
        
      });
  };

  return (
    <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
      <ProfileCompletenessHeader
        title="Privacy Settings"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div style={{paddingInline:isMobile && "15px"}} className="flex">
        <h1
          className="p-0 m-0 txt-clr flex gap-1 flex-wrap font-medium line-height-1"
          style={{ fontSize: "18px" }}
        >
          <span><IoEye style={{ fontSize: '20px' }} /></span>  Juggle Street Profile
        </h1>
      </div>
      <div style={{paddingInline:isMobile && "15px"}} className="mt-3">
        {privacyOptions.map((option) => (
          <div key={option.value} className="">
            <input
              type="radio"
              id={option.value}
              name="privacy"
              value={option.value}
              checked={privacySetting === option.value}
              onChange={handlePrivacyChange}
              className="cursor-pointer"
            />

            <label
              htmlFor={option.value}
              className="cursor-pointer txt-clr font-bold"
              style={{ fontSize: "16px" }}
            >
              {option.label}
            </label>
            <p
              className="p-0 m-0 pl-4 cursor-pointer txt-clr font-medium"
              style={{ fontSize: "14px" }}
            >
              {option.description}
            </p>
          </div>
        ))}
        <div>
          <h1
            className="p-0 m-0 pl-4 mt-3 txt-clr font-medium"
            style={{ fontSize: "14px" }}
          >
            <strong>Note</strong> having a Public profile will extend your reach
            and improve your chances of being offered jobs. You will also be
            able to share your Public profile on social media.{" "}
          </h1>
        </div>
      </div>
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left"></i>
              Previous
            </>
          }
          onClick={handleprev}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
             margin:isMobile && "5px"
          }}
        />
        <div style={{ flexGrow: 1 }} />

        <CustomButton
          className={styles.hoverClass}
          data-skip={privacySetting ? "false" : "true"}
          onClick={privacySetting ? handleContinue : handleSkip}
          label={
            <>
              {privacySetting ? "Next" : "Skip"}
              <i
                className={`pi pi-angle-${privacySetting ? "right" : "right"}`}
                style={{ marginLeft: "8px" }}
              ></i>
            </>
          }
          style={
            privacySetting
              ? {
                backgroundColor: "#FFA500",
                color: "#fff",
                width: "156px",
                height: "39px",
                fontWeight: "800",
                fontSize: "14px",
                borderRadius: "8px",
                border: "2px solid transparent",
                boxShadow: "0px 4px 12px #00000",
                transition:
                  "background-color 0.3s ease, box-shadow 0.3s ease",
                margin : isMobile && "10px"
              }
              : {
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontWeight: "400",
                fontSize: "14px",
                borderRadius: "10px",
                border: "1px solid #F0F4F7",
               margin : isMobile && "10px"
              }
          }
        />
      </footer>
    </div>
  );
};

export default PrivacySettings;
