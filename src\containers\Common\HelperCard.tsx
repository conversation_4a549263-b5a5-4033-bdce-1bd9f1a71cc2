import React, { CSSProperties } from 'react';
import { FaRegHeart } from 'react-icons/fa6';
import { FiPlayCircle } from 'react-icons/fi';
import { RiCloseLargeLine } from 'react-icons/ri';
import '../Common/styles/helper-card.css';
import useIsMobile from '../../hooks/useIsMobile';
interface Items {
    icon: React.JSX.Element;
    description: React.JSX.Element;
}

interface HelperCardProps {
    helperName: string;
    imgSrc: string;
    mainDivStyles?: CSSProperties;
    titleFontSize?: number;
    items: Items[];
    isSuperHelper?: boolean;
    hasProfileVideo?: boolean;
    onXClicked?: () => void;
    onFavClicked?: () => void;
    onImgClicked?: () => void;
    friendStatus: number;
}

function HelperCard({
    helperName,
    imgSrc,
    mainDivStyles,
    items,
    titleFontSize,
    onImgClicked,
    onFavClicked,
    onXClicked,
    isSuper<PERSON><PERSON><PERSON>,
    hasProfileVideo,
    friendStatus,
}: HelperCardProps) {
    const { isMobile } = useIsMobile();

    return !isMobile ? (
        <div
            className='flex card cursor-pointer'
            style={{
                ...mainDivStyles,
            }}
            onClick={() => onImgClicked()}

        >
            <div className='img' style={{ minWidth: '10%' }}>
                <img
                    src={imgSrc}
                    alt='Helper Image'
                    className='h-full cursor-pointer'
                    style={{
                        width: '150px',
                        height: '150px',
                    }}
                />
            </div>
            {isSuperHelper && (
                <div
                    className='absolute w-full flex justify-content-start pl-5'
                    style={{ bottom: '5px' }}
                >
                    <div
                        className='text-white font-bold'
                        style={{
                            fontSize: '0.6rem',
                            backgroundColor: '#444444',
                            padding: '5px 10px',
                            paddingInline: '10px',
                            borderRadius: '30px',
                            userSelect: 'none',
                        }}
                    >
                        Super Helper
                    </div>
                </div>
            )}
            {hasProfileVideo && (
                <div className='absolute flex' style={{ top: '5px', left: '8px' }}>
                    <div
                        className='text-white font-bold'
                        style={{
                            fontSize: '1.6rem',
                            borderRadius: '30px',
                            userSelect: 'none',
                        }}
                    >
                        <FiPlayCircle />
                    </div>
                </div>
            )}

            <div className='flex-grow-1 flex flex-column w-4 p-1 px-2' style={{ color: '#585858' }}>
                <div className='flex justify-content-between align-items-center'>
                    <h4
                        className='m-0 font-bold'
                        style={{
                            fontSize: `${titleFontSize || 12}px`,
                        }}
                    >
                        {helperName}
                    </h4>
                    <div className='flex text-md gap-2' >
                        {friendStatus === 4 ? (
                            <>
                                <FaRegHeart
                                    className='cursor-pointer'
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        onFavClicked();
                                    }}
                                />
                                <RiCloseLargeLine
                                    className='cursor-pointer'
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        onXClicked();
                                    }}
                                />
                            </>
                        ) : (
                            <FaRegHeart
                                className='cursor-pointer'
                                style={{ color: 'red' }} // Show red heart when friendStatus is not 4
                                onClick={() => onFavClicked()}
                            />
                        )}
                    </div>
                </div>
                <div className='flex-grow-1 flex flex-column '>
                    {items.map((item, index) => (
                        <div key={index} className='flex align-items-center gap-2'>
                            {item.icon}
                            {item.description}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    ) : (
        <div
            className='flex card'
            onClick={() => onImgClicked()}
            style={{
                flexDirection: 'column',
                alignItems: 'center',
                minHeight: '295px',
                maxWidth:"250px"
            }}
        >
            <div>
                <div className='img' style={{ minWidth: '10%' }}>
                    <img
                        src={imgSrc}
                        alt='Helper Image'
                        className=' cursor-pointer'
                        style={{
                            width: '150px',
                            height: '150px',
                        }}
                        onClick={() => onImgClicked()}
                    />
                </div>
            </div>

            {hasProfileVideo && (
                <div className='absolute flex' style={{ top: '5px', left: '8px' }}>
                    <div
                        className='text-white font-bold'
                        style={{
                            fontSize: '1.6rem',
                            borderRadius: '30px',
                            userSelect: 'none',
                        }}
                    >
                        <FiPlayCircle />
                    </div>
                </div>
            )}

            <div className='flex-grow-1 flex flex-column pl-2 pr-2 w-full' style={{ color: '#585858' }}>
                <div
                    style={{ justifyContent: 'space-between' }}
                    className='flex  align-items-center pl-1 '
                >
                    <h4 className='m-0 font-bold' style={{ fontSize: '14px' }}>
                        {helperName}
                    </h4>
                    <div style={{ display: 'flex', gap: '8px' }}>
                        {friendStatus === 4 ? (
                            <>
                                <FaRegHeart
                                    className='cursor-pointer'
                                    onClick={() => onFavClicked()}
                                />
                                <RiCloseLargeLine
                                    className='cursor-pointer'
                                    onClick={() => onXClicked()}
                                />
                            </>
                        ) : (
                            <FaRegHeart
                                className='cursor-pointer'
                                style={{ color: 'red' }} // Show red heart when friendStatus is not 4
                                onClick={() => onFavClicked()}
                            />
                        )}
                    </div>
                </div>
                <div className='flex-grow-1 flex flex-column '>
                    {items.map((item, index) => (
                        <div key={index} className='flex align-items-center gap-2 mt-1'>
                            {item.icon}
                            {item.description}
                        </div>
                    ))}
                    {isSuperHelper && (
                        <div
                            className=' w-full flex justify-content-center'
                            style={{ marginTop: "auto", marginBottom: "4px" }}
                        >
                            <div
                                className='text-white font-bold'
                                style={{
                                    fontSize: '8px',
                                    backgroundColor: '#444444',
                                    padding: '3px 10px',
                                    paddingInline: '10px',
                                    borderRadius: '30px',
                                    userSelect: 'none',
                                }}
                            >
                                Super Helper
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default HelperCard;
