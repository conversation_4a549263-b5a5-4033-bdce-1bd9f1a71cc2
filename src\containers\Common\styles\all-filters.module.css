.main {
  background-color: #ffffff;
  border-radius: 20px;
  height: 80vh;
  max-width: 496px;
  width: 90vw;
  color: #585858;
  font-family: "Poppins";
}

.title {
  font-size: 24px;
  font-weight: 600;
  padding: 0;
  margin: 0;
}

.closeButton {
  border: none;
  margin: 0;
  padding: 0;
  background: transparent;
  font-size: 24px;
  color: #585858;
}

.padding {
  padding: 15px 20px;
}

.buttonStyle {
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  text-transform: capitalize;
  cursor: pointer;
}

.buttonStyle[data-apply] {
  background-color: #ffa500;
  padding: 8px 10px;
  border-radius: 15px;
  min-width: 180px;
  color: #ffffff;
  font-weight: 700;
}
.buttonStyle[data-apply]:hover {
  background-color: rgba(255, 165, 0, 0.9);
}
.buttonStyle[data-apply]:active {
  background-color: rgba(255, 165, 0, 0.8);
}

.buttonStyle[data-clear]:hover {
  text-decoration: underline;
}
.buttonStyle[data-clear]:active {
  font-weight: 600;
}

.searchInput {
  width: 95%;
  max-width: 414px;
  min-height: 46px;
  border: 1px solid #bbbbbb;
  margin-inline: auto;
  margin-top: 10px;
  padding: 5px 20px;
  border-radius: 5px;
  overflow: hidden;
}
.searchInput input {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  border: none;
  background-color: transparent;
  font-weight: 275;
  font-size: 14px;
}

.customCheckBox[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  background-color: transparent;
  border: 2px solid #585858;
  width: 18px; /* Set width */
  min-width: 18px; /* Set width */
  height: 18px; /* Set height to match width */
  min-height: 18px; /* Set height to match width */
  cursor: pointer;
  position: relative;
  outline: none;
  border-radius: 3.07px; /* Small radius for sharper corners */
  transition: background-color 0.2s, border-color 0.2s;
}

.customCheckBox[type="checkbox"]:checked {
  background-color: #179d52;
  border-color: #179d52;
}

.customCheckBox[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  top: 40%;
  left: 50%;
  width: 4px; /* Larger checkmark size */
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: translate(-50%, -50%) rotate(45deg);
}

.customDropdown * {
  font-family: "Poppins";
  color: #585858;
}

.customDropdown {
  position: relative;
  display: inline-block;
  width: 100%; /* Adjust as needed */
}

.selectedOption {
  border: 1px solid #bbbbbb;
  padding: 10px;
  padding-inline: 20px;
  background-color: white;
  font-weight: 275;
  cursor: pointer;
  user-select: none;
  border-radius: 5px;
}

.dropdownMenu {
  position: absolute;
  z-index: 1000;
  border: 1px solid #bbbbbb;
  background-color: white;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.dropdownOption {
  padding: 10px;
  cursor: pointer;
}

.dropdownOption:hover {
  background-color: #f0f0f0;
}

.dropdownMenu.below {
  top: 100%;
  left: 0;
}

.dropdownMenu.above {
  bottom: 100%;
  left: 0;
}

.scrollView {
  overflow-y: auto;
}
.mainMobile {
  background-color: #ffffff;
  border-radius: 20px;
  height: 100%;
  width: 90vw;
  color: #585858;
  font-family: "Poppins";
}

.CandidateMatchingContainerMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
}
.checkboxGroupMobile {
  display: flex;
  flex-direction: column; /* Stack rows vertically */
  gap: 10px;
  margin-block: 8px;
}

.checkboxPairMobile,
.checkboxPairSecMobile {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.dialogContentMobile {
  width: 100%; 
  margin: 0 auto;
  box-sizing: border-box;
  user-select: none;
  padding-inline: 20px;
}
.dialogHeadMobile {
  font-size: 22px;
  font-weight: 700;
  line-height: 48px;
  margin-bottom: 10px;
  color: #585858;
  margin-top: 0px;
}
.checkboxGroupMobile {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkboxPairFirstMobile,
.checkboxPairSecondMobile {
  display: flex;
  gap: 10px;
  justify-content: flex-start; /* Center buttons */
}
.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}
.dialogFooter{
 float: inline-end;
 display: flex;

 gap: 10px;
}
.goBackButton{
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  background-color: transparent;
  padding-inline: 30px;
  padding-block: 5px;
  border: 1px solid #585858;
  border-radius: 20px;
}
.nextButton{
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  background-color: #ffa500;
  padding-inline: 30px;
  padding-block: 5px;
border: none;
  border-radius: 20px;
}
.dialogInstruct{
  font-size: 14px;
  color: #585858;
  font-weight: 500;
}
.checkboxPairThird{
  display: flex;
  flex-direction: column;
  gap: 10px;
}