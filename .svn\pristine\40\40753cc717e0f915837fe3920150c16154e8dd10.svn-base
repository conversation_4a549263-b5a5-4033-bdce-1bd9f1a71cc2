import axios from "axios";
import environment from "./environment";
import utils from "../components/utils/util";
import Api from "./juggleStreetApi";
import CookiesConstant from "./cookiesConst";
import { appInsights } from "../services/AppInsights";

export class HttpLink {
  static MAXRETRIES = 5;
  static RETRYINTERVALS = [0, 2000, 5000, 10000, 15000];

  static processResponse(output): any {
    let response = output.response;
    if (!response) {
      response = {
        status: 503,
        data: {
          message: "Unable to connect to the server. Please try again later.",
        },
      };
    }

    const config = output.config;
    let result: any = {
      method: config.method,
      url: config.url,
      status: response.status,
      success: response.status >= 200 && response.status < 300,
      responseJSON: response.data, // Backward compatibility.
    };

    if (result.success) {
      result.data = response.data;
    } else {
      if (response.data) {
        if (response.data.message) result.message = response.data.message;
        else if (response.data.error_description) result.message = response.data.error_description;
        else if (response.data.exceptionMessage) result.exceptionMessage = response.data.exceptionMessage;
      }

      if (!result.message) result.message = "An error occurred. Please try again later.";
    }

    return result;
  }
  static refreshToken() {
    var token = utils.getCookie("jugglestreet-refresh-token");
    const requestData = {
      refreshToken: token,
    };
    const request = axios({
      method: "POST",
      url: `${environment.getServiceURL(window.location.hostname)}${Api.identity.refreshToken}`,
      data: requestData,
    }).then((response) => {
      if (response.data) {
        var expires = new Date();
        expires.setFullYear(new Date().getFullYear() + 1);
        utils.setCookie("jugglestreet-access-token", response.data.accessToken, expires);
        utils.setCookie("jugglestreet-refresh-token", response.data.refreshToken, expires);
      }
    });

    return request;
  }

  static configureAxios() {
    const axiosValidateStatus = function (status) {
      return status >= 200 && status < 300;
    };

    axios.interceptors.request.use(
      (config: any) => {
        config.validateStatus = axiosValidateStatus;
        if (!config.retries) config.retries = 0;

        return config;
      },
      function (error) {
        return Promise.reject(error);
      }
    );

    axios.interceptors.response.use(
      (response) => {
        return HttpLink.processResponse({
          response: response,
          config: response.config,
        });
      },
      async (error) => {
        appInsights?.trackException({
          error: error,
          properties: {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            message: error.message,
          },
          severityLevel: 4, // Critical error
        });
        const result = HttpLink.processResponse({
          response: error.response,
          config: error.config,
        });
        if (result.url === "/app-info.json") return Promise.reject(result);

        if (!result.success && result.status === 503 && error.config.retries < HttpLink.MAXRETRIES) {
          // Only use safe to retry APIs.

          if (result.method === "get" || result.url.endsWith("/searchv3")) {
            error.config.retries++;
            return axios.request(error.config);
          }
        }

        if (result.status === 401) {
          // https://github.com/axios/axios/issues/934

          if (!utils.getCookie("jugglestreet-refresh-token")) {
            // No refresh token is present.
            //   EventManager.getInstance().trigger(c.events.LOGOUT);
          } else if (error.config.retries < HttpLink.MAXRETRIES) {
            try {
              await HttpLink.refreshToken();
              const token = utils.getCookie(CookiesConstant.accessToken);
              error.config.headers.Authorization = `Bearer ${token}`;
              error.config.retries++;
              return axios.request(error.config);
            } catch (e) {
              // EventManager.getInstance().trigger(c.events.LOGOUT);
            }
          } else {
            try {
              let message = "Maximum retries reached.";
              throw new Error(message);
            } catch (e) {
              (window as any).rg4js("send", e);
              // EventManager.getInstance().trigger(c.events.LOGOUT);
            }
          }
        } else if (result.status > 401) {
          try {
            let message = `Status: ${result.status}, ${result.exceptionMessage ? result.exceptionMessage : result.message}${
              error.config.retries ? ". Retry Count: " + error.config.retries : ""
            }`;
            throw new Error(message);
          } catch (e) {
            (window as any).rg4js("send", e);
          }

          if (result.status === 403) {
            //   EventManager.getInstance().trigger(c.events.LOGOUT);
          } else if (result.status === 404 || result.status === 405) {
            // The caller should handle this error.
          } else if (result.status !== 503 || error.config.retries == HttpLink.MAXRETRIES) {
            // Allow Raygun a few seconds to process the error.
            setTimeout(() => {
              window.location.href = "/error.html";
            }, 1000);
          }
        }

        return Promise.reject(result);
      }
    );
  }
  baseUrl() {
    return environment.getServiceURL(window.location.hostname);
  }

  request(config, onSuccess, onError) {
    if (!config.method) config.method = "GET";

    config.headers = {
      "Content-Type": "application/json; charset=utf-8",
    };

    const token = utils.getCookie(CookiesConstant.accessToken);
    if (token) {
      config.headers.Authorization = "Bearer " + token;
    }
    axios
      .request(config)
      .then((response) => onSuccess(response.data))
      .catch((err) => {
        onError(err);
      });
  }
}
HttpLink.configureAxios();
