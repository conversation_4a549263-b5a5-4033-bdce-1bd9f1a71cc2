import { Dialog } from "primereact/dialog";
import { IoClose } from "react-icons/io5";
import { Jobs } from "../../types";
import React from "react";
import InviteMore from "./InviteMore";
import EditCriteria from "./EditCriteria";
import Service from "../../../../../services/services";
import useLoader from "../../../../../hooks/LoaderHook";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { FilterProvider } from "../../../../../hooks/FilterManagerHook";

const ManageRecruitment: React.FC<{ visible: boolean; job: Jobs; onClose: () => void }> = ({ visible, job, onClose }) => {
  const { enableLoader, disableLoader } = useLoader();
  const { isMobile } = useIsMobile();
  const onSubmit = (payload: Jobs & { newApplicants: Array<{ applicantId: number }> }) => {
    enableLoader();
    Service.jobClientUpdate(
      payload,
      job.id,
      () => {
        disableLoader();
        onClose();
      },
      (e) => {
        disableLoader();
      }
    );
  };
  return (
    <>
      <Dialog
        visible={visible}
        onHide={onClose}
        style={{ width: "100vw", height: "100vh", maxWidth: "none", maxHeight: "none" }}
        content={
          <div
            className="flex flex-column m-auto overflow-hidden"
            style={{
              width: "90%",
              maxWidth: "1000px",
              height: "90%",
              maxHeight: "927px",
              borderRadius: "25px",
              backgroundColor: "#FFFFFF",
            }}
          >
            <div
              className="flex align-items-center px-4 justify-content-between"
              style={{
                minHeight: "89px",
                backgroundColor: "#179D52",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              }}
            >
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: isMobile ? "24px" : "30px",
                  color: "#FFFFFF",
                }}
              >
                {job.managedBy === 20 ? "Edit Criteria" : "Invite More Candidates"}
              </p>
              <IoClose
                className="cursor-pointer"
                fontSize="30px"
                color="#FFFFFF"
                onClick={(e) => {
                  e.preventDefault();

                  onClose();
                }}
              />
            </div>
            <div
              className="flex flex-column justify-content-center align-items-center overflow-hidden"
              style={{
                height: "100%",
              }}
            >
              {job.managedBy === 1 ? (
                <FilterProvider>
                  <InviteMore job={job} onSubmit={onSubmit} onClose={onClose} />
                </FilterProvider>
              ) : (
                <EditCriteria job={job} />
              )}
            </div>
          </div>
        }
      />
    </>
  );
};

export default ManageRecruitment;
