import { PayloadTemplate } from "../../provider/JobManagerProvider";

export interface SectionProps extends PayloadTemplate {
  currentStep: number;
  prevClicked: (payload: PayloadTemplate) => PayloadTemplate;
}

export interface Schedule {
  dayOfWeek: number;
  hourlyPrice: number;
  isRequired: boolean;
  jobEndTime: string;
  jobStartTime: string;
  price: number;
  shifts: Array<{
    hourlyPrice: number;
    isRequired: boolean;
    jobEndTime: string;
    jobStartTime: string;
    price: number;
  }>;
}
