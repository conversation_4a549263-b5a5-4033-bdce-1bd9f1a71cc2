.container {
  display: flex;
  border: 1px solid #dfdfdf;
  width: 951px;
  border-radius: 10px;
  height: 132px;
}
.greenDiv {
  width: 10px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  background-color: #179d52;
}
.left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 87px;
  margin-left: 5px;
  flex-direction: column;
  position: relative;
}
.awardedBtn {
  width: 112px;
  height: 31px;
  border-radius: 20px;
  border: 2px solid #179d52;
  font-size: 14px;
  font-weight: 700;
  color: #179d52;
  background-color: #fff;
  position: absolute;
  bottom: 114px;
  left: 2px;
}

.image {
  width: 86.7px;
  height: 81px;
  object-fit: cover;
  border-radius: 50%;
}

.right {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 18px;
  padding-block: 20px;
}

.nameRating {
  display: flex;
  flex-direction: column;
  margin-left: 15px;
  width: 240px;
}
.name {
  font-size: 18px;
  font-weight: 700;
  color: #585858;
  line-height: 27px;
}
.ratingsStar {
  width: 99.34px;
  height: 18.66px;
}
.buttonContainer {
  display: flex;
  flex-direction: row;
}
.name {
  font-size: 18px;
  font-weight: bold;
  margin: 0px;
}

.rating {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
}
.chatBtn {
  width: 109px;
  height: 36px;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  background-color: #ffa500;
  cursor: pointer;
}
.contactBtn {
  width: 159px;
  height: 35px;
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  background-color: #fff;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
}
.bankBtn {
  width: 125px;
  height: 35px;
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  background-color: #fff;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
}
.transportBtn {
  width: 106px;
  height: 35px;
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  background-color: #fff;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
}

.infoDiv {
  display: flex;
  flex-direction: row;
  gap: 5px;
}
.additionalButtons {
  margin-top: 16px;
  display: flex;
  /* justify-content: space-between; */
  gap: 10px;
}

.cancelJobBtn {
  background-color: rgba(255,99,89,0.3) ;
  color: #FF6359;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  width:470px;
}

.duplicateJobBtn {
  width:470px;
  background-color: #FFA500;
  color: #FFFFFF;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight:700;
}
.imageMobile {
  width: 75.7px;
  height: 71px;
  object-fit: cover;
  border-radius: 50%;
}
.ratingMobile {
  font-size: 10px;
  font-weight: bold;
  display: flex;
  flex-direction: column;
}
.nameRatingMobile{
  display: flex;
  flex-direction: column;
  width: max-content;
}
.containerMoble {
  display: flex;
      border: 2px solid #179d52;
      width: 100%;
      border-radius: 10px;
      box-shadow: 0px 4px 4px 0px #00000040;
      /* justify-content: center; */
      align-items: center;
      padding-right: 20px;

}
.chatBtnMobile {
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  padding-block: 5px;
  color: #fff;
  padding-inline: 30px;
  border: none;
  background-color: #ffa500;
  cursor: pointer;
  margin-left: auto;
}
.rightMobile {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 25px;
  border: none;
  padding-block: 20px;
}
.contactBtnMobile {
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 10px;
  font-weight: 600;
  padding-block: 3px;
}
.transportBtnMobile {
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 10px;
  font-weight: 600;
  padding-block: 5px;
}
.bankBtnMobile {
  border-radius: 15px;
  border: 1px solid #dfdfdf;
  color: #585858;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 10px;
  font-weight: 600;
  padding-block: 5px;
}
.infoDivMobile {
  display: flex;
  flex-direction: row;
  gap: 5px;
  padding-left: 20px;
  padding-bottom: 15px;
}