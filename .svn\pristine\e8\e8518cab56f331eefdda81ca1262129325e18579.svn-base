import React, { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AddChildren from './AddChildren';
import FamilyIntro from './FamilyIntro';
import AddFamilyPhoto from './AddFamilyPhoto';
import { AppDispatch, RootState } from '../../../store';
import { setCurrentProfileActivationStep } from '../../../store/slices/applicationSlice';
import CustomDialog from '../../Common/CustomDialog';
import IdentityCheck from '../../Common/IdentityCheck';
import c from '../../../helper/juggleStreetConstants';

interface ProfileCompletionProps {
    isVisible: boolean;
    closeDialog: () => void;
}

function ProfileCompletionWizard({ isVisible, closeDialog }: ProfileCompletionProps) {
    const { data: sessionData } = useSelector((state: RootState) => state.sessionInfo);
    const applicationState = useSelector(
        (state: RootState) => state.applicationState.profileActivationCurrentStep
    );
    const dispatch = useDispatch<AppDispatch>();

    const completeClientChildren = () => {
        return (c.profileItems.MY_CHILDREN & sessionData['profileItems']) !== 0;
    };
    const completeClientPhoto = () => {
        return (c.profileItems.CLIENT_PHOTO & sessionData['profileItems']) !== 0;
    };
    const completeClientAboutMe = () => {
        return (c.profileItems.CLIENT_ABOUT_ME & sessionData['profileItems']) !== 0;
    };

    const profileCompleteness = sessionData['profileCompleteness'];
    const clientType = sessionData['clientType'];
    const isIndividual = clientType == c.clientType.INDIVIDUAL;
    const steps = [];

    const identityCheckComplete = useCallback(() => {
        return (c.profileItems.TRUST_VERIFICATION & sessionData['profileItems']) !== 0;
    }, [sessionData]);

    if (!identityCheckComplete()) {
        steps.push(IdentityCheck);
    }

    if (!isIndividual && !completeClientChildren()) {
        steps.push(AddChildren);
    }

    if (!isIndividual && !completeClientAboutMe()) {
        steps.push(FamilyIntro);
    }

    if (!completeClientPhoto()) {
        steps.push(AddFamilyPhoto);
    }

    useEffect(() => {
        dispatch(setCurrentProfileActivationStep(1));
    }, [isVisible, dispatch]);

    const renderStepComponent = () => {
        const StepComponent = steps[applicationState - 1];
        return StepComponent ? <StepComponent /> : <CloseDialog closeDialog={closeDialog} />;
    };

    return (
        <React.Fragment>
            <CustomDialog
                visible={isVisible}
                onHide={closeDialog}
                closeClicked={() => { }}
                profileCompletion={profileCompleteness}
            >
                {renderStepComponent()}
            </CustomDialog>
        </React.Fragment>
    );
}

const CloseDialog = ({ closeDialog }: { closeDialog: () => void }) => {
    useEffect(() => {
        if (closeDialog) {
            closeDialog();
        }
    }, [closeDialog]);
    return <></>;
};
export default ProfileCompletionWizard;
