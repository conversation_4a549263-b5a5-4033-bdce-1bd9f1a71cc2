import React, { ReactNode } from 'react';
import Header from './Header';
import styles from '../commonStyle/card.module.css';
import logoImage from '../assets/images/juggle-st-transparent-card.png'; // Adjust the path to your image

interface CardProps {
    children: ReactNode;
    className?: string;
    altText?: string;
}

const Cards: React.FC<CardProps> = ({ children, className, altText = 'jugglestreet' }) => {
    return (
        <div>
            <Header />
            <div
                className={`flex justify-content-center align-items-center ${styles.cardContainer}`}
            >
                <div className="p-col-12 p-md-12 p-xl-12 custom-card-width">
                    <div className={styles.card}>
                        <div className={styles.logoDiv}>
                            <img
                                loading="lazy"
                                src={logoImage}
                                alt={altText}
                                className={styles.logoImage}
                            />
                        </div>
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Cards;
