import React, { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import send from "../../../assets/images/Icons/right-arrow-white.png";
import {
  Message,
  SendChatState,
  ChatSession,
  Participant,
} from "../InAppChat/InAppChatTypes";
import { RootState } from "../../../store";
import Service from "../../../services/services";
import environment from "../../../helper/environment";
import receiverSampleImage from "../../../assets/images/juggleLogoSmall.png";
import styles from "../styles/send-chat.module.css";
import c from "../../../helper/juggleStreetConstants";
import useLoader from "../../../hooks/LoaderHook";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import useIsMobile from "../../../hooks/useIsMobile";
const getMessageDeliveryStatusText = (deliveryStatus: number): string => {
  switch (deliveryStatus) {
    case c.messageDeliveryStatus.failed:
      return "Failed";
    case c.messageDeliveryStatus.sending:
      return "Sending...";
    case c.messageDeliveryStatus.delivered:
      return "Delivered";
    case c.messageDeliveryStatus.seen:
      return "Seen";
    default:
      return "Unseen";
  }
};
function formatTime(sentAt: string | Date): string {
  const date = new Date(sentAt); // Convert the sentAt value to a Date object
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? "pm" : "am";
  const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
  const formattedMinutes = minutes.toString().padStart(2, "0"); // Add leading zero if needed
  return `${formattedHours}:${formattedMinutes}${ampm}`;
}
// Sub-Components
interface ChatHeaderProps {
  participants: Participant[];
}
interface ReceverNameTimeProps {
  participants: Participant[];
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ participants }) => {
  const [clientType, setClientType] = useState<number | null>(null);
  const {isMobile}=useIsMobile();
  useEffect(() => {
    const clientTypeFromCookie = Number(utils.getCookie(CookiesConstant.clientType));
    setClientType(clientTypeFromCookie);
  }, []);

  if (participants.length <= 1) return null;

  const otherParticipant =
    clientType === 0
      ? participants.find((e) => e.jugglerTypes === 1)
      : participants.find((e) => e.jugglerTypes === 2);

  return (
    <p
      style={{
        fontSize:isMobile ?"18px": "20px",
        fontWeight: "500",
        color: "#585858",
        marginLeft:"20px",
       
      }}
    >
      Chatting to{" "}
      <span
        style={{
          fontSize:isMobile ?"18px": "20px",
          fontWeight: "700",
          color: "#585858",
          textDecoration: "underline",
         
        }}
      >
        {otherParticipant?.firstName}
      </span>
    </p>
  );
};
const RenderNameHelper: React.FC<ReceverNameTimeProps> = ({ participants }) => {
  if (participants.length <= 1) return null;

  const otherParticipant = participants.find((e) => e.jugglerTypes === 2);

  return (
    <p
      style={{
        color: "#585858",
        fontSize: "12px",
        fontWeight: "600",
        margin: "0px",
      }}
    >
      {otherParticipant.firstName}

      {" • "}
    </p>
  );
};
const RenderNameParent: React.FC<ReceverNameTimeProps> = ({ participants }) => {
  if (participants.length <= 1) return null;

  const otherParticipant = participants.find((e) => e.jugglerTypes !== 2);

  return (
    <p
      style={{
        color: "#585858",
        fontSize: "12px",
        fontWeight: "600",
        margin: "0px",
      }}
    >
      {otherParticipant.firstName}

      {" • "}
    </p>
  );
};

interface MessageListProps {
  messages: Message[];
  senderId: string;
  participants: Participant[];
}

const MessageList: React.FC<
  MessageListProps & {
    onResendMessage: (message: Message) => void;
  }
> = ({ messages, senderId, participants, onResendMessage }) => {

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: "auto", 
        block: "end",
        inline: "nearest"
      });
    }
  };
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Scroll on initial load
  useEffect(() => {
    scrollToBottom();
  }, []);

  // Scroll after a short delay to ensure all content is rendered
  useEffect(() => {
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages]);


  const findParticipantImage = (messageSenderId: number): string => {
    const participant = participants.find(
      (p) => p.participantId === messageSenderId || p.id === messageSenderId.toString()
    );

    return participant?.imageUrl
      ? `${environment.getStorageURL(window.location.hostname)}/images/${participant.imageUrl}`
      : receiverSampleImage;
  };

  const createInsuranceNoteMessage = (noteType: "initial" | "length-based"): Message => {
    const now = new Date().toISOString();
    const body = noteType === "initial"
      ? "Your family is covered by AON liability insurance when you post & award jobs through the app, not the chat."
      : "Do not confirm jobs via chat. Use the AWARD button in your job post and your family is covered by AON liability insurance.";

    return {
      body: body,
      deliveryStatus: null,
      id: noteType === "initial" ? "insurance-note-1" : "insurance-note-2",
      senderId: "js-2",
      sentAt: now,
      sentOn: now,
      subject: null,
      retryAttempts: 0,
    };
  };
  const createInsuranceNoteMessageForHelper = (noteType: "initial" | "length-based"): Message => {
    const now = new Date().toISOString();
    const body = noteType === "initial"
      ? "Please don’t share any personal details over chat. When you are awarded a job through the app, your phone number and bank details (if required) will be provided to the person hiring you."
      : "If you are offered a job on chat, please ask them to post it & award it through the app. Only jobs awarded through the app will be rated, reviewed, and added to your job counter.";

    return {
      body: body,
      deliveryStatus: null,
      id: noteType === "initial" ? "insurance-note-1" : "insurance-note-2",
      senderId: "js-2",
      sentAt: now,
      sentOn: now,
      subject: null,
      retryAttempts: 0,
    };
  };


  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const getOrdinalSuffix = (day: number): string => {
    if (day > 3 && day < 21) return 'th';
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  const formatDateKey = (date: Date): string => {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };

  const isToday = (date: Date): boolean => {
    const today = new Date();
    return formatDateKey(date) === formatDateKey(today);
  };

  const isYesterday = (date: Date): boolean => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return formatDateKey(date) === formatDateKey(yesterday);
  };

  const formatDateHeader = (dateStr: string): string => {
    const date = new Date(dateStr);

    if (isToday(date)) {
      return 'Today';
    }
    if (isYesterday(date)) {
      return 'Yesterday';
    }

    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const dayName = days[date.getDay()];
    const day = date.getDate();
    const monthName = months[date.getMonth()];
    const ordinalSuffix = getOrdinalSuffix(day);

    return `${dayName}, ${day}${ordinalSuffix} of ${monthName}`;
  };


  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {
      'initial-note': [] // Special group for initial note
    };
  
    messages.forEach(message => {
      if (message.id === 'insurance-note-1') {
        groups['initial-note'].push(message);
      } else {
        const messageDate = new Date(message.sentAt);
        const dateKey = formatDateKey(messageDate);
  
        if (!groups[dateKey]) {
          groups[dateKey] = [];
        }
        groups[dateKey].push(message);
      }
    });
  
    // Sort all groups except 'initial-note'
    const sortedGroups: { [key: string]: Message[] } = {};
    
    // Add initial note first if it exists
    if (groups['initial-note'].length > 0) {
      sortedGroups['initial-note'] = groups['initial-note'];
    }
    
    // Add rest of the messages
    Object.keys(groups)
      .filter(key => key !== 'initial-note')
      .sort((a, b) => a.localeCompare(b))
      .forEach(key => {
        sortedGroups[key] = groups[key].sort((a, b) =>
          new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime()
        );
      });
  
    return sortedGroups;
  };
  

 
  const processedMessages = [...messages];
  const displayInitialNote = sessionInfo?.data?.["id"] &&
    sessionInfo?.data["client"]["clientType"] == c.clientType.INDIVIDUAL;
    const isHelper = utils.getCookie(CookiesConstant.clientType) === "0";

 if (!processedMessages.some(msg => msg.id === "insurance-note-1")) {
    const insuranceNoteMessage = isHelper 
      ? createInsuranceNoteMessageForHelper("initial")
      : createInsuranceNoteMessage("initial");
      processedMessages.push(insuranceNoteMessage);
  }

  if (processedMessages.length > 5 && !processedMessages.some(msg => msg.id === "insurance-note-2")) {
    const insuranceNoteMessage = isHelper
      ? createInsuranceNoteMessageForHelper("length-based")
      : createInsuranceNoteMessage("length-based");
    processedMessages.push(insuranceNoteMessage);
  }
  const messageGroups = groupMessagesByDate(processedMessages);

  const [clientType, setClientType] = useState<number | null>(null);

  useEffect(() => {
   
    const clientTypeFromCookie = Number(utils.getCookie(CookiesConstant.clientType));
    setClientType(clientTypeFromCookie);
  }, []);

  
  if (clientType === null) return null;

  const shouldReverse = clientType === 0;

  return (
    <div className={styles.messageList}>
      {Object.entries(messageGroups).map(([dateKey, dateMessages]) => (
        <div key={dateKey} className={styles.dateGroup}>
        {/* Only show date header for non-initial notes */}
        {dateKey !== 'initial-note' && (
          <div className={styles.dateHeader}>
            <div className={styles.dateHeaderLine} />
            <span className={styles.dateHeaderText}>
              {formatDateHeader(dateKey)}
            </span>
            <div className={styles.dateHeaderLine} />
          </div>
        )}

          {dateMessages.map(message => {
            const isInsuranceNote = message.id === "insurance-note-1" ||
              message.id === "insurance-note-2";
            const isSentMessage = message.senderId === Number(senderId);

            return (
              <div key={message.id}>
                <div className={`${styles.messageContainer} ${isSentMessage ? styles.sentMessageTimeContainer : styles.receivedMessageTimeContainer
                  }`}>
                  <div className={`${styles.messageTime} ${isSentMessage ? styles.sentMessageTime : styles.receivedMessageTime
                    }`}>
                   
                     {isInsuranceNote ? (
                      <p style={{
                        color: "#585858",
                        fontSize: "12px",
                        fontWeight: "600",
                        margin: "0px",
                      }}>
                        Juggle Street{" • "}
                      </p>
                    ) : shouldReverse ? (
                      isSentMessage ? (
                        <RenderNameHelper participants={participants} />
                      ) : (
                        <RenderNameParent participants={participants} />
                      )
                    ) : isSentMessage ? (
                      <RenderNameParent participants={participants} />
                    ) : (
                      <RenderNameHelper participants={participants} />
                    )}
                    {formatTime(message.sentAt)}
                  </div>
                </div>

                <div className={`${styles.messageContainer} ${isSentMessage ? styles.sentMessageContainer : styles.receivedMessageContainer
                  } ${isInsuranceNote ? styles.insuranceNoteContainer : ""}`}>
                  <div className={`${styles.message} ${isSentMessage ? styles.sentMessage : styles.receivedMessage
                    } ${isInsuranceNote ? styles.insuranceNote : ""}`}>
                   <p style={{margin:"0px",fontSize:"14px",wordWrap:"break-word",overflowWrap:"break-word",wordBreak:"break-word",whiteSpace:"normal"}}>{message.body}</p>
                  </div>

                  <img
                    src={findParticipantImage(message.senderId)}
                    alt="Profile"
                    className={styles.profileImage}
                  />
                </div>

                {isSentMessage && !isInsuranceNote && (
                  <div className={styles.messageStatusContainer}>
                    <span className={`${styles.messageStatus} ${message.deliveryStatus === c.messageDeliveryStatus.failed
                        ? styles.failedStatus
                        : styles.pendingStatus
                      }`}>
                      {getMessageDeliveryStatusText(message.deliveryStatus)}
                    </span>
                    {message.deliveryStatus === c.messageDeliveryStatus.failed && (
                      <button
                        className={styles.resendButton}
                        onClick={() => onResendMessage(message)}
                      >
                        Retry
                      </button>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ))}
      <div ref={messagesEndRef} style={{ height: "1px", width: "100%" }} />
    </div>
  );
};
interface MessageInputProps {
  newMessage: string;
  onMessageChange: (message: string) => void;
  onSendMessage: () => void;
  isEnableMsg:boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  newMessage,
  onMessageChange,
  onSendMessage,
  isEnableMsg,
}) => {
  const {isMobile}=useIsMobile();
  return !isMobile ? (
    <div style={{ marginTop: "auto" }}>
    <div className={styles.customMessageInputContainer}>
      {isEnableMsg  ? (
        <div  className={styles.customChatInput}>
     
        <textarea
          className={styles.customChatInputSecond}
          placeholder="Write a message "
          value={newMessage}
          onChange={(e) => onMessageChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === "Done" || e.key === "Go" || e.key === "Next" || e.keyCode === 13) {
              e.preventDefault(); // Prevents a new line in the textarea
              onSendMessage();
            }
          }}
        ></textarea>

        <button className={styles.customChatButton} onClick={onSendMessage}>
          <img src={send} alt="send" width={10} height={8} />
        </button>
        </div>
      ):(
        <div style={{color:"#FF6359",fontWeight:"500"}}>     
        You need to subscribe and post a job to chat with helpers
        </div>
      )}
    </div>
  </div>
  ):(
    <div style={{ marginTop: "auto" }}>
    <div className={styles.customMessageInputContainerMobile}>
      {isEnableMsg  ? (
        <div  className={styles.customChatInputMobile}>
     
        <textarea
          className={styles.customChatInputSecondMobile}
          placeholder="Write a message "
          value={newMessage}
          onChange={(e) => onMessageChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault(); // Prevents a new line in the textarea
              onSendMessage();
            }
          }}
        ></textarea>

        <button className={styles.customChatButton} onClick={onSendMessage}>
          <img src={send} alt="send" width={10} height={8} />
        </button>
        </div>
      ):(
        <div style={{color:"#FF6359",fontWeight:"500"}}>     
        You need to subscribe and post a job to chat with helpers
        </div>
      )}
    </div>
  </div>
  )
};


interface SendChatProps {
  chatId?: string;
}

const SendChat: React.FC<SendChatProps> = ({ chatId }) => {
  const [state, setState] = useState<SendChatState>({
    messages: [],
    newMessage: "",
    isLoading: false,
    isEnable:false,
    participants: [],
  });
  const messageListRef = useRef<HTMLDivElement>(null);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const senderId = sessionInfo?.data?.["id"] ?? null;
  const { disableLoader, enableLoader } = useLoader();
  const {isMobile}=useIsMobile();
  useEffect(() => {
    if (messageListRef.current && !state.isLoading) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }
  }, [state.isLoading, state.messages]);

  const determineInitialDeliveryStatus = (networkStatus?: boolean): number => {
    if (networkStatus === false) {
      return c.messageDeliveryStatus.failed;
    }
    return c.messageDeliveryStatus.sending;
  };

  useEffect(() => {
    if (chatId) {
      const fetchChatMessages = async () => {
        try {
          setState((prevState) => ({ ...prevState, isLoading: true }));
          enableLoader();
          Service.getChatSpecific(
            (data: ChatSession[]) => {
              const chatSession = data[0];
              const messages = chatSession?.messages || [];

              const processedMessages = messages.map((msg) => ({
                ...msg,
               
              }));

              setState((prevState) => ({
                ...prevState,
                messages: processedMessages,
                participants: chatSession?.participants || [],
                isLoading: false,
                isEnable:chatSession.isEnabled
              }));
              disableLoader();
            },

            (error) => {
              console.error("Error fetching chat messages:", error);
              setState((prevState) => ({
                ...prevState,
              }));
              disableLoader();
            },
            chatId
          );
        } catch (error) {
          console.error("Error in fetching chat messages:", error);
          setState((prevState) => ({ ...prevState, isLoading: false }));
          disableLoader();
        }
      };

      fetchChatMessages();
    }
  }, [chatId]);


  const handleResendMessage = (message: Message) => {
    if (message.deliveryStatus !== c.messageDeliveryStatus.failed || !chatId)
      return;

   
    const updatedMessages = state.messages.map((msg) =>
      msg.id === message.id
        ? {
          ...msg,
          deliveryStatus: c.messageDeliveryStatus.sending,
          retryAttempts: (msg.retryAttempts || 0) + 1,
        }
        : msg
    );

  
    setState((prevState) => ({
      ...prevState,
      messages: updatedMessages,
    }));

    const now = new Date().toISOString();
    const resendPayload = {
      ownerId: Number(senderId),
      messageType: 4,
      sentAt: now,
      messageBody: {
        id: `resend-${now}`,
        senderId: Number(senderId),
        body: message.body,
        sentAt: now,
        sentOn: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        deliveryStatus: c.messageDeliveryStatus.sending,
        conversationId: chatId,
        retryAttempts: (message.retryAttempts || 0) + 1,
      },
    };

    Service.setChatSpecific(
      (response) => {

        setState((prevState) => ({
          ...prevState,
          messages: prevState.messages.map((msg) =>
            msg.id === message.id
              ? {
                ...msg,
                deliveryStatus: c.messageDeliveryStatus.delivered,
                id: response.messageBody.id,
              }
              : msg
          ),
        }));
      },
      (error) => {
    
        setState((prevState) => ({
          ...prevState,
          messages: prevState.messages.map((msg) =>
            msg.id === message.id
              ? {
                ...msg,
                deliveryStatus: c.messageDeliveryStatus.failed,
              }
              : msg
          ),
        }));
      },
      resendPayload,
      chatId
    );
    
  };
  const handleSendMessage = () => {
    const { newMessage } = state;
    if (newMessage.trim() && chatId) {
      const now = new Date().toISOString();

  
      const initialDeliveryStatus = determineInitialDeliveryStatus();

      const messagePayload = {
        ownerId: Number(senderId),
        messageType: 4,
        sentAt: now,
        messageBody: {
          id: `new-message-${now}`,
          senderId: Number(senderId),
          body: newMessage,
          sentAt: now,
          sentOn: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          deliveryStatus: initialDeliveryStatus,
          conversationId: chatId,
          retryAttempts: 0,
        },
      };

      const optimisticMessage: Message = {
        senderId: Number(senderId),
        subject: null,
        body: newMessage,
        deliveryStatus: initialDeliveryStatus,
        sentAt: now,
        sentOn: now,
        id: `temp-${Date.now()}`,
        retryAttempts: 0,
      };

      // Optimistically add message to the list
      setState((prevState) => ({
        ...prevState,
        newMessage: "",
        messages: [...prevState.messages, optimisticMessage],
      }));

      // Send message with comprehensive error handling
      Service.setChatSpecific(
        (response) => {
          // Successful send
          setState((prevState) => ({
            ...prevState,
            messages: prevState.messages.map((msg) =>
              msg.id === optimisticMessage.id
                ? {
                  ...msg,
                  deliveryStatus: c.messageDeliveryStatus.delivered,
                  id: response.messageBody.id,
                }
                : msg
            ),
          }));
        },
        (error) => {
          // Failed send
          setState((prevState) => ({
            ...prevState,
            messages: prevState.messages.map((msg) =>
              msg.id === optimisticMessage.id
                ? {
                  ...msg,
                  deliveryStatus: c.messageDeliveryStatus.failed,
                }
                : msg
            ),
          }));
        },
        messagePayload,
        chatId
      );
      
    }
  };

  // Handle message input change
  const handleMessageChange = (message: string) => {
    setState((prevState) => ({
      ...prevState,
      newMessage: message,
    }));
  };

  // Render component
  if (!chatId) {
    return (
      <div className={styles.chatSendContainer}>

      </div>
    );
  }

  return !isMobile ? 
      (
    <div style={{display:"flex",flexDirection:"column",overflow:"hidden",flexGrow:"1"}}>
    <ChatHeader participants={state.participants} />
    <div className={styles.chatSendContainer} ref={messageListRef}>
      {state.isLoading ? (
        <div>Loading messages...</div>
      ) : (
        <MessageList
          messages={state.messages}
          senderId={senderId}
          participants={state.participants}
          onResendMessage={handleResendMessage}
        />
      )}
    </div>
    <div>
      <MessageInput
        newMessage={state.newMessage}
        onMessageChange={handleMessageChange}
        onSendMessage={handleSendMessage}
        isEnableMsg={state.isEnable}
      />
    </div>
  </div>
  ):(
    <div style={{display:"flex",flexDirection:"column",overflow:"hidden",flexGrow:"1"}}>
    <ChatHeader participants={state.participants} />
    <div className={styles.chatSendContainerMobile} ref={messageListRef}>
      {state.isLoading ? (
        <div>Loading messages...</div>
      ) : (
        <MessageList
          messages={state.messages}
          senderId={senderId}
          participants={state.participants}
          onResendMessage={handleResendMessage}
        />
      )}
    </div>
    <div>
      <MessageInput
        newMessage={state.newMessage}
        onMessageChange={handleMessageChange}
        onSendMessage={handleSendMessage}
        isEnableMsg={state.isEnable}
      />
    </div>
  </div>
  )
};
export default SendChat;
