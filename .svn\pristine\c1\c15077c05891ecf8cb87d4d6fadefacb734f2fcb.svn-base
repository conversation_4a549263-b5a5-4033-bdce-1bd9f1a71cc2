import { ProgressBar } from 'primereact/progressbar';
import { useEffect, useState } from 'react';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import '../../../components/utils/util.css';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import c from '../../../helper/juggleStreetConstants';
import CustomWistiaUploader, { UploadedFile } from './video';
import CustomButton from '../../../commonComponents/CustomButton';
import { ConfirmationPopupRed, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import removeIcon from '../../../assets/images/Icons/remove.png';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';

const AddProfileVideo = () => {
    const dispatch = useDispatch<AppDispatch>();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploading, setUploading] = useState(false);
    const [uploadError, setUploadError] = useState(null);
    const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
    const [showInitialMessage, setShowInitialMessage] = useState(true);
    const { disableLoader, enableLoader } = useLoader();
    const [isChanged, setIsChanged] = useState(false);
    const hasExistingVideo = sessionInfo.data['medias'] && sessionInfo.data['medias'].length > 0;
    const currentVideo = hasExistingVideo ? sessionInfo.data['medias'][0] : null;
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const {isMobile}=useIsMobile();
    const saveMedia = async (media: UploadedFile) => {
        if (!media) return;

        const mediaToSave = {
            id: 0,
            externalHashedId: media.hashed_id,
            name: media.name,
            description: media.description,
            externalUpdatedDate: media.updated,
            mediaStatus: c.mediaStatus.queued,
            mediaType: c.mediaType.profileIntro,
            playerColor: '#299C4A',
            duration: media.duration,
            thumbnailUrl: media.thumbnail.url,
            width: media.thumbnail.width,
            height: media.thumbnail.height,
            mediaReviewStatus: c.mediaReviewStatus.PENDING,
            assetId: null,
            playbackId: null,
        };

        const payload = {
            ...sessionInfo.data,
            medias: [mediaToSave],
        };
        enableLoader();
        try {
            await dispatch(updateSessionInfo({ payload }));
            setShowInitialMessage(false);
            setUploadedFile(null);
            disableLoader();

            dispatch(incrementProfileActivationStep());
        } catch (error) {
            disableLoader();
            setUploadError('Failed to save media information.');
        }
    };

    const handleDelete = () => {
        showConfirmationPopup(
            'Important',
            'Are you sure you want to delete this video?',
            'Delete Video',
            <img src={removeIcon} alt="remove icon" style={{ height: '15px', width: '13.33px' }} />,
            async () => {
                try {
                    enableLoader();
                    const payload = {
                        ...sessionInfo.data,
                        medias: [],
                    };
                    await dispatch(updateSessionInfo({ payload }));
                    setShowInitialMessage(true);
                    setUploadedFile(null);
                    disableLoader();
                } catch (error) {
                    disableLoader();
                    setUploadError('Failed to delete the video.');
                }
            }
        );
    };


    const getStatusMessage = () => {
        if (showInitialMessage) {
            return 'Upload a short video, this is the best way to introduce yourself to new families. Helpers with a video receive more job invitations.';
        }

        const reviewStatus = currentVideo?.mediaReviewStatus;

        switch (reviewStatus) {
            case c.mediaReviewStatus.APPROVED:
                return 'Your profile video has been approved by our team. Families visiting your profile can now see the video.';
            case c.mediaReviewStatus.PENDING:
                return 'Your video is being reviewed and will be approved shortly.';
            case c.mediaReviewStatus.REJECTED:
                return 'The profile video you have uploaded has been rejected by our team. Please upload a different video.';
            default:
                return '';
        }
    };

    useEffect(() => {
        if (!hasExistingVideo && !uploadedFile) {
            setShowInitialMessage(true);
        }
    }, [hasExistingVideo, uploadedFile]);

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };

    const handleButtonClick = () => {
        if (isChanged) {
            // Handle the "Next" button click
            dispatch(incrementProfileActivationStep());
        } else {
            // Handle the "Skip" button click
            dispatch(incrementProfileActivationStep());
        }
    };

    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };

    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ConfirmationPopupRed confirmationProps={confirmationProps} />
            <ProfileCompletenessHeader
                title="Profile Video"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={()=> dispatch(decrementProfileActivationStep())}
            />
            <div style={{ maxWidth: '100%', width: 'auto', textWrap: 'wrap',  paddingInline:isMobile && "25px" }}>
                <h1
                    className='p-0 m-0 txt-clr font-semibold flex-wrap font-medium line-height-1'
                    style={{ fontSize: '18px' }}
                >
                    Profile Video
                </h1>
            </div>
            <div className='mt-2' style={{ maxWidth: '100%', width: '800px' }}>
                <CustomWistiaUploader
                    onUploadStateChange={(s) => {
                        setUploading(s);
                        setIsChanged(true);
                    }}
                    onUploadSuccess={(d) => {
                        setUploadedFile(d);
                        setIsChanged(true);
                    }}
                    onUploadProgress={(progress) => setUploadProgress(progress)}
                />
                <div style={{ paddingInline:isMobile && "15px"}} className='flex justify-content-center align-item-center'>
                    <button
                        className='mt-2 p-2 px-4 font-bold cursor-pointer'
                        onClick={hasExistingVideo ? handleDelete : () => saveMedia(uploadedFile)}
                        disabled={!hasExistingVideo && (!uploadedFile || uploading)}
                        style={{
                            backgroundColor: '#FFA500',
                            border: 'none',
                            borderRadius: '10px',
                            color: '#FFFFFF',
                        }}
                    >
                        {hasExistingVideo
                            ? 'Delete Video'
                            : uploading
                                ? `Uploading... ${uploadProgress}%`
                                : 'Upload Video'}
                    </button>
                </div>
                <div style={{ paddingInline:isMobile && "15px"}}  className='flex justify-content-center align-item-center'>
                    <h1 className='p-0 m-0 mt-2 txt-clr font-medium' style={{ fontSize: '12px' }}>
                        {getStatusMessage()}
                    </h1>
                </div>
                {uploadError && <p style={{ color: 'red' }}>{uploadError}</p>}
            </div>
            <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <>
                            <i className="pi pi-angle-left"></i>
                            Previous
                        </>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: "transparent",
                        color: "#585858",
                        width: "156px",
                        height: "39px",
                        fontSize: "14px",
                        fontWeight: "500",
                        margin:isMobile && "5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />
                <CustomButton
                    className={styles.hoverClass}
                    data-skip={isChanged ? 'false' : 'true'}
                    onClick={isChanged ? handleButtonClick : handleSkip}
                    label={
                        <>
                            {isChanged ? 'Next' : 'Skip'}
                            <i className={`pi pi-angle-${isChanged ? 'right' : 'right'}`} style={{ marginLeft: '8px' }}></i>
                        </>
                    }
                    style={
                        isChanged
                            ? {
                                backgroundColor: '#FFA500',
                                color: '#fff',
                                width: '156px',
                                height: '39px',
                                fontWeight: '800',
                                fontSize: '14px',
                                borderRadius: '8px',
                                border: '2px solid transparent',
                                boxShadow: '0px 4px 12px #00000',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                               margin : isMobile && "10px"
                            }
                            : {
                                backgroundColor: 'transparent',
                                color: '#585858',
                                width: '156px',
                                height: '39px',
                                fontWeight: '400',
                                fontSize: '14px',
                                borderRadius: '10px',
                                border: '1px solid #F0F4F7',
                               margin : isMobile && "10px"
                            }
                    }

                />
            </footer>

        </div>
    );
};
export default AddProfileVideo;
