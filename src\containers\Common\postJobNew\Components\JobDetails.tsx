import { useEffect, useRef, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { useSearchParams } from "react-router-dom";
import utils from "../../../../components/utils/util";
import { IoChevronDown } from "react-icons/io5";
import { Calendar } from "primereact/calendar";
import clockIconEnd from "../../../../assets/images/Icons/clockend.png";
import clockIconStart from "../../../../assets/images/Icons/clockstart.png";
import clockIconEndGreen from "../../../../assets/images/Icons/clock-Icon-end-green.png";
import clockIconStartGreen from "../../../../assets/images/Icons/clock-Icon-start-green.png";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import calender from "../../../../assets/images/Icons/calender.png";
import styles from "../../styles/job-details.module.css";
import { Divider } from "primereact/divider";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import { GoBack, Next } from "./Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

function JobDetails() {
  const { isMobile } = useIsMobile();
  return isMobile ? <JobDetailsMobile /> : <JobDetailsWeb />;
}

export default JobDetails;

const useJobTypeHook = () => {
  const { payload, prev, next, setpayload } = useJobManager();
  const [showDescription, setShowDescription] = useState<boolean>(false);
  const [searchParams] = useSearchParams();
  const [startTimeSelected, setStartTimeSelected] = useState(payload.jobStartTime ? true : false);
  const [endTimeSelected, setEndTimeSelected] = useState(payload.jobEndTime ? true : false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(payload.jobDate ? new Date(payload.jobDate) : null);
  const [dateError, setDateError] = useState<string>("");
  const [isStartSelected, setIsStartSelected] = useState(payload.jobStartTime ? true : false);
  const [isEndSelected, setIsEndSelected] = useState(payload.jobEndTime ? true : false);
  const [jobDescription, setJobDescription] = useState(payload.specialInstructions ? payload.specialInstructions : "");

  useEffect(() => {
    const subStep = Number(searchParams.get("subStep"));
    if (subStep === 1) {
      setShowDescription(true);
    } else {
      setShowDescription(false);
    }
  }, [searchParams]);

  useEffect(() => {
    if (payload.jobStartTime) {
      const startDate = utils.createDateFromTimeString(payload.jobStartTime);
      setStart(startDate);
      setUpdateStart(startDate);
      setStartTimeSelected(true);
      setIsStartSelected(true);
    }
    if (payload.jobEndTime) {
      const endDate = utils.createDateFromTimeString(payload.jobEndTime);
      setEnd(endDate);
      setUpdateEnd(endDate);
      setEndTimeSelected(true);
      setIsEndSelected(true);
    }
  }, [payload.jobStartTime, payload.jobEndTime]);

  // useEffect(() => {
  //   setEndMin(addOneHour(start));
  // }, [start]);

  useEffect(() => {
    setStart(getDefaultStartTime(selectedDate));
  }, [selectedDate]);

  useEffect(() => {
    setEnd(getDefaultEndTime(selectedDate));
  }, [selectedDate]);
  const isDateInPast = (date: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      if (isDateInPast(date)) {
        setDateError("Job date can't be earlier than today.");
        setSelectedDate(null);
      } else {
        setDateError("");
        setSelectedDate(date);
        setTimeError("");
      }
    } else {
      setSelectedDate(null);
      setDateError("");
    }
  };

  useEffect(() => {
    if (selectedDate && isDateInPast(selectedDate)) {
      setDateError("Job date can't be earlier than today.");
      setSelectedDate(null);
    }
  }, []); // Empty dependency array to run only on mount

  const getDefaultStartTime = (selectedDate: Date | null): Date => {
    const defaultStart = selectedDate ? new Date(selectedDate) : new Date();
    defaultStart.setHours(18, 0, 0, 0); // 6:00 PM
    return defaultStart;
  };

  const getDefaultEndTime = (selectedDate: Date | null): Date => {
    const defaultEnd = selectedDate ? new Date(selectedDate) : new Date();
    defaultEnd.setHours(22, 0, 0, 0); // 10:00 PM
    return defaultEnd;
  };

  const [timeError, setTimeError] = useState("");
  const [start, setStart] = useState<Date>(() => (payload.jobStartTime ? utils.createDateFromTimeString(payload.jobStartTime) : null));
  const [endMin, setEndMin] = useState<Date>(start);
  const [end, setEnd] = useState<Date | null>(() => (payload.jobEndTime ? utils.createDateFromTimeString(payload.jobEndTime) : null));
  const [updateStart, setUpdateStart] = useState<Date>(null);
  const [updateEnd, setUpdateEnd] = useState<Date>(null);

  const formatToCustomISOString = (date: Date) => {
    const year = date?.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const day = String(date.getUTCDate()).padStart(2, "0");
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    const seconds = String(date.getUTCSeconds()).padStart(2, "0");
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;
  };

  const formatTimeToString = (date: Date | null) => {
    if (!date) return "";
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    return `${hours}:${minutes}:${seconds}`;
  };

  const isFormValid = () =>
    selectedDate &&
    start &&
    end &&
    startTimeSelected &&
    endTimeSelected &&
    !isDateInPast(selectedDate) &&
    !timeError &&
    jobDescription.trim().length > 0;

  const isFormValidMobile = () => selectedDate && start && end && startTimeSelected && endTimeSelected && !isDateInPast(selectedDate) && !timeError;

  const addOneHour = (inputDate: Date): Date => {
    const updatedDate = new Date(inputDate);
    updatedDate.setHours(updatedDate.getHours() + 1);
    return updatedDate;
  };

  const setTimeToDate = (date: Date, time: string): Date => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    if (
      isNaN(hours) ||
      isNaN(minutes) ||
      (seconds !== undefined && isNaN(seconds)) ||
      hours < 0 ||
      hours > 23 ||
      minutes < 0 ||
      minutes > 59 ||
      (seconds !== undefined && (seconds < 0 || seconds > 59))
    ) {
      throw new Error('Invalid time format. Ensure the time is in "HH:mm" or "HH:mm:ss" format.');
    }
    const updatedDate = new Date(date);
    updatedDate.setHours(hours, minutes, seconds || 0, 0);
    return updatedDate;
  };

  const isJobOnNextDay = (startTime: Date | string | null, endTime: Date | string | null): boolean => {
    if (!startTime || !endTime) return false;
    const parseTime = (time: Date | string) => {
      if (typeof time === "string") {
        const [hours, minutes] = time.split(":").map(Number);
        return { hours, minutes };
      } else if (time instanceof Date) {
        return { hours: time.getHours(), minutes: time.getMinutes() };
      }
      throw new Error("Invalid time format");
    };
    const start = parseTime(startTime);
    const end = parseTime(endTime);
    return (
      end.hours < start.hours ||
      (end.hours === start.hours && end.minutes < start.minutes) ||
      (end.hours === start.hours && end.minutes === start.minutes)
    );
  };

  const isNextDay = isJobOnNextDay(start, end);

  return {
    payload,
    next,
    prev,
    setpayload,
    showDescription,
    setShowDescription,
    searchParams,
    startTimeSelected,
    setStartTimeSelected,
    endTimeSelected,
    setEndTimeSelected,
    selectedDate,
    setSelectedDate,
    dateError,
    setDateError,
    isStartSelected,
    setIsStartSelected,
    isEndSelected,
    setIsEndSelected,
    jobDescription,
    setJobDescription,
    timeError,
    setTimeError,
    start,
    setStart,
    endMin,
    setEndMin,
    end,
    setEnd,
    updateStart,
    setUpdateStart,
    updateEnd,
    setUpdateEnd,
    isDateInPast,
    handleDateChange,
    getDefaultStartTime,
    getDefaultEndTime,
    formatToCustomISOString,
    formatTimeToString,
    isFormValid,
    isFormValidMobile,
    addOneHour,
    setTimeToDate,
    isJobOnNextDay,
    isNextDay,
  };
};

function formatTimeTo12Hour(date: Date): string {
  const hours24 = date.getHours();
  const minutes = date.getMinutes();
  const period = hours24 >= 12 ? "pm" : "am";
  const hours12 = hours24 % 12 === 0 ? 12 : hours24 % 12;
  const formattedMinutes = minutes.toString().padStart(2, "0");
  return `${hours12}:${formattedMinutes}${period}`;
}

const Time = ({
  text,
  date,
  onUpdate,
  minDate,
  enabled,
  updateTime,
  imgSrc,
  isSelect,
  onTimeSelected,
  greenClockIcon,
  onTimeError,
  isStartTime,
}: {
  text?: string;
  date: Date;
  onUpdate: (date: Date) => void;
  minDate?: Date;
  enabled: boolean;
  updateTime: Date;
  imgSrc: string;
  greenClockIcon?: string;
  isSelect: boolean;
  isStartTime?: boolean;
  onTimeSelected?: (selected: boolean) => void;
  onTimeError?: (error: string) => void;
}) => {
  const [time, setTime] = useState<Date>(date);
  const [timeToShow, setTimeToShow] = useState<Date | null>(null);
  const [isSelected, setIsSelected] = useState<boolean>(isSelect);
  const timeRef = useRef<Calendar>(null);
  const timeMountRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useIsMobile();

  useEffect(() => {
    setTime(updateTime ?? date);
    setTimeToShow(updateTime);
  }, [updateTime]);

  useEffect(() => {
    onUpdate(timeToShow);
  }, [timeToShow]);

  useEffect(() => {
    if (date) {
      setTime(date);
    }
  }, [date]);

  const validateStartTime = (selectedTime: Date | null): boolean => {
    if (!selectedTime) {
      onTimeError?.("Invalid time selected.");
      return false;
    }
    const currentTime = new Date();
    const selectedDate = new Date(selectedTime);
    const isToday =
      currentTime.getFullYear() === selectedDate.getFullYear() &&
      currentTime.getMonth() === selectedDate.getMonth() &&
      currentTime.getDate() === selectedDate.getDate();
    if (isToday) {
      const diffInMilliseconds = selectedTime.getTime() - currentTime.getTime();
      const diffInMinutes = diffInMilliseconds / (1000 * 60);
      if (diffInMinutes < 30) {
        onTimeError?.("Job start time must be at least 30 minutes away.");
        return false;
      }
    }
    onTimeError?.("");
    return true;
  };

  const handleTimeChange = (e: { value: Date }) => {
    if (isStartTime) {
      const isValid = validateStartTime(e.value);
      setTime(e.value);
      if (isValid) {
        setIsSelected(true);
        onTimeSelected?.(true);
        onUpdate(e.value);
      } else {
        setIsSelected(false);
        onTimeSelected?.(false);
      }
    } else {
      setTime(e.value);
      setIsSelected(true);
      onTimeSelected?.(true);
      onUpdate(e.value);
    }
  };

  const handleSave = (e: React.MouseEvent) => {
    e.preventDefault();
    let isValid = true;
    if (isStartTime) {
      isValid = validateStartTime(time);
    }
    if (isValid) {
      setTimeToShow(time);
      timeRef.current?.hide();
      setIsSelected(true);
      onTimeSelected?.(true);
      onUpdate(time);
    }
  };

  return !isMobile ? (
    <div ref={timeMountRef} className="flex flex-column gap-2 relative">
      <div
        className="flex gap-2 align-items-center justify-content-between p-2 select-none cursor-pointer"
        style={{
          border: isSelected ? "3px solid #179D52" : "1px solid #585858",
          borderRadius: "10px",
          backgroundColor: "#ffffff",
          width: "152px",
          height: "56px",
          marginTop: "10px",
          ...(enabled ? {} : { pointerEvents: "none" }),
        }}
        onClick={(e) => {
          e.preventDefault();
          if (enabled && timeRef && timeMountRef) {
            timeRef.current.show();
          }
        }}
      >
        <img src={imgSrc} alt="clock" width="18" height="18px" style={enabled ? {} : { opacity: 0.5 }} />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: enabled ? "#585858" : "#DFDFDF",
            pointerEvents: enabled ? "auto" : "none",
            textWrap: "nowrap",
          }}
        >
          {timeToShow ? formatTimeTo12Hour(timeToShow) : text}
        </p>
        <IoChevronDown color={enabled ? "#585858" : "#DFDFDF"} />
      </div>
      <Calendar
        ref={timeRef}
        stepMinute={15}
        value={time}
        minDate={minDate}
        showOnFocus={false}
        touchUI={false}
        style={{ display: "none" }}
        timeOnly
        appendTo={timeMountRef.current}
        hourFormat="12"
        onChange={handleTimeChange}
        disabled={!enabled}
        footerTemplate={() => (
          <div className="flex gap-2">
            <button
              className="flex-grow-1 cursor-pointer"
              style={{
                backgroundColor: "#ffffff",
                fontWeight: "500",
                fontSize: "16px",
                color: "#CDCDCD",
                border: "1px solid #4E4E4E",
                borderRadius: "12px",
                height: "36px",
                pointerEvents: enabled ? "auto" : "none",
              }}
              onClick={(e) => {
                e.preventDefault();
                setTimeToShow(null);
                timeRef.current.hide();
                setIsSelected(false);
                onTimeSelected?.(false);
                onTimeError?.("");
              }}
            >
              Cancel
            </button>
            <button
              className="flex-grow-1 cursor-pointer"
              style={{
                backgroundColor: "#FFA500",
                fontWeight: "500",
                fontSize: "16px",
                color: "#FFFFFF",
                border: "none",
                borderRadius: "12px",
                height: "36px",
                pointerEvents: enabled ? "auto" : "none",
              }}
              onClick={handleSave}
            >
              Save
            </button>
          </div>
        )}
      />
    </div>
  ) : (
    <div ref={timeMountRef} className="flex flex-column gap-2 relative">
      <div style={{ display: "flex", flexDirection: "row", alignItems: "baseline", gap: "10px" }}>
        <div
          className="flex gap-2 align-items-center justify-content-between p-2 select-none cursor-pointer"
          style={{
            border: isSelected ? "1px solid #179D52" : "2px solid #585858",
            borderRadius: "30px",
            backgroundColor: "#ffffff",
            width: "min-content",
            marginTop: "10px",
            ...(enabled ? {} : { pointerEvents: "none" }),
          }}
          onClick={(e) => {
            e.preventDefault();
            if (enabled && timeRef && timeMountRef) {
              timeRef.current.show();
            }
          }}
        >
          <img src={isSelected ? greenClockIcon : imgSrc} alt="clock" width="13px" height="13.5px" style={enabled ? {} : { opacity: 0.5 }} />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "600",
              fontSize: "14px",
              color: isSelected ? "#179D52" : "#179D52",
              pointerEvents: enabled ? "auto" : "none",
              textWrap: "nowrap",
            }}
          >
            {timeToShow ? formatTimeTo12Hour(timeToShow) : text}
          </p>
          <IoChevronDown color={enabled ? "#585858" : "#DFDFDF"} />
        </div>
        <div>{isSelected && <div className={styles.checkIcon}></div>}</div>
      </div>
      <Calendar
        ref={timeRef}
        stepMinute={15}
        value={time}
        minDate={minDate}
        showOnFocus={false}
        touchUI={true}
        style={{ display: "none" }}
        timeOnly
        hourFormat="12"
        onChange={handleTimeChange}
        disabled={!enabled}
        footerTemplate={() => (
          <div className="flex gap-2">
            <button
              className="flex-grow-1 cursor-pointer"
              style={{
                backgroundColor: "#ffffff",
                fontWeight: "500",
                fontSize: "16px",
                color: "#CDCDCD",
                border: "1px solid #4E4E4E",
                borderRadius: "12px",
                height: "36px",
                pointerEvents: enabled ? "auto" : "none",
              }}
              onClick={(e) => {
                e.preventDefault();
                setTimeToShow(null);
                timeRef.current.hide();
                setIsSelected(false);
                onTimeSelected?.(false);
                onTimeError?.("");
              }}
            >
              Cancel
            </button>
            <button
              className="flex-grow-1 cursor-pointer"
              style={{
                backgroundColor: "#FFA500",
                fontWeight: "500",
                fontSize: "16px",
                color: "#FFFFFF",
                border: "none",
                borderRadius: "12px",
                height: "36px",
                pointerEvents: enabled ? "auto" : "none",
              }}
              onClick={handleSave}
            >
              Save
            </button>
          </div>
        )}
      />
    </div>
  );
};

const TimeDurationDisplay = ({ startTime, endTime }) => {
  const calculateDuration = () => {
    if (!startTime || !endTime) return null;
    try {
      const start = startTime instanceof Date ? startTime : new Date(startTime);
      const end = endTime instanceof Date ? endTime : new Date(endTime);
      let adjustedEnd = new Date(end);
      if (end <= start) {
        adjustedEnd.setDate(end.getDate() + 1);
      }
      const diff = adjustedEnd.getTime() - start.getTime();
      const totalHours = diff / (1000 * 60 * 60);
      if (totalHours < 0) return "0.0";
      return totalHours.toFixed(1);
    } catch (error) {
      console.error("Error calculating duration:", error);
      return "0.0";
    }
  };

  const duration = calculateDuration();
  if (!duration) return null;

  return (
    <div className={styles.totalDuration}>
      <span className="duration">{duration}</span> {parseFloat(duration) === 1.0 ? "Total hour" : "Total hours"}
    </div>
  );
};

const JobDetailsWeb = () => {
  const {
    payload,
    prev,
    next,
    setpayload,
    selectedDate,
    dateError,
    isStartSelected,
    isEndSelected,
    jobDescription,
    setJobDescription,
    timeError,
    setTimeError,
    start,
    setStart,
    end,
    setEnd,
    updateStart,
    updateEnd,
    handleDateChange,
    formatToCustomISOString,
    formatTimeToString,
    isFormValid,
    setStartTimeSelected,
    setEndTimeSelected,
    isNextDay,
  } = useJobTypeHook();

  return (
    <div className="w-full h-full">
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          width: "100%",
          height: "100%",
          flexDirection: "column",
          position: "relative",
          
        }}
        className="overflow-hidden overflow-y-auto"
      >
        <div className={styles.jobDetailsContainer}>
          <main className={styles.jobDetailsMainContent}>
            <div className={styles.jobDetailsUpperSection}>
              <h2 className={styles.jobDetailsSectionTitle}>Select the date of the job</h2>
              <div className={`${styles.jobDetailsDateFields} `}>
                <div className={styles.jobDetailsDateField}>
                  <label htmlFor="jobDate" className={styles.jobDetailsDateLabel}>
                    Job Date
                  </label>
                  <Calendar
                    id="jobDate"
                    readOnlyInput
                    value={selectedDate}
                    onChange={(e) => handleDateChange(e.value)}
                    dateFormat="DD, dth 'of' MM"
                    showIcon={true}
                    icon={<img src={calender} alt="Clock Icon" height={"18px"} width={"16px"} />}
                    iconPos="left"
                    placeholder="Tap to Select"
                    className={`${styles.customCalendar} ${selectedDate ? styles.selectedBorder : ""}`}
                    minDate={new Date()}
                  />
                  {dateError && <p className={styles.errorMessageTime}>{dateError}</p>}
                  {timeError && (
                    <div style={{ marginTop: "20px" }} className={styles.errorMessageTime}>
                      {timeError}
                    </div>
                  )}
                </div>
                <div className={styles.jobDetailsDateField}>
                  <label htmlFor="Start Time" className={styles.jobDetailsDateLabel}>
                    Start Time
                  </label>
                  <Time
                    date={start}
                    enabled
                    onUpdate={(e) => setStart(e)}
                    text="Start Time"
                    updateTime={updateStart}
                    imgSrc={clockIconStart}
                    isSelect={isStartSelected}
                    onTimeSelected={setStartTimeSelected}
                    onTimeError={(error) => setTimeError(error)}
                    isStartTime={true}
                  />
                </div>
                <div className={styles.jobDetailsDateField}>
                  <label htmlFor="End Time" className={styles.jobDetailsDateLabel}>
                    End Time
                  </label>
                  <Time
                    date={end}
                    enabled
                    onUpdate={(e) => setEnd(e)}
                    updateTime={updateEnd}
                    text="End Time"
                    imgSrc={clockIconEnd}
                    isSelect={isEndSelected}
                    onTimeSelected={setEndTimeSelected}
                    isStartTime={false}
                  />
                  <div>{isNextDay && <span className={styles.nextDayIndicator}>On the following day</span>}</div>
                </div>
              </div>
            </div>
            <Divider className={styles.dividerPostDetails} />
            <div className={styles.jobDetailsLowerSection}>
              <h2 className={styles.jobDetailsSectionTitleSec}>Job Details</h2>
              <p className={styles.jobDetailsSectionInstructSec}>Describe the job*</p>
              <textarea
                className={`${styles.jobDetailsTextarea} ${jobDescription ? styles.activeBorder : ""}`}
                placeholder="What does your helper need to know about this job and working with your family?"
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
              />
            </div>
          </main>
        </div>
        <Divider  />
        <br />
        <div style={{ width: "100%", paddingLeft: "70px" }} className="flex justify-content-start sticky bottom-0 bg-white align-items-center ">
          <div style={{ width: "69%" }} className="flex justify-content-between align-items-center py-3">
            <GoBack
              onClick={() => {
                setpayload({
                  ...payload,
                  // jobDate: formatToCustomISOString(selectedDate),
                  // jobDateDay: selectedDate.getDate(),
                  // jobDateMonth: selectedDate.getMonth() + 1,
                  // jobDateYear: selectedDate.getFullYear(),
                  // jobStartTime: formatTimeToString(start),
                  // jobEndTime: formatTimeToString(end),
                  // specialInstructions: jobDescription,
                });
                prev("job-posting");
              }}
            />
            <Next
              disabled={!isFormValid()}
              onClick={() => {
                setpayload({
                  ...payload,
                  jobDate: formatToCustomISOString(selectedDate),
                  jobDateDay: selectedDate.getDate(),
                  jobDateMonth: selectedDate.getMonth() + 1,
                  jobDateYear: selectedDate.getFullYear(),
                  jobStartTime: formatTimeToString(start),
                  jobEndTime: formatTimeToString(end),
                  specialInstructions: jobDescription,
                });
                next("jobpricing-step1");
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const JobDetailsMobile = () => {
  const {
    payload,
    prev,
    next,
    setpayload,
    showDescription,
    selectedDate,
    startTimeSelected, // Added
    endTimeSelected, // Added
    setSelectedDate,
    dateError,
    setDateError,
    isStartSelected,
    setIsStartSelected,
    isEndSelected,
    setIsEndSelected,
    jobDescription,
    setJobDescription,
    timeError,
    setTimeError,
    start,
    setStart,
    end,
    setEnd,
    updateStart,
    setUpdateStart,
    updateEnd,
    setUpdateEnd,
    handleDateChange,
    formatToCustomISOString,
    formatTimeToString,
    isFormValidMobile,
    setStartTimeSelected,
    setEndTimeSelected,
    isNextDay,
  } = useJobTypeHook();

  return (
    <>
      {!showDescription ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            width: "100%",
            height: "100%",
            backgroundColor: "#fff",
            position: "relative",
          }}
        >
          <div className={styles.jobDetailsContainerMobile}>
            <main className={styles.jobDetailsMainContentMobile}>
              <h2 className={styles.jobDetailsSectionTitleMobile}>Select the date of the job</h2>
              <div className={styles.jobDetailsUpperSectionMobile}>
                <div className={`${styles.jobDetailsDateFieldsMobile} flex flex-column md:flex-row`}>
                  <div className={styles.jobDetailsDateField}>
                    <Calendar
                      id="jobDate"
                      readOnlyInput
                      value={selectedDate}
                      onChange={(e) => handleDateChange(e.value)}
                      dateFormat="DD, dth 'of' MM"
                      showIcon={true}
                      icon={<img src={calender} alt="Clock Icon" height={"18px"} width={"16px"} />}
                      iconPos="left"
                      placeholder="Tap to Select"
                      className={`${styles.customCalendarMobile} ${selectedDate ? styles.selectedBorderMobile : ""}`}
                      minDate={new Date()}
                    />
                    {dateError && <p className={styles.errorMessageTime}>{dateError}</p>}
                    {timeError && (
                      <div style={{ marginTop: "5px" }} className={styles.errorMessageTime}>
                        {timeError}
                      </div>
                    )}
                  </div>
                  {selectedDate && (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                      <div
                        style={{ display: "flex", flexDirection: "column", justifyContent: "center" }}
                        className={`${styles.pickerContainer} ${endTimeSelected && startTimeSelected ? styles.pickerContainerSelected : ""}`}
                      >
                        <div style={{ display: "flex", justifyContent: "center" }}>
                          <div
                            className={`${styles.startTimeMobile} ${
                              startTimeSelected ? (endTimeSelected ? styles.startTimeWithEndSelected : styles.selectedTime) : ""
                            }`}
                          >
                            <label
                              htmlFor="Start Time"
                              className={styles.jobDetailsDateLabel}
                              style={{
                                color: startTimeSelected ? "#179D52" : "#585858",
                                fontWeight: startTimeSelected ? 700 : 500,
                              }}
                            >
                              Start Time
                            </label>
                            <Time
                              date={start}
                              enabled
                              onUpdate={(e) => setStart(e)}
                              updateTime={updateStart}
                              imgSrc={clockIconStart}
                              isSelect={isStartSelected}
                              onTimeError={(error) => setTimeError(error)}
                              onTimeSelected={setStartTimeSelected}
                              greenClockIcon={clockIconStartGreen}
                              isStartTime={true}
                            />
                          </div>
                        </div>
                        <div style={{ display: "flex", justifyContent: "center" }}>
                          <div
                            className={`${styles.endTimeMobile} ${
                              endTimeSelected ? (startTimeSelected ? styles.endTimeWithStartSelected : styles.selectedTimeEnd) : ""
                            }`}
                          >
                            <label
                              style={{
                                paddingLeft: "8px",
                                color: endTimeSelected ? "#179D52" : "#585858",
                                fontWeight: endTimeSelected ? 700 : 500,
                              }}
                              htmlFor="End Time"
                              className={styles.jobDetailsDateLabel}
                            >
                              End Time
                            </label>
                            <Time
                              date={end}
                              enabled
                              onUpdate={(e) => setEnd(e)}
                              updateTime={updateEnd}
                              imgSrc={clockIconEnd}
                              isSelect={isEndSelected}
                              onTimeSelected={setEndTimeSelected}
                              greenClockIcon={clockIconEndGreen}
                              isStartTime={false}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div style={{ display: "flex", justifyContent: "end", marginTop: "-8px" }}>
                    {isNextDay && <span className={styles.nextDayIndicator}>On the following day</span>}
                  </div>
                  <div style={{ display: "flex", justifyContent: "end" }}>
                    {startTimeSelected && endTimeSelected && <TimeDurationDisplay startTime={start} endTime={end} />}
                  </div>
                </div>
              </div>
            </main>

            <BackButtonPortal id="back-button-portal">
              <img
                src={SideArrow}
                alt="cross"
                width={13}
                height={20}
                className="cursor-pointer"
                onClick={() => {
                  setpayload({
                    ...payload,
                  });
                  prev("job-posting");
                }}
              />
            </BackButtonPortal>

            <CustomFooterButton
              label="Next"
              isDisabled={!(selectedDate && startTimeSelected && endTimeSelected)}
              onClick={() => {
                setpayload({
                  ...payload,
                  jobDate: formatToCustomISOString(selectedDate),
                  jobDateDay: selectedDate.getDate(),
                  jobDateMonth: selectedDate.getMonth() + 1,
                  jobDateYear: selectedDate.getFullYear(),
                  jobStartTime: formatTimeToString(start),
                  jobEndTime: formatTimeToString(end),
                  specialInstructions: jobDescription,
                });
                next("job-description-mobile");
              }}
            />
          </div>
        </div>
      ) : (
        <div>{/* Placeholder for JobDescription component */}</div>
      )}
    </>
  );
};
