import React, { useState, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { Jobs } from "../types";
import styles from "../../styles/rate-helpers.module.css";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import calender from "../../../../assets/images/Icons/calender.png";
import home from "../../../../assets/images/Icons/home.png";
import ChildcareImage from "../../../../assets/images/Icons/childcare-smile.png";
import OddJobImage from "../../../../assets/images/Icons/odd_job.png";
import TutoringImage from "../../../../assets/images/Icons/tutoring-book.png";
import sideArrow from "../../../../assets/images/Icons/side-aroow.png";
import filledStar from "../../../../assets/images/Icons/filled-star.png";
import unfilled from "../../../../assets/images/Icons/unfiled.png";
import { Divider } from "primereact/divider";
import { InputTextarea } from "primereact/inputtextarea";
import { Rating } from "primereact/rating";
import useIsMobile from "../../../../hooks/useIsMobile";
import { AppDispatch, RootState } from "../../../../store";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { refreshAccount } from "../../../../store/tunks/sessionInfoTunk";
import c from "../../../../helper/juggleStreetConstants";
import { IoClose } from "react-icons/io5";
import utils from "../../../../components/utils/util";

type Props = {
  visible: boolean;
  job: Jobs | null;
  onClose: () => void;
};

const getJobDetails = (currentJob?: Jobs | null) => {
  const jobToUse = currentJob || { jobType: 0 };

  switch (jobToUse.jobType) {
    case 256:
      return {
        label: "Odd Job",
        image: (
          <img src={OddJobImage} alt="Odd Job" width={14.33} height={14.5} />
        ),
      };
    case 128:
    case 64:
      return {
        label: "Tutoring",
        image: (
          <img src={TutoringImage} alt="Tutoring" width={14.33} height={14.5} />
        ),
      };
    default:
      return {
        label: "Childcare",
        image: (
          <img
            src={ChildcareImage}
            alt="Childcare"
            width={14.33}
            height={14.5}
          />
        ),
      };
  }
};

const formatTime = (time: string) => {
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours);
  const period = hour >= 12 ? "pm" : "am";
  const formattedHour = hour % 12 || 12;
  return `${formattedHour}:${minutes}${period}`;
};

const formatDateMobile = (dateString: string) => {
  const date = new Date(dateString);
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const dayOfWeek = days[date.getDay()];
  const dateNum = date.getDate();
  const monthAbbr = months[date.getMonth()];
  const year = date.getFullYear();
  return `${dayOfWeek} ${dateNum} ${monthAbbr} ${year}`;
};

const SinglePageRatingComponent = ({
  job,
  onComplete,
}: {
  job: Jobs;
  onComplete: (
    ratings: { [key: string]: number | string },
    feedback: string
  ) => void;
}) => {
  const { isMobile } = useIsMobile();
  const [ratings, setRatings] = useState({
    Punctuality: 0,
    "Performance of duties": 0,
    "Communication with you": 0,
    "What rating do you think your children would give?": 0,
  });

  const [feedback, setFeedback] = useState<string>("");

  const steps = [
    { label: "Punctuality", key: "Punctuality" },
    { label: "Performance of duties", key: "Performance of duties" },
    { label: "Communication with you", key: "Communication with you" },
    {
      label: "What rating do you think your children would give?",
      key: "What rating do you think your children would give?",
    },
    { label: "Public review of Helpers", key: "Public review of Helpers" },
  ];

  const handleRatingChange = (key: string, value: number) => {
    setRatings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleFeedbackChange = (value: string) => {
    setFeedback(value);
  };

  const handleSubmit = () => {
    onComplete(ratings, feedback);
  };

  return (
    <div
      className="flex flex-column gap-3"
      style={{ fontSize: "20px", fontWeight: "700", color: "#585858" }}
    >
      {steps.map((step, index) => (
        <div key={step.key} className={styles.ratingStep}>
          <label>{step.label}</label>
          {index < steps.length - 1 ? (
            <Rating
              value={ratings[step.key] as number}
              onChange={(e) => handleRatingChange(step.key, e.value || 0)}
              cancel={false}
              className={styles.ratingsStar}
              stars={5}
              onIcon={
                <img
                  src={filledStar}
                  alt="rating on"
                  width="40px"
                  height="40px"
                />
              }
              offIcon={
                <img
                  src={unfilled}
                  alt="rating off"
                  width="40px"
                  height="40px"
                />
              }
            />
          ) : (
            <>
              <p
                style={{
                  margin: "0px",
                  fontSize: isMobile ? "12px" : "14px",
                  fontWeight: "400",
                }}
              >
                Optional review which will appear on{" "}
                {`${job.awardedFirstName}'s`} profile
              </p>
              <InputTextarea
                value={feedback}
                autoResize
                onChange={(e) => handleFeedbackChange(e.target.value)}
                rows={5}
                cols={30}
                placeholder="Write a quick review...This will appear on the helpers profile and help other parents in your neighbourhood"
                className={isMobile ? styles.textAreaMobile : styles.textArea}
              />
            </>
          )}
        </div>
      ))}

      <div className={styles.ratingNavigation}>
        <button
          className={styles.navigationButtonSecond}
          onClick={handleSubmit}
          disabled={Object.values(ratings).some((rating) => rating === 0)}
        >
          Submit
        </button>
      </div>
    </div>
  );
};

const RateHelperModal = ({ visible, job, onClose }: Props) => {
  const { isMobile } = useIsMobile();
  const [selectedJob, setSelectedJob] = useState<Jobs | null>(job);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { disableLoader, enableLoader } = useLoader();
  const [searchParams] = useSearchParams();
  const wordLimit = 30; // Set your preferred word limit
  const [isExpanded, setIsExpanded] = useState(false);
  const truncateText = (text, wordLimit) => {
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };
  useEffect(() => {
    setSelectedJob(job);
  }, [job]);

  const handleRatingComplete = (
    ratings: { [key: string]: number | string },
    feedback: string
  ) => {
    if (!selectedJob) return;

    const payload = {
      category1: ratings["Punctuality"],
      category2: ratings["Performance of duties"],
      category3: ratings["Communication with you"],
      category4: ratings["What rating do you think your children would give?"],
      feedback: feedback,
      jobId: selectedJob.id,
      ratingFor: selectedJob.awardedApplicantId,
      ratingBy: sessionInfo.data["id"],
      ratingMode: c.ratingMode.PAGE,
    };

    enableLoader();

    Service.rateHelper(
      async (response) => {
        setSelectedJob(null);
        await dispatch(refreshAccount());
        disableLoader();
        onClose();
      },
      (error) => {
        disableLoader();
      },
      selectedJob.id,
      payload
    );
  };

  const renderSelectedJobDetails = (job: Jobs | null, isMobile: boolean) => {
    if (!job) return null;

    const jobDetails = getJobDetails(job);
    return (
      <>
        <div className={styles.backButtonContainer}></div>
        <div>
          <div className={styles.ratehelperSecondDivMobile}>
            <img
              src={job.awardedImageSrc}
              alt={`Rate helper image for ${job.awardedFirstName}`}
              style={{ width: "86px", height: "81px", borderRadius: "50%" }}
            />
            <div className={styles.rateNameDivMobile}>
              <h1 className={styles.rateName}>
                {job.awardedFirstName} {job.awardedLastInitial}
              </h1>
              <div
                style={{ display: "flex", flexDirection: "row", gap: "10px" }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    fontSize: "14px",
                    fontWeight: "400",
                    gap: "5px",
                    color: "#585858",
                    whiteSpace: "nowrap",
                  }}
                >
                  {/* {jobDetails.image} */}
                  <span
                    style={{
                      margin: "0px",
                      fontSize: "12px",
                      fontWeight: "700",
                    }}
                    className={styles.jobTypeLabel}
                  >
                    {jobDetails.label} -
                  </span>
                  <p
                    className={styles.subPara}
                    style={{
                      margin: "0px",
                      fontSize: "12px",
                      fontWeight: "700",
                    }}
                  >
                    {job.isRecurringJob === true
                      ? "Recurring Job"
                      : job.isRecurringJob === false
                      ? "One-off Job"
                      : ""}
                  </p>
                </div>
              </div>
              <div className="flex flex-row gap-2 mt-2">
                <div className={styles.timeDateMobile}>
                  {formatDateMobile(job.jobDate)}
                </div>
                {job.isRecurringJob || job.isTutoringJob ? (
                  <div className={styles.timeGetMobile}>
                    {job.duration} weeks
                  </div>
                ) : (
                  <>
                    <div className={styles.timeGetMobile}>
                      {formatTime(job.jobStartTime)} -{" "}
                      {formatTime(job.jobEndTime)}
                    </div>
                  </>
                )}
              </div>
              <div style={{marginTop:"10px"}} className={styles.timeDateMobile}>
                 {utils.cleanAddress(job.formattedAddress)}
                </div>
                <p
                style={{ marginTop: "5px", fontSize: "14px" }}
                className={styles.yourJobDescriptionMobile}
              >
                {isExpanded
                  ? job.specialInstructions
                  : truncateText(job.specialInstructions, wordLimit)}
              </p>
              {job.specialInstructions.split(" ").length > wordLimit && (
                <button
                  className={styles.seeMoreButton}
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? "See Less" : "See More"}
                </button>
              )}
            </div>
            
          </div>
          <Divider className="mb-3" />
          <SinglePageRatingComponent
            job={job}
            onComplete={handleRatingComplete}
          />
        </div>
      </>
    );
  };

  return (
    <Dialog
      visible={visible}
      onHide={onClose}
      draggable={false}
      dismissableMask={true} 
      className={styles.RatingDialog}
      content={
        <div style={{ backgroundColor: "#fff", borderRadius: "20px", minHeight:isMobile && "500px", overflowY:isMobile ? "auto" : "auto", marginInline:isMobile && "16px"}}>
          <div onClick={onClose} className={styles.closeBtn}>
            <IoClose />
          </div>
          <div className={styles.RatingHeader}>Rate Helper</div>
          <div className="p-3">
            {renderSelectedJobDetails(selectedJob, isMobile)}
          </div>
        </div>
      }
    />
  );
};

export default RateHelperModal;
