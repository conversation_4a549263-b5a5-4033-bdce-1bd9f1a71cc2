import { createContext, CSSProperties, useContext, useEffect, useMemo, useState } from "react";
import ReactDOM from "react-dom";
import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
import { Calendar } from "primereact/calendar";
import { IoChevronDown, IoClose } from "react-icons/io5";
import { CustomDropdown, CustomDropdownRoaster, CustomDropdownRoasterMobile } from "./DaysAndSchedule";
import { IoMdClose } from "react-icons/io";

// Assets
import ClockStart from "../../../../../assets/images/Icons/clockstart.png";
import ClockEnd from "../../../../../assets/images/Icons/clockend.png";
import PencilBlack from "../../../../../assets/images/Icons/pencil-black.png";
import AddShiftImg from "../../../../../assets/images/Icons/add.png";
import CalenderWhite from "../../../../../assets/images/Icons/calenderWhite.png";
import SideArrow from "../../../../../assets/images/Icons/side_arrow_left.png";
import { PayloadTemplate, useJobManager } from "../../provider/JobManagerProvider";
import { RadioButton } from "../Buttons";
import CustomFooterButton from "../../../../../commonComponents/CustomFooterButtonMobile";
import BackButtonPortal from "../../../../../commonComponents/BackButtonPortal";

// Types
interface TShift {
  isRequired: boolean;
  jobStartTime: string;
  jobEndTime: string;
  hourlyPrice: number;
  price: number;
}

interface IShiftEntry {
  dayOfWeek: number;
  price: number;
  isRequired: boolean;
  jobStartTime: string;
  jobEndTime: string;
  hourlyPrice: number;
  shifts: TShift[];
}

enum ShiftEntryErrors {
  noErrors = -1,
  startTimeNoFilled = 0,
}

type ShiftEntry = {
  startTime: Date | null;
  endTime: Date | null;
  endStartTime?: Date | null;
  error: ShiftEntryErrors[];
};
type Shift = {
  hasSecondShift: boolean;
  firstShift?: ShiftEntry | null; // Allow null for firstShift
  secondShift?: ShiftEntry | null; // Allow null for secondShift
  day: number;
  shiftType?: number;
};

// Constants
const DAYS_SHORT = ["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"];
const DAYS_FULL = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const DAYS_MAP = [
  { day: 0, short: "Sun", full: "Sunday" },
  { day: 1, short: "Mon", full: "Monday" },
  { day: 2, short: "Tue", full: "Tuesday" },
  { day: 3, short: "Wed", full: "Wednesday" },
  { day: 4, short: "Thu", full: "Thursday" },
  { day: 5, short: "Fri", full: "Friday" },
  { day: 6, short: "Sat", full: "Saturday" },
];
const ACTIVE_BORDER = "2px solid #179D52";
const INACTIVE_BORDER = "1px solid #585858";

// Styles
const createStyles = (e1: Partial<CSSProperties>, e2: Partial<CSSProperties>) => ({ e1, e2 });

const styles = {
  e1ae2a: createStyles({ border: ACTIVE_BORDER }, { borderInline: ACTIVE_BORDER, borderBottom: ACTIVE_BORDER }),
  e1ae2ia: createStyles({ border: ACTIVE_BORDER }, { borderInline: INACTIVE_BORDER, borderBottom: INACTIVE_BORDER }),
  e1iae2a: createStyles({ borderTop: INACTIVE_BORDER, borderInline: INACTIVE_BORDER }, { border: ACTIVE_BORDER }),
  e1iae2ia: createStyles({ border: INACTIVE_BORDER }, { borderInline: INACTIVE_BORDER, borderBottom: INACTIVE_BORDER }),
} as const;

// Utility Functions
function convertTimeToDate(time: string | null): Date {
  if (!time) {
    return new Date();
  }

  const timeParts = time.split(":").map(Number);

  const hours = timeParts[0];
  const minutes = timeParts[1];
  const seconds = timeParts.length > 2 ? timeParts[2] : 0;

  const now = new Date();

  now.setHours(hours, minutes, seconds, 0);

  return now;
}

function formatDateToTimeString(date: Date): string {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

function convertDate(input: Date | string): Date | string {
  if (input instanceof Date) {
    const year = input.getFullYear();
    const month = String(input.getMonth() + 1).padStart(2, "0");
    const day = String(input.getDate()).padStart(2, "0");
    const hours = "00";
    const minutes = "00";
    const seconds = "00";

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  } else if (typeof input === "string") {
    return new Date(input);
  } else {
    return null;
  }
}

function shiftEntryFilled(shift: ShiftEntry): boolean {
  return shift?.startTime !== null && shift?.endTime !== null;
}

function calculateHoursDifference(date1: Date, date2: Date): number {
  if (!date1 || !date2) return 0;

  let startTimestamp = date1.getTime();
  let endTimestamp = date2.getTime();

  // If times are exactly the same (ignore seconds), treat as 24 hours
  if (date1.getHours() === date2.getHours() && date1.getMinutes() === date2.getMinutes()) {
    return 24;
  }

  // Handle cross-midnight
  if (endTimestamp < startTimestamp) {
    endTimestamp += 24 * 60 * 60 * 1000;
  }

  const diffInMilliseconds = endTimestamp - startTimestamp;
  const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

  // Truncate to 2 decimal places to avoid 3.3 instead of 3.25
  return Math.floor(diffInHours * 100) / 100;
}

function getMondayDate(date: Date): Date {
  const givenDate = new Date(date);
  const day = givenDate.getDay();
  const diff = (day === 0 ? -6 : 1) - day;
  givenDate.setDate(givenDate.getDate() + diff);
  return givenDate;
}

function addOneHour(date: Date): Date {
  const newDate = new Date(date);
  newDate.setHours(newDate.getHours() + 1);
  return newDate;
}

function getJobName(jobType: number, subJobType = 0): { job: string; shift: string[] } {
  const mappings = {
    jobType: {
      1: { job: "Ad-hoc Childcare", shift: ["Morning", "Afternoon"] },
      2: { job: "Recurring Childcare", shift: ["Morning", "Afternoon"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["Before School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
      64: {
        job: "Primary School Tutoring",
        shift: ["Tutoring Shift", "Second Shift"],
      },
      128: {
        job: "High School Tutoring",
        shift: ["Tutoring Shift", "Second Shift"],
      },
      256: { job: "Odd Job", shift: ["Morning", "Afternoon"] },
    },
    subJobType: {
      1: { job: "Laundry", shift: ["Morning", "Afternoon"] },
      2: { job: "Errand Running", shift: ["Morning", "Afternoon"] },
      4: { job: "Outdoor Chores", shift: ["Morning", "Afternoon"] },
      8: { job: "Help for the Elderly", shift: ["Morning", "Afternoon"] },
      16: { job: "Other Odd Jobs", shift: ["Morning", "Afternoon"] },
    },
    recurringChildcare: {
      2: { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["After School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
    },
  };

  if (jobType === 2) {
    return subJobType === 0
      ? { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] }
      : mappings.recurringChildcare[jobType] || {
          job: "Recurring Childcare",
          shift: ["Morning", "Afternoon"],
        };
  }

  if (jobType === 256 && subJobType > 0) {
    return (
      mappings.subJobType[subJobType] || {
        job: "Odd Job",
        shift: ["Morning", "Afternoon"],
      }
    );
  }

  return (
    mappings.jobType[jobType] || {
      job: "Unknown Job",
      shift: ["Morning", "Afternoon"],
    }
  );
}

function formatTimeToAMPM(date: Date): string {
  const hours = date.getHours() % 12 || 12;
  const minutes = date.getMinutes();
  const ampm = date.getHours() >= 12 ? "pm" : "am";
  const minutesStr = minutes < 10 ? `0${minutes}` : minutes;
  return `${hours}:${minutesStr}${ampm}`;
}

function isNextDay(startTime: Date | string | null, endTime: Date | string | null): boolean {
  if (!startTime || !endTime) {
    return false;
  }

  const parseTime = (time: Date | string) => {
    if (typeof time === "string") {
      const [hours, minutes] = time.split(":").map(Number);
      return { hours, minutes };
    } else if (time instanceof Date) {
      return { hours: time.getHours(), minutes: time.getMinutes(), day: time.getDate() };
    }
    throw new Error("Invalid time format");
  };

  const start = parseTime(startTime);
  const end = parseTime(endTime);

  if (startTime instanceof Date && endTime instanceof Date) {
    return endTime.getDate() !== startTime.getDate();
  }

  // ✅ New logic for "next day" or same time
  if (
    end.hours < start.hours ||
    (end.hours === start.hours && end.minutes < start.minutes) ||
    (end.hours === start.hours && end.minutes === start.minutes)
  ) {
    return true;
  }

  return false;
}

function makeInitialTime(shift: "1st" | "2nd", timeType: "start" | "end", jobType?: number): Date {
  const now = new Date();

  let hours = 0;
  let minutes = 0;

  if (jobType === 8) {
    // After School Care
    if (timeType === "start") {
      hours = 15; // 3:00 PM
    } else if (timeType === "end") {
      hours = 18; // 6:00 PM
    }
  } else if (jobType === 4) {
    // Before School Care
    if (timeType === "start") {
      hours = 6; // 6:00 AM
    } else if (timeType === "end") {
      hours = 9; // 9:00 AM
    }
  } else if (jobType === 2) {
    // nannyin  Care
    if (shift === "1st") {
      if (timeType === "start") {
        hours = 6; // 6:00 AM
      } else if (timeType === "end") {
        hours = 18; // 6:00 pM
      }
    } else {
      if (timeType === "start") {
        hours = 15; // 6:00 AM
      } else if (timeType === "end") {
        hours = 18; // 6:00 pM
      }
    }
  } else if (jobType === 128 || jobType === 64) {
    //Tutoring
    if (shift === "1st") {
      if (timeType === "start") {
        hours = 16; // 6:00 pm
      } else if (timeType === "end") {
        hours = 17; // 7:00 pM
      }
    } else {
      if (timeType === "start") {
        hours = 19; // 7:00 AM
      } else if (timeType === "end") {
        hours = 20; // 8:00 pM
      }
    }
  } else {
    // Other job types
    if (shift === "2nd") {
      if (timeType === "start") {
        hours = 15; // 3:00 PM
      } else if (timeType === "end") {
        hours = 18; // 6:00 PM
      }
    } else {
      if (timeType === "start") {
        hours = 6; // 6:00 AM
      } else if (timeType === "end") {
        hours = 9; // 9:00 AM
      }
    }
  }

  now.setHours(hours, minutes, 0, 0);
  return now;
}

// Context

interface PostJobProps {
  currentPayload: Partial<PayloadTemplate>;
  nextClicked: ReturnType<typeof useJobManager>["next"];
  prevClicked: ReturnType<typeof useJobManager>["prev"];
  setPayload: ReturnType<typeof useJobManager>["setpayload"];
}

const defaultContextValue = {
  firstShiftFilled: false,
  daysEnabled: false,
  daysFilled: false,
  allowedTwoShifts: true,
  selectedDays: new Set<number>(),
  shifts: new Set<Shift>(),
  selectedDuration: "0",
  selectedDate: null,
  isValid: false,
  processState: (): Partial<PayloadTemplate> => ({}),
  setSelectedDate: (_: Date) => {},
  setSelectedDuration: (_: string) => {},
  postJobProps: (): PostJobProps => undefined,
  updateShift: (_: number, __: Shift) => {},
  toggleDay: (_: number) => {},
  autoFill: () => {},
  continueDBD: () => {},
  dateFlexible: null, // Add to default context
  setDateFlexible: (_: number | null) => {}, // Add setter
  // completionMethod: null,
  // setCompletionMethod: (_: number | null) => { },
};

const DASContext = createContext<typeof defaultContextValue>(defaultContextValue);

// Components
const TimePickerDialog = ({
  time,
  startTime,
  isVisible,
  onHide,
  shift,
  timeType,
  jobType,
}: {
  time: Date;
  startTime?: Date;
  isVisible: boolean;
  shift: "1st" | "2nd";
  timeType: "start" | "end";
  jobType?: number;
  onHide: (date?: Date) => void;
}) => {
  if (!isVisible) return null;
  const [internalTime, setInternalTime] = useState(time ?? startTime ?? makeInitialTime(shift, timeType, jobType));

  const handleTimeChange = (e: any) => {
    const selectedTime = e.value;
    setInternalTime(selectedTime);
  };

  const dialogContent = (
    <div
      className="fixed top-0 left-0 w-full h-full flex justify-content-center align-items-center"
      style={{ backgroundColor: "#404040E5", zIndex: 999 }}
    >
      <div className="flex flex-column gap-2" style={{ background: "white", padding: "1rem", borderRadius: "12px" }}>
        <Calendar
          panelClassName="border-none"
          value={internalTime}
          onChange={handleTimeChange}
          timeOnly
          hourFormat="12"
          stepMinute={15} // Add this line
          inline
          style={{ height: "auto", width: "min-content" }}
        />

        <div className="flex justify-content-between gap-2">
          <button
            className="flex-1 flex justify-content-center align-items-center"
            style={{
              all: "unset",
              padding: "10px",
              border: "1px solid #4E4E4E",
              borderRadius: "12px",
              fontWeight: "500",
              fontSize: "16px",
              color: "#CDCDCD",
            }}
            onClick={() => {
              if (!time) {
                onHide();
                return;
              }
              onHide(internalTime);
            }}
          >
            Cancel
          </button>
          <button
            className="flex-1 flex justify-content-center align-items-center"
            style={{
              all: "unset",
              border: "1px solid #FFA500",
              padding: "10px",
              borderRadius: "12px",
              backgroundColor: "#FFA500",
              fontWeight: "500",
              fontSize: "16px",
              color: "#FFFFFF",
              opacity: 1,
            }}
            onClick={() => {
              onHide(internalTime);
            }}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );

  return ReactDOM.createPortal(dialogContent, document.body);
};

const TimePicker = ({
  day,
  type,
  shift,
  jobType,
}: {
  day: number;
  type: "First" | "Second";
  index: number;
  shift: "First" | "Second";
  jobType?: number;
}) => {
  const [visible, setVisible] = useState(false);
  const [time, setTime] = useState<Date | null>(null);
  const context = useContext(DASContext);
  const timeName = getJobName(context.postJobProps().currentPayload.jobType, context.postJobProps().currentPayload.jobSubType);

  const currentShift = [...context.shifts].find((s) => s.day === day);

  useEffect(() => {
    if (!currentShift || time) return;
    const isFirstShift = shift === "First";
    const targetShift = isFirstShift ? currentShift.firstShift : currentShift.secondShift;

    if (type === "First") {
      setTime(targetShift.startTime);
    } else {
      setTime(targetShift.endTime);
    }
  }, [currentShift]);

  useEffect(() => {
    if (!time || !currentShift) return;

    const isFirstShift = shift === "First";
    const targetShift = isFirstShift ? currentShift.firstShift : currentShift.secondShift;

    if (type === "Second" && targetShift.startTime === null) {
      context.updateShift(day, {
        ...currentShift,
        [isFirstShift ? "firstShift" : "secondShift"]: {
          ...targetShift,
          error: [...targetShift.error, ShiftEntryErrors.startTimeNoFilled],
        },
      });
      setTime(null);
      return;
    }
    context.updateShift(day, {
      ...currentShift,
      firstShift:
        shift === "Second"
          ? currentShift.firstShift
          : {
              ...currentShift.firstShift,
              startTime: type === "Second" ? currentShift.firstShift.startTime : time,
              endTime: type === "First" ? currentShift.firstShift.endTime : time,
              error: [ShiftEntryErrors.noErrors],
              endStartTime: null,
            },
      secondShift:
        shift === "First"
          ? currentShift.secondShift
          : {
              ...currentShift.secondShift,
              startTime: type === "Second" ? currentShift.secondShift.startTime : time,
              endTime: type === "First" ? currentShift.secondShift.endTime : time,
              error: [ShiftEntryErrors.noErrors],
              endStartTime: null,
            },
    });
  }, [time]);

  const getBorder = () => {
    const targetShift = shift === "First" ? currentShift.firstShift : currentShift.secondShift;

    if (!targetShift.startTime && !targetShift.endTime) {
      return type === "First" ? styles.e1iae2ia.e1 : styles.e1iae2ia.e2;
    }

    if (targetShift.startTime && !targetShift.endTime) {
      return type === "First" ? styles.e1ae2ia.e1 : styles.e1ae2ia.e2;
    }

    if (targetShift.startTime && targetShift.endTime) {
      return type === "First" ? styles.e1ae2a.e1 : styles.e1ae2a.e2;
    }
    return type === "First" ? styles.e1iae2ia.e1 : styles.e1iae2ia.e2;
  };

  const getBorder2 = () => {
    // (shift === 'First'
    //     ? currentShift.firstShift
    //     : currentShift.secondShift
    // ).error.some((e) => e === ShiftEntryErrors.startTimeNoFilled) &&
    // type === 'First'
    //     ? '1px solid #FF6359'
    //     : '1px solid #DFDFDF',

    const hasError = (shift === "First" ? currentShift.firstShift : currentShift.secondShift).error.some(
      (e) => e === ShiftEntryErrors.startTimeNoFilled
    );

    if (hasError || (context.daysEnabled && !time)) {
      return "1px solid #FF6359"; // Red border
    }

    return "1px solid #DFDFDF"; // Default border
  };

  return (
    <div className="flex flex-column relative">
      <TimePickerDialog
        time={time}
        startTime={type === "First" ? null : (shift === "First" ? currentShift.firstShift : currentShift.secondShift).endStartTime}
        isVisible={visible}
        onHide={(selectedTime) => {
          setTime(selectedTime);
          setVisible(false);
        }}
        shift={shift === "First" ? "1st" : "2nd"}
        timeType={type === "First" ? "start" : "end"}
        jobType={jobType}
      />
      <div
        className="relative flex justify-content-around"
        style={{
          border: "none",
          height: type === "Second" ? "67px" : "62px",
          paddingTop: type === "Second" && "10px",
          top: type === "Second" && "-10px",
          ...getBorder(),
          borderRadius: type === "First" ? "10px" : "0 0 10px 10px",
        }}
      >
        <div className="flex flex-column mx-2 my-1 flex-1">
          <p className="m-0 p-0 font-semibold text-base text-gray-600">{timeName.shift[shift === "First" ? 0 : 1]}</p>
          <p className="m-0 p-0 text-sm text-gray-600">{type === "First" ? "Start time" : "End time"}</p>
        </div>
        <div className="mx-2 my-1 flex justify-content-between align-items-center flex-1">
          <div
            className="flex align-items-center justify-content-around px-2 cursor-pointer"
            style={{
              // border:
              //     (shift === 'First'
              //         ? currentShift.firstShift
              //         : currentShift.secondShift
              //     ).error.some((e) => e === ShiftEntryErrors.startTimeNoFilled) &&
              //     type === 'First'
              //         ? '1px solid #FF6359'
              //         : '1px solid #DFDFDF',
              border: getBorder2(),
              borderRadius: "20px",
              width: "139px",
              height: "38px",
            }}
            onClick={() => setVisible(true)}
          >
            <img src={type === "First" ? ClockStart : ClockEnd} alt="clock" width="13.5px" height="13.5px" />
            <p className="m-0 p-0 text-sm text-gray-600">{time ? formatTimeToAMPM(time) : `${type === "First" ? "Start" : "End"} time`}</p>
            <IoChevronDown color="#585858" />
          </div>
        </div>
      </div>
    </div>
  );
};

const DaysManager = () => {
  const context = useContext(DASContext);
  const daysToShow = DAYS_MAP.slice(
    [4, 8, 12].includes(context.postJobProps().currentPayload.jobType) ? 1 : 0,
    [4, 8, 12].includes(context.postJobProps().currentPayload.jobType) ? 6 : 7
  );

  return (
    <div className="w-full h-min flex gap-2 flex-wrap justify-content-around mt-1">
      {daysToShow.map((day, index) => {
        const isSelected = context.selectedDays.has(day.day);
        return (
          <div
            key={index}
            className="flex justify-content-center align-items-center cursor-pointer"
            style={{
              backgroundColor: isSelected ? "#179D5233" : "transparent",
              width: "103px",
              height: "44px",
              border: isSelected ? ACTIVE_BORDER : "2px solid #DFDFDF",
              borderRadius: "20px",
            }}
            onClick={() => context.toggleDay(day.day)}
          >
            <p
              className="m-0 p-0"
              style={{
                fontWeight: isSelected ? "700" : "400",
                fontSize: isSelected ? "18px" : "16px",
                color: isSelected ? "#179D52" : "#585858",
              }}
            >
              {day.short}
            </p>
          </div>
        );
      })}
    </div>
  );
};

const ShiftEntryComponent = ({
  dayInNumber,
  index,
  shift,
  onCloseClick,
  jobType,
}: {
  dayInNumber: number;
  index: number;
  shift: "First" | "Second";
  onCloseClick: () => void;
  jobType?: number;
}) => {
  const context = useContext(DASContext);

  const currentShift = [...context.shifts].find((s) => s.day === dayInNumber);
  if (!currentShift || (shift === "First" && !currentShift.firstShift) || (shift === "Second" && !currentShift.secondShift)) {
    return null;
  }

  const targetShift = shift === "First" ? currentShift?.firstShift : currentShift?.secondShift;
  if (!currentShift || !targetShift) {
    return null; // or render some fallback UI
  }

  return (
    <div className="w-full flex flex-column relative">
      {index === 0 && (shift === "First" || (shift === "Second" && !currentShift.firstShift)) && (
        <div
          className="absolute flex justify-content-center align-items-center"
          style={{
            right: "25px",
            top: "25px",
            width: "23px",
            height: "23px",
            backgroundColor: "#DFDFDF",
            borderRadius: "999px",
          }}
          onClick={onCloseClick}
        >
          <IoClose color="#585858" />
        </div>
      )}

      {shift === "First" || (shift === "Second" && !currentShift.firstShift) ? (
        <div
          className="px-3 py-1 w-min h-min absolute"
          style={{
            top: index !== 0 && shift === "First" ? "-18px" : undefined,
            left: "20px",
            backgroundColor: "#179D52",
            borderRadius: "20px",
          }}
        >
          <p className="m-0 p-0 font-bold text-lg text-white">{DAYS_FULL[dayInNumber]}</p>
        </div>
      ) : null}

      <div
        className={`bg-white ${index === 0 && (shift === "First" || (shift === "Second" && !currentShift.firstShift)) ? "mt-3" : ""} 
                             px-3 ${shift === "First" || (shift === "Second" && !currentShift.firstShift) ? "pt-4 pb-1" : "py-4"} flex flex-column`}
        style={{
          minHeight: "100px",
          borderRadius: index === 0 && (shift === "First" || (shift === "Second" && !currentShift.firstShift)) ? "30px 30px 0 0" : undefined,
        }}
      >
        {index === 0 && (shift === "First" || (shift === "Second" && !currentShift.firstShift)) && (
          <p
            className="m-0 p-0 mb-2 font-semibold text-xl text-gray-600"
            style={{
              width: "70%",
            }}
          >
            Complete your weekly schedule
          </p>
        )}

        <TimePicker day={dayInNumber} type="First" index={index} shift={shift} jobType={jobType} />
        <TimePicker day={dayInNumber} type="Second" index={index} shift={shift} jobType={jobType} />

        {isNextDay(
          shift === "First" ? currentShift.firstShift.startTime : currentShift.secondShift.startTime,
          shift === "First" ? currentShift.firstShift.endTime : currentShift.secondShift.endTime
        ) && (
          <p
            className="m-0 p-0 text-center"
            style={{
              fontSize: "12px",
            }}
          >
            on the following day
          </p>
        )}

        <div className="flex align-content-center justify-content-end">
          {shift === "First" && context.allowedTwoShifts && (
            <p
              className="m-0 p-0 mr-auto font-semibold text-xs text-red-500 underline"
              onClick={(e) => {
                e.preventDefault();
                context.updateShift(dayInNumber, {
                  ...currentShift,
                  firstShift: null, // Remove first shift
                  hasSecondShift: currentShift.hasSecondShift, // Preserve hasSecondShift
                });
              }}
            >
              Remove Shift
            </p>
          )}
          {shift === "Second" && currentShift.hasSecondShift && (
            <p
              className="m-0 p-0 mr-auto font-semibold text-xs text-red-500 underline"
              onClick={(e) => {
                e.preventDefault();
                context.updateShift(dayInNumber, {
                  ...currentShift,
                  secondShift: null, // Remove second shift
                  hasSecondShift: false, // Disable second shift
                });
              }}
            >
              Remove Shift
            </p>
          )}
          <div
            className="px-3 py-1"
            style={{
              borderRadius: "20px",
              backgroundColor: calculateHoursDifference(targetShift.startTime, targetShift.endTime) > 0 ? "#179D52" : "#DFDFDF",
            }}
          >
            <p className="m-0 p-0 font-bold text-xs text-white">{calculateHoursDifference(targetShift.startTime, targetShift.endTime)} Total Hours</p>
          </div>
        </div>
        {context.selectedDays.size !== index + 1 && context.daysEnabled && <Divider className="mt-3" />}
      </div>
    </div>
  );
};

const AddShiftEntryComponent = ({ day }: { day: number }) => {
  const context = useContext(DASContext);
  const currentShift = [...context.shifts].find((s) => s.day === day);

  const showFirstShiftOption = !currentShift || !currentShift.firstShift;
  const showSecondShiftOption = currentShift?.firstShift && !currentShift.hasSecondShift && context.allowedTwoShifts;

  if (!showFirstShiftOption && !showSecondShiftOption) {
    return null; // Don't render anything if neither option is available
  }
  return (
    <div className="bg-white p-2 flex flex-column">
      <Divider />
      <div
        className="flex gap-2 justify-content-center my-4 align-items-center mx-auto"
        style={{
          border: "2px solid #585858",
          borderRadius: "20px",
          width: "90%",
          maxWidth: "347px",
          height: "44px",
        }}
        onClick={(e) => {
          e.preventDefault();
          if (showFirstShiftOption) {
            // Add first shift, preserve existing second shift
            context.updateShift(day, {
              ...currentShift,
              day, // Ensure day is set for new shift
              firstShift: {
                startTime: null,
                endTime: null,
                endStartTime: null,
                error: [ShiftEntryErrors.noErrors],
              },
              // Preserve existing hasSecondShift and secondShift
              hasSecondShift: currentShift?.hasSecondShift || false,
              secondShift: currentShift?.secondShift || null,
              shiftType: 1, // Set shiftType to 1 for first shift
            });
          } else if (showSecondShiftOption) {
            // Add second shift (unchanged)
            context.updateShift(day, {
              ...currentShift,
              hasSecondShift: true,
              secondShift: {
                startTime: null,
                endTime: null,
                endStartTime: null,
                error: [ShiftEntryErrors.noErrors],
              },
              shiftType: 2, // Set shiftType to 2 for second shift
            });
          }
        }}
      >
        <img src={AddShiftImg} alt="add" width="12px" height="13.5px" />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "14px",
            color: "#585858",
          }}
        >
          {showFirstShiftOption ? "Add First Shift" : "Add Second Shift"}
        </p>
      </div>
    </div>
  );
};

const ShiftManager = () => {
  const [visible, setVisible] = useState(false);
  const context = useContext(DASContext);
  const allShiftsFilled = useMemo(() => {
    return [...context.shifts].every((shift) => {
      // Check if first shift exists and has both start and end times
      const firstShiftValid = !shift.firstShift || (shift.firstShift.startTime && shift.firstShift.endTime);

      // Check if second shift exists (if hasSecondShift is true) and has both start and end times
      const secondShiftValid = !shift.hasSecondShift || !shift.secondShift || (shift.secondShift.startTime && shift.secondShift.endTime);

      return firstShiftValid && secondShiftValid;
    });
  }, [context.shifts]);
  useEffect(() => {
    if (context.selectedDays.size === 0) {
      setVisible(false);
    }
  }, [context.selectedDays]);
  return (
    <>
      <Dialog
        maskClassName="z-1 "
        visible={visible}
        onHide={() => setVisible(false)}
        appendTo={document.getElementById("DASMMainSection")}
        style={{
          maxWidth: "none",
          width: "100%",
        }}
        content={
          <div
            style={{
              maxHeight: "600px",
              overflowY: "auto",
              overflowX: "hidden",
              position: "fixed",
              bottom: "0",
              left: "0",
              width: "100%",
              // marginBottom: "70px",
            }}
            className="mt-auto z-5"
          >
            <div
              className="fixed top-0 left-0 w-full h-full z-5"
              style={{
                backgroundColor: "#404040E5",
                zIndex: "2",
              }}
            />
            <div
              className="w-full mt-auto z-3 overflow-y-auto relative custom_slide_up flex flex-column justify-content-end hide_scrollbar z-5"
              style={{ height: "100%", maxHeight: "100%", marginBottom: "75px" }}
            >
              {Array.from(context.shifts)
                .sort((a, b) => a.day - b.day)
                .slice(0, context.daysEnabled ? context.selectedDays.size : 1)
                .map((val, index) => (
                  <div
                    key={index}
                    className="w-full flex flex-column z-5"
                    style={{
                      borderRadius: index === 0 ? "30px 30px 0 0" : undefined,
                    }}
                  >
                    {val.firstShift && (
                      <ShiftEntryComponent
                        dayInNumber={val.day}
                        index={index}
                        shift="First"
                        onCloseClick={() => setVisible(false)}
                        jobType={context.postJobProps().currentPayload.jobType}
                      />
                    )}
                    {/* Render Second Shift if it exists */}
                    {val.hasSecondShift && val.secondShift && (
                      <ShiftEntryComponent
                        dayInNumber={val.day}
                        index={index}
                        shift="Second"
                        onCloseClick={() => setVisible(false)}
                        jobType={context.postJobProps().currentPayload.jobType}
                      />
                    )}
                    {/* Render AddShiftEntryComponent for adding first or second shift */}
                    <AddShiftEntryComponent day={val.day} />

                    {context.firstShiftFilled && !context.daysEnabled && (
                      <div className="flex flex-column bg-white px-3 gap-2 py-2">
                        <button
                          className="flex-1 flex justify-content-center align-items-center"
                          style={{
                            all: "unset",
                            border: "1px solid #FFA500",
                            padding: "10px",
                            borderRadius: "5px",
                            backgroundColor: "#FFA500",
                            fontWeight: "700",
                            fontSize: "16px",
                            color: "#FFFFFF",
                          }}
                          onClick={context.autoFill}
                        >
                          Auto-fill weekly schedule
                        </button>
                        <button
                          className="flex-1 flex justify-content-center align-items-center"
                          style={{
                            all: "unset",
                            border: "1px solid #4E4E4E",
                            padding: "10px",
                            borderRadius: "5px",
                            fontWeight: "700",
                            fontSize: "16px",
                            color: "#CDCDCD",
                          }}
                          onClick={context.continueDBD}
                        >
                          Complete day-by-day
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              <div
                style={{
                  position: "fixed",
                  bottom: "0",
                  left: "0",
                  width: "100%",
                  zIndex: "5",
                  backgroundColor: "white",
                }}
                className="flex justify-content-center p-3"
              >
                <button
                  className="flex justify-content-center align-items-center"
                  style={{
                    all: "unset",
                    border: "none",
                    padding: "10px",
                    borderRadius: "10px",
                    backgroundColor: allShiftsFilled ? "#FFA500" : "#CCCCCC",
                    fontWeight: "700",
                    fontSize: "16px",
                    color: "#FFFFFF",
                    width: "80%",
                  }}
                  disabled={!allShiftsFilled}
                  onClick={() => setVisible(false)}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        }
      />
      <div
        className={`w-full mt-2 flex gap-2 justify-content-center align-items-center transition-all transition-duration-300 transition-ease-in-out ${
          context.selectedDays.size > 0 ? "cursor-pointer" : ""
        }`}
        style={{
          minHeight: "45px",
          backgroundColor: context.daysFilled ? "#FFFFFF" : context.selectedDays.size > 0 ? "#FFA500" : "#EDE9E9",
          borderRadius: "20px",
          border: context.daysFilled && "2px solid #585858",
        }}
        onClick={() => {
          if (context.selectedDays.size > 0) {
            setVisible(true);
          }
        }}
      >
        {context.daysFilled && <img src={PencilBlack} alt="pencil" width="15px" height="15px" />}
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "16px",
            color: context.daysFilled ? "#585858" : "#FFFFFF",
          }}
        >
          {context.daysFilled ? "Edit Shifts" : "Input Shift"}
        </p>
      </div>
    </>
  );
};

const DurationSelector = () => {
  const context = useContext(DASContext);
  return (
    <div
      className="flex flex-column mt-3"
      style={{
        zIndex: "0",
      }}
    >
      <Divider />
      <p
        className="m-0 p-0 mt-3"
        style={{
          fontWeight: "700",
          fontSize: "22px",
          color: "#585858",
        }}
      >
        How many weeks is this job expected to run for?
      </p>
      <div className="flex gap-2 my-2 align-items-center">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: "#585858",
          }}
        >
          Expected Job Duration
        </p>
        <CustomDropdown
          options={Array.from({ length: 52 }, (_, i) => String(i + 1))}
          selectedOption={context.selectedDuration?.toString() || ""}
          onSelect={(e) => context.setSelectedDuration(e)}
        />
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "400",
            fontSize: "16px",
            color: "#585858",
          }}
        >
          Weeks
        </p>
      </div>
      {/* {context.completionMethod !== 0 && (
        <>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "22px",
              color: "#585858",
            }}
          >
            How would you like to complete your roster?
          </p>
          <div className="flex gap-2 mx-2 align-items-center">
            <CustomDropdownRoasterMobile
              options={["Auto-fill Weeks", "Week-by-week"]}
              selectedOption={context.completionMethod === 1 ? "Auto-fill Weeks" : "Week-by-week"}
              onSelect={(option) => {
                context.setCompletionMethod(option === "Auto-fill Weeks" ? 1 : 2);
              }}
            />
          </div>
        </>
      )} */}
    </div>
  );
};

const DateSelector = () => {
  const context = useContext(DASContext);

  const [commencingDate, setCommencingDate] = useState<Date>(context.selectedDate ? getMondayDate(context.selectedDate) : null);
  const [visible, setVisible] = useState<boolean>(false);

  function formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      day: "2-digit",
      month: "short",
      year: "numeric",
    };

    return new Intl.DateTimeFormat("en-GB", options).format(date);
  }

  return (
    <div className="flex flex-column gap-2 pb-4">
      <Divider className="mt-2" />
      <p
        className="m-0 p-0 "
        style={{
          fontWeight: "700",
          fontSize: "22px",
          color: "#585858",
        }}
      >
        When do you want your weekly schedule to start?
      </p>
      <div className="mx-2 mt-3">
        <div
          className="flex gap-2 justify-content-center align-items-center"
          style={{
            border: commencingDate && "2px solid #179D52",
            backgroundColor: commencingDate ? "#FFFFFF" : "#FFA500",
            boxShadow: !commencingDate && "0 4px 4px 0 #00000040",
            height: "46px",
            borderRadius: "10px",
          }}
          onClick={(e) => {
            e.preventDefault();
            setVisible(true);
          }}
        >
          {!commencingDate && <img src={CalenderWhite} alt="calender" width="12px" height="13.5px" />}
          <p
            className="m-0 p-0"
            style={{
              fontWeight: commencingDate ? "700" : "600",
              fontSize: "16px",
              color: commencingDate ? "#179D52" : "#FFFFFF",
            }}
          >
            {commencingDate ? `Week commencing ${formatDate(commencingDate)}` : "Select Start Date"}
          </p>
        </div>
      </div>
      <Dialog
        visible={visible}
        onHide={() => setVisible(false)}
        style={{
          width: "100vw",
          height: "100vh",
          maxWidth: "none",
          maxHeight: "none",
        }}
        content={
          <div className="m-auto flex flex-column gap-3">
            <div
              className="relative"
              style={{
                textWrap: "nowrap",
              }}
            >
              <h1
                className="m-0 p-0 mx-3"
                style={{
                  fontWeight: "700",
                  fontSize: "22px",
                  color: "#FFFFFF",
                  textWrap: "wrap",
                }}
              >
                When do you want your weekly schedule to start?
              </h1>
              <div
                className="absolute flex justify-content-center align-items-center cursor-pointer"
                style={{
                  top: "-40px",
                  right: "20px",
                  width: "30px",
                  height: "32px",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  setVisible(false);
                }}
              >
                <IoMdClose />
              </div>
            </div>
            <Calendar
              className="h-min mx-auto"
              panelClassName="w-full"
              value={new Date()}
              onChange={(e) => {
                context.setSelectedDate(e.value);
                // onChange(e.value);
                setCommencingDate(getMondayDate(e.value));
                setVisible(false);
              }}
              minDate={new Date()}
              inline
            />
          </div>
        }
      />
    </div>
  );
};
// const RosterSelector = () => {
//   const context = useContext(DASContext);

//   return (
//     <div className="flex flex-column gap-2">

//       {context.completionMethod === 0 && (
//         <>
//           <p
//             className="m-0 p-0"
//             style={{
//               fontWeight: "700",
//               fontSize: "22px",
//               color: "#585858",
//             }}
//           >
//             How would you like to complete your roster?
//           </p>
//           <div className="flex gap-2 mx-2 flex-column">
//             <button
//               className="flex-1 flex justify-content-center align-items-center"
//               style={{
//                 all: "unset",
//                 border: "none",
//                 padding: "10px",
//                 borderRadius: "5px",
//                 backgroundColor: "#FFA500",
//                 fontWeight: "700",
//                 fontSize: "16px",
//                 color: "#FFFFFF",
//               }}
//               onClick={() => context.setCompletionMethod(1)}
//             >
//               Auto-fill weeks
//             </button>
//             <button
//               className="flex-1 flex justify-content-center align-items-center"
//               style={{
//                 all: "unset",
//                 border: "1px solid #585858",
//                 padding: "10px",
//                 borderRadius: "5px",
//                 backgroundColor: "#FFFFFF",
//                 fontWeight: "700",
//                 fontSize: "16px",
//                 color: "#585858",
//               }}
//               onClick={() => context.setCompletionMethod(2)}
//             >
//               Week-by-week
//             </button>

//             <p style={{ fontWeight: "300", color: "#585858", fontSize: "14px", textAlign: "center" }}>Select option to continue</p>
//           </div>
//         </>
//       )}
//     </div>
//   );
// };
const FlexibleSelector = () => {
  const context = useContext(DASContext);

  return (
    <div className="flex flex-column gap-2">
      <Divider className="mt-2" />
      <p
        className="m-0 p-0"
        style={{
          fontWeight: "700",
          fontSize: "22px",
          color: "#585858",
        }}
      >
        Is this start date flexible?
      </p>
      <div className="flex gap-2 align-items-center">
        {["Yes", "No"].map((v, i) => (
          <div
            key={i}
            className="flex gap-2 justify-content-center align-items-center cursor-pointer"
            style={{
              border: context.dateFlexible === i ? "3px solid #179D52" : "1px solid #DFDFDF",
              borderRadius: "10px",
              width: "91px",
              height: "52px",
            }}
            onClick={(e) => {
              e.preventDefault();
              context.setDateFlexible(i);
            }}
          >
            <RadioButton selected={context.dateFlexible === i} />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: context.dateFlexible === i ? "700" : "500",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              {v}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};
// const ConfirmationDialog = ({ visible, onHide, onConfirm }: { visible: boolean; onHide: () => void; onConfirm: () => void }) => {
//   const context = useContext(DASContext);

//   const getShiftInfo = () => {
//     const sortedShifts = [...context.shifts].sort((a, b) => a.day - b.day);
//     return sortedShifts.map(shift => {
//       const dayName = DAYS_SHORT[shift.day];
//       const firstShiftHours = shift.firstShift?.startTime && shift.firstShift?.endTime
//         ? calculateHoursDifference(shift.firstShift.startTime, shift.firstShift.endTime)
//         : 0;

//       const secondShiftHours = shift.hasSecondShift && shift.secondShift?.startTime && shift.secondShift?.endTime
//         ? calculateHoursDifference(shift.secondShift.startTime, shift.secondShift.endTime)
//         : 0;

//       return {
//         dayName,
//         firstShift: {
//           start: shift.firstShift?.startTime ? formatTimeToAMPM(shift.firstShift.startTime) : 'Not set',
//           end: shift.firstShift?.endTime ? formatTimeToAMPM(shift.firstShift.endTime) : 'Not set',
//           hours: firstShiftHours
//         },
//         secondShift: shift.hasSecondShift ? {
//           start: shift.secondShift?.startTime ? formatTimeToAMPM(shift.secondShift.startTime) : 'Not set',
//           end: shift.secondShift?.endTime ? formatTimeToAMPM(shift.secondShift.endTime) : 'Not set',
//           hours: secondShiftHours
//         } : null
//       };
//     });
//   };

//   const shiftInfo = getShiftInfo();
//   const totalWeeklyHours = shiftInfo.reduce((sum, day) => sum + (day.firstShift.hours + (day.secondShift?.hours || 0)), 0);
//   const totalJobHours = totalWeeklyHours * parseInt(context.selectedDuration || '0');

//   return (
//     <Dialog
//       visible={visible}
//       onHide={onHide}
//       style={{
//         position: "fixed",
//         bottom: "0",
//         width: "100%",

//         margin: "0 auto",
//         borderTopLeftRadius: "20px",
//         borderTopRightRadius: "20px",
//         backgroundColor: "#FFFFFF",
//         padding: "16px",
//         maxHeight: "70vh",
//         overflowY: "auto"
//       }}
//       header={
//         <div className="text-lg font-bold p-0">Your Roster</div>
//       }
//     >
//       <div className="flex flex-column gap-2">
//         {/* Header Info */}
//         <div className="flex justify-content-between text-sm">
//           <span>Tues 1/1/24</span>
//           <span>{context.selectedDuration || '0'} week duration</span>
//         </div>

//         <div className="text-sm">9 Christie St. South Brisbane</div>
//         <div className="text-sm mb-3">Flexible start date: Yes</div>

//         <Divider className="my-1" />

//         <div className="font-bold text-sm mb-2">Weekly roster</div>

//         {/* Table-like Structure */}
//         <table className="w-full">
//           <tbody>
//             {shiftInfo.map((day, index) => (
//               <>
//                 {/* First Shift Row */}
//                 <tr key={`${index}-first`}>
//                   <td className="font-medium text-sm py-1" style={{ width: '7%' }}>{day.dayName}:</td>
//                   <td className="text-sm py-1" style={{ width: '60%', fontWeight: "700", color: "#179D52" }}>
//                     {day.firstShift.start} - {day.firstShift.end}
//                   </td>
//                   <td className="text-sm py-1 text-center" style={{ width: '63px', height: "23px", backgroundColor: "#179D52", color: "#fff", borderRadius: "10px", fontWeight: "700" }}>
//                     {day.firstShift.hours} hrs
//                   </td>
//                 </tr>

//                 {/* Second Shift Row if exists */}
//                 {day.secondShift && (
//                   <tr key={`${index}-second`}>
//                     <td className="font-medium text-sm py-1">{day.dayName}:</td>
//                     <td className="text-sm py-1">
//                       {day.secondShift.start} - {day.secondShift.end}
//                     </td>
//                     <td className="text-sm py-1 text-right">
//                       {day.secondShift.hours} hrs
//                     </td>
//                   </tr>
//                 )}
//               </>
//             ))}
//           </tbody>
//         </table>

//         <Divider className="my-1" />

//         {/* Totals */}
//         <table className="w-full">
//           <tbody>
//             <tr>
//               <td className="font-bold text-sm py-1">Total Weekly Hours:</td>
//               <td className="font-bold text-sm py-1 text-right">{totalWeeklyHours} hrs</td>
//             </tr>
//             {/* <tr>
//               <td className="font-bold text-sm py-1">Total Job Hours:</td>
//               <td className="font-bold text-sm py-1 text-right">{totalJobHours} hrs</td>
//             </tr> */}
//           </tbody>
//         </table>

//         {/* Buttons */}
//         <div className="flex justify-content-between gap-2 mt-3 flex-column">

//           <button
//             style={
//               {
//                 backgroundColor: "#FFA500",
//                 color: "#FFFFFF",
//                 fontWeight: "700",
//                 fontSize: "16px",
//                 border: "None",
//                 padding: "10px",
//                 borderRadius: "10px",
//               }
//             }
//             onClick={onConfirm}
//           >
//             Confirm Roster
//           </button>

//           <button
//             style={
//               {
//                 backgroundColor: "#fff",
//                 color: "#585858",
//                 fontWeight: "600",
//                 fontSize: "16px",
//                 border: "None",
//                 padding: "10px",
//                 borderRadius: "10px",
//                 textDecoration: "underline",
//               }
//             }
//             onClick={onHide}
//           >
//             Edit Roster
//           </button>
//         </div>
//       </div>
//     </Dialog>
//   );
// };
const DASMMainSection = () => {
  const context = useContext(DASContext);
  return (
    <div id="DASMMainSection" className="flex-grow-1 flex flex-column gap-1 overflow-y-auto p-3 relative mb-0">
      <h1 className="m-0 p-0 font-bold text-2xl text-gray-600">Select the days required</h1>
      <p className="m-0 p-0 font-light text-sm">Candidates will respond with their availability for each day selected</p>
      <DaysManager />
      <ShiftManager />
      {context.daysFilled && <DurationSelector />}
      {context.daysFilled && Number(context.selectedDuration) > 0 && <DateSelector />}
      {/* {context.daysFilled && Number(context.selectedDuration) > 0 && (
        <RosterSelector />
      )} */}
      {context.daysFilled && Number(context.selectedDuration) > 0 && context.selectedDate && <FlexibleSelector />}
    </div>
  );
};

const DASMLayout = () => {
  const context = useContext(DASContext);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleNextClick = () => {
    setShowConfirmation(true);
  };

  const handleConfirm = () => {
    context.postJobProps().setPayload({
      ...context.postJobProps().currentPayload,
      ...context.processState(),
    });
    context.postJobProps().nextClicked("pricing-payments-step1");
    setShowConfirmation(false);
  };
  return (
    <div className="w-full h-full flex flex-column overflow-hidden">
      <DASMMainSection />
      <BackButtonPortal id="back-button-portal">
        <div
          style={{
            top: "32px",
            left: "35px",
          }}
        >
          <img
            src={SideArrow}
            alt="cross"
            width={13}
            height={20}
            className="cursor-pointer"
            onClick={() => {
              context.postJobProps().setPayload({
                ...context.postJobProps().currentPayload,
                ...context.processState(),
              });

              if ([64, 128].includes(context.postJobProps().currentPayload.jobType)) {
                context.postJobProps().prevClicked("typeand-years");
              } else {
                context.postJobProps().prevClicked("job-posting");
              }
            }}
          />
        </div>
      </BackButtonPortal>
      {/* <ConfirmationDialog
        visible={showConfirmation}
        onHide={() => setShowConfirmation(false)}
        onConfirm={handleConfirm}
      />

      <CustomFooterButton
        label="Next"
        onClick={handleNextClick}
        isDisabled={!context.isValid}
        withoutZIndex
      /> */}

      <CustomFooterButton
        label="Next"
        onClick={() => {
          context.postJobProps().setPayload({
            ...context.postJobProps().currentPayload,
            ...context.processState(),
          });

          context.postJobProps().nextClicked("pricing-payments-step1");
        }}
        isDisabled={!context.isValid}
        withoutZIndex
      />
    </div>
  );
};

// Main Component
const DaysAndScheduleMobile: React.FC<PostJobProps> = (props) => {
  const [selectedDays, setSelectedDays] = useState<Set<number>>(new Set());
  const [shifts, setShifts] = useState<Set<Shift>>(new Set());
  const [firstShiftFilled, setFirstShiftFilled] = useState(false);
  const [daysEnabled, setDaysEnabled] = useState(false);
  const [daysFilled, setDaysFilled] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState("0");
  const [selectedDate, setSelectedDate] = useState<Date>(null);
  const [allowedTwoShifts, setAllowedTwoShifts] = useState<boolean>(true);
  const [dateFlexible, setDateFlexible] = useState<number | null>(null); // Add state for dateFlexible
  // const [completionMethod, setCompletionMethod] = useState<number | null>(
  //   props.currentPayload.jobRosterType !== undefined ? props.currentPayload.jobRosterType : 0
  // );
  const isShiftFilled = (shift?: Shift) => {
    if (!shift) return false;
    return (shift.firstShift ? shiftEntryFilled(shift.firstShift) : true) && (shift.hasSecondShift ? shiftEntryFilled(shift.secondShift) : true);
  };

  const checkAllShiftsFilled = () => {
    const result = selectedDays.size <= 0 ? false : [...shifts].every((s) => isShiftFilled(s));
    setDaysFilled(result);
  };

  const isFormValid = () => {
    return selectedDays.size > 0 && shifts.size > 0 && selectedDays.size <= 0
      ? false
      : [...shifts].every((s) => isShiftFilled(s)) && Number(selectedDuration) > 0 && selectedDate !== null && dateFlexible !== null;
  };

  const isValid = useMemo(() => {
    return isFormValid();
  }, [selectedDays, shifts, selectedDuration, selectedDate, dateFlexible]);

  // const reloadState = () => {
  //   const pSelectedDays = props.currentPayload?.daysOfWeek?.filter((d) => d.isRequired === true)?.map((d) => d.dayOfWeek) || [];
  //   const pCompletionMethod = props.currentPayload.jobRosterType !== undefined ? props.currentPayload.jobRosterType : 0;
  //   setCompletionMethod(pCompletionMethod);
  //   const pShifts: Shift[] =
  //     props.currentPayload?.weeklySchedule?.weeklyScheduleEntries?.map((e: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]) => ({
  //       day: e.dayOfWeek,
  //       firstShift:
  //         e.shifts?.length > 0
  //           ? {
  //             startTime: convertTimeToDate(e.shifts[0]?.jobStartTime) as Date,
  //             endStartTime: addOneHour(convertTimeToDate(e.shifts[0]?.jobStartTime) as Date),
  //             endTime: convertTimeToDate(e.shifts[0]?.jobEndTime) as Date,
  //             error: [ShiftEntryErrors.noErrors],
  //           }
  //           : null,
  //       hasSecondShift: e.shifts?.length > 1,
  //       secondShift:
  //         e.shifts?.length > 1
  //           ? {
  //             startTime: convertTimeToDate(e.shifts[1]?.jobStartTime) as Date,
  //             endStartTime: addOneHour(convertTimeToDate(e.shifts[1]?.jobStartTime) as Date),
  //             endTime: convertTimeToDate(e.shifts[1]?.jobEndTime) as Date,
  //             error: [ShiftEntryErrors.noErrors],
  //           }
  //           : null,
  //     })) || [];

  //   const pDuration = props.currentPayload?.duration ?? null;
  //   const pDateFlexible = props.currentPayload?.isJobFlexible !== undefined ? (props.currentPayload.isJobFlexible ? 0 : 1) : null; // Map true to 0 (Yes), false to 1 (No)
  //   // Fix: Always initialize from props, not just when jobDate is null
  //   const pSelectedDate = props.currentPayload?.jobDate ? new Date(props.currentPayload.jobDate) : null;
  //   setSelectedDate(pSelectedDate);

  //   setSelectedDays(new Set(pSelectedDays));
  //   setShifts(new Set(pShifts));
  //   setFirstShiftFilled(true);
  //   setDateFlexible(pDateFlexible); // Set dateFlexible from props
  //   setDaysEnabled(pShifts.length > 0 && pShifts.every((s) => isShiftFilled(s)));
  //   setDaysFilled(true);
  //   setSelectedDuration(pDuration);
  // };

  const reloadState = () => {
    // Initialize selected days from payload
    const pSelectedDays = props.currentPayload?.daysOfWeek?.filter((d) => d.isRequired)?.map((d) => d.dayOfWeek) || [];

    setSelectedDays(new Set(pSelectedDays));

    // Initialize shifts from payload
    const pShifts: Shift[] = [];
    if (props.currentPayload?.weeklySchedule?.weeklyScheduleEntries) {
      props.currentPayload.weeklySchedule.weeklyScheduleEntries.forEach((entry) => {
        const day = entry.dayOfWeek;
        const shiftsForDay: Shift = {
          day,
          firstShift: null,
          hasSecondShift: false,
          secondShift: null,
          shiftType: entry.shiftType,
        };

        const firstShift = entry.shifts?.find((s) => s.shiftType === 1);
        const secondShift = entry.shifts?.find((s) => s.shiftType === 2);

        if (firstShift) {
          shiftsForDay.firstShift = {
            startTime: convertTimeToDate(firstShift.jobStartTime),
            endTime: convertTimeToDate(firstShift.jobEndTime),
            endStartTime: addOneHour(convertTimeToDate(firstShift.jobStartTime)),
            error: [ShiftEntryErrors.noErrors],
          };
        }

        if (secondShift) {
          shiftsForDay.hasSecondShift = true;
          shiftsForDay.secondShift = {
            startTime: convertTimeToDate(secondShift.jobStartTime),
            endTime: convertTimeToDate(secondShift.jobEndTime),
            endStartTime: addOneHour(convertTimeToDate(secondShift.jobStartTime)),
            error: [ShiftEntryErrors.noErrors],
          };
        }

        pShifts.push(shiftsForDay);
      });
    }
    setShifts(new Set(pShifts));

    // setCompletionMethod(props.currentPayload.jobRosterType ?? 0);
    setSelectedDuration(props.currentPayload.duration?.toString() ?? "0");
    if (props.currentPayload.isJobFlexible !== undefined) {
      setDateFlexible(props.currentPayload.isJobFlexible ? 0 : 1);
    } else {
      setDateFlexible(null); // No selection when no existing value
    }

    if (props.currentPayload.jobDate) {
      setSelectedDate(new Date(props.currentPayload.jobDate));
    }

    // Update derived states
    setFirstShiftFilled(pShifts.length > 0 && isShiftFilled(pShifts[0]));
    setDaysEnabled(pShifts.length > 0 && pShifts.every((s) => isShiftFilled(s)));
    setDaysFilled(pSelectedDays.length > 0);
  };

  const checkDaysEnabled = () => {
    setFirstShiftFilled(isShiftFilled([...shifts].sort((a, b) => a.day - b.day)[0]));
  };

  const init = () => {
    if ([4, 8].includes(props.currentPayload.jobType)) {
      setAllowedTwoShifts(false);
    } else {
      setAllowedTwoShifts(true);
    }
  };

  // useEffect(() => {
  //   init();
  //   reloadState();
  // }, []);

  useEffect(() => {
    init();
    reloadState();
  }, [props.currentPayload]); // Add props.currentPayload as dependency

  useEffect(() => {
    checkDaysEnabled();
    checkAllShiftsFilled();
  }, [shifts, selectedDays]);

  const remakeShifts = (day: number) => {
    const updated = [...shifts];
    const index = updated.findIndex((s) => s.day === day);

    if (index !== -1) {
      updated.splice(index, 1);
    } else {
      updated.push({
        day: day,
        firstShift: {
          error: [ShiftEntryErrors.noErrors],
          startTime: null,
          endTime: null,
          endStartTime: null,
        },
        hasSecondShift: props.currentPayload.jobType === 12,
        secondShift:
          props.currentPayload.jobType === 12
            ? {
                error: [ShiftEntryErrors.noErrors],
                startTime: null,
                endTime: null,
                endStartTime: null,
              }
            : null,
      });
    }
    setShifts(new Set(updated));
  };

  const toggleDay = (day: number) => {
    setSelectedDays((prevDays) => {
      const newSelectedDays = new Set(prevDays);

      if (newSelectedDays.has(day)) {
        // Removing day - delete from selected days and remove its shifts
        newSelectedDays.delete(day);
        setShifts((prevShifts) => new Set([...prevShifts].filter((s) => s.day !== day)));
      } else {
        // Adding day - add to selected days and create default shifts
        newSelectedDays.add(day);
        setShifts(
          (prevShifts) =>
            new Set([
              ...prevShifts,
              {
                day: day,
                firstShift: {
                  error: [ShiftEntryErrors.noErrors],
                  startTime: null,
                  endTime: null,
                  endStartTime: null,
                },
                hasSecondShift: props.currentPayload.jobType === 12,
                secondShift:
                  props.currentPayload.jobType === 12
                    ? {
                        error: [ShiftEntryErrors.noErrors],
                        startTime: null,
                        endTime: null,
                        endStartTime: null,
                      }
                    : null,
                shiftType: 1, // Default to first shift
              },
            ])
        );
      }

      return newSelectedDays;
    });
  };

  function updateShift(day: number, shift: Shift): void {
    setShifts((prev) => {
      const updated = new Set([...prev].filter((s) => s.day !== day));

      // Only add the shift back if it has at least one shift (first or second)
      if (shift.firstShift || (shift.hasSecondShift && shift.secondShift)) {
        updated.add(shift);
      } else {
        // If no shifts left, remove the day from selectedDays
        setSelectedDays((prevDays) => {
          const newDays = new Set(prevDays);
          newDays.delete(day);
          return newDays;
        });
      }

      return updated;
    });
  }

  const autoFill = () => {
    const firstShift = [...shifts].sort((a, b) => a.day - b.day)[0];
    setShifts((prev) => {
      const updated: Shift[] = [...prev].map((s) => ({
        ...firstShift,
        day: s.day,
      }));
      return new Set(updated);
    });
    setDaysEnabled(true);
  };
  const continueDBD = () => {
    setDaysEnabled(true);
  };

  const processState = (): Partial<PayloadTemplate> => {
    if (!isFormValid()) return {};


    const entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = [];

    for (let i = 0; i < 7; i++) {
      if (!selectedDays.has(i)) continue;

      const weeklyScheduleEntries = props.currentPayload.weeklySchedule?.weeklyScheduleEntries as
        | PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"]
        | undefined;
      const currentEntry = weeklyScheduleEntries?.find((e) => e.dayOfWeek === i) ?? null;

      const newEntry = [...shifts].find((s) => s.day === i) ?? null;

      // if (!newEntry || !newEntry.firstShift) continue;

      const commonShiftTime = {
        start: newEntry.firstShift ? formatDateToTimeString(newEntry.firstShift.startTime) : formatDateToTimeString(newEntry.secondShift.startTime),
        end: newEntry.firstShift ? formatDateToTimeString(newEntry.firstShift.endTime) : formatDateToTimeString(newEntry.secondShift.endTime),
      };

      const entry: Partial<PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][number]> = {
        dayOfWeek: i,
        hourlyPrice: currentEntry?.hourlyPrice ?? null,
        price: currentEntry?.price ?? null,
        isRequired: true,
        jobStartTime: commonShiftTime.start,
        jobEndTime: commonShiftTime.end,
        shifts: [],
      };

     if (newEntry.firstShift) {
  const firstShiftFromCurrent = currentEntry ? currentEntry.shifts?.find((ces) => ces.shiftType === 1) : null;
  entry.shifts.push({
    hourlyPrice: firstShiftFromCurrent?.hourlyPrice ?? (String(props.currentPayload.price) ?? null),
    price: firstShiftFromCurrent?.price ?? null,
    isRequired: true,
    jobStartTime: formatDateToTimeString(newEntry.firstShift.startTime),
    jobEndTime: formatDateToTimeString(newEntry.firstShift.endTime),
    shiftType: 1, // First shift
  });
}

if (newEntry.secondShift) {
  const secondShiftFromCurrent = currentEntry ? currentEntry.shifts?.find((ces) => ces.shiftType === 2) : null;
  entry.shifts.push({
    hourlyPrice: secondShiftFromCurrent?.hourlyPrice ?? (String(props.currentPayload.price) ?? null),
    price: secondShiftFromCurrent?.price ?? null,
    isRequired: true,
    jobStartTime: formatDateToTimeString(newEntry.secondShift.startTime) as string,
    jobEndTime: formatDateToTimeString(newEntry.secondShift.endTime) as string,
    shiftType: 2, // Second shift
  });
}

      entries.push(entry as PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][number]);
    }
    return {
      jobDate: convertDate(selectedDate) as string,
      daysOfWeek: Array.from({ length: 7 }, (_, index) => ({
        dayOfWeek: index,
        isRequired: selectedDays.has(index),
      })),
      duration: selectedDuration,
      isJobFlexible: dateFlexible === 0,
      // jobRosterType: completionMethod,
      durationType: 1,
      weeklySchedule: {
        ...props.currentPayload.weeklySchedule,
        weeklyScheduleEntries: entries,
      },
    };
  };


  return (
    <DASContext.Provider
      value={{
        ...defaultContextValue,
        postJobProps: () => props,
        selectedDays,
        toggleDay,
        shifts,
        updateShift,
        firstShiftFilled,
        autoFill,
        continueDBD,
        daysEnabled,
        daysFilled,
        selectedDuration,
        setSelectedDuration,
        selectedDate,
        setSelectedDate,
        processState,
        isValid,
        allowedTwoShifts,
        dateFlexible, // Add to context
        setDateFlexible, // Add setter to context
        // completionMethod,
        // setCompletionMethod,
      }}
    >
      <div className="w-full h-full bg-white overflow-hidden">
        <DASMLayout />
      </div>
    </DASContext.Provider>
  );
};

export default DaysAndScheduleMobile;
