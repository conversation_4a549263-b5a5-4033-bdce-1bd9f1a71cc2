import React, { useEffect, useState } from "react";
import styles from "../commonStyle/manage-job-card.module.css";
import { Jobs } from "../containers/Common/manageJobs/types";
import Service from "../services/services";

import ChildcareImage from "../assets/images/Icons/childcare-smile.png";
import TutoringImage from "../assets/images/Icons/tutoring-book.png";
import OddJobImage from "../assets/images/Icons/odd-jobs-box.png";
import { IoClose } from "react-icons/io5";

interface ManageJobCardProps {
  label?: string; // Optional label prop
  daysAhead?: number; // Number of days to look ahead for jobs (default 10)
}

const ManageJobCard: React.FC<ManageJobCardProps> = ({ 
  label, 
  daysAhead = 10 // Default to 10 days ahead
}) => {
  const [upcomingJobs, setUpcomingJobs] = useState<Jobs[]>([]);
  const [job, setJob] = useState<Jobs | null>(null);
  const [isVisible, setIsVisible] = useState(true);

  // Function to format date in the specified format
  const formatJobDate = (dateString?: string) => {
    if (!dateString) return null;

    const date = new Date(dateString);
    const days = [
      "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday",
    ];
    const months = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
    ];

    const dayName = days[date.getDay()];
    const dayOfMonth = date.getDate();
    const month = months[date.getMonth()];

    // Create the ordinal suffix
    const getDayOrdinal = (day: number) => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1: return "st";
        case 2: return "nd";
        case 3: return "rd";
        default: return "th";
      }
    };

    return (
      <span className={styles.latestJobDateFormat}>
        <span className={styles.dayName}>{dayName}</span> {dayOfMonth}
        {getDayOrdinal(dayOfMonth)}, {month}
      </span>
    );
  };

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const fetchedUpcomingJobs = await new Promise<Jobs[]>(
          (resolve, reject) => {
            Service.getUpComingJobs(
              (data: Jobs[]) => resolve(data),
              (error) => reject(error)
            );
          }
        );

        // Calculate the date range
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + daysAhead);

        // Filter jobs within the specified date range and sort by date
        const filteredAndSortedJobs = fetchedUpcomingJobs
          .filter((job) => {
            if (!job.jobDate) return false;
            const jobDate = new Date(job.jobDate);
            return jobDate >= today && jobDate <= futureDate;
          })
          .sort((a, b) => {
            // Safely handle potential undefined jobDate
            const dateA = a.jobDate ? new Date(a.jobDate) : new Date();
            const dateB = b.jobDate ? new Date(b.jobDate) : new Date();
            return dateA.getTime() - dateB.getTime();
          });

        if (filteredAndSortedJobs.length > 0) {
          setUpcomingJobs(filteredAndSortedJobs);
          setJob(filteredAndSortedJobs[0]);
        } else {
          setUpcomingJobs([]);
          setJob(null);
        }
      } catch (error) {
        console.error("Error fetching jobs:", error);
      }
    };

    fetchJobs();
  }, [daysAhead]); // Add daysAhead to dependency array

  const handleClose = () => {
    setIsVisible(false);
  };

  // Function to determine the display label and image based on job type
  const getJobDetails = (currentJob?: Jobs) => {
    const jobToUse = currentJob || job;
    if (!jobToUse)
      return {
        label: "Childcare",
        image: (
          <img
            src={ChildcareImage}
            alt="Childcare"
            width={18.33}
            height={17.5}
          />
        ),
      };

    switch (jobToUse.jobType) {
      case 256:
        return {
          label: "Odd Job",
          image: (
            <img src={OddJobImage} alt="Odd Job" width={18.33} height={17.5} />
          ),
        };
      case 128:
      case 64:
        return {
          label: "Tutoring",
          image: (
            <img
              src={TutoringImage}
              alt="Tutoring"
              width={18.33}
              height={17.5}
            />
          ),
        };
      default:
        return {
          label: "Childcare",
          image: (
            <img
              src={ChildcareImage}
              alt="Childcare"
              width={18.33}
              height={17.5}
            />
          ),
        };
    }
  };

  const getDaysUntilJob = (jobDateParam?: string) => {
    // Prioritize the parameter, then the job's jobDate
    const dateToCheck = jobDateParam || job?.jobDate;

    // Additional type and existence check
    if (!dateToCheck) return null;

    // Safely create Date object
    const jobDate = new Date(dateToCheck);
    const today = new Date();

    // Ensure valid date calculation
    const timeDiff = jobDate.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return daysDiff;
  };

  // If no jobs or card not visible, return null
  if (!job || !isVisible) {
    return null;
  }

  const { label: displayLabel, image } = getJobDetails();
  const daysUntilJob = getDaysUntilJob();
  const latestJobDate = formatJobDate(upcomingJobs[0]?.jobDate);

  return (
    <div className={styles.cardDiv}>
      <div className={styles.cardDivSmall}></div>
      <div className={styles.cardContent}>
        <p className={styles.cardPara}>
          {image}
          {displayLabel}
        </p>
        <IoClose onClick={handleClose} className={styles.CloseBtn} />
        <div className={styles.jobCountDiv}>
          <p className={styles.jobCountText}>{upcomingJobs.length}</p>
        </div>
        <div>
          <p className={styles.subPara}>
            {job.isRecurringJob === true
              ? "Recurring Job"
              : job.isRecurringJob === false
              ? "One-off Job"
              : ""}
          </p>
        </div>

        <div className={styles.commencesDiv}>
          {daysUntilJob !== null && daysUntilJob <= daysAhead && daysUntilJob > 0 && (
            <button className={styles.commencesBtn}>
              Job commences in{" "}
              <span className={styles.commences}>{daysUntilJob} days</span>
            </button>
          )}
        </div>
      </div>

      <div className={styles.cardDivLast}>
        <div className={styles.jobInfoDiv}>
          {latestJobDate && (
            <p className={styles.latestJobDateText}>{latestJobDate}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManageJobCard;