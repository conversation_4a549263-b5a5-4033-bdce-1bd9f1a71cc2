import { useState, useCallback } from 'react';
import useLoader from './LoaderHook';

interface UseApiDetailsOptions<T> {
  transform?: (data: any) => T;
  onSuccess?: (data: T) => void;
  onError?: (error: any) => void;
  debugMode?: boolean;
}

interface UseApiDetailsReturn<T> {
  details: T | null;
  isLoading: boolean;
  error: string | null;
  fetchDetails: (
    apiCall: (
      successCallback: (response: any) => void, 
      errorCallback: (error: any) => void, 
      ...params: any[]
    ) => void,
    ...params: any[]
  ) => Promise<void>;
  clearDetails: () => void;
  setDetails: (details: T | null) => void;
}

/**
 * Generic hook for fetching and managing API details (single item)
 * @param options Configuration options for the hook
 * @returns Object with details, loading state, error state, and methods
 */
export const useApiDetails = <T = any>(options: UseApiDetailsOptions<T> = {}): UseApiDetailsReturn<T> => {
  const { transform, onSuccess, onError, debugMode = false } = options;
  
  const [details, setDetails] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  const fetchDetails = useCallback(async (
    apiCall: (
      successCallback: (response: any) => void, 
      errorCallback: (error: any) => void, 
      ...params: any[]
    ) => void,
    ...params: any[]
  ): Promise<void> => {
    if (debugMode) {
      console.log("Fetching details with params:", params);
    }
    
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        apiCall(
          (response: any) => {
            try {
              if (debugMode) {
                console.log("Raw API Response:", response);
              }
              
              // Enhanced data extraction with better error handling
              let actualData: any;
              if (Array.isArray(response)) {
                actualData = response[0];
                if (debugMode) {
                  console.log("Response is array, using first element:", actualData);
                }
              } else if (response?.data) {
                actualData = response.data;
                if (debugMode) {
                  console.log("Response has data property:", actualData);
                }
              } else {
                actualData = response;
                if (debugMode) {
                  console.log("Using response directly:", actualData);
                }
              }
              
              if (debugMode) {
                console.log('actualData - Full Object:', actualData);
                console.log('actualData keys:', actualData ? Object.keys(actualData) : 'actualData is null/undefined');
              }
              
              if (!actualData) {
                const errorMsg = "No actual data found in response";
                console.error(errorMsg);
                setError(errorMsg);
                reject(new Error(errorMsg));
                return;
              }

              const processedDetails = transform ? transform(actualData) : actualData;
              
              if (debugMode) {
                console.log("Processed Details:", processedDetails);
                console.log("Details JSON stringified:", JSON.stringify(processedDetails, null, 2));
              }
              
              setDetails(processedDetails);
              onSuccess?.(processedDetails);
              resolve();
            } catch (err) {
              console.error('Error processing API response:', err);
              const errorMsg = err instanceof Error ? err.message : 'Failed to process response';
              setError(errorMsg);
              onError?.(err);
              reject(err);
            }
          },
          (error: any) => {
            console.error("API call failed:", error);
            const errorMsg = error?.message || 'API call failed';
            setError(errorMsg);
            onError?.(error);
            reject(error);
          },
          ...params
        );
      });
    } catch (err) {
      console.error('Fetch details failed:', err);
    } finally {
      setIsLoading(false);
      disableLoader();
    }
  }, [transform, onSuccess, onError, debugMode, enableLoader, disableLoader]);

  const clearDetails = useCallback(() => {
    setDetails(null);
    setError(null);
  }, []);

  return {
    details,
    isLoading,
    error,
    fetchDetails,
    clearDetails,
    setDetails
  };
};

export default useApiDetails;
