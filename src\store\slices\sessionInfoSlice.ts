import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { fetchNoRefreshSessionInfo, fetchSessionInfo, refreshAccount, updateSessionInfo, updateUser } from "../tunks/sessionInfoTunk";
import { SessionInfoType } from "../types";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";

const initialState: SessionInfoType = {
  data: null,
  loading: true,
  error: null,
};

const SessionInfo = createSlice({
  name: "sessionInfo",
  initialState,
  reducers: {
    createNewSessionInfo: (state, action: PayloadAction<SessionInfoType>) => {
      state.data = action.payload;
      state.loading = false;
    },
    removeSession: (state) => {
      utils.removeCookie(CookiesConstant.accessToken);
      utils.removeCookie(CookiesConstant.refreshToken);
      utils.removeCookie(CookiesConstant.clientType);
      utils.removeCookie(CookiesConstant.adminMode);
      state.data = null;
      state.error = "no_session";
      window.location.reload();
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSessionInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSessionInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.error = null;
      })
      .addCase(fetchSessionInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload ? action.payload.message : "An unknown error occurred";
      })
      .addCase(fetchNoRefreshSessionInfo.fulfilled, (state, action) => {
        state.data = action.payload;
      })
      .addCase(updateSessionInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.error = null;
      })
      .addCase(refreshAccount.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.error = null;
      });
  },
});

export const { createNewSessionInfo, removeSession } = SessionInfo.actions;
export default SessionInfo.reducer;
