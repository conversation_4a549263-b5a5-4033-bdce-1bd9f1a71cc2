import { useEffect, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { GoBack, Next, RadioButton, RadioButton2 } from "./Buttons";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Divider } from "primereact/divider";
import { FaAngleDown, FaCheck } from "react-icons/fa6";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";
function TutoringSubjects() {
  const { isMobile } = useIsMobile();
  return isMobile ? <TutoringSubjectsMobile /> : <TutoringSubjectsWeb />;
}

export default TutoringSubjects;
const useJobTypeHook = () => {
  const { payload, next, prev, setpayload } = useJobManager();
  interface Subject {
    optionId: number;
    text: string;
    children: Subject[] | null;
  }

  function RenderSubjects({
    subject,
    selectedSubject,
    changeSelectedSubject,
  }: {
    subject: Subject[];
    selectedSubject: number;
    changeSelectedSubject: (param: Subject) => void;
  }) {
    return subject.map((val, index) => {
      if (val.children === null) {
        return (
          <div
            key={index}
            className="flex gap-2 align-items-center mb-2 cursor-pointer"
            onClick={() => {
              changeSelectedSubject(val);
            }}
          >
            <RadioButton2 selected={val.optionId === selectedSubject} />
            <p
              className="m-0 p-0 pr-2"
              style={{
                fontWeight: val.optionId === selectedSubject ? "700" : "400",
                fontSize: "14px",
                color: val.optionId === selectedSubject ? "#179D52" : "#585858",
              }}
            >
              {val.text}
            </p>
          </div>
        );
      } else {
        return (
          <div key={index}>
            <p
              className="m-0 p-0 pr-2"
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              {val.text}
            </p>
            <div className="px-1">
              <RenderSubjects subject={val.children} selectedSubject={selectedSubject} changeSelectedSubject={changeSelectedSubject} />
            </div>
          </div>
        );
      }
    });
  }

  const [subjectType, changeSubjectType] = useState(-1);
  const { isMobile } = useIsMobile();
  const [selectedSubject, changeSelectedSubject] = useState<{
    optionId: number;
    text: string;
  }>({
    optionId: -1,
    text: "",
  });
  const [focusedSubjectIndex, setFocusedSubjectIndex] = useState<number | null>(null);
  const [defaultSubjects, updateDefaultSubjects] = useState<Subject[]>([]);

  const { data, loading } = useSelector((state: RootState) => state.sessionInfo);

  function buttonDisabled() {
    if (subjectType === -1) return true;
    if (subjectType === 1 && selectedSubject.optionId === -1) return true;
    return false;
  }

  useEffect(() => {
    if (loading) return;

    const mapToRequiredSubjects = (subjects: any[]): Subject[] => {
      return subjects.map((subject) => ({
        optionId: subject["optionId"],
        text: subject["text"],
        children: subject["children"] ? mapToRequiredSubjects(subject["children"]) : null,
      }));
    };

    updateDefaultSubjects(mapToRequiredSubjects(data["client"][`${payload.jobType === 64 ? "primary" : "high"}SchoolSubjects`]));
  }, [data]);

  useEffect(() => {
    const ss: number[] = payload?.jobSettings?.["schoolSubjects"];
    if (ss) {
      changeSubjectType(ss.length > 0 ? 1 : 0);
    }

    function findSubjectByOptionId(subjects: Subject[], selectedOptionId: number): { subject: Subject | null; groupIndex: number } {
      for (let groupIndex = 0; groupIndex < subjects.length; groupIndex++) {
        const subject = subjects[groupIndex];

        const findInNestedChildren = (currentSubject: Subject): Subject | null => {
          if (currentSubject.optionId === selectedOptionId) {
            return currentSubject;
          }

          if (currentSubject.children && currentSubject.children.length > 0) {
            for (const child of currentSubject.children) {
              const found = findInNestedChildren(child);
              if (found) return found;
            }
          }

          return null;
        };

        if (subject.optionId === selectedOptionId) {
          return { subject, groupIndex };
        }

        if (subject.children) {
          const foundInChildren = findInNestedChildren(subject);
          if (foundInChildren) {
            return { subject: foundInChildren, groupIndex };
          }
        }
      }

      return { subject: null, groupIndex: -1 };
    }
    if (ss && ss.length > 0) {
      const { subject, groupIndex } = findSubjectByOptionId(defaultSubjects, ss[0]);

      if (subject) {
        changeSelectedSubject({
          optionId: subject.optionId,
          text: subject.text,
        });
        setFocusedSubjectIndex(groupIndex);
        changeSubjectType(1);
      }
    }
  }, [payload, defaultSubjects]);

  function createFilters() {
    if (selectedSubject.optionId === -1) return payload.applicantFilters;
    if (payload.applicantFilters === null) {
      return [
        {
          field: "schoolSubjects",
          operator: "eq",
          value: [selectedSubject.optionId],
        },
      ];
    }
    return [
      ...payload.applicantFilters.filter((val) => val.field !== "schoolSubjects"),
      {
        field: "schoolSubjects",
        operator: "eq",
        value: [selectedSubject.optionId],
      },
    ];
  }

  return {
    payload,
    next,
    prev,
    setpayload,
    RenderSubjects,
    buttonDisabled,
    focusedSubjectIndex,
    isMobile,
    selectedSubject,
    changeSelectedSubject,
    subjectType,
    changeSubjectType,
    setFocusedSubjectIndex,
    defaultSubjects,
    updateDefaultSubjects,
    createFilters,
  };
};

const TutoringSubjectsWeb = () => {
  const {
    payload,
    next,
    prev,
    setpayload,
    RenderSubjects,
    buttonDisabled,
    focusedSubjectIndex,
    isMobile,
    selectedSubject,
    changeSelectedSubject,
    subjectType,
    changeSubjectType,
    setFocusedSubjectIndex,
    defaultSubjects,
    updateDefaultSubjects,
    createFilters,
  } = useJobTypeHook();
  return (
    <div className="w-full h-full select-none relative">
      <div
        className="h-full flex flex-column mx-auto"
        style={{
          width: "90%",
        }}
      >
        <div
          className="w-full flex-grow-1 pt-8 flex flex-column overflow-x-auto"
          style={{
            textWrap: "nowrap",
          }}
        >
          <div className="flex gap-3 align-items-center">
            <h1
              className="m-0 p-0"
              style={{
                fontWeight: "700",
                fontSize: "30px",
                color: "#585858",
              }}
            >
              Tutoring Subjects
            </h1>
            {selectedSubject.optionId !== -1 && (
              <div
                className="flex gap-2 align-items-center py-2 pr-4"
                style={{
                  backgroundColor: "#37A950",
                  borderRadius: "20px",
                  paddingLeft: "12px",
                }}
              >
                <div
                  className="flex justify-content-center align-items-center p-1"
                  style={{
                    backgroundColor: "#F0F4F7",
                    borderRadius: "50%",
                  }}
                >
                  <FaCheck color="#37A950" />
                </div>
                <p
                  className="m-0 p-0"
                  style={{
                    fontSize: "12px",
                    fontWeight: "800",
                    color: "#ffffff",
                  }}
                >
                  You have selected ‘{selectedSubject.text}’
                </p>
              </div>
            )}
          </div>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: "16px",
              color: "#585858",
            }}
          >
            Select subject
          </p>
          <div className="flex gap-2 mt-2">
            {["Select All Subjects", "Specific Subject"].map((val, index) => (
              <div
                key={index}
                className="flex gap-3 align-items-center cursor-pointer"
                style={{
                  border: subjectType === index ? "2px solid #179D52" : "1px solid #DFDFDF",
                  borderRadius: "10px",
                  padding: "10px 20px",
                  minWidth: "196px",
                  width: "196px",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  changeSubjectType(index);
                  changeSelectedSubject({
                    text: "",
                    optionId: -1,
                  });
                  setFocusedSubjectIndex(-1);
                }}
              >
                <RadioButton selected={subjectType === index} />
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: subjectType === index ? "700" : "400",
                    fontSize: "14px",
                    color: subjectType === index ? "#179D52" : "#585858",
                  }}
                >
                  {val}
                </p>
              </div>
            ))}
          </div>
          <div className="flex gap-2 mt-5">
            <div
              className="relative"
              style={{
                height: "51px",
                minWidth: "196px",
                width: "196px",
              }}
            >
              {(subjectType === -1 || subjectType === 0) && (
                <>
                  <div
                    className="absolute"
                    style={{
                      background: "#DFDFDF",
                      width: "1px",
                      height: "45px",
                      left: "50%",
                      top: "-50%",
                    }}
                  />
                  <div
                    className="absolute"
                    style={{
                      background: "#DFDFDF",
                      width: "100px",
                      height: "1px",
                      left: "50%",
                      top: "40%",
                    }}
                  />
                </>
              )}
            </div>
            <div className="flex gap-2 flex-column md:flex-row">
              {defaultSubjects.map((val, index) => (
                <div key={index} className=" relative">
                  {index === 0 && subjectType === 1 && (
                    <div
                      className="absolute"
                      style={{
                        width: "1px",
                        height: "24px",
                        backgroundColor: "#DFDFDF",
                        left: "31px",
                        top: "-28px",
                      }}
                    />
                  )}
                  <div
                    className="flex flex-column mt-1 md:mt-0"
                    style={{
                      border:
                        subjectType === 0 || val.children.find((val) => val.optionId === selectedSubject.optionId)
                          ? "2px solid #179D52"
                          : "1px solid #DFDFDF",
                      borderRadius: "10px",
                      padding: "10px 15px",
                      maxHeight: "270px",
                      overflow: "hidden",
                    }}
                  >
                    <div
                      className="flex gap-3 align-items-center cursor-pointer justify-content-between"
                      onClick={(e) => {
                        e.preventDefault();
                        changeSubjectType(1);
                        setFocusedSubjectIndex(index);
                        changeSelectedSubject({
                          text: "",
                          optionId: -1,
                        });
                      }}
                    >
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "400",
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        {val.text}
                      </p>
                      <FaAngleDown color="#585858" className="mb-1" />
                    </div>
                    <div
                      className="overflow-y-auto flex flex-column"
                      style={{
                        width: "min-content",
                      }}
                    >
                      {focusedSubjectIndex === index && (
                        <div
                          style={{
                            width: "1px",
                            minHeight: "10px",
                            height: "10px",
                            backgroundColor: "#DFDFDF",
                            marginLeft: "8px",
                          }}
                        />
                      )}

                      {focusedSubjectIndex === index && (
                        <RenderSubjects
                          subject={val.children}
                          selectedSubject={selectedSubject.optionId}
                          changeSelectedSubject={changeSelectedSubject}
                        />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div
        className="mt-auto sticky bottom-0"
        style={{
          width: "100%",
        }}
      >
        <Divider />
        <div className="flex justify-content-between py-3 pl-6" style={{ width: "80%" }}>
          <GoBack
            onClick={(e) => {
              e.preventDefault();

              setpayload({
                ...payload,
                jobSettings: {
                  ...payload.jobSettings,
                  schoolSubjects: selectedSubject.optionId === -1 ? [] : [selectedSubject.optionId],
                },
                applicantFilters: createFilters(),
              });

              prev("typeand-years");
            }}
          />
          <Next
            disabled={buttonDisabled()}
            onClick={(e) => {
              e.preventDefault();

              setpayload({
                ...payload,
                jobSettings: {
                  ...payload.jobSettings,
                  schoolSubjects: selectedSubject.optionId === -1 ? [] : [selectedSubject.optionId],
                },
                applicantFilters: createFilters(),
              });

              next("day-and-schedule");
            }}
          />
        </div>
      </div>
    </div>
  );
};
const TutoringSubjectsMobile = () => {
  const {
    next,
    payload,
    prev,
    setpayload,
    RenderSubjects,
    buttonDisabled,
    focusedSubjectIndex,
    isMobile,
    selectedSubject,
    changeSelectedSubject,
    subjectType,
    changeSubjectType,
    setFocusedSubjectIndex,
    defaultSubjects,
    updateDefaultSubjects,
    createFilters,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        position: "relative",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          height: "100%",
          flexGrow: "1",
          overflow: "hidden",
          overflowY: "scroll",
          width: "100%",
          padding: "25px",
        }}
      >
        <div
          className="h-full flex flex-column mx-auto"
          style={{
            width: "100%",
          }}
        >
          <div
            className="w-full flex-grow-1 flex flex-column"
            style={{
              textWrap: "nowrap",
            }}
          >
            <div className="flex flex-column gap-3 align-items-flex-start ">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "24px",
                  color: "#585858",
                }}
              >
                Tutoring Subjects
              </h1>
              {selectedSubject.optionId !== -1 && (
                <div
                  className="flex gap-2 align-items-center py-2 pr-4"
                  style={{
                    backgroundColor: "#37A950",
                    borderRadius: "20px",
                    paddingLeft: "12px",
                    width: "min-content",
                  }}
                >
                  <div
                    className="flex justify-content-center align-items-center p-1"
                    style={{
                      backgroundColor: "#F0F4F7",
                      borderRadius: "50%",
                    }}
                  >
                    <FaCheck color="#37A950" />
                  </div>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontSize: "12px",
                      fontWeight: "800",
                      color: "#ffffff",
                    }}
                  >
                    You have selected ‘{selectedSubject.text}’
                  </p>
                </div>
              )}
            </div>
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",
              }}
            >
              Select subject
            </p>
            <div className="flex gap-2 mt-2">
              {["Select All Subjects", "Specific Subject"].map((val, index) => (
                <div
                  key={index}
                  className="flex gap-3 align-items-center cursor-pointer"
                  style={{
                    border: subjectType === index ? "2px solid #179D52" : "1px solid #DFDFDF",
                    borderRadius: "10px",
                    padding: "10px 20px",
                    maxWidth: "196px",
                    width: "100%",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    changeSubjectType(index);
                    changeSelectedSubject({
                      text: "",
                      optionId: -1,
                    });
                    setFocusedSubjectIndex(-1);
                  }}
                >
                  <RadioButton selected={subjectType === index} />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: subjectType === index ? "700" : "400",
                      fontSize: "14px",
                      color: subjectType === index ? "#179D52" : "#585858",
                      textWrap: "wrap",
                    }}
                  >
                    {val}
                  </p>
                </div>
              ))}
            </div>
            <div className="flex gap-2 mt-5">
              <div
                className="relative"
                style={{
                  height: "51px",
                  maxWidth: "196px",
                  width: "100%",
                }}
              >
                {(subjectType === -1 || subjectType === 0) && (
                  <>
                    <div
                      className="absolute"
                      style={{
                        background: "#DFDFDF",
                        width: "1px",
                        height: "45px",
                        left: "50%",
                        top: "-50%",
                      }}
                    />
                    <div
                      className="absolute"
                      style={{
                        background: "#DFDFDF",
                        width: "100px",
                        height: "1px",
                        left: "50%",
                        top: "40%",
                      }}
                    />
                  </>
                )}
              </div>
              <div className="flex gap-2 flex-column md:flex-row">
                {defaultSubjects.map((val, index) => (
                  <div key={index} className=" relative">
                    {index === 0 && subjectType === 1 && (
                      <div
                        className="absolute"
                        style={{
                          width: "1px",
                          height: "24px",
                          backgroundColor: "#DFDFDF",
                          left: "31px",
                          top: "-28px",
                        }}
                      />
                    )}
                    <div
                      className="flex flex-column mt-1 md:mt-0"
                      style={{
                        border:
                          subjectType === 0 || val.children.find((val) => val.optionId === selectedSubject.optionId)
                            ? "2px solid #179D52"
                            : "1px solid #DFDFDF",
                        borderRadius: "10px",
                        padding: "10px 15px",
                        maxHeight: "270px",
                        overflow: "hidden",
                      }}
                    >
                      <div
                        className="flex gap-3 align-items-center cursor-pointer justify-content-between"
                        onClick={(e) => {
                          e.preventDefault();
                          changeSubjectType(1);
                          setFocusedSubjectIndex(index);
                          changeSelectedSubject({
                            text: "",
                            optionId: -1,
                          });
                        }}
                      >
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: "400",
                            fontSize: "14px",
                            color: "#585858",
                          }}
                        >
                          {val.text}
                        </p>
                        <FaAngleDown color="#585858" className="mb-1" />
                      </div>
                      <div
                        className="overflow-y-auto flex flex-column"
                        style={{
                          width: "min-content",
                        }}
                      >
                        {focusedSubjectIndex === index && (
                          <div
                            style={{
                              width: "1px",
                              minHeight: "10px",
                              height: "10px",
                              backgroundColor: "#DFDFDF",
                              marginLeft: "8px",
                            }}
                          />
                        )}

                        {focusedSubjectIndex === index && (
                          <RenderSubjects
                            subject={val.children}
                            selectedSubject={selectedSubject.optionId}
                            changeSelectedSubject={changeSelectedSubject}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* <div className="flex justify-content-between mt-4 mb-5">
                        <GoBack
                            onClick={(e) => {
                                e.preventDefault();
                                prevClicked({
                                    ...currentPayload,
    
                                    jobSettings: {
                                        ...currentPayload.jobSettings,
                                        schoolSubjects:
                                            selectedSubject.optionId === -1
                                                ? []
                                                : [selectedSubject.optionId],
                                    },
                                    applicantFilters: createFilters(),
                                });
                            }}
                        />
                        <Next
                            disabled={buttonDisabled()}
                            onClick={(e) => {
                                e.preventDefault();
                                nextClicked({
                                    ...currentPayload,
                                    jobSettings: {
                                        ...currentPayload.jobSettings,
                                        schoolSubjects:
                                            selectedSubject.optionId === -1
                                                ? []
                                                : [selectedSubject.optionId],
                                    },
                                    applicantFilters: createFilters(),
                                });
                            }}
                        />
                    </div> */}
        </div>
      </div>
      <BackButtonPortal id="back-button-portal">
        <img
          src={SideArrow}
          alt="cross"
          width={13}
          height={20}
          className="cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            setpayload({
              ...payload,

              jobSettings: {
                ...payload.jobSettings,
                schoolSubjects: selectedSubject.optionId === -1 ? [] : [selectedSubject.optionId],
              },
              applicantFilters: createFilters(),
            });
            prev("typeand-years");
          }}
        />
      </BackButtonPortal>

      <CustomFooterButton
        label="Next"
        onClick={() => {
          setpayload({
            ...payload,
            jobSettings: {
              ...payload.jobSettings,
              schoolSubjects: selectedSubject.optionId === -1 ? [] : [selectedSubject.optionId],
            },
            applicantFilters: createFilters(),
          });
          next("day-and-schedule");
        }}
        isDisabled={buttonDisabled()} // Use dynamic buttonDisabled function
      />
    </div>
  );
};
