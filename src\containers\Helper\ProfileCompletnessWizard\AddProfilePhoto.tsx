import React, { useCallback, useEffect, useRef, useState } from "react";
import useLoader from "../../../hooks/LoaderHook";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import {
  decrementProfileActivationStep,
  incrementProfileActivationStep,
} from "../../../store/slices/applicationSlice";
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import { ProgressBar } from "primereact/progressbar";
import CustomButton from "../../../commonComponents/CustomButton";
import ReactCrop, {
  centerCrop,
  Crop,
  makeAspectCrop,
  PixelCrop,
} from "react-image-crop";
import { HiMiniCamera } from "react-icons/hi2";
import { updateUser } from "../../../store/tunks/sessionInfoTunk";
import Userimage from "../../../assets/images/Group_light.png";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";

const
  AddProfilePhoto = () => {
    // const{disableLoader,enableLoader}=useLoader()
    const dispatch = useDispatch<AppDispatch>();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { enableLoader, disableLoader } = useLoader();
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [crop, setCrop] = useState<Crop>();
    const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
    const [isImageUploaded, setIsImageUploaded] = useState(false);
    const {isMobile}=useIsMobile();
    const imgRef = useRef<HTMLImageElement>(null);
    const [, setCroppedImageUrl] = useState<string | null>(null);
    const handleprev = () => {
      dispatch(decrementProfileActivationStep());
    };

    useEffect(() => {
      if (sessionInfo.data?.["defaultImage"]?.["scale1ImageUrl"]) {
        setSelectedImage(sessionInfo.data["defaultImage"]["scale1ImageUrl"]);
      }
    }, [sessionInfo.data]);

    const handleCancel = () => {
      setSelectedImage(null);
      setCroppedImageUrl(null);
      setCompletedCrop(undefined);
      setCrop(undefined);
      setIsImageUploaded(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    };

    const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
      const { width, height } = e.currentTarget;
      const crop = centerCrop(
        makeAspectCrop(
          {
            unit: '%',
            width: 90,
          },
          4 / 4,
          width,
          height
        ),
        width,
        height
      )
      setCrop(crop)
    }

    const getCroppedImg = useCallback(
      (image: HTMLImageElement, crop: PixelCrop) => {
        const canvas = document.createElement("canvas");
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        const pixelCrop = {
          x: Math.round(crop.x * scaleX),
          y: Math.round(crop.y * scaleY),
          width: Math.round(crop.width * scaleX),
          height: Math.round(crop.height * scaleY),
          unit: 'px' as const // Ensure the unit is explicitly set to "px"
        };

        canvas.width = pixelCrop.width;
        canvas.height = pixelCrop.height;

        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.drawImage(
            image,
            pixelCrop.x,
            pixelCrop.y,
            pixelCrop.width,
            pixelCrop.height,
            0,
            0,
            pixelCrop.width,
            pixelCrop.height
          );
        }

        return {
          croppedImageUrl: canvas.toDataURL("image/jpeg"),
          x: pixelCrop.x,
          y: pixelCrop.y,
          height: pixelCrop.height,
          width: pixelCrop.width,
          unit: pixelCrop.unit // Include the unit property in the return object
        };
      },
      []
    );

    const handleSavePhoto = useCallback(() => {
      if (imgRef.current && completedCrop) {
        enableLoader();
        const { croppedImageUrl, x, y, height, width, unit } = getCroppedImg(imgRef.current, completedCrop);
        setCroppedImageUrl(croppedImageUrl);
        setSelectedImage(croppedImageUrl); // Set the cropped image as the selected image
        const payload = {
          id: sessionInfo.data["defaultImage"]["id"],
          cropDimensions: { x, y, width, height, unit },
          isCropped: true,
          scale1ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"], // Set the cropped image URL as scale1ImageUrl
          scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
          scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
        };
        dispatch(
          updateUser({
            payload: {
              ...sessionInfo.data,
              defaultImage: payload,
            },
          })
        )
          .then(() => {
            // Ensure the component re-renders with the new image
            setIsImageUploaded(false);
            setSelectedImage(null);
            setCroppedImageUrl(null);
            setCompletedCrop(undefined);
            setCrop(undefined);
          })
          .catch((error) => {
            console.error("Error updating user:", error);
          })
          .finally(() => {
            disableLoader();
          });
      }
    }, [completedCrop, getCroppedImg, sessionInfo.data, dispatch, disableLoader]);

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        enableLoader();
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
          setIsImageUploaded(true);
          setSelectedImage(reader.result as string);
          const binaryData = e.target?.result as ArrayBuffer;
          const payload = {
            id: sessionInfo.data["defaultImage"]["id"],
            imageBinary: binaryData,
            isCropped: true,
            scale1ImageUrl: reader.result as string, // Set the uploaded image as scale1ImageUrl
            scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
            scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
          };
          dispatch(
            updateUser({
              payload: {
                ...sessionInfo.data,
                defaultImage: payload,
              },
            })
          )
            .then(() => { })
            .catch((error) => {
              console.error("Error updating user:", error);
            })
            .finally(() => {
              disableLoader();
            });
        };
        reader.readAsDataURL(file);
      }
    };

    return (
      <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
        <ProfileCompletenessHeader
          title="Profile Photo"
          profileCompleteness={sessionInfo.data['profileCompleteness']}
          loading={sessionInfo.loading}
          onBackClick={()=>dispatch(decrementProfileActivationStep())}
        />
        <div style={{ maxWidth: "100%", width: "auto", textWrap: "wrap", paddingInline:isMobile && "15px" }}>
          <h1
            className="p-0 m-0 txt-clr flex-wrap font-medium line-height-1"
            style={{ fontSize: "16px" }}
          >
            Families and helpers are required to have a profile photo, most
            helpers choose a photo of themselves doing the job they are good at.
          </h1>
        </div>
        <div
          className="mt-2"
          style={{ maxWidth: "100%", width: "auto", textWrap: "wrap" , paddingInline:isMobile && "15px"  }}
        >
          <h1
            className="p-0 m-0 txt-clr flex-wrap font-medium line-height-1"
            style={{ fontSize: "16px" }}
          >
            Your photo is only visible to other Juggle Street users. It cannot be
            found by somebody browsing the web.
          </h1>
        </div>

        <div className={`${styles.contentRight} mt-3`}>
          <div className={styles.container}>
            <div
              className=""
              style={{ display: "flex", justifyContent: "center" }}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                id="image-upload"
                style={{ display: "none" }}
              />
              {!isImageUploaded ? (
                <label htmlFor="image-upload">
                  <div
                    style={{
                      width: "200px",
                      height: "200px",
                      borderRadius: "50%",
                      backgroundColor: "#F0F4F7",
                      cursor: "pointer",
                      border: "1px solid rgba(255,165,0,1)",
                    }}
                  >
                    <img
                      src={Userimage}
                      alt=""
                      style={{
                        width: "100px",
                        height: "100px",
                        backgroundColor: "#F0F4F7",
                        cursor: "pointer",
                      }}
                      className="mt-6 ml-6"
                    />
                    <span
                      style={{
                        backgroundColor: "white",
                        color: "#585858",
                        boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                        fontSize: "12px",
                        fontWeight: "600",
                        padding: "6px",
                        paddingLeft: "20px",
                        paddingRight: "20px",
                        marginLeft: "10%",
                        position: "relative",
                        top: "16px",
                        borderRadius: "8px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        width: "152px",
                        textWrap: "nowrap",
                      }}
                    >
                      <HiMiniCamera style={{ fontSize: "16px" }} />
                      &nbsp;Upload Photo
                    </span>
                  </div>
                </label>
              ) : (
                <ReactCrop
                  crop={crop}
                  onChange={(_, percentCrop) => setCrop(percentCrop)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={1}
                  // circularCrop
                  // minWidth={30}
                  // minHeight={30}
                  style={{ objectFit: 'contain' }}
                >
                  <img
                    ref={imgRef}
                    src={selectedImage}
                    alt="Selected"
                    crossOrigin="anonymous"
                    onLoad={onImageLoad}
                    style={{
                      backgroundColor: "#F0F4F7",
                      cursor: "pointer",
                    }}
                  />
                </ReactCrop>
              )}
            </div>
            {isImageUploaded && (
              <div className={styles.resultSection}>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    marginTop: "10px",
                    zIndex: 999,
                    position: 'relative'
                  }}
                >
                  <CustomButton
                    label="Cancel"
                    onClick={handleCancel}
                    style={{
                      marginRight: "10px",
                      backgroundColor: "transparent",
                      color: "#585858",
                      height: "39px",
                      fontSize: "14px",
                      fontWeight: "500",
                      boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                    }}
                  />
                  <CustomButton
                    label="Save photo"
                    onClick={handleSavePhoto}
                    style={{ backgroundColor: "#1F9EAB" }}
                  />
                </div>
              </div>
            )}

          </div>
        </div>
        <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
          <CustomButton
            label={
              <>
                <i className="pi pi-angle-left"></i>
                Previous
              </>
            }
            onClick={handleprev}
            style={{
              backgroundColor: "transparent",
              color: "#585858",
              width: "156px",
              height: "39px",
              fontSize: "14px",
              fontWeight: "500",
              margin:isMobile && "5px"
            }}
          />
          <div style={{ flexGrow: 1 }} />
          {/* <CustomButton
          className={styles.hoverClass}
          onClick={handleSavePhoto}
          label="Next"
          style={{
            backgroundColor: "#FFA500",
            color: "#fff",
            width: "156px",
            height: "39px",
            fontWeight: "800",
            fontSize: "14px",
            borderRadius: "8px",
            border: "2px solid transparent",
            boxShadow: "0px 4px 12px #00000",
            transition: "background-color 0.3s ease, box-shadow 0.3s ease",
          }}
        /> */}
          <CustomButton
            className={styles.hoverClass}
            data-skip={selectedImage ? "false" : "true"}
            onClick={() => {
              if (selectedImage) {
                const payload = {
                  ...(sessionInfo.data as object),
                  id: sessionInfo.data["defaultImage"]["id"],
                  imageBinary: selectedImage,
                  isCropped: true, // Changed to true
                  scale1ImageUrl:
                    sessionInfo.data["defaultImage"]["scale1ImageUrl"],
                  scale2ImageUrl:
                    sessionInfo.data["defaultImage"]["scale2ImageUrl"],
                  scale3ImageUrl:
                    sessionInfo.data["defaultImage"]["scale3ImageUrl"],
                };
                enableLoader();
                dispatch(updateUser({ payload: payload })).finally(() => {
                  disableLoader();
                  dispatch(incrementProfileActivationStep());
                });
              } else {
                dispatch(incrementProfileActivationStep());
              }
            }}
            label={
              <>
                {selectedImage ? "Next" : "Skip"}
                <i
                  className={`pi pi-angle-${selectedImage ? "right" : "right"}`}
                  style={{ marginLeft: "8px" }}
                ></i>
              </>
            }
            style={
              selectedImage
                ? {
                  backgroundColor: "#FFA500",
                  color: "#fff",
                  width: "156px",
                  height: "39px",
                  fontWeight: "800",
                  fontSize: "14px",
                  borderRadius: "8px",
                  border: "2px solid transparent",
                  boxShadow: "0px 4px 12px #00000",
                  transition:
                    "background-color 0.3s ease, box-shadow 0.3s ease",
                   margin : isMobile && "10px"
                }
                : {
                  backgroundColor: "transparent",
                  color: "#585858",
                  width: "156px",
                  height: "39px",
                  fontWeight: "400",
                  fontSize: "14px",
                  borderRadius: "10px",
                  border: "1px solid #F0F4F7",
                   margin : isMobile && "10px"
                }
            }
          />
        </footer>
      </div>
    );
  };

export default AddProfilePhoto;
