.customFooter {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff; /* Footer background */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Shadow effect */
    padding: 10px 0; /* Padding for the footer */
    text-align: center;
    z-index: 999; /* Stay on top of other elements */
    margin-top: auto;
  }
  
  .customButton {
    padding: 10px 20px;
    width: 80%; /* Full width of button */
    height: 45px;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    background-color: #ffa500; /* Orange button color */
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    max-width: 270px;
  }
  
  /* Style for disabled button */
  .customButton:disabled {
    background-color: #f1f1f1;
    color: #585858;
    cursor: not-allowed;
    font-size: 14px;
    font-weight: 700;
  }
  