import styles from '../../../../Common/styles/invited-candidates-card.module.css';
import { SlLocationPin } from 'react-icons/sl';
import { IoStarSharp } from 'react-icons/io5';
import useIsMobile from '../../../../../hooks/useIsMobile';
import environment from '../../../../../helper/environment';


interface ProfileCardProps {
    name: string;
    location: string;
    jobsCompleted: number;
    imageUrl: string;
    isSelected: boolean; // Always true for selected items
    onClick?: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({
    name,
    location,
    jobsCompleted,
    imageUrl,
    isSelected,
    onClick,
}) => {
    const { isMobile } = useIsMobile();
    return !isMobile ? (
        <div className={`${styles.card} cursor-pointer`} onClick={onClick}>
            <img
                src={`${environment.getStorageURL(window.location.hostname)}/images/${imageUrl}`}
                alt={`${name}'s profile`}
                className={`${styles.profileImageTik} ml-1 cursor-pointer`}
            />
            <div className={styles.infoTik}>
                <h2 className={styles.name}>{name}</h2>
                <div className={styles.location}>
                    <SlLocationPin color='#37A950' fontSize={'12px'} />
                    {location}
                </div>
                <div className={styles.jobs}>
                    <IoStarSharp color='#FFA500' fontSize={'12px'} /> {jobsCompleted} Jobs complete
                </div>
            </div>
            <div className='pr-1' style={{ marginTop: '-55px' }}>
                <button
                    className={`${styles.selectButtonTik} ${isSelected ? styles.selectedTik : ''}`}
                    disabled // Prevent user interaction
                >
                    {isSelected && <span></span>}
                </button>
            </div>
        </div>
    ) : (
        <div className={styles.cardMobile}>
            <img
                src={`${environment.getStorageURL(window.location.hostname)}/images/${imageUrl}`}
                alt={`${name}'s profile`}
                className={styles.profileImage}
            />
            <div className={styles.info}>
                <h2 className={styles.nameMobile}>{name}</h2>
                <div className={styles.locationMobile}>
                    <SlLocationPin color='#37A950' fontSize={'18px'} />
                    {location}
                </div>
                <div className={styles.jobsMobile}>
                    <IoStarSharp color='#FFA500' fontSize={'18px'} /> {jobsCompleted} Jobs 
                </div>
            </div>
            <button
                className={`${styles.selectButton} ${isSelected ? styles.selected : ''}`}
                disabled // Prevent user interaction
            >
                Selected ✓
            </button>
        </div>
    );
};

export default ProfileCard;
