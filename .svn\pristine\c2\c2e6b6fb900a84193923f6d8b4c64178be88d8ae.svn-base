import React, { useEffect, useState } from "react";
import childrenIcon from "../../../../assets/images/Icons/my_child.png";
import manageJobsIcon from "../../../../assets/images/Icons/manage_job.png";
import { IoHomeOutline } from "react-icons/io5";
import { FaArrowLeft } from "react-icons/fa";
import styles from "../../../../containers/Common/styles/awaitingConfirmationCard.module.css";
import EditTimesheet from "./EditTimesheet";
import Doller from "../../../../assets/images/Icons/Dollar1.png"
import CookiesConstant from "../../../../helper/cookiesConst";
import utils from "../../../../components/utils/util";
import c from "../../../../helper/juggleStreetConstants";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";

interface TimesheetRow {
    start: any;
    finish: any;
    hours: number;
    rate: any;
    total: number;
    originalId?: string;
    isOriginal?: boolean;
    editVersion?: number;
}

interface AwaitingConfirmationCardProps {
    // NEW: Add required IDs for the API payload
    timesheetId?: number;
    jobId?: number;
    applicantId?: number;
    userId?: number; // For the 'ModifiedBy' field

    profileName: string;
    profileImage: string;
    jobType: string;
    jobDate: string; // Assuming format is like "25th December 2023"
    jobAddress: string;
    baseRate: number;
    extraHoursRate: number;
    initialTimesheetRows: TimesheetRow[];
    statusText?: string;
    onSubmit?: () => void;
    onGoBack?: () => void;
}

const AwaitingConfirmationCard: React.FC<AwaitingConfirmationCardProps> = ({
    timesheetId,
    jobId,
    applicantId,
    userId,

    profileName,
    profileImage,
    jobType,
    jobDate,
    jobAddress,
    baseRate,
    extraHoursRate,
    initialTimesheetRows,
    statusText = "Awaiting Your Confirmation",
    onSubmit,
    onGoBack,
}) => {

    const [submitted, setSubmitted] = useState(false);
    const handleSubmit = () => {
        setSubmitted(true);
        onSubmit?.();
    };

    const [timesheetRows, setTimesheetRows] = useState<TimesheetRow[]>(
        initialTimesheetRows.map(row => ({ ...row, isOriginal: false, editVersion: 0 }))
    );
    const [showEditPage, setShowEditPage] = useState(false);
    const [editVersionCounter, setEditVersionCounter] = useState(1);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const isParent = clientType === c.clientType.INDIVIDUAL;
    const isBusiness = clientType === c.clientType.BUSINESS;
    const showApprove = isParent || isBusiness;
    const totalAmount = timesheetRows
        .filter(row => !row.isOriginal)
        .reduce((sum, row) => sum + row.total, 0);

    const handleSaveShifts = (updatedShifts: { start: string; finish: string }[]) => {
        const currentActiveRows = timesheetRows.filter(row => !row.isOriginal);
        const previousOriginalRows = timesheetRows.filter(row => row.isOriginal);
        const newRows: TimesheetRow[] = [];

        updatedShifts.forEach((updatedShift, index) => {
            const currentRow = currentActiveRows[index];

            if (currentRow) {
                const wasChanged = currentRow.start !== updatedShift.start ||
                    currentRow.finish !== updatedShift.finish;

                if (wasChanged) {
                    const clonedOriginal = JSON.parse(JSON.stringify(currentRow));
                    newRows.push({
                        ...clonedOriginal,
                        isOriginal: true,
                        editVersion: editVersionCounter - 1
                    });

                    newRows.push({
                        start: updatedShift.start,
                        finish: updatedShift.finish,
                        hours: 0, // These will be recalculated
                        rate: 0,
                        total: 0,
                        isOriginal: false,
                        editVersion: editVersionCounter
                    });
                } else {
                    newRows.push({
                        ...currentRow,
                        editVersion: editVersionCounter
                    });
                }
            } else {
                newRows.push({
                    start: updatedShift.start,
                    finish: updatedShift.finish,
                    hours: 0,
                    rate: 0,
                    total: 0,
                    isOriginal: false,
                    editVersion: editVersionCounter
                });
            }
        });

        // NOTE: Here you should ideally recalculate hours and totals for the new/changed rows
        // For simplicity, this is omitted but is important for a full implementation.

        setTimesheetRows([
            ...previousOriginalRows,
            ...newRows
        ]);

        setEditVersionCounter(prev => prev + 1);
        setShowEditPage(false);
    };

    if (showEditPage) {
        const activeShiftsForEdit = timesheetRows
            .filter(row => !row.isOriginal)
            .map(({ start, finish }) => ({ start, finish }));

        return (
            <EditTimesheet
                day="Monday,"
                date={jobDate}
                profileImage={profileImage}
                profileName={profileName}
                baseRate={baseRate}
                extraRate={extraHoursRate}
                initialShifts={activeShiftsForEdit}
                onClose={() => setShowEditPage(false)}
                onSave={handleSaveShifts}
            />
        );
    }
    const sortedRows = [...timesheetRows].sort((a, b) => {
        if (a.isOriginal && !b.isOriginal) return -1;
        if (!a.isOriginal && b.isOriginal) return 1;
        if (a.isOriginal && b.isOriginal) return (a.editVersion || 0) - (b.editVersion || 0);
        return 0;
    });
    const getDateTimeFromString = (dateStr: string, timeStr: string): Date | null => {
        // This is a basic parser. For more robust parsing, consider a library like date-fns.
        const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})\s*(am|pm)/i);
        if (!timeMatch) return null;

        // First, try to parse the date string.
        const cleanDateStr = dateStr.replace(/(\d+)(st|nd|rd|th)/, '$1');
        const date = new Date(cleanDateStr);

        // CHANGE: Add validation to ensure the date was parsed correctly.
        if (isNaN(date.getTime())) {
            console.error("Failed to parse date string:", dateStr);
            return null;
        }

        let hours = parseInt(timeMatch[1], 10);
        const minutes = parseInt(timeMatch[2], 10);
        const period = timeMatch[3].toLowerCase();

        if (period === 'pm' && hours < 12) {
            hours += 12;
        }
        if (period === 'am' && hours === 12) { // Midnight case: 12am is 00 hours
            hours = 0;
        }

        date.setHours(hours, minutes, 0, 0);
        return date;
    };

    
    return (
        <>
            <div className={styles.headerWrapper}>
                {submitted ? (
                    <div className={styles.confirmedHeader}>
                        <div className={styles.inlineContainer}>
                            <div className={styles.head}>
                                <div className={styles.confirmRow}>
                                    <div className={styles.confirmedIconCircle}>
                                        <span className={styles.checkIcon}>✔</span>
                                    </div>
                                </div>
                                <div className={styles.headerConfirmedTitle}>Confirmed</div>
                            </div>

                            <div className={styles.confirmedSubtitle}>
                                Thanks for confirming your Timesheet
                            </div>
                        </div>
                    </div>
                ) : (
                    <button className={styles.backBtn} onClick={onGoBack}>
                        <span className={styles.arrowCircle}>
                            <span className={styles.arrow}><FaArrowLeft /></span>
                        </span>
                        Go back
                    </button>
                )}
            </div>

            <div className={`${styles.card} ${submitted ? styles.cardSubmitted : ""}`}>
                <div className={styles.headerSection}>
                    <div>
                        <h3 className={styles.title}>Review Timesheet</h3>

                        <div className={styles.status}>
                            Status: <span className={styles.statusHighlight}>
                                {submitted ? "Awaiting Craig's Approval" : statusText}
                            </span>
                        </div>
                    </div>
                    <div className={styles.profileContainer}>
                        <img
                            src={profileImage}
                            alt="Profile"
                            className={styles.avatar}
                        />
                        <div className={styles.profileName}>{profileName}</div>
                    </div>
                </div>

                <div className={styles.info}>
                    <div className={styles.infoBlock}>
                        <div className={styles.row}>
                            <img src={childrenIcon} alt="My Children" className={styles.rowIcon} />
                            <div>{jobType}</div>
                        </div>
                        <div className={styles.row}>
                            <img src={manageJobsIcon} alt="Manage My Jobs" className={styles.rowIcon} />
                            <div>{jobDate}</div>
                        </div>
                        <div className={styles.row}>
                            <IoHomeOutline className={styles.rowIcon} />
                            <div>{jobAddress}</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />

                    <div className={styles.rateBlock}>
                        <img src={Doller} alt="dollar" className={styles.rowIcon} />
                        <div className={styles.rateText}>
                            <div>Base Rate: ${baseRate} per hour</div>
                            <div className={styles.indented}>Extra Hours Rate: ${extraHoursRate} per hour</div>
                        </div>
                    </div>

                    <hr className={styles.hrFull} />
                </div>

                {!submitted && timesheetRows.some(row => row.isOriginal) && (
                    <div className={styles.adjustedTimesheetBlock}>
                        <div className={styles.adjustedHeader}>
                            <span className={styles.adjustedTitle}>Adjusted Timesheet</span>
                            <span className={styles.adjustedIcon}>?</span>
                        </div>
                        <div className={styles.adjustedDescription}>
                            You have adjusted the hours worked. Please review changes and then "Approve".
                        </div>
                        <hr className={styles.hrFull} />
                    </div>

                )}

                <div className={styles.timesheetContainer}>
                    <div className={styles.rowHeader}>
                        <div className={styles.column}>Start</div>
                        <div className={styles.column}>Finish</div>
                        <div className={styles.column}>Hours</div>
                        <div className={styles.column}>Rate</div>
                        <div className={styles.column}>Total</div>
                    </div>

                    {sortedRows.map((row, index) => (
                        <div
                            key={`${index}-${row.editVersion}-${row.isOriginal}`}
                            className={`${styles.rowData} ${row.isOriginal ? styles.originalRow : ''}`}
                            style={row.isOriginal ? {
                                color: '#ff6359',
                                textDecoration: 'line-through',
                                opacity: 0.7,
                            } : {}}
                        >
                            <div className={styles.column}>{row.start}</div>
                            <div className={styles.column}>{row.finish}</div>
                            <div className={styles.column}>{row.hours}</div>
                            <div className={styles.column}>${row.rate}</div>
                            <div className={styles.column}>${row.total}</div>
                        </div>
                    ))}


                    <hr className={styles.hr} />

                    <div className={styles.totalRow}>
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={styles.column} />
                        <div className={`${styles.column} ${styles.totalAmount}`}>${totalAmount}</div>
                    </div>

                    <hr className={styles.hr} />
                </div>

                {submitted && (
                    <div className={styles.rowAlign}>
                        <div className={styles.textBlock}>
                            <p className={styles.pendingTitle}>Pending Craig's Approval</p>
                            <p className={styles.pendingSub}>
                                Awaiting for Craig S to approve your timesheet
                            </p>
                        </div>
                        <div className={styles.profileContainerConfirm}>
                            <div className={styles.avatarWrapper}>
                                <img src={profileImage} className={styles.avatar} alt="profile" />
                                <div className={styles.redDot}></div>
                            </div>
                            <div className={styles.profileName1}>{profileName}</div>
                        </div>

                    </div>
                )}

                <div className={styles.footer}>
                    {!submitted ? (
                        <>
                            <div className={styles.section}>
                                <div className={styles.sectionText}>
                                    {showApprove ? "Approve Timesheet" : "Confirm Timesheet"}
                                    <div className={styles.subText}>
                                        {showApprove
                                            ? "Approve the submitted timesheet if it reflects the actual job performed."
                                            : "The Timesheet is an accurate record of the job."}
                                    </div>
                                </div>
                                <button className={styles.submitBtn} onClick={handleSubmit}>{showApprove ? "Approve" : "Submit"}</button>
                            </div>
                            <div className={`${styles.section} ${styles.editSection}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make adjustments to the Timesheet.</div>
                                </div>
                                <button className={styles.editBtn} onClick={() => setShowEditPage(true)}>Edit Timesheet</button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className={styles.confirmedBox}>
                                <p className={styles.confirmedTitle}>Timesheet Confirmed</p>
                                <p className={styles.confirmedSubText}>This Timesheet will now be submitted to the parent for approval</p>
                            </div>

                            <div className={`${styles.section} ${styles.editSection} ${submitted ? styles.disabledBlock : ""}`}>
                                <div className={styles.sectionText}>
                                    <strong className={styles.titlee}>Edit Timesheet</strong>
                                    <div className={styles.subText}>Make an adjustment to the actual hours worked</div>
                                </div>
                                <button className={styles.editDisabledBtn} disabled>Edit Timesheet</button>
                            </div>

                            <button className={styles.nextBtn}>Next</button>
                        </>
                    )}
                </div>
            </div>
        </>
    );
};

export default AwaitingConfirmationCard;