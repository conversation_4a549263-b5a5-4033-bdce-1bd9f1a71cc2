.container {
background: var(--Juggle-street-background-colour, #F0F4F7);
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  height: 100%;
  box-shadow: 0 4px 10px #00000020;
}

.header {
  height: 60px;
  background: linear-gradient(90deg, #FFA500 0%, #37A950 100%);
}

.successBox {
  text-align: center;
  background-color: #fff;
  border: 1px solid #179D52;
  border-radius: 20px;
  margin: 0 16px;
  margin-top: 25px;
}

.iconWrapper {
  position: relative;
  width: 80px;
  height: 80px;
  margin: auto;
}

.ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #c8e6c9;
  border-radius: 50%;
  animation: ripple 2s infinite ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  70% {
    transform: scale(1.4);
    opacity: 0;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.successText {
  font-size: 16px;
  color: #585858;
 font-weight: 400;
 margin-bottom: 0;
}

.amount {
  font-size: 30px;
  font-weight: 700;
  color: #585858;
  margin-block: 8px;

}

.detailCard {
  background-color: #fff;
  margin: 16px;
  padding: 14px 20px;
  border-radius: 20px;

}
.detailText{
    font-size: 16px;
    font-weight: 600;
    color: #585858;
    margin-top: 0;
}

.label {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #585858;
  font-weight: 400;
  margin-bottom: 10px;
}

.value {
  font-weight: 600;
  color: #585858;
  font-size: 14px;
}

.complete {
  font-weight: 600;
  color: #28a745;
  display: flex;
  align-items: center;
  gap: 4px;
}

.downloadBtn {
  margin: 0 16px;
  padding: 12px;
  width: calc(100% - 32px);
  border: 2px solid #585858;
  background-color: white;
  box-shadow: 0px 4px 4px 0px #00000040;
  color: #585858;
  font-weight: 400;
  font-size: 14px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.footer {
  text-align: center;
  padding: 16px;
}

.doneBtn {
  width: 100%;
  padding: 12px;
  background-color: #fca311;
  color: white;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.tickWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  margin: 20px auto;
}

.outerCircle {
  fill: #c8e6c9;
}

.innerCircle {
  fill: #37a950;
  transform-origin: 50% 50%;
  animation: heartbeat 1.2s ease-in-out infinite;
}

.checkmark {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: draw 0.8s ease-out forwards;
  animation-delay: 0.2s;
}

@keyframes draw {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.08);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.06);
  }
}