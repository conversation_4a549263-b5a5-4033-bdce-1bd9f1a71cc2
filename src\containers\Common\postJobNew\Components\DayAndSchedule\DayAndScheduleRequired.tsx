import useIsMobile from "../../../../../hooks/useIsMobile";
import { useJobManager } from "../../provider/JobManagerProvider";
import DaysAndSchedule from "./DaysAndSchedule";
import DaysAndScheduleMobile from "./DaysAndScheduleMobile";

function DayAndScheduleRequired() {
  const { isMobile } = useIsMobile();
  return isMobile ? <DayAndScheduleRequiredMobile /> : <DayAndScheduleRequiredWeb />;
}

export default DayAndScheduleRequired;
const useJobTypeHook = () => {
  const { next, payload, prev, setpayload, steps } = useJobManager();
  return {
    next,
    payload,
    prev,
    setpayload,
    steps,
  };
};

const DayAndScheduleRequiredWeb = () => {
  const { next, payload, prev, setpayload } = useJobTypeHook();
  return <DaysAndSchedule  key={JSON.stringify(payload)} payload={payload} next={next} prev={prev} setpayload={setpayload} />;
};
const DayAndScheduleRequiredMobile = () => {
  const { next, payload, prev, setpayload } = useJobTypeHook();
  return <DaysAndScheduleMobile currentPayload={payload} nextClicked={next} prevClicked={prev} setPayload={setpayload} />;
};
