.childCareContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0px;
  justify-content: center;
  flex-direction: column;
  padding-right: 50px;
}

.childCareBoxOne {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding-left: 25px;
  padding-top: 12px;
  color: #585858;
  padding-right: 20px;
  padding-bottom: 30px;
}

.childCareBoxTwo {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 25px;
  padding-top: 15px;
}
.childCareBoxThree {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #585858;
  padding-bottom: 25px;
  padding-top: 15px;
}
.childCareBoxFour {
  width: 100%;
  height: 150px;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding: 20px;
  display: flex;
  padding: 20px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
}
.childCareBoxFive {
  width: 100%;
  height: min-content;
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid #f1f1f1;
  border-radius: 30px;
  padding-left: 25px;
  padding-top: 25px;
  color: #585858;
  padding-bottom: 30px;
}
.childCare {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  line-height: 30px;
  margin: 0px;
}
.childCareExperience {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
  margin-top: 10px;
}
.row {
  display: flex;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  align-items: center;
  gap: 8px;
  text-wrap: nowrap;
}

.tick::after {
  content: "✔"; /* Tick mark */
  color: green;
  font-size: 16px;
  font-weight: bold;
}
.Nationality {
  display: flex;
  width: min-content;
  background-color: #f1f1f1;
  border: none;
  border-radius: 20px;
  padding: 10px 18px 10px 18px;
  gap: 6px;
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  color: #585858;
}
.checks {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #585858;
  margin: 0px;
}
.ChecksRow {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: #585858;
}
.qualificationContainer {
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
  padding-bottom: 25px;
}
.qualificationContainerTutor {
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
}
.qualificationContainerTutorSec {
  width: 100%;
  height: min-content;
  padding-left: 25px;
  padding-top: 15px;
  padding-right: 20px;
}
.checkboxInput {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #585858;
  border-radius: 3px;
  cursor: pointer;
  position: relative;
  background-color: white;
  transition: all 0.3s ease;
}

.checkboxInput:checked {
  background-color: #179d52;
  border-color: #179d52;
}

.checkboxInput:checked::after {
  content: "✓";
  color: white;
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.tag {
  margin: 0px;
  font-weight: 700;
  font-size: 20px;
  line-height: 30px;
  color: #179d52;
}
.tagSec {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  line-height: 21px;
  margin: 0px;
  margin-top: 10px;
}
.tagPara {
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: #585858;
  margin-bottom: 0px;
}
.learnMore {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 10px;
  font-weight: 300;
  cursor: pointer;
  text-decoration: underline;
}
.learnMore:hover {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 10px;
  font-weight: 300;
  cursor: pointer;
  text-decoration: underline;
}
.DialogContent {
  background-color: #ffff;
  width: 704px;
  height: 630px;
  border-radius: 33px;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
}
.DialogTag {
  font-size: 40px;
  font-weight: 700;
  color: #585858;
  line-height: 60px;
  margin: 0px;
}
.DialogTagPara {
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  line-height: 21px;
  margin-top: 0px;
}
.CloseBtn {
  display: flex;
  justify-content: end;
  position: absolute;
  right: 0%;
  width: 30px;
  height: 32px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  top: -1%;
  cursor: pointer;
}
.DialogOptions{
  padding-left: 80px;
  width: 600px;
}
.tagTwo{
  color: #585858;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
  text-align: center;
  margin-top: 0px;
}
.checkboxLabel{
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  line-height: 21px;
}
.DialogContentMobile {
  background-color: #ffff;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  position: fixed;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  left: 0;
  bottom: 0;
  height: 450px;
  overflow-y: auto;
}
.DialogOptionsMobile{
  padding-inline: 40px;
}