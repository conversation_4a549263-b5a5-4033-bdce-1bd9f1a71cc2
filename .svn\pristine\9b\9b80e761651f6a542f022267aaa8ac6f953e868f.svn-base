import "./App.css";
import Loader from "./commonComponents/Loader";
import ProfileActivation from "./containers/Common/ProfileActivation";
import Routes from "./Routes";
import useIsMobile from "./hooks/useIsMobile";
import environment from "./helper/environment";
import AdminNav from "./commonComponents/AdminNav";
<meta name="api-url" content="%VITE_VERSION%" />;

function App() {
  const { isMobile } = useIsMobile();

  return (
    <>
      <AdminNav />
      <Loader />
      <Routes />
      <ProfileActivation />
    </>
  );
}

export default App;
