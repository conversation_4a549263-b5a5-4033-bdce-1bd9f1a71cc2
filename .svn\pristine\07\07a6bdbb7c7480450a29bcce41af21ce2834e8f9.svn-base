import { BrowserRouter, Routes, Route, useNavigate } from 'react-router-dom';
import Login from './components/Auth/Login/Login';
import Signup from './components/Auth/Signup/Signup';
import ForgotPassword from './components/Auth/ForgotPassword/ForgotPassword';
import Cards from './common-components/card';
import ResetPassword from './components/Auth/ResetPassword/ResetPassword';
import utils from './components/utils/util';
import { JoinNowProvider } from './model/JoinNowContext';
import c from './helper/juggle_street_constants';
import Joinnow from './components/Auth/Signup/joinnow';
import { Layout } from './containers/Layout';
import { Welcome } from './containers/Welcome';
import Home from './containers/home';
import PostJobPage from './containers/postjob';
import Myjob from './containers/Myjob';



function RoleBasedJoinnow({ selectedRole, nextPath }) {
    const navigate = useNavigate();

    const onNext = () => {
        // Navigate based on the selectedRole if necessary
        if (selectedRole === c.lookingFor.BUSINESS_CLIENT) {
            navigate("/business-details");
        } else {
            navigate(nextPath);
        }
    };

    return <Joinnow signUpAs={selectedRole} onNext={onNext} setCurrentPage={function (): void {
        throw new Error('Function not implemented.');
    }} />;
}
const route = () => {

    const token = utils.getCookie("jugglestreet-access-token");
    if (token) {
    }
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/" element={
                    <Cards className="custom-card-width">
                        <Login />
                    </Cards>
                } />
                <Route path="/login" element={
                    <Cards className="custom-card-width">
                        <Login />
                    </Cards>
                } />
                <Route path="/register" element={
                    <Cards>
                        <Signup />
                    </Cards>
                } />
                <Route path="/forgot-password" element={
                    <Cards>
                        <ForgotPassword />
                    </Cards>
                } />
                <Route path="/reset-password" element={
                    <Cards>
                        <ResetPassword />
                    </Cards>
                } />
                <Route path="/my-job" element={<Myjob/>} />
                <Route path="/post-job" element={<PostJobPage />} />  {/* Add route for PostJob */}

                <Route path="/home" element={<Layout />}>
                    <Route path="" element={<Home/>} />


                   
                    
                

                </Route>

                <Route path='join-now' element={<JoinNowProvider><Cards ><Signup /></Cards > </JoinNowProvider>} />
                <Route path="signup-family" element={<JoinNowProvider><Cards ><RoleBasedJoinnow selectedRole={c.lookingFor.INDIVIDUAL_CLIENT} nextPath="/join-now2" /></Cards></JoinNowProvider>} />
                <Route path="signup-business" element={<JoinNowProvider><Cards><RoleBasedJoinnow selectedRole={c.lookingFor.BUSINESS_CLIENT} nextPath="/business-details" /></Cards></JoinNowProvider>} />
                <Route path="signup-helper" element={<JoinNowProvider><Cards><RoleBasedJoinnow selectedRole={c.lookingFor.INDIVIDUAL_PROVIDER} nextPath="/join-now2" /></Cards></JoinNowProvider>} />

            </Routes>
        </BrowserRouter>
    );
}
export default route;
