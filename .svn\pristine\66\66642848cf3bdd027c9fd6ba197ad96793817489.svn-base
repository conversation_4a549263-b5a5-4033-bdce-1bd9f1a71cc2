import React, { useState } from "react";
import styles from "./styles/business-introduction.module.css";
import { ProgressBar } from "primereact/progressbar";
import { Dropdown } from "primereact/dropdown";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import { InputTextarea } from "primereact/inputtextarea";
import CustomButton from "../../commonComponents/CustomButton";
import { updateUser } from "../../store/tunks/sessionInfoTunk";
import { decrementProfileActivationStep, incrementProfileActivationStep } from "../../store/slices/applicationSlice";
import useLoader from "../../hooks/LoaderHook";
import useIsMobile from "../../hooks/useIsMobile";
import HorizontalNavigation from "../Common/HorizontalNavigationMobile";
import { InputText } from "primereact/inputtext";

const BusinessIntroduction: React.FC = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { disableLoader, enableLoader } = useLoader()
  const applicationSate = useSelector<RootState>((state) => state.applicationState.profileActivationCurrentStep);
  const { isMobile } = useIsMobile()
  // Dropdown State Variables
  const [selectedYear, setSelectedYear] = useState(
    sessionInfo.data["establishYear"] || ""
  );
  const [inputValue, setInputValue] = useState(
    sessionInfo.data["aboutMe"] || ""
  );
  const [selectedEmployeesRange, setSelectedEmployeesRange] = useState(
    sessionInfo.data["numberOfMembers"] || ""
  );
  const [selectedGeneral, setSelectedGeneral] = useState(
    sessionInfo.data["industrySectorId"] || ""
  );

  const currentYear = new Date().getFullYear();
  const [accountIdentifier, setAccountIdentifier] = useState(sessionInfo.data["industrySectorText"] || "");
  const [isFocused, setIsFocused] = useState(false);
  const [industryError, setIndustryError] = useState(false);
  const [introductionError, setIntroductionError] = useState(false);
  // Generate Year Options up to Current Year
  const yearOptions = Array.from(
    { length: currentYear - 2000 + 1 },
    (_, i) => ({
      label: (2000 + i).toString(),
      value: 2000 + i,
    })
  ).reverse();  // Reverse the array to make it in descending order

  const industrySectorsList = [
    { label: "Accommodation - Hotels", value: 101 },
    { label: "Accommodation - Property Operators & Agents", value: 102 },
    { label: "Childcare - Preschool Childcare Centre", value: 201 },
    { label: "Childcare - Before & After School Centre", value: 202 },
    { label: "Childcare - School Holiday Provider", value: 203 },
    { label: "Childcare - Online Childcare Platform", value: 204 },
    { label: "Childcare - Service Provider", value: 205 },
    { label: "Health - Health Care Provider", value: 401 },
    { label: "Health - Recreation & Amusement Activities", value: 402 },
    { label: "Health - Sports & Physical Activities", value: 403 },
    { label: "Recruitment - Childcare", value: 501 },
    { label: "Recruitment - Tutoring", value: 502 },
    { label: "Other ", value: 1001 },
  ];

  const numberOfMembersList = [
    { label: "1 - 5", value: 5 },
    { label: "5 - 10", value: 10 },
    { label: "10 - 20", value: 20 },
    { label: "20 - 50", value: 50 },
    { label: "Over 50", value: 100 },
  ];
  const handleNext = () => {
    let hasError = false;

    // Validate "Specify Industry"
    if (
      sessionInfo.data?.['client']?.clientCategory === 2 &&
      accountIdentifier.trim() === ""
    ) {
      setIndustryError(true);
      hasError = true;
    } else {
      setIndustryError(false);
    }

    // Validate "Business Introduction"
    if (inputValue.trim() === "") {
      setIntroductionError(true);
      hasError = true;
    } else {
      setIntroductionError(false);
    }

    // If there are no errors, proceed to the next step
    if (!hasError) {
      const payload = {
        ...(sessionInfo.data as object),
        aboutMe: inputValue,
        numberOfMembers: selectedEmployeesRange,
        establishYear: selectedYear,
        industrySectorId: selectedGeneral,
        industrySectorText: accountIdentifier || null,
      };

      enableLoader();
      dispatch(updateUser({ payload })).finally(() => {
        disableLoader();
        // dispatch(incrementProfileActivationStep());
      });
    }
  };


  // Handle input changes
  const handleInputChange = (e) => {
    const value = e.target.value;

    // Function to count words in a text
    const countWords = (text) => {
      return text
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
    };

    // Count total words in the current input
    const totalWords = countWords(value);

    // If total words are less than or equal to 120, update input value
    if (totalWords <= 120) {
      setInputValue(value.trim().length > 0 ? value : "");
    }
  };
  const isFormComplete = (): boolean => {

    return (
      selectedYear !== "" &&
      selectedEmployeesRange !== "" &&
      selectedGeneral !== "" &&
      inputValue.trim() !== ""
    );

  };

  const handleprev = () => {
    dispatch(decrementProfileActivationStep());
  }

  return (
    <div className={!isMobile ? `${styles.buisnesscontainer}` : `${styles.buisnesscontainerMobile}`}>
      {/* Header Section */}
      <header className={styles.buisnessheader}>
        {!isMobile ? (
          <h1 className={styles.title}>Business Details</h1>
        ) : (
          <HorizontalNavigation
            title="Business Details"
            onBackClick={() => {
              if (applicationSate === 1) {
                window.location.href = '/';
              } else {
                dispatch(decrementProfileActivationStep());
              }
            }} />
        )}
        <ProgressBar
          value={
            sessionInfo.loading ? 100 : sessionInfo.data["profileCompleteness"]
          }
          className={styles.buisnessprogressbar}
        />
        <p
          style={{
            fontFamily: "Poppins",
            fontWeight: 500,
            fontSize: "14px",
            color: "#585858",
          }}
        >
          Your profile is{" "}
          <span
            style={{
              color: "#179D52",
              fontSize: "18px",
              fontWeight: 700,
            }}
          >
            {sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]}
            % complete.
          </span>
        </p>
      </header>

      {/* Main Section with PrimeFlex for responsive divs */}
      <main className="p-grid p-nogutter">
        <div className="p-col-12 p-md-6">
          <div className={`${styles.buisnessflexItem} p-shadow-2 p-p-3`}>
            {/* Year Dropdown */}
            <div className="p-field mb-3">
              <p
                className="p-mb-0"
                style={{
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Company establishment
              </p>{" "}
              {/* Column Name Above Dropdown */}
              <Dropdown
                id="year"
                value={selectedYear}
                options={yearOptions}
                onChange={(e) => setSelectedYear(e.value)}
                placeholder="Select a Year"
                className={`${styles.buisnessyear} ${currentYear ? styles["green-border"] : ""
                  }`}
              />
              <div className="p-field mb-3">
                <p
                  className="p-mb-0"
                  style={{
                    fontWeight: "600",
                    fontSize: "16px",
                    color: "#585858",
                  }}
                >
                  Number of employees
                </p>{" "}
                {/* Column Name Above Dropdown */}
                <Dropdown
                  id="employees"
                  value={selectedEmployeesRange} // Use state variable for selected value
                  options={numberOfMembersList} // Provide options list
                  onChange={(e) => setSelectedEmployeesRange(e.value)}
                  placeholder="Select Employee Range"
                  className={`${styles.buisnessemployee} ${currentYear ? styles["green-border"] : ""
                    }`}
                />
              </div>
            </div>

            <div className="p-field mb-3">
              <p className={styles.globalParagraphStyle}>Industry sector</p>{" "}
              {/* Column Name Above Dropdown */}
              <Dropdown
                id="general"
                value={selectedGeneral} // Correctly binding to the selected value
                options={industrySectorsList} // Using the predefined options
                onChange={(e) => setSelectedGeneral(e.value)}
                placeholder="Select an Option"
                className={`${styles.buisnessindustrysector} ${currentYear ? styles["green-border"] : ""
                  }`}
              />
              <div className="p-field mb-3">
                {sessionInfo.data?.['client']?.clientCategory === 2 && (
                  <div className="p-field mb-3">
                    <p className={styles.globalParagraphStyle}>
                      Specify Industry
                    </p>
                    <InputText
                      id="username"
                      name="username"
                      value={accountIdentifier}

                      onBlur={() => setIsFocused(false)}
                      onChange={(e) => {
                        const value = e.target.value;
                        setAccountIdentifier(value);
                        if (value.trim() !== "") setIndustryError(false);
                      }}
                      placeholder="Specify Industry"
                      className={`${styles.buisnessindustrysector} ${accountIdentifier ? styles["green-border"] : ""
                        }`}
                    />
                    {industryError && (
                      <p style={{ color: "red", fontSize: "12px" }}>
                        Specify Industry is required.
                      </p>
                    )}
                  </div>
                )}</div>
            </div>
          </div>
        </div>

        <div className="p-col-12 p-md-6">
          <div className={`${styles.buisnessflexsecond} p-shadow-2 p-p-3`}>
            <p className={styles.businessIntroTitle}>Business Introduction</p>
            <p className={styles.businessIntrodiscribe}>
              Please describe your business, but do not include any info that
              will need keeping up-to-date. Do NOT include job details here, you
              add these when you Post a Job.
            </p>

            <InputTextarea
              autoResize
              value={inputValue}
              required
              onChange={(e) => {
                handleInputChange(e);
                if (e.target.value.trim() !== "") setIntroductionError(false);
              }}
              rows={3}
              cols={30}
              className={styles.inputTextareabuisness}
              placeholder="Example - ABC Pilates is a family owned and run business providing individual and group classes for women of all ages since 2014."
            />
            {introductionError && (
              <p style={{ color: "red", fontSize: "12px" }}>
                Business Introduction is required.
              </p>
            )}
          </div>
        </div>
      </main>

      {/* Footer Section */}
      {!isMobile ? (
        <footer className={styles.buisnessfooter}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: "10px",
            }}
          >
            {applicationSate !== 1 &&
              <CustomButton
                label={
                  <>
                    <i
                      className="pi pi-angle-left"
                      style={{ marginRight: "8px" }}
                    ></i>
                    Previous
                  </>
                }
                // disabled={disablePrevious}
                // onClick={handlePreviousStep}
                style={{
                  backgroundColor: "transparent",
                  color: "#585858",
                  width: "156px",
                  height: "39px",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
                onClick={() => handleprev()}
              />
            }


            {/* Skip button */}
            <CustomButton
              className={styles.hoverClass}
              data-skip={isFormComplete ? "true" : "false"}
              label={
                isFormComplete() ? (
                  <>
                    Next
                    <i
                      className="pi pi-angle-right"
                      style={{ marginLeft: "8px" }}
                    ></i>
                  </>
                ) : (
                  <>
                    Skip
                    <i
                      className="pi pi-angle-right"
                      style={{ marginLeft: "8px" }}
                    ></i>
                  </>
                )
              }
              style={
                isFormComplete()
                  ? {
                    backgroundColor: "#FFA500",
                    color: "#fff",
                    width: "156px",
                    height: "39px",
                    fontWeight: "800",
                    fontSize: "14px",
                    borderRadius: "8px",
                    border: "2px solid transparent",
                    boxShadow: "0px 4px 12px #00000",
                    transition:
                      "background-color 0.3s ease, box-shadow 0.3s ease",
                    marginLeft: "auto",

                  }
                  : {
                    backgroundColor: "transparent",
                    color: "#585858",
                    width: "156px",
                    height: "39px",
                    fontWeight: "400",
                    fontSize: "14px",
                    borderRadius: "10px",
                    border: "1px solid #F0F4F7",
                    marginLeft: "auto",

                  }
              }
              onClick={handleNext} // Set the onClick to handleNext function

            />
          </div>
        </footer>
      ) : (
        <footer className={styles.buisnessfooter}>
          {/* Container to align buttons and text */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              position: "fixed",
              bottom: "0",
              left: "0",
              width: "100%",
              backgroundColor: "#fff",
              boxShadow: "0px 0px 8px 0px #00000040"
            }}
          >
            {/* Previous button */}
            {applicationSate !== 1 &&
              <CustomButton
                label={
                  <>
                    <i
                      className="pi pi-angle-left"
                      style={{ marginRight: "8px" }}
                    ></i>
                    Previous
                  </>
                }
                // disabled={disablePrevious}
                // onClick={handlePreviousStep}
                style={{
                  backgroundColor: "transparent",
                  color: "#585858",
                  width: "156px",
                  height: "39px",
                  fontSize: "14px",
                  fontWeight: "500",
                  margin: "5px"
                }}
                onClick={() => handleprev()}
              />
            }
            {/* Skip button */}
            <CustomButton
              className={styles.hoverClass}
              data-skip={isFormComplete ? "true" : "false"}
              label={
                isFormComplete() ? (
                  <>
                    Next
                    <i
                      className="pi pi-angle-right"
                      style={{ marginLeft: "8px" }}
                    ></i>
                  </>
                ) : (
                  <>
                    Skip
                    <i
                      className="pi pi-angle-right"
                      style={{ marginLeft: "8px" }}
                    ></i>
                  </>
                )
              }
              style={
                isFormComplete()
                  ? {
                    backgroundColor: "#FFA500",
                    color: "#fff",
                    width: "156px",
                    height: "39px",
                    fontWeight: "800",
                    fontSize: "14px",
                    borderRadius: "8px",
                    border: "2px solid transparent",
                    boxShadow: "0px 4px 12px #00000",
                    transition:
                      "background-color 0.3s ease, box-shadow 0.3s ease",
                    margin: "5px",
                    marginLeft: "auto",

                  }
                  : {
                    backgroundColor: "transparent",
                    color: "#585858",
                    width: "156px",
                    height: "39px",
                    fontWeight: "400",
                    fontSize: "14px",
                    borderRadius: "10px",
                    border: "1px solid #F0F4F7",
                    margin: "5px",
                    marginLeft: "auto",
                  }
              }
              onClick={handleNext} // Set the onClick to handleNext function

            />
          </div>
        </footer>
      )}
    </div>
  );
};

export default BusinessIntroduction;
