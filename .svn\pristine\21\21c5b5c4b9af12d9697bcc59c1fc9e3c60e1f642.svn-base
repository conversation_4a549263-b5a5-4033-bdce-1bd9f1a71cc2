import React, { useState, useEffect } from "react";
import styles from "../styles/receive-chat.module.css";
import sample from "../../../assets/images/sample_profile.png";
import {
  ChatSession,
  Participant,
} from "../../Parent/InAppChat/InAppChatTypes";
import environment from "../../../helper/environment";
import { useNavigate } from "react-router-dom";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";
import useIsMobile from "../../../hooks/useIsMobile";
interface ReceiveChatProps {
  chatSessions: ChatSession[];
  onChatSelect: (chatId: string) => void;
  selectedChatId: string | undefined;
}
const ReceiveChat: React.FC<ReceiveChatProps> = ({
  chatSessions,
  onChatSelect,
  selectedChatId
}) => {
  const [unreadMessages, setUnreadMessages] = useState<Set<string>>(
    new Set(
      chatSessions
        .filter((session) => session.messagesCount > 0)
        .map((session) => session.id)
    )
  );

  const [selectedChatSessionId, setSelectedChatSessionId] = useState<
    string | null
  >(null);
  const [clientType, setClientType] = useState<number>(0);
  const { isMobile } = useIsMobile();
  const navigate = useNavigate();

  useEffect(() => {
    const clientTypeFromCookie = Number(
      utils.getCookie(CookiesConstant.clientType)
    );
    setClientType(clientTypeFromCookie);
  }, []);

  const formatDateToDay = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays > 4) {
        return date.toLocaleDateString("en-US", {
          month: "2-digit",
          day: "2-digit",
          year: "2-digit",
        });
      }

      const days = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      return days[date.getDay()];
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  const handleChatClick = (sessionId: string) => {
    setUnreadMessages((prev) => {
      const updatedSet = new Set(prev);
      updatedSet.delete(sessionId);
      return updatedSet;
    });

    setSelectedChatSessionId(sessionId);
    onChatSelect(sessionId);
  };

  const sortedChatSessions = [...chatSessions].sort((a, b) => {
    if (a.lastMessageDate && b.lastMessageDate) {
      return (
        new Date(b.lastMessageDate).getTime() -
        new Date(a.lastMessageDate).getTime()
      );
    }

    const latestA = Math.max(
      ...a.participants.map((p) => new Date(p.addedOn).getTime())
    );
    const latestB = Math.max(
      ...b.participants.map((p) => new Date(p.addedOn).getTime())
    );

    return latestB - latestA;
  });

  return !isMobile ? (
    <div style={{ minWidth: "26%", display: "flex" }}>
      <div className={styles.chatReceiveContainer}>
        <h2
          style={{
            fontSize: "20px",
            fontWeight: "700",
            color: "#585858",
            paddingLeft: "20px",
          }}
        >
          Messages
        </h2>
        {sortedChatSessions.map((session) => {
          const filteredParticipants = session.participants.filter(
            (participant) => {
              if (clientType === 0) {
                return participant.jugglerTypes === 1;
              } else {
                return participant.jugglerTypes === 2;
              }
            }
          );

          return (
            <div
              key={session.id}
              className={`${styles.chatMessage} 
              ${unreadMessages.has(session.id) ? styles.unreadMessage : ""} 
              ${selectedChatId === session.id
                  ? styles.selectedMessage
                  : ""
                }`}
              onClick={() => handleChatClick(session.id)}
            >
              {filteredParticipants.map((participant: Participant) => (
                <React.Fragment key={participant.id}>
                  <img
                    src={
                      participant.imageUrl
                        ? `${environment.getStorageURL(
                          window.location.hostname
                        )}/images/${participant.imageUrl}`
                        : sample
                    }
                    alt={`${participant.firstName}'s profile`}
                    className={styles.chatPhoto}
                  />
                  <div
                    className="ml-2"
                    style={{ marginTop: "10px", flexGrow: "1" }}
                  >
                    <div style={{ display: "flex", flexDirection: "row" }}>
                      <h4 className={styles.ChatHeader}>
                        {participant.firstName ||
                          participant.publicName ||
                          "Unknown"}
                      </h4>
                      {session.unreadMessages > 0 && (
                        <p className={styles.BadgeTag}>
                          {session.unreadMessages}{" "}
                        </p>
                      )}
                    </div>

                    <p
                      style={{
                        margin: "0px",
                        fontSize: "14px",
                        color: "#585858",
                        fontWeight: "400",
                        textWrap: "nowrap",
                      }}
                    >
                      {session.lastMessage
                        ? session.lastMessage.length > 30
                          ? `${session.lastMessage.substring(0, 30)}...`
                          : session.lastMessage
                        : ""}
                    </p>
                    <div style={{
                      margin: "0px",
                      fontSize: "10px",
                      color: "#179D52",
                      fontWeight: "700",
                    }}>
                      {session.unreadMessages.toString() &&
                        session.unreadMessages > 0 && (
                          <p style={{ margin: "0" }}>Unread Messages </p>
                        )}
                    </div>
                  </div>
                  <div>
                    <p
                      style={{
                        margin: "0px",
                        color: "#585858",
                        fontSize: "12px",
                        fontWeight: "300",
                      }}
                    >
                      {formatDateToDay(session.lastMessageDate)}
                    </p>
                  </div>
                </React.Fragment>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  ) : (
    <div style={{ minWidth: "21%", display: "flex" }}>
      <div className={styles.chatReceiveContainerMobile}>
        <h2
          style={{
            fontSize: "20px",
            fontWeight: "700",
            color: "#585858",
            paddingLeft: "20px",
          }}
        >
          Messages
        </h2>
        {sortedChatSessions.map((session) => {
          const filteredParticipants = session.participants.filter(
            (participant) => {
              if (clientType === 0) {
                return participant.jugglerTypes === 1;
              } else {
                return participant.jugglerTypes === 2;
              }
            }
          );

          return (
            <div
              key={session.id}
              className={`${styles.chatMessageMobile} 
            ${unreadMessages.has(session.id) ? styles.unreadMessage : ""} 
            ${selectedChatId === session.id
                  ? styles.selectedMessageMobile
                  : ""
                }`}
              onClick={() => handleChatClick(session.id)}
            >
              {filteredParticipants.map((participant: Participant) => (
                <React.Fragment key={participant.id}>
                  <img
                    src={
                      participant.imageUrl
                        ? `${environment.getStorageURL(
                          window.location.hostname
                        )}/images/${participant.imageUrl}`
                        : sample
                    }
                    alt={`${participant.firstName}'s profile`}
                    className={styles.chatPhoto}
                  />
                  <div
                    className="ml-2"
                    style={{ marginTop: "10px", flexGrow: "1" }}
                  >
                    <div style={{ display: "flex", flexDirection: "row" }}>
                      <h4 className={styles.ChatHeader}>
                        {participant.firstName ||
                          participant.publicName ||
                          "Unknown"}
                      </h4>
                      {session.unreadMessages > 0 && (
                        <p className={styles.BadgeTag}>
                          {session.unreadMessages}{" "}
                        </p>
                      )}
                    </div>

                    <p
                      style={{
                        margin: "0px",
                        fontSize: "14px",
                        color: "#585858",
                        fontWeight: "400",
                        textWrap: "nowrap",
                      }}
                    >
                      {session.lastMessage
                        ? session.lastMessage.length > 30
                          ? `${session.lastMessage.substring(0, 30)}...`
                          : session.lastMessage
                        : ""}
                    </p>
                    <div style={{
                      margin: "0px",
                      fontSize: "10px",
                      color: "#179D52",
                      fontWeight: "700",
                    }}>
                      {session.unreadMessages.toString() &&
                        session.unreadMessages > 0 && (
                          <p style={{ margin: "0" }}>Unread Messages </p>
                        )}
                    </div>
                  </div>
                  <div>
                    <p
                      style={{
                        margin: "0px",
                        color: "#585858",
                        fontSize: "12px",
                        fontWeight: "300",
                      }}
                    >
                      {formatDateToDay(session.lastMessageDate)}
                    </p>
                  </div>
                </React.Fragment>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  )
};

export default ReceiveChat;
