import { useEffect, useState } from "react";

export function useHashQueryParam(key: string) {
  const getValue = () => {
    const [, query] = window.location.hash.split("?");
    const params = new URLSearchParams(query);
    return params.get(key);
  };

  const [value, setValue] = useState(getValue());

  useEffect(() => {
    const check = () => {
      const newValue = getValue();
      setValue((prev) => (prev !== newValue ? newValue : prev));
    };

    const patchHistoryMethod = (method: "pushState" | "replaceState") => {
      const original = history[method];
      history[method] = function (...args) {
        const result = original.apply(this, args as any);
        check();
        return result;
      };
    };

    patchHistoryMethod("pushState");
    patchHistoryMethod("replaceState");

    window.addEventListener("hashchange", check);
    return () => {
      window.removeEventListener("hashchange", check);
    };
  }, []);

  return value;
}
