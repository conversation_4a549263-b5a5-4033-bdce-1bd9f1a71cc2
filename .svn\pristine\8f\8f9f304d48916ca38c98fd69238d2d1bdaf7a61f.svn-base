import { createContext, PropsWithChildren, useContext, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import useLoader from "../../../../hooks/LoaderHook";
import c from "../../../../helper/juggleStreetConstants";

export const JobManagementScreensArray = [
  "job-type",
  "job-posting",
  "job-pricing",
  "job-details",
  "jobpricing-step1",
  "jobpricing-step2",
  "jobpricing-step3",
  "candidate-selection",
  "candidate-matching",
  "review-post",
  "job-summary",
  "job-description-mobile",
  "pricing-payments-step1",
  "pricing-payments-step2",
  "typeand-years",
  "tutoring-subjects",
  "overtime-section-mobile",
  "day-and-schedule",
  "subscription",
  "section4-mobile",
] as const;

export type JobManagementScreens = (typeof JobManagementScreensArray)[number];

export type PayloadTemplate = {
  jobStartTime: string;
  jobEndTime: string;
  paymentType: number;
  jobType: number;
  helperPaymentMethod: number;
  jobDeliveryMethod?: string;
  willPayOvertime?: boolean;
  daysOfWeek: Array<{
    dayOfWeek: number;
    isRequired: boolean;
  }>;
  weeklySchedule: {
    scheduleName: string;
    isActive: number;
    weeklyScheduleEntries: Array<{
      dayOfWeek: number;
      shifts: Array<{
        isRequired: boolean;
        jobStartTime: string;
        jobEndTime: string;
        hourlyPrice: string;
        price: number;
        shiftType?: number
        applicantId?: number | null; // Add applicantId to shifts
      }>;
      isRequired: boolean;
      jobStartTime: string;
      jobEndTime: string;
      hourlyPrice: string;
      price: number;
      shiftType?: number
    }>;
  };
  jobSettings: {
    schoolYears?: Array<number>;
    schoolSubjects?: Array<number>;
  };
  userPaymentType: string;
  actionType: number;
  isUpsellEnabled: boolean;
  jobDate: string;
  addressId: number;
  addressLabel: string;
  longitude: number;
  latitude: number;
  managedBy: number;
  jobSubType: number;
  specialInstructions: string;
  price: number;
  overtimeRate?: number;
  applicants: Array<{
    applicantId: number;
  }>;
  applicantFilters: Array<{
    field: string;
    value: any;
    operator: string;
  }>;
  cardSelected: any;
  deviceType: number;
  jobDateDay: number;
  jobDateMonth: number;
  jobDateYear: number;
  jobFinishType?: string;
  durationType?: number;
  duration: any;
  jobStartType?: string;
  isPriceNegotiable?: boolean;
  metadata: Array<{ key: string; data: any }> | null;
  durationToStart: string | null;
  jobStartDurationType: string | number | null;
  selectedCandidates: Array<any> | null;
  id?: number;
  jobRosterType?: number;
  isJobFlexible?:boolean
};

interface JobManagerContextType {
  payload: Partial<PayloadTemplate>;
  steps: Array<JobManagementScreens>;
  next: (to: JobManagementScreens, replace?: boolean) => void;
  prev: (to: JobManagementScreens, replace?: boolean) => void;
  setpayload: (payload: Partial<PayloadTemplate>) => void;
}

const JobManagerContext = createContext<JobManagerContextType>({
  payload: {},
  steps: [],
  next: () => {},
  prev: () => {},
  setpayload: () => {},
});

export const JobManagerProvider = ({ children }: PropsWithChildren) => {
  const [payload, setPayload] = useState<Partial<PayloadTemplate>>({});
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { enableLoader, disableLoader } = useLoader();

  useEffect(() => {
    enableLoader();
    const sessionStoragePayload = sessionStorage.getItem("jobManagement");
    if (sessionStoragePayload) {
      try {
        const parsedData = JSON.parse(sessionStoragePayload);
        if (parsedData?.payload) {
          setPayload(parsedData.payload);
        }
      } catch (error) {
        console.error("Invalid session storage data:", error);
      } finally {
        disableLoader();
      }
    } else {
      updatePayload({
        jobStartTime: null,
        jobEndTime: null,
        paymentType: null,
        helperPaymentMethod: null,
        jobDeliveryMethod: null,
        willPayOvertime: null,
        daysOfWeek: [
          { dayOfWeek: 1, isRequired: false },
          { dayOfWeek: 2, isRequired: false },
          { dayOfWeek: 3, isRequired: false },
          { dayOfWeek: 4, isRequired: false },
          { dayOfWeek: 5, isRequired: false },
          { dayOfWeek: 6, isRequired: false },
          { dayOfWeek: 0, isRequired: false },
        ],
        weeklySchedule: {
          scheduleName: "default",
          isActive: 1,
          weeklyScheduleEntries: [],
        },
        jobSettings: {},
        userPaymentType: "",
        actionType: c.jobActionType.POST,
        isUpsellEnabled: false,
        jobDate: null,
        addressId: null,
        longitude: null,
        latitude: null,
        managedBy: null,
        specialInstructions: null,
        // price: null,
        overtimeRate: null,
        cardSelected: null,
        // tokenId: null,
        applicants: [],
        applicantFilters: null,
        deviceType: c.deviceType.DESKTOP,
        jobDateDay: null,
        jobDateMonth: null,
        jobDateYear: null,
        duration: null,
        durationType: null,
        durationToStart: null,
        jobStartDurationType: null,
        isPriceNegotiable: null,
        jobStartType: "1",
        jobFinishType: "2",
        selectedCandidates: null,
      });
      disableLoader();
    }
  }, []);

  const updatePayload = (newPayload: Partial<PayloadTemplate>) => {
    sessionStorage.setItem("jobManagement", JSON.stringify({ payload: newPayload }));
    setPayload(newPayload);
  };

  return (
    <JobManagerContext.Provider
      value={{
        payload,
        steps: [],
        next: (to: JobManagementScreens, replace = false) => {
          navigate(to + `?${searchParams.toString()}`, { replace: replace });
        },
        prev: (_: JobManagementScreens, replace = false) => {
          navigate(-1);
        },
        setpayload: updatePayload,
      }}
    >
      {children}
    </JobManagerContext.Provider>
  );
};

export const useJobManager = () => {
  const { payload, next, prev, setpayload, steps } = useContext(JobManagerContext);

  return { payload, next, prev, setpayload, steps };
};
