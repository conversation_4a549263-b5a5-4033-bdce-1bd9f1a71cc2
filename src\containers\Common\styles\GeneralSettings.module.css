.settingsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  padding: 20px;
  padding-top: 7px;
}

.settingsHeader {
  font-size: 24px;
  font-weight: 600;
  margin-top: 0.8rem;
  color: #585858;
}
.settingsDivider {
  width: 100%; /* Make the divider take full width */
  margin-top: 8px;
  color: #f1f1f1;
}
/* GeneralSettings.module.css */

.settingsInputContainer {
  margin-bottom: 20px; /* Space between input fields */
  display: flex;
  flex-direction: column;
  width: 100%; /* Ensures full width in container */
}

.successMessageSettings {
  width: max-content;
  height: 38px;
  background-color: #37a950;
  padding: 10px;
  border-radius: 20px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 800;
  line-height: 18px;
  margin-right: 32px;
  display: flex;
  gap: 12px;
  margin-top: 10px;
  text-wrap: noWrap;
}
.errorMessageSettings {
  width: min-content;
  height: 38px;
  background-color: rgba(255, 99, 89, 1);
  padding: 10px;
  border-radius: 20px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 800;
  line-height: 18px;
  margin-right: 32px;
  display: flex;
  gap: 12px;
  margin-top: 10px;
  text-wrap: noWrap;
}

.hideAccountDescription {
  color: #585858;
}
.hideAccountText {
  color: #585858;
  font-weight: 700;
  margin-top: 0px;
}
.AccountText {
  color: #585858;
  font-weight: 700;
}
.hideAccountLink {
  color: red;
  transition: color 0.3s ease; /* Optional: smooth transition */
  width: max-content;
}
.deleteAccountLink {
  color: red;
  transition: color 0.3s ease; /* Optional: smooth transition */
  width: max-content;
}
.successMessageSettings {
  margin: 10px 0;
  display: flex;
  align-items: center;
}
.errorMessageSettings {
  margin: 10px 0;
  display: flex;
  align-items: center;
  font-size: 12px;
}
@media (min-width: 768px) {
  .settingsInputContainer {
    max-width: 500px; /* Set a max width for larger screens */
  }
}
