.dropdown {
  border-radius: 10px;
  border: 1px solid #dfdfdf;
}

.dropdown[data-hasvalue="true"] {
    border: 2px solid #179D52;
}
.dinput {
    background-color: white;
}

.dropdown[data-hasvalue="true"]  .dinput {
    font-size: 12px !important;
    font-weight: 700 !important;
    color: #179D52 !important;
}

.dropdown [data-pc-section="trigger"] {
    color: #179D52 !important;
}

.dpanel {
    max-width: 70%;
    border: 1px solid #DFDFDF;
    box-shadow: 0 4px 4px 0 #00000040;
    overflow: hidden;
}

.dialog {
    position: absolute;
    height: 70vh !important;
    bottom: 0 !important;
    border-top-left-radius: 30px !important;
    border-top-right-radius: 30px !important;
}

.saveAddress {
    display: flex;
    margin-top: 10px;
    position: sticky;
    bottom: 0;
    border-top: 2px solid #DFDFDF;
    background-color: white;
    padding-block: 20px;
}

.saveAddress > button {
    width: 80%;
    margin-inline: auto;
}

.saveAddress > button[disabled] {
    color: white;
}

.addAddressContainer {
    padding: 0 !important;
}
.flexContainer, .addAddressHeader {
    padding-inline: 20px;
}

.addAddressContainer > header {
    margin-top: 10px;
    padding-inline: 20px;
}


.footerButtonArrow {
    width: min-content;
    position: fixed;
    top: 32px;
    left: 35px;
    width: 35px;
    height: 25px;
  }