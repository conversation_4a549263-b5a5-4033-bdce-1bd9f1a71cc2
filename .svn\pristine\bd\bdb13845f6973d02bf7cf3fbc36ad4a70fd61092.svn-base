import { useSelector } from "react-redux";
import { useJobManager } from "../../provider/JobManagerProvider";
import PaymentsParent from "./PaymentParent";
import { RootState } from "../../../../../store";
import c from "../../../../../helper/juggleStreetConstants";
import PaymentsBusiness from "./PaymentBusiness";

function Payments() {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo.data);
  const { payload, setpayload, next, prev } = useJobManager();
  if (sessionInfo?.["client"]["clientType"] === c.clientType.BUSINESS) {
    return <PaymentsBusiness currentPayload={payload} nextClicked={next} prevClicked={prev} setPayload={setpayload} />;
  }
  return <PaymentsParent currentPayload={payload} nextClicked={next} prevClicked={prev} setPayload={setpayload} />;
}

export default Payments;
