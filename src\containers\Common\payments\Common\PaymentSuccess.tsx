import React from 'react';
import styles from '../../styles/payment-success.module.css';
import { FaDownload } from 'react-icons/fa';
import { Divider } from 'primereact/divider';

type PaymentSuccessProps = {
    amount: string;
    invoiceNo: string;
    status: string;
    date: string;
    invoiceString?:string;
    time: string;
    paymentType: string;
    onDownload: () => void;
    onDone: () => void;
};

const PaymentSuccess: React.FC<PaymentSuccessProps> = ({
    amount,
    invoiceNo,
    status,
    date,
    time,
    paymentType,
    onDownload,
    invoiceString,
    onDone,
}) => {
    return (
        <div className={styles.container}>
            <div className={styles.header}></div>

            <div className={styles.successBox}>
                <div className={styles.iconWrapper}>
                    <div className={styles.ripple}></div>
                    <div className={styles.tickWrapper}>
                        <svg viewBox="0 0 100 100" width="80" height="80">
                            {/* Outer static light green circle */}
                            <circle className={styles.outerCircle} cx="50" cy="50" r="50" />

                            {/* Inner green circle that beats */}
                            <circle className={styles.innerCircle} cx="50" cy="50" r="40" />

                            {/* Checkmark path with drawing animation */}
                            <path
                                className={styles.checkmark}
                                d="M30 50 L45 65 L70 35"
                                stroke="#ffffff"
                                strokeWidth="8"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                fill="none"
                            />
                        </svg>
                    </div>
                </div>
                <p className={styles.successText}>Payment Confirmed!</p>
                <p className={styles.amount}>${amount}</p>
            </div>

            <div className={styles.detailCard}>
                <p className={styles.detailText}>Payment Details</p>
                <p className={styles.label}>
                    Invoice No <span className={styles.value}>{invoiceNo}</span>
                </p>
                <p className={styles.label}>
                    Payment Status <span className={styles.complete}>{status}</span>
                </p>
                <p className={styles.label}>
                    Payment Date <span className={styles.value}>{date}</span>
                </p>
                <p className={styles.label}>
                    Payment Time <span className={styles.value}>{time}</span>
                </p>
                <p className={styles.label}>
                    Payment Type <span className={styles.value}>{paymentType}</span>
                </p>
                <Divider/>
                <p className={styles.label}>
                    Total Payment <span className={styles.value}>${amount}</span>
                </p>
            </div>

            <button className={styles.downloadBtn} onClick={onDownload}>
                <FaDownload style={{ marginRight: '8px' }} />
               {invoiceString}
            </button>

            <div className={styles.footer}>
                <button className={styles.doneBtn} onClick={onDone}>Done</button>
            </div>
        </div>
    );
};

export default PaymentSuccess;
