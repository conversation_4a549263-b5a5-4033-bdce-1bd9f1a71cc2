import React, { useState } from "react";
import styles from "./styles/small-panel.module.css";
import logoSrc from "../../assets/images/juggle-st-transparent-card.png";
import { ProgressBar } from "primereact/progressbar";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import { updateProfileActivationEnabled } from "../../store/slices/applicationSlice";

const SmallUserPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(true);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();

  const togglePanel = () => {
    setIsOpen(!isOpen);
  };

  return (
    <aside className={`${styles.smallpanel} ${!isOpen ? styles.closed : ""}`}>
      <div className={styles.imgcontainer}>
        <img
          loading="lazy"
          src={logoSrc}
          alt="User Panel Logo"
          className={`${styles.logoDefault} ${!isOpen ? styles.hidden : ""}`}
        />
      </div>

      <div className={styles.textContent}>
        <p className={styles.activationMessage}>
          <span className={styles.activationMessageHighlight}>
            Activate your profile{" "}
          </span>
          <br />
          to post a job and chat with helpers on Juggle Street
        </p>
        <p className={styles.progressLabel}>Your progress</p>
        <p className={styles.progressPercentage}>
          {sessionInfo.data["profileCompleteness"]}% complete
        </p>
        <br />
        <ProgressBar
          value={sessionInfo.data["profileCompleteness"]}
          className={styles.myprogressbar}
        />

        <div className={styles.ctaWrapper}>
          <button
            className={styles.ctaButton}
            onClick={() => {
              togglePanel();
              dispatch(updateProfileActivationEnabled(true));
            }}
          >
            Continue
            <i className="pi pi-angle-right" style={{ marginLeft: "8px" }}></i>
          </button>
        </div>
      </div>

      <button onClick={togglePanel} className={styles.toggleButton}>
        {isOpen ? (
          <i className="pi pi-angle-left" style={{ fontSize: "16px" }}></i>
        ) : (
          <i className="pi pi-angle-right" style={{ fontSize: "16px" }}></i>
        )}
      </button>
    </aside>
  );
};

export default SmallUserPanel;
