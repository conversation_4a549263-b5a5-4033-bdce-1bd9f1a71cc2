import { Dialog } from 'primereact/dialog';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import {
    updateProfileActivationEnabled,
    updateShowChatBox,
} from '../../store/slices/applicationSlice';
import { HiOutlineXMark } from 'react-icons/hi2';

type PostJobActivationProps = {
    isVisible: boolean;
    onHide: () => void;
    onXClicked: () => void;
};

function PostJobActivationPopup({ isVisible, onHide, onXClicked }: PostJobActivationProps) {
    const [activateButtonHover, setActivateButtonHover] = useState(false);
    const [activateButtonClicked, setActivateButtonClicked] = useState(false);
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);
    const { loading, data } = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();

    useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        dispatch(updateShowChatBox(!isVisible));
    }, [isVisible, dispatch]);

    useEffect(() => {
        if (loading || (data && data['profileCompleteness'] === 100)) {
            onXClicked();
        }
    }, [loading, data, onXClicked]);

    const heightStyle = windowWidth >= 992 ? '9.5rem' : '17.5rem';

    return (
        <Dialog
            resizable={false}
            visible={isVisible}
            onHide={onHide}
            position="bottom"
            style={{
                width: '100%',
            }}
            content={
                <>
                    <div
                        className="overflow-hidden w-full fixed bottom-0 shadow-4"
                        style={{
                            background: '#37A950',
                            height: heightStyle,
                        }}
                    />
                    <div
                        className="overflow-hidden h-17rem w-full fixed bottom-0 shadow-4 lg:h-10rem"
                        style={{
                            background: 'linear-gradient(90deg, #37A950, #FFA500)',
                            borderRadius: '20px 20px 0 0',
                            transform: 'translateX(-25px) rotateZ(-0.4deg)',
                        }}
                    />
                    <div
                        className="w-full h-15rem bg-white overflow-hidden shadow-4 flex flex-column justify-content-center gap-4 lg:flex-row lg:justify-content-between lg:align-items-center lg:h-9rem lg:h-9 relative lg:px-7"
                        style={{
                            color: '#585858',
                            borderRadius: '30px 30px 0 0',
                            zIndex: 10,
                        }}
                    >
                        <div
                            className="absolute cursor-pointer"
                            style={{
                                top: '10px',
                                right: '15px',
                                fontSize: '25px',
                            }}
                            onClick={() => onXClicked()}
                        >
                            <HiOutlineXMark />
                        </div>
                        <div className="flex flex-column align-items-center lg:align-items-start gap-1">
                            <h2 className="m-0 p-0 font-bold text-md text-center">
                                Activate your profile to{` `}
                                <span style={{ color: '#FEAE4F' }}>‘Post Job’</span>
                            </h2>
                            <p className="m-0 p-0 text-sm text-center">
                                Complete your profile to post a job and chat with helpers on Juggle
                                Street
                            </p>
                        </div>

                        <div className="flex justify-content-center align-items-center lg:mt-5">
                            <button
                                className="border-none text-white"
                                style={{
                                    textWrap: 'nowrap',
                                    backgroundColor: activateButtonClicked
                                        ? '#127941'
                                        : activateButtonHover
                                        ? '#1BB861'
                                        : '#179D52',
                                    borderRadius: '10px',
                                    paddingInline: '30px',
                                    paddingBlock: '7px',
                                    fontWeight: '700',
                                    fontSize: '16px',
                                    cursor: 'pointer',
                                    transform: activateButtonClicked ? 'scale(0.98)' : 'scale(1)',
                                    transition: 'background-color 0.3s, transform 0.2s',
                                }}
                                onMouseEnter={() => setActivateButtonHover(true)}
                                onMouseLeave={() => setActivateButtonHover(false)}
                                onClick={() => {
                                    setActivateButtonClicked(true);
                                    setTimeout(() => setActivateButtonClicked(false), 200);
                                    dispatch(updateProfileActivationEnabled(true));
                                    onXClicked();
                                }}
                            >
                                Activate Profile
                            </button>
                        </div>
                    </div>
                </>
            }
        />
    );
}

export default PostJobActivationPopup;
