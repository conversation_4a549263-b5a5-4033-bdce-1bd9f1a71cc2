import React, { useEffect, useState } from 'react';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import '../../../components/utils/util.css';
import { ProgressBar } from 'primereact/progressbar';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import {
    decrementProfileActivationStep,
    incrementProfileActivationStep,
    updateAccountAndSettingsActiveTab,
    updateShowAccountAndSettings,
} from '../../../store/slices/applicationSlice';
import c from '../../../helper/juggleStreetConstants';
import { InputTextarea } from 'primereact/inputtextarea';
import CustomButton from '../../../commonComponents/CustomButton';
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { updateActiveTabIndex } from '../../../store/slices/accountSettingSlice';
import { IoSettingsSharp } from 'react-icons/io5';
import { FaCity, FaFlag } from 'react-icons/fa6';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';
interface AuPairJobsComponentProps {
    onComplete?: () => void;
}
interface SessionInfo {
    data: {
        interestedInJobTypes: number;
        vouches: Array<{
            referenceType: string;
            fullName: string;
            phoneNumber: string;
            email: string;
            otherReferenceType: string;
            status: string;
        }>;
        interestedInAuPairJobs?: boolean;
        provider: {
            myExperience4?: string;
            nationality?: string;
        };
        profileCompleteness: number;
    };
    loading: boolean;
}

const AuPair: React.FC<AuPairJobsComponentProps> = ({ onComplete }) => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo as SessionInfo);
    const session = useSelector((state: RootState) => (state.sessionInfo as SessionInfo).data);
    const dispatch = useDispatch<AppDispatch>();
    const [selectedOption, setSelectedOption] = useState<string>('');
    const { disableLoader, enableLoader } = useLoader();
    const [_, setShowConsentOptions] = useState<boolean>(false);
    const [selectedConsent, setSelectedConsent] = useState<string>('');
    const [showCountryDropdown, setShowCountryDropdown] = useState<boolean>(false);
    const [selectedCountry, setSelectedCountry] = useState<string>('');
    const [showTextarea, setShowTextarea] = useState<boolean>(false);
    const [textareaPlaceholder, setTextareaPlaceholder] = useState<string>('');
    const [myExperience, setMyExperience] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState<string>('');
    const [isChanged, setIsChanged] = useState<boolean>(false);
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const [radioValue, setRadioValue] = useState<string>('yes');
    const minCharLimit = 100;
    const [hasSeenPopup, setHasSeenPopup] = useState<boolean>(false);
    const [popupShown, setPopupShown] = useState(false);
    const{isMobile}=useIsMobile()
    const notices = [
        {
            label: 'I acknowledge & agree with all of the above',
        },
        {
            label: 'Remove Au Pair from my Juggle Street profile. Note - this does NOT affect your other Childcare or Tutoring profiles.',
        },
    ];

    const consentOptions = [
        {
            value: 'AU',
            label: 'Australian Citizen',
        },
        {
            value: 'NZ',
            label: 'New Zealand Citizen',
        },
        {
            value: 'OTHER',
            label: 'Other Nationality',
        },
    ];

    useEffect(() => {
        if (selectedConsent !== 'OTHER') {
            setSelectedCountry('');
        }
    }, [selectedConsent]);

    const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSelectedOption(value);
        setIsChanged(true);

        if (value === 'I acknowledge & agree with all of the above') {
            setShowConsentOptions(true);
            // Reset other fields when acknowledging
            setSelectedConsent('');
            setShowCountryDropdown(false);
            setSelectedCountry('');
        } else {
            // Clear all fields when removing Au Pair
            setShowConsentOptions(false);
            setShowCountryDropdown(false);
            setShowTextarea(false);
            setSelectedConsent('');
            setSelectedCountry('');
            setMyExperience('');
        }
    };

    const handleConsentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSelectedConsent(value);
        setIsChanged(true);

        if (value === 'OTHER') {
            setShowCountryDropdown(true);
        } else {
            setShowCountryDropdown(false);
            setSelectedCountry('');
        }
    };

    const handleCountryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedCountry(event.target.value);
        setIsChanged(true);
        setErrorMessage('');
    };

    const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setMyExperience(event.target.value);
        setIsChanged(true);
    };

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };

    const handleHeaderRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setRadioValue(value);
        setShowTextarea(true);
        setMyExperience('');
        setIsChanged(true);
        setTextareaPlaceholder(
            value === 'yes'
                ? 'Provide details of your previous Au Pair experience'
                : 'Provide details of your other childcare experience and explain why you believe you would make a good Au Pair'
        );
    };

    const handleOkGotItClick = () => {
        setHasSeenPopup(true); // Mark that user has seen the popup
        setPopupShown(true); // Keep track that popup was shown
        dispatch(updateActiveTabIndex(17));
        dispatch(updateAccountAndSettingsActiveTab(17));
        dispatch(updateShowAccountAndSettings(true));
    };

    // const checkJuggleStreetInterview = () => {
    //     const { vouches } = sessionInfo.data;
    //     const validInterview = vouches.find(
    //         (v: any) => v.referenceType === c.vouchReferenceType.juggleStreet
    //     );
    //     if (validInterview) return;
    //     const newRef = {
    //         fullName: 'Juggle Street',
    //         phoneNumber: '02 8001 6263',
    //         email: '<EMAIL>',
    //         otherReferenceType: '',
    //         status: c.vouchStatus.pendingVerification.toString(),
    //         referenceType: c.vouchReferenceType.juggleStreet.toString(),
    //     };
    //     return [...vouches, newRef];
    // };

    const isValid = () => {
        if (selectedOption === 'I acknowledge & agree with all of the above') {
            if (selectedConsent === 'OTHER' && !selectedCountry) {
                return false;
            }
            return myExperience?.length >= minCharLimit;
        }
        return true;
    };

    const setAuPairValue = () => {
        let { interestedInJobTypes } = sessionInfo.data;
        if (selectedOption === 'I acknowledge & agree with all of the above' && isValid()) {
            interestedInJobTypes |= c.jobType.AU_PAIR;
            // checkJuggleStreetInterview();
            // dispatch(updateSessionInfo({ payload: { ...session, interestedInJobTypes } }));
        } else if (selectedOption !== 'I acknowledge & agree with all of the above') {
            interestedInJobTypes &= ~c.jobType.AU_PAIR;
            // dispatch(updateSessionInfo({ payload: { ...session, interestedInJobTypes } }));
        }
    };

    const handleUpdateProfile = async () => {
        const interestedInAuPairJobs =
            selectedOption === 'I acknowledge & agree with all of the above';
        let interestedInJobTypes = session.interestedInJobTypes;

        if (interestedInAuPairJobs) {
            interestedInJobTypes |= c.jobType.AU_PAIR;
        } else {
            interestedInJobTypes &= ~c.jobType.AU_PAIR;
        }

        // Create provider object with all required fields
        const provider = {
            ...session,
            myExperience4: myExperience,
        };

        // Create the payload
        const payload = {
            ...session,
            interestedInJobTypes,
            vouches: interestedInAuPairJobs
                ? [...session.vouches,]
                : session.vouches,
            provider,
            nationality: selectedConsent === 'OTHER' ? selectedCountry : selectedConsent, // Add nationality based on consent
            interestedInAuPairJobs,
        };

         // For debugging

        enableLoader();
        try {
            await dispatch(updateSessionInfo({ payload }));
            dispatch(incrementProfileActivationStep());
            onComplete?.();
        } finally {
            disableLoader();
        }
    };

    const handleButtonClick = async () => {
        if (!isValid()) {
            if (selectedConsent === 'OTHER' && !selectedCountry) {
                setErrorMessage('Please select a country.');
            }
            return;
        }

        const needsToSeePopup =
            sessionInfo.data.interestedInAuPairJobs &&
            sessionInfo.data.vouches.length < 2 &&
            !hasSeenPopup;

        if (needsToSeePopup) {
            showConfirmationPopup(
                'References',
                'A minimum of 2 references are required to be a Juggle Street Au Pair.',
                'Ok Got It',
                null,
                handleOkGotItClick,
                handleUpdateProfile
            );
            return;
        }

        await handleUpdateProfile();
    };


    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };

    useEffect(() => {
        if (sessionInfo?.data) {
            // setSelectedOption((sessionInfo.data['interestedInAuPairJobs'] && c.job) );
            const isInterested = (sessionInfo.data?.interestedInJobTypes & c.jobType.AU_PAIR) === c.jobType.AU_PAIR;
            setSelectedOption(isInterested ? 'I acknowledge & agree with all of the above' : '');
            setShowConsentOptions(isInterested);

            if (isInterested) {
                const nationality = sessionInfo.data?.['nationality'];
                if (nationality && nationality !== 'AU' && nationality !== 'NZ') {
                    setSelectedConsent('OTHER');
                    setShowCountryDropdown(true);
                    setSelectedCountry(nationality);
                } else {
                    setSelectedConsent(nationality);
                }
                // Just handle the experience text without changing radio value
                const experience = sessionInfo.data.provider?.myExperience4;
                if (experience) {
                    setMyExperience(experience);
                    setShowTextarea(true);
                }
            }
        }
        // Always make textarea visible and set default placeholder
        setShowTextarea(true);
        setTextareaPlaceholder('Provide details of your previous Au Pair experience');
    }, []);

    useEffect(() => {
        if (selectedOption === 'I acknowledge & agree with all of the above') {
            setAuPairValue();
        }
    }, [selectedOption, selectedConsent, selectedCountry, myExperience]);

    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            <ProfileCompletenessHeader
                title="Au Pair"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={()=>dispatch(decrementProfileActivationStep())}
            />
            <div style={{paddingInline:isMobile && "15px"}}className="txt-clr ">
                <div className='flex'>
                    <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0" style={{ fontSize: '18px' }}>
                        <span><IoSettingsSharp style={{ color: '#585858', fontSize: '18px' }} /></span> Au Pair
                    </h1>
                </div>
                <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0" style={{ fontSize: '18px' }}>
                    Important notice, please read carefully{' '}
                </h1>
                <h1 className="p-0 m-0 font-semibold mt-3" style={{ fontSize: '14px' }}>
                    Au Pairs live-in with a host family 7 days a week and look after the family’s
                    children. In return, Au Pairs receive a small weekly allowance plus lodging and
                    meals.
                </h1>

                <h1 className="p-0 m-0 font-semibold mt-3" style={{ fontSize: '14px' }}>
                    {' '}
                    Availability – you need to be able to move in with a host family and start your
                    Au Pair job within the next 4 weeks.
                </h1>
            </div>
            <div style={{paddingInline:isMobile && "15px"}} className="txt-clr mt-4">
                {notices.map((notice, index) => (
                    <div key={index} className="flex items-center gap-2 p-1 cursor-pointer">
                        <input
                            type="radio"
                            id={`notice-${index}`}
                            name="notice"
                            value={notice.label}
                            checked={selectedOption === notice.label}
                            onChange={handleRadioChange}
                            className="cursor-pointer"
                        />
                        <label
                            htmlFor={`notice-${index}`}
                            className="txt-clr font-medium cursor-pointer"
                            style={{ fontSize: '16px' }}
                        >
                            {notice.label}
                        </label>
                    </div>
                ))}
            </div>

            {selectedOption === 'I acknowledge & agree with all of the above' && (
                <>
                    <div  style={{paddingInline:isMobile && "15px"}} className="txt-clr mt-4">
                        <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0" style={{ fontSize: '18px' }}>
                            <span><FaCity style={{ color: '#585858', fontSize: '18px' }} />
                            </span> Citizenship and Right to Work
                        </h1>

                        {consentOptions.map((consent, index) => (
                            <div key={index} className="flex items-center gap-2 ml-3 cursor-pointer">
                                <input
                                    type="radio"
                                    id={`consent-${index}`}
                                    name="consent"
                                    value={consent.value}
                                    checked={selectedConsent === consent.value}
                                    onChange={handleConsentChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor={`consent-${index}`}
                                    className="txt-clr font-medium cursor-pointer"
                                    style={{ fontSize: '16px' }}
                                >
                                    {consent.label}
                                </label>
                            </div>
                        ))}
                    </div>

                    {showCountryDropdown && (
                        <div style={{paddingInline:isMobile && "15px"}} className="txt-clr mt-2 ml-3">
                            <select
                                value={selectedCountry}
                                onChange={handleCountryChange}
                                className="cursor-pointer"
                                style={{
                                    width: '200px',
                                    height: '40px',
                                    borderRadius: '10px',
                                    border: '1px solid #585858',
                                    padding: '10px',
                                }}
                            >
                                <option value="">Select a country</option>
                                {c.countriesIso.map((country) => (
                                    <option key={country.id} value={country.value}>
                                        {country.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}
                    {errorMessage && (
                        <p className="p-0 m-0" style={{ color: 'red' }}>
                            {errorMessage}
                        </p>
                    )}
                    <div  style={{paddingInline:isMobile && "15px"}} className="txt-clr mt-4">
                        <h1
                            className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0"
                            style={{ fontSize: '18px', color: '#585858' }}
                        >
                            <span><FaFlag style={{ color: '#585858', fontSize: '18px' }} /></span> Au Pair Experience
                        </h1>
                        <h1 className="p-0 m-0 pl-2 ml-3 font-medium" style={{ fontSize: '16px' }}>
                            Have you worked as an Au Pair before?
                        </h1>
                        <div className="flex ml-4">
                            <div className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="radio"
                                    id="info-yes"
                                    name="info"
                                    value="yes"
                                    checked={radioValue === 'yes'}
                                    onChange={handleHeaderRadioChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor="info-yes"
                                    className="txt-clr font-medium cursor-pointer"
                                    style={{ fontSize: '16px' }}
                                >
                                    Yes
                                </label>
                            </div>
                            <div className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="radio"
                                    id="info-no"
                                    name="info"
                                    value="no"
                                    checked={radioValue === 'no'}
                                    onChange={handleHeaderRadioChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor="info-no"
                                    className="txt-clr font-medium cursor-pointer"
                                    style={{ fontSize: '16px' }}
                                >
                                    No
                                </label>
                            </div>
                        </div>
                    </div>

                    {showTextarea && (
                        <div  style={{paddingInline:isMobile && "15px",marginBottom:isMobile && "70px"}} className="txt-clr ml-4">
                            <InputTextarea
                                autoResize
                                value={myExperience}
                                required
                                onChange={handleTextareaChange}
                                rows={3}
                                cols={30}
                                className={styles.inputTextareafamily}
                                placeholder={textareaPlaceholder}
                            />
                            <p
                                style={{
                                    fontSize: '14px',
                                    color: myExperience?.length < minCharLimit ? 'red' : 'green',
                                    fontWeight: '400',
                                }}
                            >
                                {myExperience?.length < minCharLimit &&
                                    `${minCharLimit - myExperience?.length} characters remaining`}
                            </p>
                        </div>
                    )}
                </>
            )}
           <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <>
                            <i className="pi pi-angle-left"></i>
                            Previous
                        </>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: 'transparent',
                        color: '#585858',
                        width: '156px',
                        height: '39px',
                        fontSize: '14px',
                        fontWeight: '500',
                        margin:"5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />
                <CustomButton
                    className={styles.hoverClass}
                    data-skip={isChanged ? 'false' : 'true'}
                    onClick={isChanged ? handleButtonClick : handleSkip}
                    label={
                        <>
                            {isChanged ? 'Next' : 'Skip'}
                            <i
                                className={`pi pi-angle-${isChanged ? 'right' : 'right'}`}
                                style={{ marginLeft: '8px' }}
                            ></i>
                        </>
                    }
                    style={
                        isChanged
                            ? {
                                backgroundColor: '#FFA500',
                                color: '#fff',
                                width: '156px',
                                height: '39px',
                                fontWeight: '800',
                                fontSize: '14px',
                                borderRadius: '8px',
                                border: '2px solid transparent',
                                boxShadow: '0px 4px 12px #00000',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                                margin:"5px"
                            }
                            : {
                                backgroundColor: 'transparent',
                                color: '#585858',
                                width: '156px',
                                height: '39px',
                                fontWeight: '400',
                                fontSize: '14px',
                                borderRadius: '10px',
                                border: '1px solid #F0F4F7',
                                margin:"5px"
                            }
                    }
                />
            </footer>
        </div>
    );
};

export default AuPair;
