import React, { useState } from 'react';
import '../commonStyle/custom-dropdown.css'
import { IoIosArrowDown, IoIosArrowUp } from 'react-icons/io';
interface Option {
  value: string;
  label: string;
}

interface CustomDropdownProps {
  options: Option[];
  onSelect: (value: string) => void;
  placeholder?: string;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({ options, onSelect, placeholder = 'Select an option' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  const handleMouseEnter = () => {
    setIsOpen(true);
  };

  // Close dropdown when mouse leaves
  const handleMouseLeave = () => {
    setIsOpen(false);
  };
  const handleOptionClick = (option: Option) => {
    setSelectedOption(option);
    onSelect(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-dropdown"
    onMouseEnter={handleMouseEnter}
    onMouseLeave={handleMouseLeave}
    >
      <div className="dropdown-header" onClick={toggleDropdown}>
        <div className="header-content">
          {selectedOption ? selectedOption.label : placeholder}
          <span className="arrow-icon">{isOpen ? <IoIosArrowDown /> : <IoIosArrowUp />}</span>
        </div>
      </div>
      {isOpen && (
        <ul className="dropdown-list" style={{color:"#585858",fontSize:"14px"}}>
          {options.map((option) => (
            <li key={option.value} onClick={() => handleOptionClick(option)}>
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomDropdown;