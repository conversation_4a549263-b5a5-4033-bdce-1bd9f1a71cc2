.card {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  border: 2px solid #179d52;
  border-radius: 10px;
  /* padding: 16px; */
  background-color: #fff;
  max-width: 70%;
  height: 82px;
  /* margin: 16px auto; */
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
  /* transition: transform 0.2s ease; */
  width: 217px;
  /* cursor: not-allowed; */

}

/* 
.card:hover {
  transform: translateY(-5px);
} */

.profileImageTik {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 5px;

}

.profileImage {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 17px;
}

.infoTik {
  flex: 1;
  line-height: 17px;
}

.info {
  flex: 1;
  margin-right: 16px;
}

.name {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 4px;
  color: #585858;
}

.location {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.jobs {
  /* font-size: 0.9rem; */
  color: #585858;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  /* margin: 4px 0; */
  font-size: 12px;
  font-weight: 300;
}

.selectButton {
  padding: 8px 16px;
  font-size: 12px;
  border: 2px solid #179d52;
  border-radius: 10px;
  background-color: #fff;
  color: #28a745;
  cursor: not-allowed;
  transition: all 0.3s ease;
}

.selectButton:hover {
  background-color: #179d52;
  color: #fff;
}

.selected {
  background-color: #179d52;
  color: #fff;
  font-weight: 700;
}

.selectButtonTik {
  /* existing styles */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectedTik {
  background-color: #37A950;
  border-radius: 50%;
  width: 13px;
  height: 13px;
  color: white;
  font-size: 18px;
  border: none;
}

.selectedTik::before {
  content: '✓';
  font-size: 12px;
}

/* .cardMobile {
  display: flex;
  align-items: center;
  width: 100%;
  border: 2px solid #179d52;
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
  transition: transform 0.2s ease;
} */
.cardMobile {
  display: flex;
  align-items: center;
  width: 100%; /* Full width for mobile */
  border: 2px solid #179d52;
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
  transition: transform 0.2s ease;
  flex: 1 1 100%; /* Single card per row on mobile */
  max-width: 100%; /* Ensure it takes full width */
}

/* Two cards per row for tablet (e.g., >= 768px) */
@media (min-width: 768px) {
  .cardMobile {
    flex: 1 1 calc(50% - 1rem); /* Two cards per row, accounting for gap */
    max-width: calc(50% - 1rem); /* Limit width for two cards */
    width: calc(50% - 1rem); /* Ensure consistent width */
  }
}

/* Three cards per row for desktop (e.g., >= 1024px) */
@media (min-width: 1024px) {
  .cardMobile {
    flex: 1 1 calc(33.33% - 1rem); /* Three cards per row, accounting for gap */
    max-width: calc(33.33% - 1rem); /* Limit width for three cards */
    width: calc(33.33% - 1rem); /* Ensure consistent width */
  }
}

.nameMobile {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 4px;
  color: #585858;
}

.locationMobile {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.jobsMobile {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 300;
}