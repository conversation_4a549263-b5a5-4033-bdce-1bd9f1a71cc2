import React, { useEffect, useState } from 'react';
import Check from '../../assets/images/Icons/check.png';
import styles from './styles/steps.module.css';
import useIsMobile from '../../hooks/useIsMobile';

interface Items {
    label: string;
    ref: React.RefObject<any>;
}

interface Props extends React.HTMLProps<HTMLDivElement> {
    items: Items[];
    activeStep: number;
}

function Steps({ items, activeStep }: Props) {
    const { isMobile } = useIsMobile();
    return !isMobile ? (
        <div
            className='w-full flex align-items-center'
            style={{
                minHeight: '40px',
                position: 'relative',
            }}
        >
            {items.map((value, index) => (
                <React.Fragment key={index}>
                    <div
                        className={`flex flex-column align-items-center relative ${styles['step-container']} px-2`}
                        ref={value.ref}
                        data-first={index === 0}
                        data-last={index === items.length - 1}
                        style={{
                            height: 'min-content',
                            flex: index === 0 ? '3' : '4',
                        }}
                    >
                        <div
                            className='flex justify-content-center align-items-center'
                            style={{
                                height: '27px',
                                width: '27px',
                                borderRadius: '50%',
                                zIndex: 2,
                            }}
                        >
                            <div
                                className='flex justify-content-center align-items-center'
                                style={{
                                    height: index > activeStep - 1 ? '18px' : '27px',
                                    width: index > activeStep - 1 ? '18px' : '27px',
                                    borderRadius: '50%',
                                    backgroundColor: index > activeStep ? '#ffffff' : '#FFA500',
                                    boxShadow:
                                        index > activeStep ? '' : '0 4px 4px 0 rgba(0, 0, 0, 0.25)',
                                }}
                            >
                                {index <= activeStep - 1 && (
                                    <img
                                        src={Check}
                                        alt='check'
                                        style={{
                                            height: '13.07px',
                                            width: '14px',
                                            objectFit: 'contain',
                                        }}
                                    />
                                )}
                            </div>
                        </div>
                        <span
                            style={{
                                textWrap: 'nowrap',
                                fontSize: '16px',
                                fontWeight: index === activeStep ? '700' : '500',
                                color: '#FFFFFF',
                            }}
                        >
                            {value.label}
                        </span>
                    </div>
                </React.Fragment>
            ))}
        </div>
    ) : (
        <div
            className='w-full flex align-items-center'
            style={{
                minHeight: '40px',
                position: 'relative',
                justifyContent: 'center',
                overflow: 'hidden',
            }}
        >
            {items.map(
                (value, index) =>
                    index === activeStep && (
                        <React.Fragment key={index}>
                            <div
                                ref={value.ref}
                                data-first={index === 0}
                                data-last={index === items.length - 1}
                                style={{
                                    height: 'min-content',
                                    marginBottom: '15px',
                                }}
                            >
                                <span
                                    style={{
                                        textWrap: 'nowrap',
                                        fontSize: '22px',
                                        fontWeight: '700',
                                        color: '#FFFFFF',
                                        marginBottom: '8px',
                                    }}
                                >
                                    {value.label}
                                </span>
                            </div>
                        </React.Fragment>
                    )
            )}
            <div
                className='progress-container'
                style={{
                    position: 'absolute',
                    bottom: '0',
                    left: '0',
                    width: '100%',
                    height: '8px',
                    backgroundColor: '#E0E0E0',
                    borderRadius: '4px',
                    transform: 'translateY(-50%)',
                }}
            >
                <div
                    className='progress-bar'
                    style={{
                        height: '100%',
                        width: `calc(${(activeStep / (items.length - 1)) * 95}% + 5%)`, // Ensures a minimum width
                        backgroundColor: '#FFA500',
                        transition: 'width 0.3s ease-in-out',
                        borderRadius: '4px',
                    }}
                />
            </div>
        </div>
    );
}

export default Steps;
