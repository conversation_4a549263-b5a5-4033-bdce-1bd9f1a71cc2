import React from 'react';
import { Jobs, ManageJobSectionProps } from './types';

import ChildCareImg from '../../../assets/images/Icons/child-care-green.png';
import TutoringImg from '../../../assets/images/Icons/tutoring-book-green.png';
import OddImg from '../../../assets/images/Icons/odd-jobs-box-green.png';

function getWeekFromToday(): { weeks: Date[]; today: Date } {
    const today = new Date();
    const startOfWeek = new Date(today);
    const weeks: Date[] = [];
    const currentDay = new Date(startOfWeek);

    for (let i = 0; i < 7; i++) {
        weeks.push(new Date(currentDay));
        currentDay.setDate(currentDay.getDate() + 1);
    }

    return {
        weeks,
        today: new Date(today),
    };
}

function getFullMonth(date: Date): string {
    const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    return months[date.getMonth()];
}
function getShortMonth(date: Date): string {
    const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
    ];

    return months[date.getMonth()];
}

function getShortDay(date: Date): string {
    const days = ['Sun', 'Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat'];

    return days[date.getDay()];
}

function getDateWithSuffix(date: Date): string {
    const day = date.getDate();
    let suffix = 'th';

    if (day === 1 || day === 21 || day === 31) {
        suffix = 'st';
    } else if (day === 2 || day === 22) {
        suffix = 'nd';
    } else if (day === 3 || day === 23) {
        suffix = 'rd';
    }

    return `${day}${suffix}`;
}

function compareDates(date1: Date, date2: Date, compareWithTime: boolean = false): boolean {
    if (compareWithTime) {
        return (
            date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate() &&
            date1.getHours() === date2.getHours() &&
            date1.getMinutes() === date2.getMinutes() &&
            date1.getSeconds() === date2.getSeconds() &&
            date1.getMilliseconds() === date2.getMilliseconds()
        );
    } else {
        return (
            date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate()
        );
    }
}

function isDateGreaterThanOrEqual(date: Date, today: Date): boolean {
    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    return date >= today;
}

function isDateInArray(
    dateArray: Date[],
    targetDate: Date,
    compareWithTime: boolean = false
): boolean {
    return dateArray.some((date) => compareDates(date, targetDate, compareWithTime));
}

function calculateDateDifferenceInDays(date1: Date, date2: Date): number {
    const dateOnly1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());
    const dateOnly2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());

    const timeDifference = Math.abs(dateOnly1.getTime() - dateOnly2.getTime());

    return timeDifference / (24 * 60 * 60 * 1000);
}

const ManageJobsCalender: React.FC<ManageJobSectionProps> = ({ upComingJobs }) => {
    const { weeks, today } = getWeekFromToday();

    const oneOffJobs = upComingJobs
        .filter((job) => new Date(job.jobDate) >= today)
        .sort((a, b) => new Date(a.jobDate).getTime() - new Date(b.jobDate).getTime());

    return (
        <div
            className="flex-grow-1 flex py-3"
            style={{
                paddingLeft: '80px',
            }}
        >
            <div className="flex-grow-1 p-1 flex align-items-center">
                <div className="flex flex-column justify-content-center gap-3">
                    <div className="flex gap-3 align-items-center">
                        <h1
                            className="m-0 p-0"
                            style={{
                                fontWeight: '700',
                                fontSize: '30px',
                                color: '#585858',
                            }}
                        >
                            {getFullMonth(today)}, {today.getFullYear()}
                        </h1>
                        {oneOffJobs.length > 0 &&
                        isDateInArray(weeks, new Date(oneOffJobs[0].jobDate)) ? (
                            <div
                                className="flex gap-1"
                                style={{
                                    borderRadius: '20px',
                                    border: '3px solid #179D52',
                                    padding: '10px',
                                }}
                            >
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '14px',
                                        color: '#585858',
                                    }}
                                >
                                    {(() => {
                                        const count = calculateDateDifferenceInDays(
                                            today,
                                            new Date(oneOffJobs[0].jobDate)
                                        );
                                        if (count === 0) {
                                            return <> Job commences </>;
                                        }
                                        return <> Job commences in</>;
                                    })()}
                                </p>
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '900',
                                        fontSize: '14px',
                                        color: '#179D52',
                                    }}
                                >
                                    {(() => {
                                        const count = calculateDateDifferenceInDays(
                                            today,
                                            new Date(oneOffJobs[0].jobDate)
                                        );
                                        if (count === 0) {
                                            return <>Today</>;
                                        }
                                        return <>{count} Days</>;
                                    })()}
                                </p>
                            </div>
                        ) : (
                            <div
                                style={{
                                    borderRadius: '20px',
                                    border: '1px solid #000000',
                                    padding: '10px',
                                }}
                            >
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '14px',
                                        color: '#585858',
                                    }}
                                >
                                    No jobs in the next 7 days
                                </p>
                            </div>
                        )}
                    </div>
                    <div className="flex  md:gap-2 xl:gap-3  lg:gap-4 overflow-x-auto py-2">
                        {weeks.map((week, index) =>
                            CalenderDayOneOff(index, today, week, weeks, oneOffJobs)
                        )}

                        <div
                            style={{
                                width: '2px',
                                backgroundColor: '#DFDFDF',
                                height: '71px',
                            }}
                        />
                    </div>
                </div>
                {oneOffJobs.length > 0 &&
                    (() => {
                        const unmatchedJobs = oneOffJobs.filter(
                            (job) => !isDateInArray(weeks, new Date(job.jobDate))
                        );
                        return unmatchedJobs.slice(0, 3).map((uj, i) => (
                            <div
                                key={i}
                                className="flex flex-column justify-content-center align-items-center gap-3 mr-3"
                            >
                                <div
                                    className="flex gap-2 align-items-center"
                                    style={{
                                        borderRadius: '20px',
                                        border: '3px solid #179D52',
                                        padding: '5px 10px',
                                    }}
                                >
                                    {/* <img
                                        src={
                                            [64, 128].includes(uj.jobType)
                                                ? TutoringImg
                                                : uj.jobType === 256
                                                ? OddImg
                                                : ChildCareImg
                                        }
                                        alt="jobimg"
                                        width="18px"
                                        height="18px"
                                    /> */}
                                    <p
                                        className="m-0 p-0"
                                        style={{
                                            fontWeight: '700',
                                            fontSize: '16px',
                                            color: '#179D52',
                                        }}
                                    >
                                        {[64, 128].includes(uj.jobType)
                                            ? 'Tutoring'
                                            : uj.jobType === 256
                                            ? 'Odd'
                                            : 'Childcare'}
                                    </p>
                                </div>
                                <div className="flex gap-5">
                                    <div
                                        className="flex flex-column justify-content-center align-items-center select-none px-2"
                                        style={{
                                            border: '1px solid #179D52',
                                            height: '80px',
                                            minWidth: '54px',
                                            borderRadius: '20px',
                                            backgroundColor: '#179D52',
                                        }}
                                    >
                                        <p
                                            className="m-0 p-0"
                                            style={{
                                                fontWeight: '700',
                                                fontSize: '16px',
                                                color: '#FFFFFF',
                                            }}
                                        >
                                            {getShortDay(new Date(uj.jobDate))}
                                        </p>
                                        <p
                                            className="m-0 p-0"
                                            style={{
                                                fontWeight: '700',
                                                fontSize: '16px',
                                                color: '#FFFFFF',
                                            }}
                                        >
                                            {getDateWithSuffix(new Date(uj.jobDate))}
                                        </p>
                                        <p
                                            className="m-0 p-0"
                                            style={{
                                                fontWeight: '700',
                                                fontSize: '16px',
                                                color: '#FFFFFF',
                                            }}
                                        >
                                            {getShortMonth(new Date(uj.jobDate))}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ));
                    })()}
            </div>
        </div>
    );
};

export default ManageJobsCalender;

function CalenderDayOneOff(
    index: number,
    today: Date,
    week: Date,
    weeks: Date[],
    upComingJobs: Jobs[]
) {
    const inWeekJobs: Jobs[] = [];

    upComingJobs.forEach((uCJ) => {
        weeks.forEach((w) => {
            if (compareDates(new Date(uCJ.jobDate), w)) {
                inWeekJobs.push(uCJ);
            }
        });
    });

    function isJobDay() {
        return inWeekJobs.some((iWJ) => compareDates(new Date(iWJ.jobDate), week));
    }

    if (upComingJobs.length === 0 && inWeekJobs.length === 0) {
        return (
            <div
                key={index}
                className="flex flex-column justify-content-center align-items-center select-none px-2"
                style={{
                    border: compareDates(today, week) ? '1px solid #585858' : '',
                    height: '76px',
                    minWidth: '54px',
                    borderRadius: '20px',
                }}
            >
                <p
                    className="m-0 p-0"
                    style={{
                        fontWeight: '400',
                        fontSize: '16px',
                        color: '#585858',
                    }}
                >
                    {getShortDay(week)}
                </p>
                <p
                    className="m-0 p-0"
                    style={{
                        fontWeight: '600',
                        fontSize: '16px',
                        color: '#585858',
                    }}
                >
                    {getDateWithSuffix(week)}
                </p>
            </div>
        );
    }

    return (
        <div
            key={index}
            className="flex flex-column justify-content-center align-items-center select-none px-2"
            style={{
                border: compareDates(today, week) ? '1px solid #585858' : '',
                height: '76px',
                minWidth: '54px',
                borderRadius: '20px',
                backgroundColor:
                    isJobDay() && isDateGreaterThanOrEqual(week, today) ? '#179D52' : '#FFFFFF',
            }}
        >
            <p
                className="m-0 p-0"
                style={{
                    fontWeight: isJobDay() && isDateGreaterThanOrEqual(week, today) ? '700' : '400',
                    fontSize: '16px',
                    color:
                        isJobDay() && isDateGreaterThanOrEqual(week, today) ? '#FFFFFF' : '#585858',
                }}
            >
                {getShortDay(week)}
            </p>
            <p
                className="m-0 p-0"
                style={{
                    fontWeight: isJobDay() && isDateGreaterThanOrEqual(week, today) ? '700' : '600',
                    fontSize: '16px',
                    color:
                        isJobDay() && isDateGreaterThanOrEqual(week, today) ? '#FFFFFF' : '#585858',
                }}
            >
                {getDateWithSuffix(week)}
            </p>
            <div
                style={{
                    height: '8px',
                    width: '8px',
                    backgroundColor: '#FFFFFF',
                    borderRadius: '50%',
                }}
            />
        </div>
    );
}
