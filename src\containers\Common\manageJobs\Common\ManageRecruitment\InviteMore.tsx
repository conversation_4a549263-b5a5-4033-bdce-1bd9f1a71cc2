import React, { ComponentProps, useEffect, useRef, useState } from "react";
import { Jobs } from "../../types";
import AllFilters from "../../../AllFilters";
import { Divider } from "primereact/divider";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../store";
import { UserResult, UserSearchResponse } from "../../../../../hooks/SearchGeoSearchHook";
import { Filter } from "../../../../../store/types";
import { IoChevronForward } from "react-icons/io5";
import environment from "../../../../../helper/environment";
import Service from "../../../../../services/services";
import useLoader from "../../../../../hooks/LoaderHook";
import styles from "../../../../Helper/styles/utils-profile-completness-wizard.module.css";
// Image imports remain the same
import ProfilesImg from "../../../../../assets/images/Icons/users-profiles-check.png";
import DiscoverImg from "../../../../../assets/images/Icons/discover.png";
import FiltersImg from "../../../../../assets/images/Icons/Filter_alt.png";
import RefreshImg from "../../../../../assets/images/Icons/reset-filters.png";
import LocationImg from "../../../../../assets/images/Icons/location.png";
import StarImg from "../../../../../assets/images/Icons/star.png";
import Check from "../../../../../assets/images/Icons/check.png";
import useIsMobile from "../../../../../hooks/useIsMobile";
import { useNavigate } from "react-router-dom";
import utils from "../../../../../components/utils/util";
import CookiesConstant from "../../../../../helper/cookiesConst";
import CustomDialog from "../../../../../commonComponents/CustomDialog";
import ProviderProfile from "../../../../Parent/ProviderProfile/ProviderProfile";
import { IframeBridge } from "../../../../../services/IframeBridge";
import { convertArrayToFilters, PartialFilters as PayloadFilters, useFilterContext } from "../../../../../hooks/FilterManagerHook";

const initialFilter: { [key in "childCare" | "childcareRecuring" | "oddJobs" | "tutoring"]: PayloadFilters } = {
  childCare: {
    neighbourhood: null,
    ageGroups: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  childcareRecuring: {
    neighbourhood: null,
    ageGroups: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  oddJobs: {
    neighbourhood: null,
    age: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: null,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 1,
    responseTime: null,
    activity: null,
    tutoringCategory: [],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
  tutoring: {
    neighbourhood: null,
    age: [1, 2, 3, 4],
    experience: null,
    year12GraduationYear: 0,
    ratings: [],
    driving: [],
    otherSkills: [],
    distance: 2,
    responseTime: null,
    activity: null,
    tutoringCategory: [1, 2, 3, 4],
    auPairCategory: null,
    nationality: null,
    name: null,
  },
};

function getOverideFilters(job: Jobs): PayloadFilters {
  const filters: PayloadFilters = {};

  filters.hourlyRate = job.price;
  filters.address = [job.longitude, job.latitude];
  filters.jobTypes = [job.jobType];

  if (job.jobType === 256) {
    filters.jobSubTypes = [job.jobSubType];
  }

  if (job.isTutoringJob) {
    filters.jobDeliveryMethod = job.jobDeliveryMethod;
  }

  return filters;
}

function combineArrays<T>(array1: T[], array2: T[], key: keyof T): T[] {
  const seen = new Set<string>();
  const combinedArray = [...array1];

  array1.forEach((item) => {
    seen.add(item[key] as unknown as string);
  });

  array2.forEach((item) => {
    if (!seen.has(item[key] as unknown as string)) {
      combinedArray.push(item);
      seen.add(item[key] as unknown as string);
    }
  });

  return combinedArray;
}

const useInviteController = (job: Jobs) => {
  const [helpers, setHelpers] = useState<UserResult[]>([]);
  const [selectedHelpers, setSelectedHelpers] = useState<UserResult[]>([]);
  const [allFiltersVisible, setAllFiltersVisible] = useState<boolean>(false);
  const [defaultHelpers, setDefaultHelpers] = useState<number[]>([]);
  const [newHelpers, setNewHelpers] = useState<number[]>([]);
  const [filtersApplied, setFiltersApplied] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const { enableLoader, disableLoader } = useLoader();
  const [totalResults, setTotalResults] = useState(0);
  const [searchResult, setSearchResult] = useState<UserResult[]>([]);
  const [dropdownEnabled, setDropdownEnabled] = useState(false);
  const [hideSelected, setHideSelected] = useState(false);

  const hasLoadedRef = useRef(false);
  const resetFuncRef = useRef<() => void>(null);

  const {
    getFilters,
    getInitialFilters,
    updateFilters,
    resetFilters: filtersReset,
    update,
  } = useFilterContext({
    initialFilters:
      job.jobType === 256
        ? initialFilter.oddJobs
        : job.isTutoringJob
          ? initialFilter.tutoring
          : job.isRecurringJob
            ? initialFilter.childcareRecuring
            : initialFilter.childCare,
    overrideWith: getOverideFilters(job),
    onUpdate: (f, a) => {
      enableLoader();
      let finalPayload;
      if (a === "LoadMore") {
        setCurrentPage((prev) => prev + 1);
        finalPayload = {
          filters: f,
          pageIndex: currentPage + 1,
          pageSize: 50,
        };
      } else {
        finalPayload = {
          filters: f,
          pageIndex: currentPage,
          pageSize: 50,
        };
      }

      Service.getConnections(
        (res) => {
          if (a === "Intial") {
            setHelpers(res.results);
            setTotalResults(res.total);
            job.applicants.forEach((h) => setDefaultHelpers((prev) => [...prev, h.applicantId]));
          } else if (a === "LoadMore") {
            setHelpers((prev) => combineArrays(prev, res.results, "id"));
            setTotalResults(res.total);
          } else if (a === "Update") {
            setFiltersApplied(true);
            setHelpers(res.results);
            setTotalResults(res.total);
          } else if (a === "Reset") {
            setFiltersApplied(false);
            setHelpers(res.results);
            setTotalResults(res.total);
          }
          disableLoader();
        },
        (err) => {

          disableLoader();
        },
        finalPayload
      );
    },
  });

  const debounceSearch = utils.debounce(async (searchText) => {
    if (searchText.trim().length < 2) {
      setSearchResult([]);
      setDropdownEnabled(false);
      return;
    }
    const payload = {
      pageindex: 1,
      pageSize: 10,
      sortBy: "experience",
      filters: [
        { field: "publicName", value: searchText, operator: "contains" },
        { field: "jobDeliveryMethod", value: job.jobDeliveryMethod ?? 1, operator: "eq" },
      ],
    };
    const { success, data } = await new Promise<{
      success: boolean;
      data: UserSearchResponse | null;
    }>((res, _) => {
      Service.getConnections(
        (data) => {
          res({ data: data, success: true });
        },
        () => {
          res({ data: null, success: false });
        },
        payload
      );
    });

    if (success && data && data.results.length > 0 && searchText.trim().length > 0) {
      setSearchResult(data.results);
      setDropdownEnabled(true);
    } else {
      setSearchResult([]);
      setDropdownEnabled(false);
    }
  }, 300);

  const loadSelectedHelpers = () => {
    job.applicants.forEach((a) => {
      selectHelper(a.applicantId, false);
    });
  };
  const areFiltersChanged = (currentFilters: Filter[], defaultFilters: Filter[]): boolean => {
    const defaultFilterMap = new Map(defaultFilters.filter((f) => f.field !== "jobTypes").map((f) => [f.field, f]));

    const currentFilterMap = new Map(currentFilters.filter((f) => f.field !== "jobTypes").map((f) => [f.field, f]));

    if (defaultFilterMap.size !== currentFilterMap.size) {
      return true;
    }

    for (const [field, defaultFilter] of defaultFilterMap) {
      const currentFilter = currentFilterMap.get(field);
      if (!currentFilter) {
        return true;
      }

      if (JSON.stringify(defaultFilter.value) !== JSON.stringify(currentFilter.value)) {
        return true;
      }
    }

    return false;
  };

  const selectHelper = (id: number, isNew: boolean) => {
    setSelectedHelpers((prev) => {
      if (prev.some((h) => h.id === id)) return prev;
      const result = helpers.find((h) => h.id === id) || searchResult.find((h) => h.id === id);
      return result ? [...prev, result] : prev;
    });
    if (isNew) {
      setNewHelpers((p) => {
        if (p.some((i) => i === id) || selectedHelpers.some((h) => h.id === id)) return p;
        return [...p, id];
      });
    }
    setHelpers((prev) => {
      const result = searchResult.find((h) => h.id === id) || prev.find((h) => h.id === id);
      if (result) {
        // Ensure the selected helper is added to the main list
        return [result, ...prev.filter((h) => h.id !== id)];
      }
      return prev;
    });
    setSearchResult((prev) => {
      // Keep the selected helper in the dropdown
      return prev;
    });
  };
  const unSelectHelper = (id: number) => {
    setSelectedHelpers((prev) => prev.filter((helper) => helper.id !== id));
    setNewHelpers((ids) => ids.filter((i) => i !== id));
    setHelpers((prev) => {
      const unselectedHelper = selectedHelpers.find((h) => h.id === id);
      if (unselectedHelper && !prev.some((h) => h.id === id)) {
        return [unselectedHelper, ...prev]; // Add the unselected helper back to the helpers list
      }
      return prev;
    });
  };

  const selectTopTen = () => {
    const topHelpers =
      job.jobType !== 1
        ? helpers.filter((h) => !defaultHelpers.includes(h.id)).slice(0, 25)
        : helpers.filter((h) => !defaultHelpers.includes(h.id)).slice(0, 10);

    topHelpers.forEach((h) => selectHelper(h.id, true));
  };
  const unSelectAll = () => {
    helpers.filter((h) => !defaultHelpers.includes(h.id)).forEach((h) => unSelectHelper(h.id));
  };

  const updateQuery = (text: string) => setQuery(text);

  useEffect(() => {
    if (!hasLoadedRef.current && helpers.length > 0) {
      loadSelectedHelpers();
      hasLoadedRef.current = true;
    }
  }, [helpers]);

  return {
    helpers,
    selectedHelpers,
    allFiltersVisible,
    defaultHelpers,
    query,
    newHelpers,
    filtersApplied,
    totalResults,
    setTotalResults,
    setAllFiltersVisible,
    selectHelper,
    unSelectHelper,
    selectTopTen,
    unSelectAll,
    update,
    filtersReset,
    resetFuncRef,
    updateFilters,
    setQuery: updateQuery,
    error,
    setError: (e) => setError(e),
    dropdownEnabled,
    setDropdownEnabled: (state) => setDropdownEnabled(state),
    searchResult,
    setSearchResult: (result) => setSearchResult(result),
    debounceSearch,
    hideSelected,
    setHideSelected: (state) => setHideSelected(state),
  };
};

const InviteHeader: React.FC<ReturnType<typeof useInviteController> & { jobType: number }> = ({ jobType, ...controller }) => {
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div className="flex flex-column gap-2">
      <div className="flex gap-2">
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "30px",
            color: "#FFA500",
          }}
        >
          Select Candidates
        </p>
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "30px",
            color: "#585858",
          }}
        >
          for your job
        </p>
      </div>
      {controller.error.length >= 0 && (
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "18px",
            color: "#FF6359",
          }}
        >
          {controller.error}
        </p>
      )}
    </div>
  ) : (
    <div className="flex flex-column gap-1">
      <div>
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "22px",
            color: "#585858",
          }}
        >
          Select Candidates for your job
        </p>
      </div>
      {controller.error.length >= 0 && (
        <p
          className="m-0 p-0"
          style={{
            fontWeight: "700",
            fontSize: "18px",
            color: "#FF6359",
          }}
        >
          {controller.error}
        </p>
      )}
    </div>
  );
};

const InviteControls: React.FC<ReturnType<typeof useInviteController> & { jobType: number }> = ({ ...controller }) => {
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <>
      <div className="flex align-items-center gap-2">
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "none",
            backgroundColor: "#FFA500",
            borderRadius: "10px",
            fontWeight: "700",
            fontSize: "12px",
            color: "#FFFFFF",
            padding: "10px 20px",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.selectTopTen();
          }}
        >
          <img src={ProfilesImg} alt="images" width="14.4px" height="14.4px" />
          &nbsp; {controller.jobType !== 1 ? "Select Top 25 Candidates" : "Select Top 10 Candidates"}{" "}
        </button>
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "none",
            backgroundColor: "#FFFFFF",
            borderRadius: "10px",
            fontWeight: "400",
            fontSize: "14px",
            textDecoration: "underline",
            color: "#585858",
            padding: "10px 20px",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.unSelectAll();
          }}
        >
          Unselect All
        </button>
        <div
          className="flex align-items-center gap-1"
          style={{
            border: "1px solid #DFDFDF",
            position: "relative",
            borderRadius: "10px",
            padding: "10px 20px",
          }}
        >
          <img src={DiscoverImg} alt="discover" width="12.6px" height="12.6px" />
          <input
            type="text"
            placeholder="Search by Name"
            style={{
              backgroundColor: "transparent",
              border: "none",
              width: "240px",
            }}
            onChange={(e) => {
              e.preventDefault();
              controller.setQuery(e.target.value);
              controller.debounceSearch(e.target.value);
            }}
          />
          {controller.searchResult.length > 0 && controller.dropdownEnabled && (
            <div
              className="absolute w-full overflow-y-auto"
              style={{
                top: "100%",
                left: "0",
                transform: "translateY(10px)",
                zIndex: "5",
                backgroundColor: "#ffffff",
                borderRadius: "5px",
                border: "1px solid #bbbbbb",
                maxHeight: "15rem",
                transition: "all .3s ease-in-out",
              }}
            >
              {controller.searchResult.map((val, index) => (
                <div
                  key={index}
                  className="grid nested-grid grid-nogutter align-items-center select-none hover:bg-gray-100 cursor-pointer"
                  style={{
                    padding: "5px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    controller.selectHelper(val.id, true); // Select the user
                    controller.setDropdownEnabled(false); // Close the dropdown
                    controller.setQuery(""); // Clear the search input
                  }}
                >
                  <div className="col-2 flex justify-content-center mb-2">
                    <img
                      style={{
                        height: "50px",
                        width: "50px",
                        borderRadius: "50%",
                      }}
                      src={`${environment.getStorageURL(window.location.hostname)}/images/${val.imageSrc}`}
                      alt="helper Image"
                    />
                  </div>
                  <div className="col-10 mb-2">
                    <div className="grid grid-nogutter ml-2">
                      <div className="col-12 pl-1">
                        <p className="m-0 p-0 font-semibold" style={{ fontSize: "18px", color: "#585858" }}>
                          {val.publicName}
                        </p>
                      </div>
                      <div className="col-1 w-min flex justify-content-center align-items-center"></div>
                      <div className="col-11 flex align-items-center gap-1">
                        <img src={LocationImg} alt="location" width="10.52px" height="12px" />

                        <p className="m-0 p-0" style={{ fontSize: "16px", color: "#585858" }}>
                          {`${val.suburb}, ${val.state}`}
                        </p>
                      </div>
                    </div>
                  </div>
                  <Divider className="col-12" />
                </div>
              ))}
            </div>
          )}
        </div>
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "1px solid #DFDFDF",
            borderRadius: "10px",
            padding: "10px 20px",
            backgroundColor: "transparent",
            fontWeight: "500",
            fontSize: "14px",
            color: "#585858",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.setAllFiltersVisible(true);
          }}
        >
          <img src={FiltersImg} alt="filters" width="14.4px" height="14.4px" />
          All Filters
        </button>
        {controller.filtersApplied && (
          <button
            className="flex align-items-center gap-1 cursor-pointer"
            style={{
              border: "1px solid #FF6359",
              borderRadius: "10px",
              padding: "10px 20px",
              backgroundColor: "rgba(255, 99, 89, 0.3)",
              fontWeight: "500",
              fontSize: "12px",
              color: "#FF6359",
            }}
            onClick={(e) => {
              e.preventDefault();
              controller.filtersReset();
              if (controller.resetFuncRef.current) {
                controller.resetFuncRef.current();
              }
            }}
          >
            <img src={RefreshImg} alt="filters" width="14.4px" height="14.4px" />
            Reset filters
          </button>
        )}
      </div>
      <div className="flex align-items-center gap-2 mt-2">
        <input
          type="checkbox"
          id="hideSelected"
          className={`${styles.customCheckbox} txt-clr cursor-pointer`}
          checked={controller.hideSelected}
          onChange={(e) => controller.setHideSelected(e.target.checked)}
          style={{ fontSize: "18px" }}
        />
        <label htmlFor="hideSelected" className="cursor-pointer" style={{ fontSize: "14px", color: "#585858" }}>
          Hide Helpers Already Invited
        </label>
      </div>
    </>
  ) : (
    <div className="flex gap-2 flex-column">
      <div style={{ display: "flex", flexDirection: "row" }}>
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "none",
            backgroundColor: "#FFA500",
            borderRadius: "10px",
            fontWeight: "700",
            fontSize: "12px",
            color: "#FFFFFF",
            padding: "8px",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.selectTopTen();
          }}
        >
          <img src={ProfilesImg} alt="images" width="14.4px" height="14.4px" />
          Select Top 10 Candidates
        </button>
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "none",
            backgroundColor: "#FFFFFF",
            borderRadius: "10px",
            fontWeight: "400",
            fontSize: "12px",
            textDecoration: "underline",
            color: "#585858",
            padding: "8px",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.unSelectAll();
          }}
        >
          Unselect All
        </button>
      </div>
      <div className="flex flex-row align-items-center gap-1">
        <button
          className="flex align-items-center gap-1 cursor-pointer"
          style={{
            border: "1px solid #DFDFDF",
            borderRadius: "10px",
            padding: "8px",
            backgroundColor: "transparent",
            fontWeight: "500",
            fontSize: "12px",
            color: "#585858",
            width: "max-content",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.setAllFiltersVisible(true);
          }}
        >
          <img src={FiltersImg} alt="filters" width="14.4px" height="14.4px" />
          All Filters
        </button>
        <div className="flex align-items-center gap-1">
          <input
            type="checkbox"
            id="hideSelected"
            className={`${styles.customCheckbox} txt-clr cursor-pointer`}
            checked={controller.hideSelected}
            onChange={(e) => controller.setHideSelected(e.target.checked)}
            style={{ fontSize: "14px" }}
          />
          <label htmlFor="hideSelected" className="cursor-pointer" style={{ fontSize: "12px", color: "#585858" }}>
            Hide Helpers Already Invited
          </label>
        </div>
      </div>
      {controller.filtersApplied && (
        <button
          className="flex align-items-center gap-1 cursor-pointer w-min"
          style={{
            border: "1px solid #FF6359",
            borderRadius: "10px",
            padding: "10px 20px",
            backgroundColor: "rgba(255, 99, 89, 0.3)",
            fontWeight: "500",
            fontSize: "12px",
            color: "#FF6359",
            textWrap: "nowrap",
          }}
          onClick={(e) => {
            e.preventDefault();
            controller.filtersReset();
            if (controller.resetFuncRef.current) {
              controller.resetFuncRef.current();
            }
          }}
        >
          <img src={RefreshImg} alt="filters" width="14.4px" height="14.4px" />
          Reset filters
        </button>
      )}
      <div
        className="flex align-items-center gap-1"
        style={{
          border: "1px solid #DFDFDF",
          borderRadius: "10px",
          padding: "5px",
        }}
      >
        <img src={DiscoverImg} alt="discover" width="12.6px" height="12.6px" />
        <input
          type="text"
          placeholder="Search by Name"
          style={{
            backgroundColor: "transparent",
            border: "none",
          }}
          onChange={(e) => {
            e.preventDefault();
            controller.setQuery(e.target.value);
          }}
        />
      </div>
    </div>
  );
};

const HelperCard: React.FC<
  {
    helper: UserResult;
    actionButton: (isHovered: boolean) => React.ReactNode;
    isSelected: boolean;
    isDefault: boolean;
  } & ComponentProps<"div">
> = ({ helper, actionButton, isSelected, isDefault, ...props }) => {
  const [isHovered, setHovered] = useState(false);
  const navigate = useNavigate();
  const client = Number(utils.getCookie(CookiesConstant.clientType));
  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);
  const [selectedRequestId, setSelectedRequestId] = useState(null);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);

  const viewProvider = (helperId) => {
    IframeBridge.sendToParent({
      type: "navigateHelperProfile",
      data: {
        id: String(helperId),
      },
    });
    if (!inIframe) {
      setSelectedProviderId(helperId);
      setShowPopup(true);
    }
  };

  const handleCloseProfilePopup = () => {
    setShowPopup(false);
    setSelectedProviderId(null);
    setSelectedRequestId(null);
  };

  return (
    <>
      <div
        {...props}
        className={`flex-shrink-0 h-min flex m-1 p-2 align-items-center ${props.className}`}
        style={{
          position: "relative",
          borderWidth: "2px",
          borderStyle: "solid",
          borderColor: isSelected ? (isHovered && !isDefault ? "#585858" : "#179D52") : "#DFDFDF",
          boxShadow: isHovered && !isSelected && "0px 4px 4px 0px #00000040",
          borderRadius: "10px",
          backgroundColor: isDefault ? "#E0E0E0" : "#FFFFFF", // Darker grey for default helpers
          opacity: isDefault ? 0.5 : 1, // Slightly lower opacity for more greyed-out effect
          ...props.style,
        }}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        <img
          src={`${environment.getStorageURL(window.location.hostname)}/images/${helper.imageSrc}`}
          alt="helper img"
          className="object-cover cursor-pointer"
          width="50px"
          height="50px"
          style={{
            borderRadius: "50%",
          }}
          onClick={(e) => {
            e.preventDefault();
            // const url = `/${
            //     client === 1 ? 'parent-home' : 'business-hone'
            // }/provider-profile?id=${helper.id}`;
            // navigate(url);
            viewProvider(helper.id);
          }}
        />
        {helper.metadata?.isSuperProvider && (
          <div className="absolute w-4 flex justify-content-start" style={{ bottom: "6px" }}>
            <div
              className="text-white font-bold"
              style={{
                fontSize: "5px",
                backgroundColor: "#444444",
                padding: "3px 10px",
                paddingInline: "9px",
                borderRadius: "30px",
                userSelect: "none",
              }}
            >
              Super Helper
            </div>
          </div>
        )}
        <div className="flex-grow-1 flex flex-column pl-1 pr-2">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            {helper.publicName}
          </p>
          <div className="flex gap-2 align-items-center">
            <img className="ml-1" src={LocationImg} alt="location" width="10.52px" height="12px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "600",
                fontSize: "12px",
                color: "#585858",
                textWrap: "nowrap",
              }}
            >
              {helper.suburb}
            </p>
          </div>
          <div className="grid grid-nogutter gap-2">
            <img src={StarImg} alt="star" width="15px" height="15px" />
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "300",
                fontSize: "10px",
                color: "#585858",
              }}
            >
              {helper.jobsCompleted} Jobs completed
            </p>
          </div>
        </div>
        {actionButton(isDefault ? false : isHovered)}
      </div>
      <CustomDialog
        visible={showPopup}
        style={{
          width: "100%",
          maxWidth: "100%",
          height: "100%",
          maxHeight: "100%",
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          overflowY: "auto",
        }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile candidateId={selectedProviderId} onClose={handleCloseProfilePopup} />
      </CustomDialog>
    </>
  );
};

const ActionButton1: React.FC<ComponentProps<"div">> = (props) => {
  return (
    <div
      {...props}
      className="flex justify-content-center align-items-center cursor-pointer"
      style={{
        position: "absolute",
        top: "5px",
        right: "5px",
        backgroundColor: "#FFA500",
        width: "62px",
        height: "20px",
        borderRadius: "20px",
      }}
    >
      <p
        className="m-0 p-0 mr-1"
        style={{
          fontWeight: "600",
          fontSize: "8px",
          color: "#FFFFFF",
          // height: '8px',
        }}
      >
        Select
      </p>
      <IoChevronForward className="text-white flex justify-content-center align-items-center" size={10} />
    </div>
  );
};
const ActionButton2: React.FC<ComponentProps<"div">> = (props) => {
  return (
    <div
      {...props}
      className="flex justify-content-center align-items-center cursor-pointer"
      style={{
        position: "absolute",
        top: "5px",
        right: "5px",
        backgroundColor: "#585858",
        width: "62px",
        height: "20px",
        borderRadius: "20px",
      }}
    >
      <p
        className="m-0 p-0"
        style={{
          fontWeight: "600",
          fontSize: "8px",
          color: "#FFFFFF",
        }}
      >
        Unselect
      </p>
    </div>
  );
};

const NoActionCheck: React.FC = () => {
  return (
    <div
      className="flex justify-content-center align-items-center"
      style={{
        position: "absolute",
        top: "5px",
        right: "5px",
        width: "14px",
        height: "14px",
        backgroundColor: "#179D52",
        borderRadius: "999px",
      }}
    >
      <img src={Check} alt="" width="10px" height="10px" />
    </div>
  );
};

const SelectionArea: React.FC<ReturnType<typeof useInviteController> & { jobType: number }> = ({ jobType, ...controller }) => {
  const isSelected = (id: number) => controller.selectedHelpers.some((sh) => sh.id === id);
  const isDefault = (id: number) => controller.defaultHelpers.some((d) => d === id);

  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div
      className="flex-1 flex flex-column overflow-hidden my-2"
      style={{
        border: "1px solid #DFDFDF",
        borderRadius: "10px",
      }}
    >
      <div className="flex gap-2 flex-wrap gap-3 mx-3">
        <div className="flex gap-1 justify-content-center align-items-baseline">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "30px",
              color: "#FFA500",
            }}
          >
            {controller.totalResults}
          </p>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "18px",
              color: "#585858",
            }}
          >
            Candidates meet your criteria
          </p>
        </div>
        <div className="flex gap-1 justify-content-center align-items-baseline ">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "30px",
              color: controller.selectedHelpers.length > 0 ? "#179D52" : "#585858",
            }}
          >
            {controller.selectedHelpers.length}
          </p>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "18px",
              color: "#585858",
            }}
          >
            Candidates Selected
          </p>
        </div>
      </div>

      <div className="flex-1 mx-2 mb-2 grid grid-nogutter overflow-x-hidden overflow-y-auto">
        {combineArrays(controller.helpers, controller.selectedHelpers, "id")
          .filter((h) =>
            controller.query.length > 0
              ? h.publicName.toLowerCase().includes(controller.query.toLowerCase()) || controller.selectedHelpers.some((sh) => sh.id === h.id)
              : true
          )
          .filter((h) => !controller.hideSelected || !controller.selectedHelpers.some((sh) => sh.id === h.id))
          .map((h, i) => {
            return (
              <div className="col-12 sm:col-6 md:col-4 lg:col-3" key={i}>
                <HelperCard
                  helper={h}
                  isDefault={isDefault(h.id)}
                  isSelected={isSelected(h.id)}
                  actionButton={(isHovered) =>
                    isSelected(h.id) ? (
                      isHovered ? (
                        <ActionButton2
                          onClick={(e) => {
                            e.preventDefault();
                            controller.unSelectHelper(h.id);
                          }}
                        />
                      ) : (
                        <NoActionCheck />
                      )
                    ) : (
                      <ActionButton1
                        onClick={(e) => {
                          e.preventDefault();
                          controller.selectHelper(h.id, true);
                        }}
                      />
                    )
                  }
                />
              </div>
            );
          })}
      </div>
      <div className="col-12 flex justify-content-center">
        <button
          className="px- py-1 bg-white border-none cursor-pointer underline font-semibold"
          onClick={() => {
            controller.update();
          }}
          style={{ color: "#585858", fontSize: "18px" }}
        >
          See More
        </button>
      </div>
    </div>
  ) : (
    <div
      className="flex-1 flex flex-column overflow-hidden my-2"
      style={{
        border: "1px solid #DFDFDF",
        borderRadius: "10px",
      }}
    >
      <div className="flex-1 mx-2 mb-2 grid grid-nogutter overflow-x-hidden overflow-y-auto">
        {combineArrays(controller.helpers, controller.selectedHelpers, "id")
          .filter((h) => (controller.query.length > 0 ? h.publicName.toLowerCase().includes(controller.query.toLowerCase()) : true))
          .map((h, i) => {
            return (
              <div className="col-12 sm:col-6 md:col-4 lg:col-3" key={i}>
                <HelperCard
                  helper={h}
                  isDefault={isDefault(h.id)}
                  isSelected={isSelected(h.id)}
                  actionButton={(isHovered) =>
                    isSelected(h.id) ? (
                      isHovered ? (
                        <ActionButton2
                          onClick={(e) => {
                            e.preventDefault();
                            controller.unSelectHelper(h.id);
                          }}
                        />
                      ) : (
                        <NoActionCheck />
                      )
                    ) : (
                      <ActionButton1
                        onClick={(e) => {
                          e.preventDefault();
                          controller.selectHelper(h.id, true);
                        }}
                      />
                    )
                  }
                />
              </div>
            );
          })}
      </div>
      <div className="col-12 flex justify-content-center">
        <button
          className="px-4 py-2 bg-white border-none cursor-pointer underline text-lg font-semibold"
          onClick={() => {
            controller.update();
          }}
          style={{ color: "#585858" }}
        >
          See More
        </button>
      </div>
      <div className="flex  flex-row">
        <div style={{ backgroundColor: "#DFDFDF", width: "100%" }} className="flex gap-1 justify-content-center align-items-center flex-column">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "18px",
              color: "#585858",
            }}
          >
            {controller.totalResults}
          </p>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "10px",
              color: "#585858",
              textAlign: "center",
            }}
          >
            Candidates meet your criteria
          </p>
        </div>
        <div style={{ backgroundColor: "#179D52", width: "100%" }} className="flex gap-1 justify-content-center align-items-center flex-column ">
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "700",
              fontSize: "18px",
              color: controller.selectedHelpers.length > 0 ? "#fff" : "#fff",
            }}
          >
            {controller.selectedHelpers.length}
          </p>
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "500",
              fontSize: "10px",
              color: "#fff",
              textAlign: "center",
            }}
          >
            Candidates Selected
          </p>
        </div>
      </div>
    </div>
  );
};

const ActionArea: React.FC<{ action: (action: "cancel" | "Invite") => void }> = ({ action }) => {
  return (
    <div className="flex justify-content-between">
      <button
        className="cursor-pointer"
        style={{
          backgroundColor: "transparent",
          border: "none",
          borderRadius: "10px",
          padding: "10px 20px",
          fontWeight: "500",
          fontSize: "14px",
          color: "#585858",
        }}
        onClick={(e) => {
          e.preventDefault();
          action("cancel");
        }}
      >
        Cancel
      </button>
      <button
        className="cursor-pointer"
        style={{
          backgroundColor: "#FFA500",
          border: "none",
          borderRadius: "10px",
          padding: "10px 20px",
          fontWeight: "700",
          fontSize: "14px",
          color: "#FFFFFF",
        }}
        onClick={(e) => {
          e.preventDefault();
          action("Invite");
        }}
      >
        Invite
      </button>
    </div>
  );
};

const InviteMore: React.FC<{
  job: Jobs;
  onClose: () => void;
  onSubmit: (payload: Jobs & { newApplicants: Array<{ applicantId: number }> }) => void;
}> = ({ job, onSubmit, onClose }) => {
  const controller = useInviteController(job);
  const { isMobile } = useIsMobile();

  const onInvite = () => {
    if (controller.newHelpers.length === 0) {
      controller.setError("Must Select One Helper/Tutor To Proceed");
      return;
    }
    onSubmit({
      ...job,
      newApplicants: controller.newHelpers.map((h) => ({ applicantId: h })),
    });
  };

  return (
    <div className={!isMobile ? "w-full h-full flex flex-column px-5 py-2" : "w-full h-full flex flex-column px-3 py-2"}>
      <AllFilters
        enable={controller.allFiltersVisible}
        onClose={(p, a) => {
          if (a && a !== "reset") {
            controller.updateFilters(convertArrayToFilters(p.filters));
          }
          controller.setAllFiltersVisible(false);
        }}
        availableHelpers={null}
        selectedJobCategory={job.jobType === 256 ? "Odd Jobs" : job.isTutoringJob ? "Tutoring" : "Childcare"}
        mode="post-job"
        disableDistance={job.jobDeliveryMethod === 2}
        onResetRef={(func) => (controller.resetFuncRef.current = func)}
      />
      <InviteHeader jobType={job.jobType} {...controller} />
      <Divider className="mt-2 mb-3" />
      <InviteControls {...controller} jobType={job.jobType} />
      <SelectionArea {...controller} jobType={job.jobType} />
      <ActionArea action={(a) => (a === "Invite" ? onInvite() : onClose())} />
    </div>
  );
};

export default InviteMore;
