import React from 'react';
import styles from '../styles/payment-summary-helper.module.css';
import { Divider } from 'primereact/divider';
import { FaQuestionCircle, FaRegQuestionCircle } from 'react-icons/fa';
import backarrow from '../../../assets/images/Icons/back-icon.png';

type PaymentSummaryProps = {
    isOpen: boolean;
    onClose: () => void;
    payerName: string;
    payerAddress: string;
    paymentAmount: string;
    jobType: string;
    jobDate: string;
    parentName: string;
    helperName: string;
    hoursWorked: string;
    hourlyRate: string;
    platformFee: string;
    jobTotal: string;
};

const PaymentSummaryHelper: React.FC<PaymentSummaryProps> = ({
    isOpen,
    onClose,
    payerName,
    payerAddress,
    paymentAmount,
    jobType,
    jobDate,
    parentName,
    helperName,
    hoursWorked,
    hourlyRate,
    platformFee,
    jobTotal,
}) => {
    return (
        <div className={`${styles.overlay} ${isOpen ? styles.open : ''}`}>
            <div className={styles.container}>
                <div className={styles.header}>
                    <button className={styles.backBtn} onClick={onClose}>
                        {/* <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M15 18L9 12L15 6" stroke="#333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg> */}
                        <div style={{ height: "30px", width: "30px", backgroundColor: "#fff", borderRadius: "50%", display: "flex", justifyContent: "center", alignItems: "center" }}>
                            <img src={backarrow} alt="backarrow" width="13px" height="11px" />
                        </div>
                        Go back
                    </button>

                    <p style={{ color: "#fff", fontSize: "22px", fontWeight: "700", marginLeft: "10px" }}>Payment Summary</p>
                </div>

                <div className={styles.content}>
                    <div className={styles.summaryHeader}>
                        <div>
                            <h2 style={{ fontSize: "14px", fontWeight: "400", color: "#585858" }}>Payment from</h2>
                            <p className={styles.payerName}>{payerName}</p>
                            <p className={styles.payerAddress}>{payerAddress}</p>
                        </div>
                        <div className={styles.amountBox}>
                            <span>Payment Amount Due</span>
                            <p className={styles.amount}>${paymentAmount}</p>
                        </div>
                    </div>

                    <div className={styles.jobDetails}>
                        <div className='flex flex-row justify-content-between'>
                            <div className='flex flex-column gap-1'>
                                <span style={{ fontSize: "14px", fontWeight: "400", color: "#585858" }}>Job details</span>
                                <span className={styles.jobType}>{jobType}</span>
                            </div>
                            <div className='flex flex-column gap-1'>
                                <span style={{ fontSize: "14px", fontWeight: "400", color: "#585858" }}>Parent Name</span>
                                <span className={styles.jobType}>{parentName}</span>
                            </div>
                        </div>
                        <div className='flex flex-row justify-content-between'>
                            <div className='flex flex-column gap-1'>
                                <span style={{ fontSize: "14px", fontWeight: "400", color: "#585858" }}>Job Date</span>
                                <span className={styles.jobType}>{jobDate}</span>
                            </div>
                            <div className='flex flex-column gap-1'>
                                <span style={{ fontSize: "14px", fontWeight: "400", color: "#585858" }}>Helper Name</span>
                                <span className={styles.jobType}>{helperName}</span>
                            </div>
                        </div>
                    </div>

                    <div className={styles.paymentDetails}>
                        <h3 style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>Payment Details</h3>
                        <div className={styles.detailRow}>
                            <span style={{ fontSize: "12px", fontWeight: "500", color: "#585858" }}>{hoursWorked} hours x ${hourlyRate}/h</span>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>${paymentAmount}</span>
                        </div>
                        <div className={styles.detailRow}>
                            <span style={{ fontSize: "12px", fontWeight: "500", color: "#585858" }}>
                                Juggle Street Platform Fee
                                <FaRegQuestionCircle style={{ color: "#37A950", fontSize: "16px" }} />
                            </span>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>-${platformFee}</span>
                        </div>
                        <Divider className='mb-2' />
                        <div className={styles.detailRowTotal}>
                            <span style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>Job Total</span>
                            <span className={styles.jobTotal}>${jobTotal}</span>
                        </div>
                        <Divider className='mb-2' />
                    </div>

                    <div className={styles.footer}>
                        <span style={{ fontSize: "14px", fontWeight: "600", color: "#585858" }}>When will I receive this payment?</span>
                        <span style={{ fontSize: "12px", fontWeight: "300", color: "#585858" }}>Learn more about our <span style={{ fontSize: "12px", fontWeight: "700", color: "#FFA500" }}>payment process here</span></span>
                    </div>
                    <Divider className='mb-4' />
                    <button className={styles.closeBtn} onClick={onClose}>Go back</button>
                </div>
            </div>
        </div>
    );
};

export default PaymentSummaryHelper;