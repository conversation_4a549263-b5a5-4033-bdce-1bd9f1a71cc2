.jobHistoryCard {
    border: 1px solid #DFDFDF;
    border-radius: 8px;
    width: 65vw;
    max-width: 670px;
    height: auto;
    background-color: #fff;
  }
  
  .cardContainer {
    display: flex;
    justify-content: space-around;
    padding: 0.5vw;
    gap: 0.3vw;
    height: 100%;
  }
  
  .profileSection {
    display: flex;
    align-items: center;
    gap: 0.3vw;
    width: 32%;
    min-width: 160px;
  }
  
  .avatarContainer {
    position: relative;
  }
  
  .avatarImage {
    width: 6vw;
    height: 6vw;
    max-width: 70px;
    max-height: 65px;
  }
  
  .avatarPlaceholder {
    width: 6vw;
    height: 6vw;
    max-width: 70px;
    max-height: 65px;
    background-color: #D3D3D3;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .noImageText {
    color: #FFF;
    font-size: clamp(7px, 0.9vw, 10px);
  }
  
  .profileDetails {
    display: flex;
    flex-direction: column;
  }
  
  .nameText {
    font-size: clamp(10px, 1.3vw, 14px);
    color: #585858;
  }
  
  .jobInfo {
    font-size: clamp(8px, 1vw, 12px);
    color: #585858;
    font-weight: 600;
  }
  
  .viewLink {
    font-size: clamp(8px, 1vw, 10px);
    color: #FFA500;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
  }
  
  .awardedText,
  .archivedText {
    font-size: clamp(7px, 0.9vw, 10px);
    font-weight: 600;
    color: #585858;
    width: 6vw;
    max-width: 70px;
  }
  
  .detailsSection {
    display: flex;
    flex-direction: column;
    gap: 0.3vw;
  }
  
  .timeDateContainer {
    display: flex;
    gap: 0.3vw;
  }
  
  .timeBox,
  .dateBox,
  .addressBox {
    display: flex;
    align-items: center;
    gap: 0.2vw;
    border: 1px solid #DFDFDF;
    border-radius: 6px;
    padding: 0.3vw;
    height: 2vw;
    max-height: 30px;
    text-wrap: noWrap;
  }
  
  .timeBox {
    width: 10vw;
    max-width: 110px;
    text-wrap: noWrap;
  }
  
  .dateBox {
    width: 12vw;
    max-width: 130px;
    text-wrap: noWrap;
  }
  
  .addressBox {
    width: 20vw;
    max-width: 235px;
  }
  
  .icon {
    width: 0.9vw;
    height: 0.9vw;
    max-width: 10px;
    max-height: 10px;
  }
  
  .timeText,
  .dateText,
  .addressText {
    font-size: clamp(6px, 0.8vw, 9px);
    color: #585858;
    font-weight: 600;
  }
  
  .divider {
    width: 0.5px;
    background-color: #DFDFDF;
    height: auto;
  }
  
  .actionsSection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.3vw;
    width: 25%;
    min-width: 160px;
  }
  
  .ratingContainer {
    display: flex;
    flex-direction: column;
    gap: 0.3vw;
    align-items: center;
  }
  
  .cancelledSection,
  .buttonGroup {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3vw;
  }
  
  .cancelledText {
    color: #585858;
    font-size: clamp(10px, 1.3vw, 14px);
  }
  
  .rateButton,
  .duplicateButton,
  .chatButton {
    padding: 0.4vw 1vw;
    font-size: clamp(8px, 1vw, 10px);
    font-weight: 800;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12vw;
    max-width: 140px;
    height: 1.8vw;
    max-height: 26px;
  }
  
  .rateButton {
    background-color: #FFA500;
    color: white;
    border: none;
  }
  
  .duplicateButton {
    border: 1px solid #585858;
    color: #585858;
    background-color: #fff;
  }
  
  .chatButton {
    border: 1px solid black;
    width: 8vw;
    max-width: 80px;
    height: 2vw;
    max-height: 30px;
    box-shadow: 0px 2px 2px 0px #00000040;
  }
  
  .chatText {
    color: black;
  }
  
  .ratingDisplay {
    display: flex;
    align-items: center;
    gap: 0.2vw;
  }
  
  .ratingLabel {
    font-size: clamp(10px, 1.3vw, 14px);
    color: #585858;
    font-weight: 600;
    margin-right: 0.3vw;
  }
  
  /* Rating Star Styles */
  .starContainer {
    display: inline-block;
    position: relative;
    width: 1.5vw;
    height: 1.5vw;
    max-width: 20px;
    max-height: 20px;
  }
   @media (max-width: 1000px) and (min-width: 900px) {
  .jobHistoryRow {
    display: grid;
    grid-template-columns: repeat(2, 0fr);
    gap: 1vw;
    max-width: 1069px;
    margin: 0 auto;
  }
  
  .jobHistoryCard {
    border: 1px solid #DFDFDF;
    border-radius: 8px;
    width: 100%; /* Updated */
    height: auto;
    margin: 0; /* Updated */
    background-color: #fff;
  }
  
  /* Existing cardContainer and other styles remain unchanged */
  .cardContainer {
    display: flex;
    justify-content: space-around;
    padding: 0.5vw;
    gap: 0.3vw;
    height: 100%;
  }

}
  
  /* ... (rest of your existing styles) ... */
  
  @media (max-width: 1069px) and (min-width: 900px) {
    .jobHistoryCard {
      width: 100%;
    }
  }
  
  @media (max-width: 900px) {
    .jobHistoryRow {
      grid-template-columns: 1fr;
      gap: 1vw;
    }
  }
  
  @media (max-width: 600px) {
    .jobHistoryRow {
      grid-template-columns: 1fr;
      gap: 1vw;
    }
  }

  @media (max-width: 1000px) {
    .jobHistoryCard {
      width: 32vw;
      max-width: 800px;
    }
  
    .cardContainer {
      flex-direction: column; /* Wrap to column at 1107px */
      padding: 0.8vw;
      gap: 0.6vw; /* Smaller gap between sections */
    }
  
    .profileSection,
    .detailsSection {
      width: 100%;
      min-width: unset;
    }
    .actionsSection {
        width: 100%;
        display: flex;
        align-items: start;
      }
  
    .avatarImage,
    .avatarPlaceholder {
      width: 8vw;
      height: 8vw;
      max-width: 60px;
      max-height: 55px;
    }
  
    .timeDateContainer {
      flex-direction: row;
      gap: 0.3vw;
    }
  
    .timeBox {
      width: 30%;
      max-width: 100px;
      text-wrap: noWrap;
    }
  
    .dateBox {
      width: 30%;
      max-width: 110px;
      text-wrap: noWrap;
    }
  
    .addressBox {
      width: 100%;
      max-width: 230px;
    }
  
    .divider {
      width: 100%;
      height: 0.5px;
    }
  
    .chatButton {
      width: 20%;
      max-width: 70px;
    }
  
    .starContainer {
      width: 1.2vw; /* Smaller stars at 1107px */
      height: 1.2vw;
      max-width: 18px;
      max-height: 18px;
    }
  
    /* Smaller font sizes at 1107px */
    .nameText,
    .cancelledText,
    .ratingLabel {
      font-size: clamp(9px, 1.2vw, 12px);
    }
  
    .jobInfo,
    .viewLink {
      font-size: clamp(7px, 0.9vw, 10px);
    }
  
    .awardedText,
    .archivedText {
      font-size: clamp(6px, 0.8vw, 9px);
    }
  
    .timeText,
    .dateText,
    .addressText {
      font-size: clamp(5px, 0.7vw, 8px);
    }
  
    .rateButton,
    .duplicateButton,
    .chatButton {
      font-size: clamp(7px, 0.9vw, 9px);
    }
  }
  
  /* Smaller screens (600px - 1107px) - Further compactness */
  @media (max-width: 900px) {
    .jobHistoryCard {
      width: 60vw;
      max-width: 600px;
    }
  
    .cardContainer {
      padding: 0.6vw;
      gap: 0.4vw; /* Even smaller gap */
    }
  
    .avatarImage,
    .avatarPlaceholder {
      width: 10vw;
      height: 10vw;
      max-width: 50px;
      max-height: 45px;
    }
  
    .timeDateContainer {
      flex-direction: column;
      gap: 0.2vw; /* Smaller gap */
    }
  
    .timeBox,
    .dateBox,
    .addressBox {
      width: 100%;
      max-width: unset;
      height: 2vw;
      max-height: 32px;
      text-wrap: noWrap;
    }
    .chatButton {
      width: 22%;
      max-width: 60px;
    }
  
    .starContainer {
      width: 1vw;
      height: 1vw;
      max-width: 16px;
      max-height: 16px;
    }
  }
  
  /* Very small screens (below 600px) - Optional, assuming mobile kicks in */
  @media (max-width: 600px) {
    .jobHistoryCard {
      width: 100vw;
      border-radius: 0;
    }
  
    .cardContainer {
      padding: 1vw;
      gap: 0.8vw;
    }
  
    .starContainer {
      width: 0.8vw;
      height: 0.8vw;
      max-width: 14px;
      max-height: 14px;
    }
  }
  