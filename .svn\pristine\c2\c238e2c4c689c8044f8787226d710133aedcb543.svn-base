import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import React, { useEffect, useRef, useState } from 'react';
import backArrow from '../../../../assets/images/Icons/back-icon.png';
import useIsMobile from '../../../../hooks/useIsMobile';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';
interface ConfirmAwardDialogProps {
    isOpen: boolean;
    title?: string;
    onOpenChange: (open: boolean) => void;
    onConfirm: () => void;
    onCancel: () => void;
    name?: string;
    image?: string;
    message?: string; // Add a prop for custom message
    yesText?: string; // Add a prop for custom Yes button text
    noText?: string;
    onApply?: () => void;
}

const ConfirmAwardDialog: React.FC<ConfirmAwardDialogProps> = ({
    isOpen,
    title,
    onOpenChange,
    onConfirm,
    onCancel,
    onApply,
    name,
    image,
    message, // Destructure the message prop
    yesText = 'Yes', // Default value for Yes button text
    noText = 'No',
}) => {
    const { isMobile } = useIsMobile();
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    return !isMobile ? (
        <Dialog
            visible={isOpen}
            onHide={() => onOpenChange(false)} // Called when the dialog is closed
             className='w-4 h-min'
            content={
                <div
                    className=''
                    style={{
                        backgroundColor: 'white',
                        borderRadius: '20px',
                        border: '3px solid #179D52',
                    }}
                >
                    <div className='flex flex-column justify-content-center  p-3'>
                        <div className='flex justify-content-between '>
                            <h2
                                className='font-bold p-0 m-0'
                                style={{ color: '#585858', fontSize: '32px' }}
                            >
                                {title}
                            </h2>
                            {image ? (
                                <div style={{ position: 'relative', marginLeft: '10px' }}>
                                    {/* Image */}
                                    <img
                                        style={{
                                            height: '53px',
                                            width: '53px',
                                            borderRadius: '50%',
                                        }}
                                        src={image}
                                        alt='Helper Image'
                                    />
                                    {/* Green Circular Tick */}
                                    <div
                                        style={{
                                            position: 'absolute',
                                            top: '0',
                                            right: '0',
                                            height: '16px',
                                            width: '17px',
                                            borderRadius: '50%',
                                            backgroundColor: 'green',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                    >
                                        <span
                                            style={{
                                                color: 'white',
                                                fontSize: '10px',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            ✔
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <div className='w-16 h-16 rounded-full bg-gray-200' />
                            )}
                        </div>
                        <Divider />

                        <p className='font-medium' style={{ fontSize: '20px', color: '#585858' }}>
                            {message ? (
                                <span>
                                    {message}{' '}
                                    <strong
                                        className='font-bold'
                                        style={{ color: '#585858', fontSize: '20px' }}
                                    >
                                        {name}?
                                    </strong>
                                </span>
                            ) : (
                                <>
                                    Would you like to award this job to{' '}
                                    <strong
                                        className='font-bold'
                                        style={{ color: '#585858', fontSize: '20px' }}
                                    >
                                        {name}?
                                    </strong>
                                </>
                            )}
                        </p>

                        <div className='flex justify-content-start  gap-3  '>
                            <button
                                className=' cursor-pointer underline font-medium'
                                onClick={onCancel}
                                style={{
                                    color: '#585858',
                                    backgroundColor: 'transparent',
                                    border: '1px solid #DFDFDF',
                                    borderRadius: '5px',
                                    width: '95px',
                                    height: '42px',
                                    fontSize: '18px',
                                }}
                            >
                                {noText}
                            </button>
                            <button
                                className='text-white cursor-pointer underline font-bold'
                                onClick={onConfirm}
                                style={{
                                    color: '#585858',
                                    backgroundColor: '#FFA500',
                                    border: '1px solid #DFDFDF',
                                    borderRadius: '5px',
                                    width: '209px',
                                    height: '42px',
                                    fontSize: '18px',
                                }}
                            >
                                {yesText}
                            </button>
                        </div>
                    </div>
                </div>
            }
        />
    ) : (
        <Dialog
            visible={isOpen}
            onHide={() => onOpenChange(false)} // Called when the dialog is closed
            className='w-4 h-min'
            position='bottom'
            style={{
                margin: 0,
                padding: 0,
                width: '100vw',
                maxWidth: '100%',
                transform: 'none',
                transition: 'none',
            }}
            content={
                <div
                    className=''
                    style={{
                        backgroundColor: 'white',
                        borderTopLeftRadius: '30px',
                        borderTopRightRadius: '30px',
                        border: '3px solid #179D52',
                        position: 'fixed',
                        bottom: '0',
                        left: '0',
                        width: '100%', // Ensure it spans full width
                    }}
                >
                    <div>
                        <div className='flex justify-content-between p-3'>
                            <div
                                className=' cursor-pointer underline font-medium'
                                onClick={onCancel}
                                style={{
                                    color: '#585858',
                                    borderRadius: '5px',
                                    fontSize: '12px',
                                    paddingTop: '15px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '5px',
                                }}
                            >
                                <div
                                    style={{
                                        backgroundColor: 'rgba(217, 217, 217, 0.3)',
                                        paddingInline: '8px',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        paddingBlock: '8px',
                                        borderRadius: '50%',
                                    }}
                                >
                                    {' '}
                                    <img
                                        src={backArrow}
                                        width={'10px'}
                                        height={'8px'}
                                        alt='backArrow'
                                    />
                                </div>
                                Go Back
                            </div>
                            {image ? (
                                <div
                                    style={{
                                        position: 'relative',
                                        marginLeft: '10px',
                                        bottom: '34px',
                                    }}
                                >
                                    {/* Image */}
                                    <img
                                        style={{
                                            height: '65px',
                                            width: '69px',
                                            borderRadius: '50%',
                                            border: '2px solid #179D52',
                                        }}
                                        src={image}
                                        alt='Helper Image'
                                    />
                                    {/* Green Circular Tick */}
                                    <div
                                        style={{
                                            position: 'absolute',
                                            top: '0',
                                            right: '0',
                                            height: '16px',
                                            width: '17px',
                                            borderRadius: '50%',
                                            backgroundColor: 'green',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                    >
                                        <span
                                            style={{
                                                color: 'white',
                                                fontSize: '10px',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            ✔
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <div className='w-16 h-16 rounded-full bg-gray-200' />
                            )}
                        </div>
                        <div className='flex flex-column justify-content-center  p-3'>
                            <div className='flex justify-content-between '>
                                <h2
                                    className='font-bold p-0 m-0'
                                    style={{ color: '#585858', fontSize: '22px' }}
                                >
                                    {title}
                                </h2>
                            </div>
                            <Divider />

                            <p
                                className='font-medium'
                                style={{ fontSize: '16px', color: '#585858' }}
                            >
                                {message ? (
                                    <span>
                                        {message}{' '}
                                        <strong
                                            className='font-bold'
                                            style={{ color: '#585858', fontSize: '20px' }}
                                        >
                                            {name}?
                                        </strong>
                                    </span>
                                ) : (
                                    <>
                                        Would you like to award this job to{' '}
                                        <strong
                                            className='font-bold'
                                            style={{ color: '#585858', fontSize: '20px' }}
                                        >
                                            {name}?
                                        </strong>
                                    </>
                                )}
                            </p>

                            <div className='flex justify-content-start  gap-3  '>
                                <button
                                    className='text-white cursor-pointer  font-bold'
                                    onClick={onConfirm}
                                    style={{
                                        color: '#585858',
                                        backgroundColor: '#FFA500',
                                        border: '1px solid #DFDFDF',
                                        borderRadius: '20px',
                                        paddingInline: '20px',
                                        paddingBlock: '10px',
                                        fontSize: '14px',
                                    }}
                                >
                                  {clientType === 0 ? "Award Job" : "Award Job"}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        />
    );
};

export default ConfirmAwardDialog;
