.homeHeaderContainer {
  z-index: 999;
  color: #585858;
  background-color: white !important;
}

.noTextWrap {
  text-wrap: nowrap;
  font-size: 14px !important;
  font-family: "Poppins", sans-serif !important; /* Apply Poppins font */
  font-weight: 500 !important;
}

.overlayPanelOverride > div[class="p-overlaypanel-content"] {
  padding: 0;
  padding-block: 20px;
  padding-inline: 10px;
}
.mainOptions {
  font-size: 20px !important;
  color: #585858 !important;
  font-weight: 600 !important;
}
.referbtn {
  font-size: 12px !important;
  background-color: transparent !important;
  border: 1px solid #585858 !important;
  color: #585858 !important;
  border-radius: 10px !important;
  width: max-content !important;
  height: 38px !important;
  padding: 10px !important;
  font-weight: 500 !important;
  line-height: 18px !important ;
  margin-top: 10px !important;
  box-shadow: none !important;
  margin-left: 7px !important;
  display: flex;
  justify-content: center;
  align-items: normal;
  gap: 9px;
  transition: all 0.1s ease-in-out;
}
.referbtn:hover{
  color: #FFA500 !important;
  font-weight: 700 !important;
  border: 2px solid #FFA500 !important;
  box-shadow: 0px 4px 4px 0px #00000040;
}
.defaultIcon {
  display: block;
  transition: opacity 0.1s ease-in-out;
}

.hoverIcon {
  display: none;
  transition: opacity 0.1s ease-in-out;
}

.referbtn:hover .defaultIcon {
  display: none;
}

.referbtn:hover .hoverIcon {
  display: block;
}

@media only screen and (min-width: 576px) {
  .homeHeaderContainer {
    background-color: transparent !important;
  }
}

.imageStyle {
  overflow: hidden;
}

.imageStyle img {
  object-fit: cover;
}
