import { useEffect, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useJobManager } from "../provider/JobManagerProvider";
import { RootState } from "../../../../store";
import { useSelector } from "react-redux";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { UserSearchResponse } from "../../../../hooks/SearchGeoSearchHook";
import CustomDialog from "../../CustomDialog";
import { Divider } from "primereact/divider";
import styles from "../../styles/candidate-matching.module.css";
import SideArrow from "../../../../assets/images/Icons/side_arrow_left.png";
import CustomFooterButton from "../../../../commonComponents/CustomFooterButtonMobile";
import c from "../../../../helper/juggleStreetConstants";
import { GoBack } from "./Buttons";
import ReactDOM from "react-dom";
import BackButtonPortal from "../../../../commonComponents/BackButtonPortal";

interface ExperienceLevel {
  value: number;
  label: string;
}

function CandidateMatching() {
  const { isMobile } = useIsMobile();
  return isMobile ? <CandidateMatchingMobile /> : <CandidateMatchingWeb />;
}

export default CandidateMatching;
const useJobTypeHook = () => {
  const { payload, setpayload, prev, next } = useJobManager();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo.data);
  const { isMobile } = useIsMobile();
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [distance, setDistance] = useState<number>(() => {
    return payload.jobType === 64 || payload.jobType === 128 ? 2 : 1;
  });
  const [ageRanges, setAgeRanges] = useState(["16-17", "18-24", "25-44", "45+"]);
  const defaultPayload = useSelector((state: RootState) => state.applicationState.defaultFilters);
  const [selectedCriteriaGroup3, setSelectedCriteriaGroup3] = useState<number[]>([]);

  const [year12GraduationYear, setYear12GraduationYear] = useState(payload.jobType === 64 || payload.jobType === 128 ? 0 : null);
  const [responseTime, setResponseTime] = useState(null);
  const [error, setError] = useState("");
  const [name, setName] = useState(null);
  const { disableLoader, enableLoader } = useLoader();

  // const [hourlyRate, setHourlyRate] = useState(23);
  const [age, setAge] = useState([1, 2, 3, 4]);

  const experienceLevels: ExperienceLevel[] = [
    { value: 1, label: "Newbie" },
    { value: 2, label: "Apprentice" },
    { value: 3, label: "Experienced" },
    { value: 4, label: "Professional" },
  ];

  // Initialize with all experience levels selecte
  const [selectedExperienceLevels, setSelectedExperienceLevels] = useState<number[]>(experienceLevels.map((level) => level.value));
  const handleExperienceLevelChange = (levelId: number) => {
    setSelectedExperienceLevels((prev) => (prev.includes(levelId) ? prev.filter((id) => id !== levelId) : [...prev, levelId]));
  };

  useEffect(() => {
    const ageValues = ageRanges.map((range) => {
      switch (range) {
        case "16-17":
          return 1;
        case "18-24":
          return 2;
        case "25-44":
          return 3;
        case "45+":
          return 4;
        default:
          return 0;
      }
    });
    setAge(ageValues);
  }, [ageRanges]);
  useEffect(() => {
    // Update distance whenever jobType changes
    if (payload.jobType === 64 || payload.jobType === 128) {
      setDistance(2);
    }
  }, [payload.jobType]);

  const handleAgeChange = (ageRange) => {
    setAgeRanges((prevState) => {
      if (prevState.includes(ageRange)) {
        return prevState.filter((range) => range !== ageRange); // Remove if already selected
      } else {
        return [...prevState, ageRange]; // Add to selected ranges
      }
    });
  };
  const handleCriteriaGroup3Change = (value: number) => {
    setSelectedCriteriaGroup3(
      (prev) =>
        prev.includes(value)
          ? prev.filter((item) => item !== value) // Remove from array if already selected
          : [...prev, value] // Add to array if not selected
    );
  };

  const openDialog = () => {
    setIsDialogVisible(true);
  };

  const closeDialog = () => {
    setIsDialogVisible(false);
  };
  const canConfirm = distance !== null && ageRanges.length > 0;

  // const handleConfirm = async () => {
  //   const filtersToSave = {
  //     ...defaultPayload,
  //     filters: [
  //       ...defaultPayload.filters
  //         .filter((filter) => filter.field !== "ageGroups" && filter.field !== "jobDeliveryMethod" && filter.field !== "jobSubTypes")
  //         .map((filter) => {
  //           switch (filter.field) {
  //             case "neighbourhood":
  //               return { ...filter, value: null };
  //             case "experience":
  //               return { ...filter, value: null };
  //             case "activity":
  //               return { ...filter, value: null };
  //             case "jobTypes":
  //               return { ...filter, value: Number(payload.jobType) === 12 ? [4, 8] : [Number(payload.jobType)],};
  //             case "otherSkills":
  //               return { ...filter, value: selectedCriteriaGroup3 };
  //             case "tutoringCategory":
  //               return {
  //                 ...filter,
  //                 value: payload.jobType === 64 || payload.jobType === 128 ? selectedExperienceLevels : [],
  //               };
  //             case "distance":
  //               return {
  //                 ...filter,
  //                 value: distance, // This will ensure the correct distance value is used
  //               };
  //             default:
  //               return filter;
  //           }
  //         }),
  //       ...(ageRanges.length < 4
  //         ? [
  //             {
  //               field: "age",
  //               operator: "eq",
  //               value: age,
  //             },
  //           ]
  //         : []),

  //       {
  //         field: "year12GraduationYear",
  //         operator: "eq",
  //         value: year12GraduationYear,
  //       },
  //       {
  //         field: "responseTime",
  //         operator: "eq",
  //         value: responseTime,
  //       },
  //       {
  //         field: "name",
  //         operator: "eq",
  //         value: name,
  //       },
  //       {
  //         field: "address",
  //         operator: "eq",
  //         value: [payload.longitude, payload.latitude],
  //       },
  //       {
  //         field: "distance",
  //         operator: "eq",
  //         value: distance, // Ensure this is correctly mapped
  //       },

  //       // {
  //       //   field: "otherSkills",
  //       //   operator: "eq",
  //       //   value: [],
  //       // },
  //       // {
  //       //   field: "otherSkills",
  //       //   operator: "eq",
  //       //   value: [],
  //       // },
  //       // {
  //       //   field: "otherSkills",
  //       //   operator: "eq",
  //       //   value: [],
  //       // },
  //       // {
  //       //   field: "otherSkills",
  //       //   operator: "eq",
  //       //   value: [],
  //       // },
  //       {
  //         field: "hourlyRate",
  //         operator: "eq",
  //         value: payload.price,
  //       },

  //       ...(selectedExperienceLevels.length < 4
  //         ? [
  //             {
  //               field: "tutoringCategory",
  //               operator: "eq",
  //               value: selectedExperienceLevels,
  //             },
  //           ]
  //         : []),
  //     ],
  //     includeUsers: [],
  //     // ],
  //     // includeUsers: [],
  //   };
  //   enableLoader();
  //   const allowed = await new Promise<boolean>((resolve, reject) => {
  //     Service.getConnections(
  //       (data: UserSearchResponse) => resolve(data.results.length > 1),

  //       (error: any) => {
  //         reject(false);
  //       },
  //       filtersToSave
  //     );
  //   });
  //   disableLoader();
  //   if (!allowed) {
  //     setError("No candidates match specified criteria.");
  //     return;
  //   }
  //   setpayload({
  //     ...payload,
  //     applicantFilters:
  //       payload.applicantFilters === null
  //         ? [
  //             ...(ageRanges.length < 4
  //               ? [
  //                   {
  //                     field: "age",
  //                     operator: "eq",
  //                     value: age,
  //                   },
  //                 ]
  //               : []),

  //             ...(selectedCriteriaGroup3.length > 0
  //               ? [
  //                   {
  //                     field: "otherSkills",
  //                     operator: "eq",
  //                     value: selectedCriteriaGroup3,
  //                   },
  //                 ]
  //               : []),
  //             ...(selectedExperienceLevels.length < 4
  //               ? [
  //                   {
  //                     field: "tutoringCategory",
  //                     operator: "eq",
  //                     value: selectedExperienceLevels,
  //                   },
  //                 ]
  //               : []),
  //             {
  //               field: "distance",
  //               operator: "eq",
  //               value: payload.jobType === 64 || payload.jobType === 128 ? 2 : distance,
  //             },
  //           ]
  //         : [
  //             ...payload.applicantFilters.filter(
  //               (af) => af.field !== "distance" && af.field !== "otherSkills" && af.field !== "tutoringCategory" && af.field !== "age"
  //             ),
  //             ...(ageRanges.length < 4
  //               ? [
  //                   {
  //                     field: "age",
  //                     operator: "eq",
  //                     value: age,
  //                   },
  //                 ]
  //               : []),
  //             // {
  //             //   field: "age",
  //             //   operator: "eq",
  //             //   value: age,
  //             // },
  //             ...(Number(payload.jobDeliveryMethod) !== 2
  //               ? [
  //                   {
  //                     field: "distance",
  //                     operator: "eq",
  //                     value: payload.jobType === 64 || payload.jobType === 128 ? 2 : distance,
  //                   },
  //                 ]
  //               : []),
  //             ...(selectedExperienceLevels.length < 4
  //               ? [
  //                   {
  //                     field: "tutoringCategory",
  //                     operator: "eq",
  //                     value: selectedExperienceLevels,
  //                   },
  //                 ]
  //               : []),
  //             ...(selectedCriteriaGroup3.length > 0
  //               ? [
  //                   {
  //                     field: "otherSkills",
  //                     operator: "eq",
  //                     value: selectedCriteriaGroup3,
  //                   },
  //                 ]
  //               : []),
  //           ],
  //   });
  //   if (sessionInfo?.["paymentInfo"]?.paymentType === c.userPaymentType.FREE) {
  //     next("subscription");
  //     return;
  //   }
  //   if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
  //     next("job-summary"); // Navigate to the next step
  //   } else {
  //     next("review-post"); // Navigate to the next step
  //   }

  // };

  const handleConfirm = async () => {
    // Build the filters object in a more structured way
    const buildFilters = () => {
      const baseFilters = defaultPayload.filters
        .filter(filter => {
          if (["ageGroups"].includes(filter.field)) {
            return false;
          }
          if (filter.field === "jobDeliveryMethod") {
            return payload.jobType === 64 || payload.jobType === 128;
          }
          if (filter.field === "jobSubTypes") {
            return payload.jobType === 256;
          }
          return true;
        })
        .map(filter => {
          switch (filter.field) {
            case "neighbourhood":
            case "experience":
            case "activity":
              return { ...filter, value: null };
            case "jobTypes":
              return {
                ...filter,
                value: Number(payload.jobType) === 12 ? [4, 8] : [Number(payload.jobType)]
              };
            case "otherSkills":
              return { ...filter, value: selectedCriteriaGroup3 };
            case "tutoringCategory":
              return {
                ...filter,
                value: payload.jobType === 64 || payload.jobType === 128 ? selectedExperienceLevels : [],
              };
            case "jobDeliveryMethod":
              return { ...filter, value: Number(payload.jobDeliveryMethod) };

            case "jobSubTypes":
              return { ...filter, value: [payload.jobSubType] };  
            case "distance":
              return { ...filter, value: distance };
            default:
              return filter;
          }
        });

      const conditionalFilters = [
        ...(ageRanges.length < 4 ? [{
          field: "age",
          operator: "eq",
          value: age,
        }] : []),

        ...(selectedExperienceLevels.length < 4 ? [{
          field: "tutoringCategory",
          operator: "eq",
          value: selectedExperienceLevels,
        }] : []),
      ];

      const staticFilters = [
        {
          field: "year12GraduationYear",
          operator: "eq",
          value: year12GraduationYear,
        },
        {
          field: "responseTime",
          operator: "eq",
          value: responseTime,
        },
        {
          field: "name",
          operator: "eq",
          value: name,
        },
        {
          field: "address",
          operator: "eq",
          value: [payload.longitude, payload.latitude],
        },
        {
          field: "distance",
          operator: "eq",
          value: distance,
        },
        {
          field: "hourlyRate",
          operator: "eq",
          value: payload.price,
        },
      ];

      return [...baseFilters, ...conditionalFilters, ...staticFilters];
    };

    // Build the applicant filters
    const buildApplicantFilters = () => {
      const baseFilters = payload.applicantFilters === null ? [] :
        payload.applicantFilters.filter(
          af => !["distance", "otherSkills", "tutoringCategory", "age"].includes(af.field)
        );

      const conditionalFilters = [
        ...(ageRanges.length < 4 ? [{
          field: "age",
          operator: "eq",
          value: age,
        }] : []),

        ...(payload.jobType === 64 || payload.jobType === 128 ? [{
          field: "jobDeliveryMethod",
          operator: "eq",
          value: payload.jobDeliveryMethod,
        }] : []),

        ...(Number(payload.jobDeliveryMethod) !== 2 ? [{
          field: "distance",
          operator: "eq",
          value: payload.jobType === 64 || payload.jobType === 128 ? 2 : distance,
        }] : []),

        ...(selectedExperienceLevels.length < 4 ? [{
          field: "tutoringCategory",
          operator: "eq",
          value: selectedExperienceLevels,
        }] : []),

        ...(selectedCriteriaGroup3.length > 0 ? [{
          field: "otherSkills",
          operator: "eq",
          value: selectedCriteriaGroup3,
        }] : []),
      ];

      return [...baseFilters, ...conditionalFilters];
    };

    // Prepare the complete payload
    const filtersToSave = {
      ...defaultPayload,
      filters: buildFilters(),
      includeUsers: [],
    };

    try {
      enableLoader();

      // Check if there are matching candidates
      const hasMatchingCandidates = await new Promise<boolean>((resolve) => {
        Service.getConnections(
          (data: UserSearchResponse) => resolve(data.results.length > 1),
          () => resolve(false),
          filtersToSave
        );
      });

      if (!hasMatchingCandidates) {
        setError("No candidates match specified criteria.");
        return;
      }

      // Update payload with new filters
      setpayload({
        ...payload,
        applicantFilters: buildApplicantFilters(),
      });

      // Determine next step based on payment type and job type
      if (sessionInfo?.["paymentInfo"]?.paymentType === c.userPaymentType.FREE) {
        next("subscription");
      } else if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
        next("job-summary");
      } else {
        next("review-post");
      }

    } catch (error) {
      console.error("Error in handleConfirm:", error);
      setError("An error occurred while processing your request.");
    } finally {
      disableLoader();
    }
  };
  return {
    isDialogVisible,
    payload,
    setpayload,
    prev,
    next,
    setIsDialogVisible,
    distance,
    setDistance,
    ageRanges,
    setAgeRanges,
    defaultPayload,
    selectedCriteriaGroup3,
    setSelectedCriteriaGroup3,
    year12GraduationYear,
    setYear12GraduationYear,
    responseTime,
    setResponseTime,
    error,
    setError,
    name,
    setName,
    enableLoader,
    disableLoader,
    age,
    setAge,
    isMobile,
    experienceLevels,

    selectedExperienceLevels,
    setSelectedExperienceLevels,
    handleExperienceLevelChange,

    handleAgeChange,

    openDialog,
    closeDialog,
    canConfirm,
    handleConfirm,

    handleCriteriaGroup3Change,
  };
};

const CandidateMatchingWeb = () => {
  const {
    isDialogVisible,
    payload,
    setpayload,
    prev,
    next,
    setIsDialogVisible,
    distance,
    setDistance,
    ageRanges,
    setAgeRanges,
    defaultPayload,
    selectedCriteriaGroup3,
    setSelectedCriteriaGroup3,
    year12GraduationYear,
    setYear12GraduationYear,
    responseTime,
    setResponseTime,
    error,
    setError,
    name,
    setName,
    enableLoader,
    disableLoader,
    age,
    setAge,

    experienceLevels,

    selectedExperienceLevels,
    setSelectedExperienceLevels,
    handleExperienceLevelChange,

    handleAgeChange,

    openDialog,
    closeDialog,
    canConfirm,
    handleConfirm,

    handleCriteriaGroup3Change,
  } = useJobTypeHook();
  return (
    <div>
      <div
        className={styles.CandidateMatchingContainer}
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div className={styles.CandidateMatchingMainContent}>
          <main className={styles.CandidateMatchingSection}>
            <h2 className={styles.CandidateMatchingTitle}>Candidate Matching</h2>
            <p className={styles.CandidateMatchingDescription}>
              You have chosen to post this job using Juggle Assist, Juggle Street’s candidate-matching algorithm that generates an applicant
              shortlist. To help us find the best possible match, please provide your candidate's "Must Have" criteria.
            </p>
            <div className={styles.buttonContainer}>
              <button className={styles.CandidateMatchingButton} onClick={openDialog}>
                Continue
              </button>
              {/* <button
              className={styles.goBackButton}
              onClick={() =>
                prevClicked(
                  currentPayload,
                  [2, 4, 8, 12, 64, 128].includes(currentPayload.jobType) &&
                    currentPayload.jobSubType === 0
                    ? 2
                    : 3
                )
              }
            >
              <i
                className="pi pi-angle-left"
                style={{ marginRight: "8px" }}
              ></i>
              Go Back
            </button> */}
              <GoBack
                onClick={() => {
                  setpayload({
                    ...payload,
                  });
                  if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
                    prev("pricing-payments-step2");
                  } else {
                    prev("jobpricing-step3");
                  }
                }}
              />
            </div>
          </main>

          {/* <footer className={styles.CandidateMatchingFooter}>
                    <img
                        src={matchingImage}
                        alt="Matching Illustration"
                        className={styles.CandidateMatchingFooterImage}
                    />
                </footer> */}
          <CustomDialog visible={isDialogVisible} onHide={closeDialog} closeClicked={closeDialog} profileCompletion={0}>
            <div className={styles.dialogContent}>
              <h3 className={styles.dialogHead}>Candidate Criteria</h3>

              <Divider className={styles.dialogDivider} />
              <p className={styles.dialogInstruct}>
                Select the criteria candidates “must have” to apply for this job. You can view the detailed profile of all shortlisted applicants
                before deciding to interview.
              </p>

              <div className={styles.criteriaSection}>
                {payload.jobType !== 64 && payload.jobType !== 128 && (
                  <div className={styles.criteriaItem}>
                    <p className={styles.criteriaTag}>Distance</p>
                    <div className={styles.radioOptions}>
                      <div className={styles.radioOption}>
                        <input
                          type="radio"
                          id="distance2.5km"
                          name="distance"
                          value="2.5km"
                          onChange={() => setDistance(0)}
                          checked={distance === 0}
                          className={styles.radioInput}
                        />
                        <label htmlFor="distance2.5km" className={styles.radioLabel}>
                          Within 2.5km
                        </label>
                      </div>
                      <div className={styles.radioOption}>
                        <input
                          type="radio"
                          id="distance5km"
                          name="distance"
                          value="5km"
                          onChange={() => setDistance(1)}
                          checked={distance === 1}
                          className={styles.radioInput}
                        />
                        <label htmlFor="distance5km" className={styles.radioLabel}>
                          Within 5km
                        </label>
                      </div>
                      <div className={styles.radioOption}>
                        <input
                          type="radio"
                          id="distance10km"
                          name="distance"
                          value="10km"
                          onChange={() => setDistance(2)}
                          checked={distance === 2}
                          className={styles.radioInput}
                        />
                        <label htmlFor="distance10km" className={styles.radioLabel}>
                          Within 10km
                        </label>
                      </div>
                    </div>
                  </div>
                )}
                <Divider className={styles.dialogDivider} />
                <div className={styles.criteriaItem}>
                  <p className={styles.criteriaTag} style={{ marginTop: "13px" }}>
                    Helper Age
                  </p>
                  <div className={styles.checkboxGroup}>
                    {/* First group of checkboxes */}
                    <div className={styles.checkboxPair}>
                      <div className={styles.checkboxOption}>
                        <input
                          type="checkbox"
                          id="age16-17"
                          value="16-17"
                          onChange={() => handleAgeChange("16-17")}
                          checked={ageRanges.includes("16-17")}
                          className={styles.checkboxInput}
                        />
                        <label htmlFor="age16-17" className={styles.checkboxLabel}>
                          16-17
                        </label>
                      </div>
                      <div className={styles.checkboxOption}>
                        <input
                          type="checkbox"
                          id="age18-24"
                          value="18-24"
                          onChange={() => handleAgeChange("18-24")}
                          checked={ageRanges.includes("18-24")}
                          className={styles.checkboxInput}
                        />
                        <label htmlFor="age18-24" className={styles.checkboxLabel}>
                          18-24
                        </label>
                      </div>
                    </div>

                    <div className={styles.checkboxPairSec}>
                      <div className={styles.checkboxOption}>
                        <input
                          type="checkbox"
                          id="age25-44"
                          value="25-44"
                          onChange={() => handleAgeChange("25-44")}
                          checked={ageRanges.includes("25-44")}
                          className={styles.checkboxInput}
                        />
                        <label htmlFor="age25-44" className={styles.checkboxLabel}>
                          25-44
                        </label>
                      </div>
                      <div className={styles.checkboxOption}>
                        <input
                          type="checkbox"
                          id="age45+"
                          value="45+"
                          onChange={() => handleAgeChange("45+")}
                          checked={ageRanges.includes("45+")}
                          className={styles.checkboxInput}
                        />
                        <label htmlFor="age45+" className={styles.checkboxLabel}>
                          45+
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <Divider className={styles.dialogDivider} />
                {payload.jobType === 64 || payload.jobType === 128 ? (
                  <div className={styles.criteriaItem}>
                    <p className={styles.criteriaTag}>Experience</p>
                    <div className={styles.checkboxGroup}>
                      <div className={styles.checkboxPairThird}>
                        {experienceLevels.map((level) => (
                          <div key={level.value} className={styles.checkboxOption}>
                            <input
                              type="checkbox"
                              id={`experience-${level.label.toLowerCase()}`}
                              value={level.value}
                              onChange={() => handleExperienceLevelChange(level.value)}
                              checked={selectedExperienceLevels.includes(level.value)}
                              className={styles.checkboxInput}
                            />
                            <label htmlFor={`experience-${level.label.toLowerCase()}`} className={styles.checkboxLabel}>
                              {level.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className={styles.criteriaItem}>
                    <p className={styles.criteriaTag}>Other</p>
                    <div className={styles.checkboxGroup}>
                      <div className={styles.checkboxPairThird}>
                        <div className={styles.checkboxOption}>
                          <input
                            type="checkbox"
                            id="specialNeeds"
                            value="Special Needs"
                            onChange={() => handleCriteriaGroup3Change(4)}
                            checked={selectedCriteriaGroup3.includes(4)}
                            className={styles.checkboxInput}
                          />
                          <label htmlFor="specialNeeds" className={styles.checkboxLabel}>
                            Special Needs
                          </label>
                        </div>
                        <div className={styles.checkboxOption}>
                          <input
                            type="checkbox"
                            id="driverLicence"
                            value="Driver Licence"
                            onChange={() => handleCriteriaGroup3Change(9)}
                            checked={selectedCriteriaGroup3.includes(9)}
                            className={styles.checkboxInput}
                          />
                          <label htmlFor="driverLicence" className={styles.checkboxLabel}>
                            Driver Licence
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {error && <p className={styles.errorMessageCriteria}>{error}</p>}
                <Divider style={{ width: "100%" }} />
              </div>

              {/* Footer buttons */}
              <div className={styles.dialogFooter}>
                <button className={styles.goBackButton} onClick={closeDialog}>
                  <i className="pi pi-angle-left" style={{ marginRight: "8px" }}></i>
                  Go Back
                </button>

                <button className={styles.nextButton} onClick={handleConfirm} disabled={!canConfirm}>
                  Confirm
                </button>
              </div>
            </div>
          </CustomDialog>
        </div>
      </div>
    </div>
  );
};
const CandidateMatchingMobile = () => {
  const {
    isDialogVisible,
    payload,
    setpayload,
    prev,
    isMobile,
    next,
    setIsDialogVisible,
    distance,
    setDistance,
    ageRanges,
    setAgeRanges,
    defaultPayload,
    selectedCriteriaGroup3,
    setSelectedCriteriaGroup3,
    year12GraduationYear,
    setYear12GraduationYear,
    responseTime,
    setResponseTime,
    error,
    setError,
    name,
    setName,
    enableLoader,
    disableLoader,
    age,
    setAge,

    experienceLevels,

    selectedExperienceLevels,
    setSelectedExperienceLevels,
    handleExperienceLevelChange,

    handleAgeChange,

    openDialog,
    closeDialog,
    canConfirm,
    handleConfirm,

    handleCriteriaGroup3Change,
  } = useJobTypeHook();
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        position: "relative",
      }}
    >
      <div className={styles.CandidateMatchingContainerMobile}>
        <div className={styles.CandidateMatchingMainContentMobile}>
          <BackButtonPortal id="back-button-portal">
            <div
              onClick={() => {
                setpayload(payload);
                if ([2, 4, 8, 12, 64, 128].includes(payload.jobType)) {
                  prev("section4-mobile");
                } else {
                  prev("overtime-section-mobile");
                }
              }}
            >
              <img src={SideArrow} alt="cross" width={13} height={20} className="cursor-pointer" style={{ display: "block", objectFit: "contain" }} />
            </div>
          </BackButtonPortal>
          <div className={styles.dialogContentMobile}>
            <h3 style={{ marginBottom: "0px" }} className={styles.dialogHeadMobile}>
              Candidate Criteria
            </h3>
            <p style={{ marginTop: "0px" }} className={styles.dialogInstruct}>
              Select the criteria candidates “must have” to apply for this job. You can view the detailed profile of all shortlisted applicants before
              deciding to interview.
            </p>

            <div className={styles.criteriaSection}>
              {payload.jobType !== 64 && payload.jobType !== 128 && (
                <div className={styles.criteriaItem}>
                  <p style={{ marginTop: "0px" }} className={styles.criteriaTag}>
                    Distance
                  </p>
                  <div
                    className={styles.radioOptions}
                    style={{
                      display: "flex",
                      flexWrap: "wrap",
                      marginLeft: "0px",
                    }}
                  >
                    {["2.5km", "5km"].map((value, index) => (
                      <button
                        key={index}
                        className="flex align-items-center justify-content-center cursor-pointer"
                        style={{
                          padding: "12px 22px",
                          border: `2px solid ${distance === index ? "#179d52" : "#DFDFDF"}`,
                          borderRadius: "20px",
                          backgroundColor: distance === index ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                          color: distance === index ? "#179D52" : "#585858",
                          fontSize: "14px",
                          fontWeight: distance === index ? "700" : "500",
                          cursor: "pointer",
                        }}
                        onClick={() => setDistance(index)} // Set the selected distance index
                      >
                        {`Within ${value}`} {/* The text for each button */}
                      </button>
                    ))}
                    <div style={{ width: "100%" }}>
                      {" "}
                      {/* This div pushes the last button to the next line */}
                      <button
                        key={2} // Same index as the last item
                        className="flex align-items-center justify-content-center cursor-pointer"
                        style={{
                          padding: "12px 22px",
                          border: `2px solid ${distance === 2 ? "#179d52" : "#DFDFDF"}`,
                          borderRadius: "20px",
                          backgroundColor: distance === 2 ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                          color: distance === 2 ? "#179D52" : "#585858",
                          fontSize: "14px",
                          fontWeight: distance === 2 ? "700" : "500",
                          cursor: "pointer",
                        }}
                        onClick={() => setDistance(2)}
                      >
                        {`Within 10km`}
                      </button>
                    </div>
                  </div>
                </div>
              )}
              <Divider className={styles.dialogDivider} />
              <div className={styles.criteriaItem}>
                <p className={styles.criteriaTag} style={{ marginTop: "0px" }}>
                  Helper Age
                </p>
                <div className={styles.checkboxGroupMobile}>
                  {/* First group of buttons */}
                  <div className={styles.checkboxPairMobile}>
                    {["16-17", "18-24"].map((value, index) => (
                      <button
                        key={index}
                        className={`${ageRanges.includes(value) ? styles.selectedButton : styles.button
                          } flex align-items-center justify-content-center cursor-pointer`}
                        style={{
                          border: `2px solid ${ageRanges.includes(value) ? "#179d52" : "#DFDFDF"}`,
                          borderRadius: "20px",
                          backgroundColor: ageRanges.includes(value) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                          color: ageRanges.includes(value) ? "#179D52" : "#585858",
                          fontSize: "14px",
                          fontWeight: ageRanges.includes(value) ? "700" : "500",
                          cursor: "pointer",
                          width: "125px",
                          height: "41px",
                        }}
                        onClick={() => handleAgeChange(value)} // Toggle the selected age range
                      >
                        {value}
                      </button>
                    ))}
                  </div>

                  {/* Second group of buttons */}
                  <div className={styles.checkboxPairSecMobile}>
                    {["25-44", "45+"].map((value, index) => (
                      <button
                        key={index + 2} // Adding offset to differentiate keys
                        className={`${ageRanges.includes(value) ? styles.selectedButton : styles.button
                          } flex align-items-center justify-content-center cursor-pointer`}
                        style={{
                          border: `2px solid ${ageRanges.includes(value) ? "#179d52" : "#DFDFDF"}`,
                          borderRadius: "20px",
                          backgroundColor: ageRanges.includes(value) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                          color: ageRanges.includes(value) ? "#179D52" : "#585858",
                          fontSize: "14px",
                          fontWeight: ageRanges.includes(value) ? "700" : "500",
                          cursor: "pointer",
                          width: "125px",
                          height: "41px",
                        }}
                        onClick={() => handleAgeChange(value)} // Toggle the selected age range
                      >
                        {value}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <Divider className={styles.dialogDivider} />
              {payload.jobType === 64 || payload.jobType === 128 ? (
                <div className={styles.criteriaItem}>
                  <p style={{ marginTop: "0px" }} className={styles.criteriaTag}>
                    Experience
                  </p>
                  <div className={styles.checkboxGroupMobile}>
                    {/* First row of buttons */}
                    <div className={styles.checkboxPairFirstMobile}>
                      {experienceLevels.slice(0, 2).map((level) => (
                        <div key={level.value} className={styles.checkboxOptionMobile}>
                          <button
                            className={`${selectedExperienceLevels.includes(level.value) ? styles.selectedButtonMobile : styles.buttonMobile
                              } flex align-items-center justify-content-center cursor-pointer`}
                            style={{
                              padding: "12px 30px",
                              border: `2px solid ${selectedExperienceLevels.includes(level.value) ? "#179d52" : "#DFDFDF"}`,
                              borderRadius: "20px",
                              backgroundColor: selectedExperienceLevels.includes(level.value) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                              color: selectedExperienceLevels.includes(level.value) ? "#179D52" : "#585858",
                              fontSize: "14px",
                              fontWeight: selectedExperienceLevels.includes(level.value) ? "700" : "500",
                              cursor: "pointer",
                              width: "125px",
                            }}
                            onClick={() => handleExperienceLevelChange(level.value)} // Toggle the selected experience level
                          >
                            {level.label}
                          </button>
                        </div>
                      ))}
                    </div>

                    {/* Second row of buttons */}
                    <div className={styles.checkboxPairSecondMobile}>
                      {experienceLevels.slice(2).map((level) => (
                        <div key={level.value} className={styles.checkboxOptionMobile}>
                          <button
                            className={`${selectedExperienceLevels.includes(level.value) ? styles.selectedButtonMobile : styles.buttonMobile
                              } flex align-items-center justify-content-center cursor-pointer`}
                            style={{
                              padding: "12px 30px",
                              border: `2px solid ${selectedExperienceLevels.includes(level.value) ? "#179d52" : "#DFDFDF"}`,
                              borderRadius: "20px",
                              backgroundColor: selectedExperienceLevels.includes(level.value) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                              color: selectedExperienceLevels.includes(level.value) ? "#179D52" : "#585858",
                              fontSize: "14px",
                              fontWeight: selectedExperienceLevels.includes(level.value) ? "700" : "500",
                              cursor: "pointer",
                              width: "125px",
                            }}
                            onClick={() => handleExperienceLevelChange(level.value)} // Toggle the selected experience level
                          >
                            {level.label}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className={styles.criteriaItem}>
                  <p style={{ marginTop: "0px" }} className={styles.criteriaTag}>
                    Other
                  </p>
                  <div className={styles.checkboxGroup}>
                    <div className={styles.checkboxPairThird}>
                      <div className={styles.checkboxOption}>
                        <button
                          className={`${selectedCriteriaGroup3.includes(4) ? styles.selectedButton : styles.button
                            } flex align-items-center justify-content-center cursor-pointer`}
                          style={{
                            padding: "12px 30px",
                            border: `2px solid ${selectedCriteriaGroup3.includes(4) ? "#179d52" : "#DFDFDF"}`,
                            borderRadius: "20px",
                            backgroundColor: selectedCriteriaGroup3.includes(4) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                            color: selectedCriteriaGroup3.includes(4) ? "#179D52" : "#585858",
                            fontSize: "14px",
                            fontWeight: selectedCriteriaGroup3.includes(4) ? "700" : "500",
                            cursor: "pointer",
                          }}
                          onClick={() => handleCriteriaGroup3Change(4)} // Toggle the selected criteria
                        >
                          Special Needs
                        </button>
                      </div>
                      <div className={styles.checkboxOption}>
                        <button
                          className={`${selectedCriteriaGroup3.includes(9) ? styles.selectedButton : styles.button
                            } flex align-items-center justify-content-center cursor-pointer`}
                          style={{
                            padding: "12px 30px",
                            border: `2px solid ${selectedCriteriaGroup3.includes(9) ? "#179d52" : "#DFDFDF"}`,
                            borderRadius: "20px",
                            backgroundColor: selectedCriteriaGroup3.includes(9) ? "var(--Selected-button-input, #179D5233)" : "#FFFFFF",
                            color: selectedCriteriaGroup3.includes(9) ? "#179D52" : "#585858",
                            fontSize: "14px",
                            fontWeight: selectedCriteriaGroup3.includes(9) ? "700" : "500",
                            cursor: "pointer",
                          }}
                          onClick={() => handleCriteriaGroup3Change(9)} // Toggle the selected criteria
                        >
                          Driver Licence
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {error && <p className={styles.errorMessageCriteria}>{error}</p>}
            </div>
          </div>
        </div>
        <CustomFooterButton label="Next" isDisabled={!canConfirm} onClick={handleConfirm} />
      </div>
    </div>
  );
};
