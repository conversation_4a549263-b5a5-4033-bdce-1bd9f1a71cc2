// CancelJobPopup.tsx
import React, { useEffect, useRef, useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import styles from '../../styles/cancel-job-popup.module.css';
import BackArrow from '../../../../assets/images/Icons/back-icon.png';
import useIsMobile from '../../../../hooks/useIsMobile';
interface CancelJobPopupProps {
    visible: boolean;
    heading: string;
    cancelText: string;
    confirmText: string;
    confirmIcon: React.ReactNode;
    helperImage?: string; // New optional parameter
    message: string;
    performAction: (action: 1 | 2) => void;
}

function useCancelJobPopup() {
    const [visible, setVisible] = useState<boolean>(false);
    const [heading, setHeading] = useState<string>('');
    const [message, setMessage] = useState<string>('');
    const [cancelText, setCancelText] = useState<string>('');
    const [confirmText, setConfirmText] = useState<string>('');
    const [helperImage, setHelperImage] = useState<string>(''); // New state for helper image
    const [confirmIcon, setConfirmIcon] = useState<React.ReactNode | null>(null);
    const [onConfirm, setOnConfirm] = useState<(() => void) | null>(null);
    const [onCancel, setOnCancel] = useState<(() => void) | null>(null);

    function showCancelJobPopup(
        heading: string,
        message: string,
        cancelText: string,
        confirmText: string,
        confirmIcon: React.ReactNode,
        onConfirm?: () => void,
        onCancel?: () => void,
        helperImage?: string
    ) {
        setHeading(heading);
        setMessage(message);
        setCancelText(cancelText);
        setConfirmText(confirmText);
        setConfirmIcon(confirmIcon);
        setHelperImage(helperImage || ''); // Set helper image if provided
        setOnConfirm(() => onConfirm || null);
        setOnCancel(() => onCancel || null);
        setVisible(true);
    }

    function performAction(action: 1 | 2) {
        setVisible(false);
        if (action === 1 && onConfirm) {
            onConfirm();
        } else if (action === 2 && onCancel) {
            onCancel();
        }
        setHeading('');
        setMessage('');
        setCancelText('');
        setConfirmText('');
        setConfirmIcon(null);
        setHelperImage(''); // Reset helper image
        setOnConfirm(null);
        setOnCancel(null);
    }

    return {
        cancelJobPopupProps: {
            visible,
            heading,
            cancelText,
            confirmText,
            confirmIcon,
            message,
            helperImage, // Include helper image in props
            performAction,
        },
        showCancelJobPopup,
    };
}

function CancelJobPopup({ cancelJobPopupProps }: { cancelJobPopupProps: CancelJobPopupProps }) {
    const { isMobile } = useIsMobile();
    useEffect(() => {
        
    }, []);
    return !isMobile ? (
        <Dialog
            visible={cancelJobPopupProps.visible}
            onHide={() => {}}
            content={
                <div className={styles.popupContainer}>
                    <div className={styles.popupHeader}>
                        <h1 className={styles.popupHeading}>{cancelJobPopupProps.heading}</h1>
                    </div>
                    <Divider />
                    <p className={styles.popupMessage}>{cancelJobPopupProps.message}</p>
                    <div className={styles.popupActions}>
                        <p
                            className={styles.cancelButton}
                            onClick={() => cancelJobPopupProps.performAction(2)}
                        >
                            {cancelJobPopupProps.cancelText}
                        </p>
                        <div
                            className={styles.confirmButton}
                            onClick={() => cancelJobPopupProps.performAction(1)}
                        >
                            {cancelJobPopupProps.confirmIcon}
                            <p>{cancelJobPopupProps.confirmText}</p>
                        </div>
                    </div>
                </div>
            }
        />
    ) : (
        <Dialog
            visible={cancelJobPopupProps.visible}
            onHide={() => {}}
            position='bottom'
            style={{
                margin: 0,
                padding: 0,
                width: '100vw',
                maxWidth: '100%',
                transform: 'none',
                transition: 'none',
            }}
            content={
                <div className={styles.popupContainerMobile}>
                    <div
                        style={{
                            justifyContent: 'space-between',
                            width: '100%',
                            paddingInline: '15px',
                        }}
                        className='flex '
                    >
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                gap: '8px',
                                fontWeight: '500',
                                fontSize: '12px',
                                alignItems: 'center',
                                paddingBlock: '10px',
                            }}
                            onClick={() => cancelJobPopupProps.performAction(2)}
                        >
                            <div
                                style={{
                                    backgroundColor: '#D9D9D94D',
                                    height: '32px',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: '50%',
                                    width: '32px',
                                }}
                            >
                                <img
                                    src={BackArrow}
                                    width={'10px'}
                                    height={'9.5px'}
                                    alt='backArrow'
                                />
                            </div>
                            Go back
                        </div>
                        {cancelJobPopupProps.helperImage ? (
                            <div
                                style={{
                                    display: 'flex',
                                    position: 'relative',
                                    bottom: '45px',
                                    left: '10px',
                                }}
                            >
                                <img
                                    src={cancelJobPopupProps.helperImage}
                                    width={'79px'}
                                    height={'75px'}
                                    alt='helperimg'
                                    style={{ borderRadius: '50%', border: '2px solid #FF6359' }}
                                />
                            </div>
                        ) : (
                            ''
                        )}
                    </div>
                    <div className={styles.popupHeader}>
                        <h1 className={styles.popupHeadingMobile}>{cancelJobPopupProps.heading}</h1>
                    </div>
                    <Divider className='mt-0' />
                    <p className={styles.popupMessageMobile}>{cancelJobPopupProps.message}</p>

                    <div
                        className={styles.confirmButtonMobile}
                        onClick={() => cancelJobPopupProps.performAction(1)}
                    >
                        {cancelJobPopupProps.confirmIcon}
                        <p>{cancelJobPopupProps.confirmText}</p>
                    </div>
                </div>
            }
        />
    );
}

export { CancelJobPopup, useCancelJobPopup };
