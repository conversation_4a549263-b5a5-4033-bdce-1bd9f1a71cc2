import { PropsWithChildren, useState } from "react";
import { fetchNoRefreshSessionInfo } from "../../store/tunks/sessionInfoTunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import Loader from "../../commonComponents/Loader";
import { updateInIFrame } from "../../store/slices/applicationSlice";

const IFrameInitializer = ({ children }: PropsWithChildren) => {
  const [allowed, setAllowed] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const check = () => {
    const params = new URLSearchParams(window.location.hash.split("?")[1]);
    if (params.has("admin_mode")) {
      return true;
    }
    if (params.has("accessToken") || params.has("refreshToken") || params.has("clientType")) {
      setupForIframe(params);
      return false;
    }
    return true;
  };

  const setupForIframe = async (params: URLSearchParams) => {
    const expires = new Date();
    expires.setFullYear(new Date().getFullYear() + 1);
    utils.setCookie(CookiesConstant.accessToken, params.get("accessToken"), expires);
    utils.setCookie(CookiesConstant.refreshToken, params.get("refreshToken"), expires);
    utils.setCookie(CookiesConstant.clientType, params.get("clientType"), expires);
    await dispatch(fetchNoRefreshSessionInfo());
    await dispatch(updateInIFrame(true));
    setAllowed(true);
  };

  return allowed || check() ? children : <Loader inIframe />;
};

export default IFrameInitializer;
