import { useEffect, useRef, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import Auth from "../../../../services/authService";
import Service from "../../../../services/services";
import { updateUser } from "../../../../store/tunks/sessionInfoTunk";
import { PayloadTemplate, useJobManager } from "../provider/JobManagerProvider";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import useLoader from "../../../../hooks/LoaderHook";
import styles from "../../../Common/styles/post-job-step-1.module.css";
import CustomDialog from "../../CustomDialog";
import customDialogstyles from "../../../Parent/styles/MyAddresses.module.css";
import { Divider } from "primereact/divider";
import { Dropdown } from "primereact/dropdown";
import { AutoComplete } from "primereact/autocomplete";
import { InputText } from "primereact/inputtext";

function JobEditStep1() {
    const { isMobile } = useIsMobile()
    return isMobile ? <JobEditStep1Mobile /> : <JobEditStep1Web />;
}

export default JobEditStep1;

interface Address {
    id: string;
    addressLabel: string;
    formattedAddress: string;
    longitude: number;
    latitude: number;
}

interface FormData {
    addressId: string | null;

    selectedAddressType: "existing" | "alternative" | null;
}

const useJobTypeHook = () => {
    const { payload, next, prev, setpayload } = useJobManager()
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const { isMobile } = useIsMobile();
    const [dialogVisible, setDialogVisible] = useState(false);
    const clientType = utils.getCookie(CookiesConstant.clientType);
    const [isEditMode] = useState(false);
    const [addressLine1, setAddressLine1] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["addressLine1"] || ""
    );
    const [addressLine2, setAddressLine2] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["addressLine2"] || ""
    );
    const [suburb, setSuburb] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["suburb"] || ""
    );
    const [postCode, setPostCode] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["suburb"] || ""
    );
    const [country, setCountry] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["country"] || "au"
    );
    const [state, setState] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["state"] || ""
    );
    const [nickname, setNickname] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["addressLabel"] || ""
    );
    const [defaultAddress] = useState(
        sessionInfo.loading ? [] : sessionInfo.data["defaultAddress"] || null
    );
    const [selectedAddressId, setSelectedAddressId] = useState(null); // Store the ID of the address being edited
    const [nzAddress, setNzAddress] = useState(""); // Holds the input value
    const [nzAuto, setNzAuto] = useState(true);
    const [addressSuggestions, setAddressSuggestions] = useState([]);
    const { disableLoader, enableLoader } = useLoader();
    const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<any[]>(
        []
    );
    const [successMessage, setSuccessMessage] = useState("");
    const [dropdownOpen, setDropdownOpen] = useState(false); // Manage dropdown state
    const [dropdownPosition, setDropdownPosition] = useState<"below" | "above">(
        "below"
    );
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const buttonRef = useRef<HTMLButtonElement | null>(null);

    useEffect(() => {
        if (dropdownOpen && buttonRef.current && dropdownRef.current) {
            const buttonRect = buttonRef.current.getBoundingClientRect();
            const dropdownHeight = dropdownRef.current.offsetHeight;

            const spaceBelow = window.innerHeight - buttonRect.bottom;
            const spaceAbove = buttonRect.top;

            // Decide dropdown position based on available space
            if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
                setDropdownPosition("above");
            } else {
                setDropdownPosition("below");
            }
        }
    }, [dropdownOpen]);

    const toggleDropdown = () => {
        setDropdownOpen(!dropdownOpen);
    };
    const handleDialogClose = () => {
        setDialogVisible(false);
        setAddressLine1("");
        setAddressLine2("");
        setCountry("au");
        setCountry(sessionInfo.data["country"] || "au");
        setNickname("");
        setPostCode(null);
        setState("");
        setSuburb("");
        setSelectedAddressId(null);
        setNzAuto(true);
        setNzAddress("");
    };
    const addresses: Address[] = sessionInfo.loading
        ? []
        : sessionInfo.data["addresses"] || [];

    const [formData, setFormData] = useState<FormData>({
        addressId: null,
        selectedAddressType: null,
        ...(() => {
            const rawData = (payload?.metadata || []).find(
                (val) => val.key === "step1formData"
            )?.data;
            try {
                return rawData ? JSON.parse(rawData) : {};
            } catch (error) {
                return {};
            }
        })(),
    });

    useEffect(() => {
        if (addresses.length > 0 && !formData.addressId) {
            const initialAddress = addresses[0];
            if (initialAddress && initialAddress.latitude !== undefined) {
                setFormData((prev) => ({
                    ...prev,
                    addressId: initialAddress.id,
                    selectedAddressType: "existing",
                }));
            }
        }
    }, [addresses]);

    useEffect(() => {
        if (defaultAddress && defaultAddress.id && !formData.addressId) {
            setFormData((prevFormData) => ({
                ...prevFormData,
                addressId: defaultAddress.id,
                selectedAddressType: "existing",
            }));
        }
    }, [defaultAddress]);

    const isFormValid = (): boolean => {
        // Check if selectedJob and addressId are present
        if (!formData.addressId) return false;

        // Check if address is valid based on selected address type
        const isAddressValid =
            formData.selectedAddressType === "alternative"
                ? false // Disable if alternative address type is selected
                : !!formData.addressId;

        return isAddressValid;
    };

    const searchAddress = (event: { query: string }) => {
        Auth.suburbSearch(
            event.query,
            (res) => {
                const suggestions = res.map((item) => ({
                    label: `${item.name}, ${(item.state as string).toUpperCase()}`,
                    value: item,
                }));
                setAutoCompleteSuggestions(suggestions);
            },
            () => { }
        );
    };
    const handleAddressChange = (type, addressId) => {
        if (type === "alternative") {
            setDialogVisible(true); // Open the dialog for alternative address
            // Update formData with selected address type
            setFormData({
                ...formData,
                selectedAddressType: type,
            });
        } else {
            setDialogVisible(false); // Close the dialog if not alternative
            setFormData({
                ...formData,
                selectedAddressType: type,
                addressId: addressId || null,
            });
        }
    };

    const handleNext = () => {
        if (!isFormValid()) {
            alert("Please fill in all required fields");
            return;
        }
        const address = addresses.find((addr) => addr.id === formData.addressId);
        const latitude = address ? address.latitude : null;
        const longitude = address ? address.longitude : null;

        setpayload({
            ...payload,
            addressId: Number(formData.addressId),
            latitude: latitude,
            longitude: longitude,
            applicantFilters:
                payload.applicantFilters === null
                    ? [
                        {
                            field: "address",
                            operator: "eq",
                            value: [latitude, longitude],
                        },
                    ]
                    : [
                        ...payload.applicantFilters.filter(
                            (af) => af.field !== "address"
                        ),
                        {
                            field: "address",
                            operator: "eq",
                            value: [latitude, longitude],
                        },
                    ],
        });
        if ([2, 4, 5, 12, 64, 128].includes(payload.jobType)) {
            next("job-details")
        } else {
            next("day-and-schedule");

        }
    };

    const onSuggestionsFetchRequested = async (value) => {
        if (value.length < 6) {
            setAddressSuggestions([]);
            return;
        }

        try {
            Service.addressSearch(
                value,
                (res) => {
                    setAddressSuggestions(res);
                },
                (err) => { }
            );
        } catch (error) { }
    };

    const isFormComplete = () => {
        return addressLine1 && suburb && postCode && country && state;
    };
    const selectAddress = (address) => {
        enableLoader();

        Service.geocodeAddress(
            address,
            (response) => {
                disableLoader();
                // Autofill address fields with API response
                setAddressLine1(
                    `${response.streetNumber ? response.streetNumber : ""}${response.streetName ? " " + response.streetName : ""
                    }`
                );
                setPostCode(response.postCode);
                setSuburb(response.suburb);
                setState(response.state);
                setNzAuto(false);
            },
            (error) => {
                disableLoader();
            }
        );
    };
    const handleAddressNext = () => {
        if (isFormComplete()) {
            const updatedAddresses = sessionInfo.data["addresses"].map((address) => {
                if (address.id === selectedAddressId) {
                    // If this is the address being edited, update its fields
                    return {
                        ...address,
                        addressLabel: nickname,
                        addressLine1: addressLine1,
                        addressLine2: addressLine2,
                        country: country,
                        postCode: postCode,
                        state: state,
                        suburb: suburb,
                    };
                }
                return address; // Leave other addresses unchanged
            });

            const payload = {
                ...(sessionInfo.data as object), // Retain existing session info data
                addresses:
                    selectedAddressId !== null
                        ? updatedAddresses // If editing, use the updated addresses
                        : [
                            ...sessionInfo.data["addresses"],
                            {
                                addressLabel: nickname,
                                addressLine1: addressLine1,
                                addressLine2: addressLine2,
                                autoCompleteSuggestions: [],
                                autoCompleteValue: "",
                                country: country,
                                isPrimaryAddress: false,
                                postCode: postCode,
                                showDetailedAddress: false,
                                state: state,
                                suburb: suburb,
                            },
                        ], // If adding a new address, append it to the list
            };

            enableLoader();
            dispatch(updateUser({ payload })).finally(() => {
                setSuccessMessage(
                    selectedAddressId !== null
                        ? "Changes successfully made!"
                        : "Changes successfully made!"
                );
                // Reset form fields
                setAddressLine1("");
                setAddressLine2("");
                setCountry("au");
                setNickname("");
                setPostCode(null);
                setState("");
                setSuburb("");
                setSelectedAddressId(null);
                disableLoader();
                setDialogVisible(false);
                setNzAuto(true);
                setNzAddress("");
            });
        }
    };
    return {
        payload,
        next,
        prev,
        setpayload,
        isFormComplete,
        selectAddress,
        handleAddressNext,
        isFormValid,searchAddress,
        handleAddressChange,
        handleNext,
        onSuggestionsFetchRequested,
        addresses,formData, 
        setFormData,
        toggleDropdown,
        handleDialogClose,
        suburb, 
        setSuburb,
        addressLine2, 
        setAddressLine2,
        addressLine1,
         setAddressLine1,
         isEditMode,
         clientType,
         dialogVisible,
         setDialogVisible,
         sessionInfo,
         autoCompleteSuggestions, setAutoCompleteSuggestions,
         disableLoader, enableLoader,addressSuggestions, setAddressSuggestions,
         nzAuto, setNzAuto,nzAddress, setNzAddress,
         dropdownPosition, setDropdownPosition,
         successMessage, setSuccessMessage,defaultAddress,
         country, setCountry,nickname, setNickname,state, setState,
         postCode, setPostCode,selectedAddressId, setSelectedAddressId
    }
}

const JobEditStep1Web = () => {
    const { 
        payload,
        next,
        prev,
        setpayload,
        isFormComplete,
        selectAddress,
        handleAddressNext,
        isFormValid,searchAddress,
        handleAddressChange,
        handleNext,
        onSuggestionsFetchRequested,
        addresses,formData, 
        setFormData,
        toggleDropdown,
        handleDialogClose,
        suburb, 
        setSuburb,
        addressLine2, 
        setAddressLine2,
        addressLine1,
         setAddressLine1,
         isEditMode,
         clientType,
         dialogVisible,
         setDialogVisible,
         sessionInfo,
         autoCompleteSuggestions, setAutoCompleteSuggestions,
         disableLoader, enableLoader,addressSuggestions, setAddressSuggestions,
         nzAuto, setNzAuto,nzAddress, setNzAddress,
         dropdownPosition, setDropdownPosition,
         successMessage, setSuccessMessage,defaultAddress,
         country, setCountry,nickname, setNickname,state, setState,
         postCode, setPostCode,selectedAddressId, setSelectedAddressId

    } = useJobTypeHook()
    return  <div className={styles.container}>
          <div className={styles.jobPosting}>
            <div className={styles.jobAddress}>
              <h2 style={{ fontSize: "30px" }}>Job Address</h2>
              <div className={styles.addressOptions}>
                {addresses.map((address) => (
                  <label
                    key={address.id}
                    className={`${styles.addressOption} ${
                      formData.selectedAddressType === "existing" &&
                      formData.addressId === address.id
                        ? styles.selected
                        : ""
                    } flex align-items-center p-1 cursor-pointer`}
                    onClick={() => handleAddressChange("existing", address.id)}
                  >
                    <input
                      type="radio"
                      name="jobAddress"
                      value={address.id}
                      checked={
                        address.id ===
                          (defaultAddress ? defaultAddress["id"] : -1) ||
                        address.id === formData.addressId
                      }
                      className="cursor-pointer"
                      onChange={() => handleAddressChange("existing", address.id)}
                    />
                    <span className="pl-1">
                      {address.addressLabel} - {address.formattedAddress}
                    </span>
                  </label>
                ))}
                {clientType === "1" && (
                  <label
                    className={`${styles.addressOption} ${
                      formData.selectedAddressType === "alternative"
                        ? styles.selected
                        : ""
                    } flex align-items-center p-1 cursor-pointer`}
                    onClick={() => handleAddressChange("alternative", null)}
                  >
                    <input
                      type="radio"
                      name="jobAddress"
                      value="alternative"
                      checked={formData.selectedAddressType === "alternative"}
                      onChange={() => handleAddressChange("alternative", null)}
                      className="cursor-pointer"
                    />
                    <span>Alternative Address</span>
                  </label>
                )}
              </div>
            </div>
    
            {/* CustomDialog that opens for Alternative Address selection */}
            <CustomDialog
              visible={dialogVisible}
              onHide={handleDialogClose}
              closeClicked={handleDialogClose}
              profileCompletion={0}
            >
              <div className={customDialogstyles.addAddressContainer}>
                <header>
                  <h2 className={customDialogstyles.addAddressHeader}>
                    {isEditMode ? "Edit Address" : "Add Address"}
                  </h2>
                  <Divider className={customDialogstyles.addaddressDivider} />
                </header>
    
                <div className={customDialogstyles.flexContainer}>
                  <div className={customDialogstyles.addAddressDiv}>
                    {!isEditMode ? (
                      <div className={customDialogstyles.dropdownContainerCountry}>
                        <p className={customDialogstyles.addressHead}>Country</p>
                        <Dropdown
                          id="Country"
                          name="Country"
                          options={[
                            { label: "Australia", value: "au" },
                            { label: "New Zealand", value: "nz" },
                          ]}
                          placeholder="Select Country"
                          value={country}
                          onChange={(e) => {
                            setCountry(e.target.value);
                            setAddressLine1("");
                            setAddressLine2("");
                            setNickname("");
                            setPostCode(null);
                            setState("");
                            setSuburb("");
                            setSelectedAddressId(null);
                          }} // Update country
                          className="input-placeholder"
                        />
                      </div>
                    ) : (
                      <div className={customDialogstyles.dropdownContainerCountry}>
                        <p className={customDialogstyles.addressHead}>Country</p>
                        <Dropdown
                          disabled
                          id="Country"
                          name="Country"
                          options={[
                            { label: "Australia", value: "au" },
                            { label: "New Zealand", value: "nz" },
                          ]}
                          placeholder="Select Country"
                          value={country}
                          onChange={(e) => {
                            setCountry(e.target.value);
                            setAddressLine1("");
                            setAddressLine2("");
                            setNickname("");
                            setPostCode(null);
                            setState("");
                            setSuburb("");
                            setSelectedAddressId(null);
                          }} // Update country
                          className="input-placeholder"
                        />
                      </div>
                    )}
    
                    {country === "nz" && nzAuto ? (
                      <div className={customDialogstyles.nzAddressContainer}>
                        <p className={customDialogstyles.addressHead}>Address</p>
    
                        <div
                          className="input-container"
                          style={{ marginTop: "11px", width: "100%" }}
                        >
                          <AutoComplete
                            value={nzAddress} // Bind input value to the state
                            suggestions={addressSuggestions} // Pass suggestions to AutoComplete
                            completeMethod={(e) =>
                              onSuggestionsFetchRequested(e.query)
                            }
                            className={customDialogstyles.nzAddressDropdown}
                            placeholder="Start typing your address"
                            onChange={(e) => setNzAddress(e.value)} // Update state with input value
                            onSelect={(e) => selectAddress(e.value)}
                            style={{ width: "100%" }}
                          />
                        </div>
    
                        <button
                          className={customDialogstyles.switchToBtn}
                          onClick={() => {
                            setNzAuto(false); // Set state to render Australian fields
                          }}
                        >
                          Type Address Manually
                        </button>
                      </div>
                    ) : (
                      <>
                        <div className={customDialogstyles.addAddressDiv}>
                          <p className={customDialogstyles.addressHead}>Address</p>
                          <div
                            className="input-container"
                            style={{ marginTop: "18px", width: "100%" }}
                          >
                            <InputText
                              id="addressLine1"
                              name="addressLine1"
                              placeholder=""
                              style={{ width: "100%" }}
                              className="input-placeholder"
                              value={addressLine1}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const filteredValue = inputValue.trimStart();
                                setAddressLine1(filteredValue);
                              }}
                            />
                            <label
                              htmlFor="addressLine1"
                              className={`label-name ${
                                addressLine1 ? "label-float" : ""
                              }`}
                            >
                              Address Line 1
                            </label>
                          </div>
                          <div
                            className="input-container"
                            style={{ marginTop: "25px", width: "100%" }}
                          >
                            <InputText
                              id="addressLine2"
                              name="addressLine2"
                              style={{ width: "100%" }}
                              placeholder=""
                              className="input-placeholder"
                              value={addressLine2}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const filteredValue = inputValue.trimStart();
                                setAddressLine2(filteredValue);
                              }}
                            />
                            <label
                              htmlFor="addressLine2"
                              className={`label-name ${
                                addressLine2 ? "label-float" : ""
                              }`}
                            >
                              Address Line 2
                            </label>
                          </div>
                        </div>
    
                        <div
                          className={`${customDialogstyles.addAddressSecond} flex flex-column md:flex-row`}
                        >
                          <div
                            className="input-container"
                            style={{
                              marginTop: "18px",
                              maxWidth: "259px",
                              height: "56px",
                            }}
                          >
                            <p className={customDialogstyles.addressHead}>Suburb</p>
                            <InputText
                              id="Suburb"
                              name="Suburb"
                              placeholder=""
                              className="input-placeholder"
                              value={suburb}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const filteredValue = inputValue.trimStart();
                                setSuburb(filteredValue);
                              }}
                            />
                          </div>
                          {country !== "nz" ? (
                            <div className={customDialogstyles.dropdownContainer}>
                              <p className={customDialogstyles.addressHead}>
                                State
                              </p>
    
                              <Dropdown
                                id="State"
                                name="State"
                                options={[
                                  { label: "NSW", value: "nsw" },
                                  { label: "ACT", value: "act" },
                                  { label: "VIC", value: "vic" },
                                  { label: "SA", value: "sa" },
                                  { label: "QLD", value: "qld" },
                                  { label: "WA", value: "wa" },
                                ]}
                                placeholder=""
                                className="input-placeholder"
                                value={state}
                                onChange={(e) => setState(e.target.value)} // Update state
                              />
                            </div>
                          ) : (
                            <div className={customDialogstyles.dropdownContainer}>
                              <p className={customDialogstyles.addressHead}>City</p>
                              <InputText
                                id="city"
                                name="city"
                                placeholder=""
                                className="input-placeholder"
                                value={state}
                                onChange={(e) =>
                                  setState(e.target.value.replace(/\s+/g, ""))
                                } // Remove spaces before updating state
                              />
                            </div>
                          )}
    
                          <div
                            className="input-container"
                            style={{
                              marginTop: "18px",
                              maxWidth: "134px",
                              height: "56px",
                            }}
                          >
                            <p className={customDialogstyles.addressHead}>
                              Post code
                            </p>
                            <InputText
                              id="postCode"
                              name="postCode"
                              placeholder=""
                              className="input-placeholder"
                              value={postCode === null ? "" : postCode}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const filteredValue = inputValue.replace(/\D/g, ""); // Allow only digits, remove non-numeric characters
                                setPostCode(filteredValue); // Update postCode with the filtered value
                              }}
                            />
                          </div>
                        </div>
    
                        <div className={customDialogstyles.addAddressDiv}>
                          <div
                            className={`input-container ${customDialogstyles.nicknameCont}`}
                            style={{ marginTop: "18px", width: "100%" }}
                          >
                            <p className={customDialogstyles.addressHead}>
                              Nickname
                            </p>
                            <InputText
                              style={{ width: "100%" }}
                              id="Nickname"
                              name="Nickname"
                              placeholder="For example, Home, Melb Work, Holiday House"
                              className="input-placeholder"
                              value={nickname}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const filteredValue = inputValue.trimStart();
                                setNickname(filteredValue);
                              }}
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
    
                {/* <Divider className={customDialogstyles.addressDividerSecond} /> */}
                <footer className={customDialogstyles.dialogFooter}>
                  <br />
                  <button
                    onClick={handleAddressNext} // Submit the form
                    className={customDialogstyles.saveAddress}
                    disabled={!isFormComplete()}
                    // onClick={handleDialogClose}
                  >
                    Save
                  </button>
                </footer>
              </div>
            </CustomDialog>
    
            <div
              className="flex flex-column align-items-center mt-auto pb-5"
              style={{
                width: "52%",
              }}
            >
              <Divider />
              <button
                className={styles.nextButton}
                disabled={!isFormValid()}
                onClick={handleNext}
              >
                Next
              </button>
            </div>
          </div>
        </div>;
}
const JobEditStep1Mobile = () => {
    const { } = useJobTypeHook()
    return <div>isMobile</div>;
}