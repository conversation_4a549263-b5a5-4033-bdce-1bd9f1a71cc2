import React, { KeyboardEvent, useEffect, useRef, useState } from "react";
import styles from "./styles/identity-check.module.css";
import { ProgressBar } from "primereact/progressbar";
import "primeflex/primeflex.css";
import { InputText } from "primereact/inputtext";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import "../../components/utils/util.css";
import {
  incrementProfileActivationStep,
  setCurrentProfileActivationStep,
  updateProfileActivationEnabled,
} from "../../store/slices/applicationSlice";
import { fetchNoRefreshSessionInfo } from "../../store/tunks/sessionInfoTunk";
import { validatePhoneNumber } from "../../components/utils/validation";
import Auth from "../../services/authService";
import CustomButton from "../../commonComponents/CustomButton";
import useLoader from "../../hooks/LoaderHook";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import useIsMobile from "../../hooks/useIsMobile";
import HorizontalNavigation from "./HorizontalNavigationMobile";

interface InputOtpProps {
  length?: number;
  onComplete?: (otp: string | null, disable: boolean) => void;
}
const InputOtp: React.FC<InputOtpProps> = ({ length = 6, onComplete }) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (index: number, value: string) => {
    onComplete(null, true);
    if (isNaN(Number(value))) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value !== "" && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    if (newOtp.every((digit) => digit !== "") && onComplete) {
      onComplete(newOtp.join(""), false);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && otp[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div className="flex align-items-center gap-2">
      {otp.map((digit, index) => (
        <InputText
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          value={digit}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          className={styles.otpinput}
          maxLength={1}
        />
      ))}
    </div>
  );
};

const IdentityCheck = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [accountIdentifier, setAccountIdentifier] = useState("");
  const [accountIdentifierError, setAccountIdentifierError] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [disable, setDisable] = useState(true);
  const [isOtpSubmitted, setIsOtpSubmitted] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const { disableLoader, enableLoader } = useLoader();
  const [resendDisabled, setResendDisabled] = useState(true);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [disablePrevious, setDisablePrevious] = useState(false);
  const [_, setErrorMessage] = useState("");
  const dispatch = useDispatch<AppDispatch>();
  const {isMobile}=useIsMobile();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
    if (isOtpSubmitted) {
      // enableLoader()
      dispatch(fetchNoRefreshSessionInfo());
      dispatch(setCurrentProfileActivationStep(1));
    }
  };
  const handleNextStep = () => {
    const value = accountIdentifier.trim(); // Ensure there are no leading/trailing spaces

    if (!value) {
      setAccountIdentifierError("Please enter your mobile number."); // If input is empty
    } else if (!validatePhoneNumber(value)) {
      setAccountIdentifierError("Enter a valid mobile number"); // Invalid number format
    } else {
      setAccountIdentifierError(""); // Clear error
      enableLoader();
      Auth.getVerificationCode(
        value,
        () => {
          disableLoader();
          setCurrentStep(2);
        },
        () => {
          disableLoader();
          setAccountIdentifierError("Failed to send OTP. Please try again.");
        }
      );
    }
  };

  const handlePreviousStep = () => {
    setCurrentStep((prevStep) => Math.max(prevStep - 1, 1));
  };

  const handleSubmit = () => {
    setDisablePrevious(true);
    if (otpValue.length === 6) {
      enableLoader();
      Auth.submitVerificationCode(
        otpValue,
        () => {
          disableLoader();
          setSuccessMessage("Mobile number successfully verified");
          setIsOtpSubmitted(true);
        },
        () => {
          disableLoader();
          setErrorMessage("Invalid verification code. Please try again.");
        }
      );
    } else {
      setErrorMessage("Please enter a valid 6-digit OTP.");
    }
  };
  const handleResendCode = () => {
    handleNextStep();
  };

  const handleOtpComplete = (param, state) => {
    setDisable(state);

    setOtpValue(param);
  };

  return (
    <div  className={!isMobile ? `${styles.profileActivationContainer}` : `${styles.profileActivationContainerMobile}`}>
      <header className={styles.profileActivationHeader}>
        {!isMobile ? ( <h1 className={styles.title}>Identity Check</h1>
        )
        
        : (
          <HorizontalNavigation 
           title="Identity Check"
           onBackClick={() => {
            if (currentStep === 2) {
              // If on step 2, go back to step 1
              setCurrentStep(1);
            } else {
              // Otherwise, disable profile activation
              dispatch(updateProfileActivationEnabled(false));
            }
          }}
          />
        )}
        <ProgressBar
          value={
            sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]
          }
          className={styles.idprogressbar}
        />
        <p
          style={{
            fontFamily: "Poppins",
            fontWeight: 500,
            fontSize: "14px",
            color: "#585858",
          }}
        >
          Your profile is{" "}
          <span
            style={{
              color: "#179D52",
              fontSize: "20px",
              fontWeight: "700",
            }}
          >
            {" "}
            {sessionInfo.loading ? 70 : sessionInfo.data["profileCompleteness"]}
            % complete.
          </span>
        </p>
      </header>

      <main style={{gap:isMobile && "10px",padding:isMobile && "0px"}}  className={styles.profileActivationContent}>
        {currentStep === 1 && (
          <>
            <div className={styles.contentLeft}>
              <h2 className={styles.headerTitle}>Mobile Phone Verification</h2>
              <p
                className={styles.instructionNumber}
                style={{
                  fontWeight: "400",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                At Juggle Street, trust and security are paramount, that’s why
                we need to verify each person. Your mobile number is used to
                verify your identity.
              </p>
            </div>
            <div className={styles.contentRight}>
              <p className={styles.steptext}>Part {currentStep} of 2</p>
              <div
                className="input-container"
                style={{ marginTop: "35px", maxWidth: "100%" }}
              >
                <InputText
                  id="username"
                  name="username"
                  value={accountIdentifier}
                  onFocus={() => {
                    setIsFocused(true);
                    setAccountIdentifierError("");
                  }}
                  onBlur={() => setIsFocused(false)}
                  onChange={(e) => {
                    const value = e.target.value;

                    if (/^[\d+]*$/.test(value) && value.length <= 15) {
                      setAccountIdentifier(value);
                    }
                  }}
                  placeholder=""
                  className={`input-placeholder ${accountIdentifierError ? "accountIdentifierError" : ""
                    }`}
                />

                <label
                  htmlFor="username"
                  className={`label-name ${accountIdentifier || accountIdentifierError
                      ? "label-float"
                      : ""
                    } ${accountIdentifierError ? "input-error" : ""}`}
                >
                  {accountIdentifierError && !accountIdentifier
                    ? accountIdentifierError
                    : "Mobile"}
                </label>
              </div>
              <CustomButton
                label="Get Code"
                className="hover:shadow-5"
                onClick={handleNextStep}
                style={{
                  width: "156px",
                  height: "39px",
                  backgroundColor: "#179D52",
                }}
              />

              <p className={styles.verificationMessage}>
                Click <b>'get Code'</b> to receive your 6-digit verification
                number
              </p>
              {accountIdentifierError && accountIdentifier && !isFocused && (
                <div className="error-message">{accountIdentifierError}</div>
              )}
            </div>
          </>
        )}

        {currentStep === 2 && (
          <>
           {!isOtpSubmitted  ? (
                   <div className={styles.contentLeft}>
                   <p className={styles.headerTitle}>Enter your code</p>
                   <p
                     className={styles.instructionNumber}
                     style={{
                       fontWeight: "400",
                       fontSize: "16px",
                       color: "#585858",
                     }}
                   >
                     To complete your identity verification please enter the six
                     digit code that was sent by SMS to your mobile number
                   </p>
                 </div>
           ): (
            <div style={{marginTop:"70px"}} className="flex flex-column " >
          
            <p
              className={styles.instructionNumber}
              style={{
                fontWeight: "400",
                fontSize: "16px",
                color: "#585858",
                margin:"0"
              }}
            >
             <div style={{gap:"7px"}} className={styles.successMessageContainer}>
                {successMessage && (
                  <p style={{fontSize:"16px" , margin:"0"}} className={styles.successMessage}>{successMessage}</p>
                )}
                {successMessage && (
                  <i className={`pi pi-check ${styles.successIcon}`} />
                )}
              </div>


            </p>
          </div>
           )}
            <div className={styles.contentRight}>
              <p className={styles.steptext}>Part {currentStep} of 2</p>

              {!isOtpSubmitted ? (
                <InputOtp onComplete={handleOtpComplete} />
              ) : (
                <div
                  className="input-container"
                  style={{ marginTop: "35px", maxWidth: "100%" }}
                >
                  <InputText
                    style={{ border: "3px solid #179D52" }}
                    id="username"
                    name="username"
                    disabled
                    value={accountIdentifier}
                    onFocus={() => {
                      setIsFocused(true);
                      setAccountIdentifierError("");
                    }}
                    onBlur={() => setIsFocused(false)}
                    onChange={(e) => {
                      setAccountIdentifier(e.target.value);
                    }}
                    placeholder=""
                    className={`input-placeholder ${accountIdentifierError ? "accountIdentifierError" : ""
                      }`}
                  />
                  <label
                    htmlFor="username"
                    className={`label-name ${accountIdentifier || accountIdentifierError
                        ? "label-float"
                        : ""
                      } ${accountIdentifierError ? "input-error" : ""}`}
                  >
                    {accountIdentifierError && !accountIdentifier
                      ? accountIdentifierError
                      : " Mobile Verified "}
                  </label>
                </div>
              )}

              <div className={styles.codebuttonContainer}>
                {!isOtpSubmitted ? (
                  <CustomButton
                    label="Submit"
                    disabled={disable}
                    onClick={handleSubmit}
                    style={{
                      width: "156px",
                      height: "39px",
                      font: "14px",
                      fontWeight: "800",
                    }}
                    className={`${!disable ? "shadow-4" : ""}`}
                  />
                ) : (
                  <CustomButton
                    label="Next"
                    onClick={handleSkip}
                    style={{
                      width: "156px",
                      height: "39px",
                      font: "14px",
                      fontWeight: "800",
                    }}
                    className={`${!disable ? "shadow-4" : ""}`}
                  />
                )}

                {!isOtpSubmitted && (
                  <p onClick={handleResendCode} className={styles.resendLink}>
                    Resend Code
                  </p>
                )}
              </div>
              {/* <div className={styles.successMessageContainer}>
                {successMessage && (
                  <p className={styles.successMessage}>{successMessage}</p>
                )}
                {successMessage && (
                  <i className={`pi pi-check ${styles.successIcon}`} />
                )}
              </div> */}

              {/* {!isOtpSubmitted && (
                <p className={styles.verificationMessage}>
                  To complete your identity verification please enter the six
                  digit code that was sent by SMS to your mobile number
                </p>
              )} */}
            </div>
          </>
        )}
      </main>

      {!isOtpSubmitted && (
  !isMobile ? (
    <footer className={styles.buttonContainer}>
      {currentStep !== 1 && (
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left" style={{ marginRight: "8px" }}></i>
              Previous
            </>
          }
          disabled={disablePrevious}
          onClick={handlePreviousStep}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
          }}
        />
      )}

      <div style={{ flexGrow: 1 }} />

      {clientType !== 0 && (
        <CustomButton
          label={
            <>
              Skip
              <i className="pi pi-angle-right" style={{ marginLeft: "8px" }}></i>
            </>
          }
          className={styles.hoverClass}
          onClick={handleSkip}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontWeight: "400",
            fontSize: "14px",
            borderRadius: "10px",
            border: "1px solid #F0F4F7",
          }}
        />
      )}
    </footer>
  ) : (
    <footer className={styles.buttonContainerMobile}>
      {currentStep !== 1 && (
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left" style={{ marginRight: "8px" }}></i>
              Previous
            </>
          }
          disabled={disablePrevious}
          onClick={handlePreviousStep}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
            margin: "0px",
          }}
        />
      )}

      <div style={{ flexGrow: 1 }} />

      {clientType !== 0 && (
        <CustomButton
          label={
            <>
              Skip
              <i className="pi pi-angle-right" style={{ marginLeft: "8px" }}></i>
            </>
          }
          className={styles.hoverClass}
          onClick={handleSkip}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontWeight: "400",
            fontSize: "14px",
            borderRadius: "10px",
            border: "1px solid #F0F4F7",
            margin: "5px",
          }}
        />
      )}
    </footer>
  )
)}

    </div>
  );
};

export default IdentityCheck;
