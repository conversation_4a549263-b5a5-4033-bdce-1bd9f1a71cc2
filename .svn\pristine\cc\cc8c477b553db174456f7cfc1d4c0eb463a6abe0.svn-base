import React, { useState } from "react";
import { InputText } from "primereact/inputtext";
import { validateEmail, validatePassword } from "../../utils/validation";
import { useNavigateTo } from "../../../helper/navigation";
import styles from "./login-form.module.css";
import CustomButton from "../../../commonComponents/CustomButton";
import "../../utils/util.css";

interface LoginProps {
  onLogin: (email: string, password: string) => void;
  formError?: string;
}

export const LoginForm: React.FC<LoginProps> = ({ onLogin, formError }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const navigateTo = useNavigateTo();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setEmailError("");
    setPasswordError("");

    const passwordValidationError = validatePassword(password);
    const emailValidationError = validateEmail(email);

    if (passwordValidationError || emailValidationError) {
      if (passwordValidationError) {
        setPasswordError(passwordValidationError);
      }
      if (emailValidationError) {
        setEmailError(emailValidationError);
      }
      return;
    }

    onLogin(email, password);
  };

  const renderEmailInput = () => (
    <div className="input-container" style={{ maxWidth: "100%" }}>
      <InputText
        id="username"
        name="username"
        value={email}
        onFocus={() => {
          setEmailError("");
          setIsFocused(true);
        }}
        onBlur={() => setIsFocused(false)}
        onChange={(e) => setEmail(e.target.value)}
        placeholder=" "
        className={`input-placeholder ${
          emailError ? "border-red" : email ? "border-custom" : ""
        }`}
      />
      <label
        htmlFor="username"
        className={`label-name ${email || emailError ? "label-float" : ""} ${
          emailError ? "input-error" : ""
        }`}
      >
        {emailError && !email ? emailError : "Email*"}
      </label>
    </div>
  );

  const renderPasswordInput = () => (
    <div
      className="input-container mx-negative-14"
      style={{ maxWidth: "100%" }}
    >
      <div
        style={{ position: "relative", maxWidth: "100%", marginTop: "10px" }}
      >
        <InputText
          style={{ marginTop: "-18px" }}
          id="password"
          name="password"
          type={showPassword ? "text" : "password"}
          value={password}
          onFocus={() => {
            setPasswordError("");
            setIsFocused(true);
          }}
          onBlur={() => setIsFocused(false)}
          onChange={(e) => setPassword(e.target.value)}
          placeholder=" "
          className={`input-placeholder ${
            passwordError
              ? "passwordInputError"
              : password
              ? "border-custom"
              : ""
          }`}
        />
        <label
          htmlFor="password*"
          className={`label-name ${
            password || passwordError ? "label-float" : ""
          } ${passwordError ? "input-error" : ""}`}
        >
          {passwordError && !password ? passwordError : "Password*"}
        </label>
        <span
          onClick={() => setShowPassword(!showPassword)}
          style={{
            position: "absolute",
            right: getResponsiveStyle("right"),
            top: "50%",
            transform: "translateY(-50%)",
            fontSize: getResponsiveStyle("fontSize"),
            cursor: "pointer",
            color: "#888",
          }}
        >
          <i className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"}`}></i>
        </span>
      </div>
    </div>
  );

  const getResponsiveStyle = (type: "right" | "fontSize") => {
    const width = window.innerWidth;
    if (width <= 600) {
      return type === "right" ? "8px" : "1.2rem";
    }
    if (width <= 1024) {
      return type === "right" ? "10px" : "1.5rem";
    }
    return type === "right" ? "12px" : "1.8rem";
  };

  const renderErrorMessages = () => {
    if (emailError && email && !isFocused) {
      return <div className="error-message">{emailError}</div>;
    }
    if (passwordError && password && !isFocused) {
      return <div className="error-message">{passwordError}</div>;
    }
    if (formError) {
      return <div className="error-message">{formError}</div>;
    }
    return null;
  };

  return (
    <form className="bg-red" onSubmit={handleSubmit}>
      <h1 className={styles.loginTitle}>Login to your account</h1>
      <br />

      {renderEmailInput()}
      <br />
      {renderPasswordInput()}

      <a style={{ cursor: "pointer" }}>
        <p
          className={styles.forgotPasswordLink}
          onClick={() => navigateTo("/forgot-password", { replace: true })}
        >
          Forgot password?
        </p>
      </a>

      <CustomButton type="submit" label="Log in" />

      <div className={styles.joinJuggleStreetContainer}>
        <span className={styles.labelText}>Don't have an account?</span>{" "}
        <span
          className={styles.boldText}
          onClick={() => navigateTo("/join-now", { replace: true })}
        >
          Join Juggle Street
        </span>
      </div>
      <br />

      {renderErrorMessages()}
    </form>
  );
};
