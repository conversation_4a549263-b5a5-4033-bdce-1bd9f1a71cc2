import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import '../../../components/utils/util.css';
import { InputTextarea } from 'primereact/inputtextarea';
import CustomButton from '../../../commonComponents/CustomButton';
import {
    decrementProfileActivationStep,
    incrementProfileActivationStep,
} from '../../../store/slices/applicationSlice';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import c from '../../../helper/juggleStreetConstants';
import { BsBag } from 'react-icons/bs';
import { MdOutlineCastForEducation } from 'react-icons/md';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';

const Childcare = () => {
    const dispatch = useDispatch<AppDispatch>();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const [checkedState, setCheckedState] = useState<boolean[]>(new Array(c.childcareJobTypes.length).fill(false));
    const [radioState, setRadioState] = useState<string>('');
    const minCharLimit = 100;
    const [myExperience, setMyExperience] = useState<string>(sessionInfo.data["provider"]["myExperience"] || '');
    const { disableLoader, enableLoader } = useLoader();
    const [qualificationCheckedState, setQualificationCheckedState] = useState<boolean[]>([]);
    const {isMobile}=useIsMobile()
    useEffect(() => {
        if (sessionInfo.data) {
            if (sessionInfo.data?.['interestedInJobTypes'] !== undefined) {
                const previousSelections = c.childcareJobTypes.map(jobType =>
                    Boolean(sessionInfo.data?.['interestedInJobTypes'] & jobType.value)
                );
                setCheckedState(previousSelections);
            }
            if (sessionInfo.data?.['provider']) {
                setMyExperience(sessionInfo.data?.['provider'].myExperience || '');
                const hasQualifications = sessionInfo.data?.['provider'].qualifications.some(q => q.selected);
                setRadioState(hasQualifications ? 'yes' : 'no');
                if (sessionInfo.data?.['provider'].qualifications) {
                    const qualificationStates = sessionInfo.data?.['provider'].qualifications.map(
                        q => q.selected || false
                    );
                    setQualificationCheckedState(qualificationStates);
                }
            }
        }
    }, [sessionInfo.data]);

    const handleRadioChange = (value: string) => {
        setRadioState(value);
        if (value === 'yes') {
            setQualificationCheckedState(new Array(sessionInfo.data["provider"]["qualifications"].length).fill(false));
        } else {
            setQualificationCheckedState([]);
        }
    };

    const handleCheckboxChange = (index: number) => {
        setCheckedState(prev => {
            const updated = [...prev];
            updated[index] = !updated[index];
            return updated;
        });
    };
    const calculateInterestedInJobTypes = () => {
        return checkedState.reduce((sum, checked, index) => {
            if (checked) {
                // Using bitwise OR to set the bit
                return sum | c.childcareJobTypes[index].value;
            }
            return sum;
        }, 0);
    };
    const handleQualificationCheckboxChange = (index: number) => {
        const updatedCheckedState = qualificationCheckedState.map((item, idx) => (idx === index ? !item : item));
        setQualificationCheckedState(updatedCheckedState);
    };

    const handleprev = () => {
        dispatch(decrementProfileActivationStep());
    };

    const handleInputChange = (e) => {
        const value = e.target.value;
        const countWords = (text) => {
            return text
                .trim()
                .split(/\s+/)
                .filter((word) => word.length > 0).length;
        };
        const totalWords = countWords(value);
        if (totalWords <= 120) {
            setMyExperience(value.trim().length > 0 ? value : '');
        }
    };

    const handleNext = () => {
        const selectedQualifications = sessionInfo.data["provider"]["qualifications"].map((qualification, index) => ({
            ...qualification,
            selected: qualificationCheckedState[index],
        }));
        const interestedInJobTypes = calculateInterestedInJobTypes();
        const payload = {
            ...sessionInfo.data,
            interestedInJobTypes: interestedInJobTypes,
            provider: {
                ...sessionInfo.data["provider"],
                myExperience: myExperience,
                qualifications: selectedQualifications,
            },
        };
        enableLoader();
        dispatch(updateSessionInfo({ payload: payload })).finally(() => {
            disableLoader();
            dispatch(incrementProfileActivationStep());
        });
        
    };
    const handleSkip = () => {
        dispatch(incrementProfileActivationStep());
    };
    return (
        <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
            <ProfileCompletenessHeader
                title="Childcare"
                profileCompleteness={sessionInfo.data['profileCompleteness']}
                loading={sessionInfo.loading}
                onBackClick={()=>dispatch(decrementProfileActivationStep())}
            />
            <div className='flex' style={{ maxWidth: '100%', width: 'auto', textWrap: 'wrap' , paddingInline:isMobile && "15px"}}>
                <span><BsBag style={{ color: '#585858', fontSize: '18px' }} /></span>
                <h1
                    className="m-0 p-1 txt-clr flex-wrap font-medium line-height-1"
                    style={{ fontSize: '16px', color: '#585858', }}
                >
                    Select all the childcare jobs you would be willing to do for families and
                    businesses nearby
                </h1>
            </div>
            <div className="flex flex-column justify-content-center " style={{ marginBottom: '' , paddingInline:isMobile && "15px"}}>
                {c.childcareJobTypes.map((jobType, index) => (
                    <label key={jobType.value} className="flex items-center gap-2 p-1 cursor-pointer">
                        <input
                            type="checkbox"
                            className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                            checked={checkedState[index]}
                            onChange={() => handleCheckboxChange(index)}
                            style={{ fontSize: '18px' }}
                        />
                        <span className="txt-clr" style={{ fontSize: '16px' }}>
                            {jobType.label}
                        </span>
                    </label>
                ))}
            </div>
            <div style={{paddingInline:isMobile && "15px"}}>
                <h1
                    className="m-0 p-1 txt-clr font-medium line-height-1 mt-2"
                    style={{ fontSize: '16px', color: '#585858', }}
                >
                    Describe your childcare experience
                </h1>
            </div>
            {/* Render all textareas */}
            <div
                className="mt-2 p-1"
                style={{ minHeight: '100px', overflow: 'auto', width:!isMobile ? '80%' : '', marginInline:isMobile && "15px" }}
            >
                <InputTextarea
                    autoResize
                    value={myExperience}
                    required
                    onChange={handleInputChange}
                    rows={3}
                    cols={30}
                    className={styles.inputTextareafamily}
                    placeholder="How long have you been looking after children for? What age children are you most comfortable with? What do you find most rewarding about childcare? Etc."
                />
            </div>
            <p
                style={{
                    fontSize: '14px',
                    color: myExperience.length < minCharLimit ? 'red' : 'green',
                    fontWeight: '400',
                    paddingInline:isMobile && "20px"
                }}
            >
                {myExperience.length < minCharLimit &&
                    `${minCharLimit - myExperience.length} characters remaining`}
            </p>
            {/* Render all Yes/No radio buttons */}

            <div style={{paddingInline:isMobile && "15px",marginBottom:isMobile && "70px"}}>
                <div className='flex'>
                    <h1
                        className="m-0 p-1 txt-clr flex flex-wrap gap-1 font-medium line-height-1 mt-2"
                        style={{ fontSize: '16px', color: '#585858' }}
                    >
                        <span><MdOutlineCastForEducation style={{ fontSize: '18px', color: '#585858' }} /></span> Do you have any childcare qualifications?
                    </h1>
                </div>


                <div className="flex justify-content-start items-center mt-2 gap-2 ">
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="shared-radio"
                            value="yes"
                            checked={radioState === 'yes'}
                            onChange={() => handleRadioChange('yes')}
                            className=" cursor-pointer"
                        />
                        Yes
                    </label>
                    <label className="flex justify-content-start items-center txt-clr cursor-pointer">
                        <input
                            type="radio"
                            name="shared-radio"
                            value="no"
                            checked={radioState === 'no'}
                            onChange={() => handleRadioChange('no')}
                            className="cursor-pointer"
                        />
                        No
                    </label>
                </div>
                {radioState === 'yes' && sessionInfo.data?.['provider']?.qualifications && (
                    <div className="mt-2">
                        {sessionInfo.data?.['provider'].qualifications.map((qualification, index) => (
                            <label key={qualification.optionId} className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={qualificationCheckedState[index] || false}
                                    onChange={() => handleQualificationCheckboxChange(index)}
                                    className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                                    style={{ fontSize: '18px' }}
                                />
                                <span className="txt-clr" style={{ fontSize: '16px' }}>
                                    {qualification.text}
                                </span>
                            </label>
                        ))}
                    </div>
                )}
            </div>
            <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
                <CustomButton
                    label={
                        <>
                            <i className="pi pi-angle-left"></i>
                            Previous
                        </>
                    }
                    onClick={handleprev}
                    style={{
                        backgroundColor: 'transparent',
                        color: '#585858',
                        width: '156px',
                        height: '39px',
                        fontSize: '14px',
                        fontWeight: '500',
                        margin: isMobile &&"5px"
                    }}
                />
                <div style={{ flexGrow: 1 }} />
                <CustomButton
                    className={styles.hoverClass}
                    data-skip={myExperience.length >= minCharLimit ? 'false' : 'true'}
                    onClick={myExperience.length >= minCharLimit ? handleNext : handleSkip}
                    label={
                        <>
                            {myExperience.length >= minCharLimit ? 'Next' : 'Skip'}
                            <i
                                className={`pi pi-angle-${myExperience.length >= minCharLimit ? 'right' : 'right'
                                    }`}
                                style={{ marginLeft: '8px' }}
                            ></i>
                        </>
                    }
                    style={
                        myExperience.length >= minCharLimit
                            ? {
                                backgroundColor: '#FFA500',
                                color: '#fff',
                                width: '156px',
                                height: '39px',
                                fontWeight: '800',
                                fontSize: '14px',
                                borderRadius: '8px',
                                border: '2px solid transparent',
                                boxShadow: '0px 4px 12px #00000',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                                margin: isMobile &&"5px"
                            }
                            : {
                                backgroundColor: 'transparent',
                                color: '#585858',
                                width: '156px',
                                height: '39px',
                                fontWeight: '400',
                                fontSize: '14px',
                                borderRadius: '10px',
                                border: '1px solid #F0F4F7',
                                margin: isMobile &&"5px"
                            }
                    }
                />
            </footer>
        </div>
    );
};

export default Childcare;
