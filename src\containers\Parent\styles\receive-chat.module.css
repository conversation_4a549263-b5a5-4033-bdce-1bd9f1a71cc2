.chatReceiveContainer {
  display: flex;
  flex-direction: column;
  border: 0.5px solid #dfdfdf;
  overflow-x: hidden;
  overflow-y: auto;
  flex-grow: 1;
}

.chatMessage {
  padding: 10px;
  margin: 5px 0;
  border-radius: 5px;
  width: 100%;
  height: 108px;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
}
.ChatHeader {
  margin: 0px;
  font-size: 16px;
  font-weight: 700;
  color: #585858;
}
.chatPhoto {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-top: 10px;
}
.BadgeTag{
  background-color: #FF6359;
  color: #fff;
  font-size: 10px;
  height: 15px;
  width: 15px;
  margin: 0px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

}
.selectedMessage {
  background-color: #f1f1f1;  /* Light blue background for the selected message */
  box-shadow: 0px 0px 4px 0px #00000040;
}
.chatMessageMobile {
  padding: 10px;
  margin: 5px 0;
  border-radius: 5px;
  width: 100%;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
box-shadow: 0px 0px 4px 0px #00000040;

}
.selectedMessageMobile {

}
.chatReceiveContainerMobile {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  flex-grow: 1;
}