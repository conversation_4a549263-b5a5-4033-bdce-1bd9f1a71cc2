import React from 'react';
import styles from '../commonStyle/custom-footer-button-mobile.module.css';

interface CustomFooterButtonProps {
    label: string;
    isDisabled?: boolean;
    onClick: () => void;
    withoutZIndex?: boolean; // new prop
}

const CustomFooterButton: React.FC<CustomFooterButtonProps> = ({
    label,
    isDisabled = false,
    onClick,
    withoutZIndex
    
}) => {
    return (
        <div className={styles.customFooter}
        style={withoutZIndex ? { zIndex: 'auto' } : undefined}
        >
            <button
                className={styles.customButton}
                disabled={isDisabled}
                onClick={onClick}
            >
                {label}
            </button>
        </div>
    );
};

export default CustomFooterButton;
