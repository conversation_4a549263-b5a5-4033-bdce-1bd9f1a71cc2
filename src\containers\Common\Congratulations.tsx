import React, { useEffect, useState } from 'react';
import Confetti from 'react-confetti';
import styles from './styles/congratulations.module.css'; // Adjust the path if needed
import logoSrc from '../../assets/images/juggle-st-transparent-card.png';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import CustomButton from '../../commonComponents/CustomButton';
import utils from '../../components/utils/util';
import CookiesConstant from '../../helper/cookiesConst';
import useIsMobile from '../../hooks/useIsMobile';
import HorizontalNavigation from './HorizontalNavigationMobile';

interface CongratulationsProps {
    onHide: () => void; // Add the prop to handle the dialog close
}

const Congratulations: React.FC<CongratulationsProps> = ({ onHide }) => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const { isMobile } = useIsMobile()
    const [showConfetti, setShowConfetti] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setShowConfetti(false);
        }, 3000);

        return () => clearTimeout(timer);
    }, []);
    const getMessage = () => {
        switch (clientType) {
            case 0:
                return (
                    <p className={styles.congratspara}>
                        <b>Your profile is now 100% complete!</b>
                        <br />
                        Congratulations your profile is now complete!
                        View profiles of families and businesses in your neighbourhood, and start receiving job invitations.
                    </p>
                );
            case 1:
                return (
                    <p className={styles.congratspara}>
                        <b>Your profile is now 100% complete!</b>
                        <br />
                        Welcome to the Juggle Street community! You're all set to find a local helper.
                        Our paid memberships include unlimited job posts, chat with helpers, and AON Public Liability Insurance.
                    </p>
                );
            case 2:
                return (
                    <p className={styles.congratspara}>
                        <b>Your profile is now 100% complete!</b>
                        <br />
                        Welcome to the Juggle Street
                        community! You’re all set to
                        find local childcare workers.

                    </p>
                );
            default:
                return null;
        }
    };

    return (
        <div style={{ paddingInline: isMobile && "10px", paddingTop: isMobile && "80px", height: isMobile && "100%" }} className={styles.congratscontainer}>
            {isMobile && (
                <HorizontalNavigation
                    title={`Congratulations`}
                    onBackClick={onHide} />
            )}
            {showConfetti && (
                <Confetti
                    width={window.innerWidth}
                    height={window.innerHeight}
                    numberOfPieces={300}
                    gravity={0.6}
                    tweenDuration={50}
                />
            )}
            <div className={styles.headerContainer}>
                <header className={styles.congratsheader}>
                    <img loading="lazy" src={logoSrc} alt="User Panel Logo" />
                </header>
            </div>
            <h1 className={styles.congotitle}>
                Congratulations {sessionInfo.loading ? '' : sessionInfo.data['firstName']}!
            </h1>

            <div >
                {getMessage()}
            </div>

            {/* Trigger the onHide function when button is clicked */}
            {!isMobile ? (
                <CustomButton
                    style={{ marginLeft: '20px' }}
                    label="Get Started"
                    onClick={onHide} // Close the dialog on button click
                />
            ) : (
                <div className={styles.congralationButton}>
                    <CustomButton
                        style={{ width: "max-content", paddingInline: "70px", margin: "5px" }}
                        label="Get Started"
                        onClick={onHide} // Close the dialog on button click
                    />
                </div>
            )}
        </div>
    );
};

export default Congratulations;
