import React, { useState } from 'react';
import { InputText } from 'primereact/inputtext';
import { validateEmail, validatePhoneNumber } from '../../utils/validation';
import styles from '../../../commonStyle/forgot-password-form.module.css';
import CustomButton from '../../../commonComponents/CustomButton';
import '../../utils/util.css';
import { useNavigate } from 'react-router-dom';

interface LoginProps {
    onSubmit: (accountIdentifier: string) => void;
    formError?: string;
}

const FormHeader: React.FC = () => (
    <div className="p-mb-3 flex flex-column gap-2">
        <label className={styles.imageText}>
            <h2>Forgot Your Password?</h2>
        </label>
        <p className={styles.instructionText}>
            Please type your account email or phone number and we will send a
            verification code that can be used to reset your password.
            <br />
            <br />
        </p>
    </div>
);

const AccountIdentifierInput: React.FC<{
    accountIdentifier: string;
    setAccountIdentifier: (value: string) => void;
    accountIdentifierError: string;
    setAccountIdentifierError: (error: string) => void;
}> = ({
    accountIdentifier,
    setAccountIdentifier,
    accountIdentifierError,
    setAccountIdentifierError
}) => {
    const [isFocused, setIsFocused] = useState(false);

    return (
        <div className="mb-3 flex flex-column gap-2">
            <div className="input-container" style={{ maxWidth: '100%' }}>
                <InputText
                    id="username"
                    name="username"
                    value={accountIdentifier}
                    onFocus={() => {
                        setIsFocused(true);
                        setAccountIdentifierError('');
                    }}
                    onBlur={() => setIsFocused(false)}
                    onChange={(e) => {
                        setAccountIdentifier(e.target.value);
                    }}
                    placeholder=""
                    className={`input-placeholder ${
                        accountIdentifierError 
                            ? 'accountIdentifierError' 
                            : accountIdentifier 
                                ? 'border-custom'
                                : ''
                    }`}
                />
                <label
                    htmlFor="username"
                    className={`label-name ${
                        accountIdentifier || accountIdentifierError ? 'label-float' : ''
                    } ${accountIdentifierError ? 'input-error' : ''}`}
                >
                    {accountIdentifierError && !accountIdentifier
                        ? accountIdentifierError
                        : 'Email or Phone Number*'}
                </label>
            </div>

            {accountIdentifierError && accountIdentifier && !isFocused && (
                <div className="error-message">{accountIdentifierError}</div>
            )}
        </div>
    );
};

const ForgotPasswordForm: React.FC<LoginProps> = ({ onSubmit, formError }) => {
    const [accountIdentifier, setAccountIdentifier] = useState('');
    const [accountIdentifierError, setAccountIdentifierError] = useState('');
   const navigate=useNavigate();
    const validate = (e: React.FormEvent) => {
        e.preventDefault();
        setAccountIdentifierError('');

        const isNumeric = /^\+?\d+$/.test(accountIdentifier);
        const emailValidationError = validateEmail(accountIdentifier);

        if (isNumeric) {
            const phoneValidationValid = validatePhoneNumber(accountIdentifier);
            if (!phoneValidationValid) {
                setAccountIdentifierError('Enter a valid mobile number');
                return false;
            }
        } else if (emailValidationError) {
            setAccountIdentifierError(emailValidationError);
            return false;
        }

        onSubmit(accountIdentifier);
        return true;
    };

    return (
        <div>
            <form
                onSubmit={validate}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                }}
            >
                <FormHeader />
                
                <AccountIdentifierInput
                    accountIdentifier={accountIdentifier}
                    setAccountIdentifier={setAccountIdentifier}
                    accountIdentifierError={accountIdentifierError}
                    setAccountIdentifierError={setAccountIdentifierError}
                />

                {formError && <div className="error-message">{formError}</div>}

                <div className="mb-1">
                    <CustomButton type="submit" label="Reset My Password" />
                </div>
                
                <p onClick={()=>navigate("/")} className={styles.backToLogin}>
                    Back to log in
                </p>
            </form>
        </div>
    );
};

export default ForgotPasswordForm;