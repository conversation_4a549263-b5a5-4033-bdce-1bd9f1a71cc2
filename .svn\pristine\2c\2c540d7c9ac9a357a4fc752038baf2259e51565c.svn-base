import React from 'react';
import styles from '../Common/styles/time-sheet-card.module.css';
import ChildcareImage from "../../assets/images/Icons/childcare-smile.png";
import { PiBriefcase, PiCalendar, PiMapPin, PiUserCircle } from 'react-icons/pi';
import { Divider } from 'primereact/divider';
import calendar from "../../assets/images/Icons/calender.png";
import home from "../../assets/images/Icons/home.png";
function TimeSheetCard({ status, type, date, location, userName, onReview }) {
  return (
    <div className='mx-3 my-3'>
      <div className={styles.card}>
        <div className='flex flex-row justify-content-between h-full py-3 px-3'>
          <div className={styles.cardContent}>
            <div className={styles.header}>
              <span style={{ fontSize: "16px", fontWeight: "700", color: "#585858" }}>Status:</span>
              <span className={styles.status}>{status ?? 'N/A'}</span>

            </div>
            <div className={styles.infoRow}>
              <img src={ChildcareImage}
                alt="Childcare"
                width={18.33}
                height={17.5} />
              <span className={styles.spantag}>{type ?? 'N/A'}</span>
            </div>
            <div className={styles.infoRow}>
              <img
                alt="calendar"
                src={calendar}
                style={{ width: "15px", height: "15px", color: "#FFFFFF" }}
              />
              <span className={styles.spantag}>{date ?? 'N/A'}</span>
            </div>
            <div className={styles.infoRow}>
              <img
                alt="Home"
                src={home}
                style={{ width: "16px", color: "#FFFFFF", flexShrink: 0 }} // Reduced from 18px
              />
              <span className={styles.spantag}>{location ?? 'N/A'}</span>
            </div>
          </div>
          <div className={styles.user}>
            <PiUserCircle className={styles.avatar} />
            <span className={styles.userName}>{userName ?? 'N/A'}</span>
          </div>
        </div>
        <Divider />
        <div className='flex justify-content-center'>
          <button
            onClick={onReview}
            className={styles.button}
          >
            Review Timsheet
          </button>
        </div>
      </div>
    </div>
  );
}

export default TimeSheetCard;