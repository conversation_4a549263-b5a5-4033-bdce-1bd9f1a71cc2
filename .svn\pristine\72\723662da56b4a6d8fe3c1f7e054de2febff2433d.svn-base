import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import React, { useState } from 'react';

function useConfirmationPopup() {
    const [visible, setVisible] = useState<boolean>(false);
    const [heading, setHeading] = useState<string>('');
    const [message, setMessage] = useState<string>('');
    const [confirmText, setConfirmText] = useState<string>('');
    const [confirmIcon, setConfirmIcon] = useState<React.ReactNode | null>(null);
    const [onAccept, setOnAccept] = useState<() => void | null>(null);
    const [onReject, setOnReject] = useState<() => void | null>(null);

    function showConfirmationPopup(
        heading: string,
        message: string,
        confirmText: string,
        confirmIcon: React.ReactNode,
        onAccept?: () => void,
        onReject?: () => void
    ) {
        setHeading(heading);
        setMessage(message);
        setConfirmText(confirmText);
        setConfirmIcon(confirmIcon);
        setOnAccept(() => onAccept || null);
        setOnReject(() => onReject || null);
        setVisible(true);
    }

    function performAction(action: 1 | 2) {
        setVisible(false);
        if (action === 1 && onAccept) {
            onAccept();
        } else if (action === 2 && onReject) {
            onReject();
        }
        setHeading('');
        setMessage('');
        setConfirmText('');
        setConfirmIcon(null);
        setOnAccept(null);
        setOnReject(null);
    }

    return {
        confirmationProps: {
            visible,
            heading,
            confirmText,
            confirmIcon,
            message,
            performAction,
        },
        showConfirmationPopup,
        
    };
}

function ConfirmationPopupRed({
    confirmationProps,
}: {
    confirmationProps: {
        visible: boolean;
        heading: string;
        confirmText: string;
        confirmIcon: React.ReactNode;
        message: string;
        performAction: (action: 1 | 2) => void;
    };
}) {
    return (
        <Dialog
            visible={confirmationProps.visible}
            onHide={() => {}}
            content={
                <div
                    className="w-24rem md:w-30rem border-round-3xl px-3 py-3 flex flex-column"
                    style={{
                        backgroundColor: '#FFFFFF',
                        border: '1px solid #F0F4F7',
                        color: '#585858',
                    }}
                >
                    <div className="flex justify-content-between px-1 mb-2">
                        <h1
                            className="m-0 p-0"
                            style={{
                                fontSize: '20px',
                                fontWeight: '700',
                            }}
                        >
                            {confirmationProps.heading}
                        </h1>
                        <div className="flex gap-2 align-items-center">
                            <p
                                className="m-0 p-0 cursor-pointer"
                                style={{
                                    fontSize: '13px',
                                    fontWeight: '300',
                                }}
                                onClick={() => confirmationProps.performAction(2)}
                            >
                                Cancel
                            </p>
                            <div
                                className="flex gap-2 align-items-center h-full py-1 cursor-pointer"
                                style={{
                                    backgroundColor: 'rgba(255, 99, 89, 0.3)',
                                    border: '1px solid #FF6359',
                                    color: '#FF6359',
                                    fontWeight: '500',
                                    fontSize: '14px',
                                    paddingInline: '13px',
                                    borderRadius: '10px',
                                }}
                                onClick={() => confirmationProps.performAction(1)}
                            >
                                {confirmationProps.confirmIcon}
                                <p className="m-0 p-0">{confirmationProps.confirmText}</p>
                            </div>
                        </div>
                    </div>
                    <Divider />
                    <p
                        className="m-0 p-0 mt-0 py-1"
                        style={{
                            color: '#585858',
                            fontSize: '16px',
                            fontWeight: '500',
                        }}
                    >
                        {confirmationProps.message}
                    </p>
                </div>
            }
        />
    );
}
function ConfirmationPopupGreen({
    confirmationProps,
}: {
    confirmationProps: {
        visible: boolean;
        heading: string;
        confirmText: string;
        confirmIcon: React.ReactNode;
        message: string;
        performAction: (action: 1 | 2) => void;
    };
}) {
    return (
        <Dialog
            visible={confirmationProps.visible}
            onHide={() => {}}
            content={
                <div
                    className="w-24rem md:w-30rem border-round-3xl px-3 py-3 flex flex-column"
                    style={{
                        backgroundColor: '#FFFFFF',
                        border: '1px solid #F0F4F7',
                        color: '#585858',
                    zIndex:9999
                    }}
                >
                    <div className="flex justify-content-between px-1 mb-2">
                        <h1
                            className="m-0 p-0"
                            style={{
                                fontSize: '20px',
                                fontWeight: '700',
                            }}
                        >
                            {confirmationProps.heading}
                        </h1>
                        <div className="flex gap-2 align-items-center">
                            <p
                                className="m-0 p-0 cursor-pointer"
                                style={{
                                    fontSize: '16px',
                                    fontWeight: '300',
                                    fontFamily: 'Poppins',
                                    color: '#585858',
                                }}
                                onClick={() => confirmationProps.performAction(2)}
                            >
                                Cancel
                            </p>
                            <div
                                className="flex gap-1 align-items-center h-full px-2 py-1 border-round-xl cursor-pointer"
                                style={{
                                    backgroundColor: 'rgba(23, 157, 82, 0.3)',
                                    border: '1px solid #179d52',
                                    color: '#179d52',
                                    fontWeight: '500',
                                    fontSize: '14px',
                                }}
                                onClick={() => confirmationProps.performAction(1)}
                            >
                                {confirmationProps.confirmIcon}
                                <p className="m-0 p-0">{confirmationProps.confirmText}</p>
                            </div>
                        </div>
                    </div>
                    <Divider />
                    <p
                        className="m-0 p-0 mt-2 py-3"
                        style={{
                            color: '#585858',
                            fontSize: '16px',
                            fontWeight: '500',
                        }}
                    >
                        {confirmationProps.message}
                    </p>
                </div>
            }
        />
    );
}

export { ConfirmationPopupRed, ConfirmationPopupGreen, useConfirmationPopup };
