/* eslint-disable no-console */
import { writeFile } from "node:fs/promises";
import { resolve } from "node:path";

// Generate version from current date and time
const now = new Date();

const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, "0");
const day = String(now.getDate()).padStart(2, "0");
const hour = String(now.getHours()).padStart(2, "0");
const minute = String(now.getMinutes()).padStart(2, "0");
const second = String(now.getSeconds()).padStart(2, "0");

// Globally accepted version format: YYYY.MM.DD+HHMMSS
const version = `${year}.${month}.${day}+${hour}${minute}${second}`;

// Output file path
const outputPath = resolve("src/version.json");

// Write the version as JSO<PERSON>
await writeFile(outputPath, JSON.stringify({ version }, null, 2));

console.log(`✅ Version generated: ${version}`);