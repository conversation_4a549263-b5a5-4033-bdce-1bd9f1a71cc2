import { Divider } from 'primereact/divider';
import { CSSProperties, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaPlus } from 'react-icons/fa6';
import { FiEdit3 } from 'react-icons/fi';
import { Dialog } from 'primereact/dialog';
import { IoIosClose } from 'react-icons/io';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import { ConfirmationPopupRed, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import useLoader from '../../../hooks/LoaderHook';
import utils from '../../../components/utils/util';
import styles from '../styles/my-children.module.css';
import removeIcon from '../../../assets/images/Icons/remove.png';
import useIsMobile from '../../../hooks/useIsMobile';

interface FloatingLabelInputProps {
    label: string;
    type?: string;
    style?: CSSProperties;
    onChange: (value: string) => void;
    noLabel?: boolean;
    length?: number;
    value: string | null;
    onlyText?: boolean;
}

const FloatingLabelInput: React.FC<FloatingLabelInputProps> = ({
    label,
    type = 'text',
    style,
    onChange,
    noLabel = false,
    length = -1,
    value = null,
    onlyText = false,
}) => {
    const [isFocused, setIsFocused] = useState(false);
    const [inputValue, setInputValue] = useState('');

    useEffect(() => {
        if (value !== null) {
            setInputValue(value);
        }
    }, [value]);

    const labelStyle: CSSProperties = {
        position: 'absolute',
        top: inputValue || isFocused ? '-10px' : '10px',
        left: '10px',
        fontSize: inputValue || isFocused ? '12px' : '16px',
        color: '#585858',
        background: '#F0F4F7',
        transition: '0.2s ease all',
        pointerEvents: 'none',
        padding: '0 5px',
        borderRadius: '10px',
    };

    const inputStyle: CSSProperties = {
        width: '100%',
        padding: '10px',
        fontSize: '16px',
        border: isFocused || inputValue.length > 0 ? '2px solid #179D52' : '2px solid #F0F4F7',
        borderRadius: '10px',
        outline: isFocused ? 'none' : '',
        backgroundColor: '#F0F4F7',
        color: '#585858',
        ...style,
    };

    const containerStyle: CSSProperties = {
        position: 'relative',
        display: 'inline-block',
        width: '100%',
        margin: '10px 0',
        height: '56px',
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let newValue = e.target.value;

        if (onlyText) {
            newValue = newValue.replace(/[^a-zA-Z]/g, '');
        }

        if (length === -1 || newValue.length <= length) {
            setInputValue(newValue);
            onChange(newValue);
        }
    };

    return (
        <div style={containerStyle}>
            {!noLabel && <label style={labelStyle}>{label}</label>}
            <input
                type={type}
                style={inputStyle}
                value={inputValue}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(inputValue !== '')}
                placeholder={label}
                onChange={handleInputChange}
                maxLength={length !== -1 ? length : undefined}
            />
        </div>
    );
};

interface RadioButtonProps {
    value: number;
    id: string;
    checked: boolean;
    label: string;
    onChange: (value: number) => void;
}

const RadioButton: React.FC<RadioButtonProps> = ({ value, label, checked, onChange }) => {
    return (
        <div
            style={{
                border: !checked ? '1px solid #DFDFDF' : '2px solid #179D52',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '5px 10px',
                gap: '10px',
                cursor: 'pointer',
                borderRadius: '10px',
                backgroundColor: 'white',
                transition: 'background-color 0.2s ease',
                height: '51px',
                minWidth: '125px',
            }}
            onClick={() => onChange(value)}
        >
            <div
                className='p-1'
                style={{
                    width: '18px',
                    height: '18px',
                    borderRadius: '50%',
                    border: !checked ? '1px solid #585858' : '1px solid #179D52',
                    backgroundColor: checked ? '#179D52' : 'white',
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                {checked && (
                    <div
                        className='flex justify-content-center align-items-center p-1'
                        style={{
                            width: '18px',
                            height: '18px',
                            border: '1px solid #179D52',
                            borderRadius: '50%',
                            backgroundColor: '#179D52',
                        }}
                    >
                        <FaCheck className='text-white' />
                    </div>
                )}
            </div>
            <label style={{ cursor: 'pointer', fontSize: '14px', color: '#585858' }}>{label}</label>
        </div>
    );
};

interface Child {
    firstName: string;
    dateOfBirth: string;
    gender: number;
    aboutMe: string | null;
    allergyDetails: string | null;
    schoolYear: number;
    schoolId: number | null;
    schoolName: string | null;
    hasAllergies: boolean;
    ageInYears: number;
    id: number;
}

function MyChildren() {
    const [allChildren, setAllChildren] = useState<Child[]>([]);
    const [selectedChildren, setSelectedChildren] = useState<Child | null>(null);
    const [addEditVisible, setAddEditVisible] = useState<boolean>(false);
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { isMobile } = useIsMobile();
    const { enableLoader, disableLoader } = useLoader();

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    const [firstName, setFirstName] = useState<string>('');
    const [selectedMonth, setSelectedMonth] = useState<string>('');
    const [selectedYear, setSelectedYear] = useState<string>('');
    const [selectedGender, setSelectedGender] = useState<number>(-1);
    const [selectedAllergies, setSelectedAllergies] = useState<boolean>(false);
    const [selectedAboutAllergies, setSelectedAboutAllergies] = useState<string>('');
    const [aboutMe, setAboutMe] = useState<string>('');
    const [saveEnabled, setSaveEnabled] = useState<boolean>(false);

    const dispatch = useDispatch<AppDispatch>();

    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();

    const availableYears = useMemo(() => {
        return Array.from({ length: 50 }, (_, index) => (currentYear - index).toString());
    }, [currentYear]);

    const availableMonths = useMemo(() => {
        if (!selectedYear) return [];
        const yearNumber = parseInt(selectedYear, 10);
        const monthsInYear = yearNumber === currentYear ? currentDate.getMonth() + 1 : 12;
        return Array.from({ length: monthsInYear }, (_, index) => {
            const monthNumber = index + 1;
            return {
                value: String(monthNumber).padStart(2, '0'),
                label: new Date(0, monthNumber - 1).toLocaleString('default', {
                    month: 'long',
                }),
            };
        });
    }, [selectedYear, currentYear, currentDate]);

    const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedYear(e.target.value);
        setSelectedMonth('');
    };

    const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedMonth(e.target.value);
    };

    useEffect(() => {
        if (sessionInfo.loading) return;
        setAllChildren(sessionInfo.data['children']);
    }, [sessionInfo]);

    useEffect(() => {
        let isValid = false;

        if (selectedChildren === null) {
            isValid =
                firstName.length > 0 &&
                selectedGender !== -1 &&
                selectedMonth !== '' &&
                selectedYear !== '' &&
                aboutMe !== '' &&
                (selectedAllergies ? selectedAboutAllergies !== '' : true);
        } else {
            const dateOfBirth = new Date(selectedChildren.dateOfBirth);
            const existingMonth = (dateOfBirth.getMonth() + 1).toString().padStart(2, '0');
            const existingYear = dateOfBirth.getFullYear().toString();

            isValid =
                (firstName !== selectedChildren.firstName ||
                    selectedGender !== selectedChildren.gender ||
                    selectedMonth !== existingMonth ||
                    selectedYear !== existingYear ||
                    selectedAllergies !== selectedChildren.hasAllergies ||
                    selectedAboutAllergies !== selectedChildren.allergyDetails ||
                    aboutMe !== (selectedChildren.aboutMe || '')) &&
                firstName.length > 0 &&
                selectedGender !== -1 &&
                selectedMonth !== '' &&
                aboutMe !== '' &&
                selectedYear !== '' &&
                (selectedAllergies ? selectedAboutAllergies !== '' : true);
        }

        setSaveEnabled(isValid);
    }, [
        firstName,
        selectedGender,
        selectedAllergies,
        selectedMonth,
        selectedYear,
        aboutMe,
        selectedAboutAllergies,
        selectedChildren,
    ]);

    function handleAddEditClicked(children: Child | null) {
        if (children !== null) {
            const date = new Date(children.dateOfBirth);
            setFirstName(children.firstName);
            setSelectedYear(date.getFullYear().toString());
            setSelectedMonth((date.getMonth() + 1).toString().padStart(2, '0'));
            setSelectedGender(children.gender);
            setSelectedAllergies(children.hasAllergies);
            setSelectedAboutAllergies(
                children.allergyDetails === null ? '' : children.allergyDetails
            );
            setAboutMe(children.aboutMe || '');
        } else {
            setFirstName('');
            setSelectedMonth('');
            setSelectedYear('');
            setSelectedGender(-1);
            setSelectedAllergies(false);
            setSelectedAboutAllergies('');
            setAboutMe('');
        }
        setSelectedChildren(children);
        setAddEditVisible(true);
    }

    function getDateStringAndAge(
        year: number,
        month: number
    ): { isoDateString: string; age: number } {
        const date = new Date(Date.UTC(year, month - 1, 1));

        const isoDateString = date.toISOString();

        const today = new Date();
        let age = today.getUTCFullYear() - year;

        if (
            today.getUTCMonth() < month - 1 ||
            (today.getUTCMonth() === month - 1 && today.getUTCDate() < 1)
        ) {
            age--;
        }

        return { isoDateString, age };
    }

    function handleRemoveClicked(children: Child) {
        showConfirmationPopup(
            'Remove Child',
            `Are you sure you want to remove ${children.firstName}?`,
            'Remove',
            <img src={removeIcon} alt='remove icon' style={{ height: '15px', width: '13.33px' }} />,
            () => {
                enableLoader();
                const updatedChildren = allChildren.filter((child) => child.id !== children.id);
                const payload = {
                    ...sessionInfo.data,
                    children: updatedChildren,
                };
                dispatch(updateSessionInfo({ payload: payload })).finally(() => {
                    disableLoader();
                });
            }
        );
        return;
    }

    function onSaveClicked() {
        enableLoader();
        const selectedIndex =
            selectedChildren !== null ? allChildren.indexOf(selectedChildren) : -1;
        const children = [...sessionInfo.data['children']];

        const { isoDateString, age } = getDateStringAndAge(
            Number(selectedYear),
            Number(selectedMonth)
        );

        if (selectedIndex === -1) {
            children.push({
                aboutMe: aboutMe,
                ageInYears: age,
                dateOfBirth: isoDateString,
                firstName: firstName,
                gender: selectedGender,
                hasAllergies: selectedAllergies,
                allergyDetails: selectedAllergies ? selectedAboutAllergies : '',
            });
        } else {
            children[selectedIndex] = {
                aboutMe: aboutMe,
                ageInYears: age,
                allergyDetails: selectedAllergies ? selectedAboutAllergies : '',
                dateOfBirth: isoDateString,
                firstName: firstName,
                gender: selectedGender,
                hasAllergies: selectedAllergies,
                id: selectedChildren.id,
            };
        }
        const payload = {
            ...sessionInfo.data,
            children: children,
        };
        dispatch(updateSessionInfo({ payload: payload })).finally(() => {
            disableLoader();
            setAddEditVisible(false);
        });
    }

    return (
        <div style={{ maxWidth: '750px', height: '100%', padding: '20px', paddingTop: isMobile && "0px" }}>
            <ConfirmationPopupRed confirmationProps={confirmationProps} />
            <Dialog
                visible={addEditVisible}
                onHide={() => setAddEditVisible(false)}
                className='flex justify-content-center align-items-center'
                content={
                    <div className='w-full flex justify-content-center align-items-center '>
                        <div
                            className='bg-white p-5 relative flex flex-column overflow-auto'
                            style={{
                                borderRadius: '33px',
                                color: '#585858',
                            }}
                        >
                            <div className='flex align-items-center justify-content-between mb-2'>
                                <h2
                                    className='m-0 p-0'
                                    style={{
                                        fontWeight: '700',
                                        fontSize: '25px',
                                    }}
                                >
                                    {selectedChildren !== null ? 'Edit' : 'Add'} Your Child
                                </h2>
                                <div className='flex align-items-center'>
                                    <div
                                        className='pointer-events-none'
                                        style={{
                                            border: '1px solid #787777',
                                            borderRadius: '30px',
                                            padding: '2px 13px',
                                            fontWeight: '700',
                                            fontSize: '16px',
                                            color: '#585858',
                                            textTransform: 'capitalize',
                                        }}
                                    >
                                        Child{' '}
                                        {selectedChildren !== null && allChildren.length > 0
                                            ? utils
                                                .numberToWords(
                                                    allChildren.indexOf(selectedChildren) + 1
                                                )
                                                .toLowerCase()
                                            : utils
                                                .numberToWords(allChildren.length + 1)
                                                .toLowerCase()}
                                    </div>
                                    <IoIosClose
                                        className='cursor-pointer'
                                        style={{ fontSize: '30px' }}
                                        onClick={() => setAddEditVisible(false)}
                                    />
                                </div>
                            </div>
                            <div
                                style={{
                                    height: '3px',
                                    width: '100%',
                                    backgroundColor: '#F1F1F1',
                                }}
                            />
                            <h4
                                className='m-0 p-0 mt-3'
                                style={{
                                    fontWeight: '700',
                                    fontSize: '16px',
                                }}
                            >
                                First Name
                            </h4>

                            <div className='pr-8'>
                                <FloatingLabelInput
                                    label='First Name'
                                    value={firstName}
                                    onlyText
                                    onChange={(val) => setFirstName(val)}
                                />
                            </div>
                            <h4
                                className='m-0 p-0 my-2'
                                style={{
                                    fontWeight: '700',
                                    fontSize: '16px',
                                }}
                            >
                                Date of Birth
                            </h4>

                            <div className='flex gap-2'>
                                <select
                                    value={selectedYear}
                                    onChange={handleYearChange}
                                    style={{
                                        height: '56px',
                                        width: '86px',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        border:
                                            selectedYear !== ''
                                                ? '2px solid #179d52'
                                                : '2px solid #f0f4f7',
                                        outline: 'none',
                                        backgroundColor: '#f0f4f7',
                                        color: '#585858',
                                        textAlign: 'center',
                                        fontWeight: '400',
                                        fontSize: '16px',

                                    }}
                                >
                                    <option value='' disabled>
                                        YYYY
                                    </option>
                                    {availableYears.map((year) => (
                                        <option key={year} value={year}>
                                            {year}
                                        </option>
                                    ))}
                                </select>
                                <select
                                    value={selectedMonth}
                                    onChange={handleMonthChange}
                                    disabled={!selectedYear}
                                    style={{
                                        height: '56px',
                                        width: '120px',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        border:
                                            selectedMonth !== ''
                                                ? '2px solid #179d52'
                                                : '2px solid #f0f4f7',
                                        outline: 'none',
                                        backgroundColor: '#f0f4f7',
                                        color: '#585858',
                                        textAlign: 'center',
                                        fontWeight: '400',
                                        fontSize: '16px',
                                        maxWidth: '120px',
                                    }}
                                >
                                    <option value='' disabled>
                                        Month
                                    </option>
                                    {availableMonths.map((month) => (
                                        <option key={month.value} value={month.value}>
                                            {month.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <p
                                className='m-0 p-0 mt-2'
                                style={{
                                    fontSize: '14px',
                                    fontWeight: '400',
                                    color: '#585858',
                                }}
                            >
                                Note: Only your child's age is display, not their birth month.
                            </p>
                            <h4
                                className='p-0'
                                style={{
                                    fontWeight: '700',
                                    fontSize: '16px',
                                    margin: '0',
                                    marginBlock: '10px',
                                }}
                            >
                                Gender
                            </h4>
                            <div className='flex gap-2'>
                                <RadioButton
                                    checked={selectedGender === 2}
                                    value={2}
                                    label='Female'
                                    id='gender'
                                    onChange={(value) => setSelectedGender(value)}
                                />
                                <RadioButton
                                    checked={selectedGender === 1}
                                    value={1}
                                    label='Male'
                                    id='gender'
                                    onChange={(value) => setSelectedGender(value)}
                                />
                                <RadioButton
                                    checked={selectedGender === 0}
                                    value={0}
                                    label='Prefer not to say'
                                    id='gender'
                                    onChange={(value) => setSelectedGender(value)}
                                />
                            </div>
                            <h4
                                className='p-0'
                                style={{
                                    fontWeight: '700',
                                    fontSize: '16px',
                                    margin: '0',
                                    marginBlock: '10px',
                                }}
                            >
                                Allergies
                            </h4>
                            <div className='flex gap-2'>
                                <RadioButton
                                    checked={selectedAllergies}
                                    value={1}
                                    label='Yes'
                                    id='allergies'
                                    onChange={(value) => setSelectedAllergies(Boolean(value))}
                                />
                                <RadioButton
                                    checked={!selectedAllergies}
                                    value={0}
                                    label='No'
                                    id='allergies'
                                    onChange={(value) => setSelectedAllergies(Boolean(value))}
                                />
                            </div>
                            {selectedAllergies && (
                                <textarea
                                    className={`${styles.placeholderStyle} mt-3`}
                                    value={selectedAboutAllergies}
                                    style={{
                                        height: '74px',
                                        resize: 'none',
                                        borderRadius: '10px',
                                        border:
                                            selectedAboutAllergies.length > 0
                                                ? '2px solid #179d52'
                                                : '2px solid #179d52',
                                        backgroundColor: '#f0f4f7',
                                        padding: '5px 10px',
                                        outline: 'none',
                                        color: '#585858',
                                    }}
                                    placeholder="What does your babysitter need to know about your child's allergies?"
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        const wordCount = value
                                            .trim()
                                            .split(/\s+/)
                                            .filter((word) => word.length > 0).length;

                                        if (wordCount > 0 || value.trim().length === 0) {
                                            setSelectedAboutAllergies(
                                                value.trim().length > 0 ? value : ''
                                            );
                                        }
                                    }}
                                />
                            )}
                            <h4
                                className='p-0'
                                style={{
                                    fontWeight: '700',
                                    fontSize: '16px',
                                    margin: '0',
                                    marginBlock: '10px',
                                }}
                            >
                                A brief intro about your child
                            </h4>
                            <textarea
                                className={`${styles.placeholderStyle}`}
                                value={aboutMe}
                                style={{
                                    height: '74px',
                                    resize: 'none',
                                    borderRadius: '10px',
                                    border:
                                        aboutMe.length > 0
                                            ? '2px solid #179d52'
                                            : '2px solid #f0f4f7',
                                    backgroundColor: '#f0f4f7',
                                    padding: '5px 10px',
                                    outline: 'none',
                                    color: '#585858',
                                }}
                                placeholder='Tell us something special about your child'
                                onChange={(e) => {
                                    const value = e.target.value;
                                    const wordCount = value
                                        .trim()
                                        .split(/\s+/)
                                        .filter((word) => word.length > 0).length;

                                    if (wordCount > 0 || value.trim().length === 0) {
                                        setAboutMe(value.trim().length > 0 ? value : '');
                                    }
                                }}
                            />
                            <div className='py-3 flex flex-row-reverse'>
                                <button
                                    className={`border-none px-5 py-2 border-round ${saveEnabled ? 'cursor-pointer shadow-4' : ''
                                        }`}
                                    style={{
                                        color: saveEnabled ? '#FFFFFF' : 'rgba(88, 88, 88, 0.3)',
                                        fontWeight: saveEnabled ? '800' : '400',
                                        backgroundColor: saveEnabled ? '#FFA500' : '#F0F4F7',
                                        width: '156px',
                                        height: '39px',
                                    }}
                                    onClick={() => {
                                        if (saveEnabled) {
                                            onSaveClicked();
                                        }
                                    }}
                                >
                                    Save
                                </button>
                            </div>
                        </div>
                    </div>
                }
            />
            <h1
                className='p-0'
                style={{
                    fontSize: '24px',
                    color: '#585858',
                    fontWeight: '600',
                    marginTop: "0px"
                }}
            >
                My Children
            </h1>
            <Divider />
            <p
                style={{
                    fontSize: '16px',
                    fontWeight: '400',
                    color: '#585858',
                }}
            >
                It is essential Juggle Street helpers know the number of children you have and how
                old they are. We ask for the birth month and year so your child’s age is always
                up-to-date. Only your child’s age is displayed, not the birth month.
            </p>
            <div className='flex flex-column gap-2'>
                {allChildren.map((children, index) => {
                    const date = new Date(children.dateOfBirth);
                    const formattedData = date.toLocaleDateString('en-us', {
                        year: 'numeric',
                        month: 'long',
                    });
                    return (
                        <div key={JSON.stringify(children)}>
                            <div
                                className='m-0 p-0 flex align-items-center gap-2 pb-2'
                                style={{
                                    fontSize: '1rem',
                                }}
                            >
                                <p
                                    className='m-0 p-0'
                                    style={{
                                        color: '#585858',
                                        fontWeight: '600',
                                    }}
                                >
                                    Child {utils.numberToWords(index + 1)}
                                </p>
                                <div
                                    className='flex justify-content-center align-self-center'
                                    style={{
                                        backgroundColor: '#179D52',
                                        borderRadius: '50%',
                                        color: 'white',
                                        padding: '3px',
                                        fontSize: '0.7rem',
                                    }}
                                >
                                    <FaCheck />
                                </div>
                            </div>
                            <div className='flex align-items-center gap-2'>
                                <div
                                    className='flex px-4 py-2 align-items-center flex-grow-1'
                                    style={{
                                        border: '2px solid #179D52',
                                        borderRadius: '20px',
                                        color: '#585858',
                                        backgroundColor: '#F0F4F7',
                                    }}
                                >
                                    <div className='flex flex-column flex-grow-1'>
                                        <h3 className='m-0 p-0 font-semibold'>
                                            {children.firstName}
                                        </h3>
                                        <p
                                            className='m-0 p-0'
                                            style={{
                                                fontSize: '14px',
                                                fontWeight: '500',
                                                lineHeight: '21px',
                                            }}
                                        >
                                            {formattedData}
                                        </p>
                                    </div>
                                    <div
                                        className='px-3 py-1 flex align-items-center gap-2 cursor-pointer'
                                        style={{
                                            color: '#ffffff',
                                            backgroundColor: '#FFA500',
                                            borderRadius: '10px',
                                            fontWeight: '800',
                                        }}
                                        onClick={() => handleAddEditClicked(children)}
                                    >
                                        <FiEdit3 />
                                        <p
                                            className='p-0 m-0'
                                            style={{
                                                fontSize: '12px',
                                            }}
                                        >
                                            Edit
                                        </p>
                                    </div>
                                </div>
                                <div>
                                    <div
                                        className='px-3 py-2 cursor-pointer flex align-items-center gap-2'
                                        style={{
                                            borderRadius: '10px',
                                            border: '1px solid #FF6359',
                                            backgroundColor: 'rgba(255, 99, 89, 0.3)',
                                            color: '#FF6359',
                                            fontWeight: '500',
                                            fontSize: '0.8rem',
                                        }}
                                        onClick={() => handleRemoveClicked(children)}
                                    >
                                        <img
                                            src={removeIcon}
                                            alt='Remove'
                                            width='13.33px'
                                            height='15px'
                                        />
                                        Remove
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
            {allChildren.length > 0 && <Divider className='my-3' />}
            <div
                className='w-full px-4 py-2 flex align-items-center cursor-pointer'
                style={{
                    border: '2px solid #585858',
                    color: '#585858',
                    backgroundColor: '#F0F4F7',
                    borderRadius: '20px',
                }}
                onClick={() => handleAddEditClicked(null)}
            >
                <div className='flex flex-column flex-grow-1'>
                    <h3
                        className='m-0 p-0'
                        style={{
                            fontWeight: '600',
                            fontSize: '20px',
                        }}
                    >
                        Add Child {utils.numberToWords(allChildren.length + 1)}
                    </h3>
                    <p
                        className='p-0'
                        style={{
                            fontWeight: '300',
                            fontSize: '12px',
                            margin: '0',
                            marginBottom: '5px',
                        }}
                    >
                        Add information about your child
                    </p>
                </div>
                <div>
                    <div
                        className='flex justify-content-center align-items-center p-1'
                        style={{
                            border: '1px solid #585858',
                            borderRadius: '50%',
                            backgroundColor: '#FFFFFF',
                        }}
                    >
                        <FaPlus />
                    </div>
                </div>
            </div>
            <Divider className='mt-3' />
        </div>
    );
}

export default MyChildren;
