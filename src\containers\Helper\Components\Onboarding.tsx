import { useSearchParams } from "react-router-dom";
import SidePannel from "./SidePannel";

const Onboarding = () => {
    const [searchParams] = useSearchParams();
    const activeIndex = Number(searchParams.get('activeTab') ?? 1);
    return (
        <div style={{ width: '100%', height: '100vh' }}>
            <SidePannel activeindex={activeIndex} />

        </div>
    )
}

export default Onboarding
