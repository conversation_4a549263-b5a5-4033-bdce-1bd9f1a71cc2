import { Outlet, useNavi<PERSON>, useParams, useSearchParams } from "react-router-dom";
import styles from "./jobLayout.module.css";
import { JobManagementScreensArray, JobManagerProvider, PayloadTemplate } from "../provider/JobManagerProvider";
import HelpdeskManager from "../../../../commonComponents/HelpdeskManager";
import { useEffect, useMemo, useState } from "react";
import useIsMobile from "../../../../hooks/useIsMobile";
import Timeline from "../Components/Timeline/Timeline";
import Service from "../../../../services/services";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { IframeBridge } from "../../../../services/IframeBridge";
import { AppDispatch, RootState } from "../../../../store";
import { useDispatch, useSelector } from "react-redux";
import { updateShowChatBox } from "../../../../store/slices/applicationSlice";

type Params = {
  action: string;
};

const defaultPayload = (jobid: number) => ({
  jobStartTime: null,
  jobEndTime: null,
  paymentType: null,
  jobType: null,
  helperPaymentMethod: null,
  jobDeliveryMethod: null,
  willPayOvertime: null,
  daysOfWeek: [
    { dayOfWeek: 1, isRequired: false },
    { dayOfWeek: 2, isRequired: false },
    { dayOfWeek: 3, isRequired: false },
    { dayOfWeek: 4, isRequired: false },
    { dayOfWeek: 5, isRequired: false },
    { dayOfWeek: 6, isRequired: false },
    { dayOfWeek: 0, isRequired: false },
  ],
  weeklySchedule: {
    scheduleName: "default",
    isActive: 1,
    weeklyScheduleEntries: [],
  },
  jobSettings: {},
  userPaymentType: "",
  actionType: c.jobActionType.POST,
  isUpsellEnabled: false,
  jobDate: null,
  addressId: null,
  longitude: null,
  latitude: null,
  managedBy: null,
  jobSubType: 0,
  specialInstructions: null,
  price: null,
  overtimeRate: null,
  cardSelected: null,
  applicants: [],
  applicantFilters: null,
  deviceType: c.deviceType.DESKTOP,
  jobDateDay: null,
  jobDateMonth: null,
  jobDateYear: null,
  duration: null,
  durationType: null,
  metadata: null,
  durationToStart: null,
  jobStartDurationType: null,
  isPriceNegotiable: null,
  jobStartType: "1",
  jobFinishType: "2",
  selectedCandidates: null,
  relatedJobId: jobid,
  repostCount: 0,
});

function jobLayout() {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const { isMobile } = useIsMobile();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { action } = useParams<Params>();
  const [searchparams] = useSearchParams();
  // const jobId = Number(searchparams.get('jobId'));
  if (!action || (action !== "post" && isNaN(Number(action)))) {
    throw new Error("Invalid action parameter in URL");
  }
  useEffect(() => {
    if (isMobile) {
      dispatch(updateShowChatBox(false));
    }
  }, [isMobile, dispatch]);
  useEffect(() => {
    if (searchparams.has("jobaction")) {
      setIsRefreshing(true);
      if (searchparams.get("jobaction") === "Edit") {
        processEditJob();
      } else if (searchparams.get("jobaction") === "Duplicate") {
        processDuplicateJob();
      }  else if (searchparams.get("jobaction") === "fillJob") {
      processFillJob(); // Add this new condition
    }
      return;
    }
    if (!JobManagementScreensArray.some((screen) => window.location.hash.includes(screen))) {
      navigate(`job-type?${searchparams.toString()}`, { replace: true });
    }
  }, []);

  function calculateTimeDifference(time1: string, time2: string): number {
    const parseTime = (time: string) => {
      const [hours, minutes, seconds] = time.split(":").map(Number);
      return new Date(1970, 0, 1, hours, minutes, seconds);
    };
    const date1 = parseTime(time1);
    const date2 = parseTime(time2);
    const differenceInMs = Math.abs(date2.getTime() - date1.getTime());
    const differenceInHours = differenceInMs / (1000 * 60 * 60);
    return differenceInHours;
  }
async function processFillJob() {
  const data = await new Promise<PayloadTemplate>((res, rej) => {
    Service.jobClientDetails(res, rej, Number(action));
  });

  const weeklySchedule: PayloadTemplate["weeklySchedule"] = ![1, 256].includes(data.jobType)
    ? {
        isActive: data.weeklySchedule.isActive,
        scheduleName: data.weeklySchedule.scheduleName,
        weeklyScheduleEntries: [],
      }
    : null;

  const daysOfWeek = Array(7)
    .fill(0)
    .map((_, i) => ({ dayOfWeek: i, isRequired: false }));

  if (![1, 256].includes(data.jobType)) {
    const weekEntriesByDay: {
      [key: number]: Array<PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0] & { applicantId?: number | null }>;
    } = {};

    // Group entries by dayOfWeek
    data.weeklySchedule.weeklyScheduleEntries.forEach((v) => {
      if (!weekEntriesByDay[v.dayOfWeek]) {
        weekEntriesByDay[v.dayOfWeek] = [];
      }
      weekEntriesByDay[v.dayOfWeek].push(v);
    });

    // Set daysOfWeek and process entries
    const weekAdded: number[] = [];

    Object.keys(weekEntriesByDay).forEach((day) => {
      const dayOfWeek = Number(day);
      let entries = weekEntriesByDay[dayOfWeek];

      // Count shifts with non-null applicantId
      const nonNullApplicantIds = entries.filter((v) => v.applicantId !== null).length;
      let filteredEntries: typeof entries = [];

      // Scenario 1: If all shifts on this day have non-null applicantId, exclude the entire day
      if (nonNullApplicantIds === entries.length) {
        daysOfWeek[dayOfWeek] = { dayOfWeek, isRequired: false };
        filteredEntries = []; // No entries are included for this day
      }
      // Scenario 2: If some shifts have non-null applicantId, keep only shifts with null applicantId
      else if (nonNullApplicantIds > 0) {
        daysOfWeek[dayOfWeek] = { dayOfWeek, isRequired: true }; // Day is required since some shifts are available
        filteredEntries = entries.filter((v) => v.applicantId === null); // Keep only null applicantId shifts
      }
      // Scenario 3: If no shifts have non-null applicantId, keep all shifts
      else {
        daysOfWeek[dayOfWeek] = { dayOfWeek, isRequired: true }; // Day is required
        filteredEntries = entries; // Keep all entries
      }

      filteredEntries.forEach((v) => {
        const entryIsRequired = v.applicantId !== null ? false : v.isRequired;

        if (weekAdded.includes(v.dayOfWeek)) {
          const entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = weeklySchedule.weeklyScheduleEntries.map((s) => {
            if (s.dayOfWeek === v.dayOfWeek) {
              return {
                dayOfWeek: s.dayOfWeek,
                hourlyPrice: String(s.price / calculateTimeDifference(s.jobStartTime, s.jobEndTime)),
                price: s.price,
                isRequired: entryIsRequired,
                jobStartTime: s.jobStartTime,
                jobEndTime: s.jobEndTime,
                shiftType: s.shiftType,
                shifts: [
                  ...s.shifts,
                  {
                    hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                    isRequired: v.applicantId !== null ? false : v.isRequired,
                    jobEndTime: v.jobEndTime,
                    jobStartTime: v.jobStartTime,
                    price: v.price,
                    shiftType: v.shiftType,
                    applicantId: v.applicantId,
                  },
                ],
              };
            }
            return s;
          });
          weeklySchedule.weeklyScheduleEntries = entries;
          return;
        }

        weekAdded.push(v.dayOfWeek);
        weeklySchedule.weeklyScheduleEntries = [
          ...weeklySchedule.weeklyScheduleEntries,
          {
            dayOfWeek: v.dayOfWeek,
            hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
            isRequired: entryIsRequired,
            jobEndTime: v.jobEndTime,
            jobStartTime: v.jobStartTime,
            price: v.price,
            shiftType: v.shiftType,
            shifts: [
              {
                hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                isRequired: v.applicantId !== null ? false : v.isRequired,
                jobEndTime: v.jobEndTime,
                jobStartTime: v.jobStartTime,
                price: v.price,
                shiftType: v.shiftType,
                applicantId: v.applicantId,
              },
            ],
          },
        ];
      });
    });
  }

  // Directly create the final payload object without using state
  const validKeys = Object.keys(defaultPayload(data.id)) as (keyof PayloadTemplate)[];

  const filteredData = Object.keys(data)
    .filter((key): key is keyof PayloadTemplate => validKeys.includes(key as keyof PayloadTemplate))
    .reduce((obj, key) => {
      return {
        ...obj,
        [key]: data[key],
      };
    }, {} as Partial<PayloadTemplate>);

  const payload = {
    ...filteredData,
    daysOfWeek,
    weeklySchedule: weeklySchedule,
    actionType: c.jobActionType.POST,
    relatedJobId: data.id,
   jobDate: data.jobDate, // Preserve the jobDate from the input response
    isJobFlexible: data.isJobFlexible,
  };

  sessionStorage.setItem("jobManagement", JSON.stringify({ payload }));
  setIsRefreshing(false);
}
  async function processEditJob() {
    const data = await new Promise<PayloadTemplate>((resolve, reject) => {
      Service.jobClientDetails(resolve, reject, Number(action));
    });

    if (!data) {
      console.error("Data is undefined or null");
      return;
    }

    const weeklySchedule: PayloadTemplate["weeklySchedule"] = ![1, 256].includes(data.jobType)
      ? {
          isActive: data.weeklySchedule.isActive,
          scheduleName: data.weeklySchedule.scheduleName,
          weeklyScheduleEntries: [],
        }
      : {
          isActive: 0, // Default value for isActive
          scheduleName: "default", // Default schedule name
          weeklyScheduleEntries: [], // Ensure weeklyScheduleEntries is an empty array
        };

    const daysOfWeek = Array(7)
      .fill(0)
      .map((_, i) => ({ dayOfWeek: i, isRequired: false }));

    if (![1, 256].includes(data.jobType)) {
      data.weeklySchedule.weeklyScheduleEntries.forEach((v) => {
        daysOfWeek[v.dayOfWeek] = { dayOfWeek: v.dayOfWeek, isRequired: true };
      });

      const weekAdded: number[] = [];

      data.weeklySchedule.weeklyScheduleEntries.forEach((v) => {
        if (weekAdded.includes(v.dayOfWeek)) {
          const entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = weeklySchedule.weeklyScheduleEntries.map((s) => {
            if (s.dayOfWeek === v.dayOfWeek) {
              return {
                dayOfWeek: s.dayOfWeek,
                hourlyPrice: String(s.price / calculateTimeDifference(s.jobStartTime, s.jobEndTime)),
                price: s.price,
                isRequired: s.isRequired,
                jobStartTime: s.jobStartTime,
                jobEndTime: s.jobEndTime,
                shiftType: s.shiftType,
                shifts: [
                  ...s.shifts,
                  {
                    hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                    isRequired: s.isRequired,
                    jobEndTime: v.jobEndTime,
                    jobStartTime: v.jobStartTime,
                    price: v.price,
                    shiftType: v.shiftType,
                  },
                ],
              };
            }
            return s;
          });
          weeklySchedule.weeklyScheduleEntries = entries;
          return;
        }

        weekAdded.push(v.dayOfWeek);
        weeklySchedule.weeklyScheduleEntries = [
          ...weeklySchedule.weeklyScheduleEntries,
          {
            dayOfWeek: v.dayOfWeek,
            hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
            isRequired: v.isRequired,
            jobEndTime: v.jobEndTime,
            jobStartTime: v.jobStartTime,
            price: v.price,
            shiftType: v.shiftType,
            shifts: [
              {
                hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                isRequired: v.isRequired,
                jobEndTime: v.jobEndTime,
                jobStartTime: v.jobStartTime,
                price: v.price,
                shiftType: v.shiftType,
              },
            ],
          },
        ];
      });
    }

    const updatedWeeklySchedule = {
      ...weeklySchedule,
      weeklyScheduleEntries: weeklySchedule?.weeklyScheduleEntries?.map((entry) => ({
        ...entry,
        hourlyPrice: entry.hourlyPrice.toString(),
        shifts: entry.shifts.map((shift) => ({
          ...shift,
          hourlyPrice: shift.hourlyPrice.toString(),
        })),
      })),
    };

    // Directly create the final payload object without using state
    const payload = {
      ...data,
      daysOfWeek,
      weeklySchedule: updatedWeeklySchedule,
      selectedCandidates: data.applicants.map((a) => ({
        applicantId: a.applicantId,
      })),
      actionType: c.jobActionType.EDIT_JOB,
      isJobFlexible: data.isJobFlexible,
    };
    sessionStorage.setItem("jobManagement", JSON.stringify({ payload }));
    setIsRefreshing(false);
  }

  async function processDuplicateJob() {
    const data = await new Promise<PayloadTemplate>((res, rej) => {
      Service.jobClientDetails(res, rej, Number(action));
    });

    const weeklySchedule: PayloadTemplate["weeklySchedule"] = ![1, 256].includes(data.jobType)
      ? {
          isActive: data.weeklySchedule.isActive,
          scheduleName: data.weeklySchedule.scheduleName,
          weeklyScheduleEntries: [],
        }
      : null;
    const daysOfWeek = Array(7)
      .fill(0)
      .map((_, i) => ({ dayOfWeek: i, isRequired: false }));
    if (![1, 256].includes(data.jobType)) {
      data.weeklySchedule.weeklyScheduleEntries.forEach((v) => {
        daysOfWeek[v.dayOfWeek] = { dayOfWeek: v.dayOfWeek, isRequired: true };
      });

      const weekAdded: number[] = [];

      data.weeklySchedule.weeklyScheduleEntries.forEach((v: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"][0]) => {
        if (weekAdded.includes(v.dayOfWeek)) {
          const entries: PayloadTemplate["weeklySchedule"]["weeklyScheduleEntries"] = weeklySchedule.weeklyScheduleEntries.map((s) => {
            if (s.dayOfWeek === v.dayOfWeek) {
              return {
                dayOfWeek: s.dayOfWeek,
                hourlyPrice: String(s.price / calculateTimeDifference(s.jobStartTime, s.jobEndTime)),
                price: s.price,
                isRequired: s.isRequired,
                jobStartTime: s.jobStartTime,
                jobEndTime: s.jobEndTime,
                shiftType: s.shiftType,
                shifts: [
                  ...s.shifts,
                  {
                    hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                    isRequired: s.isRequired,
                    jobEndTime: v.jobEndTime,
                    jobStartTime: v.jobStartTime,
                    price: v.price,
                    shiftType: v.shiftType,
                  },
                ],
              };
            }
            return s;
          });
          weeklySchedule.weeklyScheduleEntries = entries;
          return;
        }
        weekAdded.push(v.dayOfWeek);
        weeklySchedule.weeklyScheduleEntries = [
          ...weeklySchedule.weeklyScheduleEntries,
          {
            dayOfWeek: v.dayOfWeek,
            hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
            isRequired: v.isRequired,
            jobEndTime: v.jobEndTime,
            jobStartTime: v.jobStartTime,
            price: v.price,
            shifts: [
              {
                hourlyPrice: String(v.price / calculateTimeDifference(v.jobStartTime, v.jobEndTime)),
                isRequired: v.isRequired,
                jobEndTime: v.jobEndTime,
                jobStartTime: v.jobStartTime,
                price: v.price,
              },
            ],
          },
        ];
      });
    }

    // Directly create the final payload object without using state

    const validKeys = Object.keys(defaultPayload(data.id)) as (keyof PayloadTemplate)[];

    const filteredData = Object.keys(data)
      .filter((key): key is keyof PayloadTemplate => validKeys.includes(key as keyof PayloadTemplate))
      .reduce((obj, key) => {
        return {
          ...obj,
          [key]: data[key],
        };
      }, {} as Partial<PayloadTemplate>);

    const payload = {
      ...filteredData,
      daysOfWeek,
      weeklySchedule: weeklySchedule,

      actionType: c.jobActionType.POST,
      relatedJobId: data.id,
      jobDate: null,
      isJobFlexible: data.isJobFlexible,
    };
    sessionStorage.setItem("jobManagement", JSON.stringify({ payload }));
    setIsRefreshing(false);
  }

  if (isRefreshing) return null;

  return (
    <JobManagerProvider>
      {isMobile ? <LayoutMobile /> : <Layout />}
      <HelpdeskManager />
    </JobManagerProvider>
  );
}

export default jobLayout;

const Layout = () => {
  const [searchParams] = useSearchParams();
  const hashPath = window.location.hash;
  const isHidden = useMemo(() => ["job-type", "job-posting"].some((p) => hashPath.includes(p)), [hashPath]);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);
  const handleGoBack = () => {
    sessionStorage.removeItem("jobManagement");
    if (clientType === 1) {
      navigate("/parent-home");
    } else if (clientType === 2) {
      navigate("/business-home");
    } else {
      navigate("/");
    }
  };
  return (
    <div className={`w-screen h-screen ${styles.layout} overflow-hidden`}>
      <div className={`h-full ${styles.stepsArea} overflow-hidden`}>
        <div
          onClick={handleGoBack}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{
            position: "absolute",
            top: "30px",
            marginLeft: "18px",
            width: "40px",
            height: "40px",
            borderRadius: "50%",
            backgroundColor: "#fff",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            zIndex: 10,
          }}
        >
          <p className="pi pi-times text-green-700 "></p>
          {isHovered && (
            <div
              className="font-semibold absolute"
              style={{
                top: "2px",
                left: "147px",
                transform: "translateX(-50%)",
                backgroundColor: "#FFFFFF",
                color: "#179d52",
                padding: "8px 8px",
                borderRadius: "8px",
                fontSize: "12px",
                whiteSpace: "nowrap",
                zIndex: 20,
                boxShadow: "0 4px 4px 0 #00000040",
              }}
            >
              Exit back to "Helpers Near me"
            </div>
          )}
        </div>
        {isHidden ? (
          <div className="w-full h-full flex justify-content-center align-items-center">
            <p
              className="m-0 p-0"
              style={{
                fontSize: "50px",
                fontWeight: "700",
                color: "#FFFFFF",
              }}
            >
              {searchParams.has("jobaction") ? searchParams.get("jobaction") : "Post"} Job
            </p>
          </div>
        ) : (
          <Timeline />
        )}
      </div>
      <div className={`h-full ${styles.outletArea} overflow-hidden`}>
        <Outlet />
      </div>
    </div>
  );
};

const LayoutMobile = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const hashPath = window.location.hash;
  const isHidden = useMemo(() => ["job-type", "job-posting"].some((p) => hashPath.includes(p)), [hashPath]);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const handleGoBack = () => {
    sessionStorage.removeItem("jobManagement");
    IframeBridge.sendToParent({
      type: "goBack-postjob",
    });
    if (!inIframe) {
      navigate("/");
    }
  };
  useEffect(() => {
    IframeBridge.sendToParent({
      type: "hideChatBox",
      data: isHidden,
    });
  }, [isHidden]);
  return (
    <div className={`w-screen h-screen ${styles.layoutMobile} overflow-hidden`}>
      <div
        className={`${styles.stepsAreaMobile} overflow-hidden flex flex-col align-items-center px-4`}
        data-expanded={!isHidden && hashPath.includes("jobpricing-step1")}
      >
        {isHidden ? (
          <>
            <div
              id="back-button-portal"
              style={{
                width: "30px",
                height: "30px",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                zIndex: 10,
              }}
            />
            <div className="w-full flex justify-content-center align-items-center">
              <p
                className="m-0 p-0"
                style={{
                  fontSize: "22px",
                  fontWeight: "800",
                  color: "#FFFFFF",
                }}
              >
                {searchParams.has("jobaction") ? searchParams.get("jobaction") : "Post"} Job
              </p>
            </div>
            <div
              onClick={handleGoBack}
              style={{
                minWidth: "30px",
                minHeight: "30px",
                maxWidth: "30px",
                maxHeight: "30px",
                borderRadius: "50%",
                backgroundColor: "#fff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                zIndex: 10,
              }}
            >
              <p className="pi pi-times text-green-700"></p>
            </div>
          </>
        ) : (
          <Timeline />
        )}
      </div>
      <div className={`w-full ${styles.outletAreaMobile} overflow-hidden`}>
        <Outlet />
      </div>
    </div>
  );
};
