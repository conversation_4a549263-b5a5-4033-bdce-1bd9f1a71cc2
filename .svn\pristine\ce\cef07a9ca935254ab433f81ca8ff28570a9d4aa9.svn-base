import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import "../../../components/utils/util.css";
import CustomButton from '../../../commonComponents/CustomButton';
import { decrementProfileActivationStep, incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import OutlineButton from '../../../commonComponents/OutlineButton';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import { InputTextarea } from 'primereact/inputtextarea';
import useLoader from '../../../hooks/LoaderHook';
import { SiHyperskill } from 'react-icons/si';
import { IoSchool } from 'react-icons/io5';
import ProfileCompletenessHeader from '../Components/ProfileCompletenessHeader';
import useIsMobile from '../../../hooks/useIsMobile';

const HighSchool = () => {
  const dispatch = useDispatch<AppDispatch>();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const [ageGroups, setAgeGroups] = useState(sessionInfo.data["provider"]["highSchoolYears"]);
  const [highSchoolSubjects, setHighSchoolSubjects] = useState(sessionInfo.data["provider"]["highSchoolSubjects"]);
  const [canTeachStudySkills, setCanTeachStudySkills] = useState<boolean | null>(sessionInfo.data["provider"]["canTeachStudySkills"] || null);
  const [canTeachExamPreparation, setCanTeachExamPreparation] = useState<boolean | null>(sessionInfo.data["provider"]["canTeachExamPreparation"] || null);
  const [myExperience, setMyExperience] = useState<string>(sessionInfo.data["provider"]["myExperience3"] || '');
  const [experienceError, setExperienceError] = useState<boolean>(false);
  const [ageGroupError, setAgeGroupError] = useState<boolean>(false);
  const [graduationYear, setGraduationYear] = useState<number | null>(sessionInfo.data["provider"]["year12GraduationYear"] || null);
  const [graduationYearError, setGraduationYearError] = useState<boolean>(false);
  const [subjectError, setSubjectError] = useState<boolean>(false);
  const [highschoolsubjectError, setHighschoolsubjectError] = useState<boolean>(false);
  const {isMobile}=useIsMobile()
  const [studySkillsError, setStudySkillsError] = useState<boolean>(false);
  const [examPreparationError, setExamPreparationError] = useState<boolean>(false);
  const { disableLoader, enableLoader } = useLoader();
  const [hasChanges, setHasChanges] = useState(false);
  const minCharLimit = 100;

  // Toggle age group selection
  useEffect(() => {
    setHighSchoolSubjects(highSchoolSubjects.map(group => ({
      ...group,
      isOpen: group.text === "Academic",
      children: group.children.map(subject => ({
        ...subject,
        isOpen: group.text === "Academic" && subject.text === "English",
        children: subject.children || []
      }))
    })));
  }, []);


  const toggleAgeGroup = (index: number) => {
    setAgeGroups(ageGroups.map((group, i) => ({
      ...group,
      selected: i === index ? !group.selected : group.selected,
    })));
    setAgeGroupError(false);
    setHasChanges(true);
  };

  const toggleGroup = (groupIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => ({
      ...group,
      // Close all other groups when opening a new one
      isOpen: i === groupIndex ? !group.isOpen : false,
      children: group.children.map(subject => ({
        ...subject,
        // Close all subjects when closing a group
        isOpen: i === groupIndex ? subject.isOpen : false
      }))
    })));
    setHasChanges(true);
  };


  const toggleSubject = (groupIndex: number, subjectIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => {
      if (group.text === "Academic") {
        // For Academic group, handle opening/closing of subjects
        if (i === groupIndex) {
          return {
            ...group,
            children: group.children.map((subject, j) => ({
              ...subject,
              isOpen: j === subjectIndex ? !subject.isOpen : false
            }))
          };
        }
        return group;
      } else {
        // For non-Academic groups, handle checkbox selection
        if (i === groupIndex) {
          return {
            ...group,
            children: group.children.map((subject, j) => ({
              ...subject,
              selected: j === subjectIndex ? !subject.selected : subject.selected
            }))
          };
        }
        return group;
      }
    }));
    setSubjectError(false);
    setHasChanges(true);
  };

  const toggleSubSubject = (groupIndex: number, subjectIndex: number, subSubjectIndex: number) => {
    setHighSchoolSubjects(highSchoolSubjects.map((group, i) => {
      if (group.text === "Academic" && i === groupIndex) {
        return {
          ...group,
          children: group.children.map((subject, j) => {
            if (j === subjectIndex) {
              return {
                ...subject,
                children: subject.children.map((subSubject, k) => ({
                  ...subSubject,
                  selected: k === subSubjectIndex ? !subSubject.selected : subSubject.selected,
                })),
              };
            }
            return subject;
          }),
        };
      }
      return group;
    }));
    setSubjectError(false);
    setHasChanges(true);
  };

  const renderSubjects = (group, groupIndex) => {
    if (group.text === "Academic") {
      return group.children.map((subject, subjectIndex) => (
        <div key={subject.name} className="border-l-2 border-gray-200 pl-4">
          <div
            className="flex items-center gap-2 mb-1 cursor-pointer"
            onClick={() => toggleSubject(groupIndex, subjectIndex)}
          >
            <span
              className="transition-transform duration-200"
              style={{
                transform: subject.isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                color: '#179D52'
              }}
            >
              ▼
            </span>
            <span className="txt-clr font-semibold">{subject.text}</span>
          </div>
          {subject.isOpen && (
            <div className="space-y-2 ml-4">
              {subject.children.map((subSubject, subSubjectIndex) => (
                <label
                  key={subSubject.name}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={subSubject.selected || false}
                    onChange={() => toggleSubSubject(groupIndex, subjectIndex, subSubjectIndex)}
                    className={`${styles.customCheckbox} txt-clr cursor-pointer`}
                  />
                  <span className="txt-clr font-medium">{subSubject.text}</span>
                </label>
              ))}
            </div>
          )}
        </div>
      ));
    } else {
      // For non-Academic groups, render simple checkboxes
      return group.children.map((subject, subjectIndex) => (
        <label
          key={subject.name}
          className="flex items-center gap-2 cursor-pointer ml-4"
        >
          <input
            type="checkbox"
            checked={subject.selected || false}
            onChange={() => toggleSubject(groupIndex, subjectIndex)}
            className={`${styles.customCheckbox} txt-clr cursor-pointer`}
          />
          <span className="txt-clr font-medium">{subject.text}</span>
        </label>
      ));
    }
    setHasChanges(true);
  };

  const handleRadioChange = (setter: React.Dispatch<React.SetStateAction<boolean | null>>, value: boolean) => {
    setter(value);
    if (setter === setCanTeachStudySkills) {
      setStudySkillsError(false);
    } else if (setter === setCanTeachExamPreparation) {
      setExamPreparationError(false);
    }
    setHasChanges(true);
  };

  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMyExperience(event.target.value);
    setExperienceError(false);
    setHasChanges(true);
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setGraduationYear(parseInt(e.target.value, 10));
    setHasChanges(true);
    setGraduationYearError(false);
  };

  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = 1960; year <= currentYear; year++) {
      years.push(year);
    }
    return years;
    // setHasChanges(true);
  };

  const handleSkip = () => {
    dispatch(incrementProfileActivationStep());
  };

  const handleNext = async () => {
    let hasError = false;
    const selectedAgeGroups = ageGroups.some(group => group.selected);
    const selectedSubjects = highSchoolSubjects.some(group => group.children.some(subject => subject.selected));
    const selectedhighSchoolSubjects = highSchoolSubjects.some(group =>
      group.children?.some(child =>
        child.children?.some(subject => subject.selected) || child.selected // Handle cases where there are no children
      ) || group.selected // Check the group itself if no children are present
    );


    if (!selectedAgeGroups) {
      setAgeGroupError(true);
      hasError = true;
    }
    if (canTeachStudySkills === null) {
      setStudySkillsError(true);
      hasError = true;
    }
    if (!myExperience) {
      setExperienceError(true);
      hasError = true;
    }
    if (!graduationYear) {
      setGraduationYearError(true);
      hasError = true;
    }
    if (myExperience?.length < minCharLimit) {
      hasError = true;
    }
    if (!selectedhighSchoolSubjects) {
      setHighschoolsubjectError(true);
      hasError = true;
    } else {
      setHighschoolsubjectError(false);
    }
    if (canTeachExamPreparation === null) {
      setExamPreparationError(true);
      hasError = true;
    }
    if (hasError) {
      return;
    }
    const payload = {
      ...sessionInfo.data,
      provider: {
        ...sessionInfo.data["provider"],
        highSchoolYears: ageGroups,
        canTeachStudySkills: canTeachStudySkills,
        canTeachExamPreparation: canTeachExamPreparation,
        year12GraduationYear: graduationYear,
        highSchoolSubjects: highSchoolSubjects,
        myExperience3: myExperience
      },
    };
    enableLoader();
    try {
      await dispatch(updateSessionInfo({ payload }));
      dispatch(incrementProfileActivationStep());
    } finally {
      disableLoader();

    }
  };

  return (
    <div  className={`${styles.utilcontainer} ${isMobile ? "p-0" : ""}`}>
      <ProfileCompletenessHeader
        title="High School"
        profileCompleteness={sessionInfo.data['profileCompleteness']}
        loading={sessionInfo.loading}
        onBackClick={()=>dispatch(decrementProfileActivationStep())}
      />
      <div className='flex align-items-center justify-content-center'>
        <h1
          className="p-0 m-0 txt-clr font-bold line-height-1"
          style={{ fontSize: "24px" }}
        >
          High School
        </h1>
      </div>
      <div className=" p-4">
        <div className="mb-5">
          <div className='flex'>
            <h1 className="txt-clr font-medium mb-2 p-0 m-0"
              style={{ fontSize: "18px", color: ageGroupError ? 'red' : '#585858' }}
            >
              <span><SiHyperskill /></span>   Select age groups you work with
            </h1>
          </div>

          <div className="flex flex-wrap gap-2">
            {ageGroups.map((primarySchoolYears, index) => (
              <OutlineButton
                key={index}
                onClick={() => toggleAgeGroup(index)}
                style={{
                  fontSize: "14px",
                  border: primarySchoolYears.selected ? "2px solid #179D52" : "none",
                  paddingBlock: "15px",
                  fontWeight: primarySchoolYears.selected ? "700" : "500",
                  backgroundColor: primarySchoolYears.selected ? "#F0F4F7" : "#FFFFFF",
                }}
              >
                {primarySchoolYears.text}
              </OutlineButton>
            ))}
          </div>
        </div>

        <h1
          className="p-0 m-0 txt-clr font-medium line-height-1"
          style={{ fontSize: "18px", color: studySkillsError ? 'red' : '#585858' }}
        >
          Do you teach Study Skills
        </h1>
        <div className="flex justify-content-start items-center mt-1 gap-2">
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="teachStudySkills"
              value="yes"
              checked={canTeachStudySkills === true}
              onChange={() => handleRadioChange(setCanTeachStudySkills, true)}
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="teachStudySkills"
              value="no"
              checked={canTeachStudySkills === false}
              onChange={() => handleRadioChange(setCanTeachStudySkills, false)}
              className="cursor-pointer"
            />
            No
          </label>
        </div>
        <h1
          className="p-0 m-0 mt-3 txt-clr font-medium line-height-1"
          style={{ fontSize: "18px", color: examPreparationError ? 'red' : '#585858' }}
        >
          Do you teach Exam Preparation
        </h1>
        <div className="flex justify-content-start items-center mt-1 gap-2">
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="teachExamPreparation"
              value="yes"
              checked={canTeachExamPreparation === true}
              onChange={() => handleRadioChange(setCanTeachExamPreparation, true)}
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex justify-content-start items-center txt-clr cursor-pointer">
            <input
              type="radio"
              name="teachExamPreparation"
              value="no"
              checked={canTeachExamPreparation === false}
              onChange={() => handleRadioChange(setCanTeachExamPreparation, false)}
              className="cursor-pointer"
            />
            No
          </label>
        </div>
        <h1
          className="p-0 m-0 mt-3 txt-clr font-medium line-height-1"
          style={{ fontSize: "18px", color: graduationYearError ? 'red' : '#585858' }}
        >
          {`What year did you complete (or will complete) ${sessionInfo.data['country'] === 'au' ? 'Year 12' : 'Year 13'
            }?`}
        </h1>
        <div className="relative mt-2">
          <select
            value={graduationYear || ''}
            onChange={handleYearChange}
            className="appearance-none p-2 border border-gray-300 rounded cursor-pointer	"
          >
            <option value="" disabled style={{ color: '#585858' }}>Select Year ▼</option>
            {generateYears().map(year => (
              <option key={year} value={year}>{year} </option>
            ))}
          </select>
        </div>
      </div>
      <div className='flex'>
        <h1 className="flex flex-wrap gap-1 font-medium line-height-1 mt-2 p-0 m-0"
          style={{ fontSize: "18px", color: highschoolsubjectError ? 'red' : '#585858' }}>
          <span><IoSchool style={{ color: '#585858', fontSize: '18px' }} /></span>  High School Subjects
        </h1>
      </div>

      <div className="space-y-4 mt-1">
        {highSchoolSubjects.map((group, groupIndex) => (
          <div key={groupIndex} className="border-l-2 border-gray-200 pl-4">
            <div
              className="flex items-center gap-2 mb-1 cursor-pointer"
              onClick={() => toggleGroup(groupIndex)}
            >
              <span
                className="transition-transform duration-200"
                style={{
                  transform: group.isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  color: '#179D52'
                }}
              >
                ▼
              </span>
              <span className="txt-clr font-semibold">{group.text}</span>
            </div>
            {group.isOpen && renderSubjects(group, groupIndex)}
          </div>
        ))}
      </div>

      <div className='flex align-items-center justify-content-center'>
        <h1
          className="p-0 m-0 txt-clr font-bold line-height-1"
          style={{ fontSize: "24px" }}
        >
          Tutoring Experience
        </h1>
      </div>
      <h1 className="text-base font-medium p-0 m-0 pl-3 mt-2"
        style={{ fontSize: '18px', color: experienceError || myExperience.length < minCharLimit ? 'red' : '#585858' }}
      >
        Describe your Tutoring experience
      </h1>
      <div style={{marginBottom:isMobile && "20%"}}className={!isMobile ? "txt-clr mt-1 pl-3" : "txt-clr mt-1 px-3"}  >
        <InputTextarea
          autoResize
          value={myExperience}
          required
          onChange={handleTextareaChange}
          rows={3}
          cols={30}
          className={styles.inputTextareafamily}
          placeholder='How long have you been tutoring? What subjects are you passionate about? What academic qualifications do you have?
             What formal teaching experience do you have? Etc.'
        />
        <p
          style={{
            fontSize: '14px',
            color: myExperience.length < minCharLimit ? 'red' : 'green',
            fontWeight: '400',
          }}
        >
          {myExperience.length < minCharLimit &&
            `${minCharLimit - myExperience.length} characters remaining`}
        </p>
      </div>
      <footer className={ !isMobile ? `${styles.utilfooterContainer}` : `${styles.utilfooterContainerMobile}`}>
        <CustomButton
          label={
            <>
              <i className="pi pi-angle-left"></i>
              Previous
            </>
          }
          onClick={() => dispatch(decrementProfileActivationStep())}
          style={{
            backgroundColor: "transparent",
            color: "#585858",
            width: "156px",
            height: "39px",
            fontSize: "14px",
            fontWeight: "500",
             margin:isMobile && "5px"
          }}
        />
        <div style={{ flexGrow: 1 }} />
        <CustomButton
          className={styles.hoverClass}
          data-skip={hasChanges ? "false" : "true"}
          onClick={hasChanges ? handleNext : handleSkip}
          label={
            <>
              {hasChanges ? 'Next' : 'Skip'}
              <i
                className={`pi pi-angle-${hasChanges ? 'right' : 'right'}`}
                style={{ marginLeft: '8px' }}
              ></i>
            </>
          }
          style={
            hasChanges
              ? {
                backgroundColor: "#FFA500",
                color: "#fff",
                width: "156px",
                height: "39px",
                fontWeight: "800",
                fontSize: "14px",
                borderRadius: "8px",
                border: "2px solid transparent",
                boxShadow: "0px 4px 12px #00000",
                transition:
                  "background-color 0.3s ease, box-shadow 0.3s ease",
           margin : isMobile && "10px"
              }
              : {
                backgroundColor: "transparent",
                color: "#585858",
                width: "156px",
                height: "39px",
                fontWeight: "400",
                fontSize: "14px",
                borderRadius: "10px",
                border: "1px solid #F0F4F7",
           margin : isMobile && "10px"
              }
          }
        />

      </footer>
    </div>
  )
}

export default HighSchool