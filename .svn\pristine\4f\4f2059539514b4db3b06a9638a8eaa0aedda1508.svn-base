import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';
import CookiesConstant from '../helper/cookiesConst';
import utils from '../components/utils/util';

export interface AdjustedTimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  statusCode: number;
}

interface AdjustedTimesheetApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

export const useAdjustedTimesheetDetails = () => {
  const location = useLocation();
  const [adjustedTimesheetData, setAdjustedTimesheetData] = useState<AdjustedTimesheetEntry[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const isParent = clientType === c.clientType.INDIVIDUAL;
  const isBusiness = clientType === c.clientType.BUSINESS;
  const showApprove = isParent || isBusiness;
  const isHelper = clientType === c.clientType.UNSPECIFIED;

  const statusKey = showApprove
    ? c.ApprovalStatus.HELPER_ADJUSTED
    : c.ApprovalStatus.PARENT_ADJUSTED;

  const statusMap: { [key: number]: string } = {[statusKey]: showApprove ? "Awaiting Your Approval" : "Awaiting Your Confirmation"};
  // Check if current route is adjusted-timesheets
  const isAdjustedTimesheetsRoute = location.pathname.includes(showApprove ? '/helper-adjusted' : '/parent-adjusted') ||
    (isParent && location.pathname === '/parent-home/timesheet') || (isBusiness && location.pathname === '/business-home/timesheet') ||
    (isHelper && location.pathname === '/helper-home/timesheet');

  const fetchAdjustedTimesheetData = async (): Promise<void> => {
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getHelperAdjustedlist(
          (response: AdjustedTimesheetApiItem[]) => {
            console.log("API Response getTimeSheet for adjusted:", response);

            if (!Array.isArray(response)) {
              console.warn("Expected array response, got:", typeof response);
              setAdjustedTimesheetData([]);
              resolve();
              return;
            }
            const clientType = Number(utils.getCookie(CookiesConstant.clientType));
            const isParent = clientType === c.clientType.INDIVIDUAL;
            const isBusiness = clientType === c.clientType.BUSINESS;
            const isHelper = clientType === c.clientType.UNSPECIFIED;
            const showApprove = isParent || isBusiness;
            // Filter for adjusted timesheets (status 4: PARENT_ADJUSTED, status 5: HELPER_ADJUSTED)
            const filteredData = response.filter((item: AdjustedTimesheetApiItem) =>
              item.status === c.ApprovalStatus.PARENT_ADJUSTED ||
              item.status === c.ApprovalStatus.HELPER_ADJUSTED
            );
            console.log("Filtered adjusted timesheet data (status 4 or 5):", filteredData);
            const basePath = isParent
              ? '/parent-home/timesheet'
              : isBusiness
                ? '/business-home/timesheet'
                : isHelper
                  ? '/helper-home/timesheet'
                  : '/';
            const subRoute = showApprove
              ? 'helper-confirm'
              : 'awaiting-approval';
            const targetPath = `${basePath}/${subRoute}`;

            const mappedData: AdjustedTimesheetEntry[] = filteredData.map((item: AdjustedTimesheetApiItem) => ({
              id: item.id,
              status: statusMap[item.status] || "Adjusted Timesheet",
              statusCode: item.status,
              type: jobTypeMap[item.jobType] || "Unknown",
              date: new Date(item.jobDate).toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }),
              location: item.formattedAddress,
              userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
              originalImageUrl: item.originalImageUrl,
            }));
            // navigate(targetPath);
            setAdjustedTimesheetData(mappedData);
            resolve();
          },
          (error: any) => {
            setError(error?.message || 'Failed to fetch adjusted timesheet data');
            reject(error);
          }
        );
      });

    } catch (err) {
      setError('Failed to fetch adjusted timesheet data');
    } finally {
      disableLoader();
    }
  };

  useEffect(() => {
    // Only fetch data when on the adjusted-timesheets route
    if (isAdjustedTimesheetsRoute) {
      fetchAdjustedTimesheetData();
    } else {
    }
  }, [location.pathname]); // Trigger when route changes

  const refreshData = () => {
    fetchAdjustedTimesheetData();
  };

  const clearAdjustedTimesheetData = () => {
    setAdjustedTimesheetData([]);
    setError(null);
  };

  return {
    adjustedTimesheetData,
    error,
    refreshData,
    clearAdjustedTimesheetData
  };
};

export default useAdjustedTimesheetDetails;
