import { useEffect, useRef } from 'react';
import { SearchFilters } from '../store/types';
import { UserSearchResponse } from './SearchGeoSearchHook';
import useLoader from './LoaderHook';

type useProviderFilterProps = {
    defaultFilters: SearchFilters;
    onFilterChange: (filters: SearchFilters) => void;
    enableLoader: () => void;
    setSearchResponse: (response: UserSearchResponse) => void;
};

const useProviderFilter = ({ defaultFilters, onFilterChange }: useProviderFilterProps) => {
    const { enableLoader, disableLoader } = useLoader();
    useEffect(() => {
        enableLoader();
        onFilterChange(defaultFilters);
    }, []);
};

export default useProviderFilter;
