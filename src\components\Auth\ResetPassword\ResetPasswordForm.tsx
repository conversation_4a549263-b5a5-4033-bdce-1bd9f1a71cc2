import { InputText } from "primereact/inputtext";
import { useState } from "react";
import { validatePassword } from "../../utils/validation";
import styles from "../../../commonStyle/forgot-password-form.module.css";
import CustomButton from "../../../commonComponents/CustomButton";
import "../../utils/util.css";
import { useNavigate } from "react-router-dom";

interface ResetPasswordProps {
  onResetPassword: (
    otp: string,
    password: string,
    confirmPassword: string
  ) => void;
  formError?: string;
}
export const ResetPasswordForm: React.FC<ResetPasswordProps> = ({
  onResetPassword,
  formError,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, _] = useState(false);
  const navigate=useNavigate();
  const [formdata, setformdata] = useState({
    code: "",
    password: "",
    confirmPassword: "",
    codeErrorText: "",
    passwordErrorText: "",
    confirmPasswordErrorText: "",
  });
  const [passwordStrength, setPasswordStrength] = useState("");

  const handleChange = (event) => {
    const { name, value } = event.target;
    setformdata((prevState) => ({
      ...prevState,
      [name]: value,
      [`${name}ErrorText`]: "",
    }));
  };

  const handlePasswordChange = (event) => {
    const value = event.target.value;
    setformdata((prevState) => ({
      ...prevState,
      password: value,
    }));
    setPasswordStrength(checkPasswordStrength(value));
  };

  const checkPasswordStrength = (value) => {
    if (value.length < 6) {
      return "Too simple";
    } else if (value.length < 10) {
      return "Average complexity";
    } else {
      return "Complex password";
    }
  };

  const validate = () => {
    setformdata((prevState) => ({
      ...prevState,
      codeErrorText: "",
      passwordErrorText: "",
      confirmPasswordErrorText: "",
    }));

    const { code, password, confirmPassword } = formdata;
    let isValid = true;

    if (!code || code.length !== 6) {
      setformdata((prevState) => ({
        ...prevState,
        codeErrorText: !code
          ? "Please enter your 6 digit code"
          : "Please enter a valid code sent to your email",
      }));
      isValid = false;
    }

    const passwordError = validatePassword(password);
    if (passwordError) {
      setformdata((prevState) => ({
        ...prevState,
        passwordErrorText: passwordError,
      }));
      isValid = false;
    }

    if (password !== confirmPassword) {
      setformdata((prevState) => ({
        ...prevState,
        confirmPasswordErrorText: "Passwords do not match",
      }));
      isValid = false;
    }

    return isValid;
  };
  const handlePasswordBlur = () => {
    setPasswordStrength("");
  };

  const handleReset = (event) => {
    event.preventDefault();
    if (validate()) {
      setformdata({
        code: "",
        password: "",
        confirmPassword: "",
        codeErrorText: "",
        passwordErrorText: "",
        confirmPasswordErrorText: "",
      });
      onResetPassword(
        formdata.code,
        formdata.password,
        formdata.confirmPassword
      );
    }
  };

  return (
    <div>
      <form
        className="mt-3"
        onSubmit={handleReset}
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div className="p-mb-3 flex flex-column gap-2">
          <label className="imagelabel">
            <h2>Forgot Your Password?</h2>
          </label>
        </div>
        <div className="p-mb-3 flex flex-column gap-2">
          <p className={styles.instructionText}>
            Please enter the 6 digit reset code along with the new password.
            <br />
            <br />
          </p>
        </div>
        <div className="mb-3 flex flex-column gap-2">
          <div className="input-container" style={{ maxWidth: "100%" }}>
            <InputText
              id="code"
              type="text"
              name="code"
              value={formdata.code}
              onChange={(event) => {
                const value = event.target.value;

                if (/^\d*$/.test(value)) {
                  handleChange(event);
                }
              }}
              maxLength={6}
              inputMode="numeric"
              pattern="[0-9]*"
              onFocus={() => setformdata({ ...formdata, codeErrorText: "" })}
              placeholder=" "
              className={`input-placeholder ${
                formdata.codeErrorText
                  ? "codeInputError"
                  : formdata.code
                  ? "border-custom" // Apply border-custom when there's content
                  : ""
              }`}
            />

            <label
              htmlFor="code"
              className={`label-name ${
                formdata.code || formdata.codeErrorText ? "label-float" : ""
              } ${formdata.codeErrorText ? "input-error" : ""}`}
            >
              {formdata.codeErrorText && !formdata.code
                ? formdata.codeErrorText
                : "Enter 6 Digit Code*"}
            </label>
          </div>
        </div>
        <br />
        <div className="mb-3 flex flex-column gap-2">
          <div className="input-container" style={{ maxWidth: "100%" }}>
            <div style={{ position: "relative" }}>
              <InputText
                style={{ marginTop: "-18px", maxWidth: "100%" }}
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formdata.password}
                onFocus={() =>
                  setformdata({ ...formdata, passwordErrorText: "" })
                }
                onBlur={handlePasswordBlur}
                onChange={handlePasswordChange}
                placeholder=" "
                className={`input-placeholder ${
                  formdata.passwordErrorText
                    ? "passwordInputError"
                    : formdata.password
                    ? "border-custom" // Apply border-custom when there's content
                    : ""
                }`}
              />
              <label
                htmlFor="password"
                className={`label-name ${
                  formdata.password || formdata.passwordErrorText
                    ? "label-float"
                    : ""
                } ${formdata.passwordErrorText ? "input-error" : ""}`}
              >
                {formdata.passwordErrorText && !formdata.password
                  ? formdata.passwordErrorText
                  : "Enter New Password*"}
              </label>
              <span
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  position: "absolute",
                  right:
                    window.innerWidth <= 600
                      ? "8px"
                      : window.innerWidth <= 1024
                      ? "10px"
                      : "12px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  fontSize:
                    window.innerWidth <= 600
                      ? "1.2rem"
                      : window.innerWidth <= 1024
                      ? "1.5rem"
                      : "1.8rem",
                  cursor: "pointer",
                  color: "#888",
                }}
              >
                <i
                  className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"}`}
                ></i>
              </span>
            </div>
          </div>

          <div
            className="password-strength-label"
            style={{
              color:
                passwordStrength === "Complex password"
                  ? "green"
                  : passwordStrength === "Average complexity"
                  ? "orange"
                  : "red",
            }}
          >
            {passwordStrength}
          </div>
        </div>

        <div className="mb-3 flex flex-column gap-2">
          <div className="input-container" style={{ maxWidth: "100%" }}>
            <div style={{ position: "relative" }}>
              <InputText
                style={{ marginTop: "-6px", maxWidth: "100%" }}
                id="confirmPassword"
                name="confirmPassword"
                type={showPassword ? "text" : "password"}
                value={formdata.confirmPassword}
                onFocus={() =>
                  setformdata({ ...formdata, confirmPasswordErrorText: "" })
                }
                onChange={(e) =>
                  setformdata((prevState) => ({
                    ...prevState,
                    confirmPassword: e.target.value,
                  }))
                }
                placeholder=" "
                className={`input-placeholder ${
                  formdata.confirmPasswordErrorText
                    ? "confirmPasswordErrorText"
                    : formdata.confirmPassword
                    ? "border-custom" // Apply border-custom when there's content
                    : ""
                }`}
              />

              <label
                htmlFor="confirmPassword"
                className={`label-name ${
                  formdata.confirmPassword || formdata.confirmPasswordErrorText
                    ? "label-float"
                    : ""
                } ${formdata.confirmPasswordErrorText ? "input-error" : ""}`}
              >
                {formdata.confirmPasswordErrorText && !formdata.confirmPassword
                  ? formdata.confirmPasswordErrorText
                  : "Repeat New Password*"}
              </label>

              <span
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  position: "absolute",
                  right:
                    window.innerWidth <= 600
                      ? "8px"
                      : window.innerWidth <= 1024
                      ? "10px"
                      : "12px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  fontSize:
                    window.innerWidth <= 600
                      ? "1.2rem"
                      : window.innerWidth <= 1024
                      ? "1.5rem"
                      : "1.8rem",
                  cursor: "pointer",
                  color: "#888",
                }}
              >
                <i
                  className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"}`}
                ></i>
              </span>
            </div>
          </div>
        </div>

        {formdata.codeErrorText && formdata.code && !isFocused ? (
          <div className="error-message">{formdata.codeErrorText}</div>
        ) : formdata.passwordErrorText && formdata.password && !isFocused ? (
          <div className="error-message">{formdata.passwordErrorText}</div>
        ) : formdata.confirmPasswordErrorText &&
          formdata.password &&
          !isFocused ? (
          <div className="error-message">
            {formdata.confirmPasswordErrorText}
          </div>
        ) : null}
        {formError && <div className={styles.formError}>{formError}</div>}

        <div className="mb-1">
          <CustomButton label="Reset My password" type="submit" />
        </div>
        <p onClick={()=>navigate("/")} className={styles.backToLogin}>
                    Back to log in
                </p>
      </form>
    </div>
  );
};
