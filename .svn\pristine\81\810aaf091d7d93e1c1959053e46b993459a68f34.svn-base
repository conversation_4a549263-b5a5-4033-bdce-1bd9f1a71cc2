import React, { useState } from "react";
import { Joinnow2 } from "./JoinNow-2";
import { Joinnow3 } from "./JoinNow-3";
import { Joinnow4 } from "./JoinNow-4";
import { BusinessDetails } from "./BusinessDetails";
import Joinnow from "./JoinNow";

const Signup: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);

  const goToNext = () => {
    if (currentPage < 5) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const renderPage = () => {
    switch (currentPage) {
      case 1:
        return <Joinnow onNext={goToNext} setCurrentPage={setCurrentPage} />;
      case 2:
        return <Joinnow2 onNext={goToNext} onPrevious={goToPrevious} />;
      case 3:
        return (
          <Joinnow3
            onNext={goToNext}
            onPrevious={goToPrevious}
            setCurrentPage={setCurrentPage}
          />
        );
      case 4:
        return <Joinnow4 onPrevious={goToPrevious} />;
      case 5:
        return (
          <BusinessDetails
            onNext={goToNext}
            onPrevious={goToPrevious}
            setCurrentPage={setCurrentPage}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        flexDirection: "row",
        flex: 1,
      }}
    >
      {renderPage()}
    </div>
  );
};

export default Signup;
