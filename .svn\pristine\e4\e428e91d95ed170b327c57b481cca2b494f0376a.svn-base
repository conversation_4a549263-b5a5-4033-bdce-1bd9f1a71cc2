import React, { useEffect } from "react";
import {
  JobLoadingStates,
  Jobs,
  ManageJobSectionProps,
  WeeklySchedule,
} from "../types";
import JobCard from "../Common/JobCard";
import NoJobsCard from "../Common/NoJobsCard";
import useIsMobile from "../../../../hooks/useIsMobile";
import c from "../../../../helper/juggleStreetConstants";

const JobCardSkeleton = () => (
  <div
    style={{
      borderRadius: "20px",
      border: "1px solid #DFDFDF",
      width: "100%",
      marginBottom: "12px",
      padding: "0",
      backgroundColor: "#fff",
    }}
  >
    {/* Date Section */}
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "12px 20px 8px",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", width: "100px" }}>
        <div
          className="animate-pulse"
          style={{
            width: "60px",
            height: "20px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
      </div>
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <div
          className="animate-pulse"
          style={{
            width: "40px",
            height: "14px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: "30px",
            height: "14px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
      </div>
    </div>

    {/* Divider */}
    <div
      style={{
        height: "1px",
        backgroundColor: "#DFDFDF",
        margin: "0 0 8px",
      }}
    ></div>

    {/* Header Section */}
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "8px 8px",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          padding: "0 8px",
        }}
      >
        <div
          className="animate-pulse"
          style={{
            width: "17px",
            height: "17px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: "80px",
            height: "14px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
      </div>
    </div>

    {/* Job Details */}
    <div
      style={{ display: "flex", flexDirection: "column", padding: "0 16px" }}
    >
      {[1, 2, 3, 4].map((_, index) => (
        <div
          key={index}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            padding: "4px 8px",
          }}
        >
          <div
            className="animate-pulse"
            style={{
              width: "15px",
              height: "15px",
              backgroundColor: "#e0e0e0",
              borderRadius: "4px",
            }}
          ></div>
          <div
            className="animate-pulse"
            style={{
              width: index === 3 ? "120px" : "100px",
              height: "14px",
              backgroundColor: "#e0e0e0",
              borderRadius: "4px",
            }}
          ></div>
          {index === 3 && (
            <div style={{ display: "flex", gap: "8px" }}>
              {[1, 2].map((_, i) => (
                <div
                  key={i}
                  className="animate-pulse"
                  style={{
                    width: "33px",
                    height: "33px",
                    backgroundColor: "#e0e0e0",
                    borderRadius: "50%",
                    marginLeft: i === 0 ? "0" : "-10px",
                  }}
                ></div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>

    {/* Divider */}
    <div
      style={{
        height: "1px",
        backgroundColor: "#DFDFDF",
        margin: "8px 0",
      }}
    ></div>

    {/* Status Section */}
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "12px 20px",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <div
          className="animate-pulse"
          style={{
            width: "50px",
            height: "14px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
        <div
          className="animate-pulse"
          style={{
            width: "60px",
            height: "14px",
            backgroundColor: "#e0e0e0",
            borderRadius: "4px",
          }}
        ></div>
      </div>
      <div
        className="animate-pulse"
        style={{
          width: "80px",
          height: "28px",
          backgroundColor: "#e0e0e0",
          borderRadius: "20px",
        }}
      ></div>
    </div>
  </div>
);

const UnawardedJobs: React.FC<ManageJobSectionProps> = ({
  upComingJobs,
  viewJob,
  isLoading,
}) => {
  const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  const { isMobile } = useIsMobile();
  // const [isLoading, setIsLoading] = useState(true); // Loading state

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };
  // useEffect(() => {
  //     // Simulate data fetching delay (remove this if you have actual API calls)
  //     const timer = setTimeout(() => {
  //       setIsLoading(false);
  //     }, 1000); // Adjust delay as needed

  //     return () => clearTimeout(timer);
  //   }, []);
  const formatJobDate = (jobDate: string | Date | null) => {
    if (!jobDate) {
      return null;
    }

    const dateObj = new Date(jobDate);
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthsOfYear = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Extract the day of the week
    const day = daysOfWeek[dateObj.getDay()];

    // Extract the date with suffix
    const date = dateObj.getDate();
    const twoDigitDate = date < 10 ? `0${date}` : `${date}`; // Ensure two-digit format

    // Extract the short month
    const month = monthsOfYear[dateObj.getMonth()];

    // Calculate months difference from today
    const today = new Date();
    const monthsDifference =
      (dateObj.getFullYear() - today.getFullYear()) * 12 +
      (dateObj.getMonth() - today.getMonth());

    return {
      day,
      date: `${twoDigitDate}`, // Add suffix to two-digit date
      month,
      monthsDifference,
    };
  };

  const formatJobDateMobile = (jobDate: string | Date | null) => {
    if (!jobDate) {
      return null;
    }

    const dateObj = new Date(jobDate);
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthsOfYear = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Extract the day of the week
    const day = daysOfWeek[dateObj.getDay()];

    // Extract the date with suffix
    const date = dateObj.getDate();
    const twoDigitDate = date < 10 ? `0${date}` : `${date}`; // Ensure two-digit format
    const suffix =
      date % 10 === 1 && date !== 11
        ? "st"
        : date % 10 === 2 && date !== 12
        ? "nd"
        : date % 10 === 3 && date !== 13
        ? "rd"
        : "th";

    // Extract the short month
    const month = monthsOfYear[dateObj.getMonth()];

    // Calculate months difference from today
    const today = new Date();
    const monthsDifference =
      (dateObj.getFullYear() - today.getFullYear()) * 12 +
      (dateObj.getMonth() - today.getMonth());

    return {
      day,
      date: `${twoDigitDate}${suffix}`, // Add suffix to two-digit date
      month,
      monthsDifference,
    };
  };

  const unawardedJObs = upComingJobs
    .filter((job) => job.jobStatus === 1)
    .sort(
      (a, b) => new Date(a.jobDate).getTime() - new Date(b.jobDate).getTime()
    );
  const cleanAddress = (address: string) => {
    return address.split(",")[0].trim();
  };
  useEffect(() => {
    if (unawardedJObs.length > 0) {
      unawardedJObs.forEach((job, index) => {
        const entries = job.weeklySchedule?.weeklyScheduleEntries || [];
        const nonNullCount = entries.filter(
          (entry) => entry.applicantId !== null
        ).length;
        const nullCount = entries.filter(
          (entry) => entry.applicantId === null
        ).length;
        const hasAnyApplicant = nonNullCount > 0;
      });
    }
  }, [unawardedJObs]);
  return !isMobile ? (
    <div>
      {unawardedJObs.length > 0 ? (
        unawardedJObs.map((job, index) => (
          <JobCard
            key={index}
            date={formatJobDate(`${job.jobDate}`)}
            jobOption={job.managedBy === 1 ? "Do It Yourself" : "Juggle Assist"}
            timeSlot={
              job.isTutoringJob
                ? `${job.duration} week duration` // Show duration for tutoring jobs
                : job.isRecurringJob
                ? `${job.duration} week duration` // Show duration for recurring jobs
                : convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`) // Show time slot for one-off jobs
            }
            location={`${cleanAddress(job.formattedAddress)}, ${
              job.suburb || ""
            }`}
            expiry={job.expiresInDays}
            title={job.jobType}
            jobType={
              job.isTutoringJob
                ? job.jobDeliveryMethod === 1
                  ? "In home Tutoring"
                  : "Online Tutoring"
                : job.isRecurringJob
                ? "Recurring Job"
                : "One-off Job"
            }
            status={job.managedBy === 20 ? "Processing" : undefined}
            applications={job.applicantsApplied}
            onClick={() => viewJob(job.id)}
            aplicants={job.applicants}
            managedBy={job.managedBy}
          />
        ))
      ) : (
        <div style={{ width: "60%" }} className="mr-4">
          <NoJobsCard description={"No jobs match the specified criteria"} />
        </div>
      )}
    </div>
  ) : (
    <div>
      {isLoading === JobLoadingStates.initial ||
      isLoading === JobLoadingStates.loading ? (
        // Show skeleton cards for mobile during loading
        <div>
          {[...Array(3)].map((_, index) => (
            <JobCardSkeleton key={index} />
          ))}
        </div>
      ) : unawardedJObs.length > 0 ? (
        unawardedJObs.map((job, index) => (
          <JobCard
            key={index}
            date={formatJobDateMobile(`${job.jobDate}`)}
            jobOption={job.managedBy === 1 ? "Do It Yourself" : "Juggle Assist"}
            timeSlot={
              job.isTutoringJob
                ? `${job.duration} week duration` // Show duration for tutoring jobs
                : job.isRecurringJob
                ? `${job.duration} week duration` // Show duration for recurring jobs
                : convertTo12HourFormat(`${job.jobStartTime}-${job.jobEndTime}`) // Show time slot for one-off jobs
            }
            location={`${cleanAddress(job.formattedAddress)}, ${
              job.suburb || ""
            }`}
            expiry={job.expiresInDays}
            title={job.jobType}
            jobType={
              job.isTutoringJob
                ? "Tutoring Job"
                : job.isRecurringJob
                ? "Recurring Job"
                : "One Off Job"
            }
            status={
              job.managedBy === 1 ? `${job.applicantsTotal}`  : "Processing"
            }
            applications={job.applicantsApplied}
            onClick={() => viewJob(job.id)}
            onAnalytics={() => viewJob(job.id, true)}
            aplicants={job.applicants}
            availablity={
              job.weeklySchedule?.weeklyScheduleEntries?.some(
                (entry) => entry.applicantId !== null
              ) || false
            }
            managedBy={job.managedBy}
          />
        ))
      ) : (
        <div>
          <NoJobsCard description={"No jobs match the specified criteria"} />
        </div>
      )}
    </div>
  );
};

export default UnawardedJobs;
