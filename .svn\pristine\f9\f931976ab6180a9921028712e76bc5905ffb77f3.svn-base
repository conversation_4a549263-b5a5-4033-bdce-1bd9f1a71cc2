import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Divider } from "primereact/divider";
import { ProgressBar } from "primereact/progressbar";
import { AppDispatch, RootState } from "../../store";
import {
  toggleSideBar,
  updateAccountAndSettingsActiveTab,
  updateChatWindowState,
  updateProfileActivationEnabled,
  updateShowAccountAndSettings,
} from "../../store/slices/applicationSlice";
import Service from "../../services/services";
import { Jobs, WeeklySchedule } from "./manageJobs/types";
import styles from "../Parent/styles/left-hand-user-panel.module.css";
import logoSrc from "../../assets/images/juggle-st-transparent-card.png";
import logoBusiness from "../../assets/images/js-pro-logo-childcare-white (1).png";
import userprofile from "../../assets/images/sample_profile.png";
import discoverIcon from "../../assets/images/Icons/discover.png";
import manageJobsIcon from "../../assets/images/Icons/manage_job.png";
import chatIcon from "../../assets/images/Icons/chat_helper.png";
import rateHelperIcon from "../../assets/images/Icons/rate.png";
import questionIcon from "../../assets/../assets/images/Icons/question.png";
import menuIcon from "../../assets/../assets/images/Icons/menu.png";
import giftIcon from "../../assets/images/Icons/refer.png";
import defaultGiftIcon from "../../assets/images/Icons/referGrey.png";
import greenDot from "../../assets/../assets/images/Icons/green-dot.png";
import CustomButton from "../../commonComponents/CustomButton";
import PostJobActivationPopup from "./PostJobActivationPopup";
import ChatWithHelperActivationPopup from "./ChatWithHelperActivationPopup";
import utils from "../../components/utils/util";
import CookiesConstant from "../../helper/cookiesConst";
import EmployeeBenefits from "./EmployeeBenefits";
import { FaRegAddressCard } from "react-icons/fa6";
import { IoMenuOutline } from "react-icons/io5";
import useIsMobile from "../../hooks/useIsMobile";
import { updateActiveTabIndex } from "../../store/slices/accountSettingSlice";
import c from "../../helper/juggleStreetConstants";
import environment from "../../helper/environment";

interface LeftHandPanelProps {
  activeindex: number;
}
interface PendingStats {
  pendingChatMessages: number;
}

const LeftHandUserPanel: React.FC<LeftHandPanelProps> = ({ activeindex }) => {
  const clientType = Number(utils.getCookie(CookiesConstant.clientType));
  const [visible, setVisible] = useState(false);
  const TABS = [
    {
      icon: (
        <img
          src={discoverIcon}
          alt="Discover Helpers"
          width="16.8px"
          height="16.8px"
        />
      ),
      text: "Discover Helpers Near Me",
      onclick: (navigate: (path: string) => void) => {
        const path = clientType === 2 ? "/business-home" : "/parent-home";
        navigate(path);
        if (isMobile && isOpen) {
          dispatch(toggleSideBar());
        }
      },
    },

    {
      icon: (
        <img
          src={manageJobsIcon}
          alt="Manage My Jobs"
          width="15px"
          height="17px"
        />
      ),
      text: "Manage My Jobs",
      onclick: (navigate: (path: string) => void) => {
        const path =
          clientType === 2
            ? "/business-home/manage-jobs?jobId=-1&activeTab=0"
            : "/parent-home/manage-jobs?jobId=-1&activeTab=0";
        navigate(path);
        if (isMobile && isOpen) {
          dispatch(toggleSideBar());
        }
      },
      showJobCount: true,
    },

    {
      icon: (
        <img src={chatIcon} alt="Chat With Helper" width="15px" height="15px" />
      ),
      text: "Chat With Helper",
      requiresFullProfile: true,
      onclick: (navigate: (path: string) => void) => {
        if (
          !sessionInfo.loading &&
          sessionInfo.data["profileCompleteness"] < 100
        ) {
          setShowChatWithHelperActivationPopup(true);
        } else {
          const path =
            clientType === 2
              ? "/business-home/inAppChat"
              : "/parent-home/inAppChat";
          navigate(path);
          if (isMobile && isOpen) {
            dispatch(toggleSideBar());
          }
        }
      },
    },

    {
      icon: (
        <img
          src={rateHelperIcon}
          alt="Rate A Helper"
          width="15.94px"
          height="15.2px"
        />
      ),
      text: "Rate A Helper",
      onclick: (navigate: (path: string) => void) => {
        const path =
          clientType === 2
            ? "/business-home/manage-jobs?jobId=-1&activeTab=3"
            : "/parent-home/manage-jobs?jobId=-1&activeTab=3";
        navigate(path);
        if (isMobile && isOpen) {
          dispatch(toggleSideBar());
        }
      },
    },
  ];
  const [activeTab, setActiveTab] = useState(0);
  const [showPostJobActivationPopup, setShowPostJobActivationPopup] =
    useState(false);
  const [
    showChatWithHelperActivationPopup,
    setShowChatWithHelperActivationPopup,
  ] = useState(false);
  const [upcomingJobs, setUpcomingJobs] = useState<Jobs[]>([]);
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();

  const { sideBarIsOpened: isOpen } = useSelector(
    (state: RootState) => state.applicationState
  );
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  const jobId = searchParams.get("jobId");
  const activeTabSec = searchParams.get("activeTab");
  const { isMobile } = useIsMobile();
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [pendingStats, setPendingStats] = useState<PendingStats | null>(null);
  // Minimum swipe distance required (in pixels)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent<HTMLElement>) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent<HTMLElement>) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;

    if (isLeftSwipe && isOpen) {
      dispatch(toggleSideBar());
    }
  };
  // Handle clicks outside the panel
  const handleOutsideClick = (e: MouseEvent | TouchEvent) => {
    if (isMobile && isOpen) {
      const panel = document.querySelector(`.${styles.panel}`);
      const toggleBtn = document.querySelector(`.${styles.toggleButton}`);

      // Check if click is outside both panel and toggle button
      if (
        panel &&
        toggleBtn &&
        !(panel as any).contains(e.target) &&
        !(toggleBtn as any).contains(e.target)
      ) {
        dispatch(toggleSideBar());
      }
    }
  };

  // Add and remove event listeners
  // useEffect(() => {
  //   // document.addEventListener("mousedown", handleOutsideClick);
  //   document.addEventListener("touchstart", handleOutsideClick);

  //   return () => {
  //     // document.removeEventListener("mousedown", handleOutsideClick);
  //     document.removeEventListener("touchstart", handleOutsideClick);
  //   };
  // }, [isOpen, isMobile]);
  const toggleSidebar = () => {
    // If on mobile, toggle sidebar state
    if (isMobile) {
      dispatch(toggleSideBar());
    }
  };
  useEffect(() => {
    setActiveTab(activeindex);
  }, [activeTab]);

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const fetchedUpcomingJobs = await new Promise<Jobs[]>(
          (resolve, reject) => {
            Service.getUpComingJobs(
              (data: Jobs[]) => resolve(data),
              (error) => reject(error)
            );
          }
        );
        setUpcomingJobs(fetchedUpcomingJobs);
      } catch (error) {
        console.error("Error fetching jobs:", error);
      }
    };

    fetchJobs();
  }, [jobId, activeTabSec]);

  useEffect(() => {
    const fetchPendingStats = () => {
      Service.requestPendingStats(
        (response: PendingStats) => {
          setPendingStats(response);
        },
        (error) => {
          console.error("API call failed:", error);
          setPendingStats(null);
        }
      );
    };

    fetchPendingStats();
  }, []);

  const handlePostJobClick = () => {
    if (!sessionInfo.loading) {
      if (sessionInfo.data["profileCompleteness"] >= 100) {
        const path =
          clientType === 2
            ? "/business-home/job/post/job-type"
            : "/parent-home/job/post/job-type";
        navigate(path);
        if (isMobile && isOpen) {
          dispatch(toggleSideBar());
        }
      } else {
        setShowPostJobActivationPopup(true);
      }
    }
  };

  const handleTabClick = (index: number, tab: any) => {
    setActiveTab(index);

    if (tab.onclick) {
      tab.onclick(navigate);
    }

    if (
      tab.text === "Chat With Helper" &&
      !sessionInfo.loading &&
      sessionInfo.data["profileCompleteness"] >= 100
    ) {
      setShowChatWithHelperActivationPopup(true);
    }
  };

  const renderProfileActivationContent = () => (
    <aside className={`${styles.userPanel} ${!isOpen ? styles.closed : ""}`}>
      <div className={`${styles.imgContainer} ml-2`}>
        {clientType === 2 ? (
          <img
            loading="lazy"
            src={logoBusiness}
            alt="User Panel Logo"
            className={styles.logoDefault}
          />
        ) : (
          <img
            loading="lazy"
            src={logoSrc}
            alt="User Panel Logo"
            className={styles.logoDefault}
          />
        )}
      </div>
      <div className={styles.textContent}>
        <p className={styles.activationMessage}>
          <span className={styles.activationMessageHighlight}>
            Activate your profile{" "}
          </span>
          <br />
          to post a job and chat with helpers on Juggle Street
        </p>
        <p className={styles.progressLabel}>Your progress</p>
        <p className={styles.progressPercentage}>
          {sessionInfo.data["profileCompleteness"]}% complete
        </p>
        <ProgressBar
          value={sessionInfo.data["profileCompleteness"]}
          className={styles.progressBar}
        />
        <div className={styles.ctaWrapper}>
          <button
            className={styles.ctaButton}
            onClick={() => {
              dispatch(updateProfileActivationEnabled(true));
            }}
          >
            Continue
            <i className="pi pi-angle-right" style={{ marginLeft: "8px" }}></i>
          </button>
        </div>
      </div>
    </aside>
  );

  const renderUserProfile = () => (
    <div
      className={`${styles.userProfile} ${!isOpen ? styles.profileClosed : ""}`}
    >
      <img
        src={
          sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? userprofile
        }
        alt="Profile"
        className={styles.profilePhoto}
      />
      <img src={greenDot} alt="" className={styles.greenDot} />
      {isOpen && (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginLeft: "10px",
            marginRight: "10px",
          }}
        >
          <p className={styles.profileName}>
            Hi {sessionInfo.loading ? "" : sessionInfo.data["firstName"]}
          </p>

          {!sessionInfo.loading &&
            sessionInfo.data["profileCompleteness"] < 100 && (
              <p
                onClick={() => dispatch(updateProfileActivationEnabled(true))}
                style={{
                  margin: "0px",
                  fontFamily: "Manrope, sans-serif",
                  fontSize: "10px",
                  fontWeight: 300,
                  color: "#585858",
                  cursor: "pointer",
                  lineHeight: "15px",
                }}
              >
                Activate profile
              </p>
            )}
        </div>
      )}
    </div>
  );
 const manageByJobs = upcomingJobs.filter(job => job.jobStatus === 1).length;
  return (
    <>
      {isMobile && (
        <button
          onClick={toggleSidebar}
          className={styles.toggleButton}
          style={{
            position: "fixed",
            top: !isOpen ? "16px" : "27px",
            left:!isOpen ?  "10px" : "211px",
            zIndex: 3,
            transition: isOpen ? "transform 0.3s ease" : "transform 0.3s ease",
            padding: "10px",
            borderRadius: "5px",
          }}
        >
          {/* {isOpen ? (
                        <IoMenuOutline fontSize={'30px'} />
                    ) : (
                        <IoMenuOutline fontSize={'30px'} />
                    )} */}
          {!isOpen ? <IoMenuOutline fontSize={"30px"} /> : <IoMenuOutline fontSize={"30px"} color="green" />}
        </button>
      )}
      <PostJobActivationPopup
        isVisible={showPostJobActivationPopup}
        onHide={() => setShowPostJobActivationPopup(false)}
        onXClicked={() => setShowPostJobActivationPopup(false)}
      />
      <ChatWithHelperActivationPopup
        isVisible={showChatWithHelperActivationPopup}
        onHide={() => setShowChatWithHelperActivationPopup(false)}
        onXClicked={() => setShowChatWithHelperActivationPopup(false)}
      />
      <aside
        onTouchMove={onTouchMove}
        onTouchStart={onTouchStart}
        onTouchEnd={onTouchEnd}
        className={`${styles.panel} ${!isOpen ? styles.closed : ""}`}
      >
        {!sessionInfo.loading &&
          sessionInfo.data["profileCompleteness"] >= 100 && (
            <div className="">
              {clientType === 2 ? (
                <img
                  loading="lazy"
                  src={logoBusiness}
                  onClick={() => {
                    const path =
                      clientType === 2 ? "/business-home" : "/parent-home";
                    navigate(path);
                  }}
                  alt="User Panel Logo"
                  className={`${styles.logoDefault} ${
                    !isOpen ? styles.hidden : ""
                  } ml-3`}
                />
              ) : (
                <img
                  loading="lazy"
                  src={logoSrc}
                  onClick={() => {
                    const path =
                      clientType === 2 ? "/business-home" : "/parent-home";
                    navigate(path);
                  }}
                  alt="User Panel Logo"
                  className={`${styles.logoDefault} ${
                    !isOpen ? styles.hidden : ""
                  }`}
                />
              )}
              {renderUserProfile()}
            </div>
          )}

        {!sessionInfo.loading &&
          sessionInfo.data["profileCompleteness"] < 100 &&
          renderProfileActivationContent()}

        {/* {isOpen && (
                    <p
                        style={{
                            fontWeight: 600,
                            fontSize: '18px',
                            marginBottom: '5px',
                            marginLeft: '35px',
                            color: '#585858',
                        }}
                    >
                        Childcare
                    </p>
                )} */}
        <Divider className={styles.divider} />

        {isOpen ? (
          <CustomButton
            className={styles.postjobbtn}
            type="submit"
            label={
              <>
                <i
                  className="pi pi-pen-to-square"
                  style={{ marginRight: "5px" }}
                ></i>
                Post Job
              </>
            }
            onClick={handlePostJobClick}
          />
        ) : (
          <div className={styles.iconOnlyContainer}>
            <i className="pi pi-pen-to-square" style={{ color: "#ffa500" }}></i>
          </div>
        )}

        <div
          className={`${styles.verticalTabContainer} ${
            !isOpen ? styles.panelClosed : ""
          }`}
        >
          <div className={styles.icons}>
            {TABS.map((tab, index) => (
              <div key={index} className={styles.iconContainer}>
                {tab.icon}
              </div>
            ))}
          </div>

          <div className={styles.tabs}>
            {TABS.map((tab, index) => (
              <div
                key={index}
                className={`${styles.tabItem} ${
                  activeTab === index ? styles.activeTab : ""
                }`}
                style={{ fontSize: activeTab === index ? 600 : "normal" }}
                onClick={() => handleTabClick(index, tab)}
              >
                <div
                  className={styles.spaceDiv}
                  data-index={index === 0 ? "first" : index === 3 ? "last" : ""}
                />
                <div className={styles.textContainer}>
                  <div className="flex">
                  <span 
                  >{tab.text}
                  </span>
                 
                  {index === 1 && manageByJobs > 0 && (
                    <div className={styles.upcomingJobs}>{manageByJobs}</div>
                  )}
                   <div  className={`${activeTab === index ? styles.activeTabDot : ""}`}>

                      </div>
                      </div>
                  {index === 2 && pendingStats?.pendingChatMessages > 0 && (
                    <div className={styles.upcomingJobs}>
                      {pendingStats.pendingChatMessages}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className={styles.dividerContainer}>
          <Divider className={styles.dividersecond} />
        </div>

        <div className={styles.simpleIconContainer}>
          {/* <div className={styles.iconRow}>
                        <div className={styles.iconItem}>
                            <img
                                src={menuIcon}
                                alt='Menu Icon'
                                style={{ width: '18px', height: '18px' }}
                            />
                        </div>
                        <button className={styles.button} type='button'>
                            Explore More
                        </button>
                    </div> */}
          {clientType === 1 ? (
            <div className={styles.iconRow}>
              <div className={styles.iconItem}>
                <FaRegAddressCard
                  style={{ width: "18px", height: "18px", color: "#585858" }}
                />
              </div>
              <button
                onClick={() => setVisible(true)}
                className={styles.employeeBenifit}
              >
                Employee Benefits
              </button>
              <EmployeeBenefits
                isDialogVisible={visible}
                onHide={() => setVisible(false)}
              />
            </div>
          ) : null}

          <div className={styles.iconRow}>
            <div className={styles.iconItem}>
              <img
                src={questionIcon}
                alt="Question Icon"
                style={{ width: "18px", height: "18px" }}
              />
            </div>
            <button className={styles.button} type="button">
              Need Help?{" "}
              <p
                style={{
                  fontWeight: "400",
                  fontSize: "12px",
                  color: "#585858",
                  margin: "0px",
                  textDecoration: "underline",
                }}
                onClick={(e) => {
                  e.preventDefault();
                  dispatch(updateChatWindowState(true));
                }}
              >
                Message Our team
              </p>
            </button>
            
           
          </div>


          {clientType === 1 && (
                      <div className={styles.iconRow}>
                      <div className={styles.iconItem}>
                      <img
                          src={defaultGiftIcon}
                          alt="Gift Icon"
                          className={`${styles.defaultIcon}`}
                          width="14.4px"
                          height="14.48px"
                        />
                      </div>
                      <button
                        className={`border-none flex justify-content-center align-items-center  cursor-pointer  ${styles.referbtn}`}
                        style={{
                          backgroundColor: "transparent",
                          color: "#585858",
                          fontWeight: "300",
                          position: "relative",
                          fontSize:"12px",
                          padding: "8px 16px"
                        }}
                        onClick={() => {
                          dispatch(updateActiveTabIndex(26));
                          dispatch(updateAccountAndSettingsActiveTab(26));
                          dispatch(updateShowAccountAndSettings(true));
                        }}
                      >
                       
                        {/* <img
                          src={giftIcon}
                          alt="Hover Gift Icon"
                          className={`${styles.hoverIcon}`}
                          width="14.4px"
                          height="14.48px"
                        />{" "} */}
                        Get $10 for each friend you refer
                      </button>  
                      
                     
                    </div>
          )}      
          {/* <button
              className={`border-round-xl px-2  flex justify-content-center align-items-center gap-1 md:gap-2 cursor-pointer text-xs md:text-sm ${styles.referbtn}`}
              style={{
                backgroundColor: "transparent",
                color: "#585858",
                fontWeight: "500",
                position: "relative",
              }}
              onClick={() => {
                dispatch(updateActiveTabIndex(26));
                dispatch(updateAccountAndSettingsActiveTab(26));
                dispatch(updateShowAccountAndSettings(true));
              }}
            >
              <img
                src={defaultGiftIcon}
                alt="Gift Icon"
                className={`${styles.defaultIcon}`}
                width="14.4px"
                height="14.48px"
              />
              <img
                src={giftIcon}
                alt="Hover Gift Icon"
                className={`${styles.hoverIcon}`}
                width="14.4px"
                height="14.48px"
              />{" "}
              Get $10 for each friend you refer
            </button>       */}
          {/* <div>
              <button
              className={styles.employeeBenifit}
                onClick={() => setShowDialog(true)}
              >Employee Benefits</button>
              <EmployeeBenefitsRequest
                visible={showDialog}
                onHide={() => setShowDialog(false)}
              />
            </div> */}
        </div>
        {!isMobile && (
        <div style={{ zIndex: 1000555550, position: "absolute", left: 10, bottom: 10 }}>
          <p className="p-0 m-0 " style={{ color: "#585858", fontSize: "14px" }}>
            Version: {environment.getVersion()}
          </p>
        </div>
      )}
      </aside>
    </>
  );
};

export default LeftHandUserPanel;
