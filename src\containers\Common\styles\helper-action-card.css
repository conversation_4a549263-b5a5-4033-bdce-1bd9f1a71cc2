/* General styling */
.dialog-overlay {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background: rgba(0, 0, 0, 0.6); */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
  }
  
  .dialog-content {
    background-color: white;
    border-radius: 20px;
    width: 100%;
    max-width: 100%;
    /* padding: 20px; */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
  }
  
  .close-icon {
    position: absolute;
    top: -15px;
    right: 15px;
    background: white;
    border: none;
    border-radius: 50%;
    font-size: 1.1rem;
    color: #000;
    cursor: pointer;
    height: 30px;
    width: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .gradient-color{
    background: linear-gradient(
    90deg,
    rgba(255, 165, 0, 0.2),
    rgba(55, 169, 80, 0.2)
  );
    height: 30px;
    margin: 0;
padding: 0;  
border-top-left-radius: 20px;
border-top-right-radius: 20px;
}

  .image-container {
    display: flex;
    justify-content: center;
    position: relative;
  }
  
  .actionCard-profile-image {
    border-radius: 50%;
    height: 127px;
    width: 127px;
    object-fit: cover;
    position: absolute;
    top: -89px; /* Adjust based on the dialog box */
    border: 2px solid white;
  }
  
  .profile-name {
    margin-top: 44px; /* Adjust based on image placement */
    font-size: 24px;
    font-weight: bold;
    color: #585858;
  }
  
  .dialog-message {
    font-size: 16px;
    color: #585858;

  }
  
  .button-container {
    display: flex;
 flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;

  }
  
  .btn {
    padding: 10px;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    border-radius: 20px;
    width: 41%;
  }
  
  .btn-warning {
    background-color: #ffa500;
    color: #FFFFFF;
    font-size: 14px;

  }
  
  .btn-secondary {
    background-color: #585858;
    color: #FFFFFF;
    font-size: 14px;

  }
  
  .btn i {
    margin-right: 5px;
  }
  