import styles from "../../Helper/styles/responsibilities.module.css";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { useState } from "react";
import Service from "../../../services/services";
import useLoader from "../../../hooks/LoaderHook";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import '../../../components/utils/util.css';
import { IoShieldCheckmark } from "react-icons/io5";
import ProfileCompletenessHeader from "../Components/ProfileCompletenessHeader";
import useIsMobile from "../../../hooks/useIsMobile";
import { decrementProfileActivationStep, updateProfileActivationEnabled } from "../../../store/slices/applicationSlice";

const Responsibilities = () => {
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const session = useSelector((state: RootState) => state.sessionInfo.data);
  const [checkedStates, setCheckedStates] = useState(
    Array(5).fill(false) // Assuming 5 checkboxes
  );
  const { enableLoader, disableLoader } = useLoader();
  const {isMobile}=useIsMobile();
  const allSelected = checkedStates.every((isChecked) => isChecked);
  const dispatch = useDispatch<AppDispatch>();
  const handleCheckboxChange = (index: number) => {
    const updatedStates = [...checkedStates];
    updatedStates[index] = !updatedStates[index];
    setCheckedStates(updatedStates);
  };

  const handleContinue = async () => {
    const payload = {
      ...session,
      networkResponsibilitiesAcceptanceDate: new Date().toISOString(),
    };
    enableLoader();
    Service.updateSessionInfo(payload,
      (newSession) => {
        dispatch(createNewSessionInfo(newSession));
        disableLoader();
      },
      (error) => {
        disableLoader();
        
      });
  };

  return (
    <>
     {isMobile && (
       <ProfileCompletenessHeader
       title="Responsibilities"
       profileCompleteness={sessionInfo.data['profileCompleteness']}
       loading={sessionInfo.loading}
       onBackClick={()=>dispatch(updateProfileActivationEnabled(false))}
     />
     )}
    <div style={{paddingTop:isMobile && "10px" , paddingInline:isMobile && "15px"}} className={styles.responsibilitiescontainer}>
     {!isMobile && (
       <ProfileCompletenessHeader
       title="Responsibilities"
       profileCompleteness={sessionInfo.data['profileCompleteness']}
       loading={sessionInfo.loading}
     />
     )}
      <div
        className=""
        style={{ maxWidth: "100%", width: "auto", textWrap: "wrap" }}
      >
        <div className="flex align-items-center gap-1" >
          <IoShieldCheckmark style={{ fontSize: "18px", color: "#585858" }} />
          <h1
            className="p-0 m-0 font-bold"
            style={{ fontSize: "18px", color: "#585858" }}
          >
            As a member of the juggle St community you need to acknowledge and
            accept the following responsibilities:
          </h1>
        </div>
        <div
          className="flex flex-column align-items-start font-medium mt-2"
          style={{ fontSize: "16px", color: "#585858" , gap:isMobile && "20px"}}
        >
          {[
            "Respond to all job invitations, either Apply or Decline as soon as possible",
            "Respond to all messages",
            "Turn up on time for all jobs and complete the job to the best of your ability",
            "If you need to cancel a job do so through the Juggle St app, always provide notice",
            "Comply with Juggle St Terms & Conditions",
          ].map((label, index) => (
            <div className="flex" key={index}>
              <input
                type="checkbox"
                id={`checkbox-${index}`}
                // className="custom-checkbox"
                // className={styles.customCheckbox}
                checked={checkedStates[index]}
                onChange={() => handleCheckboxChange(index)}
                className={`${styles.customCheckbox}  cursor-pointer`}
                style={{ fontSize: '18px', color: "#fff" }}
              />
              <label
                htmlFor={`checkbox-${index}`}
                className="cursor-pointer ml-2"
              >
                {label}
              </label>
            </div>
          ))}
        </div>
      </div>
    {!isMobile ? (
        <button
        className={`${styles.responsibilitiesButton} mt-2 cursor-pointer font-semibold`}
        onClick={handleContinue}
        disabled={!allSelected}
        style={{
          backgroundColor: allSelected ? "#179d52" : "#D3D3D3",
          color: allSelected ? "#FFFFFF" : "#A9A9A9",
          cursor: allSelected ? "pointer" : "not-allowed",
        }}
      >
        Continue
      </button>
    ):(
     <div className={styles.responsibilitiesButtonMobileDiv}>
       <button
      className={`${styles.responsibilitiesButtonMobile} my-2 cursor-pointer font-semibold`}
      onClick={handleContinue}
      disabled={!allSelected}
      style={{
        backgroundColor: allSelected ? "#179d52" : "#D3D3D3",
        color: allSelected ? "#FFFFFF" : "#A9A9A9",
        cursor: allSelected ? "pointer" : "not-allowed",
        margin:"50px"
      }}
    >
      Continue
    </button>
     </div>
    )}
    </div>
    </>
  );
};

export default Responsibilities;
