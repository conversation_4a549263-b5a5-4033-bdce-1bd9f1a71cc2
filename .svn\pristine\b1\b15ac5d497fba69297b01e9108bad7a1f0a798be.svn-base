import { Jobs } from '../../types';
import { useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { IoClose } from 'react-icons/io5';
import { Divider } from 'primereact/divider';

import styles from '../../../styles/all-filters.module.css';
import EditCriteriaImg from '../../../../../assets/images/candiate-matching.png';
import c from '../../../../../helper/juggleStreetConstants';
import useLoader from '../../../../../hooks/LoaderHook';
import Service from '../../../../../services/services';
import useIsMobile from '../../../../../hooks/useIsMobile';

function Filters<T>({
    values,
    type,
    name,
    expanded = false,
    groupName,
    getId,
    getLabel,
    checked,
    onChange,
}: {
    values: T[];
    type: 'checkbox' | 'radio';
    name: string;
    expanded?: boolean;
    groupName: string;
    getId: (value: T, index: number) => string;
    getLabel: (value: T, index: number) => string;
    checked: (value: T, index: number) => boolean;
    onChange: (value: T, index: number) => void;
}) {
    return values.map((value, index) => {
        const isSelected = checked(value, index);

        return (
            <div
                className={`flex align-items-center gap-2 ${expanded ? 'w-6' : ''}`}
                key={index}
                style={{
                    marginBottom: expanded ? '5px' : '0px',
                    cursor: 'pointer',
                }}
                onClick={type === 'radio' ? () => onChange(value, index) : undefined}
            >
                {type === 'radio' ? (
                    <div
                        style={{
                            height: '18px',
                            width: '18px',
                            borderRadius: '50%',
                            padding: '1px',
                            border: `1px solid ${isSelected ? '#179D52' : '#DFDFDF'}`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        {isSelected && (
                            <div
                                style={{
                                    height: '100%',
                                    width: '100%',
                                    borderRadius: '50%',
                                    backgroundColor: '#179D52',
                                }}
                            />
                        )}
                    </div>
                ) : (
                    <input
                        className={`${styles.customCheckBox} cursor-pointer`}
                        type='checkbox'
                        name={`${groupName}-${name}`}
                        id={getId(value, index)}
                        checked={isSelected}
                        onChange={(e) => {
                            e.stopPropagation(); // Prevents container click event
                            onChange(value, index);
                        }}
                    />
                )}
                <label
                    className='cursor-pointer'
                    style={{
                        fontWeight: '500',
                        fontSize: '16px',
                        userSelect: 'none',
                        textWrap: 'nowrap',
                        color: '#585858',
                    }}
                    htmlFor={getId(value, index)}
                    onClick={(e) => {
                        if (type === 'checkbox') {
                            e.stopPropagation();
                        }
                    }}
                >
                    {getLabel(value, index)}
                </label>
            </div>
        );
    });
}

const CriteriaEditor: React.FC<{
    job: Jobs;
    onClose: (newCriteria: Record<string, any> | null) => void;
}> = ({ job, onClose }) => {
    const [distance, setDistance] = useState(1);
    const [helpersAge, setHelpersAge] = useState<boolean[]>([true, true, true, true]);
    const [experience, setExperience] = useState<boolean[]>(new Array(4).fill(true));
    const [other, setOther] = useState<boolean[]>(new Array(10).fill(false));
    const { isMobile } = useIsMobile();
    function processCriteria() {
        const newCriteria: Record<string, any> = {};

        if (job.isTutoringJob) {
            const age: number[] = [];
            helpersAge.forEach((value, index) => {
                if (value) {
                    age.push(index + 1);
                }
            });
            newCriteria.age = {
                field: 'age',
                operator: 'eq',
                value: age,
            };

            const tutoringCategory: number[] = [];
            experience.forEach((value, index) => {
                if (value) {
                    tutoringCategory.push(index + 1);
                }
            });
            newCriteria.tutoringCategory = {
                field: 'tutoringCategory',
                operator: 'eq',
                value: tutoringCategory,
            };
        } else {
            newCriteria.distance = {
                field: 'distance',
                operator: 'eq',
                value: distance,
            };

            const age: number[] = [];
            helpersAge.forEach((value, index) => {
                if (value) {
                    age.push(index + 1);
                }
            });
            newCriteria.age = {
                field: 'age',
                operator: 'eq',
                value: age,
            };

            const otherCriteria: number[] = [];
            other.forEach((value, index) => {
                if (value) {
                    otherCriteria.push(index);
                }
            });
            newCriteria.otherSkills = {
                field: 'otherSkills',
                operator: 'eq',
                value: otherCriteria,
            };
        }

        onClose(newCriteria);
    }

    return !isMobile ? (
        <div
            className='m-auto flex flex-column gap-3 py-3 overflow-y-auto'
            style={{
                width: '90%',
                maxWidth: '567px',
                height: '90%',
                maxHeight: '707px',
                backgroundColor: '#FFFFFF',
                borderRadius: '33px',
            }}
        >
            <div className='w-full flex justify-content-between px-5 align-items-center'>
                <h1
                    className='m-0 p-0'
                    style={{
                        fontWeight: '700',
                        fontSize: '32px',
                        color: '#585858',
                    }}
                >
                    Candidate Criteria
                </h1>
                <IoClose
                    className='cursor-pointer'
                    color='#585858'
                    fontSize='30'
                    onClick={(e) => {
                        e.preventDefault();
                        onClose(null);
                    }}
                />
            </div>
            <Divider />
            <p
                className='m-0 p-0 px-5'
                style={{
                    fontWeight: '400',
                    fontSize: '16px',
                    color: '#585858',
                }}
            >
                Select the criteria candidates “must have” to apply for this job. You can view the
                detailed profile of all shortlisted applicants before deciding to interview.
            </p>

            {!job.isTutoringJob && (
                <div className='w-full flex flex-column gap-1 px-5'>
                    <h1
                        className='m-0 p-0'
                        style={{
                            fontWeight: '700',
                            fontSize: '18px',
                            color: '#585858',
                        }}
                    >
                        Distance
                    </h1>
                    <div className={`flex justify-content-start gap-2 px-2`}>
                        <Filters
                            values={[2.5, 5, 10]}
                            type='radio'
                            name='distance'
                            groupName='distance-group'
                            getId={(value, index) => `distance-${value.toString()}-${index}`}
                            getLabel={(value) => `Within ${value}km`}
                            checked={(_, index) => distance === index}
                            onChange={(_, index) => setDistance(index)}
                        />
                    </div>
                    <Divider className='mt-3' />
                </div>
            )}
            <div className='w-full flex flex-column gap-1 px-5'>
                <h1
                    className='m-0 p-0'
                    style={{
                        fontWeight: '700',
                        fontSize: '18px',
                        color: '#585858',
                    }}
                >
                    Helpers Age
                </h1>
                <div className={`flex flex-wrap px-2`}>
                    <Filters
                        values={['16 - 17', '18 - 24', '25-44', '45+']}
                        type='checkbox'
                        name='HelpersAge'
                        groupName='Helpers-age-group'
                        expanded={true}
                        getId={(value, index) => `Helpers-age-${value.trim()}-${index}`}
                        getLabel={(value) => value}
                        checked={(_, index) => helpersAge[index]}
                        onChange={(_, index) =>
                            setHelpersAge((prev) => {
                                const newHelpersAge = [...prev];
                                newHelpersAge[index] = !newHelpersAge[index];
                                return newHelpersAge;
                            })
                        }
                    />
                </div>
                <Divider className='mt-3' />
            </div>
            {job.isTutoringJob && (
                <div className='w-full flex flex-column gap-1 px-5'>
                    <h1
                        className='m-0 p-0'
                        style={{
                            fontWeight: '700',
                            fontSize: '18px',
                            color: '#585858',
                        }}
                    >
                        Experience
                    </h1>
                    <div className={`flex flex-wrap px-2`}>
                        <Filters
                            values={[
                                ['Newbie', 0],
                                ['Apprentice', 1],
                                ['Experienced', 2],
                                ['Professional', 3],
                            ]}
                            type='checkbox'
                            name='experience'
                            groupName='experience-group'
                            expanded={true}
                            getId={(value, index) =>
                                `experience-${(value[0] as string).trim()}-${index}`
                            }
                            getLabel={(value) => value[0] as string}
                            checked={(value) => experience[value[1] as number]}
                            onChange={(value) =>
                                setExperience((prev) => {
                                    let newValue = [...prev];
                                    newValue[value[1] as number] = !newValue[value[1] as number];
                                    return newValue;
                                })
                            }
                        />
                    </div>
                    <Divider className='mt-3' />
                </div>
            )}
            {!job.isTutoringJob && (
                <div className='w-full flex flex-column gap-1 px-5'>
                    <h1
                        className='m-0 p-0'
                        style={{
                            fontWeight: '700',
                            fontSize: '18px',
                            color: '#585858',
                        }}
                    >
                        Experience
                    </h1>
                    <div className={`flex flex-wrap px-2`}>
                        <Filters
                            values={[
                                ['Special Needs', 4],
                                ['Driving License', 9],
                            ]}
                            type='checkbox'
                            name='other'
                            groupName='other-group'
                            expanded={true}
                            getId={(value, index) =>
                                `other-${(value[0] as string).trim()}-${index}`
                            }
                            getLabel={(value) => value[0] as string}
                            checked={(value) => other[value[1] as number]}
                            onChange={(value) =>
                                setOther((prev) => {
                                    let newValue = [...prev];
                                    newValue[value[1] as number] = !newValue[value[1] as number];
                                    return newValue;
                                })
                            }
                        />
                    </div>
                    <Divider className='mt-3' />
                </div>
            )}
            <div className='w-full mt-auto px-5 flex justify-content-between align-items-center'>
                <button
                    className={`${styles.buttonStyle}`}
                    data-clear
                    onClick={(e) => {
                        e.preventDefault();
                        onClose(null);
                    }}
                >
                    Clear
                </button>
                <button
                    className={`${styles.buttonStyle}`}
                    data-apply
                    onClick={(e) => {
                        e.preventDefault();
                        processCriteria();
                    }}
                >
                    Apply
                </button>
            </div>
        </div>
    ) : (
        <div
            className='m-auto flex flex-column gap-3 py-3 overflow-y-auto'
            style={{
                width: '90%',
                maxWidth: '567px',
                height: '90%',
                maxHeight: '707px',
                backgroundColor: '#FFFFFF',
                borderRadius: '33px',
            }}
        >
            <div className={styles.dialogContentMobile}>
                <h3 className={styles.dialogHeadMobile} style={{ marginBottom: '0px' }}>
                    Candidate Criteria
                </h3>
                <p className={styles.dialogInstruct} style={{ marginTop: '0px' }}>
                    Select the criteria candidates “must have” to apply for this job. You can view
                    the detailed profile of all shortlisted applicants before deciding to interview.
                </p>

                <div className={styles.criteriaSection}>
                    {!job.isTutoringJob && (
                        <div className={styles.criteriaItem}>
                            <p
                                className={styles.criteriaTag}
                                style={{
                                    margin: '0px',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    color: '#585858',
                                }}
                            >
                                Distance
                            </p>
                            <div
                                className={styles.radioOptions}
                                style={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    marginLeft: '0px',
                                    gap: '10px',
                                }}
                            >
                                {[2.5, 5, 10].map((value, index) => (
                                    <button
                                        key={index}
                                        className='flex align-items-center justify-content-center cursor-pointer'
                                        style={{
                                            padding: '7px 17px',
                                            border: `2px solid ${
                                                distance === index ? '#179d52' : '#DFDFDF'
                                            }`,
                                            borderRadius: '20px',
                                            backgroundColor:
                                                distance === index ? '#179D5233' : '#FFFFFF',
                                            color: distance === index ? '#179D52' : '#585858',
                                            fontSize: '14px',
                                            fontWeight: distance === index ? '700' : '500',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => setDistance(index)}
                                    >
                                        {`Within ${value}km`}
                                    </button>
                                ))}
                            </div>
                            <Divider className='my-3' />
                        </div>
                    )}

                    <div className={styles.criteriaItem}>
                        <p
                            className={styles.criteriaTag}
                            style={{
                                margin: '0px',
                                fontSize: '18px',
                                fontWeight: '700',
                                color: '#585858',
                            }}
                        >
                            Helper Age
                        </p>
                        <div className={styles.checkboxGroupMobile}>
                            <div className={styles.checkboxPairMobile}>
                                {['16 - 17', '18 - 24'].map((value, index) => (
                                    <button
                                        key={index}
                                        className={`${
                                            helpersAge[index]
                                                ? styles.selectedButton
                                                : styles.button
                                        } flex align-items-center justify-content-center cursor-pointer`}
                                        style={{
                                            border: `2px solid ${
                                                helpersAge[index] ? '#179d52' : '#DFDFDF'
                                            }`,
                                            borderRadius: '20px',
                                            backgroundColor: helpersAge[index]
                                                ? '#179D5233'
                                                : '#FFFFFF',
                                            color: helpersAge[index] ? '#179D52' : '#585858',
                                            fontSize: '14px',
                                            fontWeight: helpersAge[index] ? '700' : '500',
                                            cursor: 'pointer',
                                            padding: '7px 17px',
                                        }}
                                        onClick={() =>
                                            setHelpersAge((prev) => {
                                                const newHelpersAge = [...prev];
                                                newHelpersAge[index] = !newHelpersAge[index];
                                                return newHelpersAge;
                                            })
                                        }
                                    >
                                        {value}
                                    </button>
                                ))}
                            </div>

                            <div className={styles.checkboxPairSecMobile}>
                                {['25-44', '45+'].map((value, index) => (
                                    <button
                                        key={index + 2}
                                        className={`${
                                            helpersAge[index + 2]
                                                ? styles.selectedButton
                                                : styles.button
                                        } flex align-items-center justify-content-center cursor-pointer`}
                                        style={{
                                            border: `2px solid ${
                                                helpersAge[index + 2] ? '#179d52' : '#DFDFDF'
                                            }`,
                                            borderRadius: '20px',
                                            backgroundColor: helpersAge[index + 2]
                                                ? '#179D5233'
                                                : '#FFFFFF',
                                            color: helpersAge[index + 2] ? '#179D52' : '#585858',
                                            fontSize: '14px',
                                            fontWeight: helpersAge[index + 2] ? '700' : '500',
                                            cursor: 'pointer',
                                            padding: '7px 17px',
                                        }}
                                        onClick={() =>
                                            setHelpersAge((prev) => {
                                                const newHelpersAge = [...prev];
                                                newHelpersAge[index + 2] =
                                                    !newHelpersAge[index + 2];
                                                return newHelpersAge;
                                            })
                                        }
                                    >
                                        {value}
                                    </button>
                                ))}
                            </div>
                        </div>
                        <Divider className='my-3' />
                    </div>

                    {job.isTutoringJob && (
                        <div className={styles.criteriaItem}>
                            <p
                                className={styles.criteriaTag}
                                style={{
                                    margin: '0px',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    color: '#585858',
                                }}
                            >
                                Experience
                            </p>
                            <div className={styles.checkboxGroupMobile}>
                                <div className={styles.checkboxPairFirstMobile}>
                                    {[
                                        ['Newbie', 0],
                                        ['Apprentice', 1],
                                    ].map(([label, value], index) => (
                                        <div key={index} className={styles.checkboxOptionMobile}>
                                            <button
                                                className={`${
                                                    experience[value]
                                                        ? styles.selectedButtonMobile
                                                        : styles.buttonMobile
                                                } flex align-items-center justify-content-center cursor-pointer`}
                                                style={{
                                                    padding: '7px 17px',
                                                    border: `2px solid ${
                                                        experience[value] ? '#179d52' : '#DFDFDF'
                                                    }`,
                                                    borderRadius: '20px',
                                                    backgroundColor: experience[value]
                                                        ? '#179D5233'
                                                        : '#FFFFFF',
                                                    color: experience[value]
                                                        ? '#179D52'
                                                        : '#585858',
                                                    fontSize: '14px',
                                                    fontWeight: experience[value] ? '700' : '500',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() =>
                                                    setExperience((prev) => {
                                                        const newExperience = [...prev];
                                                        newExperience[value] =
                                                            !newExperience[value];
                                                        return newExperience;
                                                    })
                                                }
                                            >
                                                {label}
                                            </button>
                                        </div>
                                    ))}
                                </div>

                                <div className={styles.checkboxPairSecondMobile}>
                                    {[
                                        ['Experienced', 2],
                                        ['Professional', 3],
                                    ].map(([label, value], index) => (
                                        <div key={index} className={styles.checkboxOptionMobile}>
                                            <button
                                                className={`${
                                                    experience[value]
                                                        ? styles.selectedButtonMobile
                                                        : styles.buttonMobile
                                                } flex align-items-center justify-content-center cursor-pointer`}
                                                style={{
                                                    padding: '7px 17px',
                                                    border: `2px solid ${
                                                        experience[value] ? '#179d52' : '#DFDFDF'
                                                    }`,
                                                    borderRadius: '20px',
                                                    backgroundColor: experience[value]
                                                        ? '#179D5233'
                                                        : '#FFFFFF',
                                                    color: experience[value]
                                                        ? '#179D52'
                                                        : '#585858',
                                                    fontSize: '14px',
                                                    fontWeight: experience[value] ? '700' : '500',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() =>
                                                    setExperience((prev) => {
                                                        const newExperience = [...prev];
                                                        newExperience[value] =
                                                            !newExperience[value];
                                                        return newExperience;
                                                    })
                                                }
                                            >
                                                {label}
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <Divider className='my-3' />
                        </div>
                    )}

                    {!job.isTutoringJob && (
                        <div className={styles.criteriaItem}>
                            <p
                                className={styles.criteriaTag}
                                style={{
                                    margin: '0px',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    color: '#585858',
                                }}
                            >
                                Other
                            </p>
                            <div className={styles.checkboxGroup}>
                                <div className={styles.checkboxPairThird}>
                                    {[
                                        ['Special Needs', 4],
                                        ['Driving License', 9],
                                    ].map(([label, value], index) => (
                                        <div key={index} className={styles.checkboxOption}>
                                            <button
                                                className={`${
                                                    other[value]
                                                        ? styles.selectedButton
                                                        : styles.button
                                                } flex align-items-center justify-content-center cursor-pointer`}
                                                style={{
                                                    padding: '7px 17px',
                                                    border: `2px solid ${
                                                        other[value] ? '#179d52' : '#DFDFDF'
                                                    }`,
                                                    borderRadius: '20px',
                                                    backgroundColor: other[value]
                                                        ? '#179D5233'
                                                        : '#FFFFFF',
                                                    color: other[value] ? '#179D52' : '#585858',
                                                    fontSize: '14px',
                                                    fontWeight: other[value] ? '700' : '500',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() =>
                                                    setOther((prev) => {
                                                        const newOther = [...prev];
                                                        newOther[value] = !newOther[value];
                                                        return newOther;
                                                    })
                                                }
                                            >
                                                {label}
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <Divider className='my-3' />
                        </div>
                    )}
                </div>

                <div className={styles.dialogFooter}>
                    <button
                        className={styles.goBackButton}
                        onClick={(e) => {
                            e.preventDefault();
                            onClose(null);
                        }}
                    >
                        Clear
                    </button>
                    <button
                        className={styles.nextButton}
                        onClick={(e) => {
                            e.preventDefault();
                            processCriteria();
                        }}
                    >
                        Apply
                    </button>
                </div>
            </div>
        </div>
    );
};

const EditCriteria: React.FC<{ job: Jobs }> = ({ job }) => {
    const [isVisible, setIsVisible] = useState(false);
    const { enableLoader, disableLoader } = useLoader();
    const { isMobile } = useIsMobile();
    function processCriteria(criteria: Record<string, any>) {
        let payload: Jobs = {
            ...job,
            actionType: c.jobActionType.JOB_TO_HELPER,
        };

        if (job.isTutoringJob) {
            const finalCriteria = [
                ...job.applicantFilters.filter(
                    (f) => !['distance', 'age', 'tutoringCategory'].includes(f.field)
                ),
            ];

            for (const key in criteria) {
                if (
                    key === 'age' &&
                    criteria[key].value.length > 0 &&
                    criteria[key].value.length < 4
                ) {
                    finalCriteria.push(criteria[key]);
                } else if (
                    key === 'tutoringCategory' &&
                    criteria[key].value.length > 0 &&
                    criteria[key].value.length < 4
                ) {
                    finalCriteria.push(criteria[key]);
                } else if (key === 'distance') {
                    finalCriteria.push({
                        field: 'distance',
                        operator: 'eq',
                        value: 2,
                    });
                }
            }

            payload.applicantFilters = finalCriteria;
        } else {
            const finalCriteria = [
                ...job.applicantFilters.filter(
                    (f) => !['distance', 'age', 'otherSkills'].includes(f.field)
                ),
            ];

            for (const key in criteria) {
                if (key === 'distance') {
                    finalCriteria.push(criteria[key]);
                } else if (
                    key === 'age' &&
                    criteria[key].value.length > 0 &&
                    criteria[key].value.length < 4
                ) {
                    finalCriteria.push(criteria[key]);
                } else if (
                    key === 'otherSkills' &&
                    criteria[key].value.length > 0 &&
                    criteria[key].value.length < 2
                ) {
                    finalCriteria.push(criteria[key]);
                }
            }

            payload.applicantFilters = finalCriteria;
        }
        updateJob(payload);
    }

    function updateJob(job: Jobs) {
        enableLoader();
        Service.jobClientUpdate(
            job,
            job.id,
            () => {
                setIsVisible(false);
                disableLoader();
            },
            (error) => {
                
                disableLoader();
            }
        );
    }

    return !isMobile ? (
        <div className='w-full h-full flex flex-column align-items-center gap-2'>
            <Dialog
                visible={isVisible}
                onHide={() => setIsVisible(false)}
                style={{
                    width: '100vw',
                    height: '100vh',
                    maxWidth: 'none',
                    maxHeight: 'none',
                }}
                content={
                    <CriteriaEditor
                        job={job}
                        onClose={(c) => {
                            if (!c) {
                                setIsVisible(false);
                            } else {
                                processCriteria(c);
                            }
                        }}
                    />
                }
            />
            <h1
                className='mx-0 mb-0 p-0 text-center'
                style={{
                    marginTop: '5%',
                    fontWeight: '700',
                    fontSize: '60px',
                    color: '#585858',
                }}
            >
                Candidate Matching
            </h1>
            <p
                className='m-0 p-0 text-center'
                style={{
                    fontWeight: '500',
                    fontSize: '18px',
                    color: '#585858',
                }}
            >
                You have chosen to edit this job using Juggle Assist, Juggle Street’s
                candidate-matching algorithm that generates an applicant shortlist. To help us find
                the best possible match, please provide your candidate's "Must Have" criteria.
            </p>
            <button
                className='cursor-pointer mt-3'
                type='button'
                style={{
                    minWidth: 'fit-content',
                    border: 'none',
                    borderRadius: '10px',
                    backgroundColor: '#FFA500',
                    color: '#FFFFFF',
                    padding: '10px 20px',
                    width: '20%',
                    fontWeight: '700',
                    fontSize: '14px',
                }}
                onClick={(e) => {
                    e.preventDefault();
                    setIsVisible(true);
                }}
            >
                View Criteria
            </button>
            {/* <div className='flex-grow-1 w-full flex justify-content-center mt-3'>
                <img
                    className='mx-auto'
                    src={EditCriteriaImg}
                    alt='EditCriteriaImg'
                    style={{
                        objectFit: 'contain',
                        width: '80%',
                    }}
                />
            </div> */}
        </div>
    ) : (
        <div className='w-full h-full flex flex-column align-items-center gap-2'>
            <Dialog
                visible={isVisible}
                onHide={() => setIsVisible(false)}
                style={{
                    width: '100vw',
                    height: '100vh',
                    maxWidth: 'none',
                    maxHeight: 'none',
                }}
                content={
                    <CriteriaEditor
                        job={job}
                        onClose={(c) => {
                            if (!c) {
                                setIsVisible(false);
                            } else {
                                processCriteria(c);
                            }
                        }}
                    />
                }
            />
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    paddingTop: '60px',
                }}
            >
                <h1
                    className='mx-0 mb-0 p-0 text-center'
                    style={{
                        marginTop: '5%',
                        fontWeight: '700',
                        fontSize: '30px',
                        color: '#585858',
                    }}
                >
                    Candidate Matching
                </h1>
                <p
                    className='m-0 p-0 text-center'
                    style={{
                        fontWeight: '500',
                        fontSize: '14px',
                        color: '#585858',
                    }}
                >
                    You have chosen to edit this job using Juggle Assist, Juggle Street’s
                    candidate-matching algorithm that generates an applicant shortlist. To help us
                    find the best possible match, please provide your candidate's "Must Have"
                    criteria.
                </p>
                <button
                    className='cursor-pointer mt-3'
                    type='button'
                    style={{
                        minWidth: 'fit-content',
                        border: 'none',
                        borderRadius: '10px',
                        backgroundColor: '#FFA500',
                        color: '#FFFFFF',
                        padding: '10px 20px',
                        width: '20%',
                        fontWeight: '700',
                        fontSize: '14px',
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        setIsVisible(true);
                    }}
                >
                    View Criteria
                </button>
            </div>
            {/* <div className='flex-grow-1 w-full flex justify-content-center mt-3'>
                <img
                    className='mx-auto'
                    src={EditCriteriaImg}
                    alt='EditCriteriaImg'
                    style={{
                        objectFit: 'contain',
                        width: '100%',
                        height: '100%',
                    }}
                />
            </div> */}
        </div>
    );
};

export default EditCriteria;
