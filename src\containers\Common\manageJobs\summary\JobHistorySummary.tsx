import React, { useEffect, useMemo, useRef, useState } from "react";
import { Jobs, ManageJobSectionProps, WeeklySchedule } from "../types";
import styles from "../../../Common/styles/review-and-post.module.css";
import { Tag } from "primereact/tag";
import calendar from "../../../../assets/images/Icons/Icon (1).png";
import clock from "../../../../assets/images/Icons/Vector.png";
import multipleFile from "../../../../assets/images/Icons/file-multiple.png";
import ArrowLeft from "../../../../assets/images/Icons/arrow-left.png";
import home from "../../../../assets/images/Icons/home-05.png";
import doller from "../../../../assets/images/Icons/Dollar.png";
import { Divider } from "primereact/divider";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { useNavigate, useSearchParams } from "react-router-dom";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { Dialog } from "primereact/dialog";
import JobAwardedCard from "../Common/JobAwardedCard";
import AwardedDetailsPopup from "../Common/AwardedDetailsPopup";
import { CancelJobPopup, useCancelJobPopup } from "../Common/CancelJobPopup";
import ViewJobFull from "./ViewJob";
import WeeklyScheduleTable from "../Common/WeeklyScheduleTable";
import BackArrow from "../../../../assets/images/Icons/back-icon.png";
import calendarMobile from "../../../../assets/images/Icons/calender.png";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import dollerMobile from "../../../../assets/images/Icons/family_membership.png";
import homeMobile from "../../../../assets/images/Icons/home.png";
import CloseIcon from "../../../../assets/images/Icons/white-close.png";
import useIsMobile from "../../../../hooks/useIsMobile";
import { MdOutlineHomeWork } from "react-icons/md";
import JobHistoryTable from "../Common/JobHistroyTable";
import JobAnalytics from "../Common/JobAnalytics";
import { IframeBridge } from "../../../../services/IframeBridge";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { IoClose } from "react-icons/io5";
const JobHistorySummary: React.FC<ManageJobSectionProps> = (
  {
    // refresh,
    // jobHistory,
  }
) => {
  const { disableLoader, enableLoader } = useLoader();
  const [searchParams] = useSearchParams();
  const [jobClients, setJobClients] = useState<Jobs | null>(null);
  const jobIdString = searchParams.get("jobId");
  const activeTab = searchParams.get("activeTab");
  const navigate = useNavigate();
  const [showfullJob, setShowFullJob] = useState(false);
  const [duplicateJob, setDuplicateJob] = useState<boolean>(false);
  const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const ischateligible = sessionInfo.data?.["paymentInfo"]["paymentType"];
  const [dialogVisible, setDialogVisible] = useState(false);
  const [chatErrorMessage, setChatErrorMessage] = useState<string>('');
  const jobId = jobIdString ? parseInt(jobIdString) : null;
  const showBank = jobClients?.helperPaymentMethod === 2;
  const showTransport = !(jobClients?.isTutoringJob && jobClients?.isRecurringJob);
  const [isPhoneNumber, setIsPhoneNumber] = useState(false);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const convertTo12HourFormat = (timeSlot: string) => {
    const [start, end] = timeSlot.split("-");
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  const { isMobile } = useIsMobile();
  const [popupData, setPopupData] = useState({
    title: "",
    subTitle: "",
    data: [],
    name: "",
    helperimg: "",
    isPhone: isPhoneNumber,
  });
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const wordLimit = 30; // Set your preferred word limit

  // Function to truncate text based on the word limit
  const truncateText = (text, wordLimit) => {
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };

  const clientType = utils.getCookie(CookiesConstant.clientType);
  //Time slots 12 hour format
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };
  let formattedDate = "";

  //date slots eg. FRI 14th NOvember
  if (jobClients && jobClients.jobDate) {
    const jobDate = new Date(jobClients.jobDate);
    function getDaySuffix(date) {
      const day = date.getDate();
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    }
    // Get the formatted string
    formattedDate = `${jobDate.toLocaleString("en-EN", {
      weekday: "short",
    })} ${jobDate.getDate()}${getDaySuffix(jobDate)} of ${jobDate.toLocaleString("en-EN", {
      month: "long",
    })}`;
  }
  const checkPhoneNumber = (applicant) => {
    if (!applicant) return false;
    return !!(applicant.awardedPhoneNumber || applicant.applicantPhoneNumber);
  };
  const responseTable = useMemo(() => {
    const result: Array<{
      dayOfWeek: number;
      day: string;
      startTime: string;
      endTime: string;
      shiftId: number;
      price: number;
      awardedAplicantId: number | null;
      jobType?: number; // Add this
      subJobType?: number; // Add this
      //   date:string
      applicants: Array<{
        applicantId: number;
        availabilityId: number;
        publicName: string;
        imageSrc: string;
        status: number;
      }>;
    }> = [];

    if (jobClients === null) return result;

    const dows = ["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"];
    const applicants = jobClients.applicants;
    const schedules = (jobClients?.weeklySchedule as WeeklySchedule)?.weeklyScheduleEntries || [];

    for (const schedule of schedules) {
      const data: {
        dayOfWeek: number;
        day: string;
        startTime: string;
        endTime: string;
        shiftId: number;
        price: number;
        awardedAplicantId: number | null;
        jobType?: number; // Add this
        subJobType?: number; // Add this
        // date:string;
        applicants: Array<{
          applicantId: number;
          availabilityId: number;
          publicName: string;
          imageSrc: string;
          status: number;
        }>;
      } = {
        dayOfWeek: schedule.dayOfWeek,
        day: dows[schedule.dayOfWeek],
        startTime: schedule.jobStartTime,
        endTime: schedule.jobEndTime,
        shiftId: schedule.id,
        price: schedule.price,
        awardedAplicantId: schedule.applicantId,
        jobType: jobClients.jobType, // Pass from jobClients
        subJobType: jobClients.jobSubType, // Pass from jobClients
        applicants: [],
        // date:schedule.
      };
      for (const applicant of applicants) {
        for (const availability of applicant.applicantAvailability) {
          function inPrevShift(params: { dow: number; id: number }) {
            const shift = result.find((r) => r.dayOfWeek === params.dow);
            if (!shift) return false;
            const app = shift.applicants.find((a) => a.availabilityId === params.id);
            if (!app) return false;
            return true;
          }
          if (
            availability.dayOfWeek === schedule.dayOfWeek &&
            !data.applicants.some((a) => a.applicantId === applicant.applicantId) &&
            !inPrevShift({
              dow: availability.dayOfWeek,
              id: availability.id,
            })
          ) {
            data.applicants.push({
              applicantId: applicant.applicantId,
              availabilityId: availability.id,
              publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
              imageSrc: applicant.applicantImageSrc,
              status: availability.availabilityStatus,
            });
          }
        }
      }
      result.push(data);
    }

    return result;
  }, [jobClients]);

  const awardedApplicants = useMemo(() => {
    if (!jobClients) return [];

    if (!jobClients.isRecurringJob && !jobClients.isTutoringJob) return [];

    const applicants = jobClients.applicants || [];

    // Filter by awarded status (e.g., 6 or your AWARDED constant)
    const uniqueApplicants = applicants.filter(
      (applicant) => applicant.applicationStatus === 6 // Replace 6 with your awarded status if different
    );

    return uniqueApplicants;
  }, [jobClients]);
  // const handleCancelJob = (id: number, tabIndex: string) => {
  //     // Show the confirmation popup
  //     showCancelJobPopup(
  //         'Are you sure?', // Heading
  //         'If you cancel this job all applicants will be notified and the canceled job will be moved to the Job History section of My Jobs. Are you sure you want to continue?', // Message
  //         'Go Back', // Cancel Text
  //         'Cancel Job', // Confirm Text
  //         <img src={remove} alt='confirm' style={{ height: '20px', width: '20px' }} />, // Confirm Icon
  //         () => {
  //             const payload = {
  //                 ...jobClients,
  //                 jobStatus: c.jobStatus.CANCELLED,
  //             };
  //          

  //             enableLoader();
  //             Service.jobClientAwardJobs(
  //                 async (response: Jobs) => {

  //                     await refresh();
  //                     const newSearchParams = new URLSearchParams();
  //                     newSearchParams.set('jobId', '-1');
  //                     newSearchParams.set('activeTab', tabIndex);
  //                     navigate({ search: newSearchParams.toString() });
  //                     disableLoader();
  //                 },
  //                 (error: any) => {
  //                     disableLoader();
  //                     console.error('Error cancelling job:', error);
  //                 },
  //                 id,
  //                 payload
  //             );
  //         },
  //         () => {
  //             // onCancel callback
  //             // Handle cancellation, maybe log or close popup

  //         },
  //         jobClients.awardedImageSrc
  //     );
  // };
  const handleViewJobIdChange = (newJobId: number, index: number) => {
    const updatedParams = new URLSearchParams(searchParams);
    updatedParams.delete("jobId");
    updatedParams.set("rateJobId", String(newJobId));
    updatedParams.set("activeTab", String(index));
    navigate({ search: updatedParams.toString() });
  };
  const handleViewJobIdChangeSecond = (jobId: number, applicantId: number, tabIndex: number) => {
    const updatedParams = new URLSearchParams(searchParams);
    updatedParams.delete("jobId");
    updatedParams.set("rateJobId", String(jobId));
    updatedParams.set("applicantId", String(applicantId)); // Add applicantId to URL
    updatedParams.set("activeTab", String(tabIndex));
    navigate({ search: updatedParams.toString() });
  };
  function formatDate(date: Date, format: "full" | "short" | "custom" = "full"): string {
    switch (format) {
      case "full":
        return date.toLocaleDateString("en-US", {
          day: "numeric",
          month: "long",
          year: "numeric",
        }); // "November 25, 2025"

      case "short":
        return date.toLocaleDateString("en-US", {
          day: "numeric",
          month: "short",
        }); // "Nov 25"

      case "custom":
        return `${date.getDate()} ${date.toLocaleString("default", {
          month: "short",
        })} ${date.getFullYear()}`;
      // "25 Nov 2025"
    }
  }

  const awardedHelpers = () => {
    const result: Array<{
      name: string;
      imageSrc: string;
      location: string;
      phoneNumber: string;
      bsb: string;
      bankNumber: string;
      accountName: string;
      ratingAvg: number;
      ratingCount: number;
      useId: number;
    }> = [];
    if (!jobClients) return result;

    if (!jobClients.isRecurringJob && !jobClients.isTutoringJob) {
      return result;
    }
    for (const entries of (jobClients.weeklySchedule as WeeklySchedule).weeklyScheduleEntries) {
      const applicant = jobClients.applicants.find((a) => a.applicantId === entries.applicantId);

      if (applicant && applicant.applicationStatus === c.jobApplicationStatus.AWARDED) {
        result.push({
          name: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
          imageSrc: applicant.applicantImageSrc,
          location: applicant.suburb,
          phoneNumber: applicant.applicantPhoneNumber,
          bsb: applicant.applicantBankAccountBsb,
          bankNumber: applicant.applicantBankAccountNumber,
          accountName: applicant.applicantBankAccountName,
          ratingAvg: applicant.applicantRatingsAvg,
          ratingCount: applicant.applicantRatingsCount,
          useId: applicant.applicantId,
        });
      }
    }

    return result;
  };

  const handleActionClick = (action, subTitleData, detailData, name?, helperimg?, isPhone?) => {
    // Define popup content dynamically

    const popupContent = {
      title: `${action}`,
      subTitle: subTitleData,
      data: detailData,
      name: name,
      helperimg: helperimg,
      isPhone: isPhone,
    };
    setIsPhoneNumber(isPhone);
    // Update popup state
    setPopupData(popupContent);
    setIsPopupVisible(true);
  };

  useEffect(() => {
    const fetchData = () => {
      enableLoader();
      Service.jobClientDetails(
        (response: Jobs) => {
          setJobClients(response);
          disableLoader();
        },
        (error: any) => {
          console.error("Error fetching data:", error);
          disableLoader();
        },
        jobId
      );
    };
    fetchData();
  }, [activeTab]);
  const getJobDetails = (jobclientProp) => {
    const jobToUse = jobclientProp || { jobType: 0 };

    switch (jobToUse.jobType) {
      case 256:
        return {
          label: "Odd Job",
        };
      case 64:
        return {
          label: "Primary School",
        };
      case 128:
        return {
          label: "High School",
        };
      default:
        return {
          label: "Childcare",
        };
    }
  };
  const calculateTotalPrice = (startTime: string, endTime: string, hourlyRate: number): number => {
    const [startHours, startMinutes] = startTime.split(":").map(Number);
    const [endHours, endMinutes] = endTime.split(":").map(Number);

    const startInMinutes = startHours * 60 + startMinutes;
    const endInMinutes = endHours * 60 + endMinutes;

    let totalMinutes = endInMinutes - startInMinutes;
    if (totalMinutes < 0) {
      totalMinutes += 24 * 60; // Adjust for overnight jobs
    }

    const totalHours = totalMinutes / 60;
    return totalHours * hourlyRate;
  };
  const calculateHours = (startTime: string, endTime: string): number => {
    const [startHours, startMinutes] = startTime.split(":").map(Number);
    const [endHours, endMinutes] = endTime.split(":").map(Number);

    // Convert to minutes for easier calculation
    const startInMinutes = startHours * 60 + startMinutes;
    const endInMinutes = endHours * 60 + endMinutes;

    // Calculate total minutes difference
    let totalMinutes = endInMinutes - startInMinutes;

    // If end time is less than start time, assume it crosses midnight
    if (totalMinutes < 0) {
      totalMinutes += 24 * 60; // Add 24 hours in minutes
    }

    // Convert to hours
    const totalHours = totalMinutes / 60;
    return totalHours;
  };

  const calculateWeeklyScheduleHours = () => {
    if (!jobClients || (!jobClients.isRecurringJob && !jobClients.isTutoringJob)) {
      return 0; // Return 0 if not recurring or tutoring
    }

    const scheduleEntries = jobClients.weeklySchedule?.weeklyScheduleEntries || [];
    let totalHours = 0;

    scheduleEntries.forEach((entry) => {
      const hours = calculateHours(entry.jobStartTime, entry.jobEndTime);
      totalHours += hours;
    });

    return totalHours;
  };

  const calculatePriceBasedOnDuration = (startTime: string, endTime: string, hourlyPrice: number) => {
    // Convert times to Date objects for calculation
    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(`2000-01-01 ${endTime}`);

    // If end time is before start time, assume it crosses midnight
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    // Calculate hours difference
    const diffMs = end.getTime() - start.getTime();
    const hours = diffMs / (1000 * 60 * 60);

    // Calculate total price
    const totalPrice = hours * hourlyPrice;

    return {
      hours: hours.toFixed(1), // Return hours with 1 decimal place
      totalPrice: totalPrice.toFixed(2), // Return price with 2 decimal places
    };
  };
  const calculateHourlyRate = (totalPrice, hours) => {
    if (hours <= 0) return 0;
    return totalPrice / hours;
  };
  const formatShortDate = (jobDate: string | null): string => {
    if (!jobDate) return "";

    const date = new Date(jobDate);
    const day = date.toLocaleString("en-EN", { weekday: "short" });
    const dayOfMonth = date.getDate();
    const month = date.toLocaleString("en-EN", { month: "short" });
    const year = date.getFullYear().toString().slice(-2); // Get last two digits of year

    return `${day} ${dayOfMonth} ${month} ${year}`;
  };
  return jobClients ? (
    !isMobile ? (
      <>

        <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
        <Dialog
          visible={dialogVisible}
          onHide={() => setDialogVisible(false)}
          content={
            <div className={styles.dialogContent}>
              <IoClose
                onClick={() => setDialogVisible(false)}
                className={styles.CloseBtn}
              />
              <div>
                <h1
                  style={{
                    fontSize: '32px',
                    fontWeight: '700',
                    color: '#585858',
                    margin: '0px',
                  }}
                >
                  Chat
                </h1>
              </div>
              <Divider />

              <div>
                <p
                  style={{
                    fontSize: '20px',
                    fontWeight: '500',
                    color: '#585858',
                  }}
                >
                  {chatErrorMessage}
                </p>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '20px' }}>
                  <button
                    className={styles.buttonPost}
                    onClick={() => setDialogVisible(false)}
                  >
                    Ok
                  </button>
                  {/* <button
                                            className={styles.buttonPost}
                                            onClick={() => {
                                                if (clientType === '1') {
                                                    navigate('/parent-home/job/post/job-type');
                                                } else if (clientType === '2') {
                                                    navigate('/business-home/job/post/job-type');
                                                }
                                            }}
                                        >
                                            Post Job
                                        </button> */}
                </div>
              </div>
            </div>
          }
        />
        <Dialog
          visible={duplicateJob}
          onHide={() => {
            setDuplicateJob(false);
          }}
          style={{
            width: "100vw",
            height: "100vh",
            maxHeight: "none",
            maxWidth: "none",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
          content={
            <div
              className="flex flex-column py-3 px-4"
              style={{
                backgroundColor: "#FFFFFF",
                border: "1px solid #F0F4F7",
                borderRadius: "20px",
                maxWidth: "610px",
              }}
            >
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "32px",
                  color: "#585858",
                }}
              >
                Duplicate Job
              </h1>
              <Divider />
              <p
                className="m-0 p-0 mt-3"
                style={{
                  fontWeight: "500",
                  fontSize: "16px",
                  color: "#585858",
                }}
              >
                Use this feature to post a new job based on the current job details, all applicable job settings are copied.
              </p>
              <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
                <button
                  className="px-4 py-1 cursor-pointer"
                  style={{
                    backgroundColor: "#FFFFFF",
                    border: "none",
                    boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                    fontWeight: "500",
                    fontSize: "18px",
                    color: "#585858",
                    textDecoration: "underline",
                    borderRadius: "5px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setDuplicateJob(false);
                  }}
                >
                  No
                </button>
                <button
                  className="px-7 py-1 cursor-pointer"
                  style={{
                    backgroundColor: "#FFA500",
                    border: "none",
                    boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                    fontWeight: "700",
                    fontSize: "18px",
                    color: "#FFFFFF",
                    textDecoration: "underline",
                    borderRadius: "5px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setDuplicateJob(false);
                    const newParams = new URLSearchParams();
                    const action = duplicateJob ? "Duplicate" : "Edit";
                    // newParams.set("jobId", String(jobClients.id));
                    newParams.set("jobaction", action); // ← fixed typo here
                    if (Number(clientType) === 1) {
                      navigate({
                        pathname: `/parent-home/job/${jobClients.id}/job-type`,
                        search: newParams.toString(),
                      });
                      return;
                    }
                    navigate({
                      pathname: `/business-home/job/${jobClients.id}/job-type`,
                      search: newParams.toString(),
                    });
                  }}
                >
                  Yes
                </button>
              </div>
            </div>
          }
        />

        <div
          className="flex gap-2 justify-content-around align-items-center w-min cursor-pointer mb-3"
          style={{
            textWrap: "nowrap",
            border: "1px solid #F1F1F1",
            padding: "10px 25px",
            borderRadius: "20px",
          }}
          onClick={(e) => {
            e.preventDefault();
            const newGoBackParams = new URLSearchParams();
            newGoBackParams.set("jobId", "-1");
            newGoBackParams.set("activeTab", activeTab);
            navigate({ search: newGoBackParams.toString() });
          }}
        >
          <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
          <p
            className="m-0 p-0"
            style={{
              fontWeight: "400",
              fontSize: "14px",
              color: "#585858",
            }}
          >
            Go Back
          </p>
        </div>

        <div
          className="mb-2"
          style={{
            width: "826px",
            borderRadius: "20px",
            paddingLeft: "30px",
            paddingRight: "20px",
            marginRight: "2%",
            border: "1px solid #dfdfdf",
          }}
        >
          <div className="flex justify-content-between align-items-center">
            <h3
              className="font-bold"
              style={{
                color: "#585858",
                fontSize: "30px",
                marginBottom: "15px",
              }}
            >
              Job Summary
            </h3>
            {/* <div className={`${styles.jobPrice}`}>
              <p
                className="font-normal"
                style={{ textAlign: "end", color: "#585858", fontSize: "16px" }}
              >
                {jobClients.isTutoringJob
                  ? "Price Per Week = "
                  : jobClients.isRecurringJob
                  ? "Weekly Job Price = "
                  : "Price = "}

                <strong className={styles.jobPriceDoller}>
                  $
                  {jobClients.isTutoringJob
                    ? jobClients.pricePerWeek
                    : jobClients.isRecurringJob
                    ? jobClients.pricePerWeek
                    : jobClients.price}
                </strong>
              </p>
            </div> */}
          </div>

          <div className={styles.tagButton}>
            {/* First Line: Calendar, Clock, Payment Method, Address */}
            <div
              style={{
                display: "flex",
                flexWrap: "nowrap", // Prevent tags from wrapping to next line
                gap: "8px", // Space between tags
                width: "100%",
                justifyContent: "flex-start", // Left-align tags
                alignItems: "flex-start", // Align to top for dynamic heights
                marginBottom: "8px", // Space between rows
              }}
            >
              {/* Tag 1: Calendar */}
              <Tag style={{ background: "#2F9ACD", padding: "4px 8px" }}>
                <div className={styles.tagData} style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                  <img
                    alt="Calendar"
                    src={calendar}
                    style={{ width: "16px", color: "#FFFFFF" }} // Reduced from 18px
                  />
                  {formattedDate}
                </div>
              </Tag>

              {/* Tag 2: Clock */}
              <Tag style={{ background: "#8577DB", padding: "4px 8px" }}>
                <div className={styles.tagData} style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                  <img
                    alt="Clock"
                    src={clock}
                    style={{ width: "16px", color: "#FFFFFF" }} // Reduced from 18px
                  />
                  {jobClients.isTutoringJob
                    ? `${jobClients.duration} week duration`
                    : jobClients.isRecurringJob
                      ? `${jobClients.duration} week duration`
                      : convertTo12HourFormat(`${jobClients["jobStartTime"]}-${jobClients["jobEndTime"]}`)}
                </div>
              </Tag>

              {/* Tag 3: Payment Method */}
              <Tag style={{ background: "#77DBC9", padding: "4px 8px" }}>
                <div className={styles.tagData} style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                  <img
                    alt="Dollar"
                    src={doller}
                    style={{ width: "8px", color: "#FFFFFF" }} // Reduced from 10px
                  />
                  {jobClients.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer"}
                </div>
              </Tag>

              {/* Tag 4: Address */}
              <Tag
                style={{
                  background: "#179D52",
                  padding: "4px 8px",
                  maxWidth: "242px", // Fixed width to stay in first line
                  flex: "0 0 auto", // Prevent stretching
                }}
              >
                <div
                  className={styles.tagData}
                  style={{
                    display: "flex",
                    alignItems: "flex-start", // Align icon and text to top
                    gap: "4px",
                  }}
                >
                  <img
                    alt="Home"
                    src={home}
                    style={{ width: "16px", color: "#FFFFFF", flexShrink: 0 }} // Reduced from 18px
                  />
                  <div
                    style={{
                      wordBreak: "break-word",
                      maxWidth: "200px", // Constrain text for wrapping
                    }}
                  >
                    {`${utils.cleanAddress(jobClients.formattedAddress)}`}
                  </div>
                </div>
              </Tag>
            </div>

            {/* Second Line: Do It Yourself/Juggle Assist */}
            <div
              style={{
                display: "flex",
                flexWrap: "wrap", // Allow wrapping (though only one tag)
                gap: "8px",
                width: "100%",
                justifyContent: "flex-start",
                alignItems: "center",
              }}
            >
              {/* Tag 5: Managed By */}
              <Tag style={{ background: "#8bb8ff", padding: "4px 8px" }}>
                <div className={styles.tagData} style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                  <MdOutlineHomeWork style={{ fontSize: "18px", color: "#FFFFFF" }} /> {/* Reduced from 20px */}
                  {jobClients.managedBy === 1 ? "Do It Yourself" : "Juggle Assist"}
                </div>
              </Tag>
            </div>
          </div>
          {/* {
                        jobClients.managedBy === 20 ?
                            <div className="flex flex-wrap gap-1 mt-2">
                                <h4 className={`${styles.headerH4} p-0 m-0`}>
                                    Status:
                                </h4>
                                <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                    {jobClients.jobStatus === c.jobStatus.PENDING ? "Processing" : jobClients.jobStatus === c.jobStatus.AWARDED ? "award" : jobClients.jobStatus}
                                </p>
                            </div>
                            : null

                    } */}
          {/* {filteredApplicantFilters?.length > 0 && (
                        <div>
                            <h4 className="p-0 m-0 mt-2" style={{ color: '#585858' }}>Candidate Criteria: </h4>
                            <div>
                                {filteredApplicantFilters.map((filter, index) => {
                                    let displayValue = '';
                                    let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                                    if (filter.field === 'distance') {
                                        displayValue = distanceMapping[filter.value];
                                    } else if (filter.field === 'age') {
                                        if (filter.value === 0) {
                                            displayValue = Object.values(ageMapping).join(', ');
                                        } else {
                                            displayValue = (filter.value as number[])
                                                .map((val) => ageMapping[val])
                                                .join(', ');
                                        }
                                    } else if (filter.field === 'otherSkills') {
                                        displayValue = (filter.value as number[])
                                            .map((val) => otherSkillsMapping[val])
                                            .join(', ');
                                        displayField = 'Other';
                                    }
                                    else if (filter.field === 'tutoringCategory') {
                                        displayValue = (filter.value as number[])
                                            .map((val) => tutoringCategoryMapping[val])
                                            .join(', ');
                                        displayField = 'Experience';
                                    }
                                    return (
                                        <div key={index} className={''}>
                                            <span className={''} style={{ color: '#585858', fontWeight: '600' }}>
                                                {displayField}:&nbsp;
                                            </span>
                                            <span className={''} style={{ color: '#585858' }}>
                                                {displayValue}
                                            </span>
                                        </div>
                                    );
                                })}
                            </div>

                        </div>
                    )} */}
          <div className="flex flex-wrap gap-1 mt-2">
            <h4 className={`${styles.headerH4} p-0 m-0 `}>Job Description:</h4>
            <p className={`${styles.yourJobDescription} p-0 m-0`}>{jobClients.specialInstructions}</p>
          </div>
          <Divider className="my-4" />
          <div>{jobClients.isRecurringJob || jobClients.isTutoringJob ? <JobHistoryTable rows={responseTable} /> : null}</div>

          <div className="flex flex-row gap-4">
            {jobClients.jobStatus === c.jobStatus.COMPLETED ? (
              <>
                <div>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "700",
                    }}
                    className="p-0 m-0"
                  >
                    Hours
                  </p>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "300",
                    }}
                    className="p-0 m-0"
                  >
                    {jobClients.isRecurringJob || jobClients.isTutoringJob
                      ? `${calculateWeeklyScheduleHours()} Hours Completed`
                      : jobClients.jobStartTime && jobClients.jobEndTime
                        ? `${calculateHours(jobClients.jobStartTime, jobClients.jobEndTime)} Hours Completed`
                        : "N/A"}
                  </p>
                </div>
                <Divider layout="vertical" />
              </>
            ) : null}
            {!jobClients.isTutoringJob && !jobClients.isRecurringJob && (
              <>
                <div>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "700",
                    }}
                    className="p-0 m-0"
                  >
                    Hourly Rate
                  </p>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "300",
                    }}
                    className="p-0 m-0"
                  >
                    {jobClients.price} Per Hour
                  </p>
                </div>
                <Divider layout="vertical" />
                <div className="flex flex-column ">
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "700",
                    }}
                    className="p-0 m-0"
                  >
                    Overtime
                  </p>
                  <p
                    style={{
                      fontSize: "14px",
                      color: "#585858",
                      fontWeight: "300",
                    }}
                    className="p-0 m-0"
                  >
                    {jobClients.overtimeRate != null ? `${jobClients.overtimeRate} Per Hour` : "N/A"}
                  </p>
                </div>
              </>
            )}
            <Divider layout="vertical" />
            <div className="flex flex-column ">
              <p
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "700",
                }}
                className="p-0 m-0"
              >
                Total Price
              </p>
              <p
                style={{
                  fontSize: "14px",
                  color: "#585858",
                  fontWeight: "300",
                }}
                className="p-0 m-0"
              >
                {jobClients.isTutoringJob || jobClients.isRecurringJob
                  ? jobClients.pricePerWeek
                    ? `$${jobClients.pricePerWeek.toFixed(2)}`
                    : "N/A"
                  : jobClients.jobStartTime && jobClients.jobEndTime && jobClients.price
                    ? `$${calculateTotalPrice(jobClients.jobStartTime, jobClients.jobEndTime, jobClients.price).toFixed(2)}`
                    : "N/A"}
              </p>
            </div>
            <Divider layout="vertical" />
          </div>
        </div>

        {/* <JobAnalytics job={jobClients} /> */}

        {(() => {
          const awardedApplicant = jobClients.applicants.find((applicant) => applicant.applicantId === jobClients.awardedApplicantId);

          return (
            <div className="mt-2">
              {/* <h3
                                className='font-bold p-0 m-0 mt-3 mb-4'
                                style={{ color: '#585858', fontSize: '30px' }}
                            >
                                {jobClients.isTutoringJob || jobClients.isRecurringJob
                                    ? 'Helpers Details'
                                    : 'Helper Details'}
                            </h3> */}

              {jobClients.jobStatus !== c.jobStatus.CANCELLED && (
                <>
                  {!jobClients.isTutoringJob && !jobClients.isRecurringJob ? (
                    <JobAwardedCard
                      statusShow="Completed"
                      image={jobClients.awardedImageSrc || awardedApplicant?.applicantImageSrc}
                      name={`${jobClients.awardedFirstName || awardedApplicant?.applicantFirstName || "Anonymous"} ${jobClients.awardedLastInitial || "Anonymous"
                        }`}
                      rating={awardedApplicant?.applicantRatingsAvg ?? 0}
                      ratingCount={awardedApplicant?.applicantRatingsCount ?? 0}
                      location={awardedApplicant?.suburb ?? "Unknown Location"}
                      onContactNumber={() =>
                        handleActionClick(`${jobClients.awardedFirstName} Contact Number`, null, [
                          {
                            text: null,
                            boldText: jobClients.awardedPhoneNumber || "Not Available", // Phone number in bold
                            fontSize: "32px",
                          },
                        ])
                      }
                      onBankDetails={() =>
                        handleActionClick(
                          `Bank Transfer Details`,

                          null,
                          [
                            {
                              text: "Bank: ",
                              boldText: jobClients.awardedBankAccountName || "Not Available", // Phone number in bold
                              fontSize: "30px",
                            },
                            {
                              text: "BSP: ",
                              boldText: jobClients.awardedBankAccountBsb || "Not Available", // Phone number in bold
                              fontSize: "30px",
                            },
                            {
                              text: "Account Details: ",
                              boldText: jobClients.awardedBankAccountNumber || "Not Available", // Phone number in bold
                              fontSize: "30px",
                            },
                          ]
                        )
                      }
                      showBank={showBank}
                      showTransport={showTransport}
                      {...(showTransport && {
                        onTransport: () => {
                          const getTransportMessage = (option) => {
                            switch (option) {
                              case 0:
                                return "Unspecified";
                              case 1:
                                return "Needs you to provide transport";
                              case 2:
                                return "Walking";
                              case 3:
                                return "Public Transport";
                              case 4:
                                return "Car (Being picked up)";
                              case 5:
                                return "Driving";
                              case 6:
                                return "Uber / Taxi";
                              default:
                                return "Not Available";
                            }
                          };

                          handleActionClick(`Getting Home`, `How is the Helper getting home after this job?`, [
                            {
                              text: null,
                              boldText: getTransportMessage(awardedApplicant?.gettingHome),
                              fontSize: "30px",
                            },
                          ]);
                        },
                      })}
                      onChatClick={() => {
                        const applicantId = jobClients.applicants[0]?.applicantId;
                        if (ischateligible === 0) {
                          setChatErrorMessage("You need an active membership to chat with Juggle St users.");
                          setDialogVisible(true);
                          return;
                        }
                        {
                          if (clientType === "1") {
                            navigate(`/parent-home/inAppChat?userId=${applicantId}`);
                          } else {
                            navigate(`/business-home/inAppChat?userId=${applicantId}`);
                          }
                        }
                      }}
                    />
                  ) : (
                    awardedApplicants.map((a, index) => (
                      <JobAwardedCard
                        statusShow="Completed"
                        key={index}
                        image={a.applicantImageSrc}
                        name={`${a.applicantFirstName} ${a.applicantLastInitial}`}
                        rating={a.applicantRatingsAvg}
                        ratingCount={a.applicantRatingsCount}
                        location={a.suburb}
                        onRateClick={() => {
                          // Pass both jobId and applicantId
                          handleViewJobIdChangeSecond(jobClients.id, a.applicantId, 3);
                        }}
                        isTutorRecuring={true}
                        hasbeenRated={a.hasBeenRated}
                        onContactNumber={() =>
                          handleActionClick(`${a.applicantFirstName} Contact Number`, null, [
                            {
                              text: null,
                              boldText: a.applicantPhoneNumber || "Not Available", // Phone number in bold
                              fontSize: "32px",
                            },
                          ])
                        }
                        onBankDetails={() =>
                          handleActionClick(
                            `Bank Transfer Details`,

                            null,
                            [
                              {
                                text: "Bank: ",
                                boldText: a.applicantBankAccountName || "Not Available", // Phone number in bold
                                fontSize: "30px",
                              },
                              {
                                text: "BSP: ",
                                boldText: a.applicantBankAccountBsb || "Not Available", // Phone number in bold
                                fontSize: "30px",
                              },
                              {
                                text: "Account Details: ",
                                boldText: a.applicantBankAccountNumber || "Not Available", // Phone number in bold
                                fontSize: "30px",
                              },
                            ]
                          )
                        }
                        showBank={showBank}
                        showTransport={!showTransport}
                        {...(!showTransport && {
                          onTransport: () => {
                            const getTransportMessage = (option) => {
                              switch (option) {
                                case 0:
                                  return "Unspecified";
                                case 1:
                                  return "Needs you to provide transport";
                                case 2:
                                  return "Walking";
                                case 3:
                                  return "Public Transport";
                                case 4:
                                  return "Car (Being picked up)";
                                case 5:
                                  return "Driving";
                                case 6:
                                  return "Uber / Taxi";
                                default:
                                  return "Not Available";
                              }
                            };

                            handleActionClick(`Getting Home`, `How is the Helper getting home after this job?`, [
                              {
                                text: null,
                                boldText: getTransportMessage(awardedApplicant?.gettingHome),
                                fontSize: "30px",
                              },
                            ]);
                          },
                        })}
                        onChatClick={() => {
                          if (ischateligible === 0) {
                            setChatErrorMessage("You need an active membership to chat with Juggle St users.");
                            setDialogVisible(true);
                            return;
                          }
                          if (clientType === "1") {
                            navigate(`/parent-home/inAppChat?userId=${a.applicantId}`);
                          } else {
                            navigate(`/business-home/inAppChat?userId=${a.applicantId}`);
                          }
                        }}
                      />
                    ))
                  )}
                </>
              )}
              <AwardedDetailsPopup
                visible={isPopupVisible}
                onHide={() => setIsPopupVisible(false)}
                title={popupData.title}
                subTitle={popupData.subTitle}
                data={popupData.data}
              />
            </div>
          );
        })()}

        <div className="gap-2 flex mt-4 ">
          <button
            className="cursor-pointer font-bold font-bold flex justify-content-center align-items-center gap-2"
            style={{
              backgroundColor: "#fff",
              color: "#585858",
              border: "1px solid #585858",
              padding: "10px 10px",
              borderRadius: "5px",
              fontSize: "12px",
              width: "476px",
            }}
            onClick={() => setDuplicateJob(true)}
          >
            Duplicate Job
          </button>
          {!jobClients.isRatedByClient ? (
            <>
              {jobClients.jobStatus !== c.jobStatus.CANCELLED ? (
                <>
                  {!jobClients.isRecurringJob && !jobClients.isTutoringJob && (
                    <button
                      className="cursor-pointer font-bold flex justify-content-center align-items-center gap-2"
                      style={{
                        backgroundColor: "#FFA500",
                        color: "#FFFFFF",
                        border: "none",
                        padding: "10px 10px",
                        borderRadius: "5px",
                        fontSize: "12px",
                        width: "476px",
                      }}
                      onClick={() => handleViewJobIdChange(jobClients.id, 3)}
                    >
                      Rate {jobClients.awardedFirstName}
                    </button>
                  )}
                </>
              ) : (
                <button
                  className="font-bold flex justify-content-center align-items-center gap-2"
                  style={{
                    backgroundColor: "#FF63594D",
                    color: "var(--Alert-colour, #FF6359)",
                    border: "1px solid #FF6359",
                    padding: "10px 10px",
                    borderRadius: "5px",
                    fontSize: "12px",
                    width: "476px",
                  }}
                >
                  This job has been Cancelled.
                </button>
              )}
            </>
          ) : (
            <>
              {!jobClients.isRecurringJob && !jobClients.isTutoringJob && (
                <button
                  className=" font-bold font-bold flex justify-content-center align-items-center gap-2"
                  style={{
                    backgroundColor: "#fff",
                    color: "#179D52",
                    border: "1px solid #179D52",
                    padding: "10px 10px",
                    borderRadius: "5px",
                    fontSize: "12px",
                    width: "476px",
                  }}
                >
                  Already Rated
                </button>
              )}
            </>
          )}
        </div>

        {/* <div style={{ marginRight: '2%', width: '952px' }}>
                    <JobFooter
                        expiryDate={jobClients.expiresInDays}
                        managedBy={jobClients.managedBy}
                    />
                </div> */}

        <ViewJobFull
          visible={showfullJob}
          onClose={() => {
            setShowFullJob(false);
          }}
          job={jobClients}
        />
        <div style={{ minHeight: "50px" }}></div>
      </>
    ) : (
      <>
        <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
        <Dialog
          visible={dialogVisible}
          onHide={() => setDialogVisible(false)}
          content={
            <div className={styles.dialogContentMobile}>
              <IoClose
                onClick={() => setDialogVisible(false)}
                className={styles.CloseBtn}
              />
              <div>
                <h1
                  style={{
                    fontSize: '20px',
                    fontWeight: '700',
                    color: '#585858',
                    margin: '0px',
                  }}
                >
                  Chat
                </h1>
              </div>
              <Divider />

              <div>
                <p
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#585858',
                  }}
                >
                  {chatErrorMessage}
                </p>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '20px' }}>
                  <button
                    className={styles.buttonPostMobile}
                    onClick={() => setDialogVisible(false)}
                  >
                    Ok
                  </button>
                  {/* <button
                                    className={styles.buttonPost}
                                    onClick={() => {
                                        if (clientType === '1') {
                                            navigate('/parent-home/job/post/job-type');
                                        } else if (clientType === '2') {
                                            navigate('/business-home/job/post/job-type');
                                        }
                                    }}
                                >
                                    Post Job
                                </button> */}
                </div>
              </div>
            </div>
          }
        />
        <Dialog
          visible={duplicateJob}
          onHide={() => {
            setDuplicateJob(false);
          }}
          style={{
            width: "100vw",
            height: "100vh",
            maxHeight: "none",
            maxWidth: "none",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
          position="bottom"
          content={
            <div
              className="flex flex-column py-3 px-4"
              style={{
                backgroundColor: "#FFFFFF",
                borderTop: "2px solid  #179D52",
                borderRight: "2px solid  #179D52",
                borderLeft: "2px solid  #179D52",
                borderTopLeftRadius: "30px",
                borderTopRightRadius: "30px",
                width: "100%",
                position: "fixed",
                bottom: "0px",
                left: "0px",
              }}
            >
              <h1
                className="mb-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "22px",
                  color: "#585858",
                }}
              >
                Duplicate Job
              </h1>
              <Divider className="mt-2" />
              <p
                className="m-0 p-0 mt-3"
                style={{
                  fontWeight: "500",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                Use this feature to post a new job based on the current job details, all applicable job settings are copied.
              </p>
              <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
                <button
                  className="px-4 py-1 cursor-pointer"
                  style={{
                    backgroundColor: "#FFFFFF",
                    border: "none",
                    boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                    fontWeight: "500",
                    fontSize: "16px",
                    color: "#585858",
                    textDecoration: "underline",
                    borderRadius: "5px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setDuplicateJob(false);
                  }}
                >
                  No
                </button>
                <button
                  className="px-7 py-1 cursor-pointer"
                  style={{
                    backgroundColor: "#FFA500",
                    border: "none",
                    boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                    fontWeight: "700",
                    fontSize: "16px",
                    color: "#FFFFFF",
                    textDecoration: "underline",
                    borderRadius: "5px",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    setDuplicateJob(false);
                    IframeBridge.sendToParent({
                      type: "navigateToPostJob",
                      data: {
                        id: jobClients.id,
                        action: "Duplicate",
                      },
                    });
                    if (!inIframe) {
                      const newParams = new URLSearchParams();
                      const action = duplicateJob ? "Duplicate" : "Edit";
                      newParams.set("jobaction", action);
                      // newParams.set("jobId", String(jobClients.id));
                      if (Number(clientType) === 1) {
                        navigate({
                          // pathname: "/parent-home/post-job/duplicate",
                          pathname: `/parent-home/job/${jobClients.id}/job-type`,
                          search: newParams.toString(),
                        });
                        return;
                      }
                      navigate({
                        // pathname: "/business-home/post-job/duplicate",
                        pathname: `/business-home/job/${jobClients.id}/job-type`,
                        search: newParams.toString(),
                      });
                    }
                  }}
                >
                  Yes
                </button>
              </div>
            </div>
          }
        />

        <div className="relative">
          {/* Go Back Button */}
          <div
            className="flex gap-1 align-items-center w-min cursor-pointer mb-2"
            style={{
              textWrap: "nowrap",
              position: "absolute",
              left: "-10px",
              top: "3px",
              borderRadius: "20px",
              zIndex: "2",
            }}
            // onClick={(e) => {
            //   e.preventDefault();
            //   const newGoBackParams = new URLSearchParams();
            //   newGoBackParams.set("jobId", "-1");
            //   newGoBackParams.set("activeTab", activeTab);
            //   navigate({ search: newGoBackParams.toString() });
            // }}

            onClick={(e) => {
              window.history.back();
            }}
          >
            <div
              style={{
                backgroundColor: "#D9D9D94D",
                paddingInline: "8px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                paddingBlock: "8px",
                borderRadius: "50%",
              }}
            >
              <img src={BackArrow} alt="Arrow Left" width="10px" height="8px" />
            </div>
            <p
              className="m-0 p-0"
              style={{
                fontWeight: "500",
                fontSize: "14px",
                color: "#585858",
              }}
            >
              Go Back
            </p>
          </div>

          {/* Job Summary Title */}
          <div className="flex justify-content-center align-items-center relative">
            <h3
              className="font-bold"
              style={{
                color: "#585858",
                fontSize: "18px",
                margin: "0px",
                textDecoration: "underline",
                marginTop: "0px",
                marginBottom: "5px",
              }}
            >
              Job Summary
            </h3>
          </div>
        </div>

        <div
          className="mt-2"
          style={{
            width: "100%",
            borderRadius: "20px",
          }}
        >
          <div>
            {!jobClients.isRecurringJob && !jobClients.isTutoringJob && (
              <div style={{ fontSize: "14px", color: "#585858", fontWeight: "700" }}>
                {`${getJobDetails(jobClients).label} - ${jobClients.isTutoringJob ? "Tutoring Job" : jobClients.isRecurringJob ? "Recurring Job" : "One Off Job"
                  }`}
              </div>
            )}

            <div className="flex flex-row gap-1">
              <p className={`${styles.summeryInfo} m-0 p-0`}>
                {jobClients.isRecurringJob && "You awarded a recurring job starting on "}
                {jobClients.isTutoringJob && "Starting on "}
                {formatShortDate(jobClients?.jobDate)},
                {clientType === "0" ? (
                  <Tag style={{ background: "transparent", width: "max-content", display: "inline-flex" }}>
                    <div className={styles.tagDataMobile}>
                      <img alt="Clock" src={clockStart} style={{ width: "14px" }} />
                      {jobClients.durationType === null
                        ? convertTo12HourFormat(`${jobClients.jobStartTime}-${jobClients.jobEndTime}`)
                        : `${jobClients.duration} week duration`}
                    </div>
                  </Tag>
                ) : (
                  <span className={styles.summeryInfo}>
                    {jobClients.isTutoringJob
                      ? ` for ${jobClients.duration} weeks`
                      : jobClients.isRecurringJob
                        ? ` for ${jobClients.duration} weeks`
                        : ` ${convertTo12HourFormat(`${jobClients.jobStartTime}-${jobClients.jobEndTime}`)}`}
                  </span>
                )}
              </p>
            </div>
          </div>
          <div className="flex flex-column ">
            {(jobClients.isRecurringJob || jobClients.isTutoringJob) && <p className={styles.addressTag}>{jobClients.addressLabel}</p>}
            {clientType !== "0" ? <div className={styles.summeryInfo}>{`${utils.cleanAddress(jobClients.formattedAddress)}`}</div> : null}
          </div>

          <div className="flex flex-column mt-3">
            {!jobClients.isRecurringJob && (
              <p className="m-0 p-0" style={{ fontSize: "14px", color: "#585858", fontWeight: "700" }}>
                Payment
              </p>
            )}
            {jobClients.isTutoringJob && (
              <>
                <div className="flex" style={{ color: "#585858", fontSize: "14px" }}>
                  Payment: $&nbsp;
                  <p className="m-0 p-0">{`${jobClients.price} p/h`}</p>
                </div>
              </>
            )}
            {clientType !== "0" && jobClients.price ? (
              <div className={`${styles.jobPrice}`}>
                {/* Tutoring Job */}
                {/* {jobClients.isTutoringJob && (
                <div className="flex" style={{ color: "#585858", fontSize: "14px" }}>
                  Price Per Week = 
                  <p className="m-0 p-0">${jobClients.pricePerWeek}</p>
                </div>
              )} */}

                {/* Recurring Job */}
                {/* {jobClients.isRecurringJob && (
                <div className="flex" style={{ color: "#585858", fontSize: "14px" }}>
                  Weekly Job Total = 
                  <p className="m-0 p-0">${jobClients.pricePerWeek}</p>
                </div>
              )} */}

                {/* Odd Job (one-time job) */}
                {!jobClients.isTutoringJob && !jobClients.isRecurringJob && (
                  <>
                    <div className="flex" style={{ color: "#585858", fontSize: "14px" }}>
                      Price: $&nbsp;
                      <p className="m-0 p-0">{jobClients.paymentType !== 1 ? `${jobClients.price} p/h` : `${jobClients.price} total`}</p>
                    </div>

                    {jobClients.overtimeRate && (
                      <div className={`${styles.summeryInfo} flex`}>Extra hours rate: ${jobClients.overtimeRate} per hour</div>
                    )}
                  </>
                )}
              </div>
            ) : (
              ""
            )}
            {!jobClients.isRecurringJob && (
              <div className={`${styles.summeryInfo}`}>Payment Method: {jobClients.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer"}</div>
            )}
            {jobClients.isRecurringJob && (
              <div className={`${styles.summeryInfo} font-bold`}>
                Payment Method
                <p className="p-0 m-0 font-normal">{jobClients.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer"}</p>
              </div>
            )}
            {jobClients.isTutoringJob && (
              <div className={`${styles.summeryInfo}`}>
                Tutoring Type: {jobClients.jobDeliveryMethod === 1 ? "In-home Tutoring" : "Online Tutoring"}
              </div>
            )}
            {responseTable.length > 0 && jobClients.isRecurringJob && (
              <div className="mt-3" style={{ overflowX: "auto" }}>
                <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "12px" }}>
                  <thead>
                    <tr>
                      <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                      <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}></th>
                      <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Hours</th>
                      <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Rate</th>
                      <th style={{ padding: "4px", textAlign: "center", borderBottom: "1px solid #ddd", color: "#585858" }}>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {responseTable.map((shift, index) => {
                      const hours = utils.getTimeDifference(shift.startTime, shift.endTime);
                      const rate = calculateHourlyRate(shift.price, hours);

                      return (
                        <tr key={index} style={{ borderBottom: "1px solid #ddd" }}>
                          <td style={{ padding: "4px", color: "#585858", fontWeight: "700" }}>{shift.day}</td>
                          <td style={{ padding: "4px", color: "#585858" }}>
                            {formatTime(shift.startTime)} to {formatTime(shift.endTime)}
                          </td>
                          <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>{hours}</td>
                          <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${rate.toFixed(2)}</td>
                          <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>${shift.price.toFixed(2)}</td>
                        </tr>
                      );
                    })}
                    {/* Total row */}
                    {responseTable.length >= 1 && (
                      <tr style={{ fontWeight: "bold" }}>
                        <td style={{ padding: "4px", color: "#585858" }} colSpan={2}></td>
                        <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>
                          {responseTable.reduce((sum, shift) => sum + utils.getTimeDifference(shift.startTime, shift.endTime), 0)}
                        </td>
                        <td style={{ padding: "4px", color: "#585858" }}></td>
                        <td style={{ padding: "4px", color: "#585858", textAlign: "center" }}>
                          ${responseTable.reduce((sum, shift) => sum + shift.price, 0).toFixed(2)}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          <div className="py-2 px-3 mt-2" style={{ backgroundColor: "#BBBBBB33", borderRadius: "20px" }}>
            <h4
              style={{
                marginTop: "0px",
                fontSize: "16px",
                marginBottom: "0px",
                color: "#585858",
              }}
            >
              Job Description
            </h4>
            <p style={{ marginTop: "5px", fontSize: "14px" }} className={styles.yourJobDescriptionMobile}>
              {isExpanded ? jobClients.specialInstructions : truncateText(jobClients.specialInstructions, wordLimit)}
            </p>
            {jobClients.specialInstructions.split(" ").length > wordLimit && (
              <button className={styles.seeMoreButton} onClick={() => setIsExpanded(!isExpanded)}>
                {isExpanded ? "See Less" : "See More"}
              </button>
            )}
          </div>
          <Divider className="mt-2" />
          {/* <div className="flex justify-content-center align-items-center">
            <span
                className="mt-3 mb-3 cursor-pointer font-bold"
                style={{
                    color: '#FFA500',
                    fontSize: '14px',
                    textDecoration: 'underLine',
                }}
                onClick={(e) => {
                    e.preventDefault();
                    setShowFullJob(true);
                }}
            >
                View job details
            </span>
        </div> */}
        </div>
        {jobClients.jobStatus !== c.jobStatus.CANCELLED && (
          <div>
            {(jobClients.isTutoringJob || jobClients.isRecurringJob) && (
              <h3
                className="font-bold p-0 m-0 mt-3"
                style={{
                  color: "#585858",
                  fontSize: "18px",
                  textDecoration: "underline",
                }}
              >
                Weekly Schedule
              </h3>
            )}
            {jobClients.isRecurringJob || jobClients.isTutoringJob ? (
              <JobHistoryTable
                rows={responseTable}
                jobDate={new Date(jobClients.jobDate)}
                onChat={(id) => {
                  IframeBridge.sendToParent({
                    type: "navigateChatWithId",
                    data: {
                      id: String(id),
                    },
                  });
                  if (!inIframe) {
                    if (clientType === "1") {
                      navigate(`/parent-home/inAppChat?userId=${id}`);
                    } else {
                      navigate(`/business-home/inAppChat?userId=${id}`);
                    }
                  }
                }}
              />
            ) : null}
          </div>
        )}
        {(() => {
          const awardedApplicant = jobClients.applicants.find((applicant) => applicant.applicantId === jobClients.awardedApplicantId);

          return (
            <div className="mt-2">
              <div className="flex flex-row  align-items-center gap-3">
                <h3
                  className="font-bold p-0 m-0 mb-2"
                  style={{
                    color: "#585858",
                    fontSize: "18px",
                    textDecoration: "underline",
                    margin: "0px",
                  }}
                >
                  {/* {jobClients.isTutoringJob || jobClients.isRecurringJob ? "Helpers Details" : "Helper Details"} */}
                </h3>
                {/* <p
                  style={{
                    fontSize: "15px",
                    fontWeight: "400",
                    color: "#179D52",
                    marginTop:"8px"
                  }}
                >
                  •Job Awarded!
                </p> */}
              </div>
              {jobClients.jobStatus !== c.jobStatus.CANCELLED && (
                <>
                  {!jobClients.isTutoringJob && !jobClients.isRecurringJob ? (
                    <JobAwardedCard
                      statusShow="Completed"
                      image={jobClients.awardedImageSrc || awardedApplicant?.applicantImageSrc}
                      name={`${jobClients.awardedFirstName || awardedApplicant?.applicantFirstName || "Anonymous"} ${jobClients.awardedLastInitial || "Anonymous"
                        }`}
                      rating={awardedApplicant?.applicantRatingsAvg ?? 0}
                      ratingCount={awardedApplicant?.applicantRatingsCount ?? 0}
                      location={awardedApplicant?.suburb ?? "Unknown Location"}
                      onContactNumber={() => {
                        const hasPhoneNumber = checkPhoneNumber(jobClients);
                        handleActionClick(
                          `Contact Number`,
                          null,
                          [
                            {
                              text: null,
                              boldText: jobClients.awardedPhoneNumber || "Not Available",
                              fontSize: "32px",
                            },
                          ],
                          `${jobClients.awardedFirstName}`,
                          jobClients.awardedImageSrc,
                          hasPhoneNumber
                        );
                      }}
                      onBankDetails={() =>
                        handleActionClick(
                          `Bank Transfer Details`,
                          null,
                          [
                            {
                              text: "Bank: ",
                              boldText: jobClients.awardedBankAccountName || "Not Available", // Phone number in bold
                              fontSize: "16px",
                            },
                            {
                              text: "BSP: ",
                              boldText: jobClients.awardedBankAccountBsb || "Not Available", // Phone number in bold
                              fontSize: "16px",
                            },
                            {
                              text: "Account Details: ",
                              boldText: jobClients.awardedBankAccountNumber || "Not Available", // Phone number in bold
                              fontSize: "16px",
                            },
                          ],
                          `${jobClients.awardedFirstName}`,
                          jobClients.awardedImageSrc
                        )
                      }
                      showBank={showBank}
                      showTransport={showTransport}
                      {...(showTransport && {
                        onTransport: () => {
                          const getTransportMessage = (option) => {
                            switch (option) {
                              case 0:
                                return "Unspecified";
                              case 1:
                                return "Needs you to provide transport";
                              case 2:
                                return "Walking";
                              case 3:
                                return "Public Transport";
                              case 4:
                                return "Car (Being picked up)";
                              case 5:
                                return "Driving";
                              case 6:
                                return "Uber / Taxi";
                              default:
                                return "Not Available";
                            }
                          };

                          handleActionClick(
                            `How is the Helper getting home after this job?`,
                            null,
                            [
                              {
                                text: null,
                                boldText: getTransportMessage(awardedApplicant?.gettingHome),
                                fontSize: "30px",
                              },
                            ],
                            ``,
                            jobClients.awardedImageSrc
                          );
                        },
                      })}
                      onChatClick={() => {
                        const applicantId = jobClients.applicants[0]?.applicantId;
                   
                        if (ischateligible === 0) {
                          setChatErrorMessage("You need an active membership to chat with Juggle St users.");
                          setDialogVisible(true);
                          return;
                        }
                        IframeBridge.sendToParent({
                          type: "navigateChatWithId",
                          data: {
                            id: String(applicantId),
                          },
                        });
                        if (!inIframe) {
                          if (clientType === "1") {
                            navigate(`/parent-home/inAppChat?userId=${applicantId}`);
                          } else {
                            navigate(`/business-home/inAppChat?userId=${applicantId}`);
                          }
                        }
                      }}
                    />
                  ) : (
                    awardedApplicants.map((a, index) => (
                      <JobAwardedCard
                        statusShow="Completed"
                        key={index}
                        image={a.applicantImageSrc}
                        name={`${a.applicantFirstName} ${a.applicantLastInitial}`}
                        rating={a.applicantRatingsAvg}
                        ratingCount={a.applicantRatingsCount}
                        location={a.suburb}
                        onRateClick={() => {
                          // Pass both jobId and applicantId
                          handleViewJobIdChangeSecond(jobClients.id, a.applicantId, 3);
                        }}
                        isTutorRecuring={true}
                        hasbeenRated={a.hasBeenRated}
                        onContactNumber={() => {
                          const hasPhoneNumber = checkPhoneNumber(a);
                          handleActionClick(
                            `Contact Number`,
                            null,
                            [
                              {
                                text: null,
                                boldText: a.applicantPhoneNumber || "Not Available", // Phone number in bold
                                fontSize: "32px",
                              },
                            ],
                            `${a.applicantFirstName}`,
                            `${a.applicantImageSrc}`,
                            hasPhoneNumber
                          );
                        }}
                        onBankDetails={() =>
                          handleActionClick(
                            `Bank Transfer Details`,
                            null,
                            [
                              {
                                text: "Bank: ",
                                boldText: a.applicantBankAccountName || "Not Available", // Phone number in bold
                                fontSize: "16px",
                              },
                              {
                                text: "BSP: ",
                                boldText: a.applicantBankAccountBsb || "Not Available", // Phone number in bold
                                fontSize: "16px",
                              },
                              {
                                text: "Account Details: ",
                                boldText: a.applicantBankAccountNumber || "Not Available", // Phone number in bold
                                fontSize: "16px",
                              },
                            ],
                            `${a.applicantFirstName}`,
                            `${a.applicantImageSrc}`
                          )
                        }
                        showBank={showBank}
                        showTransport={!showTransport}
                        {...(!showTransport && {
                          onTransport: () => {
                            const getTransportMessage = (option) => {
                              switch (option) {
                                case 0:
                                  return "Unspecified";
                                case 1:
                                  return "Needs you to provide transport";
                                case 2:
                                  return "Walking";
                                case 3:
                                  return "Public Transport";
                                case 4:
                                  return "Car (Being picked up)";
                                case 5:
                                  return "Driving";
                                case 6:
                                  return "Uber / Taxi";
                                default:
                                  return "Not Available";
                              }
                            };

                            handleActionClick(`Getting Home`, `How is the Helper getting home after this job?`, [
                              {
                                text: null,
                                boldText: getTransportMessage(awardedApplicant?.gettingHome),
                                fontSize: "30px",
                              },
                            ]);
                          },
                        })}
                        onChatClick={() => {
                          if (ischateligible === 0) {
                            setChatErrorMessage("You need an active membership to chat with Juggle St users.");
                            setDialogVisible(true);
                            return;
                          }
                          IframeBridge.sendToParent({
                            type: "navigateChatWithId",
                            data: {
                              id: String(a.applicantId),
                            },
                          });
                          if (!inIframe) {
                            if (clientType === "1") {
                              navigate(`/parent-home/inAppChat?userId=${a.applicantId}`);
                            } else {
                              navigate(`/business-home/inAppChat?userId=${a.applicantId}`);
                            }
                          }
                        }}
                      />
                    ))
                  )}
                </>
              )}

              <AwardedDetailsPopup
                visible={isPopupVisible}
                onHide={() => setIsPopupVisible(false)}
                title={popupData.title}
                subTitle={popupData.subTitle}
                data={popupData.data}
                name={popupData.name}
                helperimg={popupData.helperimg}
                isPhone={isPhoneNumber}
              />
            </div>
          );
        })()}
        {/* 
    <div style={{ marginRight: '2%', width: '952px' }}>
        <JobFooter expiryDate={jobClients.expiresInDays} managedBy={jobClients.managedBy} />
    </div> */}

        <ViewJobFull
          visible={showfullJob}
          onClose={() => {
            setShowFullJob(false);
          }}
          job={jobClients}
        />

        <div
          style={{
            position: "fixed",
            bottom: "0",
            left: "0",
            width: "100%",
            display: "flex",

            justifyContent: "space-between",
            boxSizing: "border-box",
          }}
        >
          {/* Duplicate Job Button - Always visible */}
          <div
            className="cursor-pointer flex justify-content-center align-items-center"
            style={{
              backgroundColor: "#179D52",

              height: "41px",
              justifyContent: "center",
              padding: "0 10px",
              fontWeight: "600",
              fontSize: "12px",
              color: "#FFFFFF",
              boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
              flex: 1,
              display: "flex",
              alignItems: "center",
              gap: "8px",
            }}
            onClick={() => {
              setDuplicateJob(true);
              IframeBridge.sendToParent({
                type: "navigateToPostJob",
                data: {
                  id: jobClients.id,
                  action: "Duplicate",
                },
              });
            }}
          >
            <img alt="multipleFile" src={multipleFile} style={{ width: "18px" }} />
            <span>Duplicate Job</span>
          </div>

          {/* Conditional Second Button */}
          {!jobClients.isRatedByClient ? (
            jobClients.jobStatus !== c.jobStatus.CANCELLED ? (
              !jobClients.isRecurringJob &&
              !jobClients.isTutoringJob && (
                <div
                  className="cursor-pointer flex justify-content-center align-items-center"
                  style={{
                    backgroundColor: "#FFA500",

                    height: "41px",
                    justifyContent: "center",
                    padding: "0 10px",
                    fontWeight: "600",
                    fontSize: "12px",
                    color: "#FFFFFF",
                    boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
                    flex: 1,
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}
                  onClick={() => handleViewJobIdChange(jobClients.id, 3)}
                >
                  <span>Rate {jobClients.awardedFirstName}</span>
                </div>
              )
            ) : (
              <div
                className="flex justify-content-center align-items-center"
                style={{
                  backgroundColor: "#FF63594D",

                  height: "41px",
                  justifyContent: "center",
                  padding: "0 10px",
                  fontWeight: "600",
                  fontSize: "12px",
                  color: "#FF6359",
                  boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
                  flex: 1,
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                }}
              >
                <span>Cancelled Job</span>
              </div>
            )
          ) : (
            !jobClients.isRecurringJob &&
            !jobClients.isTutoringJob && (
              <div
                className="flex justify-content-center align-items-center"
                style={{
                  backgroundColor: "#fff",

                  border: "1px solid #179D52",
                  height: "41px",
                  justifyContent: "center",
                  padding: "0 10px",
                  fontWeight: "600",
                  fontSize: "12px",
                  color: "#179D52",
                  boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
                  flex: 1,
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                }}
              >
                <span>Already Rated</span>
              </div>
            )
          )}
        </div>
      </>
    )
  ) : (
    <div></div>
  );
};

export default JobHistorySummary;
