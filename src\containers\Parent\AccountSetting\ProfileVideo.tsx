import { Divider } from 'primereact/divider';
import { useEffect, useState } from 'react';
import { BiVideoPlus } from 'react-icons/bi';
import removeIcon from '../../../assets/images/Icons/remove.png';
import useLoader from '../../../hooks/LoaderHook';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import { incrementProfileActivationStep } from '../../../store/slices/applicationSlice';
import { useConfirmationPopup, ConfirmationPopupRed } from '../../Common/ConfirmationPopup';
import CustomWistiaUploader, { UploadedFile } from '../../Helper/ProfileCompletnessWizard/video';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import c from '../../../helper/juggleStreetConstants';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import useIsMobile from '../../../hooks/useIsMobile';
const ProfileVideo = () => {
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { disableLoader, enableLoader } = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploading, setUploading] = useState(false);
    const [uploadError, setUploadError] = useState(null);
    const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
    const [showInitialMessage, setShowInitialMessage] = useState(true);
    const {isMobile}=useIsMobile();
    const hasExistingVideo = sessionInfo.data['medias'] && sessionInfo.data['medias'].length > 0;
    const currentVideo = hasExistingVideo ? sessionInfo.data['medias'][0] : null;
    const saveMedia = async (media: UploadedFile) => {
        if (!media) return;
        const mediaToSave = {
            id: 0,
            externalHashedId: media.hashed_id,
            name: media.name,
            description: media.description,
            externalUpdatedDate: media.updated,
            mediaStatus: c.mediaStatus.queued,
            mediaType: c.mediaType.profileIntro,
            playerColor: '#299C4A',
            duration: media.duration,
            thumbnailUrl: media.thumbnail.url,
            width: media.thumbnail.width,
            height: media.thumbnail.height,
            mediaReviewStatus: c.mediaReviewStatus.PENDING,
            assetId: null,
            playbackId: null,
        };
        const payload = {
            ...sessionInfo.data,
            medias: [mediaToSave],
        };
        enableLoader();
        try {
            await dispatch(updateSessionInfo({ payload }));
            setShowInitialMessage(false);
            setUploadedFile(null);

            dispatch(incrementProfileActivationStep());
        } catch (error) {
            disableLoader();
            setUploadError('Failed to save media information.');
        }
        disableLoader();

    };

    const getStatusMessage = () => {
        if (showInitialMessage) {
            return 'Upload a short video, this is the best way to introduce yourself to new families. Helpers with a video receive more job invitations.';
        }
        const reviewStatus = currentVideo?.mediaReviewStatus;
        switch (reviewStatus) {
            case c.mediaReviewStatus.APPROVED:
                return 'Your profile video has been approved by our team. Families visiting your profile can now see the video.';
            case c.mediaReviewStatus.PENDING:
                return 'Your video is being reviewed and will be approved shortly.';
            case c.mediaReviewStatus.REJECTED:
                return 'The profile video you have uploaded has been rejected by our team. Please upload a different video.';
            default:
                return '';
        }
    };

    useEffect(() => {
        if (!hasExistingVideo && !uploadedFile) {
            setShowInitialMessage(true);
        }
    }, [hasExistingVideo, uploadedFile]);

    const handleDelete = () => {
        showConfirmationPopup(
            'Important',
            'Are you sure you want to delete this video?',
            'Delete Video',
            <img src={removeIcon} alt="remove icon" style={{ height: '15px', width: '13.33px' }} />,
            async () => {
                try {
                    enableLoader();
                    const payload = {
                        ...sessionInfo.data,
                        medias: [],
                    };
                    await dispatch(updateSessionInfo({ payload }));
                    setShowInitialMessage(true);
                    setUploadedFile(null);
                    disableLoader();
                } catch (error) {
                    disableLoader();
                    setUploadError('Failed to delete the video.');
                }
            }
        );
    };
    const pendingProfileTotalPoints = () => {
        let score = 0;
        sessionInfo.data['rankingItems'].forEach((item) => {
            if (item.userManageable) {
                score += item.pointsPerEntry;
            }
        });
        return score;
    };

    const entryPercentage = () => {
        const entry = sessionInfo.data['rankingItems'].find((r) => r.rankingCategory === 'profile.video');
        if (!entry || entry.entriesCount > 0) return '';

        const perc = Math.round((entry.pointsPerEntry / pendingProfileTotalPoints()) * 100);
        return `Add ${perc}% to your Profile Strength`;
    };
    return (
        <div className="w-full h-full flex flex-column py-4 px-4">
            <ConfirmationPopupRed confirmationProps={confirmationProps} />
            <header className={styles.utilheader}>
                <h1 style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Profile Video</h1>
            </header>
            <div>
            </div>
            <p
                style={{
                    fontWeight: '500',
                    fontSize: '16px',
                    color: '#585858',
                    display: 'flex',
                    alignItems: 'center',
                }}
            >
                <BiVideoPlus size={24} style={{ marginRight: '8px' }} />
                Profile Video
            </p>
            <Divider className="mb-2" />
            <h1 className="p-0 m-0 mt-2 txt-clr font-medium" style={{ fontSize: '16px' }}>
                {entryPercentage() && (
                    <p className="mt-2" style={{ fontSize: '16px', color: '#585858' }}>
                        {entryPercentage()}
                    </p>
                )}
            </h1>
            <div className="flex justify-content-center align-item-center">
            </div>

            <div className="mt-2" style={{ maxWidth: '100%' }}>
                {hasExistingVideo ? (
                    <div style={{ marginTop: '10px', position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden' }}>
                        <h1 className="m-0 p-0 mb-2 font-semibold" style={{ color: "#585858", fontSize: '20px' }}>Uploaded Video:</h1>
                        <iframe
                            src={`https://fast.wistia.net/embed/iframe/${currentVideo.externalHashedId}`}
                            title="Uploaded Video"
                            style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                            }}
                            frameBorder="0"
                            allow="autoplay; fullscreen"
                            allowFullScreen
                        ></iframe>
                    </div>
                ) : (
                    <CustomWistiaUploader
                        onUploadStateChange={(s) => setUploading(s)}
                        onUploadSuccess={(d) => setUploadedFile(d)}
                        onUploadProgress={(progress) => setUploadProgress(progress)}
                    />
                )}
                <h1 className="p-0 m-0 mt-2 txt-clr font-medium" style={{ fontSize: '16px' }}>
                    {getStatusMessage()}
                </h1>
                <div className="flex justify-content-center align-item-center">
                    <button
                        className="mt-2 p-2 px-4 font-bold cursor-pointer"
                        onClick={hasExistingVideo ? handleDelete : () => saveMedia(uploadedFile)}
                        disabled={!hasExistingVideo && (!uploadedFile || uploading)}
                        style={{
                            backgroundColor: '#FFA500',
                            border: 'none',
                            borderRadius: '10px',
                            color: '#FFFFFF',
                        }}
                    >
                        {hasExistingVideo
                            ? 'Delete Video'
                            : uploading
                                ? `Uploading... ${uploadProgress}%`
                                : 'Upload Video'}
                    </button>
                </div>

                {uploadError && <p style={{ color: 'red' }}>{uploadError}</p>}
            </div>
        </div>
    );
};

export default ProfileVideo;

