import { Outlet, useNavigate } from 'react-router-dom';
import c from '../../helper/juggleStreetConstants';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import utils from '../../components/utils/util';
import CookiesConstant from '../../helper/cookiesConst';

type LayoutProps = {
    clientType?: (typeof c.clientType)[keyof typeof c.clientType];
};

const Layout = ({}: LayoutProps) => {
    const session = useSelector((state: RootState) => state.sessionInfo);
    const clientType = utils.getCookie(CookiesConstant.clientType);
    const navigate = useNavigate();

    const clientOnUrl = () => {
        if (window.location.href.includes('parent-home')) return c.clientType.INDIVIDUAL;
        if (window.location.href.includes('business-home')) return c.clientType.BUSINESS;
        if (window.location.href.includes('helper-home')) return c.clientType.UNSPECIFIED;
        return -1;
    };

    const checkAuthorization = () => {
        const urlClient = clientOnUrl();
        if (urlClient === -1) return;
        if (urlClient === Number(clientType)) return;
        navigate('/unauthorized');
    };

    useEffect(() => {
        if (session.loading || !session.data || !clientType) return;
        checkAuthorization();
    }, [session.data, session.loading, clientType, window.location.href]);

    return (
        <>
            <Outlet />
        </>
    );
};

export default Layout;
