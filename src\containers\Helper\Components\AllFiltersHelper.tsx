import { Dialog } from 'primereact/dialog';
import { RiCloseLargeFill } from 'react-icons/ri';
import { useEffect, useRef, useState } from 'react';
import helperImage1 from '../../../assets/images/helper_image_1.png'
import helperImage2 from '../../../assets/images/helper_image_2.png';
import helperImage3 from '../../../assets/images/helper_image_3.png';

import { Divider } from 'primereact/divider';
import { BiLocationPlus } from 'react-icons/bi';
import { useSelector } from 'react-redux';
import { FaCheck, FaChevronRight } from 'react-icons/fa6';
import { FaTimes } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../../../store';
import { Filter, SearchFilters } from '../../../store/types';
import utils from '../../../components/utils/util';
import Service from '../../../services/services';
import environment from '../../../helper/environment';
import { UserSearchResponse } from '../../../hooks/SearchGeoSearchHook';
import styles from '../../Common/styles/all-filters.module.css'
import useIsMobile from '../../../hooks/useIsMobile';
export const RadioButton2 = ({ selected }: { selected: boolean }) => {
    return (
        <div
            className="flex justify-content-center align-items-center"
            style={{
                minHeight: '16px',
                height: '16px',
                width: '16px',
                minWidth: '16px',
                borderRadius: '50%',
                border: `1px solid ${selected ? '#179D52' : '#DFDFDF'}`,
                backgroundColor: selected ? '#179D52' : '',
            }}
        >
            {selected && <FaCheck fontSize={'10px'} color="#ffffff" />}
        </div>
    );
};

interface Subject {
    optionId: number;
    text: string;
    children: Subject[] | null;
}

const RenderNodes = ({
    subject,
    selectedOption,
    onSelect,
    depth = 0,
    expandedNodes,
    setExpandedNodes,
}: {
    subject: Subject;
    selectedOption: number | null;
    onSelect: (optionId: number) => void;
    depth?: number;
    expandedNodes: Set<number>;
    setExpandedNodes: React.Dispatch<React.SetStateAction<Set<number>>>;
}) => {
    const hasChildren = subject.children !== null && subject.children.length > 0;
    const isExpanded = expandedNodes.has(subject.optionId);

    const handleClick = () => {
        if (hasChildren) {
            const newExpandedNodes = new Set(expandedNodes);
            if (isExpanded) {
                newExpandedNodes.delete(subject.optionId);
            } else {
                newExpandedNodes.add(subject.optionId);
            }
            setExpandedNodes(newExpandedNodes);
        } else {
            onSelect(subject.optionId);
        }
    };

    return (
        <div className="w-full">
            <div
                className={`
                    flex align-items-center p-2 px-3 cursor-pointer 
                    hover:surface-hover transition-colors
                    ${hasChildren ? 'justify-content-between' : 'justify-content-start'}
                    ${depth > 0 ? 'pl-' + depth * 4 : ''}
                `}
                onClick={handleClick}
            >
                <div className="flex align-items-center gap-3">
                    {hasChildren ? (
                        <i
                            className={`pi pi-chevron-right 
                                transition-transform 
                                ${isExpanded ? 'rotate-90' : ''}
                            `}
                        />
                    ) : (
                        <RadioButton2 selected={subject.optionId === selectedOption} />
                    )}
                    <span className="text-color-secondary font-medium">{subject.text}</span>
                </div>
            </div>

            {hasChildren && isExpanded && (
                <div className="pl-4">
                    {subject.children?.map((child) => (
                        <RenderNodes
                            key={child.optionId}
                            subject={child}
                            onSelect={onSelect}
                            depth={depth + 1}
                            selectedOption={selectedOption}
                            expandedNodes={expandedNodes}
                            setExpandedNodes={setExpandedNodes}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

interface AllFiltersProps {
    enable: boolean;
    availableHelpers: UserSearchResponse;
    selectedJobCategory: 'Childcare' | 'Tutoring' | 'Odd Jobs';
    mode?: 'Full' | 'post-job';
    onClose: (payload: {
        filters: Filter[];
        pageIndex: number;
        pageSize: number;
        sortBy: 'experience';
    }) => void;
    onResetRef?: (resetFn: () => void) => void;
}

function Filters<T>({
    values,
    type,
    name,
    expanded = false,
    groupName,
    getId,
    getLabel,
    checked,
    onChange,
}: {
    values: T[];
    type: 'checkbox' | 'radio';
    name: string;
    expanded?: boolean;
    groupName: string;
    getId: (value: T, index: number) => string;
    getLabel: (value: T, index: number) => string;
    checked: (value: T, index: number) => boolean;
    onChange: (value: T, index: number) => void;
}) {
    return values.map((value, index) => {
        const isSelected = checked(value, index);

        return (
            <div
                className={`flex align-items-center gap-1 ${expanded ? 'w-6' : ''}`}
                key={index}
                style={{
                    marginBottom: expanded ? '5px' : '0px',
                    cursor: 'pointer',
                }}
                onClick={type === 'radio' ? () => onChange(value, index) : undefined} // Only apply for radio
            >
                {type === 'radio' ? (
                    <div
                        style={{
                            height: '18px',
                            width: '18px',
                            borderRadius: '50%',
                            padding: '1px',
                            border: `1px solid ${isSelected ? '#179D52' : '#DFDFDF'}`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        {isSelected && (
                            <div
                                style={{
                                    height: '100%',
                                    width: '100%',
                                    borderRadius: '50%',
                                    backgroundColor: '#179D52',
                                }}
                            />
                        )}
                    </div>
                ) : (
                    <input
                        className={`${styles.customCheckBox} cursor-pointer`}
                        type="checkbox"
                        name={`${groupName}-${name}`}
                        id={getId(value, index)}
                        checked={isSelected}
                        onChange={(e) => {
                            e.stopPropagation(); // Prevents container click event
                            onChange(value, index);
                        }}
                    />
                )}
                <label
                    className="cursor-pointer"
                    style={{
                        fontWeight: '500',
                        fontSize: '16px',
                        userSelect: 'none',
                        textWrap: 'nowrap',
                    }}
                    htmlFor={getId(value, index)}
                    onClick={(e) => {
                        if (type === 'checkbox') {
                            e.stopPropagation(); // Stop propagation for checkboxes
                        }
                    }}
                >
                    {getLabel(value, index)}
                </label>
            </div>
        );
    });
}

function AllFiltersHelper({
    enable,
    availableHelpers,
    mode = 'Full',
    onClose,
    onResetRef
}: AllFiltersProps) {
    const [searchResult, setSearchResult] = useState<UserSearchResponse>(null);
    const [dropdownEnabled, setDropdownEnabled] = useState(false);
    const { isMobile } = useIsMobile();
    // * Filters
    const [distance, setDistance] = useState<number>(2);
    const [families, setfamilies] = useState<number>(0);
    const [activity, setActivity] = useState<number>(0);
    const { defaultFilters } = useSelector((state: RootState) => state.applicationState);
    const navigate = useNavigate();

    useEffect(() => {
        if (searchResult && searchResult.results.length > 0) {
            setDropdownEnabled(true);
            return;
        }
        setDropdownEnabled(false);
    }, [searchResult]);

    const resetAllFilters = () => {
        setDistance(2);
        setfamilies(0);
        setActivity(0);
    }

    const debounceSearch = utils.debounce(async (searchText) => {
        if (searchText.trim().length < 2) {
            setSearchResult(null);
            setDropdownEnabled(false);
            return;
        }
        const payload = {
            pageindex: 1,
            pageSize: 10,
            sortBy: 'experience',
            filters: [
                { field: 'publicName', value: searchText, operator: 'contains' },
                { field: 'jobDeliveryMethod', value: 1, operatro: 'eq' },
            ],
        };
        const { success, data } = await new Promise<{
            success: boolean;
            data: UserSearchResponse | null;
        }>((res, _) => {
            Service.getConnections(
                (data) => {
                    res({ data: data, success: true });
                },
                () => {
                    res({ data: null, success: false });
                },
                payload
            );
        });

        if (success && data && data.results.length > 0) {
            setSearchResult(data);
        } else {
            setSearchResult(null);
        }
    }, 300);

    const viewProvider = (helperId) => {
        const newParams = new URLSearchParams();
        newParams.set('id', String(helperId));
        setDropdownEnabled(false);
        navigate({
            pathname: 'provider-profile',
            search: newParams.toString(),
        });
    };

    function getDisplayHelpers() {
        if (availableHelpers && availableHelpers.results.length > 2) {
            return availableHelpers.results
                .slice(0, 3)
                .map(
                    (helper) =>
                        `${environment.getStorageURL(window.location.hostname)}/images/${helper.imageSrc
                        }`
                );
        } else {
            return [helperImage1, helperImage2, helperImage3];
        }
    }
    function onApply(event: React.MouseEvent<HTMLButtonElement>) {
        event.preventDefault();
        const filterFamilies = families === 2 ? 5 : families;
        const payload: SearchFilters = {
            pageIndex: 1,
            pageSize: 50,
            sortBy: 'experience',
            filters: [
                { field: 'neighbourhood', operator: 'eq', value: filterFamilies },
                { field: 'distance', operator: 'eq', value: distance },
                { field: 'activity', operator: 'eq', value: activity },
            ],
        };
        onClose(payload);
    }
    useEffect(() => {
        if (onResetRef && typeof onResetRef === 'function') {
          onResetRef(resetAllFilters);
        }
      }, [onResetRef]);
    return (
        <>
            <Dialog
                visible={enable}
                onHide={() => {
                    setDropdownEnabled(false);
                    onClose(null);
                }}
                content={
                    <div className={`${styles.main} overflow-hidden flex flex-column`}>
                        <div className={`w-full flex justify-content-between ${styles.padding}`}>
                            <h1 className={`${styles.title} pointer-events-none`}>All Filters</h1>
                            <button
                                className={`${styles.closeButton} cursor-pointer`}
                                onClick={() => {
                                    setDropdownEnabled(false);
                                    onClose(null);
                                }}
                            >
                                <RiCloseLargeFill />
                            </button>
                        </div>
                        <div
                            style={{ minHeight: '3px', width: '100%', backgroundColor: '#f1f1f1' }}
                        />
                        <div
                            className={`flex-grow-1 ${styles.scrollView} ${styles.padding} flex flex-column`}
                        >
                            {mode === 'Full' && (
                                <>
                                    <h1
                                        className="m-0 p-0 pointer-events-none"
                                        style={{ fontSize: '18px', fontWeight: '700' }}
                                    >
                                        Search by name
                                    </h1>
                                    <div
                                        className={`${styles.searchInput} relative overflow-visible`}
                                    >
                                        <input
                                            type="text"
                                            className={`${styles.inputStyle}`}
                                            placeholder="Min 2 characters"
                                            onChange={(e) => {
                                                debounceSearch(e.target.value);
                                            }}
                                        />
                                        {searchResult &&
                                            dropdownEnabled &&
                                            searchResult.results.length > 0 && (
                                                <div
                                                    className="absolute w-full overflow-y-auto"
                                                    style={{
                                                        left: '0.45px',
                                                        transform: 'translateY(10px)',
                                                        zIndex: '5',
                                                        backgroundColor: '#ffffff',
                                                        borderRadius: '5px',
                                                        border: '1px solid #bbbbbb',
                                                        maxHeight: '15rem',
                                                        transition: 'all .3s ease-in-out',
                                                    }}
                                                >
                                                    {searchResult.results.map((val, index) => (
                                                        <div
                                                            key={index}
                                                            className="grid nested-grid grid-nogutter align-items-center select-none hover:bg-gray-100 cursor-pointer"
                                                            style={{
                                                                padding: '5px',
                                                            }}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                viewProvider(val.id);
                                                            }}
                                                        >
                                                            <div className="col-2 flex justify-content-center mb-2">
                                                                <img
                                                                    style={{
                                                                        height: '50px',
                                                                        width: '50px',
                                                                        borderRadius: '50%',
                                                                    }}
                                                                    src={`${environment.getStorageURL(
                                                                        window.location.hostname
                                                                    )}/images/${val.imageSrc}`}
                                                                    alt="helper Image"
                                                                />
                                                            </div>
                                                            <div className="col-10 mb-2">
                                                                <div className="grid grid-nogutter">
                                                                    <div className="col-12 pl-1">
                                                                        <p className="m-0 p-0">
                                                                            {val.publicName}
                                                                        </p>
                                                                    </div>
                                                                    <div className="col-1 w-min flex justify-content-center align-items-center">
                                                                        <BiLocationPlus />
                                                                    </div>
                                                                    <div className="col-11 flex">
                                                                        <p className="m-0 p-0">
                                                                            {`${val.suburb} - Helper`}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <Divider className="col-12" />
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                    </div>
                                    <div
                                        className={`flex justify-content-start align-items-center`}
                                        style={{ marginTop: '10px', padding: '5px 20px' }}
                                    >
                                        <div className="relative flex">
                                            {getDisplayHelpers().map((helper, index) => (
                                                <div
                                                    key={index}
                                                    style={{
                                                        position: 'relative',
                                                        left: `-${index * 10}px`,
                                                        zIndex: index,
                                                    }}
                                                >
                                                    <img
                                                        src={helper}
                                                        alt="helper display"
                                                        height="33px"
                                                        width="33px"
                                                        style={{ borderRadius: '50%' }}
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                        <p
                                            className={`p-0 m-0 pb-1 pointer-events-none`}
                                            style={{ fontWeight: '300', fontSize: '12px' }}
                                        >
                                            Search Client by name
                                        </p>
                                    </div>
                                    <Divider className="w-11 align-self-center mt-2" />
                                </>
                            )}
                            <h1
                                className="pointer-events-none"
                                style={{ fontSize: '18px', fontWeight: '700', marginBlock: '10px' }}
                            >
                                Local Families
                            </h1>
                            <div
                                className={`flex justify-content-start gap-2 ${isMobile ? 'flex justify-content-start gap-3 flex-column' : ""}`}
                                style={{ padding: '0 25px', paddingBottom: '15px' }}
                            >
                                <Filters
                                    values={['All', 'My Connections', 'New']}
                                    type="radio"
                                    name="families"
                                    groupName="families-group"
                                    getId={(value, index) =>
                                        `families-${value.toString()}-${index}`
                                    }
                                    getLabel={(value) => `${value}`}
                                    checked={(_, index) => families === index}
                                    onChange={(_, index) => setfamilies(index)}
                                />
                            </div>
                            {mode === 'Full' && (
                                <>
                                    <Divider
                                        className="align-self-center mt-2"
                                        style={{ width: '100%' }}
                                    />
                                </>
                            )}
                            <h1
                                className="pointer-events-none"
                                style={{ fontSize: '18px', fontWeight: '700', marginBlock: '10px' }}
                            >
                                Distance
                            </h1>
                            <div
                                className={`flex justify-content-start gap-2 ${isMobile ? 'flex justify-content-start gap-3 flex-column' : ""}`}
                                style={{ padding: '0 25px', paddingBottom: '15px' }}
                            >
                                <Filters
                                    values={[2.5, 5, 10]}
                                    type="radio"
                                    name="distance"
                                    groupName="distance-group"
                                    getId={(value, index) =>
                                        `distance-${value.toString()}-${index}`
                                    }
                                    getLabel={(value) => `Within ${value}km`}
                                    checked={(_, index) => distance === index}
                                    onChange={(_, index) => setDistance(index)}
                                />
                            </div>
                            {mode === 'Full' && (
                                <>
                                    <Divider
                                        className="align-self-center mt-2"
                                        style={{ width: '100%' }}
                                    />
                                </>
                            )}
                            <h1
                                className="pointer-events-none"
                                style={{ fontSize: '18px', fontWeight: '700', marginBlock: '10px' }}
                            >
                                Activity
                            </h1>
                            <div
                                className={`flex flex-column gap-2 ${isMobile ? 'flex flex-column gap-3' : ""}`}
                                style={{ padding: '0 25px', paddingBottom: '15px' }}
                            >
                                <Filters
                                    values={['All', 'Activity in last 30 days', 'Activity in last 90 days']}
                                    type="radio"
                                    name="activity"
                                    groupName="activity-group"
                                    getId={(value, index) => `activity-${value.toString()}-${index}`}
                                    getLabel={(value) => `${value}`}
                                    checked={(_, index) => activity === index}
                                    onChange={(_, index) => setActivity(index)}
                                />
                            </div>

                        </div>
                        <div
                            style={{ minHeight: '2px', width: '100%', backgroundColor: '#f1f1f1' }}
                        />
                        <div className={`${styles.padding} flex justify-content-end gap-3`}>
                            <button
                                className={`${styles.buttonStyle}`}
                                data-clear
                                onClick={(e) => {
                                    e.preventDefault();
                                    setDropdownEnabled(false);
                                    resetAllFilters();
                                    onClose({
                                        pageIndex: 1,
                                        pageSize: 50,
                                        sortBy: 'experience',
                                        filters: [
                                            { field: 'distance', operator: 'eq', value: 2 },
                                            { field: 'activity', operator: 'eq', value: 0 },
                                            { field: 'neighbourhood', operator: 'eq', value: 0 },
                                        ],
                                    });
                                }}
                            >
                                Clear
                            </button>
                            <button
                                className={`${styles.buttonStyle}`}
                                data-apply
                                onClick={onApply}
                            >
                                Apply
                            </button>
                        </div>
                    </div>
                }
            />
        </>
    );
}
export default AllFiltersHelper;
