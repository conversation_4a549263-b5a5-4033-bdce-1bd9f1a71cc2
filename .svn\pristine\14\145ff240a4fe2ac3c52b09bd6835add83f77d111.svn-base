import React, { useState } from "react";
import clock from "../../../../assets/images/Icons/clockstart.png";
import clocked from "../../../../assets/images/Icons/clockend.png";
import fileCheck from "../../../../assets/images/Icons/file-check-01.png";
import cross from "../../../../assets/images/Icons/Close_round.png";
import check from "../../../../assets/images/Icons/check-green.png";
import "primeflex/primeflex.css";
import "primeicons/primeicons.css";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import styles from "../../../Common/styles/manage-job-weekly-schedule.module.css";
import c from "../../../../helper/juggleStreetConstants";
import { FaRegEye } from "react-icons/fa6";
import AwardShiftsTo from "./AwardShiftsTo";
import { Jobs } from "../types";
import AwardCard from "./AwardCard";
import { useNavigate } from "react-router-dom";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import NoJobsCard from "./NoJobsCard";
import useLoader from "../../../../hooks/LoaderHook";
import remove from "../../../../assets/images/Icons/remove.png";
import Service from "../../../../services/services";
import { CancelJobPopup, useCancelJobPopup } from "./CancelJobPopup";
import useIsMobile from "../../../../hooks/useIsMobile";
import { Divider } from "primereact/divider";
import CustomDialog from "../../../../commonComponents/CustomDialog";
import ProviderProfile from "../../../Parent/ProviderProfile/ProviderProfile";
import { IframeBridge } from "../../../../services/IframeBridge";

interface Helper {
  imageUrl: string;
  availabilityStatus: number;
  id: string;
  name: string;
}

interface TimeSlot {
  dayOfWeek: number;
  day: string;
  startTime: string;
  endTime: string;
  shiftId: number;
  price: number;
  awardedAplicantId: number | null;
  applicants: Array<{
    applicantId: number;
    availabilityId: number;
    publicName: string;
    imageSrc: string;
    status: number;
    applicantRatingsCount: number;
    completedJobsCount: number;
    applicantRatingsAvg: number;
    responseRate: number;
  }>;
}
interface AvailableApplicants {
  applicantId: number;
  publicName: string;
  imageSrc: string;
  isSuperHelper: boolean;
  applicantRatingsCount: number;
  applicantRatingsAvg: number;
  responseRate: number;
  id: number;
  applicationStatus: number;
  gettingHome: number;
  suburb: string;
}

interface WeeklyScheduleProps {
  timeSlots: TimeSlot[];
  className?: string;
  availabilityStatusEnum: any; // Pass the enum from constants
  totalInvited: number;
  totalViewed: number;
  job: Jobs;
  index?: number;
  availableApplicants: AvailableApplicants[];
  viewShifts?: boolean;
}
function getJobName(jobType, subJobType = 0): { job: string; shift: string[] } {
  const mappings = {
    jobType: {
      1: { job: 'Ad-hoc Childcare', shift: ['Morning', 'Afternoon'] },
      2: { job: 'Recurring Childcare', shift: ['Morning', 'Afternoon'] },
      4: {
        job: 'Before School Care',
        shift: ['Before School', 'Second Shift'],
      },
      8: {
        job: 'After School Care',
        shift: ['Before School', 'Second Shift'],
      },
      12: {
        job: 'Before & After School Care',
        shift: ['Before School', 'After School'],
      },
      64: {
        job: 'Primary School Tutoring',
        shift: ['Before School', 'After School'],
      },
      128: {
        job: 'High School Tutoring',
        shift: ['Before School', 'After School'],
      },
      256: { job: 'Odd Job', shift: ['Morning', 'Afternoon'] },
    },
    subJobType: {
      1: { job: 'Laundry', shift: ['Morning', 'Afternoon'] },
      2: { job: 'Errand Running', shift: ['Morning', 'Afternoon'] },
      4: { job: 'Outdoor Chores', shift: ['Morning', 'Afternoon'] },
      8: { job: 'Help for the Elderly', shift: ['Morning', 'Afternoon'] },
      16: { job: 'Other Odd Jobs', shift: ['Morning', 'Afternoon'] },
    },
    recurringChildcare: {
      2: { job: 'Daytime Nanny', shift: ['Nanny Shift', 'Second Shift'] },
      4: {
        job: 'Before School Care',
        shift: ['Before School', 'Second Shift'],
      },
      8: {
        job: 'After School Care',
        shift: ['After School', 'Second Shift'],
      },
      12: {
        job: 'Before & After School Care',
        shift: ['Before School', 'After School'],
      },
    },
  };

  if (jobType === 2) {
    return subJobType === 0
      ? { job: 'Daytime Nanny', shift: ['Nanny Shift', 'Second Shift'] }
      : mappings.recurringChildcare[jobType] || {
        job: 'Recurring Childcare',
        shift: ['Morning', 'Afternoon'],
      };
  }

  if (jobType === 256 && subJobType > 0) {
    return (
      mappings.subJobType[subJobType] || {
        job: 'Odd Job',
        shift: ['Morning', 'Afternoon'],
      }
    );
  }

  return (
    mappings.jobType[jobType] || {
      job: 'Unknown Job',
      shift: ['Morning', 'Afternoon'],
    }
  );
}
const WeeklyScheduleds: React.FC<WeeklyScheduleProps> = ({
  timeSlots,
  className,
  totalInvited,
  totalViewed,
  job,
  availableApplicants,
  index,
  viewShifts,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedHelper, setSelectedHelper] = useState<Helper | null>(null);
  const [selectedShifts, setSelectedShifts] = useState<string[]>([]);
  const [clickedShiftId, setClickedShiftId] = useState<number | null>(null);
  const navigate = useNavigate();
  const { disableLoader, enableLoader } = useLoader();
  const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const { isMobile } = useIsMobile();
  const isHelperAvailable = (status: number) =>
    status === c.applicantAvailabilityStatus.AVAILABLE;
  const getImageStyles = (status: number) => {
    return {
      border: isHelperAvailable(status) ? "1px solid #179D52" : "none",
      borderRadius: "50%",
      opacity: isHelperAvailable(status) ? 1 : 0.5,
      position: "relative" as const,
    };
  };
  function formatTimeTo12Hour(time: string): string {
    const [hours, minutes] = time.split(":").map(Number); // Split the time and convert to numbers
    const period = hours >= 12 ? "pm" : "am"; // Determine AM/PM
    const formattedHours = hours % 12 || 12; // Convert to 12-hour format (handle 0 as 12)
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  }

  const handleAwardJobClicked = (applicantId: number) => {
    setClickedShiftId(applicantId);
    setIsDialogOpen(true);
  };
  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedHelper(null);
    setSelectedShifts([]);
    setClickedShiftId(null);
  };

  const handleShiftSelect = (shiftId: string) => {
    setSelectedShifts((prev) => {
      if (prev.includes(shiftId)) {
        return prev.filter((id) => id !== shiftId);
      } else {
        return [...prev, shiftId];
      }
    });
  };

  const handleRemoveJob = (id: number, tabIndex: string) => {
    // Show the confirmation popup
    showCancelJobPopup(
      "Remove Applicant?", // Heading
      `will be removed from your shortlist. Their response will remain in the response table.`, // Message
      "Go Back", // Cancel Text
      "Remove", // Confirm Text
      <img
        src={remove}
        alt="confirm"
        style={{ height: "20px", width: "20px" }}
      />,
      () => {
        const payload = {
          applicants: [
            {
              id: id,
              applicationStatus: c.jobApplicationStatus.EXCLUDED_BY_CLIENT,
            },
          ],
          id: job.id,
          jobStatus: job.jobStatus.toString(),
        };

        enableLoader();
        Service.jobClientAwardJobs(
          (response: Jobs) => {

            const newSearchParams = new URLSearchParams();
            newSearchParams.set("jobId", "-1");
            newSearchParams.set("activeTab", tabIndex);
            navigate({ search: newSearchParams.toString() });

            disableLoader();
          },
          (error: any) => {
            disableLoader();
            console.error("Error cancelling job:", error);
          },
          job.id,
          payload
        );
      },
      () => {
        // onCancel callback

      }
    );
  };

  const handleSelectAll = () => {
    // if (selectedHelper) {
    //   const availableShifts = (selectedHelper, clickedShiftId);
    //   setSelectedShifts(availableShifts.map((shift) => shift.id));
    // }
  };
  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);

  const redirectAfterHome = (id: number) => {
    IframeBridge.sendToParent({
      type: 'navigateHelperProfile',
      data: {
        id: String(id),
      },
    });
    if (isMobile) {
      const navigatePArams = new URLSearchParams();
      navigatePArams.set("id", id.toString());
      const clientType = utils.getCookie(CookiesConstant.clientType);
      if (clientType === "2") {
        navigate(`/business-home/provider-profile?${navigatePArams.toString()}`);
      } else if (clientType === "1") {
        navigate(`/parent-home/provider-profile?${navigatePArams.toString()}`);
      } else {
        console.warn("Unknown clientType, no navigation performed.");
      }
    }
    else {
      setSelectedProviderId(id);
      setShowPopup(true);
    }
  };

  const handleCloseProfilePopup = () => {
    setShowPopup(false);
    // setSelectedCandidate(null);
  };

  const Items = [
    { label: "Unspecified", value: "0" },
    { label: "I need taking home", value: "1" },
    { label: "Walking", value: "2" },
    { label: "Public transport", value: "3" },
    { label: "Getting picked up", value: "4" },
    { label: "Driving myself", value: "5" },
    { label: "Uber/Taxi", value: "6" },
    { label: "Uber/Taxi", value: "6" },
  ];

  const getGettingHomeLabel = (value: number) => {
    const item = Items.find((item) => item.value === value.toString());
    return item ? item.label : "Not Available";
  };
  const calculateDuration = (start, end) => {
    if (!start || !end) return "";

    try {
      let startTime = new Date(`1970-01-01T${start}`);
      let endTime = new Date(`1970-01-01T${end}`);

      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) return ""; // Handle invalid date cases

      // If the start time is later than the end time, assume end time is on the next day
      if (startTime > endTime) {
        endTime.setDate(endTime.getDate() + 1);
      }

      const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60); // Convert to hours
      return duration.toFixed(1); // Show one decimal place
    } catch (error) {
      console.error("Error calculating duration:", error);
      return "";
    }
  };

  const getShiftNameForSlot = (slots: TimeSlot[], currentSlot: TimeSlot) => {
    const jobInfo = getJobName(job.jobType, job.jobSubType);

    // Count how many times this day appears
    const dayOccurrences = slots.filter(
      slot => slot.dayOfWeek === currentSlot.dayOfWeek
    ).length;

    // Find the index of current slot among slots with same day
    const slotIndex = slots
      .filter(slot => slot.dayOfWeek === currentSlot.dayOfWeek)
      .findIndex(
        slot =>
          slot.startTime === currentSlot.startTime &&
          slot.endTime === currentSlot.endTime
      );

    // If day appears once, always use first shift name
    // If day appears twice, use corresponding shift name based on index
    return dayOccurrences === 1 ? jobInfo.shift[0] : jobInfo.shift[slotIndex];
  };
  const sortedTimeSlots = [...timeSlots].sort((a, b) => {
    const aStatus = a.awardedAplicantId === null ? 1 : 0;
    const bStatus = b.awardedAplicantId === null ? 1 : 0;
    return bStatus - aStatus; // Reverse the comparison
  });
  return !isMobile ? (
    <>
      <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />

      <div
        className={`${className} mt-3`}
        style={{
          width: "952px",
          borderRadius: "20px",
          border: "1px solid #DFDFDF",
        }}
      >
        <div
          className="flex align-items-end pt-2 pl-3 pr-3 gap-4 justify-content-between"
          style={{ width: "952px" }}
        >
          <h2
            className="font-bold p-0 m-0"
            style={{ fontSize: "30px", color: "#585858" }}
          >
            {timeSlots.every((t) => t.awardedAplicantId === null)
              ? "Weekly Schedule"
              : ""}
          </h2>
          {/* {timeSlots.every((t) => t.awardedAplicantId === null) && (
            <div className="flex gap-3">
              <div className="flex align-items-center">
                <img
                  src={fileCheck}
                  alt="file check"
                  width="12.4px"
                  height="12.8px"
                  className="text-gray-500"
                />
                <h1 className={`${styles.clrFont} p-1 m-0 font-bold`}>
                  Invited: {totalInvited}
                </h1>
              </div>

              <div className="flex align-items-center">
                <FaRegEye
                  className="text-gray-500"
                  style={{ width: "12.4px", height: "12.8px" }}
                />
                <h1 className={`${styles.clrFont} p-1 m-0 font-bold`}>
                  Viewed: {totalViewed}
                </h1>
              </div>
            </div>
          )} */}
          <div className="flex flex-column gap-1 ml-6">
            <div className={`flex align-items-center`}>
              <div
                className={`flex align-items-center justify-content-center  text-white`}
                style={{
                  borderRadius: "50%",
                  height: "17px",
                  width: "17px",
                  borderBottomColor: "1px solid #FF6359",
                  boxShadow: "0 4px 4px 1px rgba(0, 0, 0, 0.3)",
                }}
              >
                <img
                  src={check}
                  alt="file check"
                  width="12.4px"
                  height="12.4px"
                  className=""
                />
              </div>
              &nbsp;
              <span
                className="text-overflow-clip font-bold"
                style={{ fontSize: "12px", color: "#179D52" }}
              >
                Helper Available
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div
                className="flex justify-content-center align-items-center"
                style={{
                  borderRadius: "50%",
                  height: "17px",
                  width: "17px",
                  borderBottomColor: "1px solid #FF6359",
                  boxShadow: "0 4px 4px 1px rgba(0, 0, 0, 0.3)",
                }}
              >
                <img
                  src={cross}
                  alt="file check"
                  width="12.4px"
                  height="12.4px"
                  className=""
                />
              </div>
              <span
                className="text-overflow-clip font-bold"
                style={{
                  fontSize: "12px",
                  color: "#FF6359",
                  textWrap: "nowrap",
                }}
              >
                Helper Unavailable
              </span>
            </div>
          </div>
        </div>
        <div className="grid pl-3 flex justify-content-between align-items-center">
          <div className="col-12 md:col-5">
            {/* <h3
                            className="font-medium p-0 m-0 pb-2"
                            style={{ fontSize: '20px', color: '#585858' }}
                        >
                            Shift date and time
                        </h3> */}
          </div>
          <div className="col-12 md:col-4">
            {/* <h3
                            className="font-medium p-0 m-0 pb-2"
                            style={{ fontSize: '20px', color: '#585858' }}
                        >
                            Shortlisted Helpers
                        </h3> */}
          </div>
          <div className="col-12 md:col-3">

          </div>
        </div>

        <div className="grid p-2 mb-3">
          {(() => {
            const unfilledShifts = sortedTimeSlots.filter(slot => slot.awardedAplicantId === null);
            const filledShifts = sortedTimeSlots.filter(slot => slot.awardedAplicantId !== null);

            return (
              <>
                {unfilledShifts.length > 0 && (
                  <>
                    {/* <h2
              className="font-bold p-0 m-0 pl-3"
              style={{ fontSize: "20px", color: "#585858" }}
            >
              Shifts Remaining
      </h2> */}
                    {unfilledShifts.map((slot, index) => (
                      <div key={`${slot.day}-${index}`} className="col-12 p-0 m-0">
                        <div
                          className=""
                          style={{
                            borderTop: "1px solid #DFDFDF",
                            borderBottom: "1px solid #DFDFDF",
                            height: "90px",
                          }}
                        >
                          <div className="grid">
                            <div className=" flex justify-content-between align-items-center col-12 md:col-5">
                              <div className="flex align-items-center ">
                                <span style={{ width: "70px" }} className={`${styles.clrFont} p-2 font-bold`}>
                                  {slot.day}
                                </span>
                                <div className="flex align-items-center gap-2 flex-wrap">
                                  <div
                                    className="p-2"
                                    style={{
                                      border: "1px solid #DFDFDF",
                                      borderRadius: "10px",
                                      display: "flex",
                                      alignItems: "center"
                                    }}
                                  >
                                    <img
                                      src={clock}
                                      alt="file check"
                                      width="13.5px"
                                      height="13.5px"
                                      className="text-gray-500"
                                    />
                                    <span
                                      className="p-2 font-bold"
                                      style={{
                                        color: "#585858",
                                        fontSize: "14px",
                                      }}
                                    >
                                      {formatTimeTo12Hour(slot.startTime)}
                                    </span>
                                  </div>
                                  {slot.endTime && (
                                    <>
                                      <span
                                        className="font-normal"
                                        style={{
                                          color: "#585858",
                                          fontSize: "12px",
                                        }}
                                      >
                                        to
                                      </span>
                                      <div
                                        className="p-2"
                                        style={{
                                          border: "1px solid #DFDFDF",
                                          borderRadius: "10px",
                                          display: "flex",
                                          alignItems: "center"
                                        }}
                                      >
                                        <img
                                          src={clocked}
                                          alt="file check"
                                          width="14.34px"
                                          height="13.5px"
                                          className="text-gray-500"
                                        />
                                        <span
                                          className="p-2 font-bold"
                                          style={{
                                            color: "#585858",
                                            fontSize: "14px",
                                          }}
                                        >
                                          {formatTimeTo12Hour(slot.endTime)}
                                        </span>
                                      </div>
                                    </>
                                  )}
                                </div>
                                <div className={`${styles.clrFont} pl-2 pr-2 font-bold`}>
                                  Total
                                  <div className={`${styles.clrFont} font-medium`}>
                                    {" "}
                                    ${slot.price}
                                  </div>
                                </div>
                              </div>

                              <div
                                style={{
                                  height: "90px",
                                  width: "2px",
                                  backgroundColor: "#DFDFDF",
                                }}
                              ></div>
                            </div>

                            <div className="col-12 md:col-7">
                              <div className="flex justify-content-evenly align-items-center gap-1">
                                <div className="flex align-items-center gap-2 md:col-7 overflow-auto">
                                  {slot.awardedAplicantId === null ? (
                                    slot.applicants.map((helper, helperIndex) => (
                                      <div
                                        key={helperIndex}
                                        className="relative"
                                        style={getImageStyles(helper.status)}
                                      >
                                        <div className={styles.imageContainer}>
                                          <img
                                            src={helper.imageSrc}
                                            alt="Shift"
                                            className={styles.shiftImage}
                                            style={{
                                              filter: isHelperAvailable(helper.status)
                                                ? "none"
                                                : "sepia(90%)",
                                            }}
                                          />
                                        </div>

                                        {helper.status !== c.applicantAvailabilityStatus.AVAILABLE && helper.status !== c.applicantAvailabilityStatus.PENDING && (
                                          <div
                                            className="absolute top-0 right-0"
                                            style={{
                                              backgroundColor: "#FFFFFF",
                                              borderRadius: "50%",
                                              width: "16px",
                                              height: "16px",
                                              display: "flex",
                                              justifyContent: "center",
                                              alignItems: "center",
                                              position: "absolute",
                                              marginTop: "-2px",
                                              marginRight: "-4px",
                                              zIndex: 10,
                                            }}
                                          >
                                            <img
                                              src={cross}
                                              alt="Unavailable"
                                              width="12px"
                                              height="12px"
                                            />
                                          </div>
                                        )}

                                        {isHelperAvailable(helper.status) && (
                                          <span className={styles.Imgcheckmark}>✓</span>
                                        )}
                                      </div>
                                    ))
                                  ) : (
                                    <div className=" flex gap-1 justify-content-around align-items-center ">
                                      <img
                                        src={
                                          slot.applicants.find(
                                            (a) =>
                                              a.applicantId === slot.awardedAplicantId
                                          )?.imageSrc
                                        }
                                        alt=""
                                        style={{
                                          height: "71px",
                                          width: "71px",
                                          borderRadius: "10px",
                                          minHeight: "71px",
                                          minWidth: "71px",
                                        }}
                                      />
                                      <h1
                                        className="p-0 m-0 font-bold mx-1"
                                        style={{
                                          color: "#585858",
                                          fontSize: "18px",
                                          textOverflow: "ellipsis",
                                          minWidth: "90px",
                                          maxWidth: "90px",
                                          textWrap: "nowrap",
                                          overflow: "hidden",
                                        }}
                                      >
                                        {" "}
                                        {
                                          slot.applicants.find(
                                            (a) =>
                                              a.applicantId === slot.awardedAplicantId
                                          )?.publicName
                                        }
                                      </h1>
                                      <div
                                        style={{
                                          border: "1px solid #179D52",
                                          borderRadius: "20px",
                                        }}
                                      >
                                        <p
                                          className="p-0 px-2 font-bold  m-1"
                                          style={{
                                            fontSize: "10px",
                                            color: "#179D52",
                                          }}
                                        >
                                          {" "}
                                          Job Awarded
                                        </p>
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <div
                                  style={{
                                    height: "90px",
                                    width: "2px",
                                    backgroundColor: "#DFDFDF",
                                  }}
                                ></div>
                                {slot.awardedAplicantId === null ? (
                                  <div
                                    className=" md:col- px-4 py-2  flex align-items-center justify-content-between"
                                    style={{
                                      backgroundColor: "#37A950",
                                      color: "#FFFFFF",
                                      width: "174px",
                                      height: "38px",
                                      borderRadius: "20px",
                                    }}
                                  >
                                    <span
                                      className="font-bold"
                                      style={{ fontSize: "20px" }}
                                    >
                                      {
                                        slot.applicants.filter(
                                          (h) =>
                                            h.status ===
                                            c.applicantAvailabilityStatus.AVAILABLE
                                        ).length
                                      }
                                    </span>

                                    <span
                                      className="font-bold"
                                      style={{ fontSize: "12px" }}
                                    >
                                      Helpers available
                                    </span>
                                  </div>
                                ) : (
                                  <div
                                    style={{
                                      width: "174px",
                                      height: "1px",
                                      backgroundColor: "transparent",
                                    }}
                                  ></div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
                {filledShifts.length > 0 && (
                  <>
                    <h2
                      className="font-bold p-0 m-0 pl-3 mt-3"
                      style={{ fontSize: "20px", color: "#585858" }}
                    >
                      Shifts filled
                    </h2>
                    {filledShifts.map((slot, index) => (
                      <div key={`${slot.day}-${index}`} className="col-12 p-0 m-0">
                        <div
                          className=""
                          style={{
                            borderTop: "1px solid #DFDFDF",
                            borderBottom: "1px solid #DFDFDF",
                            height: "90px",
                          }}
                        >
                          <div className="grid">
                            <div className=" flex justify-content-between align-items-center col-12 md:col-5">
                              <div className="flex align-items-center ">
                                <span style={{ width: "70px" }} className={`${styles.clrFont} p-2 font-bold`}>
                                  {slot.day}
                                </span>
                                <div className="flex align-items-center gap-2 flex-wrap">
                                  <div
                                    className="p-2"
                                    style={{
                                      border: "1px solid #DFDFDF",
                                      borderRadius: "10px",
                                      display: "flex",
                                      alignItems: "center"
                                    }}
                                  >
                                    <img
                                      src={clock}
                                      alt="file check"
                                      width="13.5px"
                                      height="13.5px"
                                      className="text-gray-500"
                                    />
                                    <span
                                      className="p-2 font-bold"
                                      style={{
                                        color: "#585858",
                                        fontSize: "14px",
                                      }}
                                    >
                                      {formatTimeTo12Hour(slot.startTime)}
                                    </span>
                                  </div>
                                  {slot.endTime && (
                                    <>
                                      <span
                                        className="font-normal"
                                        style={{
                                          color: "#585858",
                                          fontSize: "12px",
                                        }}
                                      >
                                        to
                                      </span>
                                      <div
                                        className="p-2"
                                        style={{
                                          border: "1px solid #DFDFDF",
                                          borderRadius: "10px",
                                          display: "flex",
                                          alignItems: "center"
                                        }}
                                      >
                                        <img
                                          src={clocked}
                                          alt="file check"
                                          width="14.34px"
                                          height="13.5px"
                                          className="text-gray-500"
                                        />
                                        <span
                                          className="p-2 font-bold"
                                          style={{
                                            color: "#585858",
                                            fontSize: "14px",
                                          }}
                                        >
                                          {formatTimeTo12Hour(slot.endTime)}
                                        </span>
                                      </div>
                                    </>
                                  )}
                                </div>
                                <div className={`${styles.clrFont} pl-2 pr-2 font-bold`}>
                                  Total
                                  <div className={`${styles.clrFont} font-medium`}>
                                    {" "}
                                    ${slot.price}
                                  </div>
                                </div>
                              </div>

                              <div
                                style={{
                                  height: "90px",
                                  width: "2px",
                                  backgroundColor: "#DFDFDF",
                                }}
                              ></div>
                            </div>

                            <div className="col-12 md:col-7">
                              <div className="flex justify-content-evenly align-items-center gap-1">
                                <div className="flex align-items-center gap-2 md:col-7 overflow-auto">
                                  {slot.awardedAplicantId === null ? (
                                    slot.applicants.map((helper, helperIndex) => (
                                      <div
                                        key={helperIndex}
                                        className="relative"
                                        style={getImageStyles(helper.status)}
                                      >
                                        <div className={styles.imageContainer}>
                                          <img
                                            src={helper.imageSrc}
                                            alt="Shift"
                                            className={styles.shiftImage}
                                            style={{
                                              filter: isHelperAvailable(helper.status)
                                                ? "none"
                                                : "sepia(90%)",
                                            }}
                                          />
                                        </div>

                                        {!isHelperAvailable(helper.status) && (
                                          <div
                                            className="absolute top-0 right-0"
                                            style={{
                                              backgroundColor: "#FFFFFF",
                                              borderRadius: "50%",
                                              width: "16px",
                                              height: "16px",
                                              display: "flex",
                                              justifyContent: "center",
                                              alignItems: "center",
                                              position: "absolute",
                                              marginTop: "-2px",
                                              marginRight: "-4px",
                                              zIndex: 10,
                                            }}
                                          >
                                            <img
                                              src={cross}
                                              alt="Unavailable"
                                              width="12px"
                                              height="12px"
                                            />
                                          </div>
                                        )}

                                        {isHelperAvailable(helper.status) && (
                                          <span className={styles.Imgcheckmark}>✓</span>
                                        )}
                                      </div>
                                    ))
                                  ) : (
                                    <div className=" flex gap-1 justify-content-around align-items-center ">
                                      <img
                                        src={
                                          slot.applicants.find(
                                            (a) =>
                                              a.applicantId === slot.awardedAplicantId
                                          )?.imageSrc
                                        }
                                        alt=""
                                        style={{
                                          height: "71px",
                                          width: "71px",
                                          borderRadius: "10px",
                                          minHeight: "71px",
                                          minWidth: "71px",
                                        }}
                                      />
                                      <h1
                                        className="p-0 m-0 font-bold mx-1"
                                        style={{
                                          color: "#585858",
                                          fontSize: "18px",
                                          textOverflow: "ellipsis",
                                          minWidth: "90px",
                                          maxWidth: "90px",
                                          textWrap: "nowrap",
                                          overflow: "hidden",
                                        }}
                                      >
                                        {" "}
                                        {
                                          slot.applicants.find(
                                            (a) =>
                                              a.applicantId === slot.awardedAplicantId
                                          )?.publicName
                                        }
                                      </h1>
                                      <div
                                        style={{
                                          border: "1px solid #179D52",
                                          borderRadius: "20px",
                                        }}
                                      >
                                        <p
                                          className="p-0 px-2 font-bold  m-1"
                                          style={{
                                            fontSize: "10px",
                                            color: "#179D52",
                                          }}
                                        >
                                          {" "}
                                          Job Awarded
                                        </p>
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <div
                                  style={{
                                    height: "90px",
                                    width: "2px",
                                    backgroundColor: "#DFDFDF",
                                  }}
                                ></div>
                                {slot.awardedAplicantId === null ? (
                                  <div
                                    className=" md:col- px-4 py-2  flex align-items-center justify-content-between"
                                    style={{
                                      backgroundColor: "#37A950",
                                      color: "#FFFFFF",
                                      width: "174px",
                                      height: "38px",
                                      borderRadius: "20px",
                                    }}
                                  >
                                    <span
                                      className="font-bold"
                                      style={{ fontSize: "20px" }}
                                    >
                                      {
                                        slot.applicants.filter(
                                          (h) =>
                                            h.status ===
                                            c.applicantAvailabilityStatus.AVAILABLE
                                        ).length
                                      }
                                    </span>

                                    <span
                                      className="font-bold"
                                      style={{ fontSize: "12px" }}
                                    >
                                      Helpers available
                                    </span>
                                  </div>
                                ) : (
                                  <div
                                    style={{
                                      width: "174px",
                                      height: "1px",
                                      backgroundColor: "transparent",
                                    }}
                                  ></div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </>
            );
          })()}
        </div>
      </div>
      {!viewShifts && (
        <div
          className="mt-3 p-3"
          style={{
            width: "952px",
            border: "1px solid #DFDFDF",
            borderRadius: "20px",
          }}
        >
          <h1
            className="m-0 p-0 font-bold"
            style={{ color: "#585858", fontSize: "30px" }}
          >
            Applicants (
            {
              availableApplicants.filter(
                (a) =>
                  a.applicationStatus !==
                  c.jobApplicationStatus.EXCLUDED_BY_CLIENT
              ).length
            }
            )
          </h1>
          {availableApplicants.filter(
            (a) =>
              a.applicationStatus !== c.jobApplicationStatus.EXCLUDED_BY_CLIENT
          ).length === 0 ? (
            <NoJobsCard description="Sit tight Helpers will apply to your job soon!" />
          ) : (
            availableApplicants
              .filter(
                (a) =>
                  a.applicationStatus !==
                  c.jobApplicationStatus.EXCLUDED_BY_CLIENT
              )
              .map((a) => (
                <div className="mt-2" key={a.applicantId}>
                  <AwardCard
                    publicName={a.publicName}
                    helperImgSrc={a.imageSrc}
                    onAwardJobClicked={() => handleAwardJobClicked(a.applicantId)}
                    onContactClicked={() => {
                      if (clientType === "1") {
                        navigate(
                          `/parent-home/inAppChat?userId=${a.applicantId}`
                        );
                      } else {
                        navigate(
                          `/business-home/inAppChat?userId=${a.applicantId}`
                        );
                      }
                    }}
                    onViewProfileClicked={() => {
                      redirectAfterHome(a.applicantId);
                    }}
                    // gettingHome={getGettingHomeLabel(a.gettingHome)}
                    // avgRating={a.applicantRatingsAvg}
                    // numRatings={a.applicantRatingsCount}
                    // responseRate={a.responseRate}
                    isSuperHelper={a.isSuperHelper}
                    isOneoffJob={false}
                    onDismissClicked={() => {
                      handleRemoveJob(a.id, "-1");
                    }}
                    applicantId={a.applicantId}
                    applicationStatus={a.applicationStatus}

                  />
                </div>
              ))
          )}

          {isDialogOpen && (
            <AwardShiftsTo
              isOpen={isDialogOpen}
              onClose={handleDialogClose}
              title={`Award Shifts to ${job?.applicants.find((a) => a.applicantId === clickedShiftId)
                ?.applicantFirstName
                }`}
              shifts={timeSlots}
              onShiftSelect={handleShiftSelect}
              onSelectAll={handleSelectAll}
              selectedAplicant={clickedShiftId}
              timeSlots={timeSlots}
              job={job}
            />
          )}
        </div>
      )}
      <CustomDialog
        visible={showPopup}
        style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%', backgroundColor: '#ffffff', borderRadius: '0px', overflowY: 'auto' }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile
          candidateId={selectedProviderId}
          onClose={handleCloseProfilePopup}
        />
      </CustomDialog>
    </>
  ) : (
    <>
      <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />

      <div
        className={`${className}`}
      >
        <div
          className="flex  pt-2 gap-1 flex-column"

        >
          <h2
            className="font-bold p-0 m-0"
            style={{ fontSize: "22px", color: "#585858", textDecoration: "underline" }}
          >
            {timeSlots.every((t) => t.awardedAplicantId === null)
              ? "Response Table"
              : ""}
          </h2>
          {timeSlots.every((t) => t.awardedAplicantId === null) && job.applicantsApplied <= 0 && (job.applicantsNotAvailable + job.applicantsNotInterested) <= 0 && (
            <div className="flex gap-1">
              <div className="flex align-items-center">
                <img
                  src={fileCheck}
                  alt="file check"
                  width="12.4px"
                  height="12.8px"
                  className="text-gray-500"
                />
                <h1 className={`${styles.clrFont} p-1 m-0 font-bold`}>
                  Invited: {totalInvited}
                </h1>
              </div>

              <div className="flex align-items-center">
                <FaRegEye
                  className="text-gray-500"
                  style={{ width: "12.4px", height: "12.8px" }}
                />
                <h1 className={`${styles.clrFont} p-1 m-0 font-bold`}>
                  Viewed: {totalViewed}
                </h1>
              </div>
            </div>
          )}
        </div>


        <div className="mt-2 mb-2">
          {(() => {
            const unfilledShifts = sortedTimeSlots.filter(slot => slot.awardedAplicantId === null);
            const filledShifts = sortedTimeSlots.filter(slot => slot.awardedAplicantId !== null);

            return (
              <>
                {unfilledShifts.length > 0 && (
                  <>
                    {/* <h2
              className="font-bold p-0 m-0 my-2"
              style={{ fontSize: "16px", color: "#585858", textDecoration: "underline" }}
            >
              Shifts Remaining
            </h2> */}
                    {unfilledShifts.map((slot, index) => (
                      <div key={`${slot.day}-${index}`} style={{
                        borderRadius: "20px",
                        boxShadow: "0px 0px 4px 0px #00000040",
                      }} className="m-0 mb-3">
                        <div>
                          <div className="flex px-4 pt-3 pb-2 gap-2 flex-column">
                            <div className="flex align-items-center gap-2">
                              <span
                                style={{ fontSize: "16px", color: "#585858" }}
                                className={`font-bold`}
                              >
                                {slot.day}
                              </span>
                              <div
                                style={{
                                  color: "#179D52",
                                  fontSize: "16px",
                                  fontWeight: "700",
                                }}
                              >
                                <span
                                  className="font-bold"
                                  style={{
                                    color: "#179D52",
                                    fontSize: "16px",
                                    fontWeight: "700",
                                  }}
                                >
                                  {formatTimeTo12Hour(slot.startTime)}
                                </span>
                              </div>
                              {slot.endTime && (
                                <>
                                  <span
                                    style={{
                                      color: "#179D52",
                                      fontSize: "16px",
                                      fontWeight: "700",
                                    }}
                                  >
                                    to
                                  </span>
                                  <div>
                                    <span
                                      className="font-bold"
                                      style={{
                                        color: "#179D52",
                                        fontSize: "16px",
                                        fontWeight: "700",
                                      }}
                                    >
                                      {formatTimeTo12Hour(slot.endTime)}
                                    </span>
                                  </div>
                                </>
                              )}
                            </div>
                            <div className="flex flex-row gap-2">
                              <div
                                style={{
                                  border: "1px solid #DFDFDF",
                                  backgroundColor: "#EDEDED",
                                  borderRadius: "10px",
                                  paddingInline: "5px",
                                  paddingBlock: "2px",
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center"
                                }}
                              >
                                <span
                                  style={{
                                    fontSize: "10px",
                                    fontWeight: "500",
                                    color: "#585858",
                                  }}
                                >
                                  {getShiftNameForSlot(timeSlots, slot)}
                                </span>
                              </div>
                              <div
                                style={{
                                  border: "1px solid #DFDFDF",
                                  borderRadius: "10px",
                                  paddingInline: "5px",
                                  display: "flex",
                                  justifyContent: "center",
                                  paddingBlock: "2px",
                                  alignItems: "center"
                                }}
                              >
                                <span
                                  style={{
                                    fontSize: "10px",
                                    fontWeight: "500",
                                    color: "#585858",
                                  }}
                                >
                                  {calculateDuration(slot.startTime, slot.endTime)}hrs
                                </span>
                              </div>
                              <div style={{
                                border: "1px solid #DFDFDF",
                                borderRadius: "10px",
                                paddingInline: "5px",
                                display: "flex",
                                justifyContent: "center",
                                paddingBlock: "2px",
                                alignItems: "center"
                              }}>
                                <span style={{
                                  fontSize: "10px",
                                  fontWeight: "600",
                                  color: "#585858",
                                }}> ${slot.price}total</span>
                              </div>
                            </div>
                          </div>
                          <Divider className="my-1" />
                          <div className="flex align-items-center gap-4 px-4 pb-3 pt-2">
                            <div className="flex align-items-center gap-2 w-max overflow-auto">
                              {slot.awardedAplicantId === null ? (
                                slot.applicants.map((helper, helperIndex) => (
                                  <div
                                    key={helperIndex}
                                    className="relative"
                                    style={getImageStyles(helper.status)}
                                  >
                                    <div className={styles.imageContainerMobile}>
                                      <img
                                        src={helper.imageSrc}
                                        alt="Shift"
                                        className={styles.shiftImage}
                                        style={{
                                          filter: isHelperAvailable(helper.status)
                                            ? "none"
                                            : "sepia(90%)",
                                        }}
                                      />
                                    </div>

                                    {helper.status !== c.applicantAvailabilityStatus.AVAILABLE && helper.status !== c.applicantAvailabilityStatus.PENDING && (

                                      <div
                                        className="absolute top-0 right-0"
                                        style={{
                                          backgroundColor: "#FFFFFF",
                                          borderRadius: "50%",
                                          width: "16px",
                                          height: "16px",
                                          display: "flex",
                                          justifyContent: "center",
                                          alignItems: "center",
                                          position: "absolute",
                                          marginTop: "-2px",
                                          marginRight: "-4px",
                                          zIndex: 10,
                                        }}
                                      >
                                        <img
                                          src={cross}
                                          alt="Unavailable"
                                          width="12px"
                                          height="12px"
                                        />
                                      </div>
                                    )}

                                    {isHelperAvailable(helper.status) && (
                                      <span className={styles.ImgcheckmarkMobile}>✓</span>
                                    )}
                                  </div>
                                ))
                              ) : (
                                <div className="flex gap-1 justify-content-around align-items-center">
                                  <img
                                    src={
                                      slot.applicants.find(
                                        (a) => a.applicantId === slot.awardedAplicantId
                                      )?.imageSrc
                                    }
                                    alt=""
                                    style={{
                                      height: "51px",
                                      width: "51px",
                                      borderRadius: "10px",
                                    }}
                                  />
                                  <h1
                                    className="p-0 m-0 font-bold mx-1"
                                    style={{
                                      color: "#585858",
                                      fontSize: "10px",
                                      textOverflow: "ellipsis",
                                      textWrap: "nowrap",
                                      overflow: "hidden",
                                    }}
                                  >
                                    {
                                      slot.applicants.find(
                                        (a) => a.applicantId === slot.awardedAplicantId
                                      )?.publicName
                                    }
                                  </h1>
                                  <div
                                    style={{
                                      border: "1px solid #179D52",
                                      borderRadius: "20px",
                                    }}
                                  >
                                    <p
                                      className="p-0 px-2 font-bold m-1"
                                      style={{
                                        fontSize: "10px",
                                        color: "#179D52",
                                      }}
                                    >
                                      Job Awarded
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>

                            {slot.awardedAplicantId === null && (
                              <div
                                className="flex align-items-center justify-content-between"
                                style={{
                                  backgroundColor: "#FFFFFF",
                                  color: "#179D52",
                                  border: "1px solid #179D52",
                                  borderRadius: "20px",
                                  paddingInline: "10px",
                                  gap: "3px",
                                }}
                              >
                                <span className="font-bold" style={{ fontSize: "10px" }}>
                                  {
                                    slot.applicants.filter(
                                      (h) =>
                                        h.status ===
                                        c.applicantAvailabilityStatus.AVAILABLE
                                    ).length
                                  }
                                </span>

                                <span className="font-bold" style={{ fontSize: "10px", textWrap: "nowrap" }}>
                                  Helpers available
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
                {filledShifts.length > 0 && (
                  <>
                    <h2
                      className="font-bold p-0 m-0 my-2"
                      style={{ fontSize: "16px", color: "#585858", textDecoration: "underline" }}
                    >
                      Shifts filled
                    </h2>
                    {filledShifts.map((slot, index) => (
                      <div key={`${slot.day}-${index}`} style={{
                        borderRadius: "20px",
                        boxShadow: "0px 0px 4px 0px #00000040",
                      }} className="m-0 mb-3">
                        <div>
                          <div className="flex px-4 pt-3 pb-2 gap-2 flex-column">
                            <div className="flex align-items-center gap-2">
                              <span
                                style={{ fontSize: "16px", color: "#585858" }}
                                className={`font-bold`}
                              >
                                {slot.day}
                              </span>
                              <div
                                style={{
                                  color: "#179D52",
                                  fontSize: "16px",
                                  fontWeight: "700",
                                }}
                              >
                                <span
                                  className="font-bold"
                                  style={{
                                    color: "#179D52",
                                    fontSize: "16px",
                                    fontWeight: "700",
                                  }}
                                >
                                  {formatTimeTo12Hour(slot.startTime)}
                                </span>
                              </div>
                              {slot.endTime && (
                                <>
                                  <span
                                    style={{
                                      color: "#179D52",
                                      fontSize: "16px",
                                      fontWeight: "700",
                                    }}
                                  >
                                    to
                                  </span>
                                  <div>
                                    <span
                                      className="font-bold"
                                      style={{
                                        color: "#179D52",
                                        fontSize: "16px",
                                        fontWeight: "700",
                                      }}
                                    >
                                      {formatTimeTo12Hour(slot.endTime)}
                                    </span>
                                  </div>
                                </>
                              )}
                            </div>
                            <div className="flex flex-row gap-2">
                              <div
                                style={{
                                  border: "1px solid #DFDFDF",
                                  backgroundColor: "#EDEDED",
                                  borderRadius: "10px",
                                  paddingInline: "5px",
                                  paddingBlock: "2px",
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center"
                                }}
                              >
                                <span
                                  style={{
                                    fontSize: "10px",
                                    fontWeight: "500",
                                    color: "#585858",
                                  }}
                                >
                                  {getShiftNameForSlot(timeSlots, slot)}
                                </span>
                              </div>
                              <div
                                style={{
                                  border: "1px solid #DFDFDF",
                                  borderRadius: "10px",
                                  paddingInline: "5px",
                                  display: "flex",
                                  justifyContent: "center",
                                  paddingBlock: "2px",
                                  alignItems: "center"
                                }}
                              >
                                <span
                                  style={{
                                    fontSize: "10px",
                                    fontWeight: "500",
                                    color: "#585858",
                                  }}
                                >
                                  {calculateDuration(slot.startTime, slot.endTime)}hrs
                                </span>
                              </div>
                              <div style={{
                                border: "1px solid #DFDFDF",
                                borderRadius: "10px",
                                paddingInline: "5px",
                                display: "flex",
                                justifyContent: "center",
                                paddingBlock: "2px",
                                alignItems: "center"
                              }}>
                                <span style={{
                                  fontSize: "10px",
                                  fontWeight: "600",
                                  color: "#585858",
                                }}> ${slot.price}total</span>
                              </div>
                            </div>
                          </div>
                          <Divider className="my-1" />
                          <div className="flex align-items-center gap-4 px-4 pb-3 pt-2">
                            <div className="flex align-items-center gap-2 w-max overflow-auto">
                              {slot.awardedAplicantId === null ? (
                                slot.applicants.map((helper, helperIndex) => (
                                  <div
                                    key={helperIndex}
                                    className="relative"
                                    style={getImageStyles(helper.status)}
                                  >
                                    <div className={styles.imageContainerMobile}>
                                      <img
                                        src={helper.imageSrc}
                                        alt="Shift"
                                        className={styles.shiftImage}
                                        style={{
                                          filter: isHelperAvailable(helper.status)
                                            ? "none"
                                            : "sepia(90%)",
                                        }}
                                      />
                                    </div>

                                    {!isHelperAvailable(helper.status) && (
                                      <div
                                        className="absolute top-0 right-0"
                                        style={{
                                          backgroundColor: "#FFFFFF",
                                          borderRadius: "50%",
                                          width: "16px",
                                          height: "16px",
                                          display: "flex",
                                          justifyContent: "center",
                                          alignItems: "center",
                                          position: "absolute",
                                          marginTop: "-2px",
                                          marginRight: "-4px",
                                          zIndex: 10,
                                        }}
                                      >
                                        <img
                                          src={cross}
                                          alt="Unavailable"
                                          width="12px"
                                          height="12px"
                                        />
                                      </div>
                                    )}

                                    {isHelperAvailable(helper.status) && (
                                      <span className={styles.ImgcheckmarkMobile}>✓</span>
                                    )}
                                  </div>
                                ))
                              ) : (
                                <div className="flex gap-1 justify-content-around align-items-center">
                                  <img
                                    src={
                                      slot.applicants.find(
                                        (a) => a.applicantId === slot.awardedAplicantId
                                      )?.imageSrc
                                    }
                                    alt=""
                                    style={{
                                      height: "51px",
                                      width: "51px",
                                      borderRadius: "10px",
                                    }}
                                  />
                                  <h1
                                    className="p-0 m-0 font-bold mx-1"
                                    style={{
                                      color: "#585858",
                                      fontSize: "10px",
                                      textOverflow: "ellipsis",
                                      textWrap: "nowrap",
                                      overflow: "hidden",
                                    }}
                                  >
                                    {
                                      slot.applicants.find(
                                        (a) => a.applicantId === slot.awardedAplicantId
                                      )?.publicName
                                    }
                                  </h1>
                                  <div
                                    style={{
                                      border: "1px solid #179D52",
                                      borderRadius: "20px",
                                    }}
                                  >
                                    <p
                                      className="p-0 px-2 font-bold m-1"
                                      style={{
                                        fontSize: "10px",
                                        color: "#179D52",
                                      }}
                                    >
                                      Job Awarded
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>

                            {slot.awardedAplicantId === null && (
                              <div
                                className="flex align-items-center justify-content-between"
                                style={{
                                  backgroundColor: "#FFFFFF",
                                  color: "#179D52",
                                  border: "1px solid #179D52",
                                  borderRadius: "20px",
                                  paddingInline: "10px"
                                }}
                              >
                                <span className="font-bold" style={{ fontSize: "10px" }}>
                                  {
                                    slot.applicants.filter(
                                      (h) =>
                                        h.status ===
                                        c.applicantAvailabilityStatus.AVAILABLE
                                    ).length
                                  }
                                </span>

                                <span className="font-bold" style={{ fontSize: "10px", textWrap: "nowrap" }}>
                                  Helpers available
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </>
            );
          })()}
        </div>
      </div>
      {!viewShifts && (
        <div
          className="mt-1"
        >
          <h1
            className="m-0 p-0 font-bold"
            style={{ color: "#585858", fontSize: "22px" }}
          >
            Applicants (
            {
              availableApplicants.filter(
                (a) =>
                  a.applicationStatus !==
                  c.jobApplicationStatus.EXCLUDED_BY_CLIENT
              ).length
            }
            )
          </h1>
          {availableApplicants.filter(
            (a) =>
              a.applicationStatus !== c.jobApplicationStatus.EXCLUDED_BY_CLIENT
          ).length === 0 ? (
            <NoJobsCard description="Sit tight Helpers will apply to your job soon!" />
          ) : (
            availableApplicants
              .filter(
                (a) =>
                  a.applicationStatus !==
                  c.jobApplicationStatus.EXCLUDED_BY_CLIENT
              )
              .map((a) => (
                <div className="mt-2" key={a.applicantId}>
                  <AwardCard
                    publicName={a.publicName}
                    location={a.suburb}
                    helperImgSrc={a.imageSrc}

                    onAwardJobClicked={() => handleAwardJobClicked(a.applicantId)}
                    onContactClicked={() => {
                      if (clientType === "1") {
                        navigate(
                          `/parent-home/inAppChat?userId=${a.applicantId}`
                        );
                      } else {
                        navigate(
                          `/business-home/inAppChat?userId=${a.applicantId}`
                        );
                      }
                    }}
                    onViewProfileClicked={() => {

                      redirectAfterHome(a.applicantId);
                    }}
                    // gettingHome={getGettingHomeLabel(a.gettingHome)}
                    avgRating={a.applicantRatingsAvg}
                    numRatings={a.applicantRatingsCount}
                    // responseRate={a.responseRate}
                    isSuperHelper={a.isSuperHelper}
                    applicationStatus={a.applicationStatus}
                    applicantId={a.applicantId}
                    isOneoffJob={false}
                    onDismissClicked={() => {
                      handleRemoveJob(a.id, "-1");
                    }}
                  />
                </div>
              ))
          )}

          {isDialogOpen && (
            <AwardShiftsTo
              isOpen={isDialogOpen}
              onClose={handleDialogClose}
              title={`Award Shifts to ${job?.applicants.find((a) => a.applicantId === clickedShiftId)
                ?.applicantFirstName
                }`}
              shifts={timeSlots}
              onShiftSelect={handleShiftSelect}
              onSelectAll={handleSelectAll}
              selectedAplicant={clickedShiftId}
              timeSlots={timeSlots}
              job={job}
            />
          )}
        </div>
      )}
    </>
  );
};

export default WeeklyScheduleds;
