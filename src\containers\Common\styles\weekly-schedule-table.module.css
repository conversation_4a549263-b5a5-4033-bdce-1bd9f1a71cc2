.WeeklyTableContainer {
  /* overflow-x: auto; */
  width: 952px;
}

.WeeklyHeader {
  background-color: #dfdfdf;
  font-weight: bold;
  border: 1px solid #dfdfdf;
  border-radius: 10px 10px 0 0;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.WeeklyHeaderItem {
  flex: 1;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  padding-left: 61px;
}

.WeeklyRow {
  display: flex;
  border: 1px solid #ddd;
  border-top: none;
  padding-block: 20px;
  align-items: center;
}
.dateCell {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-inline: 30px;
  margin-right: 27px;
  width: 120px;
}
.timeGet {
  width: 102px;
  height: 46px;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}
.imagesSample {
  width: 49px;
  height: 46px;
  border-radius: 50%;
  border: 1px solid #179d52;
}
.helperName {
  font-size: 18px;
  font-weight: 700;
  color: #585858;
  margin: 0px;
}
.viewProfile {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 14px;
  font-weight: 300;
  margin: 0px;
  padding: 0px;
  text-decoration: underline;
  cursor: pointer;
}
.approvedStatusBtn {
  width: 121px;
  height: 34px;
  border-radius: 20px;
  border: 2px solid #179d52;
  background-color: #ffffff;
  color: #179d52;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
}
.pendingStatusBtn {
  width: 121px;
  height: 34px;
  border-radius: 20px;
  border: 2px solid #ff6359;
  background-color: #ffffff;
  color: #ff6359;
  font-size: 10px;
  font-weight: 700;
  text-wrap: nowrap;
  cursor: pointer;
}
.inviteHelpers {
  width: 107px;
  height: 34px;
  background-color: #ffa500;
  border-radius: 10px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0px 4px 4px 0px #00000040;
  border: none;
  cursor: pointer;
}
.viewSchedule {
  background-color: transparent;
  font-size: 14px;
  font-weight: 300;
  color: #585858;
  text-decoration: underline;
  border: none;
  text-wrap: nowrap;
  cursor: pointer;
}

.WeeklyCell {
  flex: 1;
  padding: 12px;
}

.WeeklyCell:last-child {
  border-right: none;
}
.TableDivider {
  margin-left: 30px;
}
.TableDividerTwo {
  margin-left: 40px;
}
.WeeklyTableContainerMobile {
  width: 100%;
}

.WeeklyHeaderMobile {
  background-color: #dfdfdf;
  font-weight: bold;
  border: 1px solid #dfdfdf;
  border-radius: 10px 10px 0 0;
  height: 64px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.WeeklyHeaderItemMobile {
  flex: 1;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  text-align: center;
}

.WeeklyRowMobile {
  display: flex;
  border: 1px solid #ddd;
  border-top: none;
  padding-block: 20px;
  align-items: center;
  justify-content: space-around;
}

.dateCellMobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 120px;
}

.dayTextMobile {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #585858;
}

.timeCellMobile {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeGetMobile {
  width: 102px;
  height: 46px;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}

.timeSeparatorMobile {
  margin: 0;
  font-size: 14px;
  color: #585858;
}

.helperCellMobile {
  display: flex;
  align-items: center;
  justify-content: center;
}

.helperInfoMobile {
  display: flex;
  align-items: center;
  gap: 2px;
}

.imagesSampleMobile {
  width: 49px;
  height: 46px;
  border-radius: 50%;
  border: 1px solid #179d52;
}

.helperDetailsMobile {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
}

.viewProfileMobile {
  background-color: transparent;
  border: none;
  color: #585858;
  font-size: 12px;
  font-weight: 300;
  text-decoration: underline;
  cursor: pointer;
  padding: 0 !important;
}

.inviteSectionMobile {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.inviteHelpersMobile {
  width: 107px;
  height: 34px;
  background-color: #ffa500;
  border-radius: 10px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0px 4px 4px 0px #00000040;
  border: none;
  cursor: pointer;
}

.viewScheduleMobile {
  background-color: transparent;
  font-size: 14px;
  font-weight: 300;
  color: #585858;
  text-decoration: underline;
  border: none;
  cursor: pointer;
}

.statusCellMobile {
  display: flex;
  justify-content: center;
}

.approvedStatusBtnMobile {
  width: 121px;
  height: 34px;
  border-radius: 20px;
  border: 2px solid #179d52;
  background-color: #ffffff;
  color: #179d52;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
}
.cardContainerMobile {
  width: 100%;
  margin-top: 10px;
}

.scheduleCardMobile {
  border: 1px solid #179d52;
  border-radius: 20px;
  background: white;
  margin-bottom: 16px;
  box-shadow: 0px 0px 4px 0px #00000040;

}

.cardHeaderMobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-inline: 20px;
  padding-top: 15px;
}

.dayTitleMobile {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  margin: 0;
}

.statusTextMobile {
  color: #179d52;
  font-size: 12px;
  display: flex;
  align-items: center;
  font-weight: 400;
  gap: 4px;
}

.statusDotMobile {
  width: 6px;
  height: 6px;
  background-color: #179d52;
  border-radius: 50%;
}

.dateRowMobile {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #585858;
  font-size: 14px;
  padding-inline: 20px;
}

.timeRowMobile {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #585858;
  padding-inline: 20px;
  font-size: 14px;
}

.helperSectionMobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-block: 12px;
  padding-inline: 20px;
}
.helperImageMobile {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #179d52;
}

.helperNameMobile {
  font-size: 16px;
  font-weight: 700;
  color: #585858;
  margin: 0;
}

.chatButtonMobile {
  border-radius: 20px;
  font-size: 14px;
  font-weight: 800;
  padding-block: 5px;
  color: #fff;
  padding-inline: 30px;
  border: none;
  background-color: #ffa500;
  cursor: pointer;
}
.ImgcheckmarkMobile {
  position: relative;
  top: -16px;
  right: 16px;
  background-color: white;
  color: #179D52;
  font-size: 8px;
  font-weight: bold;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #179D52;
}
.WeeklyRowHistroy {
  display: flex;
  border-top: none;
  align-items: center;
  gap: 10px;
}
.HistroyDivider{
  width: 95%;
}
.dateCellHistroy {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin-right: 17px; */
  width: 120px;
}
.timeGetHistroy {
  width: 102px;
  height: 41px;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}