/* Default Styles (for larger screens) */
.customButton {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  height: 46px;
  width: 362px;
  max-width: 100%;
  min-height: 46px;
  background-color: #FFA500;
  margin-top: 28px;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 700;
  text-align: center;
  padding: 13px 10px;
  border: none;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  /* transition: background-color 0.3s ease; */
}

.customButton:hover,
.customButton:focus {
  background-color: #FF8C00; /* Slightly darker shade on hover/focus */
}

/* Disabled <PERSON><PERSON> Styles */
.customButton:disabled {
  font-weight: 400 !important;
  background-color: #DFDFDF; /* Greyed-out background */
  color: #585858; /* Muted text color */
  cursor: not-allowed; /* Disable the pointer */
}

.customButton:disabled:hover,
.customButton:disabled:focus {
  background-color: #DFDFDF; /* Keep the background color the same on hover/focus */
}


/* Small Devices (up to 600px) */
@media (max-width: 600px) {
  .customButton {
    max-width: 100%;
      height: 40px;
      font-size: 12px;
      padding: 10px 8px;
  }
  
  .customButton:disabled {
      height: 40px;
      font-size: 12px;
  }
}

/* Medium Devices (600px to 1024px) */
@media (min-width: 600px) and (max-width: 1024px) {
  .customButton {
    max-width: 100%;
      height: 44px;
      font-size: 13px;
      padding: 12px 9px;
  }

  .customButton:disabled {
      height: 44px;
      font-size: 13px;
  }
}

/* Large Devices (1024px and above) */
@media (min-width: 1024px) {
  .customButton {
      width: 362px;
      height: 46px;
      font-size: 14px;
  }

  .customButton:disabled {
      width: 362px;
      height: 46px;
      font-size: 14px;
  }
}
