.customDialog {
  width: 100%;
  max-width: 987px;
  margin: 0 auto; /* Center the dialog */
  /* padding: 20px; */
  background-color: #fff;
  border-radius: 33px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.customDialogContent {
  width: 100%; /* Full width for content */
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

/* Additional Flexibility for Inner Content */
.customDialogContent > div {
  width: 100%;
  height: unset !important;
  position: relative;
}
.mobileDialog {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  border-radius: 0 !important;
  max-height: none !important;
}

.mobileContent {
  min-height: 100%;
}

/* Preserve your existing media queries */
@media (max-width: 768px) {
  .customDialog:not(.mobileDialog) {
    width: 95%;
    max-height: 75vh;
    margin-inline: 10px;
  }
}

@media (max-width: 480px) {
  .customDialog:not(.mobileDialog) {
    width: 90%;
    max-height: 70vh;
    margin-inline: 10px;
  }
}
/* Media Queries */
@media (max-width: 768px) {
  .customDialog {
    width: 95%; /* Adjust dialog width for smaller screens */
    max-height: 75vh; /* Reduce height for smaller screens */
    margin-inline: 10px;
  }
}

@media (max-width: 480px) {
  .customDialog {
    width: 90%; /* Full width on mobile devices */
    max-height: 70vh; /* Further reduce height on small screens */
    margin-inline: 10px;
  }
}
