import { CSSProperties, useEffect, useState } from "react";
import clockStart from "../../../../../assets/images/Icons/clockstart.png";
import clockEnd from "../../../../../assets/images/Icons/clockend.png";
import styles from "../../../styles/job-shifts.module.css";
import { Schedule } from "./Interface";
import useIsMobile from "../../../../../hooks/useIsMobile";

const weekMap = new Map<number, string>([
  [1, "Mon"],
  [2, "Tu<PERSON>"],
  [3, "Wed"],
  [4, "Thurs"],
  [5, "Fri"],
  [6, "Sat"],
  [0, "Sun"],
]);

const inlineStyles: Map<string, CSSProperties> = new Map();
inlineStyles.set("Heading 1", {
  color: "#585858",
  fontWeight: "700",
  fontSize: "16px",
});
inlineStyles.set("Heading 2", {
  color: "#585858",
  fontWeight: "700",
  fontSize: "14px",
});
inlineStyles.set("Content", {
  color: "#585858",
  fontWeight: "400",
  fontSize: "12px",
});
inlineStyles.set("Content 2", {
  color: "#585858",
  fontWeight: "500",
  fontSize: "16px",
});

type Props = {
  schedule: Schedule;
  price: number;
  jobType?: number;
  subJobType?: number;
  onSchedulePriceChange: (
    index: number,
    shift: {
      hourlyPrice: number;
      isRequired: boolean;
      jobEndTime: string;
      jobStartTime: string;
      price: number;
    }
  ) => void;
};
function getJobName(jobType, subJobType = 0): { job: string; shift: string[] } {
  const mappings = {
    jobType: {
      1: { job: "Ad-hoc Childcare", shift: ["Morning", "Afternoon"] },
      2: { job: "Recurring Childcare", shift: ["Morning", "Afternoon"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["Before School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
      64: {
        job: "Primary School Tutoring",
        shift: ["Before School", "After School"],
      },
      128: {
        job: "High School Tutoring",
        shift: ["Before School", "After School"],
      },
      256: { job: "Odd Job", shift: ["Morning", "Afternoon"] },
    },
    subJobType: {
      1: { job: "Laundry", shift: ["Morning", "Afternoon"] },
      2: { job: "Errand Running", shift: ["Morning", "Afternoon"] },
      4: { job: "Outdoor Chores", shift: ["Morning", "Afternoon"] },
      8: { job: "Help for the Elderly", shift: ["Morning", "Afternoon"] },
      16: { job: "Other Odd Jobs", shift: ["Morning", "Afternoon"] },
    },
    recurringChildcare: {
      2: { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] },
      4: {
        job: "Before School Care",
        shift: ["Before School", "Second Shift"],
      },
      8: {
        job: "After School Care",
        shift: ["After School", "Second Shift"],
      },
      12: {
        job: "Before & After School Care",
        shift: ["Before School", "After School"],
      },
    },
  };

  if (jobType === 2) {
    return subJobType === 0
      ? { job: "Daytime Nanny", shift: ["Nanny Shift", "Second Shift"] }
      : mappings.recurringChildcare[jobType] || {
          job: "Recurring Childcare",
          shift: ["Morning", "Afternoon"],
        };
  }

  if (jobType === 256 && subJobType > 0) {
    return (
      mappings.subJobType[subJobType] || {
        job: "Odd Job",
        shift: ["Morning", "Afternoon"],
      }
    );
  }

  return (
    mappings.jobType[jobType] || {
      job: "Unknown Job",
      shift: ["Morning", "Afternoon"],
    }
  );
}
function ShiftRow({
  label,
  dayOfWeak,
  startTime,
  endTime,
  rate,
  changeRate,
  jobType,
  subJobType = 0,
  index,
}: {
  label: string;
  dayOfWeak: number;
  startTime: string;
  endTime: string;
  rate: number;
  jobType?: number;
  subJobType?: number;
  index?: number;
  changeRate: (value: string, hours: number) => void;
}) {
  const jobInfo = getJobName(jobType, subJobType);
  const shiftName = jobInfo.shift[index];
  const { isMobile } = useIsMobile();

  function getTimeDifferenceInHourMinuteFormat(start: string, end: string): number {
    const [startHours, startMinutes] = start.split(":").map(Number);
    const [endHours, endMinutes] = end.split(":").map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    let differenceInMinutes = endTotalMinutes - startTotalMinutes;
    if (differenceInMinutes < 0) {
      differenceInMinutes += 24 * 60;
    }

    const hours = Math.floor(differenceInMinutes / 60);
    const minutes = differenceInMinutes % 60;

    return parseFloat((hours + minutes / 60).toFixed(2));
  }

  function convertTo12HourFormat(time: string): string {
    const [hourStr, minuteStr] = time.split(":");
    const hour = parseInt(hourStr, 10);
    const minute = parseInt(minuteStr, 10);

    const period = hour >= 12 ? "PM" : "AM";

    const hour12 = hour % 12 || 12;

    return `${hour12}:${minute.toString().padStart(2, "0")} ${period}`;
  }

  const hours = getTimeDifferenceInHourMinuteFormat(startTime, endTime);

  useEffect(() => {
    changeRate(rate.toString(), hours);
  }, [rate]);

  return !isMobile ? (
    <div className="grid align-items-center pt-3 gap-2">
      <div className="col-12 md:col-2">
        <p className="m-0" style={inlineStyles.get("Content")}>
          <span>
            <span className="mr-1" style={inlineStyles.get("Heading 1")}>
              {weekMap.get(dayOfWeak)}
            </span>
            {label}
          </span>
        </p>
      </div>
      <div className="col-12 md:col-5 flex gap-2 align-items-center">
        <div
          className="flex align-items-center justify-content-center gap-2 p-2"
          style={{
            border: "1px solid #DFDFDF",
            borderRadius: "10px",
            width: "104px",
          }}
        >
          <img src={clockStart} alt="start" height="13.5px" width="13.5" />
          <p className="m-0" style={inlineStyles.get("Heading 2")}>
            {convertTo12HourFormat(startTime)}
          </p>
        </div>
        <p className="m-0" style={inlineStyles.get("Content")}>
          to
        </p>
        <div
          className="flex just justify-content-center align-items-center gap-2 p-2"
          style={{
            border: "1px solid #DFDFDF",
            borderRadius: "10px",
            width: "104px",
          }}
        >
          <img src={clockEnd} alt="end" height="13.5px" width="13.5" />
          <p className="m-0" style={inlineStyles.get("Heading 2")}>
            {convertTo12HourFormat(endTime)}
          </p>
        </div>
      </div>
      <div className="col-12 md:col-5 grid gap-3 justify-content-end">
        <div className="flex flex-column align-items-center">
          <p className="m-0" style={inlineStyles.get("Heading 1")}>
            Hours
          </p>
          <p className="m-0" style={inlineStyles.get("Content 2")}>
            {hours}
          </p>
        </div>
        <div className="flex flex-column align-items-center">
          <p className="m-0" style={inlineStyles.get("Heading 1")}>
            Rate
          </p>
          <input
            type="text"
            name="rate"
            id="rate"
            value={rate}
            className={styles.rateInput}
            onClick={(e) => {
              e.preventDefault();
              changeRate("", hours);
            }}
            onChange={(e) => {
              e.preventDefault();
              if (/^\d*$/.test(e.target.value)) {
                changeRate(e.target.value, hours);
              } else {
                changeRate("", hours);
              }
            }}
          />
        </div>
        <div className="flex flex-column align-items-center">
          <p className="m-0" style={inlineStyles.get("Heading 1")}>
            Total
          </p>
          <p className="m-0" style={inlineStyles.get("Content 2")}>
            ${(hours * Number(rate)).toFixed(2)}
          </p>
        </div>
      </div>
    </div>
  ) : (
    <>
      <div className={styles.container}>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            minWidth: "207px",
          }}
        >
          {/* Time Section */}
          <div className={styles.timeInfo}>
            <p className={`m-0 ${styles.day}`}>
              <span className={styles.dayLabel}>{weekMap.get(dayOfWeak)}:</span>
            </p>
            <p className={`m-0 ${styles.time}`}>{convertTo12HourFormat(startTime)}</p>
            <p className={`m-0 ${styles.separator}`}>to</p>
            <p className={`m-0 ${styles.time}`}>{convertTo12HourFormat(endTime)}</p>
          </div>

          {/* Tags Section */}
          <div className={styles.tags}>
            <span className={styles.tag}>{shiftName}</span>
            <span className={styles.tag}>{hours} hrs</span>
            <span className={styles.tag}>
              ${(hours * Number(rate)) % 1 === 0 ? (hours * Number(rate)).toString() : (hours * Number(rate)).toFixed(1)} total
            </span>
          </div>
        </div>
        {/* Rate Section */}
        <div className={styles.rateSection}>
          <input
            type="text"
            name="rate"
            id="rate"
            value={rate ? `$${rate}` : ""}
            className={styles.rateInputMobile}
            onClick={(e) => {
              e.preventDefault();
              changeRate("", hours);
            }}
            onChange={(e) => {
              e.preventDefault();
              const inputValue = e.target.value.replace(/^\$/, ""); // Remove $ if present
              if (/^\d*$/.test(inputValue)) {
                changeRate(inputValue, hours);
              } else {
                changeRate("", hours);
              }
            }}
          />
        </div>
      </div>
    </>
  );
}

function JobShifts({ schedule, price, jobType, subJobType = 0, onSchedulePriceChange }: Props) {
  const safePrice = !isFinite(price) || price === null ? 0 : price;

  const [rate1, setRate1] = useState<number>(
    schedule.shifts[0].hourlyPrice !== null && isFinite(schedule.shifts[0].hourlyPrice) ? schedule.shifts[0].hourlyPrice : safePrice
  );

  const [rate2, setRate2] = useState<number>(
    schedule.shifts.length > 1 && schedule.shifts[1].hourlyPrice !== null && isFinite(schedule.shifts[1].hourlyPrice)
      ? schedule.shifts[1].hourlyPrice
      : safePrice
  );

  const [prevPrice, setPrevPrice] = useState<number>(safePrice);

  useEffect(() => {
    if (!Object.is(prevPrice, safePrice)) {
      setPrevPrice(safePrice);
    }
  }, [safePrice, prevPrice]);

  useEffect(() => {
    if (!isFinite(rate1) || rate1 === prevPrice) {
      setRate1(safePrice);
    }
  }, [prevPrice, safePrice, rate1]);

  useEffect(() => {
    if (!isFinite(rate2) || rate2 === prevPrice) {
      setRate2(safePrice);
    }
  }, [prevPrice, safePrice, rate2]);

  return (
    <div className="py-2" style={{ borderTop: "1px solid #F1F1F1" }}>
      <ShiftRow
        label=""
        dayOfWeak={schedule.dayOfWeek}
        startTime={schedule.shifts[0].jobStartTime}
        endTime={schedule.shifts[0].jobEndTime}
        rate={rate1}
        index={0}
        jobType={jobType}
        subJobType={subJobType}
        changeRate={(rate, hour) => {
          onSchedulePriceChange(0, {
            ...schedule.shifts[0],
            hourlyPrice: Number(rate),
            price: Number(rate) * hour,
          });
          setRate1(Number(rate));
        }}
      />
      {schedule.shifts.length > 1 && (
        <ShiftRow
          label=""
          dayOfWeak={schedule.dayOfWeek}
          startTime={schedule.shifts[1].jobStartTime}
          endTime={schedule.shifts[1].jobEndTime}
          rate={rate2}
          index={1}
          jobType={jobType}
          subJobType={subJobType}
          changeRate={(rate, hour) => {
            onSchedulePriceChange(1, {
              ...schedule.shifts[1],
              hourlyPrice: Number(rate),
              price: Number(rate) * hour,
            });
            setRate2(Number(rate));
          }}
        />
      )}
    </div>
  );
}

export default JobShifts;
