import React, { useState, useEffect, useRef, KeyboardEvent, useCallback } from "react";
import styles from "../styles/AboutMe.module.css";
import { Divider } from "primereact/divider";
import { InputText } from "primereact/inputtext";
import "../../../components/utils/util.css";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import CustomButton from "../../../commonComponents/CustomButton";
import { updateSessionInfo, updateUser } from "../../../store/tunks/sessionInfoTunk";
import { FaCheck } from "react-icons/fa6";
import CustomDialog from "../../Common/CustomDialog";
import Auth from "../../../services/authService";
import {
  validateEmail,
  validatePhoneNumber,
} from "../../../components/utils/validation";
import useLoader from "../../../hooks/LoaderHook";
import { InputTextarea } from "primereact/inputtextarea";
import ReactCrop, { centerCrop, Crop, makeAspectCrop, PixelCrop } from "react-image-crop";
import Userimage from "../../../assets/images/Group_light.png"
import CookiesConstant from "../../../helper/cookiesConst";
import utils from "../../../components/utils/util";
import myfamilystyles from "../styles/my-family.module.css";
import useIsMobile from "../../../hooks/useIsMobile";
import HorizontalNavigation from "../../Common/HorizontalNavigationMobile";
import { updateActiveTabIndex } from "../../../store/slices/accountSettingSlice";
import { updateAccountAndSettingsActiveTab } from "../../../store/slices/applicationSlice";


interface InputOtpProps {
  length?: number;
  onComplete?: (otp: string | null, disable: boolean) => void;
}
const InputOtp: React.FC<InputOtpProps> = ({ length = 6, onComplete }) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (index: number, value: string) => {
    onComplete(null, true);
    if (isNaN(Number(value))) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value !== "" && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    if (newOtp.every((digit) => digit !== "") && onComplete) {
      onComplete(newOtp.join(""), false);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && otp[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div className="flex align-items-center gap-2">
      {otp.map((digit, index) => (
        <InputText
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          value={digit}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          className={styles.otpinput}
          maxLength={1}
        />
      ))}
    </div>
  );
};

const genders = {
  MALE: "Male",
  FEMALE: "Female",
  PREFER_NOT_TO_SAY: "Prefer not to say",
};

const AboutMe: React.FC = () => {
  const [accountIdentifier, setAccountIdentifier] = useState("");
  const [accountIdentifierError, setAccountIdentifierError] = useState("");
  const [firstName, setFirstName] = useState("");
  const [aboutMe, setaboutMe] = useState("");
  const [dateOfBirth, setdateOfBirth] = useState("");
  const [lastName, setLastName] = useState("");
  const [firstNameError, setFirstNameError] = useState("");
  const [introductionError, setintroductionError] = useState("");
  const [dobError, setdobError] = useState("");
  const [lastNameError, setLastNameError] = useState("");
  const [gender, setGender] = useState("");
  const [genderError, setGenderError] = useState("");
  const [isSaveEnabled, setIsSaveEnabled] = useState(false);
  const [initialGender, setInitialGender] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessageotp, setErrorMessageotp] = useState("");
  const [successMessageotp, setSuccessMessageotp] = useState("");
  const [successMessageEmail, setSuccessMessageEmail] = useState("");
  const [dialogVisible, setDialogVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isFocused, setIsFocused] = useState(false);
  const [_, setErrorMessage] = useState("");
  const [disable, setDisable] = useState(true);
  const [otpValue, setOtpValue] = useState("");
  const [resendDisabled, setResendDisabled] = useState(true);
  const [accountEmail, setAccountEmail] = useState("");
  const [accountEmailError, setAccountEmailError] = useState(null);
  const [originalEmail, setOriginalEmail] = useState("");
  const [crop, setCrop] = useState<Crop>();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isOtpSubmitted, setIsOtpSubmitted] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const { disableLoader, enableLoader } = useLoader();
  const dispatch = useDispatch<AppDispatch>();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [isImageUploaded, setIsImageUploaded] = useState(false);
  const minCharLimit = 100;
  const { isMobile } = useIsMobile();

  useEffect(() => {
    if (sessionInfo) {
      setFirstName(sessionInfo.loading ? "" : sessionInfo.data["firstName"]);
      setLastName(sessionInfo.loading ? "" : sessionInfo.data["lastName"]);
      setaboutMe(sessionInfo.loading ? "" : sessionInfo.data["aboutMe"]);
      setdateOfBirth(sessionInfo.loading ? "" : sessionInfo.data["dateOfBirth"])
      setAccountIdentifier(
        sessionInfo.loading ? "" : sessionInfo.data["phoneNumber"]
      );
      setAccountEmail(sessionInfo.loading ? "" : sessionInfo.data["email"]);

      if (!sessionInfo.loading) {
        if (sessionInfo.data["gender"] === 1) {
          setGender(genders["MALE"]);
          setInitialGender(genders["MALE"]);
        } else if (sessionInfo.data["gender"] === 2) {
          setGender(genders["FEMALE"]);
          setInitialGender(genders["FEMALE"]);
        } else if (sessionInfo.data["gender"] === 0) {
          setGender(genders["PREFER_NOT_TO_SAY"]);
          setInitialGender(genders["PREFER_NOT_TO_SAY"]);
        }
      }
    }
  }, [sessionInfo]);
  useEffect(() => {
    if (dialogVisible === false) {
      setIsOtpSubmitted(false);
      setDisable(true);
    }
  }, [dialogVisible]);
  useEffect(() => {
    setIsSaveEnabled(gender !== "" && gender !== initialGender);
  }, [gender, initialGender]);

  const handleGenderChange = (selectedGender: string) => {
    setGender(selectedGender);
  };
  const handleDialogOpen = () => {
    setCurrentStep(1);
    setDialogVisible(true);
  };

  const handleDialogClose = () => {
    setAccountIdentifier(sessionInfo.data["phoneNumber"]);
    setDialogVisible(false);
    setAccountIdentifierError("");
  };
  const handleSave = () => {
    if (aboutMe && aboutMe.length < minCharLimit) {
      return;
    }
    enableLoader();
    const payload = {
      ...sessionInfo.data,
      aboutMe: aboutMe,
    };
    dispatch(updateSessionInfo({ payload })).finally(() => {
      disableLoader();
    });
  };
  const handleSubmit = () => {
    const payload = {
      ...(sessionInfo.data as object),
      phoneNumber: otpValue,
    };
    if (otpValue.length === 6) {
      enableLoader();
      Auth.submitVerificationCode(
        otpValue,

        () => {
          enableLoader();
          dispatch(updateUser({ payload }))
            .then(() => {
              setSuccessMessageEmail(
                `Your phone number ${accountIdentifier} has been successfully verified and updated`
              );
              disableLoader()
              setAccountIdentifier(accountIdentifier);
            })
          setIsOtpSubmitted(false);
          setDisable(true);
          setDialogVisible(false);
          setCurrentStep(1);
        },
        () => {
          disableLoader();
          setErrorMessageotp("Invalid verification code. Please try again.");
        }
      );
    } else {
      setErrorMessageotp("Please enter a valid 6-digit OTP.");
    }
  };
  const handleOtpComplete = (param, state) => {
    setDisable(state);
    setOtpValue(param);
  }
  const startResendCountdown = () => {
    setResendDisabled(true);

  };
  const handleNextStep = () => {
    const value = accountIdentifier.trim(); // Trim the input to avoid empty spaces

    // Check if the mobile number is empty
    if (!value) {
      setAccountIdentifierError("Please enter your mobile number.");
      return;
    }

    // Check if the mobile number is valid
    if (validatePhoneNumber(value)) {
      enableLoader();
      Auth.getVerificationCode(
        value,
        () => {
          disableLoader();
          startResendCountdown();
          setCurrentStep(2);
        },
        () => {
          disableLoader();
          setAccountIdentifierError("Failed to send OTP. Please try again.");
        }
      );
      setAccountIdentifierError(""); // Clear error on valid phone number
    } else {
      setAccountIdentifierError("Enter a valid mobile number.");
    }
  };

  const handleResendCode = () => {
    handleNextStep();
  };

  useEffect(() => {
    if (sessionInfo.data && sessionInfo.data["email"]) {
      setAccountEmail(sessionInfo.data["email"]);
      setOriginalEmail(sessionInfo.data["email"]);
    }
  }, [sessionInfo]);

  const handleUpdateEmail = () => {
    const error = validateEmail(accountEmail);
    if (error) {
      setAccountEmailError(error);
      return;
    }

    const payload = {
      ...(sessionInfo.data as object),
      email: accountEmail,
    };

    enableLoader();
    dispatch(updateUser({ payload }))
      .then(() => {
        setSuccessMessageEmail(
          `Your email address ${accountEmail} has been successfully verified and updated`
        );

        setOriginalEmail(accountEmail);
      })
      .finally(() => {
        disableLoader();
      });
  };
  useEffect(() => {
    if (sessionInfo.data?.["defaultImage"]?.["scale1ImageUrl"]) {
      setSelectedImage(sessionInfo.data["defaultImage"]["scale1ImageUrl"]);
    }
  }, [sessionInfo.data]);

  const handleCancel = () => {
    setSelectedImage(null);
    setCroppedImageUrl(null);
    setCompletedCrop(undefined);
    setCrop(undefined);
    setIsImageUploaded(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        4 / 4,
        width,
        height
      ),
      width,
      height
    )
    setCrop(crop)
  }

  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop) => {
      const canvas = document.createElement("canvas");
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      const pixelCrop = {
        x: Math.round(crop.x * scaleX),
        y: Math.round(crop.y * scaleY),
        width: Math.round(crop.width * scaleX),
        height: Math.round(crop.height * scaleY),
        unit: 'px' as const // Ensure the unit is explicitly set to "px"
      };

      canvas.width = pixelCrop.width;
      canvas.height = pixelCrop.height;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(
          image,
          pixelCrop.x,
          pixelCrop.y,
          pixelCrop.width,
          pixelCrop.height,
          0,
          0,
          pixelCrop.width,
          pixelCrop.height
        );
      }

      return {
        croppedImageUrl: canvas.toDataURL("image/jpeg"),
        x: pixelCrop.x,
        y: pixelCrop.y,
        height: pixelCrop.height,
        width: pixelCrop.width,
        unit: pixelCrop.unit // Include the unit property in the return object
      };
    },
    []
  );

  const handleSavePhoto = useCallback(() => {
    if (imgRef.current && completedCrop) {
      enableLoader();
      const { croppedImageUrl, x, y, height, width, unit } = getCroppedImg(imgRef.current, completedCrop);
      setCroppedImageUrl(croppedImageUrl);
      setSelectedImage(croppedImageUrl); // Set the cropped image as the selected image
      const payload = {
        id: sessionInfo.data["defaultImage"]["id"],
        cropDimensions: { x, y, width, height, unit },
        isCropped: true,
        scale1ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"], // Set the cropped image URL as scale1ImageUrl
        scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
        scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
      };
      dispatch(
        updateUser({
          payload: {
            ...sessionInfo.data,
            defaultImage: payload,
          },
        })
      )
        .then(() => {
          // Ensure the component re-renders with the new image
          setIsImageUploaded(false);
          setSelectedImage(null);
          setCroppedImageUrl(null);
          setCompletedCrop(undefined);
          setCrop(undefined);
        })
        .catch((error) => {
          console.error("Error updating user:", error);
        })
        .finally(() => {
          disableLoader();
        });
    }
  }, [completedCrop, getCroppedImg, sessionInfo.data, dispatch, disableLoader]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      enableLoader();
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        setIsImageUploaded(true);
        setSelectedImage(reader.result as string);
        const binaryData = e.target?.result as ArrayBuffer;
        const payload = {
          id: sessionInfo.data["defaultImage"]["id"],
          imageBinary: binaryData,
          isCropped: true,
          scale1ImageUrl: reader.result as string, // Set the uploaded image as scale1ImageUrl
          scale2ImageUrl: sessionInfo.data["defaultImage"]["scale2ImageUrl"],
          scale3ImageUrl: sessionInfo.data["defaultImage"]["scale3ImageUrl"],
        };
        dispatch(
          updateUser({
            payload: {
              ...sessionInfo.data,
              defaultImage: payload,
            },
          })
        )
          .then(() => { })
          .catch((error) => {
            console.error("Error updating user:", error);
          })
          .finally(() => {
            disableLoader();
          });
      };
      reader.readAsDataURL(file);
    }
  };
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  const isButtonDisabled = accountEmail === originalEmail;
  return (
    <div style={{ paddingTop: isMobile && "0px", paddingLeft: isMobile && "10px" }} className={styles.aboutMeContainer}>
      <div className="flex align-items-center justify-content-between
                     flex-wrap">
        <h2 style={{ marginBottom: isMobile && "5px" }} className={styles.aboutMeHeader}>About Me</h2>
        {clientType === "0" && (
          <CustomButton
            label={"Save"}
            className={`${myfamilystyles.customButton}`}
            style={{ margin: "0", width: "150px" }}
            onClick={handleSave}
          />
        )}
      </div>
      <Divider style={{ width: "744px" }} />
      <br />
      <div className={styles.contentContainer}>
        <div style={{ paddingTop: isMobile && "0px", marginBottom: isMobile && "0px" }} className={styles.aboutMeSection}>
          {/* First Name Input */}
          <div
            className="input-container"
            style={{ marginTop: "8px", maxWidth: "100%" }}
          >
            <InputText
              readOnly
              id="firstName"
              name="firstName"
              value={firstName}
              onChange={(e) => {
                const value = e.target.value;
                if (/^[A-Za-z\s]*$/.test(value)) {
                  setFirstName(value);
                }
              }}
              placeholder=""
              className={`input-placeholder ${firstNameError ? "firstNameError" : ""
                }`}
              style={{ borderWidth: "1px", cursor: "auto" }}
            />
            <label
              htmlFor="firstName*"
              className={`label-name ${firstName || firstNameError ? "label-float" : ""
                } ${firstNameError ? "input-error" : ""}`}
            >
              {firstNameError && !firstName ? firstNameError : "First Name*"}
            </label>
          </div>

          {/* Last Name Input */}
          <div
            className="input-container"
            style={{ marginTop: "25px", maxWidth: "100%" }}
          >
            <InputText
              readOnly
              id="lastName"
              name="lastName"
              value={lastName}
              onChange={(e) => {
                const value = e.target.value;
                if (/^[A-Za-z\s]*$/.test(value)) {
                  setLastName(value);
                }
              }}
              placeholder=""
              className={`input-placeholder ${lastNameError ? "lastNameError" : ""
                }`}
              style={{ borderWidth: "1px", cursor: "auto" }}
            />
            <label
              htmlFor="lastName*"
              className={`label-name ${lastName || lastNameError ? "label-float" : ""
                } ${lastNameError ? "input-error" : ""}`}
            >
              {lastNameError && !lastName ? lastNameError : "Last Name*"}
            </label>
          </div>

          {/* Mobile Number Input */}
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div
              className="input-container"
              style={{ marginTop: "25px", maxWidth: "100%" }}
            >
              <InputText
                readOnly
                id="mobile"
                name="mobile"
                value={accountIdentifier}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value) && value.length <= 15) {
                    setAccountIdentifier(value);
                  }
                }}
                onFocus={() => {
                  // setIsFocused(true);
                  // setAccountIdentifierError("");
                  setSuccessMessageotp(""); // Clear success message on focus
                }}
                placeholder=""
                className={`input-placeholder ${accountIdentifierError ? "accountIdentifierError" : ""
                  }`}
                style={{ borderWidth: "1px", cursor: "auto" }}
              />
              <label
                htmlFor="mobile"
                className={`label-name ${accountIdentifier || accountIdentifierError
                  ? "label-float"
                  : ""
                  } ${accountIdentifierError ? "input-error" : ""}`}
              >
                {accountIdentifierError && !accountIdentifier
                  ? accountIdentifierError
                  : "Mobile"}
              </label>
            </div>
            <CustomButton
              label="Change Number"
              className={styles.changeNumber}
              onClick={handleDialogOpen}
            />

            <CustomDialog
              visible={dialogVisible}
              onHide={handleDialogClose}
              closeClicked={handleDialogClose}
              profileCompletion={0}
            >
              <main className={!isMobile ? `${styles.numberdialogContent}` : `${styles.numberdialogContentMobile}`}>
                {currentStep === 1 && (
                  <>
                    <div className={styles.numberdialogLeft}>
                      {!isMobile ? (
                        <h2 className={styles.headernumber}>
                          Update Your Mobile Number
                        </h2>
                      ) : (
                        <HorizontalNavigation
                          title=' Update Your Mobile Number'
                          onBackClick={() => {
                            setDialogVisible(false)
                          }}
                        />
                      )}
                      <p
                        className={!isMobile ? `${styles.dialogDescription}` : `${styles.dialogDescriptionMobile}`}
                        style={{
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        At Juggle Street, trust and security are paramount,
                        that’s why we need to verify each person. Your mobile
                        number is used to verify your identity.
                      </p>
                    </div>
                    <div className={!isMobile ? `${styles.numberdialogRight}` : `${styles.numberdialogRightMobile}`}>
                      <p className={styles.steptext}>Part {currentStep} of 2</p>
                      <div
                        className={styles.inputGroup}
                        style={{ marginTop: "35px", maxWidth: "100%" }}
                      >
                        <div
                          className="input-container"
                          style={{ maxWidth: "100%" }}
                        >
                          <InputText
                            id="username"
                            name="username"
                            value={accountIdentifier}
                            onFocus={() => {
                              setIsFocused(true);
                              setAccountIdentifierError("");
                              setSuccessMessageotp(""); // Clear success message on focus
                            }}
                            onBlur={() => setIsFocused(false)}
                            onChange={(e) => {
                              const value = e.target.value;

                              if (
                                /^[\d+]*$/.test(value) &&
                                value.length <= 15
                              ) {
                                setAccountIdentifier(value);
                              }
                            }}
                            placeholder=""
                            className={`input-placeholder ${accountIdentifierError
                              ? "accountIdentifierError"
                              : ""
                              }`}
                          />

                          <label
                            htmlFor="username"
                            className={`label-name ${accountIdentifier || accountIdentifierError
                              ? "label-float"
                              : ""
                              } ${accountIdentifierError ? "input-error" : ""}`}
                          >
                            {accountIdentifierError && !accountIdentifier
                              ? accountIdentifierError
                              : "Mobile"}
                          </label>
                        </div>
                      </div>
                      <CustomButton
                        label="Get Code"
                        className="hover:shadow-5"
                        onClick={handleNextStep}
                        style={{
                          width: "156px",
                          height: "39px",
                          backgroundColor: "#179D52",
                        }}
                      />

                      <p className={styles.verificationMessage}>
                        Click <b>'Get Code'</b> to receive your 6-digit
                        verification number.
                      </p>
                      {accountIdentifierError &&
                        accountIdentifier &&
                        !isFocused && (
                          <div className="error-message">
                            {accountIdentifierError}
                          </div>
                        )}
                    </div>
                  </>
                )}

                {currentStep === 2 && (
                  <>
                    <div className={styles.numberdialogLeft}>

                      {!isMobile ? (<p className={styles.headernumber}>
                        Update Your Mobile Number
                      </p>) : (
                        <HorizontalNavigation
                          title=' Update Your Mobile Number'
                          onBackClick={() => {
                            setCurrentStep(1)
                          }}
                        />
                      )}

                      <p
                        className={!isMobile ? `${styles.dialogDescription}` : `${styles.dialogDescriptionMobile}`}
                        style={{
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        To complete your identity verification, please enter the
                        six-digit code that was sent by SMS to your mobile
                        number.
                      </p>
                    </div>
                    <div className={!isMobile ? `${styles.numberdialogRight}` : `${styles.numberdialogRightMobile}`}>
                      <p className={styles.steptext}>Part {currentStep} of 2</p>
                      <InputOtp onComplete={handleOtpComplete} />
                      <div className={styles.codebuttonContainer}>
                        <CustomButton
                          label="Submit"
                          disabled={disable}
                          onClick={handleSubmit}
                          style={{
                            width: "156px",
                            height: "39px",
                            font: "14px",
                            fontWeight: "800",
                          }}
                          className={`${!disable ? "shadow-4" : ""}`}
                        />

                        {!isOtpSubmitted && (
                          <p onClick={handleResendCode} className={styles.resendLink}>
                            Resend Code
                          </p>
                        )}

                      </div>
                    </div>
                  </>
                )}
              </main>
            </CustomDialog>
          </div>
          {successMessageotp && (
            <div className={styles.successMobile}>
              <div
                style={{
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  color: "#179D52",
                  padding: "5px",
                  height: "16px",
                  width: "16px",
                  display: "inline-flex",
                  alignItems: "center",
                  marginRight: "3px",
                }}
              >
                <FaCheck />
              </div>
              {successMessageotp}
            </div>
          )}
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div
              className="input-container"
              style={{ marginTop: "25px", maxWidth: "100%" }}
            >
              <InputText
                id="email"
                name="email"
                value={accountEmail}
                onChange={(e) => setAccountEmail(e.target.value)}
                placeholder=""
                className={`input-placeholder ${accountEmailError ? "emailInputError" : ""
                  }`}
                style={{
                  borderColor: accountEmailError ? "red" : "",
                  borderWidth: "1px",
                }}
                onFocus={() => {
                  setAccountEmailError("");
                  setSuccessMessageEmail("");
                }}
              />
              <label
                htmlFor="username"
                className={`label-name ${accountEmail || accountEmailError ? "label-float" : ""
                  } ${accountEmailError ? "input-error" : ""}`}
              >
                {accountEmailError && !accountEmail
                  ? accountEmailError
                  : "Email"}
              </label>
            </div>

            <CustomButton
              label="Update Email"
              className={styles.changeEmail}
              onClick={handleUpdateEmail}
              disabled={isButtonDisabled}
            />
          </div>
          {successMessageEmail && (
            <div className={styles.successMobile}>
              <div
                style={{
                  backgroundColor: "#FFFFFF",
                  borderRadius: "50%",
                  color: "#179D52",
                  padding: "5px",
                  height: "16px",
                  width: "16px",
                  display: "inline-flex",
                  alignItems: "center",
                  marginRight: "3px",
                }}
              >
                <FaCheck />
              </div>
              {successMessageEmail}
            </div>
          )}
        </div>
        {clientType === "0" && (
          <div style={{ padding: '10px' }}>
            {/* About Me Section */}
            {/* <p
              className={myfamilystyles.inputTextareafamily}
              style={{ color: "#585858", fontWeight: "400", fontSize: "14px" }}
            >
              Tell your neighbours a bit about yourself, but leave out your name and age as this will already be shown. This introduction will be displayed on your profile card.
            </p> */}
            <div className="input-containerhelper" style={{ marginTop: "8px", maxWidth: "100%" }}>
              <h2 className={styles.aboutMegender}>Introduce Yourself</h2>
              <InputTextarea
                id="aboutMe"
                name="aboutMe"
                value={aboutMe}
                onChange={(e) => {
                  const value = e.target.value;
                  setaboutMe(value);
                }}
                rows={3}
                cols={30}
                className={`${myfamilystyles.inputTextareafamily} w-full p-3 ${introductionError ? "border-red-500" : ""}`}
                placeholder="EXAMPLE -  Hi mums and dad’s, I grew up in a big family with 2 younger brothers and a younger sister, so I have many years of experience looking after children! I know lots of people in the neighbourhood and look forward to meeting you."
                style={{
                  borderWidth: "1px",
                  cursor: "auto",
                  height: "150px",
                }}
              />
              <p
                style={{
                  fontSize: '14px',
                  color: aboutMe && aboutMe.length < minCharLimit ? 'red' : 'green',
                  fontWeight: '400',
                }}
              >
                {aboutMe && aboutMe.length < minCharLimit &&
                  `${minCharLimit - aboutMe.length} characters remaining`}
              </p>
              {/* Label */}

              {/* {introductionError && (
                <p className="text-red-500 p-0 m-0 text-sm">required</p>
              )} */}
            </div>
            {/* Image Upload Section */}
            <div className="">
              <div className="">
                <div className="flex flex-wrap" style={{ marginBlock: "20px" }}>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    id="image-upload"
                    style={{ display: "none" }}
                  />
                  {!isImageUploaded ? (
                    <label htmlFor="image-upload" className="flex flex-wrap align-items-center">
                      <div
                        className="cursor-pointer"
                        style={{
                          width: "150px",
                          height: "150px",
                          borderRadius: "50%",
                          backgroundColor: "#F0F4F7",
                          border: "1px solid #FFA500",
                        }}
                      >
                        {!isImageUploaded && !sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
                          <img
                            src={Userimage}
                            alt=""
                            className="cursor-pointer mt-4 ml-4 "
                            style={{
                              width: "95px",
                              height: "99px",
                              // borderRadius: "50%",
                              backgroundColor: "#F0F4F7",
                            }}
                          />
                        ) : (
                          <img
                            src={sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ?? Userimage}
                            alt=""
                            className="cursor-pointer"
                            style={{
                              width: "148px",
                              height: "148px",
                              borderRadius: "50%",
                              backgroundColor: "#F0F4F7",
                            }}
                          />
                        )}
                      </div>
                      <div
                        className="ml-3 mt-1 pl-3 pr-3 p-2 cursor-pointer"
                        style={{
                          backgroundColor: "#1F9EAB",
                          color: "#FFFFFF",
                          fontSize: "12px",
                          fontWeight: "600",
                          borderRadius: "8px",
                        }}
                      >
                        <span className="pi pi-camera"></span>
                        &nbsp;&nbsp;Upload Photo
                      </div>
                    </label>
                  ) : (
                    <ReactCrop
                      crop={crop}
                      onChange={c => setCrop(c)}
                      onComplete={(c) => setCompletedCrop(c)}
                      aspect={1}
                      // // circularCrop
                      // minWidth={30}
                      // minHeight={30}
                      style={{ objectFit: "contain" }}
                    >
                      <img
                        ref={imgRef}
                        src={selectedImage}
                        alt="Selected"
                        crossOrigin="anonymous"
                        onLoad={onImageLoad}
                        className="cursor-pointer"
                        style={{
                          // width: "150px",
                          // height: "150px",
                          // borderRadius: "50%",
                          // display: "flex",
                          // justifyContent: "center",
                          // alignItems: "end",
                          backgroundColor: "#F0F4F7",
                          // objectFit: "contain",
                        }}
                      />
                    </ReactCrop>
                  )}
                </div>
                {isImageUploaded && (
                  <div className="flex  flex-wrap justify-content-center flex-column">
                    <div
                      style={{
                        display: "flex",
                        // marginTop: "-30px",
                      }}
                    >
                      <CustomButton
                        label="Cancel"
                        onClick={handleCancel}
                        style={{
                          marginRight: "10px",
                          backgroundColor: "transparent",
                          color: "#585858",
                          height: "30px",
                          fontSize: "14px",
                          fontWeight: "500",
                          boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                          width: "150px",
                        }}
                      />
                      <CustomButton
                        label="Save photo"
                        onClick={handleSavePhoto}
                        style={{
                          backgroundColor: "#1F9EAB",
                          width: "150px",
                          height: "30px",
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Date of Birth Section */}
            <div className="input-container mt-5" style={{ maxWidth: "100%" }}>
              <InputText
                readOnly
                id="dateOfBirth"
                name="dateOfBirth"
                value={formatDate(dateOfBirth)}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^[A-Za-z\s]*$/.test(value)) {
                    setdateOfBirth(value);
                  }
                }}
                placeholder=""
                className={`input-placeholder ${dobError ? "dobError" : ""}`}
                style={{ borderWidth: "1px", cursor: "auto" }}
              />
              <label
                htmlFor="dateOfBirth*"
                className={`label-name ${dateOfBirth || dobError ? "label-float" : ""} ${dobError ? "input-error" : ""
                  }`}
              >
                {dobError && !dateOfBirth ? dobError : "Date of Birth*"}
              </label>
            </div>
          </div>
        )}


        {/* Gender Section */}
        <div className={styles.aboutMeSectionsecond}>
          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <h2 className={styles.aboutMegender}>Gender</h2>
            {successMessage && (
              <div className={styles.successMessagegender}>
                <div
                  style={{
                    backgroundColor: "#FFFFFF",
                    borderRadius: "50%",
                    color: "#179D52",
                    padding: "5px",
                    height: "16px",
                    width: "16px",
                    display: "inline-flex",
                    alignItems: "center",
                    marginRight: "3px",
                  }}
                >
                  <FaCheck />
                </div>
                {successMessage}
              </div>
            )}
          </div>
          <Divider className={styles.genderdivider} />

          <div
            className={`${styles.inputbtn} flex-column md:flex-row md:align-items-baseline`}
          >
            <div className="flex flex-wrap gap-3 mb-2 mt-3 cursor-pointer">
              {Object.keys(genders).map((key, index) => (
                <label
                  key={index}
                  htmlFor={`gender${index}`}
                  className="align-items-center radiobutton-style"
                  style={{
                    border: genderError
                      ? "1px solid red"
                      : gender === genders[key]
                        ? "1px solid #179D52"
                        : "1px solid gainsboro",
                    padding: "11px 16px 11px 11px",
                    borderRadius: "10px",
                    position: "relative",
                    cursor: "pointer", // Make it appear clickable
                    backgroundColor:
                      gender === genders[key] ? "" : "transparent",
                  }}
                  onClick={() => handleGenderChange(genders[key])} // Handle click event on the entire label
                >
                  <label
                    htmlFor={`gender${index}`}
                    className="ml-1 h-joinnow2-gender pr-5 cursor-pointer"
                    style={{
                      color: gender === genders[key] ? "#179D52" : "",
                      fontWeight: gender === genders[key] ? 700 : 500,
                    }}
                  >
                    {String(key)
                      .replace(/_/g, " ") // Replace underscores with spaces
                      .split(" ") // Split into words
                      .map(
                        (word) =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase()
                      ) // Capitalize each word
                      .join(" ")}
                  </label>
                  <div
                    style={{
                      position: "absolute",
                      top: 6.5,
                      right: 9,
                    }}
                  >
                    <input
                      type="radio"
                      id={`gender${index}`}
                      value={genders[key]}
                      checked={gender === genders[key]}
                      onChange={() => handleGenderChange(genders[key])}
                      style={{
                        cursor: "pointer",
                        opacity: 0,
                        position: "absolute",
                      }}
                    />

                    {gender === genders[key] ? (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          backgroundColor: "#179D52",
                          borderRadius: "50%",
                          display: "flex",
                          cursor: "pointer",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <span
                          style={{
                            color: "white",
                            fontWeight: "bold",
                            fontSize: "12px",
                            cursor: "pointer",
                            lineHeight: "12px",
                          }}
                        >
                          ✓
                        </span>
                      </div>
                    ) : (
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          border: "1px solid gainsboro",
                          borderRadius: "50%",
                          display: "flex",
                          cursor: "pointer",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: "transparent",
                        }}
                      />
                    )}
                  </div>
                </label>
              ))}
            </div>

            <CustomButton
              label="Save"
              onClick={() => {
                let temp = -1;
                if (gender === "Male") {
                  temp = 1;
                } else if (gender === "Female") {
                  temp = 2;
                } else if (gender === "Prefer not to say") {
                  temp = 0; // New gender value
                }
                if (temp === -1) {
                  return;
                }
                const payload = {
                  ...(sessionInfo.data as object),
                  gender: temp,
                };
                enableLoader();
                dispatch(updateUser({ payload: payload }))
                  .then(() => {
                    setSuccessMessage("Changes successfully made");
                  })
                  .finally(() => {
                    disableLoader();
                  });
              }}
              className={styles.changeNumber}
              disabled={!isSaveEnabled}
            />
          </div>
          <p className={styles.instruct}>
            *This field is not editable. Contact Juggle Street to update.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AboutMe;
