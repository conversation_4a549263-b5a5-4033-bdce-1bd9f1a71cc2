import { Jobs, MyJobSectionProps } from '../../Common/manageJobs/types';
// import CompletedJobs from './MyJobTabs/CompletedJobs';
import RateFamilies from './MyJobTabs/RateFamilies';
import UpcomingJobs from './MyJobTabs/UpcomingJobs';
import NoteSearch from '../../../assets/images/Icons/note-search-mobile.png';
import NoteTodo from '../../../assets/images/Icons/note-todo-mobile.png';
import NoteWrite from '../../../assets/images/Icons/note-write-mobile.png';
import StarGray from '../../../assets/images/Icons/star-gray-mobile.png';
import { useEffect, useState } from 'react';
import Service from '../../../services/services';
import { useNavigate, useSearchParams } from 'react-router-dom';
import useLoader from '../../../hooks/LoaderHook';
import JobSummaryAwarded from '../../Common/manageJobs/summary/JobSummaryAwarded';
import JobSummaryUnAwarded from '../../Common/manageJobs/summary/JobSummaryUnawarded';
import { Badge } from 'primereact/badge';
import ManageJobsCalender from '../../Common/manageJobs/ManageJobsCalender';
import SidePannel from './SidePannel';
import CompletedJobs from './MyJobTabs/CompletedJobs';
import HomeHeaderHelper from './HomeHeaderHelper';
import useIsMobile from '../../../hooks/useIsMobile';
import HorizontalNavigation from '../../Common/HorizontalNavigationMobile';
import CookiesConstant from '../../../helper/cookiesConst';
import utils from '../../../components/utils/util';
const TabFactory: Record<
    number,
    {
        tab: React.FC<MyJobSectionProps>;
        tabName: string;
        tabIcon: React.ReactNode;
    }
> = {
    0: {
        tabName: 'Upcoming Jobs',
        tab: UpcomingJobs,
        tabIcon: <img src={NoteSearch} alt='NoteSearch' width='13.5px' height='14.4px' />,
    },
    1: {
        tabName: 'Rate Families',
        tab: RateFamilies,
        tabIcon: <img src={StarGray} alt='NoteTodo' width='13.35px' height='14.4px' />,
    },
    2: {
        tabName: 'Completed Jobs',
        tab: CompletedJobs,
        tabIcon: <img src={NoteWrite} alt='NoteWrite' width='14.46px' height='14.46px' />,
    },
};
const MyJobs = () => {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [upComingJobs, setUpComingJobs] = useState<Jobs[]>([]);
    const [jobHistory, setJobHistory] = useState<Jobs[]>([]);
    const [unRatedJobs, setUnRatedJobs] = useState<Jobs[]>([]);
    const {isMobile}=useIsMobile();
    const clientType = Number(utils.getCookie(CookiesConstant.clientType));
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { enableLoader, disableLoader } = useLoader();

    const jobId = searchParams.get('jobId');
    const activeTab = searchParams.get('activeTab');
    const tabNameIcon = Object.values(TabFactory).map((v) => ({
        tabName: v.tabName,
        tabIcon: v.tabIcon,
    }));
    const ActiveTab = TabFactory[activeTabIndex].tab;

    const fetchJobs = async () => {
        enableLoader();
        try {
            const [upcomingJobs, completedJobs, unratedJobs] = await Promise.all([
                new Promise<Jobs[]>((resolve, reject) => {
                    Service.gethelperUpComingJobs(
                        (data: Jobs[]) => resolve(data),
                        (error) => reject(error)
                    );
                }),
                new Promise<Jobs[]>((resolve, reject) => {
                    Service.gethelperCompletedJobs(
                        (data: Jobs[]) => resolve(data),
                        (error) => reject(error)
                    );
                }),
                new Promise<Jobs[]>((resolve, reject) => {
                    Service.gethelperUnratedJobs(
                        (data: Jobs[]) => resolve(data),
                        (error) => reject(error)
                    );
                }),
            ]);
            setUpComingJobs(upcomingJobs);
            setJobHistory(completedJobs);
            setUnRatedJobs(unratedJobs);
        } catch (error) {
            console.warn('Error fetching jobs:', error);
        } finally {
            disableLoader();
        }
    };

    useEffect(() => {
        if (activeTab) {
            const activeTabIndex = Number(activeTab);
            if (activeTabIndex >= 0 && activeTabIndex < Object.keys(TabFactory).length) {
                setActiveTabIndex(activeTabIndex);
            } else {
                setActiveTabIndex(0);
            }
        }
    }, [activeTab]);

    const handleTabChange = (index: number) => {
        setActiveTabIndex(index);
        const updatedParams = new URLSearchParams(searchParams);
        updatedParams.set('jobId', '-1');
        updatedParams.set('activeTab', String(index));
        updatedParams.set('rateJobId', '');
        navigate({ search: updatedParams.toString() });
    };
    const handleViewJobIdChange = (newJobId: number) => {
        const updatedParams = new URLSearchParams(searchParams);
        updatedParams.set('jobId', String(newJobId));
        updatedParams.set('activeTab', activeTab);
        navigate({ search: updatedParams.toString() });
    };

    useEffect(() => {
        fetchJobs();
    }, [jobId, activeTab]);

    
    const unAwardedCount = upComingJobs.length;
    return !isMobile ? (
        <div>
        <HomeHeaderHelper />
        <SidePannel activeindex={2} />
        <div className='w-screen h-screen'>

            <div
                className='overflow-hidden h-full absolute right-0 pt-3 flex flex-column'
                style={{
                    width: 'calc(100% - 240px)',
                }}
            >
                <div
                    className='flex mb-5'
                    style={{
                        height: '23%',
                        width: '100%',
                        borderTop: '2px solid #179D52',
                        borderBottom: '2px solid #179D52',
                    }}
                >
                    <ManageJobsCalender
                        upComingJobs={upComingJobs}
                        jobHistory={jobHistory}
                        unRatedJobs={unRatedJobs}
                    />
                </div>
                <div
                    className='flex flex-column w-full p-1'
                    style={{
                        borderBottom: '1px solid #D6D6D6',
                    }}
                >
                    <div
                        className='flex w-full gap-4'
                        style={{
                            paddingLeft: '80px',
                        }}
                    >
                        {tabNameIcon.map((tni, index) => (
                            <div
                                key={index}
                                className='flex flex-column gap-2 align-items-center cursor-pointer relative'
                                onClick={() => handleTabChange(index)}
                            >
                                <div className='flex gap-1 align-items-center cursor-pointer'>
                                    {tni.tabIcon}
                                    <p
                                        className='m-0 p-0'
                                        style={{
                                            fontWeight: activeTabIndex === index ? '700' : '500',
                                            fontSize: '16px',
                                            color: activeTabIndex === index ? '#585858' : '#444444',
                                            textAlign: 'center',
                                        }}
                                    >
                                        {tni.tabName}
                                    </p>
                                    <Badge
                                        value={unAwardedCount}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '15px',
                                            minWidth: '15px',
                                            maxHeight: '15px',
                                            maxWidth: '15px',
                                            fontSize: '60%',
                                            display:
                                                index === 0 && unAwardedCount > 0 ? 'flex' : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                    />
                                    <Badge
                                        value={unRatedJobs.length}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '15px',
                                            minWidth: '15px',
                                            maxHeight: '15px',
                                            maxWidth: '15px',
                                            fontSize: '60%',
                                            display:
                                                index === 1 && unRatedJobs.length > 0
                                                    ? 'flex'
                                                    : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                    />
                                    <Badge
                                        value={jobHistory.length}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '15px',
                                            minWidth: '15px',
                                            maxHeight: '15px',
                                            maxWidth: '15px',
                                            fontSize: '60%',
                                            display:
                                                index === 2 && jobHistory.length > 0
                                                    ? 'flex'
                                                    : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}
                                    />
                                    {/* <Badge
                                    value={unRatedJobs.length}
                                    style={{
                                        backgroundColor: '#FF6359',
                                        minHeight: '15px',
                                        minWidth: '15px',
                                        maxHeight: '15px',
                                        maxWidth: '15px',
                                        fontSize: '60%',
                                        display:
                                            index === 3 && unRatedJobs.length > 0
                                                ? 'flex'
                                                : 'none',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                /> */}
                                </div>
                                {activeTabIndex === index && (
                                    <div
                                        className='relative'
                                        style={{
                                            bottom: '-6px',
                                            width: '100%',
                                            height: '3px',
                                            backgroundColor: '#585858',
                                            borderRadius: '20px',
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                <div
                    className='flex-grow-1 flex overflow-y-auto'
                    style={{
                        paddingLeft: '80px',
                    }}
                >
                    <div
                        className='p-1 pt-4 flex flex-column flex-grow-1 '
                        style={{
                            maxHeight: '100%',
                        }}
                    >
                        {Number(jobId || -1) === -1 ? (
                            <ActiveTab
                                upComingJobs={upComingJobs}
                                unRatedJobs={unRatedJobs}
                                jobHistory={jobHistory}
                                CompletedJobs={unRatedJobs}
                                viewJob={(j) => handleViewJobIdChange(j)}
                                refresh={() => fetchJobs()}
                            />
                        ) : activeTabIndex === 0 ? (
                            <JobSummaryUnAwarded
                                upComingJobs={upComingJobs}
                                jobHistory={jobHistory}
                                unRatedJobs={unRatedJobs}
                                refresh={() => fetchJobs()}
                            />
                        ) : (
                            <JobSummaryAwarded
                                upComingJobs={upComingJobs}
                                jobHistory={jobHistory}
                                unRatedJobs={unRatedJobs}
                                refresh={() => fetchJobs()}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    </div>
    ):(
        <div>
        <HomeHeaderHelper />
        <SidePannel activeindex={2} />
                    <HorizontalNavigation
                        title='Jobs'
                        tabs={[
                            {
                                item: ({ index, activeIndex, itemStyles }) =>
                                    HorizontalNavigation.Item(
                                        index,
                                        activeIndex,
                                        <i className={`pi pi-list ${itemStyles.navIcon}`}></i>,
                                        'My Jobs',
                                        () => {
                                            const path = 
                                                     '/helper-home/manage-jobs?jobId=-1&activeTab=0'
                                            navigate(path);
                                        }
                                    ),
                            },
                            {
                                item: ({ index, activeIndex, itemStyles }) =>
                                    HorizontalNavigation.Item(
                                        index,
                                        activeIndex,
                                        <i className={`pi pi-home ${itemStyles.navIcon}`}></i>,
                                        'Home',
                                        () => {
                                            const path =
                                                     '/helper-home'
                                            navigate(path);
                                        }
                                    ),
                            },
                            {
                                item: ({ index, activeIndex, itemStyles }) =>
                                    HorizontalNavigation.Item(
                                        index,
                                        activeIndex,
                                        <i className={`pi pi-comments ${itemStyles.navIcon}`}></i>,
                                        'Chat',
                                        () => {
                                            const path =
                                                    '/helper-home/inAppChat'
                                            navigate(path);
                                        }
                                    ),
                            },
                        ]}
                        onBackClick={() => {
                            const path = '/helper-home' ;
                            navigate(path);
                        }}
                    />
        <div  style={{ height: '100vh', width: '100vw' }}className='w-screen h-screen'>

            <div
                className='overflow-hidden h-full absolute right-0 pt-3 flex flex-column'
                style={{
                    width: '100%',
                }}
            >
                <div
                    className='flex flex-column w-full p-1'
                    style={{
                        borderBottom: '1px solid #D6D6D6', marginTop: '113px'
                    }}
                >
                    <div
                        className='flex w-full gap-4 justify-content-between px-4'
                    >
                        {tabNameIcon.map((tni, index) => (
                            <div
                                key={index}
                                className='flex flex-column gap-2 align-items-center cursor-pointer relative'
                                onClick={() => handleTabChange(index)}
                            >
                                <div className='flex gap-1 align-items-center cursor-pointer flex-column'>
                                    {tni.tabIcon}
                                    <p
                                        className='m-0 p-0'
                                        style={{
                                            fontWeight: activeTabIndex === index ? '700' : '500',
                                            fontSize: '10px',
                                            color: activeTabIndex === index ? '#585858' : '#444444',
                                            textAlign: 'center',
                                        }}
                                    >
                                        {tni.tabName}
                                    </p>
                                    <Badge
                                        value={unAwardedCount}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '13px',
                                            minWidth: '13px',
                                            maxHeight: '13px',
                                            maxWidth: '13px',
                                            fontSize: '60%',
                                            display:
                                                index === 0 && unAwardedCount > 0 ? 'flex' : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            position:"absolute",
                                            left:"48px",
                                            bottom:"39px"
                                        }}
                                    />
                                    <Badge
                                        value={unRatedJobs.length}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '13px',
                                            minWidth: '13px',
                                            maxHeight: '13px',
                                            maxWidth: '13px',
                                            fontSize: '60%',
                                            display:
                                                index === 1 && unRatedJobs.length > 0
                                                    ? 'flex'
                                                    : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            position:"absolute",
                                            left:"43px",
                                            bottom:"37px"
                                        }}
                                    />
                                    <Badge
                                        value={jobHistory.length}
                                        style={{
                                            backgroundColor: '#FF6359',
                                            minHeight: '13px',
                                            minWidth: '13px',
                                            maxHeight: '13px',
                                            maxWidth: '13px',
                                            fontSize: '60%',
                                            display:
                                                index === 2 && jobHistory.length > 0
                                                    ? 'flex'
                                                    : 'none',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            position:"absolute",
                                            left:"49px",
                                            bottom:"40px"
                                        }}
                                    />
                                    <Badge
                                    value={unRatedJobs.length}
                                    style={{
                                        backgroundColor: '#FF6359',
                                        minHeight: '15px',
                                        minWidth: '15px',
                                        maxHeight: '15px',
                                        maxWidth: '15px',
                                        fontSize: '60%',
                                        display:
                                            index === 3 && unRatedJobs.length > 0
                                                ? 'flex'
                                                : 'none',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        position:"absolute",
                                        left:"53px",
                                        bottom:"43px"
                                    }}
                                />
                                </div>
                                {activeTabIndex === index && (
                                    <div
                                        className='relative'
                                        style={{
                                            bottom: '-6px',
                                            width: '100%',
                                            height: '3px',
                                            backgroundColor: '#FFA500',
                                            borderRadius: '20px',
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                <div
                    className='flex-grow-1 flex overflow-y-auto'
                >
                    <div
                        className='p-1 px-4 pt-3 flex flex-column flex-grow-1 '
                        style={{
                            maxHeight: '100%',
                        }}
                    >
                        {Number(jobId || -1) === -1 ? (
                            <ActiveTab
                                upComingJobs={upComingJobs}
                                unRatedJobs={unRatedJobs}
                                jobHistory={jobHistory}
                                CompletedJobs={unRatedJobs}
                                viewJob={(j) => handleViewJobIdChange(j)}
                                refresh={() => fetchJobs()}
                            />
                        ) : activeTabIndex === 0 ? (
                            <JobSummaryUnAwarded
                                upComingJobs={upComingJobs}
                                jobHistory={jobHistory}
                                unRatedJobs={unRatedJobs}
                                refresh={() => fetchJobs()}
                            />
                        ) : (
                            <JobSummaryAwarded
                                upComingJobs={upComingJobs}
                                jobHistory={jobHistory}
                                unRatedJobs={unRatedJobs}
                                refresh={() => fetchJobs()}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    </div>
    )
}

export default MyJobs