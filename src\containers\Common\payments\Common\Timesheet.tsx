import React, { useState } from 'react';
import PaymentSummaryHelper from '../PaymentSummaryHelper';
import PaymentApprovalSummary from '../PaymentApprovalSummary';
import PaymentSuccess from './PaymentSuccess';
import PaymentMethodDialog from './PaymentMethodDialog';
import MakingPayment from './MakingPayment';
import { Button } from 'primereact/button';
import TimeSheetCard from './TimeSheetCard';
import GeneratingInvoice from './GeneratingInvoice';

function TimeSheet() {
  const timesheetData = [
    {
      status: "Awaiting Your Approval",
      type: "Before & After School",
      date: "16th of January, 2024",
      location: "9 Christie Beach, South Brisbane",
      userName: "Ciara S",
    },
    {
      status: "Pending Review",
      type: "Contract Job",
      date: "30th of May, 2025",
      location: "12 Ocean Drive, North Sydney",
      userName: "Sarah M",
    },
    {
      status: "Approved",
      type: "Freelance Job",
      date: "22nd of April, 2025",
      location: "15 Park Lane, Melbourne",
      userName: "John D",
    },
  ];
    const [isSummaryOpen, setIsSummaryOpen] = useState(false);
    const [showMakingPayment, setShowMakingPayment] = useState(false);
    const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

  const defaultProps = {
    payerName: 'Craig S',
    payerAddress: '9 Christie Street, South Brisbane',
    paymentAmount: '112.50',
    jobType: 'One-Off Job',
    jobDate: '16th of Jan, 2025',
    parentName: 'Craig Sawtell',
    helperName: 'Ciara Sanders',
    hoursWorked: '4.5',
    hourlyRate: '25',
    platformFee: '5.30',
    jobTotal: '106.87',
  };

  const handleReview = (userName: string) => {
    alert(`Review Timesheet for ${userName} clicked!`);
  };

  const handleStartPayment = () => {
    setShowMakingPayment(true);
  };

  const handlePaymentComplete = () => {
    setShowMakingPayment(false);
    setShowPaymentSuccess(true);
  };

  const handlePaymentSuccessClose = () => {
    setShowPaymentSuccess(false);
  };
  const [dialogVisible, setDialogVisible] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 'payto-1',
      name: 'Payto',
      icon: 'path/to/payto-icon.png',
      owner: 'Craig Sawtell',
      number: '6885',
      fee: 0
    },
    {
      id: 'visa-1',
      name: 'VISA',
      icon: 'path/to/visa-icon.png',
      owner: 'Craig Sawtell',
      number: '4569',
      fee: 1.75
    }
  ]);

  interface PaymentMethod {
    id: string;
    name: string;
    icon: string;
    owner: string;
    number: string;
    fee: number;
    token?: string;
  }

  const handlePay = (method: PaymentMethod) => {
    console.log(`Paying $${amount} with ${method.name} - ${method.number}`);
    setDialogVisible(false);
  };

  const handleAddNewCard = (cardData: PaymentMethod) => {
    console.log('New card added:', cardData);
    // Add the new card to the payment methods list
    setPaymentMethods(prevMethods => [...prevMethods, cardData]);
  };

  const amount = 115.50;

  // Show MakingPayment component if payment is in progress
  if (showMakingPayment) {
    return (
      <MakingPayment
      />
    );
  }

  // Show PaymentSuccess component if payment is completed
  // if (showPaymentSuccess) {
  //   return (
  //     <PaymentSuccess
  //       amount="197.60"
  //       invoiceNo="INV-2025-001"
  //       status="Complete"
  //       date="17.01.2024"
  //       time="1:24pm"
  //       invoiceString="Download Invoice"
  //       paymentType="PayTo"
  //       onDownload={() => console.log('Download PDF')}
  //       onDone={handlePaymentSuccessClose}
  //     />
  //   );
  // }

  return (
    <div className='h-full'>


   
      {/* Test Buttons for Payment Flow */}
      {/* <div style={{
        padding: '20px',
        background: '#f8f9fa',
        margin: '20px',
        borderRadius: '10px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#333', marginBottom: '15px' }}>Payment Flow Test</h3>
        <Button
          label="Test Making Payment"
          onClick={handleStartPayment}
          style={{
            backgroundColor: '#179D52',
            border: 'none',
            padding: '10px 20px',
            marginRight: '10px',
            borderRadius: '6px'
          }}
        />
        { <Button
          label="View Payment Summary"
          onClick={() => setIsSummaryOpen(true)}
          style={{
            backgroundColor: '#FFA500',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '6px'
          }}
        /> }
      </div> */}

      {/* TimeSheet Cards */}
      {/* {timesheetData.map((entry, index) => (
        <TimeSheetCard
          key={index}
          status={entry?.status}
          type={entry?.type}
          date={entry?.date}
          location={entry?.location}
          userName={entry?.userName}
          onReview={() => handleReview(entry?.userName)}
        />
      ))} */}
      {/* <InvoiceApproved/> */}

      <PaymentSuccess
  amount="127.50"
  invoiceNo="102938"
  status="Complete"
  date="17.01.2024"
  time="1:24pm"
  invoiceString='Download Invoice'
  paymentType="PayTo"
  onDownload={() => console.log('Download PDF')}
  onDone={() => console.log('Go to home or close')}
/>
<div>
      {/* <button
       
        onClick={() => setIsSummaryOpen(true)}
      >
        View Payment Summary
      </button> */}

      <PaymentSummaryHelper
        isOpen={isSummaryOpen}
        onClose={() => setIsSummaryOpen(false)}
        {...defaultProps}
      />
      {/* <PaymentApprovalSummary
     isOpen={isSummaryOpen}
    onClose={() => setIsSummaryOpen(false)}
    invoiceNo="INV-2025-001"
    dateIssued="[DD/MM/YYYY]"
    reference="N/A"
    totalAmount="115.50"
    platformFee="5.00"
    gst="10.50"
    paymentType="Credit Card"
    payerEmail="<EMAIL>"
    dueDateStatus="Due Now"
    jobType='One-Off Job'
    jobDate='16th of Jan, 2025'
/> */}

    </div>
    {/* <div>
      <Button
        label="Open Payment Dialog"
        onClick={() => setDialogVisible(true)}
      />
      <PaymentMethodDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        paymentMethods={paymentMethods}
        amount={amount}
        onPay={handlePay}
        onAddNewCard={handleAddNewCard}
      />
    </div> */}



    </div>
  );
}

export default TimeSheet;