const c = {
  jugglerTypes: {
    UNSPECIFIED: 0,
    CLIENT: 1,
    PROVIDER: 2,
  },
  clientType: {
    UNSPECIFIED: 0,
    INDIVIDUAL: 1,
    BUSINESS: 2,
  },
  clientCategory: {
    UNSPECIFIED: 0,
    FAMILY: 1,
    BUSINESS_SINGLE_SITE: 2,
    BUSINESS_MULTI_SITE: 3,
  },
  providerType: {
    UNSPECIFIED: 0,
    INDIVIDUAL: 1,
    BUSINESS: 2,
  },
  lookingFor: {
    UNSPECIFIED: 0,
    INDIVIDUAL_CLIENT: 1,
    INDIVIDUAL_PROVIDER: 2,
    BUSINESS_CLIENT: 3,

    // BUSINESS_PROVIDER: 4,
  },
  businessAccountType: {
    UNSPECIFIED: 0,
    PRO_CHILDCARE: 1,
    PRO_EDUCATION: 2,
  },
  accountStatus: {
    PENDING: 1,
    APPROVED: 2,
  },
  profileItems: {
    BASIC_INFO: 1,
    CLIENT_PHOTO: 2,
    TRUST_VERIFICATION: 4,
    C<PERSON>IENT_ABOUT_ME: 8,
    MY_CHILDREN: 16,
    MY_HOME: 32,
    PROVIDER_APPROVAL: 64,
    PROVIDER_ABOUT_ME: 128,
    PROVIDER_PHOTO: 256,
  },
  profileItemsHelper: {
    basicInfo: 1,
    clientPhoto: 2,
    trustVerification: 4,
    clientAboutMe: 8,
    myChildren: 16,
    myHome: 32,
    myBabysittingExperience: 64,
    providerAboutMe: 128,
    providerPhoto: 256,
    providerJobCategories: 512,
    profileVisibility: 1024,
    providerCertificates: 2048,
    providerGuardian: 4096,
    citizenship: 8192,
    paymentMethods: 16384,
    providerMembership: 32768,
  },
  genders: {
    // EITHER: 0,
    MALE: 1,
    FEMALE: 2,
    // NON_BINARY: 3,
    PREFER_NOT_TO_SAY: 0,
  },
  deviceType: {
    DESKTOP: 1,
    MOBILE: 2,
  },
  friendStatus: {
    UNSPECIFIED: 0,
    PENDING: 1,
    APPROVED: 2,
    REJECTED: 3,
    NONE: 4,
    HIDDEN: 5,
    UNFRIENDED: 6,
  },
  paymentActionType: {
    ACTIVATE_MEMBERSHIP: 1,
    DEACTIVATE_MEMBERSHIP: 2,
    ACTIVATE_GIFT_CARD: 3,
    UPDATE_PAYMENT_INFO: 4,
    PURCHASE_GIFT_CARD: 5,
    // PURCHASE_JOB_PLAN: 6,
    UPGRADE_PLAN: 7,
    REACTIVATE_MEMBERSHIP: 8,
  },
  userPaymentType: {
    FREE: 0,
    MONTHLY_STANDARD_SUBSCRIPTION: 1,
    ANNUAL_FOUNDING_MEMBER: 2,
    ANNUAL_STANDARD_SUBSCRIPTION: 3,
    QUARTERLY_STANDARD_SUBSCRIPTION: 4,
    // GIFT_CARD: 4,
    ONE_OFF_PAYG: 5,
    // JOB_CREDITS_OCCASIONAL: 1001,
    // JOB_CREDITS_REGULAR: 1002,
    // JOB_CREDITS_PROFESSIONAL: 1003
    SINGLE_ONE_OFF_JOB: 1001,
    SINGLE_RECURRING_JOB: 1002,
    ANNUAL_UNLIMITED_PAY_AS_YOU_GO: 1003,
    SIX_MONTHS_UNLIMITED_PAY_AS_YOU_GO: 1004,
    EXTEND_RECURRING_JOB: 1005,
    QUARTERLY_STANDARD_SUBSCRIPTION_60: 11,
    ANNUAL_STANDARD_SUBSCRIPTION_120: 12,
    ANNUAL_STANDARD_SUBSCRIPTION_180: 13,
    QUARTERLY_STANDARD_SUBSCRIPTION_75: 14,
    ANNUAL_STANDARD_SUBSCRIPTION_150: 15,
    SINGLE_AU_PAIR_JOB: 1006,
    //SINGLE_HOME_TUTORING_JOB: 1007,
    SINGLE_PRIMARY_SCHOOL_TUTORING_JOB: 1008,
    SINGLE_HIGH_SCHOOL_TUTORING_JOB: 1009,
    ANNUAL_BABYSITTING_SUBSCRIPTION60: 2001,
    ANNUAL_BABYSITTING_SUBSCRIPTION120: 2013,
    MONTHLY_BABYSITTING_SUBSCRIPTION40: 2014,
    ANNUAL_CHILDCARE_SUBSCRIPTION120: 2002,
    ANNUAL_CHILDCARE_SUBSCRIPTION180: 2012,
    ANNUAL_TUTORING_SUBSCRIPTION90: 2003,
    ANNUAL_AU_PAIR_SUBSCRIPTION195: 2004,
    ANNUAL_PRIMARY_SCHOOL_TUTORING_SUBSCRIPTION120: 2005,
    ANNUAL_TUTORING_SUBSCRIPTION195: 2006,
    ANNUAL_HIGH_SCHOOL_TUTORING_SUBSCRIPTION195: 2007,
    ANNUAL_TUTORING_SUBSCRIPTION245: 2008,
    ANNUAL_TUTORING_SUBSCRIPTION180: 2009,
    ANNUAL_FAMILY_CARE_SUBSCRIPTION240: 2010,
    ANNUAL_STANDARD_SUBSCRIPTION360: 2011,
    QUARTERLY_BUSINESS_TIER1_SUBSCRIPTION_500: 4001,
    QUARTERLY_BUSINESS_TIER2_SUBSCRIPTION_500: 4002,
    ANNUAL_BUSINESS_TIER1_SUBSCRIPTION_1500: 4003,
    ANNUAL_BUSINESS_TIER2_SUBSCRIPTION_1500: 4004,
    QUARTERLY_CHILDCARE_SUBSCRIPTION90: 2015,
    QUARTERLY_BUSINESS_TIER2_SUBSCRIPTION_750: 4005,
    MONTHLY_CHILDCARE_SUBSCRIPTION90: 2016,
    CONCIERGE_SUBSCRIPTION: 2017,
    FAMILY_JOB_REQUEST: 1010,
    SUBSCRIBER_FAMILY_JOB_REQUEST: 1011,
    //helper membership
    annualHelperStandardSubscription24: 3001,
    monthlyHelperStandardSubscription5: 3002,
    annualHelperStandardSubscription30: 3003,
    quarterlyHelperStandardSubscription15: 3004,
    annualHelperStandardSubscription40: 3005,
    quarterlyHelperStandardSubscription20: 3006,
    monthlyChildcareSubsription90: 2016,
    quarterlyChildcareSubsription90: 2015,
    quarterlyBusinessTier1Subscription500: 4001,
    quarterlyBusinessTier2Subscription500: 4002,
  },
  paymentEditActionType: {
    NEW_SUBSCRIPTION: 1,
    EDIT_PAYMENT_INFO: 2,
    EDIT_SUBSCRIPTION: 3,
    ENABLE_AUTO_RENEW: 4,
    DISABLE_AUTO_RENEW: 5,
    ACTIVATE_GIFT_CARD: 6,
    // BUY_CREDITS: 7
    UPGRADE_TO_UNLIMITED: 8,
    POST_JOB: 9,
    UPGRADE_AT_THE_END_OF_SUBSCRIPTION: 10,
  },
  jobActionType: {
    POST: 1,
    REPOST: 2,
    EDIT_JOB: 3,
    JOB_TO_HELPER: 4,
    CLONE_JOB: 5,
  },
  managedBy: {
    UNSPECIFIED: 0,
    USER: 1,
    SYSTEM: 20,
    ADMIN: 30,
  },
  jobType: {
    UNSPECIFIED: 0,
    BABYSITTING: 1,
    NANNYING: 2,
    BEFORE_SCHOOL_CARE: 4,
    AFTER_SCHOOL_CARE: 8,
    BEFORE_AFTER_SCHOOL_CARE: 12,
    AU_PAIR: 16,
    HOME_TUTORING: 32,
    PRIMARY_SCHOOL_TUTORING: 64,
    HIGH_SCHOOL_TUTORING: 128,
    ONE_OFF_ODD_JOB: 256,
  },
  oddJobType: {
    UNSPECIFIED: 0,
    LAUNDRY: 1,
    ERRANDS: 2,
    OUTDOOR_CHORES: 4,
    ELDERLY_HELP: 8,
    OTHER_ODD_JOBS: 16,
  },
  jobTypeGroup: {
    UNSPECIFIED: 0,
    CHILDCARE: 1,
    ODD_JOBS: 2,
    TUTORING: 4,
  },
  vouchReferenceType: {
    family: 1,
    childcareEmployment: 2,
    nonChildcareEmployment: 3,
    other: 4,
    juggleStreet: 5,
  },

  jobFrequency: {
    UNSPECIFIED: 0,
    ONE_OFF: 1,
    RECURRING: 2,
  },
  tutoringCategory: {
    NEWBIE: 1,
    APPRENTICE: 2,
    EXPERIENCED: 3,
    PROFESSIONAL: 4,
  },
  mediaReviewStatus: {
    PROCESSING: 0,
    PENDING: 1,
    APPROVED: 2,
    REJECTED: 3,
  },
  mediaType: {
    unspecified: 0,
    profileIntro: 1,
    testimonial: 2,
  },
  mediaStatus: {
    unknown: 0,
    queued: 1,
    processing: 2,
    ready: 3,
    failed: 4,
  },
  year12FilterGroups: {
    ALL: 0,
    WITHIN_5_YRS: new Date().getUTCFullYear() - 5,
    WITHIN_2_YRS: new Date().getUTCFullYear() - 2,
  },
  jobApplicationStatus: {
    PENDING: 1,
    Approved: 2,
    CANCELLED: 3,
    VIEWED: 4,
    APPLIED: 5,
    AWARDED: 6,
    NOT_INTERESTED: 7,
    NOT_AVAILABLE: 8,
    SHORTLISTED_BY_CLIENT: 9,
    EXCLUDED_BY_CLIENT: 10,
    SHORTLISTED_BY_SYSTEM: 11,
    WITHRDAWN: 12,
  },
  ratingMode: {
    UNSPECIFIED: 0,
    PAGE: 1,
    POPUP: 2,
  },
  providerUnavailableReason: {
    UNSPECIFIED: 0,
    CALENDAR_CLASH: 1,
    NOT_ENOUGH_NOTICE: 2,
    PRICE_TOO_LOW: 3,
    OTHER: 4,
    TOO_FAR_AWAY: 5,
    SHIFTS_TOO_SHORTS: 6,
  },
  jobStatus: {
    PENDING: 1,
    AWARDED: 2,
    CANCELLED: 3,
    COMPLETED: 4,
    FILLED: 5,
    FILLED_AND_CANCELLED: 6,
  },
  applicantAvailabilityStatus: {
    PENDING: 1,
    NOT_INTERESTED: 2,
    NOT_AVAILABLE: 3,
    AVAILABLE: 4,
  },
  appEventType: {
    
    unspecified: 0,
    appDownload: 1,
    userRegistration: 2,
    appRating: 3,
    overTheAirUpdate: 4,
    appStoreUpdate: 5,
    appError: 6,
    ignoreAppRating: 7,
    initiatedEmployeeBenefits: 8,
    delayedEmployeeBenefits: 9,
    cancelledEmployeeBenefits: 10,
    requestedEmployeeBenefits: 11,
    hasSeenBanner: 12,
    interestinhomeagedcareYes: 13,
    interestinhomeagedcareNo: 14,
    interestinhomeagedcareNA: 15,
  },
  interestinhomeagedcareResponse: {
    Yes: 1,
    No: 2,
    NA: 3,
  },
  employeeBenefitsReferralStatus: {
    NONE: 0,
    INITIATED: 1,
    DELAYED: 2,
    CANCELLED: 3,
    REQUESTED: 4,
  },
  messageDeliveryStatus: {
    failed: 0,
    sending: 1,
    sent: 2,
    delivered: 3,
    seen: 4,
  },
  childcareJobTypes: [
    { value: 1, label: "One-off Casual jobs" },
    { value: 2, label: "Nannying - Recurring jobs" },
    { value: 4, label: "Before School - Recurring jobs" },
    { value: 8, label: "After School - Recurring jobs" },
    // { value: 16, label: 'Au Pair' },
  ],
  countriesIso: [
    { id: 4, label: "Afghanistan", alpha2: "af", value: "afg" },
    { id: 8, label: "Albania", alpha2: "al", value: "alb" },
    { id: 12, label: "Algeria", alpha2: "dz", value: "dza" },
    { id: 20, label: "Andorra", alpha2: "ad", value: "and" },
    { id: 24, label: "Angola", alpha2: "ao", value: "ago" },
    { id: 28, label: "Antigua and Barbuda", alpha2: "ag", value: "atg" },
    { id: 32, label: "Argentina", alpha2: "ar", value: "arg" },
    { id: 51, label: "Armenia", alpha2: "am", value: "arm" },
    { id: 36, label: "Australia", alpha2: "au", value: "aus" },
    { id: 40, label: "Austria", alpha2: "at", value: "aut" },
    { id: 31, label: "Azerbaijan", alpha2: "az", value: "aze" },
    { id: 44, label: "Bahamas", alpha2: "bs", value: "bhs" },
    { id: 48, label: "Bahrain", alpha2: "bh", value: "bhr" },
    { id: 50, label: "Bangladesh", alpha2: "bd", value: "bgd" },
    { id: 52, label: "Barbados", alpha2: "bb", value: "brb" },
    { id: 112, label: "Belarus", alpha2: "by", value: "blr" },
    { id: 56, label: "Belgium", alpha2: "be", value: "bel" },
    { id: 84, label: "Belize", alpha2: "bz", value: "blz" },
    { id: 204, label: "Benin", alpha2: "bj", value: "ben" },
    { id: 64, label: "Bhutan", alpha2: "bt", value: "btn" },
    { id: 68, label: "Bolivia (Plurinational State of)", alpha2: "bo", value: "bol" },
    { id: 70, label: "Bosnia and Herzegovina", alpha2: "ba", value: "bih" },
    { id: 72, label: "Botswana", alpha2: "bw", value: "bwa" },
    { id: 76, label: "Brazil", alpha2: "br", value: "bra" },
    { id: 96, label: "Brunei Darussalam", alpha2: "bn", value: "brn" },
    { id: 100, label: "Bulgaria", alpha2: "bg", value: "bgr" },
    { id: 854, label: "Burkina Faso", alpha2: "bf", value: "bfa" },
    { id: 108, label: "Burundi", alpha2: "bi", value: "bdi" },
    { id: 132, label: "Cabo Verde", alpha2: "cv", value: "cpv" },
    { id: 116, label: "Cambodia", alpha2: "kh", value: "khm" },
    { id: 120, label: "Cameroon", alpha2: "cm", value: "cmr" },
    { id: 124, label: "Canada", alpha2: "ca", value: "can" },
    { id: 140, label: "Central African Republic", alpha2: "cf", value: "caf" },
    { id: 148, label: "Chad", alpha2: "td", value: "tcd" },
    { id: 152, label: "Chile", alpha2: "cl", value: "chl" },
    { id: 156, label: "China", alpha2: "cn", value: "chn" },
    { id: 170, label: "Colombia", alpha2: "co", value: "col" },
    { id: 174, label: "Comoros", alpha2: "km", value: "com" },
    { id: 178, label: "Congo", alpha2: "cg", value: "cog" },
    { id: 180, label: "Congo, Democratic Republic of the", alpha2: "cd", value: "cod" },
    { id: 188, label: "Costa Rica", alpha2: "cr", value: "cri" },
    { id: 384, label: "Cote D'Ivoire", alpha2: "ci", value: "civ" },
    { id: 191, label: "Croatia", alpha2: "hr", value: "hrv" },
    { id: 192, label: "Cuba", alpha2: "cu", value: "cub" },
    { id: 196, label: "Cyprus", alpha2: "cy", value: "cyp" },
    { id: 203, label: "Czechia", alpha2: "cz", value: "cze" },
    { id: 208, label: "Denmark", alpha2: "dk", value: "dnk" },
    { id: 262, label: "Djibouti", alpha2: "dj", value: "dji" },
    { id: 212, label: "Dominica", alpha2: "dm", value: "dma" },
    { id: 214, label: "Dominican Republic", alpha2: "do", value: "dom" },
    { id: 218, label: "Ecuador", alpha2: "ec", value: "ecu" },
    { id: 818, label: "Egypt", alpha2: "eg", value: "egy" },
    { id: 222, label: "El Salvador", alpha2: "sv", value: "slv" },
    { id: 226, label: "Equatorial Guinea", alpha2: "gq", value: "gnq" },
    { id: 232, label: "Eritrea", alpha2: "er", value: "eri" },
    { id: 233, label: "Estonia", alpha2: "ee", value: "est" },
    { id: 748, label: "Eswatini", alpha2: "sz", value: "swz" },
    { id: 231, label: "Ethiopia", alpha2: "et", value: "eth" },
    { id: 242, label: "Fiji", alpha2: "fj", value: "fji" },
    { id: 246, label: "Finland", alpha2: "fi", value: "fin" },
    { id: 250, label: "France", alpha2: "fr", value: "fra" },
    { id: 266, label: "Gabon", alpha2: "ga", value: "gab" },
    { id: 270, label: "Gambia", alpha2: "gm", value: "gmb" },
    { id: 268, label: "Georgia", alpha2: "ge", value: "geo" },
    { id: 276, label: "Germany", alpha2: "de", value: "deu" },
    { id: 288, label: "Ghana", alpha2: "gh", value: "gha" },
    { id: 300, label: "Greece", alpha2: "gr", value: "grc" },
    { id: 308, label: "Grenada", alpha2: "gd", value: "grd" },
    { id: 320, label: "Guatemala", alpha2: "gt", value: "gtm" },
    { id: 324, label: "Guinea", alpha2: "gn", value: "gin" },
    { id: 624, label: "Guinea-Bissau", alpha2: "gw", value: "gnb" },
    { id: 328, label: "Guyana", alpha2: "gy", value: "guy" },
    { id: 332, label: "Haiti", alpha2: "ht", value: "hti" },
    { id: 340, label: "Honduras", alpha2: "hn", value: "hnd" },
    { id: 341, label: "Hong Kong", alpha2: "hk", value: "hkg" },
    { id: 348, label: "Hungary", alpha2: "hu", value: "hun" },
    { id: 352, label: "Iceland", alpha2: "is", value: "isl" },
    { id: 356, label: "India", alpha2: "in", value: "ind" },
    { id: 360, label: "Indonesia", alpha2: "id", value: "idn" },
    { id: 364, label: "Iran (Islamic Republic of)", alpha2: "ir", value: "irn" },
    { id: 368, label: "Iraq", alpha2: "iq", value: "irq" },
    { id: 372, label: "Ireland", alpha2: "ie", value: "irl" },
    { id: 376, label: "Israel", alpha2: "il", value: "isr" },
    { id: 380, label: "Italy", alpha2: "it", value: "ita" },
    { id: 388, label: "Jamaica", alpha2: "jm", value: "jam" },
    { id: 392, label: "Japan", alpha2: "jp", value: "jpn" },
    { id: 400, label: "Jordan", alpha2: "jo", value: "jor" },
    { id: 398, label: "Kazakhstan", alpha2: "kz", value: "kaz" },
    { id: 404, label: "Kenya", alpha2: "ke", value: "ken" },
    { id: 296, label: "Kiribati", alpha2: "ki", value: "kir" },
    { id: 408, label: "Korea (Democratic People's Republic of)", alpha2: "kp", value: "prk" },
    { id: 410, label: "Korea, Republic of", alpha2: "kr", value: "kor" },
    { id: 414, label: "Kuwait", alpha2: "kw", value: "kwt" },
    { id: 417, label: "Kyrgyzstan", alpha2: "kg", value: "kgz" },
    { id: 418, label: "Lao People's Democratic Republic", alpha2: "la", value: "lao" },
    { id: 428, label: "Latvia", alpha2: "lv", value: "lva" },
    { id: 422, label: "Lebanon", alpha2: "lb", value: "lbn" },
    { id: 426, label: "Lesotho", alpha2: "ls", value: "lso" },
    { id: 430, label: "Liberia", alpha2: "lr", value: "lbr" },
    { id: 434, label: "Libya", alpha2: "ly", value: "lby" },
    { id: 438, label: "Liechtenstein", alpha2: "li", value: "lie" },
    { id: 440, label: "Lithuania", alpha2: "lt", value: "ltu" },
    { id: 442, label: "Luxembourg", alpha2: "lu", value: "lux" },
    { id: 807, label: "Macedonia, the former Yugoslav Republic of", alpha2: "mk", value: "mkd" },
    { id: 450, label: "Madagascar", alpha2: "mg", value: "mdg" },
    { id: 454, label: "Malawi", alpha2: "mw", value: "mwi" },
    { id: 458, label: "Malaysia", alpha2: "my", value: "mys" },
    { id: 462, label: "Maldives", alpha2: "mv", value: "mdv" },
    { id: 466, label: "Mali", alpha2: "ml", value: "mli" },
    { id: 470, label: "Malta", alpha2: "mt", value: "mlt" },
    { id: 584, label: "Marshall Islands", alpha2: "mh", value: "mhl" },
    { id: 478, label: "Mauritania", alpha2: "mr", value: "mrt" },
    { id: 480, label: "Mauritius", alpha2: "mu", value: "mus" },
    { id: 484, label: "Mexico", alpha2: "mx", value: "mex" },
    { id: 583, label: "Micronesia (Federated States of)", alpha2: "fm", value: "fsm" },
    { id: 498, label: "Moldova, Republic of", alpha2: "md", value: "mda" },
    { id: 492, label: "Monaco", alpha2: "mc", value: "mco" },
    { id: 496, label: "Mongolia", alpha2: "mn", value: "mng" },
    { id: 499, label: "Montenegro", alpha2: "me", value: "mne" },
    { id: 504, label: "Morocco", alpha2: "ma", value: "mar" },
    { id: 508, label: "Mozambique", alpha2: "mz", value: "moz" },
    { id: 104, label: "Myanmar", alpha2: "mm", value: "mmr" },
    { id: 516, label: "Namibia", alpha2: "na", value: "nam" },
    { id: 520, label: "Nauru", alpha2: "nr", value: "nru" },
    { id: 524, label: "Nepal", alpha2: "np", value: "npl" },
    { id: 528, label: "Netherlands", alpha2: "nl", value: "nld" },
    { id: 554, label: "New Zealand", alpha2: "nz", value: "nzl" },
    { id: 558, label: "Nicaragua", alpha2: "ni", value: "nic" },
    { id: 562, label: "Niger", alpha2: "ne", value: "ner" },
    { id: 566, label: "Nigeria", alpha2: "ng", value: "nga" },
    { id: 578, label: "Norway", alpha2: "no", value: "nor" },
    { id: 512, label: "Oman", alpha2: "om", value: "omn" },
    { id: 586, label: "Pakistan", alpha2: "pk", value: "pak" },
    { id: 585, label: "Palau", alpha2: "pw", value: "plw" },
    { id: 587, label: "Palestine", alpha2: "ps", value: "pse" },
    { id: 591, label: "Panama", alpha2: "pa", value: "pan" },
    { id: 598, label: "Papua New Guinea", alpha2: "pg", value: "png" },
    { id: 600, label: "Paraguay", alpha2: "py", value: "pry" },
    { id: 604, label: "Peru", alpha2: "pe", value: "per" },
    { id: 608, label: "Philippines", alpha2: "ph", value: "phl" },
    { id: 616, label: "Poland", alpha2: "pl", value: "pol" },
    { id: 620, label: "Portugal", alpha2: "pt", value: "prt" },
    { id: 634, label: "Qatar", alpha2: "qa", value: "qat" },
    { id: 642, label: "Romania", alpha2: "ro", value: "rou" },
    { id: 643, label: "Russian Federation", alpha2: "ru", value: "rus" },
    { id: 646, label: "Rwanda", alpha2: "rw", value: "rwa" },
    { id: 659, label: "Saint Kitts and Nevis", alpha2: "kn", value: "kna" },
    { id: 662, label: "Saint Lucia", alpha2: "lc", value: "lca" },
    { id: 670, label: "Saint Vincent and the Grenadines", alpha2: "vc", value: "vct" },
    { id: 882, label: "Samoa", alpha2: "ws", value: "wsm" },
    { id: 674, label: "San Marino", alpha2: "sm", value: "smr" },
    { id: 678, label: "Sao Tome and Principe", alpha2: "st", value: "stp" },
    { id: 682, label: "Saudi Arabia", alpha2: "sa", value: "sau" },
    { id: 686, label: "Senegal", alpha2: "sn", value: "sen" },
    { id: 688, label: "Serbia", alpha2: "rs", value: "srb" },
    { id: 690, label: "Seychelles", alpha2: "sc", value: "syc" },
    { id: 694, label: "Sierra Leone", alpha2: "sl", value: "sle" },
    { id: 702, label: "Singapore", alpha2: "sg", value: "sgp" },
    { id: 703, label: "Slovakia", alpha2: "sk", value: "svk" },
    { id: 705, label: "Slovenia", alpha2: "si", value: "svn" },
    { id: 90, label: "Solomon Islands", alpha2: "sb", value: "slb" },
    { id: 706, label: "Somalia", alpha2: "so", value: "som" },
    { id: 710, label: "South Africa", alpha2: "za", value: "zaf" },
    { id: 728, label: "South Sudan", alpha2: "ss", value: "ssd" },
    { id: 724, label: "Spain", alpha2: "es", value: "esp" },
    { id: 144, label: "Sri Lanka", alpha2: "lk", value: "lka" },
    { id: 729, label: "Sudan", alpha2: "sd", value: "sdn" },
    { id: 740, label: "Suriname", alpha2: "sr", value: "sur" },
    { id: 752, label: "Sweden", alpha2: "se", value: "swe" },
    { id: 756, label: "Switzerland", alpha2: "ch", value: "che" },
    { id: 760, label: "Syrian Arab Republic", alpha2: "sy", value: "syr" },
    { id: 761, label: "Taiwan", alpha2: "tw", value: "twn" },
    { id: 762, label: "Tajikistan", alpha2: "tj", value: "tjk" },
    { id: 834, label: "Tanzania, United Republic of", alpha2: "tz", value: "tza" },
    { id: 764, label: "Thailand", alpha2: "th", value: "tha" },
    { id: 626, label: "Timor-Leste", alpha2: "tl", value: "tls" },
    { id: 768, label: "Togo", alpha2: "tg", value: "tgo" },
    { id: 776, label: "Tonga", alpha2: "to", value: "ton" },
    { id: 780, label: "Trinidad and Tobago", alpha2: "tt", value: "tto" },
    { id: 788, label: "Tunisia", alpha2: "tn", value: "tun" },
    { id: 792, label: "Türkiye", alpha2: "tr", value: "tur" },
    { id: 795, label: "Turkmenistan", alpha2: "tm", value: "tkm" },
    { id: 798, label: "Tuvalu", alpha2: "tv", value: "tuv" },
    { id: 800, label: "Uganda", alpha2: "ug", value: "uga" },
    { id: 804, label: "Ukraine", alpha2: "ua", value: "ukr" },
    { id: 784, label: "United Arab Emirates", alpha2: "ae", value: "are" },
    { id: 826, label: "United Kingdom", alpha2: "gb", value: "gbr" },
    { id: 840, label: "United States of America", alpha2: "us", value: "usa" },
    { id: 858, label: "Uruguay", alpha2: "uy", value: "ury" },
    { id: 860, label: "Uzbekistan", alpha2: "uz", value: "uzb" },
    { id: 548, label: "Vanuatu", alpha2: "vu", value: "vut" },
    { id: 862, label: "Venezuela (Bolivarian Republic of)", alpha2: "ve", value: "ven" },
    { id: 704, label: "Viet Nam", alpha2: "vn", value: "vnm" },
    { id: 887, label: "Yemen", alpha2: "ye", value: "yem" },
    { id: 894, label: "Zambia", alpha2: "zm", value: "zmb" },
    { id: 716, label: "Zimbabwe", alpha2: "zw", value: "zwe" },
    /* eslint-enable */
  ],

  filterSchoolSubjects: [
    { label: "All Subjects", value: -1 },
    { label: "English", value: 4012, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Reading", value: 4013, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Maths", value: 4014, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - All", value: 4016, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - French", value: 4034, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - Mandarin", value: 4041, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - Spanish", value: 4045, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - Italian", value: 4039, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Language - German", value: 4035, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - All", value: 4017, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - Piano", value: 4055, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - Guitar", value: 4053, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - Flute", value: 4052, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - Clarinet", value: 4049, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },
    { label: "Music - Singing", value: 4060, visible: (jobType) => jobType === c.jobType.PRIMARY_SCHOOL_TUTORING },

    { label: "English", value: 4023, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Mathematics", value: 4024, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Chemistry", value: 4081, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Physics", value: 4082, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Biology", value: 4083, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Economics", value: 4106, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - All", value: 4020, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - French", value: 4144, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - Mandarin", value: 4151, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - Spanish", value: 4155, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - Italian", value: 4149, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Language - German", value: 4145, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Music - Piano", value: 4165, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Music - Guitar", value: 4163, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Music - Flute", value: 4162, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Music - Clarinet", value: 4159, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
    { label: "Music - Singing", value: 4170, visible: (jobType) => jobType === c.jobType.HIGH_SCHOOL_TUTORING },
  ],

  highSchoolSubjects: [
    { label: "All Subjects", value: -1 },
    { label: "Physics", value: 4012 },
    { label: "Chemistry", value: 4013 },
    { label: "Maths", value: 4014 },
  ],

  responseTimeTypes: [
    // { label: '1 Hour', value: 1, icon: '' },
    { label: "1 Day", value: 24, icon: "" },
    { label: "2 Days", value: 48 },
    { label: "3 days", value: 72 },
    // { label: '3-7 Days', value: 168 },
    { label: "Slower than 3 Days", value: 1000 },
    // { label: 'Late/No Responder', value: -1 },
    { label: "No recent invites", value: 0 },
  ],
  responseTimes: {
    unknown: 0,
    withinAnHour: 1,
    withinADay: 24,
    within2Days: 48,
    within3Days: 72,
    within7Days: 168,
    moreThan7Days: 1000,
  },
  vouchStatus: {
    pendingVerification: 1,
    approved: 2,
    invalid: 3,
    expired: 4,
  },
  certificateStatus: {
    pendingVerification: 1,
    active: 2,
    invalid: 3,
    expired: 4,
  },
  neighbourhood: {
    any: 0,
    myConnections: 1,
    nearMe: 2,
    invitations: 3,
    workedForMe: 4,
    new: 5,
    recentInvitations: 6,
  },
  bankAccountVerificationStatus: {
    unspecified: 0,
    pending: 1,
    approved: 2,
    rejected: 3,
    inProgress: 4,
  },
  auPairCategory: {
    none: 0,
    newAuPair: 1,
    experiencedAuPair: 2,
  },
  membershipType: {
    free: 0,
    subscription: 1,
    payAsYouGo: 2,
  },
  helperPaymentMethod: {
    unspecified: 0,
    cashPayment: 1,
    bankTransfer: 2,
  },
  gettingHome: {
    unspecified: 0,
  },
  industrySectorsList: [
    { label: "Accommodation - Hotels", value: 101 },
    { label: "Accommodation - Property Operators & Agents", value: 102 },

    { label: "Childcare - Preschool Childcare Centre", value: 201 },
    { label: "Childcare - Before & After School Centre", value: 202 },
    { label: "Childcare - School Holiday Provider", value: 203 },
    { label: "Childcare - Online Childcare Platform", value: 204 },
    { label: "Childcare - Service Provider", value: 205 },

    // { label: 'Education - Primary & High School Tutoring', value: 301 },
    // { label: 'Education - Tertiary Education Tutoring', value: 302 },
    // { label: 'Education - Online Education Platform', value: 303 },
    // { label: 'Education - Service Provider', value: 304 },

    { label: "Health - Health Care Provider", value: 401 },
    { label: "Health - Recreation & Amusement Activities", value: 402 },
    { label: "Health - Sports & Physical Activities", value: 403 },

    { label: "Recruitment - Childcare", value: 501 },
    { label: "Recruitment - Tutoring", value: 502 },

    { label: "Other ", value: 1001 },
  ],
} as const;

export default c;
