# Separate Hooks Implementation Guide

## 🎯 **What Was Implemented**

I've created **separate hook files** for each timesheet route, similar to `useTimesheetDetails.ts`, with proper API calls and loader integration.

### ✅ **1. Separate Hook Files Created**
- **`useAdjustedTimesheetDetails.ts`** → For `/parent-home/timesheet/adjusted-timesheets`
- **`useAwaitingApprovalDetails.ts`** → For `/parent-home/timesheet/awaiting-approval`
- **Each hook makes its own API call** with proper filtering

### ✅ **2. Real API Integration**
- **Uses actual `Service.getTimeSheetDetails`** API call
- **Filters response by status** (2 for adjusted, 3 for approval)
- **Uses existing `useLoader()` hook** - no new loaders created

### ✅ **3. Proper Error Handling**
- **Console logging** for debugging API calls
- **Error states** and loading states
- **Refresh functionality** for each hook

## 🔧 **Implementation Details**

### **1. useAdjustedTimesheetDetails.ts**

#### **API Call with Status 2 Filtering:**
```typescript
const fetchAdjustedTimesheetData = async (): Promise<void> => {
  console.log('🔄 Starting adjusted timesheet data fetch from API...');
  setIsLoading(true);
  setError(null);
  enableLoader(); // Uses existing useLoader() hook

  try {
    await new Promise<void>((resolve, reject) => {
      Service.getTimeSheetDetails(
        (response: any) => {
          console.log("API Response getTimeSheetDetails for adjusted:", response);
          
          // Handle different response formats
          let actualData: AdjustedTimesheetApiItem[];
          if (Array.isArray(response)) {
            actualData = response;
          } else if (response?.data && Array.isArray(response.data)) {
            actualData = response.data;
          } else {
            console.warn("Unexpected response format:", typeof response);
            setAdjustedTimesheetData([]);
            resolve();
            return;
          }

          // Filter only status 2 entries (Adjusted Timesheets)
          const filteredData = actualData.filter((item) => item.status === 2);
          console.log("Filtered adjusted timesheet data (status 2):", filteredData);

          const mappedData = filteredData.map((item) => ({
            id: item.id,
            status: statusMap[item.status] || "Adjusted Timesheet",
            statusCode: item.status,
            type: jobTypeMap[item.jobType] || "Unknown",
            date: new Date(item.jobDate).toLocaleDateString('en-AU', {
              day: 'numeric',
              month: 'long',
              year: 'numeric',
            }),
            location: item.formattedAddress,
            userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
            originalImageUrl: item.originalImageUrl,
          }));

          setAdjustedTimesheetData(mappedData);
          console.log('✅ Adjusted timesheet data loaded successfully, count:', mappedData.length);
          resolve();
        },
        (error: any) => {
          console.error('Error fetching adjusted timesheet data from API:', error);
          setError(error?.message || 'Failed to fetch adjusted timesheet data');
          reject(error);
        }
      );
    });
  } catch (err) {
    console.error('Fetch adjusted timesheet data failed:', err);
    setError('Failed to fetch adjusted timesheet data');
  } finally {
    setIsLoading(false);
    disableLoader(); // Uses existing useLoader() hook
    console.log('✅ Loader disabled for adjusted timesheets');
  }
};
```

### **2. useAwaitingApprovalDetails.ts**

#### **API Call with Status 3 Filtering:**
```typescript
const fetchAwaitingApprovalData = async (): Promise<void> => {
  console.log('🔄 Starting awaiting approval data fetch from API...');
  setIsLoading(true);
  setError(null);
  enableLoader(); // Uses existing useLoader() hook

  try {
    await new Promise<void>((resolve, reject) => {
      Service.getTimeSheetDetails(
        (response: any) => {
          console.log("API Response getTimeSheetDetails for awaiting approval:", response);
          
          // Handle different response formats
          let actualData: AwaitingApprovalApiItem[];
          if (Array.isArray(response)) {
            actualData = response;
          } else if (response?.data && Array.isArray(response.data)) {
            actualData = response.data;
          } else {
            console.warn("Unexpected response format:", typeof response);
            setAwaitingApprovalData([]);
            resolve();
            return;
          }

          // Filter only status 3 entries (Awaiting Approval)
          const filteredData = actualData.filter((item) => item.status === 3);
          console.log("Filtered awaiting approval data (status 3):", filteredData);

          const mappedData = filteredData.map((item) => ({
            id: item.id,
            status: statusMap[item.status] || "Awaiting Approval",
            statusCode: item.status,
            type: jobTypeMap[item.jobType] || "Unknown",
            date: new Date(item.jobDate).toLocaleDateString('en-AU', {
              day: 'numeric',
              month: 'long',
              year: 'numeric',
            }),
            location: item.formattedAddress,
            userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
            originalImageUrl: item.originalImageUrl,
          }));

          setAwaitingApprovalData(mappedData);
          console.log('✅ Awaiting approval data loaded successfully, count:', mappedData.length);
          resolve();
        },
        (error: any) => {
          console.error('Error fetching awaiting approval data from API:', error);
          setError(error?.message || 'Failed to fetch awaiting approval data');
          reject(error);
        }
      );
    });
  } catch (err) {
    console.error('Fetch awaiting approval data failed:', err);
    setError('Failed to fetch awaiting approval data');
  } finally {
    setIsLoading(false);
    disableLoader(); // Uses existing useLoader() hook
    console.log('✅ Loader disabled for awaiting approval');
  }
};
```

### **3. Updated TimeSheet.tsx**

#### **Separate Hook Usage:**
```typescript
// Use custom hooks
const { timesheetData } = useTimesheetData(); // Keep for awaiting-confirmation tab
const { 
  adjustedTimesheetData,
  isLoading: adjustedLoading,
  refreshData: refreshAdjustedData 
} = useAdjustedTimesheetDetails(); // Separate hook for adjusted timesheets
const { 
  awaitingApprovalData,
  isLoading: approvalLoading,
  refreshData: refreshApprovalData 
} = useAwaitingApprovalDetails(); // Separate hook for awaiting approval
```

## 🔄 **Data Flow**

### **For Adjusted Timesheets Route:**
```
1. User navigates to /parent-home/timesheet/adjusted-timesheets
         ↓
2. useAdjustedTimesheetDetails() hook initializes
         ↓
3. enableLoader() called - shows existing loader
         ↓
4. Service.getTimeSheetDetails() API call made
         ↓
5. API response filtered for status === 2
         ↓
6. Data mapped and set to adjustedTimesheetData
         ↓
7. disableLoader() called - hides loader
         ↓
8. TimeSheetCard components rendered for status 2 entries
         ↓
9. Badge count shows adjustedTimesheetData.length
```

### **For Awaiting Approval Route:**
```
1. User navigates to /parent-home/timesheet/awaiting-approval
         ↓
2. useAwaitingApprovalDetails() hook initializes
         ↓
3. enableLoader() called - shows existing loader
         ↓
4. Service.getTimeSheetDetails() API call made
         ↓
5. API response filtered for status === 3
         ↓
6. Data mapped and set to awaitingApprovalData
         ↓
7. disableLoader() called - hides loader
         ↓
8. TimeSheetCard components rendered for status 3 entries
         ↓
9. Badge count shows awaitingApprovalData.length
```

## 📊 **Expected Console Output**

### **For Adjusted Timesheets:**
```
🔄 Starting adjusted timesheet data fetch from API...
API Response getTimeSheetDetails for adjusted: [your API response]
Response is array: [processed response]
Filtered adjusted timesheet data (status 2): [entries with status 2]
Mapped adjusted timesheet data: [final mapped data]
Adjusted timesheet count: X
✅ Adjusted timesheet data loaded successfully, count: X
✅ Loader disabled for adjusted timesheets
```

### **For Awaiting Approval:**
```
🔄 Starting awaiting approval data fetch from API...
API Response getTimeSheetDetails for awaiting approval: [your API response]
Response is array: [processed response]
Filtered awaiting approval data (status 3): [entries with status 3]
Mapped awaiting approval data: [final mapped data]
Awaiting approval count: Y
✅ Awaiting approval data loaded successfully, count: Y
✅ Loader disabled for awaiting approval
```

## 🧪 **Testing**

### **1. Check API Calls:**
- Open browser console
- Navigate to `/parent-home/timesheet/adjusted-timesheets`
- Should see API call logs for adjusted timesheets
- Navigate to `/parent-home/timesheet/awaiting-approval`
- Should see API call logs for awaiting approval

### **2. Verify Loader:**
- Should see existing loader during API calls
- No new loaders created - uses existing `useLoader()` hook

### **3. Verify Filtering:**
- Adjusted route should only show entries with status 2
- Approval route should only show entries with status 3
- Badge counts should reflect filtered data

### **4. Test Empty States:**
- If no entries match status, should show NoJobsCard
- Console should log 0 count for that status

## 🎯 **Benefits**

### **✅ Separate Files:**
- Each route has its own dedicated hook file
- Similar structure to existing `useTimesheetDetails.ts`
- Easy to maintain and debug

### **✅ Real API Calls:**
- Uses actual `Service.getTimeSheetDetails` API
- Proper error handling and loading states
- Console logging for debugging

### **✅ Existing Loader Integration:**
- Uses existing `useLoader()` hook
- No new loaders created
- Consistent loading experience

### **✅ Status Filtering:**
- Each hook filters for specific status
- Clean separation of concerns
- Dynamic badge counts

## 🚀 **Summary**

Your timesheet implementation now has:
- ✅ **Separate hook files** for each route (like useTimesheetDetails.ts)
- ✅ **Real API calls** using Service.getTimeSheetDetails
- ✅ **Proper status filtering** (2 for adjusted, 3 for approval)
- ✅ **Existing loader integration** - no new loaders
- ✅ **Console logging** for debugging API calls
- ✅ **Error handling** and loading states

The API calls should now work properly with separate hooks! 🎉
