import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface TutoringSelectionsState {
    interestedInFaceToFaceTutoring: boolean;
    interestedInOnlineTutoring: boolean;
    primarySchool: boolean;
    highSchool: boolean;
    secondarySchool: boolean;
    selectedCategory: number | null;
    qualificationCheckedState: boolean[];
    radioState: string;
    selectedJobTypes: number[];
}

const initialState: TutoringSelectionsState = {
    interestedInFaceToFaceTutoring: false,
    interestedInOnlineTutoring: false,
    primarySchool: false,
    highSchool: false,
    secondarySchool: false,
    selectedCategory: null,
    qualificationCheckedState: [],
    radioState: '',
    selectedJobTypes: [],
};

const tutoringSlice = createSlice({
    name: 'tutoringSelections',
    initialState,
    reducers: {
        updateTutoringSelectionsState(state, action: PayloadAction<Partial<TutoringSelectionsState>>) {
            return { ...state, ...action.payload };
        },
    },
    
});

export const { updateTutoringSelectionsState } = tutoringSlice.actions;
export default tutoringSlice.reducer;