import React from 'react';
import styles from '../styles/payment-summary-helper.module.css';
import { Divider } from 'primereact/divider';
import backarrow from '../../../assets/images/Icons/back-icon.png';
import { FaRegQuestionCircle } from 'react-icons/fa';

type PaymentApprovalSummaryProps = {
    isOpen: boolean;
    onClose: () => void;
    invoiceNo: string;
    dateIssued: string;
    totalAmount: string;
    paymentType: string;
    payerEmail: string;
    platformFee: string;
    gst: string;
    jobType: string;
    jobDate?: string; // Optional, if needed
    reference: string;
    dueDateStatus: string;
};

const PaymentApprovalSummary: React.FC<PaymentApprovalSummaryProps> = ({
    isOpen,
    onClose,
    invoiceNo,
    dateIssued,
    totalAmount,
    paymentType,
    jobDate,
    jobType,
    payerEmail,
    platformFee,
    gst,
    reference,
    dueDateStatus
}) => {
    return (
        <div className={`${styles.overlay} ${isOpen ? styles.open : ''}`}>
            <div className={styles.container}>
                <div className={styles.Aproovalheader}>
                    <button className={styles.backBtn} onClick={onClose}>
                        <div style={{ height: "30px", width: "30px", backgroundColor: "#fff", borderRadius: "50%", display: "flex", justifyContent: "center", alignItems: "center" }}>
                            <img src={backarrow} alt="backarrow" width="13px" height="11px" />
                        </div>
                       Payments
                    </button>

                    <p style={{ color: "#fff", fontSize: "22px", fontWeight: "700", marginLeft: "10px", marginTop: "0px" }}>
                        Approve Invoice <span style={{ width: "7px", height: "7px", borderRadius: "50%", color: "#FF6359" }}>•</span> <span style={{ color: "#fff", fontSize: "14px", fontWeight: 400 }}>{dueDateStatus}</span>
                    </p>
                </div>

                <div className={styles.content}>
                    <div className={styles.ApprovalsummaryHeader}>
                        <div>
                            <h2 style={{ fontSize: "14px", fontWeight: "700", color: "#585858", margin: "0" }}>From</h2>
                            <p  style={{ fontSize: "12px", fontWeight: "600", color: "#585858", margin: "0" }} >Juggle Street Pty Ltd</p>
                            <p  style={{ fontSize: "12px", fontWeight: "600", color: "#585858", margin: "0" }} >ABN: **************</p>
                        </div>
                        <div>
                            <h2 style={{ fontSize: "14px", fontWeight: "700", color: "#585858", margin: "0" }}>To</h2>
                            <p style={{ fontSize: "12px", fontWeight: "600", color: "#585858", margin: "0" }}>Craig Sawtell</p>
                            <p style={{ fontSize: "12px", fontWeight: "600", color: "#585858", margin: "0" }}>{payerEmail}</p>
                        </div>
                    </div>

                    <div className={styles.jobDetails}>
                        <span style={{ fontSize: "14px", fontWeight: "600", color: "#585858" }}>Invoice Details</span>
                        <div className={styles.jobDetailRow}>
                            <span style={{fontSize:"12px", color:"#585858" , fontWeight:"600"}}>Invoice Number</span>
                            <span style={{ fontSize: "12px", fontWeight: "600", color: "#585858" }}>{invoiceNo}</span>
                        </div>
                        <div className={styles.jobDetailRow}>
                            <span  style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>Date Issued</span>
                            <span style={{ fontSize: "12px", fontWeight: "600", color: "#585858" }} >{dateIssued}</span>
                        </div>
                        <div className={styles.jobDetailRow}>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>Reference</span>
                            <span style={{ fontSize: "12px", fontWeight: "600", color: "#585858" }}>{reference}</span>
                        </div>
                        <div className={styles.jobDetailRow}>
                            <span style={{ fontSize: "14px", fontWeight: "600", color: "#585858" }}>Total Amount Due</span>
                            <span style={{ fontSize: "12px", fontWeight: "600", color: "#585858" }}>${totalAmount}</span>
                        </div>
                        <div className={styles.jobDetailRow}>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>Due Date</span>
                            <span style={{ color: '#FF6359', fontSize: '12px', fontWeight: '700' }}>{dueDateStatus}</span>
                        </div>
                    </div>
                    <div className={styles.paymentDetails}>
                        <h3 style={{ fontSize: "14px", fontWeight: "700", color: "#585858" }}>Service Details</h3>
                        <div className='flex flex-column gap-1'>
                            <span className={styles.jobType}>{jobType}</span>
                            <span className={styles.jobType}>{jobDate}</span>
                        </div>
                        <div className={styles.detailRow}>
                            <span style={{ fontSize: "12px", fontWeight: "500", color: "#585858", margin: "0" }}>4 hours x $25/h</span>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>$100.00</span>
                        </div>
                        <div className={styles.detailRow}>
                            <span  style={{ fontSize: "12px", fontWeight: "500", color: "#585858", margin: "0" }} >Juggle Street Platform Fee 5% <FaRegQuestionCircle style={{ color: "#37A950", fontSize: "16px" }} /></span>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>${platformFee}</span>
                        </div>
                        <div className={styles.detailRow}>
                            <span  style={{ fontSize: "12px", fontWeight: "500", color: "#585858", margin: "0" }}>GST</span>
                            <span style={{ fontSize: "14px", fontWeight: "500", color: "#585858" }}>${gst}</span>
                        </div>
                        <Divider className='mb-2' />
                        <div className={styles.detailRowTotal}>
                            <span>Job Total</span>
                            <span className={styles.jobTotal}>${totalAmount}</span>
                        </div>
                        <Divider className='mb-2' />
                    </div>
                    <div className='flex flex-row gap-2 justify-content-between' style={{ paddingInline: '10px', marginBottom: '30px'}}>
                        <button className={`${styles.closeBtn}`} style={{ background: "#fff", color: "#FF6359", border: "1px solid #FF6359", fontSize: "14px", fontWeight: "700" }} onClick={onClose}>Reject</button>
                        <button className={`${styles.closeBtn}`} style={{ background: "#FFA500", fontSize: "14px", fontWeight: "700" }}>Approve Invoice</button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PaymentApprovalSummary;
