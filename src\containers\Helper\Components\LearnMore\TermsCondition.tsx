import React, { useEffect, useState } from 'react'
import useLoader from '../../../../hooks/LoaderHook';
import { useNavigate, useSearchParams } from 'react-router-dom';
import environment from '../../../../helper/environment';
import SidePannel from '../SidePannel';
import HomeHeaderHelper from '../HomeHeaderHelper';
// import ArrowLeft from '../../../../assets/images/Icons/side_arrow_left.png'
import ArrowLeft from '../../../../assets/images/Icons/arrow-left.png';
import useIsMobile from '../../../../hooks/useIsMobile';
import HorizontalNavigation from '../../../Common/HorizontalNavigationMobile';

const TermsCondition = () => {
    const navigate = useNavigate();
    const { disableLoader, enableLoader } = useLoader();
    const [frameSrc, setFrameSrc] = useState<string>('');
    const [searchParams] = useSearchParams();
    const{isMobile}=useIsMobile();
    const activeIndex = Number(searchParams.get('activeTab') ?? 1);
    useEffect(() => {
        enableLoader();
        const link = `https://${environment.getMarketingRoot(window.location.hostname)}/terms-and-conditions?hideLayout=1&loggedIn=1`
        setFrameSrc(link);
    }, []);
    const handleIframeLoad = () => {
        
        disableLoader();
      };
    return (
        <div style={{ width: '100%', height: '100vh' }}>
            <SidePannel activeindex={activeIndex} />
            <HomeHeaderHelper />
            <HorizontalNavigation
             title='Terms & Condition'
             onBackClick={()=>navigate('/helper-home/public/learn-more?')}
            />
        {!isMobile && (
                      <div
                      className="relative flex flex-column px-6"
                      style={{
                          minWidth: '1150px',
                          width: 'calc(100% - 288px)',
                          left: '288px',
                      }}
                  >
                      <div
                          className="flex gap-2 justify-content-around align-items-center w-min mt-4 cursor-pointer"
                          style={{
                              textWrap: 'nowrap',
                              border: '1px solid #F1F1F1',
                              padding: '10px 25px',
                              borderRadius: '20px',
                          }}
                          onClick={(e) => {
                              e.preventDefault();
                              navigate('/helper-home/public/learn-more?');
                          }}
                      >
                          <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
                          <p
                              className="m-0 p-0"
                              style={{
                                  fontWeight: '400',
                                  fontSize: '14px',
                                  color: '#585858',
                              }}
                          >
                              Go Back
                          </p>
                      </div>
                  </div>
        )}
            <iframe
                src={frameSrc}
                frameBorder="0"
                style={{  width:!isMobile ? 'calc(100% - 250px)' : "100%", height: '100%', position: 'absolute', right: 0 }}
                onLoad={handleIframeLoad}
            />
        </div>
    )
}

export default TermsCondition