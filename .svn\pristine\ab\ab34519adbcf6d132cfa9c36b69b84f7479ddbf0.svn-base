/* src/common-components/Loader.module.css */

.loaderOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.6); /* White shade with transparency */
    backdrop-filter: blur(6px); /* Slightly stronger blur effect */
    z-index: 9999;
    transition: background-color 0.3s ease, backdrop-filter 0.3s ease; /* Smooth transition */
  }
  
  .loader img {
    width: 150px;  /* Increased size of the logo */
    height: 150px;
  }
  
  .form-container.transparent-effect {
    pointer-events: none; /* Prevent interaction with the form */
    transition: opacity 0.3s ease;
  }

  
  .loaderOverlay[data-ismobile="true"] {
    background-color: rgba(255, 255, 255, 1);
  }