.container {
  width: 100%;
  height: 100%;
}

.p-dialog {
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  max-height: 90%;
  transform: scale(1);
  position: relative;
  width: 72% !important;
}

.reviewPost {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #585858;
  padding-top: 38px;
  height: auto;
  padding-bottom: 30px;
}

.okGotItBtn {
  height: 38px;
  width: 140px;
  font-size: 14px;
  font-weight: 600;
  background-color: #ffa500;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.headerH2 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 24px;
}

.headerH3 {
  font-size: 18px;
  font-weight: 700;
  /* margin-bottom: 18px; */
  /* color: #585858; */
}

.headerH4 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: -7px;
  color: #585858;
}

.jobSummary {
  border: 1px solid #dfdfdf;
  width: 75%;
  /* height: 400px; */
  border-radius: 20px;
  /* padding: 15px 40px; */
  padding-left: 30px;
  padding-right: 20px;
  margin-right: 2%;
}

.tagButton {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tagData {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  border-radius: 10px;
  padding: 3px;
  border-radius: 10px !important;
}

.yourJobDescription {
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.truncated {
  -webkit-line-clamp: 2;
  /* Show only 2 lines */
}

.toggleButton {
  background: none;
  border: none;
  color: blue;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
}

.jobPrice {
  float: right;
}

.jobPriceDoller {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  height: 33px;
}

.jobOvertime {
  font-size: 16px;
  font-weight: 700;
  color: #585858;
  height: 33px;
}

.postJobBtn {
  width: 671px;
  height: 46px;
  border-radius: 20px;
  background-color: #ffa500;
  border: none;
  margin-top: 1.5%;
  color: #ffffff;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
}

.postJobBtn:hover {
  box-shadow: 0px 4px 4px 0px #00000040;
}

.goBack {
  margin-top: 2%;
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  cursor: pointer;
  align-items: center;
  display: flex;
}

.errorMessage {
  color: red;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: 500;
  padding-inline: 15px;
  text-align: center;
  /* display: none; */
  padding-bottom: 20px;
}

.containerMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;

  background-color: #fff;
}

.reviewPostMobile {
  display: flex;
  flex-direction: column;
  color: #585858;
  height: auto;
  width: 100%;
  padding: 20px;
  overflow: auto;
  width: 100%;
}

.headerH2Mobile {
  font-size: 24px;
  font-weight: 700;
  color: #585858;
  margin: 0px;
  margin-bottom: 10px;
}

.headerH4Mobile {
  font-size: 16px;
  font-weight: 700;
  color: #179D52;
  margin-bottom: 0px;
}

.yourJobDescriptionMobile {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  margin-bottom: 0px;
}

.criteriaValue {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
  margin-bottom: 0px;
}

.criteriaLabel {
  font-size: 16px;
  font-weight: 500;
  color: #585858;
}

.fixedFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  /* Optional shadow for better visibility */
  padding: 10px 0;
  /* Padding for the button */
  text-align: center;
  z-index: 999;
  /* Ensure it stays on top of other elements */
  margin-top: auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

/* Style for the Save button */
.nextButtonMobile {
  padding: 10px 20px;
  width: 191px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-color: #FFA500;
  /* Button color */
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nextButtonMobile:disabled {
  background-color: #f1f1f1;
  color: #585858;
  cursor: not-allowed;
  font-size: 14px;
  font-weight: 700;
  width: 100%;
  padding: 10px 20px;
  width: 311px;
  height: 45px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.jobPriceMobile {
  display: flex;
  flex-direction: column;
  /* Default for mobile and tablet */
}

/* For desktop screens (e.g., 1024px and above) */
@media (min-width: 1024px) {
  .jobPriceMobile {
    flex-direction: row;
    align-items: center;
    gap: 20px;
  }
}


.seeMoreButton {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}

.tagButtonMobile {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tagDataMobile {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 10px;
  font-weight: 400;
  color: #585858;
  border-radius: 10px;
  padding: 3px;
  border-radius: 10px !important;
}

.headerH4MobileSec {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  text-wrap: nowrap;
}

.parentInviteMobile {
  font-size: 14px;
  font-weight: 500;
  color: #585858;
}

.helperinfoMobile {
  font-size: 12px;
  font-weight: 500;
  color: #585858;
  border: 1px solid #dfdfdf;
  border-radius: 20px;
  padding-inline: 8px;
  padding-block: 1px;
}

.seeMoreButton {
  font-size: 14px;
  font-weight: 700;
  color: #585858;
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

/* Toast Styles */
/* .customToast {
  background: red !important;
  border: 1px solid #ff4d4f !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  padding: 0 !important;
} */

.customToastContent {
  padding: 12px 16px !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
  /* color: #fff !important; */
}

.customToast .p-toast-message-close {
  display: none !important;
}

/* Remove default icon */
.customToast .p-toast-message-icon {
  display: none !important;
}

.summeryInfo {
  font-size: 14px;
  /* font-weight: 500; */
  color: #585858;
}

.addressTag {
  margin: 0;
  margin-top: 5px;
  padding: 0;
  color: #179D52;
  font-weight: 700;
}

.jobDetailsPlain {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.jobDetailItem {
  display: flex;
  gap: 3px;
}

.detailLabel {
  font-weight: bold;
  color: #585858;
}

.detailValue {
  color: #585858;
}

.CloseBtn {
  position: absolute;
  display: flex;
  justify-content: end;
  width: 25px;
  height: 27px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  right: 15px;
  top: 11px;
}
.dialogContent {
  background-color: #ffffff;
  width: 610px;
  border-radius: 20px;
  padding: 20px;
}
.dialogContentMobile {
  background-color: #ffffff;
  width: 100%;
  max-width: 340px;
  border-radius: 20px;
  padding: 20px;
}

.buttonOk {
  width: 88px;
  height: 42px;
  background-color: #ffa500;
  box-shadow: 0px 0px 4px 0px #00000040;
  text-decoration: underline;
  font-size: 18px;
  font-weight: 500;
 color: #ffffff;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}
.buttonPostMobile {
  padding-inline: 20px;
  background-color: #ffa500;
  box-shadow: 0px 0px 4px 0px #00000040;
  text-decoration: underline;
  font-size: 16px;
  font-weight: 500;
 color: #ffffff;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}

.buttonPost {
  width: 209.78px;
  height: 42px;
  background-color: #ffa500;
  box-shadow: 0px 0px 4px 0px #00000040;
  text-decoration: underline;
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}

.CloseBtn {
  display: flex;
  justify-content: end;
  position: absolute;
  right: -5px;
  width: 30px;
  height: 32px;
  background-color: rgba(255, 255, 255, 1);
  color: #585858;
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  top: -9px;
  cursor: pointer;
}