import React, { useEffect, useState } from "react";
import { ManageJobSectionProps, Jobs } from "../types";
import styles from "../../styles/rate-helpers.module.css";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import calender from "../../../../assets/images/Icons/calender.png";
import home from "../../../../assets/images/Icons/home.png";
import ChildcareImage from "../../../../assets/images/Icons/childcare-smile.png";
import OddJobImage from "../../../../assets/images/Icons/odd_job.png";
import TutoringImage from "../../../../assets/images/Icons/tutoring-book.png";
import sideArrow from "../../../../assets/images/Icons/side-aroow.png";
import filledStar from "../../../../assets/images/Icons/filled-star.png";
import unfilled from "../../../../assets/images/Icons/unfiled.png";
import { Divider } from "primereact/divider";
import { InputTextarea } from "primereact/inputtextarea";
import { Rating } from "primereact/rating";
import NoJobsCard from "../Common/NoJobsCard";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import c from "../../../../helper/juggleStreetConstants";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { useSearchParams } from "react-router-dom";
import useIsMobile from "../../../../hooks/useIsMobile";
import utils from "../../../../components/utils/util";

const getJobDetails = (currentJob?: Jobs) => {
  const jobToUse = currentJob || { jobType: 0 };

  switch (jobToUse.jobType) {
    case 256:
      return {
        label: "Odd Job",
        image: (
          <img src={OddJobImage} alt="Odd Job" width={14.33} height={14.5} />
        ),
      };
    case 128:
    case 64:
      return {
        label: "Tutoring",
        image: (
          <img src={TutoringImage} alt="Tutoring" width={14.33} height={14.5} />
        ),
      };
    default:
      return {
        label: "Childcare",
        image: (
          <img
            src={ChildcareImage}
            alt="Childcare"
            width={14.33}
            height={14.5}
          />
        ),
      };
  }
};

const formatTime = (time: string) => {
  // Assuming the input time is in 24-hour format
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours);
  const period = hour >= 12 ? "pm" : "am";
  const formattedHour = hour % 12 || 12;
  return `${formattedHour}:${minutes}${period}`;
};

function formatDate(dateString: string) {
  const date = new Date(dateString);

  // Array of day names
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Array of month names
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Get day of week
  const dayOfWeek = days[date.getDay()];

  // Get date
  const dateNum = date.getDate();

  // Create ordinal suffix
  function getOrdinalSuffix(n: number) {
    const s = ["th", "st", "nd", "rd"];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
  }

  // Get month name
  const monthName = months[date.getMonth()];

  // Combine into desired format
  return `${dayOfWeek} ${getOrdinalSuffix(dateNum)} of ${monthName}`;
}
function formatDateMobile(dateString: string) {
  const date = new Date(dateString);

  // Array of day names
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Array of month abbreviations
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Get day of the week
  const dayOfWeek = days[date.getDay()];

  // Get date number
  const dateNum = date.getDate();

  // Get abbreviated month name
  const monthAbbr = months[date.getMonth()];

  // Get year
  const year = date.getFullYear();

  // Combine into the required format
  return `${dayOfWeek} ${dateNum} ${monthAbbr} ${year}`;
}

const RateHelperCard = ({
  index,
  job,
  jobDetails,
  changeSelectedJob,
}: {
  index: number;
  job: Jobs;
  jobDetails: {
    label: string;
    image: JSX.Element;
  };
  changeSelectedJob: (value: Jobs) => void;
}) => {
  const getTutoringLabel = (jobType: number) => {
    switch (jobType) {
      case 64: // PRIMARY_SCHOOL_TUTORING
        return "Primary School Tutoring";
      case 128: // HIGH_SCHOOL_TUTORING
        return "High School Tutoring";
      default:
        return "";
    }
  };

  const renderJobTypeInfo = () => {
    if (job.jobType === 64 || job.jobType === 128) {
      return getTutoringLabel(job.jobType);
    } else {
      return job.isRecurringJob ? "Recurring Job" : "One-off Job";
    }
  };
  function cleanAddress(address) {
    return address?.split(" ").slice(0, -2).join(" ");
  }
  const { isMobile } = useIsMobile();
  return !isMobile ? (
    <div className={styles.ratehelperFirstDiv}>
      <div
        style={{
          display: "flex",
          // justifyContent: "center",
          alignItems: "center",
          flexDirection: "row",
          // flex:"1"
        }}
      >
        <img
          src={job.awardedImageSrc}
          alt={`Rate helper image ${index + 1}`}
          height={81}
          width={86.7}
          className={styles.rateHelperImg}
        />

        <div className={styles.rateNameDiv}>
          <h1 className={styles.rateName}>
            {job.awardedFirstName} {job.awardedLastInitial}
          </h1>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              fontSize: "12px",
              fontWeight: "400",
              gap: "5px",
              textDecoration: "underline",
              color: "#585858",
              whiteSpace: "nowrap",
            }}
          >
            {/* {jobDetails.image} */}
            <div
              style={{
                display: "flex",
                flexDirection:
                  job.jobType === 64 || job.jobType === 128 ? "column" : "column",
              }}
            >
              <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                {jobDetails.label} -
              </span>
              <p
                className={styles.subPara}
                style={{
                  margin: "0px",
                  textWrap:
                    job.jobType === 64 || job.jobType === 128
                      ? "wrap"
                      : "wrap",
                }}
              >
                {renderJobTypeInfo()}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.jobInfoContainer}>
        <div className={styles.timeAndDateContainer}>
          {job.isRecurringJob || job.isTutoringJob ? (
            <div  style={{width:"max-content"}}className={styles.timeGet}>
               <img src={clockStart} alt="Clock" width={10.4} height={10.4} />{job.duration} weeks</div>
          ) : (
            <>
              <div className={styles.timeGet}>
              <img src={clockStart} alt="Clock" width={10.4} height={10.4} />
              {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)}
              </div>
            </>
          )}
          <div className={styles.dateGet}>
            <img src={calender} alt="Calender" width={10.4} height={10.4} />
            {formatDate(job.jobDate)}
          </div>
        </div>
        <div className={styles.adressGet}>
          <img src={home} alt="Location" width={10.4} height={10.4} />
          <p>{`${cleanAddress(job.formattedAddress)}`}</p>
        </div>
      </div>

      <Divider layout="vertical" className={styles.rateDivider} />
      <div
        style={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flex:"1"
        }}
      >
        <button
          className={styles.ratehelperButton}
          onClick={() => changeSelectedJob(job)}
        >
          Rate {job.awardedFirstName}{" "}
          <img src={sideArrow} alt="sideArrow" width={6.14} height={12} />
        </button>
      </div>
    </div>
  ) : (
    <div onClick={() => changeSelectedJob(job)}  className={styles.ratehelperFirstDivMobile}>
      <div
        style={{
          display: "flex",
          alignItems: "flex-start",
          flexDirection: "row",
          paddingInline: "10px",
        }}
      >
        <img
          src={job.awardedImageSrc}
          alt={`Rate helper image ${index + 1}`}
          height={81}
          width={86.7}
          className={styles.rateHelperImgMobile}
        />

        <div style={{ display: "flex", flexDirection: "row" }}>
          <div className={styles.rateNameDivMobile}>
            <h1 className={styles.rateNameMobile}>
             <div className="flex flex-row">
             <div>
             {job.awardedFirstName} {job.awardedLastInitial}
             </div>
             {/* <div>
              {job.ratingProvided}
             </div> */}
             </div>
            </h1>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                fontSize: "14px",
                fontWeight: "400",
                gap: "5px",
           
                color: "#585858",
                whiteSpace: "nowrap",
              }}
            >
              {jobDetails.image}
              <div
                style={{
                  display: "flex",
                  flexDirection:
                    job.jobType === 64 || job.jobType === 128
                      ? "column"
                      : "row",
                }}
              >
                <span style={{ margin: "0px", fontSize: "10px",fontWeight:"600" }}>
                  {jobDetails.label} -
                </span>
                <p
                  className={styles.subPara}
                  style={{
                    margin: "0px",
                    textWrap:
                      job.jobType === 64 || job.jobType === 128
                        ? "nowrap"
                        : "nowrap",
                    fontSize: "10px",
                    fontWeight:"600"
                  }}
                >
                  {renderJobTypeInfo()}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <button
            className={styles.ratehelperButtonMobile}
            onClick={() => changeSelectedJob(job)}
          >
            Rate {job.awardedFirstName}{" "}
          </button>
        </div>
      </div>
      <div className={`${styles.jobInfoContainer}`}>
        <div
          style={{
            alignItems: "center",
            marginLeft: "72px",
            marginBottom:"10px",
            bottom: "20px",
            gap:"5px"
          }}
          className={styles.timeAndDateContainer}
        >
          <div className={styles.timeDateMobile}>
            {formatDateMobile(job.jobDate)}
          </div>

          {job.isRecurringJob || job.isTutoringJob ? (
            <div className={styles.timeGetMobile}>{job.duration} weeks</div>
          ) : (
            <>
              <div className={styles.timeGetMobile}>
                {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
const StepRatingComponent = ({
  job,
  onComplete,
}: {
  job: Jobs;
  onComplete: (
    ratings: { [key: string]: number | string },
    feedback: String
  ) => void;
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const { isMobile } = useIsMobile();
  const [ratings, setRatings] = useState({
    Punctuality: 0,
    "Performance of duties": 0,
    "Communication with you": 0,
    "What rating do you think your children would give?": 0,
  });

  const [feedback, setFeedback] = useState<string>("");

  const steps = [
    { label: "Punctuality", key: "Punctuality" },
    { label: "Performance of duties", key: "Performance of duties" },
    { label: "Communication with you", key: "Communication with you" },
    {
      label: "What rating do you think your children would give?",
      key: "What rating do you think your children would give?",
    },
    { label: "Public review of Helpers", key: "Public review of Helpers" },
  ];

  const handleRatingChange = (value: number | string) => {
    const currentStepKey = steps[currentStep].key;
    setRatings((prev) => ({
      ...prev,
      [currentStepKey]: value,
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep((prev) => prev + 1);
    } else {
      // Final step - complete ratings
      onComplete(ratings, feedback);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleFeedbackChange = (value: string) => {
    setFeedback(value);
  };

  const currentStepDetails = steps[currentStep];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
      case 1:
      case 2:
      case 3:
        return  !isMobile ? (
          <>
          <Rating
            value={ratings[currentStepDetails.key] as number}
            onChange={(e) => handleRatingChange(e.value || 0)}
            cancel={false}
            className={styles.ratingsStar}
            stars={5}
            onIcon={
              <img
                src={filledStar}
                alt="rating on"
                width="40px"
                height="40px"
              />
            }
            offIcon={
              <img
                src={unfilled}
                alt="rating off"
                width="40px"
                height="40px"
              />
            }
          />
          <p
            style={{
              marginBlock: "20px",
              color: "#585858",
              fontSize: "12px",
              fontWeight: "400",
            }}
          >
            What would you rate out of 5 stars?
          </p>
        </>
        ):(          <>
          <Rating
            value={ratings[currentStepDetails.key] as number}
            onChange={(e) => handleRatingChange(e.value || 0)}
            cancel={false}
            className={styles.ratingsStar}
            stars={5}
            onIcon={
              <img
                src={filledStar}
                alt="rating on"
                width="40px"
                height="40px"
              />
            }
            offIcon={
              <img
                src={unfilled}
                alt="rating off"
                width="40px"
                height="40px"
              />
            }
          />
          <p
            style={{
              marginBlock: "15px",
              color: "#585858",
              fontSize: "12px",
              fontWeight: "400",
            }}
          >
            What would you rate out of 5 stars?
          </p>
        </>) 
      case 4:
        return !isMobile ? (
          <>
          <p style={{ margin: "0px", fontSize: "14px", fontWeight: "400" }}>
            Optional review which will appear on {`${job.awardedFirstName}'s`}{" "}
            profile
          </p>
          <InputTextarea
            value={feedback}
            autoResize
            onChange={(e) => handleFeedbackChange(e.target.value)}
            rows={5}
            cols={30}
            placeholder="Write a quick review...This will appear on the helpers profile and help other parents in your neighbourhood"
            className={styles.textArea}
          />
        </>
        ):(
          <>
          <p style={{ margin: "0px", fontSize: "12px", fontWeight: "400"}}>
            Optional review which will appear on {`${job.awardedFirstName}'s`}{" "}
            profile
          </p>
          <InputTextarea
            value={feedback}
            autoResize
            onChange={(e) => handleFeedbackChange(e.target.value)}
            rows={5}
            cols={30}
            placeholder="Write a quick review...This will appear on the helpers profile and help other parents in your neighbourhood"
            className={styles.textAreaMobile}
          />
        </>
        )
    }
  };

  return !isMobile ? (
    <div className={styles.stepRatingContainer}>
      <div className={styles.horizontalTabContainer}>
        {steps.map((step, index) => (
          <div key={index} style={{ display: "contents" }}>
            <div
              className={`${styles.tabItem} ${
                index <= currentStep ? styles.activeTab : ""
              }`}
            ></div>
            {index < steps.length - 1 && (
              <div className={styles.tabSpacing}></div>
            )}
          </div>
        ))}
      </div>

      <div className={styles.ratingStepContent}>
        <label
          style={{
            marginTop: "15px",
            marginBottom: currentStep === 4 ? "0px" : "30px",
          }}
          className={styles.ratingLabel}
        >
          {currentStepDetails.label}
        </label>
        {renderStepContent()}
      </div>
      <Divider className={styles.maxDivider} />
      <div className={styles.ratingNavigation}>
        {currentStep > 0 && (
          <button className={styles.navigationButton} onClick={handleBack}>
            {"<  Go Back"}
          </button>
        )}

        <button
          className={styles.navigationButtonSecond}
          onClick={handleNext}
          disabled={currentStep < 4 && ratings[currentStepDetails.key] === 0}
        >
          {currentStep < steps.length - 1 ? "Next >" : "Submit >"}
        </button>
      </div>
    </div>
  ):(

    <div className={styles.stepRatingContainer}>
    <div className={styles.horizontalTabContainerMobile}>
      {steps.map((step, index) => (
        <div key={index} style={{ display: "contents" }}>
          <div
            className={`${styles.tabItem} ${
              index <= currentStep ? styles.activeTab : ""
            }`}
          ></div>
          {index < steps.length - 1 && (
            <div className={styles.tabSpacing}></div>
          )}
        </div>
      ))}
    </div>

    <div className={`pr-3 ${styles.ratingStepContentMobile}`}>
      <label
        style={{
          marginTop: "15px",
          marginBottom: currentStep === 4 ? "0px" : "10px",
        }}
        className={styles.ratingLabel}
      >
        {currentStepDetails.label}
      </label>
      {renderStepContent()}
    </div>
    <div className={styles.ratingNavigationMobile}>
      {currentStep > 0 && (
        <button className={styles.navigationButton} onClick={handleBack}>
          {"<  Go Back"}
        </button>
      )}

      <button
        className={styles.navigationButtonSecondMobile}
        onClick={handleNext}
       disabled={currentStep < 4 && ratings[currentStepDetails.key] === 0}
      >
        {currentStep < steps.length - 1 ? "Next" : "Submit"}
      </button>
    </div>
  </div>
  )
};

const RateHelpers: React.FC<ManageJobSectionProps> = ({
  unRatedJobs,
  refresh,
}) => {
  const [selectedJob, setSelectedJob] = useState<Jobs | null>(null);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { disableLoader, enableLoader } = useLoader();
  const [searchParams] = useSearchParams();
  const { isMobile } = useIsMobile();
  const handleRatingComplete = (
    ratings: { [key: string]: number | string },
    feedback: string
  ) => {
    const payload = {
      category1: ratings["Punctuality"],
      category2: ratings["Performance of duties"],
      category3: ratings["Communication with you"],
      category4: ratings["What rating do you think your children would give?"],
      feedback: feedback,
      jobId: selectedJob?.id, // Assuming there's an id field in the Jobs type
      ratingFor: selectedJob.awardedApplicantId,
      ratingBy: sessionInfo.data["id"],
      ratingMode: c.ratingMode.PAGE,
    };

    enableLoader();

    Service.rateHelper(
      async (response) => {
        setSelectedJob(null);
        await refresh();
        disableLoader();
      },
      (error) => {
        disableLoader();
      },
      selectedJob.id,
      payload
    );
  };

  useEffect(() => {
    const rateHelperId = searchParams.get("rateJobId");
    const applicantId = searchParams.get("applicantId"); // Get applicantId from URL
  
    if (rateHelperId) {
      // Find jobs matching rateJobId
      const matchingJobs = unRatedJobs.filter((job) => job.id === Number(rateHelperId));
  
      if (matchingJobs.length > 0) {
        if (applicantId) {
          // If applicantId is provided, find the job where awardedApplicantId matches
          const selected = matchingJobs.find(
            (job) => job.awardedApplicantId === Number(applicantId)
          );
          if (selected) {
            setSelectedJob(selected);
          } else {
            setSelectedJob(null); // or setSelectedJob(matchingJobs[0]) if you want a fallback
          }
        } else {
          setSelectedJob(matchingJobs[0]);
        }
      } else {
        setSelectedJob(null); // No jobs found with rateJobId
      }
    } else {
      setSelectedJob(null); // No rateJobId provided
    }
  }, [unRatedJobs, searchParams]);

  const renderSelectedJobDetails = (job: Jobs,isMobile:boolean) => {
    const jobDetails = getJobDetails(job);
    return (
      !isMobile ? (
        <>
        <div className={styles.ratehelperSecondDiv}>
          <img
            src={job.awardedImageSrc}
            alt={`Rate helper image for ${job.awardedFirstName}`}
            height={81}
            width={86.7}
            className={styles.rateHelperImg}
          />
          <div className={styles.rateNameDiv}>
            <h1 className={styles.rateName}>
              {job.awardedFirstName} {job.awardedLastInitial}
            </h1>
            <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  fontSize: "14px",
                  fontWeight: "400",
                  gap: "5px",
                  textDecoration: "underline",
                  color: "#585858",
                  whiteSpace: "nowrap",
                }}
              >
                {jobDetails.image}
                <span style={{ margin: "0px" }} className={styles.jobTypeLabel}>
                  {jobDetails.label} -
                </span>
                <p className={styles.subPara} style={{ margin: "0px" }}>
                  {job.isRecurringJob === true
                    ? "Recurring Job"
                    : job.isRecurringJob === false
                    ? "One-off Job"
                    : ""}
                </p>
              </div>
              <div className={styles.dateGetSec}>
                {" "}
                <img src={calender} alt="calender" width={14.4} height={14.4} />
                {formatDate(selectedJob.jobDate)}
              </div>
            </div>
          </div>
        </div>
        <StepRatingComponent job={job} onComplete={handleRatingComplete} />
      </>
      ):(
        
        <>
         <div className={styles.backButtonContainer}>
                <div 
                   style={{fontSize:"16px", paddingBottom:"10px", fontWeight:"500",  color:"#585858"}}
                    onClick={() => setSelectedJob(null)}
                >
                    {"< Go Back"}
                </div>
            </div>
       <div className="pt-3 pb-3 pl-3 pr-0" style={{
        boxShadow:"0px 0px 3px 0px #00000040",
        borderRadius:"20px"
       }} >
         <>
        <div className={styles.ratehelperSecondDivMobile}>
          <img
            src={job.awardedImageSrc}
            alt={`Rate helper image for ${job.awardedFirstName}`}
            style={{width:"61px",height:"61px",borderRadius:"50%"}}
          />
          <div className={styles.rateNameDivMobile}>
            <h1 className={styles.rateNameMobile}>
              {job.awardedFirstName} {job.awardedLastInitial}
            </h1>
            <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  fontSize: "14px",
                  fontWeight: "400",
                  gap: "5px",
                
                  color: "#585858",
                  whiteSpace: "nowrap",
                }}
              >
                {jobDetails.image}
                <span style={{ margin: "0px",fontSize:"10px",fontWeight:"700" }}  className={styles.jobTypeLabel}>
                  {jobDetails.label} -
                </span>
                <p className={styles.subPara} style={{ margin: "0px",fontSize:"10px",fontWeight:"700" }}>
                  {job.isRecurringJob === true
                    ? "Recurring Job"
                    : job.isRecurringJob === false
                    ? "One-off Job"
                    : ""}
                </p>
              </div>
             
            </div>
            <div className="flex flex-row gap-2 mt-2">
            <div className={styles.timeDateMobile}>
                {" "}
             
                {formatDateMobile(selectedJob.jobDate)}
              </div>
               {job.isRecurringJob || job.isTutoringJob ? (
            <div className={styles.timeGetMobile}>{job.duration} weeks</div>
          ) : (
            <>
              <div className={styles.timeGetMobile}>
                {formatTime(job.jobStartTime)} - {formatTime(job.jobEndTime)}
              </div>
            </>
          )}
            </div>
          </div>
        </div>
        <StepRatingComponent job={job} onComplete={handleRatingComplete} />
      </>
       </div>
       </>
      ) 
    );
  };

  return !isMobile ? (
    <div   className={styles.ratehelperContainer}>
      {!selectedJob ? (
        <div
          className={styles.rateHelperMain}
          style={{
            border:
              unRatedJobs.length > 0
                ? "1px solid rgba(223, 223, 223, 1)"
                : "none",
          }}
        >
          {unRatedJobs.length > 0 ? (
            <div className={styles.rateHelperImgContainer}>
              {unRatedJobs.map((job, index) => {
                const jobDetails = getJobDetails(job);
                return (
                  <RateHelperCard
                    key={index}
                    index={index}
                    job={job}
                    jobDetails={jobDetails}
                    changeSelectedJob={(v) => setSelectedJob(v)}
                  />
                );
              })}
            </div>
          ) : (
            <div style={{width:"60%"}} className="mr-4">
              <NoJobsCard description="There are currently no jobs awaiting review. " />
            </div>
          )}
        </div>
      ) : (
        renderSelectedJobDetails(selectedJob, isMobile)
      )}
    </div>
  ) : (
    <div className={styles.ratehelperContainerMobile}>
     <div >
     {!selectedJob ? (
        <div className={styles.rateHelperMainMobile}>
          {unRatedJobs.length > 0 ? (
            <div className={styles.rateHelperImgContainer}>
              {unRatedJobs.map((job, index) => {
                const jobDetails = getJobDetails(job);
                return (
                  <RateHelperCard
                    key={index}
                    index={index}
                    job={job}
                    jobDetails={jobDetails}
                    changeSelectedJob={(v) => setSelectedJob(v)}
                  />
                );
              })}
            </div>
          ) : (
            <div>
              <NoJobsCard description="There are currently no jobs awaiting review. " />
            </div>
          )}
        </div>
      ) : (
        renderSelectedJobDetails(selectedJob,isMobile)
      )}
     </div>
    </div>
  );
};

export default RateHelpers;
