import React, { useState } from 'react';
import HelperCard from './HelperCard';
import { SlLocationPin } from 'react-icons/sl';
import { IoFlagSharp, IoLanguage, IoStarSharp } from 'react-icons/io5';
import { BiMessageRoundedDetail } from 'react-icons/bi';
import { RiFileCheckLine } from 'react-icons/ri';
import { UserResult } from '../../hooks/SearchGeoSearchHook';
import HelperActionCard from './HelperActionCard';
import Service from '../../services/services';
import c from '../../helper/juggleStreetConstants';
import useLoader from '../../hooks/LoaderHook';
import { ConfirmationPopupGreen, useConfirmationPopup } from './ConfirmationPopup';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { RxUpdate } from 'react-icons/rx';
import { updateProfileActivationEnabled } from '../../store/slices/applicationSlice';
import { GrHide } from 'react-icons/gr';
import environment from '../../helper/environment';
import { useNavigate } from 'react-router-dom';
import CookiesConstant from '../../helper/cookiesConst';
import utils from '../../components/utils/util';
import useIsMobile from '../../hooks/useIsMobile';
import { BsFillPatchCheckFill } from 'react-icons/bs';
import { TbManFilled } from "react-icons/tb";
import { IoCarOutline } from "react-icons/io5";
import { FaPlusSquare } from "react-icons/fa";
import ProviderProfile from '../Parent/ProviderProfile/ProviderProfile';
import CustomDialog from '../../commonComponents/CustomDialog';
interface homePageProps extends UserResult {
    refresh: () => void;
}

const CustomHomepageCard: React.FC<homePageProps> = ({
    refresh,
    id,
    publicName,
    suburb,
    jobsCompleted,
    ratingsCount,
    imageSrc,
    metadata,
    friendStatus,
    requestId,
}) => {
    const navigate = useNavigate();
    const [dialogVisible, setDialogVisible] = useState(false);
    const [errorDialogVisible, setErrorDialogVisible] = useState(false);
    const { enableLoader, disableLoader } = useLoader();
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const { isMobile } = useIsMobile();
    const reqId = isMobile ? new URLSearchParams().get('requestId') : requestId;
    const redirectAfterHome = () => {
        const searchParams = new URLSearchParams();
        searchParams.set('id', id.toString());
        const reqId = isMobile ? searchParams.get('requestId') : requestId;
        if (reqId) {
            searchParams.set('requestId', requestId.toString());
        }
        const clientType = utils.getCookie(CookiesConstant.clientType);

        // Conditionally navigate based on clientType
        if (clientType === '2') {
            navigate(`/business-home/provider-profile?${searchParams.toString()}`);
        } else if (clientType === '1') {
            navigate(`/parent-home/provider-profile?${searchParams.toString()}`);
        } else {
            console.warn('Unknown clientType, no navigation performed.');
        }
    };
    const [showPopup, setShowPopup] = useState(false);
    const redirectProfilePopup = () => {
        setShowPopup(true);

    };
    const handleCloseProfilePopup = () => {
        setShowPopup(false);
        // setSelectedCandidate(null);
    };

    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const dispatch = useDispatch<AppDispatch>();
    const handleXClicked = () => {
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                publicName,
                `Your profile must be 100% complete before you can connect with ${publicName}`,
                'Complete',
                <RxUpdate />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        if (friendStatus === 4) {
            setDialogVisible(true);
        }
    };

    const handleFavClicked = () => {
        if (sessionInfo.data['profileCompleteness'] <= 99) {
            showConfirmationPopup(
                publicName,
                `Your profile must be 100% complete before you can connect with ${publicName}`,
                'Complete',
                <RxUpdate />,
                () => {
                    dispatch(updateProfileActivationEnabled(true));
                }
            );
            return;
        }
        if (friendStatus === 4) {
            setDialogVisible(true);
        }
    };

    const handleAddToFavorites = () => {
        setDialogVisible(false);
        enableLoader();
        let newStatus = friendStatus;
        if (friendStatus === c.friendStatus.NONE) {
            newStatus = c.friendStatus.PENDING;
        } else if (friendStatus === c.friendStatus.PENDING) {
            newStatus = c.friendStatus.APPROVED;
        }
        const payload = {
            friendId: id,
            invitationMessage: '',
            status: newStatus,
        };
        Service.addFriend(
            payload,
            () => {

                setDialogVisible(false); // Close dialog on success
                disableLoader();
                refresh();
            },
            (error) => {
                console.error('Failed to add friend:', error);
                // Handle failure case
            }
        );
    };

    const handleHide = () => {
        // Show confirmation popup regardless of profile completeness
        showConfirmationPopup(
            publicName,
            `Are you sure you want to Hide ${publicName}?`, // Message asking for confirmation
            'Hide',
            <GrHide style={{ fontSize: '20px' }} />,
            () => {
                // If confirmed, execute hide logic here

                executeHideAction(); // This function will handle the actual hide logic
            }
        );
        return;
    };

    const executeHideAction = () => {
        setDialogVisible(false);
        enableLoader();
        let newStatus = c.friendStatus.HIDDEN;
        const payload = {
            friendId: id,
            invitationMessage: '',
            status: newStatus,
        };

        Service.addFriend(
            payload,
            () => {
                setDialogVisible(false);
                disableLoader();
                setTimeout(() => {
                    refresh();
                }, 10);
            },
            (error) => {
                console.error('Failed to hide profile:', error);
                setErrorDialogVisible(true);
                showConfirmationPopup(
                    'Error',
                    error.message || 'Failed to delete the account.',
                    'OK',
                    null,
                    () => { }
                );
                disableLoader();
            }
        );
    };

    const handleClosePopup = () => {
        setDialogVisible(false);
    };
    const getCountryLabel = (value: string) => {
        if (!value) return null;
        value = value.toLowerCase();
        if (value === "au") return "Australia";
        if (value === "nz") return "New Zealand";
        const country = c.countriesIso.find(country => country.value === value);
        return country ? country.label : null;
    };

    const getUserLanguages = (metadata) => {
        var allLanguages: any = utils.getLanguageList();
        var sep = ', ';
        var selectedList = [];
        var list = metadata.languages;
        if (list.length === 0) return '';

        for (let i = 0; i < list.length; i++) {
            const match = allLanguages.find(lang => lang.payload == list[i]);
            if (match) {
                selectedList.push(match.text);
            }
        }

        if (selectedList.length > 0) {
            return selectedList.slice(0, 3).join(sep);
        }

        return '';
    };



    const items = [
        {
            icon: <SlLocationPin color='#37A950' fontSize={'18px'} />,
            description: (
                <p className='m-0' style={{ fontSize: '16px', fontWeight: 600 }}>
                    {suburb}
                </p>
            ),
        },
        (jobsCompleted > 0 || ratingsCount > 0) && {
            icon: <IoStarSharp color='#FFA500' fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {jobsCompleted} {jobsCompleted === 1 ? 'Job' : 'Jobs'}
                    {ratingsCount > 0 && <span> ({ratingsCount} Ratings)</span>}
                </p>
            ),
        },
        (metadata.responseRate >= 75 && metadata.responseTime == 1 || metadata.responseTime == 24) && {
            icon: (
                <BiMessageRoundedDetail
                    fontSize={'18px'}
                    style={{ transform: 'rotateY(180deg)' }}
                />
            ),
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    {utils.getResponseTimeText(metadata.responseTime)} Response Rate
                </p>
            ),
        },

        (metadata.hasSpecialNeedsExperience) && {
            icon: <TbManFilled fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    Special Needs
                </p>
            ),
        },

        (metadata.qualifications.length > 0 || metadata.firstAid.length > 0) && {
            icon: <FaPlusSquare fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    Qualification
                </p>
            ),
        },
        getUserLanguages(metadata) && {
            icon: <IoLanguage color='#585858' fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {getUserLanguages(metadata)}
                </p>
            ),
        },
        (metadata.hasDrivingLicence) && {
            icon: <IoCarOutline fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    Drivers Licence
                </p>
            ),
        },
        metadata.nationality && getCountryLabel(metadata.nationality) && {
            icon: <IoFlagSharp color='#585858' fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    {getCountryLabel(metadata.nationality)}
                </p>
            ),
        },
        (metadata.certificates.wwcc) && {
            icon: <BsFillPatchCheckFill color='#585858' fontSize={'18px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '16px' }}>
                    wwcc
                </p>
            ),
        },

    ].filter(Boolean).slice(0, 4);

    const itemsMobile = [
        {
            icon: <SlLocationPin color='#37A950' fontSize={'15px'} />,
            description: (
                <p className='m-0' style={{ fontSize: '12px', fontWeight: 600 }}>
                    {suburb}
                </p>
            ),
        },
        (jobsCompleted > 0 || ratingsCount > 0) && {
            icon: <IoStarSharp color='#FFA500' fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {jobsCompleted} {jobsCompleted === 1 ? 'Job' : 'Jobs'}
                    {ratingsCount > 0 && <span> ({ratingsCount} Ratings)</span>}
                </p>
            ),
        },
        (metadata.responseRate >= 75 && metadata.responseTime == 1 || metadata.responseTime == 24) && {
            icon: (
                <BiMessageRoundedDetail
                    fontSize={'15px'}
                    style={{ transform: 'rotateY(180deg)' }}
                />
            ),
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    {utils.getResponseTimeText(metadata.responseTime)} Response Rate
                </p>
            ),
        },
        (metadata.hasSpecialNeedsExperience) && {
            icon: <TbManFilled fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    Special Needs
                </p>
            ),
        },
        (metadata.qualifications.length > 0 || metadata.firstAid.length > 0) && {
            icon: <RiFileCheckLine fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    Qualification
                </p>
            ),
        },
        getUserLanguages(metadata) && {
            icon: <IoLanguage color='#585858' fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    {getUserLanguages(metadata)}
                </p>
            ),
        },
        (metadata.hasDrivingLicence) && {
            icon: <IoCarOutline fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    Drivers Licence
                </p>
            ),
        },
        metadata.nationality && getCountryLabel(metadata.nationality) && {
            icon: <IoFlagSharp color='#585858' fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    {getCountryLabel(metadata.nationality)}
                </p>
            ),
        },
        (metadata.certificates.wwcc) && {
            icon: <BsFillPatchCheckFill color='#585858' fontSize={'15px'} />,
            description: (
                <p className='m-0 font-light' style={{ fontSize: '12px' }}>
                    wwcc
                </p>
            ),
        },

    ].filter(Boolean).slice(0, 4);



    return !isMobile ? (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />

            <HelperCard
                helperName={publicName}
                imgSrc={`${environment.getStorageURL(window.location.hostname)}/images/${imageSrc}`}
                mainDivStyles={{
                    height: '150px',
                }}
                titleFontSize={22}
                isSuperHelper={metadata.isSuperProvider}
                hasProfileVideo={metadata.hasVideo}
                items={items}
                onImgClicked={redirectProfilePopup}
                onXClicked={handleXClicked}
                onFavClicked={handleFavClicked}
                friendStatus={friendStatus}
            />
            {friendStatus === 4 && (
                <HelperActionCard
                    visible={dialogVisible}
                    onHide={handleHide}
                    onAddToFavorites={handleAddToFavorites}
                    name={publicName}
                    imageSrc={`${environment.getStorageURL(
                        window.location.hostname
                    )}/images/${imageSrc}`}
                    onClose={handleClosePopup}
                />
            )}
            <CustomDialog
                visible={showPopup}
                style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%', backgroundColor: '#ffffff', borderRadius: '0px', overflowY: 'auto' }}
                onHide={() => { }}
                draggable={false}
            >
                <ProviderProfile
                    candidateId={id}
                    requestId={Number(reqId)}
                    onClose={handleCloseProfilePopup}
                />
            </CustomDialog>
        </>
    ) : (
        <>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />

            <HelperCard
                helperName={publicName}
                imgSrc={`${environment.getStorageURL(window.location.hostname)}/images/${imageSrc}`}
                mainDivStyles={{
                    height: '150px',
                }}
                titleFontSize={22}
                isSuperHelper={metadata.isSuperProvider}
                hasProfileVideo={metadata.hasVideo}
                items={itemsMobile}
                onImgClicked={redirectAfterHome}
                onXClicked={handleXClicked}
                onFavClicked={handleFavClicked}
                friendStatus={friendStatus}
            />
            {friendStatus === 4 && (
                <HelperActionCard
                    visible={dialogVisible}
                    onHide={handleHide}
                    onAddToFavorites={handleAddToFavorites}
                    name={publicName}
                    imageSrc={`${environment.getStorageURL(
                        window.location.hostname
                    )}/images/${imageSrc}`}
                    onClose={handleClosePopup}
                />
            )}
        </>
    );
};
export default CustomHomepageCard;
