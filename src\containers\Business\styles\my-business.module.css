/* Container for the whole page */
.buisnesscontainer {
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-top: 7px;
}

/* Header styling */
.buisnessheader h1 {
  width: 325px;
  height: 48px;
  font-weight: 700;
  font-size: 32px;
  color: #585858;
  line-height: 48px;
}

.businessHeader {
  margin-top: 10px;
  font-size: 24px;
  font-weight: 600;
  color: #585858;
}


.buisnessprogressbar {
  width: 666px;
  max-width: 100%;
  height: 7px;
  margin-top: 1rem;
}

.buisnessprogressbar>div {
  background-color: #179d52 !important;
}

.buisnessprogressbar>div>div {
  display: none;
}

/* Main section for the two flex divs */
.buisnessmain {}

/* Flex items styling */
.buisnessflexItem {
  display: flex;
  background-color: white;
  margin: 10px;
  width: min-content;
  gap: 55px;
}

.buisnessflexItem h2 {
  font-size: 24px;
  color: #585858;
}

.buisnessyear {
  width: 302px;
  box-shadow: none;
  text-align: center;
  border-radius: 10px;
}
.businessTitle{
    color: rgb(88, 88, 88);
    font-size: 18px;
    width: 325px;
    height: 48px;
    font-weight: 700;
    color: #585858;
    line-height: 48px;
    margin-top: 10px;
}
.successBusiness{
    display: flex;
    justify-content: space-between;
}

.buisnessyear>div[class="p-dropdown-trigger"] {
  display: none !important;
}

.buisnessemployee {
  width: 302px;
  box-shadow: none;
  border-radius: 10px;
}

.buisnessemployee>div[class="p-dropdown-trigger"] {
  display: none !important;
}

.buisnessindustrysector {
  width: 302px;
  box-shadow: none;
  border-radius: 10px;

}

.buisnessindustrysector>div[class="p-dropdown-trigger"] {
  display: none !important;
}

.buisnessindustrysector span {
  display: flex !important;
  font-size: 14px !important;
  justify-content: center !important;

}

.buisnessindustrysector>div[class="p-dropdown"] {
  white-space: nowrap !important;
  /* Prevent text from wrapping */
  overflow: hidden !important;
  /* Hide the overflowing text */
  text-overflow: ellipsis !important;
  /* Apply the ellipsis (...) */
  max-width: 100% !important;
  /* Ensure it applies to the width of the container */
}

.buisnessemployee span {
  display: flex !important;
  font-size: 14px !important;
  justify-content: center !important;
}

.buisnessyear span {
  display: flex !important;
  font-size: 14px !important;
  justify-content: center !important;
}

.buisnessflexsecond {
  background-color: white;
  margin: 10px;
  width: 658px;
  max-width: 100%;

}

.businessIntroTitle {
  height: 48px;
  font-weight: 600;
  font-size: 18px;
  color: #585858;
  line-height: 48px;
  margin-top: 0px;
  margin-bottom: 6px;

}

.businessIntrodiscribe {
  font-size: 16px;
  color: #585858;
  font-weight: 400;
  line-height: 24px;
  margin-top: 0px;
}

.inputTextareabuisness {
  width: 100%;
  height: 100%;
  border: none;
  border-color: unset;
  box-shadow: none;
  border-radius: 10px;
  font-weight: 300;
  font-size: 12px;
}
.instruct {
    font-size: 12px;
    font-weight: 600;
    line-height: 18px;
    color: #585858;
  }

@media (max-width: 600px) {
  .businessIntroTitle {
    font-size: 20px;
    /* Adjusted font size for smaller screens */
    line-height: 32px;
    /* Optional: adjust line-height to maintain spacing */
  }
}

.businessIntrodiscribe {
  font-size: 16px;
  /* Default font size */
  color: #585858;
  font-weight: 400;
  line-height: 24px;
  margin-top: 0px;
}

@media (max-width: 600px) {
  .businessIntrodiscribe {
    font-size: 12px;
    line-height: 20px;
  }
}



/* Responsive adjustments */
@media (max-width: 768px) {
  .buisnessflexItem {
    width: 100%;
    /* Stack items vertically on smaller screens */
  }
}

/* Footer styling */
.buisnessfooter {}

@media (max-width: 768px) {

  .buisnessflexItem,
  .buisnessflexsecond {
    width: 100%;
    flex-direction: column;
    gap: 0px;
  }

  .buisnessyear,
  .buisnessemployee,
  .buisnessindustrysector {
    width: 100%;
    /* Full width dropdowns on smaller screens */
  }

  .buisnessprogressbar {
    width: 100%;
  }
}

@media (max-width: 480px) {

  .buisnessflexItem,
  .buisnessflexsecond {
    width: 100%;
    flex-direction: column;
    margin: 0;
  }

  .buisnessyear,
  .buisnessemployee,
  .buisnessindustrysector {
    width: 100%;
    /* Full width on small screens */
  }

  .buisnessprogressbar {
    width: 100%;
  }
}

.globalParagraphStyle {
  font-weight: 600;
  font-size: 16px;
  color: #585858;
}

@media (max-width: 768px) {
  .globalParagraphStyle {
    margin-top: -7px;
  }
}
