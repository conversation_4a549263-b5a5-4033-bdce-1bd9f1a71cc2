// import { useEffect, useRef, useState } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { AppDispatch, RootState } from "../../../store";
// import { useNavigate } from "react-router-dom";
// import menu from "../../../assets/images/Icons/menu.png";
// import { Avatar } from "primereact/avatar";
// import aboutMeIcon from "../../../assets/images/Icons/about_me.png";
// import familyIcon from "../../../assets/images/Icons/my_family.png";
// import settingsIcon from "../../../assets/images/Icons/settings.png";
// import logoutIcon from "../../../assets/images/Icons/logout.png";
// import giftIcon from "../../../assets/images/Icons/refer.png";
// import defaultGiftIcon from "../../../assets/images/Icons/referGrey.png";
// import {
//   updateAccountAndSettingsActiveTab,
//   updateShowAccountAndSettings,
//   updateShowProfileActivation,
// } from "../../../store/slices/applicationSlice";
// import { removeSession } from "../../../store/slices/sessionInfoSlice";
// import CustomDialog from "../../Common/CustomDialog";
// import AccountLayout from "../../Parent/AccountSetting/AccountLayout";
// import style from "../../Common/styles/home-header.module.css";
// import { IoCheckmarkSharp, IoEye, IoVideocamSharp } from "react-icons/io5";
// import { TiArrowBack } from "react-icons/ti";
// import {
//   MdCardMembership,
//   MdChildCare,
//   MdDryCleaning,
//   MdFeed,
//   MdGrading,
//   MdLocationCity,
//   MdMonetizationOn,
//   MdStars,
//   MdSupervisedUserCircle,
// } from "react-icons/md";
// import { FaCar } from "react-icons/fa6";
// import { FaHome } from "react-icons/fa";
// import { updateActiveTabIndex } from "../../../store/slices/accountSettingSlice";
// import { IoMdSchool } from "react-icons/io";
// import c from "../../../helper/juggleStreetConstants";
// import useIsMobile from "../../../hooks/useIsMobile";

// function HomeHeaderHelper() {
//   const menuRef = useRef<HTMLDivElement>(null);
//   const buttonRef = useRef<HTMLDivElement>(null);
//   const [showMenu, setShowMenu] = useState(false);
//   const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
//   const { enableAccountAndSettings } = useSelector(
//     (state: RootState) => state.applicationState
//   );
//   const dispatch = useDispatch<AppDispatch>();
//   const {isMobile} = useIsMobile()
//   const navigate = useNavigate();
//   const isApproved = (): boolean => {
//     const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
//     if (!sessionInfo.data) {
//       return false;
//     }
//     return sessionInfo.data?.["accountStatus"] === c.accountStatus.APPROVED;
//   };
//   const approved = isApproved();
//   const options = [
//     {
//       option: "Juggle Street Profile",
//       options: [
//         isApproved() && {
//           icon: <IoCheckmarkSharp width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Profile Strength",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(21));
//             dispatch(updateAccountAndSettingsActiveTab(21));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <img
//               src={aboutMeIcon}
//               alt="About Me"
//               width="14.47px"
//               height="14.4px"
//             />
//           ),
//           optionName: "About Me",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(0));
//             dispatch(updateAccountAndSettingsActiveTab(0));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <IoVideocamSharp
//               style={{ width: "14.47px", height: "14.4px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Profile Video",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(10));
//             dispatch(updateAccountAndSettingsActiveTab(10));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         isApproved() && {
//           icon: (
//             <TiArrowBack
//               style={{ width: "17px", height: "19px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Response Stats",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(18));
//             dispatch(updateAccountAndSettingsActiveTab(18));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         isApproved() && {
//           icon: (
//             <img
//               src={familyIcon}
//               alt="View Profile"
//               width="14.47px"
//               height="14.4px"
//             />
//           ),
//           optionName: "View Profile",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(20));
//             dispatch(updateAccountAndSettingsActiveTab(20));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//       ],
//     },
//     {
//       option: "My Account",
//       options: [
//         sessionInfo.data?.["provider"]["isSuperProvider"] === true && {
//           icon: (
//             <MdStars
//               style={{ width: "17px", height: "19px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Super Helper Seetings",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(22));
//             dispatch(updateAccountAndSettingsActiveTab(22));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <MdChildCare
//               style={{ width: "17px", height: "19px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Childcare",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(11));
//             dispatch(updateAccountAndSettingsActiveTab(11));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <MdDryCleaning
//               style={{ width: "17px", height: "19px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Odd Jobs",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(12));
//             dispatch(updateAccountAndSettingsActiveTab(12));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <IoMdSchool
//               style={{ width: "17px", height: "19px" }}
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Tutoring",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(23));
//             dispatch(updateAccountAndSettingsActiveTab(23));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <MdSupervisedUserCircle
//               width="17px"
//               height="19px"
//               color="#6D6D6D"
//             />
//           ),
//           optionName: "Au Pair",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(13));
//             dispatch(updateAccountAndSettingsActiveTab(13));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <MdLocationCity width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Citizenship",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(15));
//             dispatch(updateAccountAndSettingsActiveTab(15));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <MdFeed width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Certificates",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(19));
//             dispatch(updateAccountAndSettingsActiveTab(19));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <MdGrading width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "References",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(17));
//             dispatch(updateAccountAndSettingsActiveTab(17));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <FaCar width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Driving & Language",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(16));
//             dispatch(updateAccountAndSettingsActiveTab(16));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//       ],
//     },
//     {
//       option: "Billing",
//       options: [
//         {
//           icon: <MdMonetizationOn width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Payment Methods",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(25));
//             dispatch(updateAccountAndSettingsActiveTab(25));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//       ],
//     },
//     {
//       option: "Settings",
//       options: [
//         {
//           icon: <FaHome width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "My Addresses",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(3));
//             dispatch(updateAccountAndSettingsActiveTab(3));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <MdCardMembership width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "MemberShip",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(24));
//             dispatch(updateAccountAndSettingsActiveTab(24));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: <IoEye width="17px" height="19px" color="#6D6D6D" />,
//           optionName: "Privacy setting",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(14));
//             dispatch(updateAccountAndSettingsActiveTab(14));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <img
//               src={settingsIcon}
//               alt="General Settings"
//               width="17px"
//               height="19px"
//             />
//           ),
//           optionName: "General Settings",
//           func: () => {
//             setShowMenu(false);
//             dispatch(updateActiveTabIndex(7));
//             dispatch(updateAccountAndSettingsActiveTab(7));
//             dispatch(updateShowAccountAndSettings(true));
//           },
//         },
//         {
//           icon: (
//             <img src={logoutIcon} alt="Log out" width="17px" height="19px" />
//           ),
//           optionName: "Log out",
//           func: () => {
//             setShowMenu(false);
//             try {
//               dispatch(removeSession());
//               dispatch(updateShowProfileActivation(false));
//             } catch (_e) {
//             } finally {
//               navigate("/");
//             }
//           },
//         },
//       ],
//     },
//   ];
//   function checkValue<T>(val: any, defaultValue: T): T {
//     if (val !== null && val !== undefined) {
//       return val;
//     }
//     return defaultValue;
//   }
//   const firstName = checkValue(sessionInfo?.data?.["firstName"], "");
//   const lastName = checkValue(sessionInfo?.data?.["lastName"], "");
//   useEffect(() => {
//     function handleClickOutside(event: MouseEvent) {
//       if (
//         menuRef.current &&
//         buttonRef.current &&
//         !menuRef.current.contains(event.target as Node) &&
//         !buttonRef.current.contains(event.target as Node)
//       ) {
//         setShowMenu(false);
//       }
//     }

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, []);
//   return (
//     <div style={{paddingTop:isMobile && "12px", top:isMobile && "0", width:isMobile && "max-content" , right:isMobile && "0"}} className={`${!isMobile ?`fixed z-3 pt-2 md:pt-4 pr-2 md:pr-6 w-full flex justify-content-end pointer-events-none` : `fixed z-3  pr-3  w-full flex justify-content-end pointer-events-none`}`}>
//       <div
//         ref={buttonRef}
//         className={`${!isMobile ? "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl shadow-3 gap-1 md:gap-2 align-items-center w-min pointer-events-auto hover:shadow-4" : "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl  gap-1 md:gap-2 align-items-center w-min pointer-events-auto"}`}
//         onClick={() => setShowMenu(!showMenu)}
//       >
//         {!isMobile && (
//                      <img
//                      // className="cursor-pointer"
//                      src={menu}
//                      alt='hamburger'
//                      height={'15px'}
//                      width={'20.69'}
//                  />
//                 )}
//         <div
//           className="border-circle p-1 md:p-2 flex justify-content-center align-items-center pointer-events-none select-none"
//           style={{
//             height: "24px",
//             width: "24px",
//             backgroundColor: "#9F9F9F",
//             fontSize: "10px",
//             fontWeight: "600",
//             color: "white",
//             overflow: "hidden",
//           }}
//         >
//           {sessionInfo.data &&
//           sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
//             <img
//               src={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
//               alt="Profile"
//               className="profilePhoto"
//               style={{
//                 height: "30.71px",
//                 width: "30.65px",
//                 borderRadius: "50%",
//                 objectFit: "contain",
//               }}
//             />
//           ) : (
//             `${firstName[0]}.${lastName[0]}`
//           )}
//         </div>
//       </div>
//       {showMenu && (
//         <div
//           ref={menuRef}
//           className="fixed bg-white shadow-3 p-2 pt-3 pb-4 overflow-y-auto overflow-x-hidden w-64 md:w-80 border-round-2xl pointer-events-auto"
//           style={{ top: "70px", maxHeight: "85vh" }}
//         >
//           <div className="flex flex-column justify-content-center align-items-center">
//             <div className="w-full flex justify-content-center align-content-center gap-2 md:gap-3 mb-2 mt-2">
//               <Avatar
//                 className={`${style.imageStyle}`}
//                 label={`${firstName.charAt(0)}.${lastName.charAt(0)}`}
//                 image={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
//                 shape="circle"
//                 size="large"
//                 style={{
//                   fontSize: "16px",
//                   fontWeight: "600",
//                   backgroundColor: "#9F9F9F",
//                   color: "#FFFFFF",
//                 }}
//               />
//               <h3
//                 className={`text-base md:text-lg font-semibold m-0 mt-2 ${style.mainOptions}`}
//               >
//                 {`${firstName} ${lastName}`}
//               </h3>
//             </div>
//             {options.map((option, index) => (
//               <div
//                 key={index}
//                 className="w-full flex flex-column mb-2 pl-3 md:pl-5"
//               >
//                 <h3
//                   className={`text-base md:text-lg font-semibold m-0 mt-2 ${style.mainOptions}`}
//                 >
//                   {option.option}
//                 </h3>
//                 <div
//                   className="pl-2 md:pl-3 mt-1 md:mt-2"
//                   style={{
//                     borderLeft: "2px solid #F1F1F1",
//                   }}
//                 >
//                   {option.options.map((value, optionIndex) => (
//                     <div
//                       key={optionIndex}
//                       className="flex justify-content-start align-items-center gap-2 md:gap-3"
//                     >
//                       <div style={{ width: "15px" }}>{value.icon}</div>
//                       <p
//                         className={`m-0 w-min cursor-pointer text-sm md:text-base ${style.noTextWrap}`}
//                         style={{
//                           paddingBlock: "4px",
//                           color: "#585858",
//                           fontWeight: "500",
//                         }}
//                         onClick={value.func}
//                       >
//                         {value.optionName}
//                       </p>
//                     </div>
//                   ))}
//                 </div>
//               </div>
//             ))}
//             {approved && (
//               <button
//                 className={`border-round-xl px-2 md:px-3 py-1 md:py-2 flex justify-content-center align-items-center gap-1 md:gap-2 cursor-pointer text-xs md:text-sm ${style.referbtn}`}
//                 style={{
//                   backgroundColor: "transparent",
//                   color: "#585858",
//                   fontWeight: "500",
//                   position: "relative",
//                 }}
//                 onClick={() => {
//                   setShowMenu(false);
//                   dispatch(updateActiveTabIndex(26));
//                   dispatch(updateAccountAndSettingsActiveTab(26));
//                   dispatch(updateShowAccountAndSettings(true));
//                 }}
//               >
//                 <img
//                   src={defaultGiftIcon}
//                   alt="Gift Icon"
//                   className={`${style.defaultIcon}`}
//                   width="14.4px"
//                   height="14.48px"
//                 />
//                 <img
//                   src={giftIcon}
//                   alt="Hover Gift Icon"
//                   className={`${style.hoverIcon}`}
//                   width="14.4px"
//                   height="14.48px"
//                 />{" "}
//                 Get $10 for each friend you refer
//               </button>
//             )}
//           </div>
//         </div>
//       )}

//       <CustomDialog
//         visible={enableAccountAndSettings}
//         onHide={() => {
//           dispatch(updateShowAccountAndSettings(false));
//         }}
//         closeClicked={() => {
//           dispatch(updateShowAccountAndSettings(false));
//         }}
//         profileCompletion={0}
//       >
//         <AccountLayout visible={enableAccountAndSettings} />
//       </CustomDialog>
//     </div>
//   );
// }

// export default HomeHeaderHelper;

import { useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import { useNavigate } from "react-router-dom";
import menu from "../../../assets/images/Icons/menu.png";
import { Avatar } from "primereact/avatar";
import logoutIcon from "../../../assets/images/Icons/logout.png";
import { removeSession } from "../../../store/slices/sessionInfoSlice";
import { updateShowProfileActivation } from "../../../store/slices/applicationSlice";
import style from "../../Common/styles/home-header.module.css";
import useIsMobile from "../../../hooks/useIsMobile";
import utils from "../../../components/utils/util";
import CookiesConstant from "../../../helper/cookiesConst";

function HomeHeaderHelper() {
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const [showMenu, setShowMenu] = useState(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const { isMobile } = useIsMobile();
  const navigate = useNavigate();

  function checkValue<T>(val: any, defaultValue: T): T {
    if (val !== null && val !== undefined) {
      return val;
    }
    return defaultValue;
  }

  const firstName = checkValue(sessionInfo?.data?.["firstName"], "");
  const lastName = checkValue(sessionInfo?.data?.["lastName"], "");

  const handleLogout = () => {
    setShowMenu(false);
    try {
      dispatch(removeSession());
      dispatch(updateShowProfileActivation(false));
      utils.obliterateEverything();
    } catch (_e) {
    } finally {
      navigate("/");
    }
  };

  return (
    <div
      style={{
        paddingTop: isMobile && "12px",
        top: isMobile && "0",
        width: isMobile && "max-content",
        right: isMobile && "0",
      }}
      className={`${
        !isMobile
          ? `fixed z-3 pt-2 md:pt-4 pr-2 md:pr-6 w-full flex justify-content-end pointer-events-none`
          : `fixed z-3 pr-3 w-full flex justify-content-end pointer-events-none`
      }`}
    >
      <div
        ref={buttonRef}
        className={`${
          !isMobile
            ? "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl shadow-3 gap-1 md:gap-2 align-items-center w-min pointer-events-auto hover:shadow-4"
            : "flex bg-white px-2 md:px-3 py-1 md:py-2 cursor-pointer border-round-xl gap-1 md:gap-2 align-items-center w-min pointer-events-auto"
        }`}
        onClick={() => setShowMenu(!showMenu)}
      >
        {!isMobile && <img src={menu} alt="hamburger" height={"15px"} width={"20.69"} />}
        <div
          className="border-circle p-1 md:p-2 flex justify-content-center align-items-center pointer-events-none select-none"
          style={{
            height: "24px",
            width: "24px",
            backgroundColor: "#9F9F9F",
            fontSize: "10px",
            fontWeight: "600",
            color: "white",
            overflow: "hidden",
          }}
        >
          {sessionInfo.data && sessionInfo.data?.["defaultImage"]?.["scale3ImageUrl"] ? (
            <img
              src={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
              alt="Profile"
              className="profilePhoto"
              style={{
                height: "30.71px",
                width: "30.65px",
                borderRadius: "50%",
                objectFit: "contain",
              }}
            />
          ) : (
            `${firstName[0]}.${lastName[0]}`
          )}
        </div>
      </div>
      {showMenu && (
        <div
          ref={menuRef}
          className="fixed bg-white shadow-3 p-2 pt-3 pb-4 overflow-y-auto overflow-x-hidden w-64 md:w-80 border-round-2xl pointer-events-auto"
          style={{ top: "70px", maxHeight: "85vh" }}
        >
          <div className="flex flex-column justify-content-center align-items-center">
            <div className="w-full flex justify-content-center align-content-center gap-2 md:gap-3 mb-2 mt-2">
              <Avatar
                className={`${style.imageStyle}`}
                label={`${firstName.charAt(0)}.${lastName.charAt(0)}`}
                image={sessionInfo.data["defaultImage"]["scale3ImageUrl"]}
                shape="circle"
                size="large"
                style={{
                  fontSize: "16px",
                  fontWeight: "600",
                  backgroundColor: "#9F9F9F",
                  color: "#FFFFFF",
                }}
              />
              <h3 className={`text-base md:text-lg font-semibold m-0 mt-2 ${style.mainOptions}`}>{`${firstName} ${lastName}`}</h3>
            </div>
            <div className="w-full flex flex-column mb-2 pl-3 md:pl-5">
              <div className="flex justify-content-start align-items-center gap-2 md:gap-3 cursor-pointer" onClick={handleLogout}>
                <div style={{ width: "15px" }}>
                  <img src={logoutIcon} alt="Log out" width="17px" height="19px" />
                </div>
                <p
                  className={`m-0 w-min text-sm md:text-base ${style.noTextWrap}`}
                  style={{
                    paddingBlock: "4px",
                    color: "#585858",
                    fontWeight: "500",
                  }}
                >
                  Log out
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HomeHeaderHelper;
