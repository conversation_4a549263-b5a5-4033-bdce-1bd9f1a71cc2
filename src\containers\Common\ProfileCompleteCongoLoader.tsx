import React, { useState } from 'react';
import Congratulations from './Congratulations';
import CustomDialog from './CustomDialog';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../store';
import { updateProfileActivationEnabled, updateShowProfileActivation } from '../../store/slices/applicationSlice';

interface ProfileCompleteCongoLoaderProps {}

function ProfileCompleteCongoLoader({}: ProfileCompleteCongoLoaderProps) {
    const [isVisible, setIsVisible] = useState<boolean>(true);
    const dispatch = useDispatch<AppDispatch>();

    // Function to close the dialog
    const handleCloseDialog = () => {
        setIsVisible(false);
        dispatch(updateShowProfileActivation(false));
         dispatch(updateProfileActivationEnabled(false));
    };

    return (
        <React.Fragment>
            <CustomDialog
                visible={isVisible}
                onHide={handleCloseDialog} // Pass the close function
                closeClicked={handleCloseDialog} // Handle close on button/icon click
                profileCompletion={100}
            >
                <Congratulations onHide={handleCloseDialog} /> {/* Pass onHide prop */}
            </CustomDialog>
        </React.Fragment>
    );
}

export default ProfileCompleteCongoLoader;
