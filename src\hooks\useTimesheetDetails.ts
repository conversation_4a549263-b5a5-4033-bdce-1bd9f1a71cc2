import { useState } from 'react';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';

interface TimesheetDetails {
  id?: number;
  firstName?: string;
  lastName?: string;
  originalImageUrl?: string;
  jobType?: string;
  jobDate?: string;
  formattedAddress?: string;
  price?: number;
  overtimeRate?: number;
  jobEndTime?: string;
  jobStartTime?: any;
  status?: string;
  estimatedJobValue?: number;
  estimatedJobHours?: number;
  timesheetId: number;
  jobId: number;
  applicantId: number;
  userId: number;
  weeklyScheduleEntries?: TimesheetRow[]; // Add the extracted timesheet rows
}

interface TimesheetRow {
  start: string;
  finish: string;
  hours: number;
  rate: number;
  total: number;
  dayOfWeek: number;
  isRequired: boolean;
  id: number;
  isOriginal?: boolean;
  editVersion?: number;
}

interface TimesheetApiResponse {
  id?: number;
  firstName?: string;
  lastName?: string;
  originalImageUrl?: string;
  jobType?: number;
  jobDate?: string;
  formattedAddress?: string;
  price?: number;
  overtimeRate?: number;
  jobEndTime?: string;
  jobStartTime?: string;
  status?: string;
  estimatedJobValue?: number;
  estimatedJobHours?: number;
  timeSheetId?: number;
  timesheetId?: number;
  jobId?: number;
  applicantId?: number;
  userId?: number;
  weeklyScheduleList?: Array<{
    weeklyScheduleEntries: Array<{
      id: number;
      dayOfWeek: number;
      jobStartTime: string;
      jobEndTime: string;
      price: number;
      isRequired: boolean;
      applicantId: number;
    }>;
  }>;
}

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

const calculateHoursFromTimes = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const parseTime = (timeStr: string): number => {
    const [hours, minutes, seconds] = timeStr.split(":").map(Number);
    return hours + (minutes || 0) / 60 + (seconds || 0) / 3600;
  };

  const startHours = parseTime(startTime);
  const endHours = parseTime(endTime);

  return Math.max(0, endHours - startHours);
};

const formatTime = (time: string): string => {
  if (!time) return '';
  const [hours, minutes] = time.split(":").map(Number);
  const period = hours >= 12 ? "pm" : "am";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};

export const useTimesheetDetails = () => {
  const [timesheetDetails, setTimesheetDetails] = useState<TimesheetDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  const fetchTimesheetDetails = async (timesheetId: number): Promise<void> => {
    console.log("Fetching timesheet details for ID:", timesheetId);
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getTimeSheetDetails(
          (response: any) => {
            console.log("Raw Timesheet API Response getTimeSheetDetails:", response);
            
            // Enhanced data extraction with better error handling
            let actualData: TimesheetApiResponse;
            if (Array.isArray(response)) {
              actualData = response[0];
              console.log("Response is array, using first element:", actualData);
            } else if (response?.data) {
              actualData = response.data;
              console.log("Response has data property:", actualData);
            } else {
              actualData = response;
              console.log("Using response directly:", actualData);
            }
            
            // Log all properties of actualData for debugging
            console.log('actualData getTimeSheetDetails - Full Object:', actualData);
            console.log('actualData keys:', actualData ? Object.keys(actualData) : 'actualData is null/undefined');

            if (!actualData) {
              const errorMsg = "No actual data found in response";
              console.error(errorMsg);
              setError(errorMsg);
              reject(new Error(errorMsg));
              return;
            }

            // Extract weekly schedule entries for multiple timesheet rows
            let weeklyScheduleEntries: TimesheetRow[] = [];
            if (actualData.weeklyScheduleList && Array.isArray(actualData.weeklyScheduleList)) {
              const weeklySchedule = actualData.weeklyScheduleList[0]; // Get first schedule
              if (weeklySchedule && weeklySchedule.weeklyScheduleEntries) {
                weeklyScheduleEntries = weeklySchedule.weeklyScheduleEntries.map((entry: any, index: number) => {
                  const startTime = formatTime(entry.jobStartTime || '06:00:00');
                  const endTime = formatTime(entry.jobEndTime || '18:00:00');
                  const hours = calculateHoursFromTimes(entry.jobStartTime, entry.jobEndTime);
                  const rate = entry.price || actualData.price || 60;

                  return {
                    start: startTime,
                    finish: endTime,
                    hours: hours,
                    rate: rate,
                    total: hours * rate,
                    dayOfWeek: entry.dayOfWeek,
                    isRequired: entry.isRequired,
                    id: entry.id || index + 1,
                    isOriginal: false,
                    editVersion: 0
                  };
                });
              }
            }

            console.log('Extracted weeklyScheduleEntries:', weeklyScheduleEntries);

            const details: TimesheetDetails = {
              id: actualData.id,
              firstName: actualData.firstName,
              lastName: `${actualData.lastName?.charAt(0) || ''}`,
              originalImageUrl: actualData.originalImageUrl,
              jobType: jobTypeMap[actualData.jobType || 0] || actualData.jobType?.toString() || 'Unknown',
              jobDate: actualData.jobDate,
              formattedAddress: actualData.formattedAddress,
              price: actualData.price,
              overtimeRate: actualData.overtimeRate,
              jobStartTime: actualData.jobStartTime ? formatTime(actualData.jobStartTime) : '',
              jobEndTime: actualData.jobEndTime ? formatTime(actualData.jobEndTime) : '',
              status: actualData.status,
              estimatedJobValue: actualData.estimatedJobValue,
              estimatedJobHours: actualData.estimatedJobHours,
              timesheetId: actualData.timeSheetId || actualData.timesheetId || timesheetId,
              jobId: actualData.jobId || 0,
              applicantId: actualData.applicantId || 0,
              userId: actualData.userId || 0,
              weeklyScheduleEntries: weeklyScheduleEntries, // Add the extracted timesheet rows
            };

            console.log("Parsed Timesheet Details getTimeSheetDetails:", details);
            console.log("Details JSON stringified:", JSON.stringify(details, null, 2));
            setTimesheetDetails(details);
            resolve();
          },
          (error: any) => {
            console.error("Error fetching timesheet details:", error);
            setError(error?.message || 'Failed to fetch timesheet details');
            reject(error);
          },
          timesheetId
        );
      });
    } catch (err) {
      console.error('Fetch timesheet details failed:', err);
    } finally {
      setIsLoading(false);
      disableLoader();
    }
  };

  const clearTimesheetDetails = () => {
    setTimesheetDetails(null);
    setError(null);
  };

  return {
    timesheetDetails,
    isLoading,
    error,
    fetchTimesheetDetails,
    clearTimesheetDetails
  };
};

export type { TimesheetDetails };
