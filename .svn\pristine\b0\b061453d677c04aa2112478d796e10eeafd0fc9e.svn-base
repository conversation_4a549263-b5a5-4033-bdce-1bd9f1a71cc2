/* FamilyIntro.module.css */

.familycontainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    justify-content: space-between;
    padding: 40px;
}

.familyheader {

}

.familyheader h1{
    width: 325px;
    height: 48px;
    font-weight: 700 ;
    font-size: 32px;
    color: #585858;
    line-height: 48px;   
}
.familyprogressbar{
    width: 487px; 
    max-width: 100%; 
    height: 7px; 
    margin-top: 1rem;
}

.familyprogressbar > div {
    background-color: #179d52 !important; 
}
.familyprogressbar > div > div {
    display: none; 
}
.familycontent {
    width: 487px; 
    max-width: 100%;

}
.familycontent p{
    font-weight: 600;
    color: #585858;
    font-size: 16px;
    line-height: 24px;
}
.familyinstruction{
    font-weight: 400 !important;
    color: #585858 !important;
    font-size: 16px !important;
    line-height: 24px !important;
}
.inputfamilyinstructions
{
    width: 487px; 
    max-width: 100%;
    height: 114px;
    background-color: #F0F4F7;
    border-radius: 10px;
    border: 1px solid #F0F4F7;
  
}
.familyline{
    text-align: center;
    width: 487px; 
    max-width: 100% ;
    font-weight: 400 !important;
    font-size: 14px !important;
    line-height: 21px !important;
    color: black !important;
}
.footerContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 55px;
    width: 100%;
}
.inputTextareafamily{
    width: 477px;
    max-width: 100%;
    height: 100%;
    border: none;
    border-color: unset;
    box-shadow: none;
    border-radius: 10px;
    font-weight: 300;
    font-size: 12px;
}


.inputTextareafamily:enabled:focus{
    width: 477px;
    max-width: 100%;
    height: 100%;

    border:2px solid #179d52 !important;
    box-shadow: none;
    font-weight: 300;
    font-size: 12px;
    border-radius: 10px;
    font-weight: 300;
    font-size: 12px;
}
.inputTextareafamily:enabled:hover{
    width: 477px;
    max-width: 100%;
    height: 100%;
    font-weight: 300;
    font-size: 12px;
    border:2px solid #179d52 !important;
    box-shadow: none;
    border-radius: 10px;
}
.inputTextareafamily::placeholder{
    font-weight: 300;
    font-size: 12px;
    line-height: 14px;
}
.familyfooter {
  
}
.hoverClass[data-skip=true]:hover{
    box-shadow: 0 5px 4px 0 rgba(241, 241, 241, 1);
    font-weight: 800 !important;
    text-shadow: 0 5px rgba(241, 241, 241, 1) !important;
}
.familycontainerMobile {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    padding-inline: 15px;
    margin-top: 84px;
    
}
.footerContainerMobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0px 0px 8px 0px #00000040;

}
