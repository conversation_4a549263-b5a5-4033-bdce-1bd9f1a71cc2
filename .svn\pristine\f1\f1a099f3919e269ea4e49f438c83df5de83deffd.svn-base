.headerH1 {
  font-size: 30px;
  font-weight: 700;
  color: #585858;
}

.spanSelect {
  font-size: 30px;
  font-weight: 700;
  color: #ffa500;
  margin-right: 10px;
}

.spanFor {
  font-size: 30px;
  font-weight: 300;
  color: #585858;
  margin-right: 10px;
}

.selectTopHelperBtn {
  width: 191px;
  height: 46px;
  border-radius: 10px;
  background-color: #ffa500;
  border: none;
  color: #ffffff;
  text-wrap: nowrap;
  align-items: center;
  display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.selectTopHelperBtn:hover {
  box-shadow: 0px 4px 4px 0px #00000040;
}

.searchInput {
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  padding-left: 26px;
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
  color: #585858;
}

.searchAllFilters {
  font-size: 14px;
  font-weight: 500;
  color: #585858;
  border-radius: 10px;
  padding-left: 26px;

  /* background-color: #DFDFDF; */
  border: 1px solid #dfdfdf;
  text-wrap: nowrap;
  display: flex;
  align-items: center;
}

/* .helpersMeetCriteria {
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  width: 1052px;
  height: auto;
  max-height: 555px;
  overflow-y: auto;
} */

.helpersMeetCriteria {
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  width: auto;
  height: auto;
  /* max-height: 700px; */
  /* overflow-y: auto; */
}
.criteriaCard {
  border: 2px solid #dfdfdf;
  border-radius: 10px;
  height: 88px;
  width: 247px;
  display: flex;
  justify-content: space-between;
}

.criteriaCard:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  /* border: 2px solid #585858; */
}

.criteriaCardHovered {
  border: 1px solid #dfdfdf;
}

.criteriaCardHovered:hover {
  border: 2px solid #585858;
}

.criteriaCardSelected {
  border: 2px solid #179d52;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.criteriaCardUnselect {
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  height: 88px;
  width: 247px;
  display: flex;
  justify-content: space-between;
}

.criteriaCardUnselect:hover {
  border: 2px solid #585858;
  border-radius: 10px;
  height: 116px;
  width: 435px;
}

.profileImage {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  /* margin-right: 16px; */
}

.info {
  flex: 1;
  /* margin-right: 46px;
  margin-left: 16px; */
}

.selectedCheck {
  position: absolute;
  top: 5px;
  right: -64px;
  width: 14px;
  height: 14px;
  background-color: #179d52;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 90%;
}

.selectedCheck span {
  color: #fff;
  font-size: 10px;
}

.name {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 4px;
  color: #585858;
}

.location {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 600;
  max-width: 165px;
  text-wrap: wrap;
}

.jobs {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 300;
}

.selectButton {
  /* padding: 8px 16px; */
  font-size: 8px;
  font-weight: 600;
  /* border: 2px solid #179d52; */
  border-radius: 10px;
  background-color: #ffa500;
  color: #ffffff;
  cursor: pointer;
  border: none;
  height: 20px;
  width: 62px;
}

.unSelecttButton {
  /* padding: 8px 16px; */
  font-size: 8px;
  font-weight: 600;
  border: 2px solid #179d52;
  border-radius: 10px;
  background-color: #179d52;
  color: #ffffff;
  cursor: pointer;
  border: none;
  height: 20px;
  width: 62px;
}

.candidatesearchMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  user-select: none;
  flex-grow: 1;
  overflow: hidden;
  overflow-y: scroll;
  padding: 15px;
  padding-bottom: 0;
}

.countsDiv {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  /* Background color for the footer */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  /* Optional shadow for better visibility */
  text-align: center;
  z-index: 999;
  /* Ensure it stays on top of other elements */
  margin-top: auto;
  display: flex;
  flex-direction: row;
}

.unselectDiv {
  background-color: #DFDFDF;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 61px;
}

.selectDiv {
  background-color: #179D52;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 61px;
}

.headerH1Mobile {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  margin-top: 0px;
}

.selectTopHelperBtnMobile {
  width: 158px;
  height: 41px;
  border-radius: 10px;
  background-color: transparent;
  border: 1px solid #DFDFDF;
  color: #585858;
  text-wrap: nowrap;
  align-items: center;
  display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;

}

.unselectTopHelperBtnMobile {
  width: 158px;
  height: 41px;
  background-color: #ffa500;
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-decoration: underline;
  box-shadow: 0px 4px 4px 0px #00000040;

}

/* .helpersMeetCriteriaMobile {
  border-radius: 10px;
  width: 100%;
  height: auto;
  max-height: 460px;
  overflow-y: auto;

} */

.helpersMeetCriteriaMobile {
  border-radius: 10px;
  width: 100%;
  height: auto;
  max-height: 460px;
  overflow-y: auto;
  display: flex; /* Add Flexbox for card layout */
  flex-wrap: wrap; /* Allow cards to wrap */
  gap: 1rem; /* Spacing between cards */
  /* padding: 1rem; Optional: Add padding for better spacing */
}

.criteriaCardMobile {
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  height: 85px;
  width: 100%; /* Full width for mobile */
  display: flex;
  justify-content: space-between;
  flex: 1 1 100%; /* Single card per row on mobile */
  max-width: 100%; /* Ensure it takes full width */
}

/* Two cards per row for tablet (e.g., >= 768px) */
@media (min-width: 768px) {
  .criteriaCardMobile {
    flex: 1 1 calc(50% - 1rem); /* Two cards per row, accounting for gap */
    max-width: calc(50% - 1rem); /* Limit width for two cards */
    width: calc(50% - 1rem); /* Ensure consistent width */
  }
}

/* Three cards per row for desktop (e.g., >= 1024px) */
@media (min-width: 1024px) {
  .criteriaCardMobile {
    flex: 1 1 calc(33.33% - 1rem); /* Three cards per row, accounting for gap */
    max-width: calc(33.33% - 1rem); /* Limit width for three cards */
    width: calc(33.33% - 1rem); /* Ensure consistent width */
  }
}

.criteriaCardMobile {
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  height: 85px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.nameMobile {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 4px;
  color: #585858;
}

.locationMobile {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 600;
  max-width: 165px;
  text-wrap: wrap;
}

.jobsMobile {
  font-size: 0.9rem;
  color: #585858;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 300;
}

.infoMobile {
  margin-left: 10px;
}

.selectButtonMobile {
  border: 1px solid #585858;
  border-radius: 20px;
  background-color: #DFDFDF;
  color: #585858;
  font-size: 12px;
  font-weight: 700;
  padding-inline: 15px;
  padding-block: 5px;
}

.unSelecttButtonMobile {
  border: 1px solid #179D52;
  border-radius: 20px;
  background-color: #179D52;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  padding-inline: 10px;
  padding-block: 5px;
}

.searchAllFiltersMobile {
  font-size: 12px;
  font-weight: 400;
  color: #585858;
  border-radius: 10px;
  padding-left: 24px;
  border: 1px solid #dfdfdf;
  text-wrap: nowrap;
  display: flex;
  align-items: center;
}

.searchInputMobile {
  border-radius: 10px;
  border: 1px solid #dfdfdf;
  padding-left: 26px;
  font-size: 12px;
  font-weight: 500;
  color: #585858;
}

.footerButtonArrow {
  width: min-content;
  position: fixed;
  top: 32px;
  left: 35px;
  width: 35px;
  height: 25px;
}