.DialogContent {
  width: 610px;
  height: min-content;
  background-color: #fff;
  border-radius: 20px;
  border: 1px solid #f0f4f7;
}
.detailsTitle {
  font-size: 32px;
  font-weight: 300;
  color: #585858;
  margin: 0px;
}
.detailsPara {
  font-size: 16px;
  font-weight: 300;
  color: #585858;
  margin: 0px;
}
.closeBtn {
  position: absolute;
  right: 3px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0px 4px 4px 0px #00000040;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -9px;
  cursor: pointer;
}
.DialogContentMobile {
  height: min-content;
  background-color: #fff;
  border-radius: 30px;
  border: 1px solid #179D52;
  margin-inline: 15px;
}
.detailsTitleMobile {
  font-size: 20px;
  font-weight: 700;
  color: #585858;
  margin: 0px;
  text-align: center;
}
.copyBtn{
  background-color: transparent !important;
  border-radius: 20px;
  border: 1px solid #000000 !important;
  box-shadow: none !important;
  color: #585858 !important;
}
