import React, { useEffect, useRef, useState } from 'react';
import styles from '../../styles/jobawared-card.module.css';
import profile from '../../../../assets/images/sample_profile.png';
import { Rating } from 'primereact/rating';
import filledStar from '../../../../assets/images/Icons/filled-star.png';
import unfilled from '../../../../assets/images/Icons/unfiled.png';
import chevronRight from '../../../../assets/images/Icons/chevron-right.png';
import { Divider } from 'primereact/divider';
import useIsMobile from '../../../../hooks/useIsMobile';

interface JobAdCardProps {
    image: string;
    name: string;
    location: string;
    statusShow?: string;
    rating: number;
    ratingCount: number;
    onContactNumber: () => void;
    onBankDetails: () => void;
    onTransport: () => void;
    onChatClick: () => void;
    showBank: boolean;
    showTransport: boolean;
    isTutorRecuring?:boolean;
    hasbeenRated?:boolean;
    onRateClick?: () => void;
    // onCancelJobClick: () => void;
    // onDuplicateJobClick: () => void;
}

const JobAwardedCard: React.FC<JobAdCardProps> = ({
    image,
    name,
    rating,
    ratingCount,
    location,
    showBank,
    showTransport,
    onContactNumber,
    onBankDetails,
    onTransport,
    onChatClick,
    onRateClick,
    isTutorRecuring,
    hasbeenRated,
    statusShow
    // onCancelJobClick,
    // onDuplicateJobClick,
}) => {
    const { isMobile } = useIsMobile();
    return !isMobile ? (
        <div className={`${styles.mainDiv} mt-4`}>
            <div className={styles.container}>
                <div className={styles.greenDiv}></div>
                <div className={styles.left}>
                    <button className={styles.awardedBtn}>{statusShow}</button>
                    <img src={image} alt='image' className={styles.image} />
                </div>
                <div className={styles.right}>
                    <div className={styles.nameRating}>
                        <h3 className={styles.name}>{name}</h3>
                        <div>
                            <p
                                style={{
                                    margin: '0px',
                                    fontSize: '14px',
                                    fontWeight: '300',
                                    color: '#585858',
                                }}
                            >
                                {location}
                            </p>
                        </div>
                        <div className={styles.rating}>
                            <Rating
                                readOnly
                                value={rating}
                                cancel={false}
                                className={styles.ratingsStar}
                                stars={5}
                                onIcon={
                                    <img
                                        src={filledStar}
                                        alt='rating on'
                                        width='18.27px'
                                        height='18.66px'
                                    />
                                }
                                offIcon={
                                    <img
                                        src={unfilled}
                                        alt='rating off'
                                        width='18.27px'
                                        height='18.66px'
                                    />
                                }
                            />
                            <p
                                style={{
                                    margin: '0px',
                                    fontSize: '14px',
                                    fontWeight: '300',
                                    color: '#585858',
                                }}
                            >
                                {`(${ratingCount} rating)`}
                            </p>
                        </div>
                    </div>
                    <div className={isTutorRecuring ? `${'flex flex-row gap-2'}` : ``}>
                        <button onClick={onChatClick} className={styles.chatBtn}>
                            Chat
                        </button>
                        {isTutorRecuring && !hasbeenRated ? (
                              <button onClick={onRateClick} className={styles.chatBtn}>
                              Rate
                          </button>
                        ) : (
                            <>
                            {isTutorRecuring && (
                              <div style={{ 
                                 
                                 backgroundColor: "#fff",
                                 color: "#179D52",
                                 border: "1px solid #179D52",
                                 padding: "10px 10px",
                                 borderRadius: "10px",
                                 fontWeight:"500",
                                 fontSize: "12px",
                                 width: "109px",}}
                                 >
                                Already Rated     
                             </div>
                            )}
                            </>
                        )}
                              
                    </div>
                    <Divider layout='vertical' />

                    <div className={styles.infoDiv}>
                        <button onClick={onContactNumber} className={styles.contactBtn}>
                            Contact Number
                            <img src={chevronRight} alt='chevronRight' width='8px' height='12px' />
                        </button>
                        {showBank && (
                            <button onClick={onBankDetails} className={styles.bankBtn}>
                                Bank Details
                                <img
                                    src={chevronRight}
                                    alt='chevronRight'
                                    width='8px'
                                    height='12px'
                                />
                            </button>
                        )}
                        {showTransport && (
                            <button onClick={onTransport} className={styles.transportBtn}>
                                Transport
                                <img
                                    src={chevronRight}
                                    alt='chevronRight'
                                    width='8px'
                                    height='12px'
                                />
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    ) : (
        <div className={`${styles.mainDiv} mt-2`}>
            <div className={styles.containerMoble}>
                <div className='flex flex-column w-full'>
                    <div className='flex flex-row justify-content-between align-items-center'>
                    <div className='flex flex-row '>
                        <div className={styles.left}>
                            <img src={image} alt='image' className={styles.imageMobile} />
                        </div>
                        <div className={styles.rightMobile}>
                            <div className={styles.nameRatingMobile}>
                                <h3 className={styles.name}>{name}</h3>
                                <div>
                                    <p
                                        style={{
                                            margin: '0px',
                                            fontSize: '14px',
                                            fontWeight: '300',
                                            color: '#585858',
                                        }}
                                    >
                                        {location}
                                    </p>
                                </div>
                                <div className={styles.ratingMobile}>
                                    <Rating
                                        readOnly
                                        value={rating}
                                        cancel={false}
                                        className={styles.ratingsStar}
                                        stars={5}
                                        onIcon={
                                            <img
                                                src={filledStar}
                                                alt='rating on'
                                                width='14.27px'
                                                height='15.66px'
                                            />
                                        }
                                        offIcon={
                                            <img
                                                src={unfilled}
                                                alt='rating off'
                                                width='14.27px'
                                                height='15.66px'
                                            />
                                        }
                                    />
                                    {/* <p
                                        style={{
                                            margin: '0px',
                                            fontSize: '10px',
                                            fontWeight: '300',
                                            color: '#585858',
                                        }}
                                    >
                                        {`(${ratingCount} rating)`}
                                    </p> */}
                                </div>
                            </div>
                          
                        </div>
                    </div>
                    <div className='ml-auto flex flex-column gap-2'>
                                <button  style={{paddingInline:"18px"}}onClick={onChatClick} className={styles.chatBtnMobile}>
                                    Chat
                                </button>
                                {isTutorRecuring && !hasbeenRated ? (
                              <button style={{paddingInline:"19px"}} onClick={onRateClick} className={styles.chatBtnMobile}>
                              Rate
                          </button>
                        ) : (
                           <>
                           {isTutorRecuring && (
                             <div style={{ 
                                
                                backgroundColor: "#fff",
                                color: "#179D52",
                                border: "1px solid #179D52",
                                paddingInline:"10px",
                                paddingBlock:"5px",
                                borderRadius: "20px",
                                fontWeight:"500",
                                fontSize: "12px",
                             }}
                                >
                               Already Rated     
                            </div>
                           )}
                           </>
                        )}
                </div>        
                </div>              
                    <div className={styles.infoDivMobile}>
                        <button onClick={onContactNumber} className={styles.contactBtnMobile}>
                            Contact Number
                            <img src={chevronRight} alt='chevronRight' width='8px' height='12px' />
                        </button>
                        {showBank && (
                            <button onClick={onBankDetails} className={styles.bankBtnMobile}>
                                Bank Details
                                <img
                                    src={chevronRight}
                                    alt='chevronRight'
                                    width='8px'
                                    height='12px'
                                />
                            </button>
                        )}
                        {showTransport && (
                            <button onClick={onTransport} className={styles.transportBtnMobile}>
                                Transport
                                <img
                                    src={chevronRight}
                                    alt='chevronRight'
                                    width='8px'
                                    height='12px'
                                />
                            </button>
                        )}
                    </div>
                    
                </div>

            </div>
            
        </div>
    );
};

export default JobAwardedCard;
