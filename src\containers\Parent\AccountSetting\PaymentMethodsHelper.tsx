import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../store';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import styles from "../../Helper/styles/utils-profile-completness-wizard.module.css";
import { InputText } from 'primereact/inputtext';
import myfamilystyles from "../styles/my-family.module.css";
import CustomButton from '../../../commonComponents/CustomButton';
import c from '../../../helper/juggleStreetConstants';
import useIsMobile from '../../../hooks/useIsMobile';
interface FormData {
    bankAccountName: string;
    bsbNumber: string;
    bankAccountNumber: string;
    bankAccountNameErrorText?: string;
    bsbNumberErrorText?: string;
    bankAccountNumberErrorText?: string;
}
export const PaymentMethods = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const { disableLoader, enableLoader } = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const {isMobile}=useIsMobile();
    const [_, setHasChanges] = useState<boolean>(false);
    const [formData, setFormData] = useState<FormData>({
        bankAccountName: '',
        bsbNumber: '',
        bankAccountNumber: '',
        bankAccountNameErrorText: '',
        bsbNumberErrorText: '',
        bankAccountNumberErrorText: ''
    });
    useEffect(() => {
        if (sessionInfo?.data['paymentInfo']) {
            const { bankAccountName, bankAccountBsb, bankAccountNumber } = sessionInfo.data['paymentInfo'];
            setFormData({
                bankAccountName: bankAccountName || '',
                bsbNumber: bankAccountBsb || '',
                bankAccountNumber: bankAccountNumber || '',
                bankAccountNameErrorText: '',
                bsbNumberErrorText: '',
                bankAccountNumberErrorText: ''
            });
        }
    }, [sessionInfo]);
    const isFormValid = () => {
        return (
            formData.bankAccountName.trim() !== "" &&
            formData.bsbNumber.trim() !== "" &&
            formData.bankAccountNumber.trim() !== ""
        );
    };
    const validateBSBNumber = (value: string) => {
        if (sessionInfo.data['country'] === 'au') {
            return value.length === 6 || value.length === 7;
        }
        return true;
    };

    const handleInputChange = (field: string, value: string) => {
        let errorText = '';
        if (field === 'bankAccountNumber' && value.length < 5) {
            errorText = 'At least 5 characters long';
        } else if (field === 'bsbNumber' && !validateBSBNumber(value)) {
            errorText = '6 or 7 digit BSB';
        }

        setFormData((prev) => ({
            ...prev,
            [field]: value,
            [`${field}ErrorText`]: errorText
        }));
        setHasChanges(true);
    };
    const paymentMethods = ['Cash', 'Online Bank Transfer'];
    // const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string[]>([]);

    // const handlePaymentMethodChange = (method: string) => {
    //     setSelectedPaymentMethod(method);
    //     setHasChanges(true);
    // };
    const handlePaymentMethodChange = (method: string) => {
        setSelectedPaymentMethod((prev) =>
            prev.includes(method) ? prev.filter((m) => m !== method) : [...prev, method]
        );
        setHasChanges(true);
    };
    useEffect(() => {
        if (sessionInfo) {
            const paymentMethod = sessionInfo.data['paymentInfo'].interestedInCashPayments
                ? 'Cash'
                : 'Online Bank Transfer';
            setSelectedPaymentMethod([paymentMethod]);
        }
    }, [sessionInfo]);
    useEffect(() => {
        if (sessionInfo.data?.['paymentInfo']?.interestedInBankTransfers) {
            setSelectedPaymentMethod(['Online Bank Transfer']);
        }
    }, [sessionInfo]);

    const handleSave = () => {
        let hasError = false;
        formData.bankAccountNameErrorText = '';
        formData.bsbNumberErrorText = '';
        formData.bankAccountNumberErrorText = '';

        if (selectedPaymentMethod.includes('Online Bank Transfer')) {
            if (!formData.bankAccountName) {
                formData.bankAccountNameErrorText = 'Bank Account Name is required';
                hasError = true;
            }

            if (!formData.bsbNumber) {
                formData.bsbNumberErrorText = 'BSB Number is required';
                hasError = true;
            } else if (!/^\d{6}$/.test(formData.bsbNumber)) {
                formData.bsbNumberErrorText = 'BSB Number must be exactly 6 digits';
                hasError = true;
            }

            if (!formData.bankAccountNumber) {
                formData.bankAccountNumberErrorText = 'Bank Account Number is required';
                hasError = true;
            } else if (formData.bankAccountNumber.length < 5) {
                formData.bankAccountNumberErrorText = 'Bank Account Number must be at least 5 digits';
                hasError = true;
            }
        }

        if (hasError) {
            // Prevent further processing if there are validation errors
            return;
        }

        const payload = {
            ...sessionInfo.data,
            paymentInfo: {
                interestedInCashPayments: selectedPaymentMethod.includes('Cash'),
                interestedInBankTransfers: selectedPaymentMethod.includes('Online Bank Transfer'),
                bankAccountName: formData.bankAccountName,
                bankAccountBsb: formData.bsbNumber,
                bankAccountNumber: formData.bankAccountNumber,
            },
        };

        enableLoader();
        dispatch(updateSessionInfo({ payload })).finally(() => {
            disableLoader();
        });
    };

    const getBankAccountVerificationText = () => {
        const { bankAccountVerificationStatus, bankAccountVerificationMessage } = sessionInfo.data['paymentInfo'];

        if (bankAccountVerificationStatus === c.bankAccountVerificationStatus.unspecified) return '';

        let statusText = '';
        switch (bankAccountVerificationStatus) {
            case c.bankAccountVerificationStatus.pending:
                statusText = 'Pending';
                break;
            case c.bankAccountVerificationStatus.approved:
                statusText = 'Approved';
                break;
            case c.bankAccountVerificationStatus.rejected:
                statusText = 'Invalid';
                break;
            case c.bankAccountVerificationStatus.inProgress:
                statusText = 'In Progress';
                break;
            default:
                statusText = 'Unspecified';
                break;
        }

        return `Verification Status: ${statusText}${bankAccountVerificationMessage ? ` ( ${bankAccountVerificationMessage} )` : ''}`;
    };

    return (
        <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }} className={styles.utilcontainerhelper}>
            <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
                <header className={styles.utilheader}>
                    <h1  style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Payment Methods</h1>
                </header>
                <CustomButton
                    label={"Save"}
                    className={`${myfamilystyles.customButton}`}
                    disabled={!isFormValid()}
                    style={{
                        margin: "0", width: "150px",
                        cursor: isFormValid() ? "pointer" : "not-allowed",
                    }}
                    onClick={handleSave}
                />
            </div>
            <PaymentMethodSelection
                paymentMethods={paymentMethods}
                selectedPaymentMethod={selectedPaymentMethod}
                handlePaymentMethodChange={handlePaymentMethodChange}
                formData={formData}
                handleInputChange={handleInputChange}
            />
            {selectedPaymentMethod.includes('Online Bank Transfer') && (
                <div>
                    <p style={{ color: '#585858' }}>{getBankAccountVerificationText()}</p>
                </div>
            )}
        </div>
    )
}
const PaymentMethodSelection = ({ paymentMethods, selectedPaymentMethod, handlePaymentMethodChange, formData, handleInputChange }) => {
    const {isMobile}=useIsMobile()
    return (
        <>
            <div>
                <h1 className='m-0 p-0 txt-clr  line-height-3' style={{ fontSize: "18px" , fontWeight:isMobile ? "300" : "500"}}>
                    Parents can chose to pay by cash or online bank transfer.
                </h1>
            </div>
            <div>
                <h1 className='m-0 p-0 txt-clr  line-height-3' style={{ fontSize: "18px" , fontWeight:isMobile ? "300" : "500"}}>
                    Businesses only pay by online bank transfer.
                </h1>
            </div>
            <div>
                <h1 className='m-0 p-0 txt-clr  line-height-3' style={{ fontSize: "18px", fontWeight:isMobile ? "300" : "500" }}>
                    You will be paid directly at the end of each job, Juggle Street does not take a percentage.
                </h1>
            </div>
            <div>
                <h1 className='p-0 txt-clr font-bold line-height-5' style={{ fontSize: "20px" , marginTop:!isMobile ? "0px" : "10px"}}>Select your payment methods below:</h1>
            </div>
            {paymentMethods?.map((method) => (
                <div key={method} className='flex items-center txt-clr font-semibold'>
                    <input
                        type="checkbox"
                        id={method}
                        checked={selectedPaymentMethod.includes(method)}
                        // checked={selectedPaymentMethod === method}
                        onChange={() => handlePaymentMethodChange(method)}
                        className={`${styles.customCheckbox}`}
                        style={{
                            fontSize: '18px',
                            cursor: method === 'Cash' ? 'not-allowed' : 'pointer'
                        }}
                        disabled={method === 'Cash'}
                    />
                    <label
                        htmlFor={method}
                        className='cursor-pointer'
                        style={{ cursor: method === 'Cash' ? 'not-allowed' : 'pointer' }}
                    >
                        {method}
                    </label>
                </div>
            ))}
            {selectedPaymentMethod.includes('Online Bank Transfer') && (
                <div>
                    <h2 className='m-0 mt-2 p-0 txt-clr font-bold' style={{ fontSize: '18px' }}>Bank Account Details</h2>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bankAccountName"
                            type="text"
                            placeholder=" "
                            value={formData.bankAccountName}
                            onChange={(e) => handleInputChange("bankAccountName", e.target.value.replace(/[^a-zA-Z\s]/g, ""))}
                            className={`input-placeholder ${formData.bankAccountNameErrorText ? "border-red" : formData.bankAccountName ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bankAccountName"
                            className={`label-name ${formData.bankAccountName || formData.bankAccountNameErrorText ? "label-float" : ""} ${formData.bankAccountNameErrorText ? "input-error" : ""}`}
                        >
                            {formData.bankAccountNameErrorText && !formData.bankAccountName ? formData.bankAccountNameErrorText : "Bank Account Name*"}
                        </label>
                    </div>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bsbNumber"
                            type="text"
                            placeholder=" "
                            value={formData.bsbNumber}
                            onChange={(e) => handleInputChange("bsbNumber", e.target.value.replace(/\D/g, ""))}
                            className={`input-placeholder ${formData.bsbNumberErrorText ? "border-red" : formData.bsbNumber ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bsbNumber"
                            className={`label-name ${formData.bsbNumber || formData.bsbNumberErrorText ? "label-float" : ""} ${formData.bsbNumberErrorText ? "input-error" : ""}`}
                        >
                            {formData.bsbNumberErrorText && !formData.bsbNumber ? formData.bsbNumberErrorText : "BSB Number*"}
                        </label>
                    </div>
                    <div className='mt-3 input-container'>
                        <InputText
                            id="bankAccountNumber"
                            type="text"
                            placeholder=""
                            value={formData.bankAccountNumber}
                            onChange={(e) => handleInputChange("bankAccountNumber", e.target.value.replace(/\D/g, ""))}
                            className={`input-placeholder ${formData.bankAccountNumberErrorText ? "border-red" : formData.bankAccountNumber ? "border-custom" : ""}`}
                        />
                        <label
                            htmlFor="bankAccountNumber"
                            className={`label-name ${formData.bankAccountNumber || formData.bankAccountNumberErrorText ? "label-float" : ""} ${formData.bankAccountNumberErrorText ? "input-error" : ""}`}
                        >
                            {formData.bankAccountNumberErrorText && !formData.bankAccountNumber ? formData.bankAccountNumberErrorText : "Bank Account Number*"}
                        </label>
                    </div>
                </div>
            )}
            <div className='mt-3' style={{ color: '#585858' }}>
                <span className="font-bold mt-2" style={{ color: 'red' }}>Note:</span>&nbsp;
                Your bank details will only be released when you are awarded a job with payment by online bank transfer.
            </div>
        </>
    );
};
export default PaymentMethodSelection;
