import { GoogleMap, useLoadScript } from '@react-google-maps/api';
import { CSSProperties, useCallback, useEffect, useRef, useState } from 'react';
import { GeoSearchFeature, useGeoSearchHook } from '../../hooks/SearchGeoSearchHook';

import MarkerWithInfoWindow from './MarkerWithInfoWindow';
import environment from '../../helper/environment';

const MAP_API_KEY = `${import.meta.env.VITE_GOOGLE_MAP_API_KEY}&loading=async`;

const defaultCountryCenter: { [key: string]: { lat: number; lng: number } } = {
    au: { lat: -25.2744, lng: 133.7751 },
    nz: { lat: -40.9006, lng: 174.886 },
    default: { lat: 37.7749, lng: -122.4194 },
};

interface JuggleMapProps extends CSSProperties {
    className?: string;
}

function loadingView() {
    return (
        <div style={{ height: '100vh', width: '100vw', display: 'flex', justifyContent: 'center' }}>
            Loading...
        </div>
    );
}
function errorView(error: string) {
    return (
        <div style={{ height: '100vh', width: '100vw', display: 'flex', justifyContent: 'center' }}>
            Error...
        </div>
    );
}

function JuggleMaps({ height, width, className, ...styleProps }: JuggleMapProps) {
    const [data, setData] = useState<GeoSearchFeature[]>([]);
    const [defaultCenter, setDefaultCenter] = useState<{ lat: number; lng: number }>(
        defaultCountryCenter[environment.getCountry(window.location.hostname)]
    );
    const [zoom, setZoom] = useState<number>(14.2);

    const mapRef = useRef<google.maps.Map | null>(null);
    const boundsChangedTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const { isLoaded, loadError } = useLoadScript({
        googleMapsApiKey: MAP_API_KEY,
    });

    const { setBounds } = useGeoSearchHook((responseData) => {
        if (JSON.stringify(responseData) !== JSON.stringify(data)) {
            setData(responseData);
        }

        const center = responseData.find((value) => value.properties.isCenterPoint);

        if (
            !Object.values(defaultCountryCenter).some(
                (countryCenter) =>
                    countryCenter.lat === defaultCenter.lat &&
                    countryCenter.lng === defaultCenter.lng
            )
        ) {
            return;
        }

        if (center) {
            setDefaultCenter({
                lat: center.geometry.coordinates[1],
                lng: center.geometry.coordinates[0],
            });
        }
    });

    const updateBounds = useCallback(() => {
        if (mapRef.current) {
            const bounds = mapRef.current.getBounds();
            if (bounds) {
                setBounds([
                    bounds.getNorthEast().lng(),
                    bounds.getNorthEast().lat(),
                    bounds.getSouthWest().lng(),
                    bounds.getSouthWest().lat(),
                ]);
            }
        }
    }, [setBounds]);

    const onLoad = useCallback(
        (map: google.maps.Map) => {
            mapRef.current = map;
            mapRef.current.setCenter(defaultCenter);
            updateBounds();
        },
        [updateBounds]
    );

    const onBoundsChanged = useCallback(() => {
        if (boundsChangedTimeoutRef.current) {
            clearTimeout(boundsChangedTimeoutRef.current);
        }
        boundsChangedTimeoutRef.current = setTimeout(() => {
            updateBounds();
        }, 300);
    }, [updateBounds]);

    useEffect(() => {
        return () => {
            if (boundsChangedTimeoutRef.current) {
                clearTimeout(boundsChangedTimeoutRef.current);
            }
        };
    }, []);

    if (!isLoaded) return loadingView();
    if (loadError) return errorView(loadError.message);

    return (
        <div style={{ height: '100vh', width: '100vw' }} className='fixed'>
            <div
                className={className}
                style={{ height: height || '100%', width: width || '100%', ...styleProps }}
            >
                <GoogleMap
                    mapContainerStyle={{ height: '100%', width: '100%' }}
                    zoom={zoom}
                    center={defaultCenter}
                    onLoad={onLoad}
                    onBoundsChanged={onBoundsChanged}
                    onZoomChanged={() => {
                        if (mapRef.current) {
                            const newZoom = mapRef.current.getZoom();
                            if (newZoom !== undefined) {
                                setZoom(Math.min(Math.max(newZoom, 5), 18));
                            }
                        }
                    }}
                    options={{
                        disableDefaultUI: true,
                        clickableIcons: false,
                        minZoom: 5,
                        maxZoom: 18,
                        gestureHandling: 'greedy',
                        mapTypeControl: false,
                        streetViewControl: false,
                        rotateControl: false,

                        styles: [
                            {
                                featureType: 'all',
                                stylers: [{ visibility: 'on' }],
                            },
                            {
                                featureType: 'transit',
                                stylers: [{ visibility: 'off' }],
                            },
                            {
                                featureType: 'poi',
                                stylers: [{ visibility: 'off' }],
                            },
                        ],
                    }}
                >
                    {data.map((value, index) => (
                        <MarkerWithInfoWindow
                            key={`${index}_marker`}
                            {...value}
                            map={mapRef.current}
                        />
                    ))}
                </GoogleMap>
            </div>
        </div>
    );
}

export default JuggleMaps;
