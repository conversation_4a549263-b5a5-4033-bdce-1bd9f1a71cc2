import React, { useEffect, useMemo, useState } from 'react';
import { Applicants, Jobs, ManageJobSectionProps } from '../types';
import styles from '../../../Common/styles/review-and-post.module.css';
import { Tag } from 'primereact/tag';
import calendar from '../../../../assets/images/Icons/Icon (1).png';
import clock from '../../../../assets/images/Icons/Vector.png';
import remove from '../../../../assets/images/Icons/remove.png';
import ArrowLeft from '../../../../assets/images/Icons/arrow-left.png';
import home from '../../../../assets/images/Icons/home-05.png';
import BackArrow from '../../../../assets/images/Icons/back-icon.png';
import doller from '../../../../assets/images/Icons/Dollar.png';
import { Divider } from 'primereact/divider';
import useLoader from '../../../../hooks/LoaderHook';
import Service from '../../../../services/services';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AwardCard from '../Common/AwardCard';
import c from '../../../../helper/juggleStreetConstants';
import utils from '../../../../components/utils/util';
import CookiesConstant from '../../../../helper/cookiesConst';
import { Dialog } from 'primereact/dialog';
import NoJobsCard from '../Common/NoJobsCard';
import ConfirmAwardDialog from '../Common/ConfirmAwardDialog';
import JobFooter from '../Common/JobFooter';
import { CancelJobPopup, useCancelJobPopup } from '../Common/CancelJobPopup';
import JobAnalytics from '../Common/JobAnalytics';
import WeeklyScheduleds from '../Common/WeeklySheduled';
import ManageRecruitment from '../Common/ManageRecruitment/ManageRecruitment';
import ViewJobFull from './ViewJob';
import Loader from '../../../../commonComponents/Loader';
import ApplyFor from './ApplyFor';
import calendarMobile from '../../../../assets/images/Icons/calender.png';
import clockStart from '../../../../assets/images/Icons/clockstart.png';
import dollerMobile from '../../../../assets/images/Icons/family_membership.png';
import homeMobile from '../../../../assets/images/Icons/home.png';
import useIsMobile from '../../../../hooks/useIsMobile';
import { MdOutlineHomeWork } from 'react-icons/md';
import { FaCalendarXmark, FaCheck, FaRegEye } from 'react-icons/fa6';
import CustomDialog from '../../../../commonComponents/CustomDialog';
import ProviderProfile from '../../../Parent/ProviderProfile/ProviderProfile';
import { HiOutlineCurrencyDollar } from 'react-icons/hi2';
import environment from '../../../../helper/environment';
import { IframeBridge } from '../../../../services/IframeBridge';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../store';
import { IoClose } from 'react-icons/io5';
import JobDetails from '../../postJobNew/Components/JobDetails';
import starIcon from '../../../../assets/images/Icons/star.png';
import locationIcon from '../../../../assets/images/Icons/location.png';
import { SlLocationPin } from 'react-icons/sl';
import fileCheck from '../../../../assets/images/Icons/file-check-01.png';

const JobSummaryUnAwarded: React.FC<ManageJobSectionProps> = ({ refresh }) => {
    const { disableLoader, enableLoader } = useLoader();
    const [searchParams] = useSearchParams();
    const [jobClients, setJobClients] = useState<Jobs | null>(null);
    const jobIdString = searchParams.get('jobId');
    const activeTab = searchParams.get('activeTab');
    const navigate = useNavigate();
    const [showfullJob, setShowFullJob] = useState(false);
    const [duplicateJob, setDuplicateJob] = useState<boolean>(false);
    const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedJobtId, setSelectedJobId] = useState<number | null>(null);
    const [applicantId, setApplicantId] = useState<number | null>(null);
    const [manageRecruitment, setManageRecruitment] = useState<boolean>(false);
    const [update, setUpdate] = useState<boolean>(false);
    const jobId = jobIdString ? parseInt(jobIdString) : null;
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const ischateligible = sessionInfo.data?.['paymentInfo']['paymentType'];
    const [dialogVisible, setDialogVisible] = useState(false);
    const [chatErrorMessage, setChatErrorMessage] = useState<string>('');
    const clientType = utils.getCookie(CookiesConstant.clientType);
    const totalInvited = jobClients?.applicantsTotal || 0;
    const totalViewed = jobClients?.applicantsViewed || 0;
    const client = Number(utils.getCookie(CookiesConstant.clientType));

    const { inIframe } = useSelector((state: RootState) => state.applicationState);
    const convertTo12HourFormat = (timeSlot: string) => {
        const [start, end] = timeSlot.split('-');
        return `${formatTime(start)} - ${formatTime(end)}`;
    };
    const { isMobile } = useIsMobile();
    const [isExpanded, setIsExpanded] = useState(false);
    const wordLimit = 30; // Set your preferred word limit

    const truncateText = (text, wordLimit) => {
        const words = text.split(' ');
        if (words.length > wordLimit) {
            return words.slice(0, wordLimit).join(' ') + '...';
        }
        return text;
    };
    const distanceMapping = {
        0: 'Within 2.5km',
        1: 'Within 5km',
        2: 'Within 10km',
    };
    const ageMapping = {
        1: '16-17',
        2: '18-24',
        3: '25-44',
        4: '45+',
    };
    const otherSkillsMapping = {
        4: 'Special Needs',
        9: 'Driving Licence',
    };

    const tutoringCategoryMapping = {
        1: 'Newbie',
        2: 'Apprentice',
        3: 'Experienced',
        4: 'Professional',
    };

    const filteredApplicantFilters = jobClients?.applicantFilters?.filter(
        (filter) =>
            filter.field === 'age' ||
            filter.field === 'distance' ||
            filter.field === 'otherSkills' ||
            filter.field === 'tutoringCategory'
    );

    const formatTime = (time: string) => {
        const [hours, minutes] = time.split(':').map(Number);
        const period = hours >= 12 ? 'pm' : 'am';
        const formattedHours = hours % 12 || 12;
        return `${formattedHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    };
    let formattedDate = '';

    if (jobClients && jobClients.jobDate) {
        const jobDate = new Date(jobClients.jobDate);
        function getDaySuffix(date) {
            const day = date.getDate();
            if (day > 3 && day < 21) return 'th';
            switch (day % 10) {
                case 1:
                    return 'st';
                case 2:
                    return 'nd';
                case 3:
                    return 'rd';
                default:
                    return 'th';
            }
        }
        formattedDate = `${jobDate.toLocaleString('en-EN', {
            weekday: 'short',
        })} ${jobDate.getDate()}${getDaySuffix(jobDate)} of ${jobDate.toLocaleString('en-EN', {
            month: 'long',
        })}`;
    }

    const formatShortDate = (jobDate: string | null): string => {
        if (!jobDate) return '';

        const date = new Date(jobDate);
        const day = date.toLocaleString('en-EN', { weekday: 'short' });
        const dayOfMonth = date.getDate();
        const month = date.toLocaleString('en-EN', { month: 'short' });
        const year = date.getFullYear().toString().slice(-2); // Get last two digits of year

        return `${day} ${dayOfMonth} ${month} ${year}`;
    };

    const [showPopup, setShowPopup] = useState(false);
    const [selectedProviderId, setSelectedProviderId] = useState(null);

    const redirectAfterHome = (id: number) => {
        IframeBridge.sendToParent({
            type: 'navigateHelperProfile',
            data: {
                id: String(id),
            },
        });
        if (isMobile && !inIframe) {
            const navigateParams = new URLSearchParams();
            navigateParams.set('id', id.toString());
            const clientType = utils.getCookie(CookiesConstant.clientType);
            if (clientType === '2') {
                navigate(`/business-home/provider-profile?${navigateParams.toString()}`);
            } else if (clientType === '1') {
                navigate(`/parent-home/provider-profile?${navigateParams.toString()}`);
            } else if (clientType === '0') {
                navigate(`/helper-home/client-profile?${navigateParams.toString()}`);
            } else {
                console.warn('Unknown clientType, no navigation performed.');
            }
        } else {
            setSelectedProviderId(id);
            setShowPopup(true);
        }
    };
    const handleCloseProfilePopup = () => {
        setShowPopup(false);
    };
    //     const canShoCard =()=>{

    // }



    const handleAwardJob = (id: number) => {
        const isOneOffJob = jobClients?.isRecurringJob ? false : true;
        const payload = {
            ...jobClients,
            // awardedApplicantIds: [applicantId],
            awardedApplicantId: isOneOffJob ? applicantId : null,
            awardedApplicantIds: isOneOffJob ? null : [applicantId],
            applicantFilters: null,
            applicationSummaries: null,
            weeklySchedule: null,
            jobInvitations: null,

            returnUpdatedJob: true,
            applicants: jobClients.applicants.map((v) => {
                // Return a new object with applicantAvailability set to null
                return {
                    ...v, // Spread the properties of the existing applicant object
                    applicantAvailability: null, // Override applicantAvailability to null
                    numOfMatches: null,
                };
            }),
        };
        enableLoader();
        Service.jobClientAwardJobs(
            async (response: Jobs) => {
                // setAwardJob(response);

                await refresh();

                const newSearchParams = new URLSearchParams();
                newSearchParams.set('jobId', jobIdString);
                newSearchParams.set('activeTab', '1');
                navigate({ search: newSearchParams.toString() });
                disableLoader();
            },
            (error: any) => {
                disableLoader();
                console.error('Error awarding job:', error);
            },

            id,
            payload
        );
    };
    const handleRemoveJob = (id: number, tabIndex: string) => {
        // Show the confirmation popup
        showCancelJobPopup(
            'Remove Applicant?', // Heading
            `will be removed from your shortlist. Their response will remain in the response table.`, // Message
            'Go Back', // Cancel Text
            'Remove', // Confirm Text
            <img src={remove} alt="confirm" style={{ height: '20px', width: '20px' }} />,
            () => {
                const payload = {
                    applicants: [
                        {
                            id: id,
                            applicationStatus: c.jobApplicationStatus.EXCLUDED_BY_CLIENT,
                        },
                    ],
                    id: jobClients.id,
                    jobStatus: jobClients.jobStatus.toString(),
                };

                enableLoader();
                Service.jobClientAwardJobs(
                    (response: Jobs) => {
                        const newSearchParams = new URLSearchParams();
                        newSearchParams.set('jobId', '-1');
                        newSearchParams.set('activeTab', tabIndex);
                        navigate({ search: newSearchParams.toString() });

                        disableLoader();
                    },
                    (error: any) => {
                        disableLoader();
                        console.error('Error cancelling job:', error);
                    },
                    jobClients.id,
                    payload
                );
            },
            () => {
                // onCancel callback
            }
        );
    };
    const handleCancelJob = (id: number, tabIndex: string) => {
        // Show the confirmation popup
        showCancelJobPopup(
            'Are you sure?', // Heading
            'If you cancel this job all applicants will be notified and the canceled job will be moved to the Job History section of My Jobs. Are you sure you want to continue?', // Message
            'Go Back', // Cancel Text
            'Cancel Job', // Confirm Text
            <img src={remove} alt="confirm" style={{ height: '20px', width: '20px' }} />,

            () => {
                const payload = {
                    ...jobClients,
                    jobStatus: c.jobStatus.CANCELLED,
                };

                enableLoader();
                Service.jobClientAwardJobs(
                    async (response: Jobs) => {
                        // setcancelJob(response);
                        await refresh();
                        const newSearchParams = new URLSearchParams();
                        newSearchParams.set('jobId', '-1');
                        newSearchParams.set('activeTab', tabIndex);
                        navigate({ search: newSearchParams.toString() });
                        disableLoader();
                    },
                    (error: any) => {
                        disableLoader();
                        console.error('Error cancelling job:', error);
                    },
                    id,
                    payload
                );
            },

            () => {
                // onCancel callback
                // Handle cancellation, maybe log or close popup
            }
        );
    };
    const daysOfWeek = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
    ];

    const { responseTable, availableApplicants } = useMemo(() => {
        if (!jobClients) return { result: [], availableApplicants: [] };
        const dows = ['Sun', 'Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat'];
        const schedules = jobClients.weeklySchedule?.weeklyScheduleEntries || [];
        const applicants = jobClients.applicants || [];
        const result: Array<{
            dayOfWeek: number;
            day: string;
            startTime: string;
            endTime: string;
            shiftId: number;
            price: number;
            awardedAplicantId: number | null;
            applicants: Array<{
                applicantId: number;
                availabilityId: number;
                publicName: string;
                imageSrc: string;
                status: number;
                completedJobsCount: number;
                applicantRatingsCount: number;
                applicantRatingsAvg: number;
                responseRate: number;
            }>;
        }> = [];
        const availableApplicants: Array<{
            applicantId: number;
            publicName: string;
            imageSrc: string;
            isSuperHelper: boolean;
            applicantRatingsCount: number;
            applicantRatingsAvg: number;
            responseRate: number;
            id: number;
            applicationStatus: number;
            gettingHome: number;
            suburb: string;
        }> = [];
        const addedApplicantIds = new Set<number>();

        const inPrevShift = (dow: number, id: number) => {
            const shift = result.find((r) => r.dayOfWeek === dow);
            return shift?.applicants.some((a) => a.availabilityId === id) || false;
        };

        for (const schedule of schedules) {
            const shiftData = {
                dayOfWeek: schedule.dayOfWeek,
                day: dows[schedule.dayOfWeek],
                startTime: schedule.jobStartTime,
                endTime: schedule.jobEndTime,
                shiftId: schedule.id,
                price: schedule.price,
                awardedAplicantId: schedule.applicantId,
                applicants: [] as Array<{
                    applicantId: number;
                    availabilityId: number;
                    publicName: string;
                    imageSrc: string;
                    status: number;
                    completedJobsCount: number;
                    applicantRatingsCount: number;
                    applicantRatingsAvg: number;
                    responseRate: number;
                    id: number;
                    applicationStatus: number;
                }>,
            };

            for (const applicant of applicants) {
                for (const availability of applicant.applicantAvailability) {
                    if (
                        availability.dayOfWeek === schedule.dayOfWeek &&
                        !shiftData.applicants.some(
                            (a) => a.applicantId === applicant.applicantId
                        ) &&
                        !inPrevShift(availability.dayOfWeek, availability.id)
                    ) {
                        shiftData.applicants.push({
                            applicantId: applicant.applicantId,
                            availabilityId: availability.id,
                            publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
                            imageSrc: applicant.applicantImageSrc,
                            status: availability.availabilityStatus,
                            completedJobsCount: applicant.completedJobsCount,
                            applicantRatingsCount: applicant.applicantRatingsCount,
                            applicantRatingsAvg: applicant.applicantRatingsAvg,
                            responseRate: applicant.responseRate,
                            id: applicant.id,
                            applicationStatus: applicant.applicationStatus,
                        });

                        if (
                            schedule.applicantId === null &&
                            availability.availabilityStatus ===
                            c.applicantAvailabilityStatus.AVAILABLE &&
                            !addedApplicantIds.has(applicant.applicantId)
                        ) {
                            availableApplicants.push({
                                applicantId: applicant.applicantId,
                                publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
                                imageSrc: applicant.applicantImageSrc,
                                isSuperHelper: applicant.completedJobsCount >= 15,
                                applicantRatingsCount: applicant.applicantRatingsCount,
                                applicantRatingsAvg: applicant.applicantRatingsAvg,
                                responseRate: applicant.responseRate,
                                id: applicant.id,
                                applicationStatus: applicant.applicationStatus,
                                gettingHome: applicant.gettingHome,
                                suburb: applicant.suburb,
                            });
                            addedApplicantIds.add(applicant.applicantId);
                        }
                    }
                }
            }

            result.push(shiftData);
        }

        return { responseTable: result, availableApplicants };
    }, [jobClients]);

    useEffect(() => {
        const fetchData = () => {
            enableLoader();
            if (clientType == '0') {
                Service.jobProviderDetails(
                    (response: Jobs) => {
                        setJobClients(response);
                        refresh();
                        disableLoader();
                    },
                    (error: any) => {
                        console.error('Error fetching data:', error);
                        disableLoader();
                    },
                    jobId
                );
            } else {
                Service.jobClientDetails(
                    (response: Jobs) => {
                        setJobClients(response);
                        refresh();
                        disableLoader();
                    },
                    (error: any) => {
                        console.error('Error fetching data:', error);
                        disableLoader();
                    },
                    jobId
                );
            }
        };
        fetchData();
    }, [activeTab, update]);
    const Items = [
        { label: 'Unspecified', value: '0' },
        { label: 'I need taking home', value: '1' },
        { label: 'Walking', value: '2' },
        { label: 'Public transport', value: '3' },
        { label: 'Getting picked up', value: '4' },
        { label: 'Driving myself', value: '5' },
        { label: 'Uber/Taxi', value: '6' },
        { label: 'Uber/Taxi', value: '6' },
    ];

    const getGettingHomeLabel = (value: number) => {
        const item = Items.find((item) => item.value === value.toString());
        return item ? item.label : 'Not Available';
    };

    const getOptionInternal = (value, option) => {
        if (option.optionId === value) return option;

        let result = null;
        if (option.children != null) {
            for (let i = 0; i < option.children.length; i++) {
                result = getOptionInternal(value, option.children[i]);
                if (result !== null) break;
            }
        }

        return result;
    };
    const getOption = (value, options) => {
        for (let i = 0; i < options?.length; i++) {
            const result = getOptionInternal(value, options[i]);
            if (result !== null) return result;
        }

        return null;
    };
    const getSchoolYears = (jobClients: Jobs | null) => {
        if (
            !jobClients?.jobSettings?.schoolYears ||
            jobClients.jobSettings.schoolYears.length === 0
        ) {
            return '';
        }

        const schoolYears =
            jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING
                ? jobClients?.['provider']?.primarySchoolYears
                : jobClients?.['provider']?.highSchoolYears;
        let text = '';
        jobClients.jobSettings.schoolYears.forEach((schoolYear) => {
            const option = getOption(schoolYear, schoolYears);
            text += `${option?.text}/`;
        });

        return text.substring(0, text.length - 1);
    };

    const getSchoolSubjects = (jobClients: Jobs | null) => {
        if (
            !jobClients?.jobSettings?.schoolSubjects ||
            jobClients.jobSettings.schoolSubjects.length === 0
        ) {
            return 'All';
        }

        const schoolSubjects =
            jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING
                ? jobClients?.['provider']?.primarySchoolSubjects
                : jobClients?.['provider']?.highSchoolSubjects;
        const option = getOption(jobClients.jobSettings.schoolSubjects[0], schoolSubjects);

        return option?.text;
    };

    const getJobDeliveryMethodText = (jobDeliveryMethod) => {
        switch (jobDeliveryMethod) {
            case 1:
                return 'In-home Tutoring';
            case 2:
                return 'Online Tutoring';
            default:
                return 'Unknown';
        }
    };
    const hasApplicantsToShow = availableApplicants.filter(
        (a) => a.applicationStatus !== c.jobApplicationStatus.EXCLUDED_BY_CLIENT
    ).length > 0 || (jobClients?.applicantsApplied > 0 && jobClients.managedBy === 20);
    const jobDeliveryMethodText = getJobDeliveryMethodText(jobClients?.jobDeliveryMethod);
    const cleanAddress = (address: string) => {
        return address.split(',')[0].trim();
    };
    const calculatePriceBasedOnDuration = (
        startTime: string,
        endTime: string,
        hourlyPrice: number
    ) => {
        // Convert times to Date objects for calculation
        const start = new Date(`2000-01-01 ${startTime}`);
        const end = new Date(`2000-01-01 ${endTime}`);
        // If end time is before start time, assume it crosses midnight
        if (end < start) {
            end.setDate(end.getDate() + 1);
        }
        // Calculate hours difference
        const diffMs = end.getTime() - start.getTime();
        const hours = diffMs / (1000 * 60 * 60);
        // Calculate total price
        const totalPrice = hours * hourlyPrice;

        return {
            hours: hours.toFixed(1), // Return hours with 1 decimal place
            totalPrice: totalPrice.toFixed(2), // Return price with 2 decimal places
        };
    };
    const country = environment.getCountry(window.location.hostname);
    const WeeklySummary = () => {
        const { isMobile } = useIsMobile();
        if (!jobClients?.weeklySchedule?.weeklyScheduleEntries) return null;
        const entries = jobClients.weeklySchedule.weeklyScheduleEntries; // Calculate totalDays (unique days)
        const uniqueDays = new Set(entries.map((entry) => entry.dayOfWeek));
        const totalDays = uniqueDays.size;
        const totalShifts = entries.length;
        let totalHours = 0;
        entries.forEach((entry) => {
            totalHours += utils.getTimeDifference(entry.jobStartTime, entry.jobEndTime);
        });
        return (
            <div
                key="shift-summary"
                className="entry-body"
                style={{ padding: !isMobile ? '10px 0' : '0px' }}
            >
                <span
                    style={{
                        fontSize: isMobile && '14px',
                        fontWeight: isMobile && '400',
                    }}
                    className={`${styles.headerH4} p-0 m-0 `}
                >
                    Weekly Summary:{' '}
                </span>
                <span
                    style={{
                        fontSize: isMobile && '16px',
                        fontWeight: isMobile && '700',
                    }}
                >
                    {totalHours} Hours over {totalShifts} shifts in {totalDays} Days
                </span>
            </div>
        );
    };
    const getJobDetails = (jobclientProp) => {
        const jobToUse = jobclientProp || { jobType: 0 };

        switch (jobToUse.jobType) {
            case 256:
                return {
                    label: 'Odd Job',
                };
            case 64:
                return {
                    label: 'Primary School',
                };
            case 128:
                return {
                    label: 'High School',
                };
            default:
                return {
                    label: 'Childcare',
                };
        }
    };

    const calculateHourlyRate = (totalPrice, hours) => {
        if (hours <= 0) return 0;
        return totalPrice / hours;
    };
    const viewProvider = (applicantId) => {
        IframeBridge.sendToParent({
            type: 'navigateHelperProfile',
            data: {
                id: String(applicantId),
            },
        });
        if (!inIframe) {
            if (isMobile) {
                const url = `/${client === 1 ? 'parent-home' : 'business-home'
                    }/provider-profile?id=${applicantId}`;
                navigate(url);
            } else {
                // setSelectedapplicantId(applicantId);
                setShowPopup(true);
            }
        }
    };

    const renderApplicantsSection = (jobClients: Jobs,) => {
    if (
  jobClients.applicants?.length === 0 &&
  jobClients.applicantsApplied === 0 &&
  jobClients.managedBy === 20
) {
  return <NoJobsCard description="No jobs match the specified criteria" />;
}

        const availableApplicants: Applicants[] = [];
        jobClients.applicants?.forEach((a) => {
            const status = a.applicationStatus as 5 | 9 | 11;
            if (
                [
                    c.jobApplicationStatus.APPLIED,
                    c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM,
                    c.jobApplicationStatus.SHORTLISTED_BY_CLIENT,
                ].includes(status)
            ) {
                availableApplicants.push(a);
            }
        });

        if (
            availableApplicants.filter(
                (a) => a.applicationStatus !== c.jobApplicationStatus.EXCLUDED_BY_CLIENT
            ).length === 0 &&
            jobClients.managedBy === c.managedBy.SYSTEM
        ) {
            return <NoJobsCard description="Sit tight Helpers will apply to your job soon!" />;
        }

        return availableApplicants.map((a, index) => (
            <div className="mt-3" key={index}>
                <AwardCard
                    gettingHome={getGettingHomeLabel(a.gettingHome)}
                    helperImgSrc={a.applicantImageSrc}
                    publicName={`${a.applicantFirstName || 'name'} ${a.applicantLastInitial || 'Anonymous'}`}
                    isSuperHelper={a.completedJobsCount >= 15}
                    onViewProfileClicked={() => redirectAfterHome(a.applicantId)}
                    onAwardJobClicked={() => {
                        setSelectedJobId(jobClients.id);
                        setApplicantId(a.applicantId);
                        setIsDialogOpen(true);
                    }}
                    isOneoffJob={
                        !jobClients.isTutoringJob || !jobClients.isRecurringJob
                    }
                    onContactClicked={() => {
                        if (ischateligible === 0) {
                            setChatErrorMessage('You need an active membership to chat with Juggle St users.');
                            setDialogVisible(true);
                            return;
                        }
                        IframeBridge.sendToParent({
                            type: 'navigateChatWithId',
                            data: { id: String(a.applicantId) },
                        });
                        if (!inIframe) {
                            navigate(
                                clientType === '1'
                                    ? `/parent-home/inAppChat?userId=${a.applicantId}`
                                    : `/business-home/inAppChat?userId=${a.applicantId}`
                            );
                        }
                    }}
                    onDismissClicked={() => handleRemoveJob(a.id, '-1')}
                    applicationStatus={a.applicationStatus}
                    applicantId={a.applicantId}
                />
            </div>
        ));
    };

   
const shouldShow = (
    (jobClients?.managedBy === c.managedBy.SYSTEM && [1, 256].includes(jobClients.jobType)) ||
    
    jobClients?.applicants?.some((a) =>
        a.applicationStatus === c.jobApplicationStatus.APPLIED
    ) 
);
    return jobClients ? (
        !isMobile ? (
            <>
                <ManageRecruitment
                    visible={manageRecruitment}
                    job={jobClients}
                    onClose={() => {
                        setManageRecruitment(false);
                        setUpdate((prev) => !prev);
                    }}
                />
                <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
                <Dialog
                    visible={dialogVisible}
                    onHide={() => setDialogVisible(false)}
                    content={
                        <div className={styles.dialogContent}>
                            <IoClose
                                onClick={() => setDialogVisible(false)}
                                className={styles.CloseBtn}
                            />
                            <div>
                                <h1
                                    style={{
                                        fontSize: '32px',
                                        fontWeight: '700',
                                        color: '#585858',
                                        margin: '0px',
                                    }}
                                >
                                    Chat
                                </h1>
                            </div>
                            <Divider />

                            <div>
                                <p
                                    style={{
                                        fontSize: '20px',
                                        fontWeight: '500',
                                        color: '#585858',
                                    }}
                                >
                                    {chatErrorMessage}
                                </p>
                                <div style={{ display: 'flex', flexDirection: 'row', gap: '20px' }}>
                                    <button
                                        className={styles.buttonPost}
                                        onClick={() => setDialogVisible(false)}
                                    >
                                        Ok
                                    </button>
                                    {/* <button
                                    className={styles.buttonPost}
                                    onClick={() => {
                                        if (clientType === '1') {
                                            navigate('/parent-home/job/post/job-type');
                                        } else if (clientType === '2') {
                                            navigate('/business-home/job/post/job-type');
                                        }
                                    }}
                                >
                                    Post Job
                                </button> */}
                                </div>
                            </div>
                        </div>
                    }
                />
                <Dialog
                    visible={duplicateJob}
                    onHide={() => {
                        setDuplicateJob(false);
                    }}
                    style={{
                        width: '100vw',
                        height: '100vh',
                        maxHeight: 'none',
                        maxWidth: 'none',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                    content={
                        <div
                            className="flex flex-column py-3 px-4"
                            style={{
                                backgroundColor: '#FFFFFF',
                                border: '1px solid #F0F4F7',
                                borderRadius: '20px',
                                maxWidth: '610px',
                            }}
                        >
                            <h1
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '700',
                                    fontSize: '32px',
                                    color: '#585858',
                                }}
                            >
                                Duplicate Job
                            </h1>
                            <Divider />
                            <p
                                className="m-0 p-0 mt-3"
                                style={{
                                    fontWeight: '500',
                                    fontSize: '16px',
                                    color: '#585858',
                                }}
                            >
                                Use this feature to post a new job based on the current job details,
                                all applicable job settings are copied.
                            </p>
                            <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
                                <button
                                    className="px-4 py-1 cursor-pointer"
                                    style={{
                                        backgroundColor: '#FFFFFF',
                                        border: 'none',
                                        boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.25)',
                                        fontWeight: '500',
                                        fontSize: '18px',
                                        color: '#585858',
                                        textDecoration: 'underline',
                                        borderRadius: '5px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setDuplicateJob(false);
                                    }}
                                >
                                    No
                                </button>
                                <button
                                    className="px-7 py-1 cursor-pointer"
                                    style={{
                                        backgroundColor: '#FFA500',
                                        border: 'none',
                                        boxShadow: '0 4px 4px 0 rgba(0, 0, 0, 0.25)',
                                        fontWeight: '700',
                                        fontSize: '18px',
                                        color: '#FFFFFF',
                                        textDecoration: 'underline',
                                        borderRadius: '5px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setDuplicateJob(false);
                                        const newParams = new URLSearchParams();
                                        newParams.set('jobId', String(jobClients.id));
                                        if (Number(clientType) === 1) {
                                            navigate({
                                                pathname: '/parent-home/post-job/duplicate',
                                                search: newParams.toString(),
                                            });
                                            return;
                                        }
                                        navigate({
                                            pathname: '/business-home/post-job/duplicate',
                                            search: newParams.toString(),
                                        });
                                    }}
                                >
                                    Yes
                                </button>
                            </div>
                        </div>
                    }
                />

                <div
                    className="flex gap-2 justify-content-around align-items-center w-min cursor-pointer mb-3"
                    style={{
                        textWrap: 'nowrap',
                        border: '1px solid #F1F1F1',
                        padding: '10px 25px',
                        borderRadius: '20px',
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        const newGoBackParams = new URLSearchParams();
                        newGoBackParams.set('jobId', '-1');
                        newGoBackParams.set('activeTab', activeTab);
                        navigate({ search: newGoBackParams.toString() });
                    }}
                >
                    <img src={ArrowLeft} alt="Arrow Left" width="18px" height="18px" />
                    <p
                        className="m-0 p-0"
                        style={{
                            fontWeight: '400',
                            fontSize: '14px',
                            color: '#585858',
                        }}
                    >
                        Go Back
                    </p>
                </div>

                <div
                    className=""
                    style={{
                        width: isMobile ? '100%' : '792px',
                        borderRadius: '20px',
                        paddingLeft: '30px',
                        // paddingRight: "20px",
                        marginRight: '2%',
                        border: '1px solid #dfdfdf',
                    }}
                >
                    <div
                        style={{
                            display: 'grid',
                            gridTemplateColumns: '1fr', // Single column layout to stack sections
                            width: '100%', // Full width of the parent
                            maxWidth: '100%', // Prevent overflow
                            gap: '20px', // Space between grid rows (upper and lower sections)
                        }}
                    >
                        {/* Upper Section: Job Summary and Price */}
                        <div
                            style={{
                                display: 'grid',
                                gridTemplateColumns: 'auto 1fr', // Two columns: Job Summary (auto) and Price (takes remaining space)
                                alignItems: 'center',
                                width: '100%',
                                paddingRight: '25px',
                            }}
                        >
                            <h3
                                className="font-bold"
                                style={{ color: '#585858', fontSize: '30px' }}
                            >
                                Job Summary
                            </h3>
                            {clientType !== '0' && jobClients.price ? (
                                <div className={`${styles.jobPrice}`} style={{ textAlign: 'end' }}>
                                    <p
                                        className="font-normal mb-2"
                                        style={{
                                            textAlign: 'end',
                                            color: '#585858',
                                            fontSize: '16px',
                                        }}
                                    >
                                        {jobClients.isTutoringJob
                                            ? 'Price Per Week = '
                                            : jobClients.isRecurringJob
                                                ? 'Weekly Job Total = '
                                                : 'Job Total  = '}

                                        <strong className={styles.jobPriceDoller}>
                                            $
                                            {jobClients.isTutoringJob
                                                ? jobClients.pricePerWeek
                                                : jobClients.isRecurringJob
                                                    ? jobClients.pricePerWeek
                                                    : (() => {
                                                        const { hours, totalPrice } =
                                                            calculatePriceBasedOnDuration(
                                                                jobClients.jobStartTime,
                                                                jobClients.jobEndTime,
                                                                jobClients.price
                                                            );
                                                        const hoursNum = parseFloat(hours); // Convert string to number
                                                        const hourText =
                                                            hoursNum <= 1 ? 'hour' : 'hours'; // Compare as number
                                                        if (jobClients.paymentType === 1) {
                                                            return `${jobClients.price} for ${hours} ${hourText}`; // No multiplication, just raw price
                                                        } else if (jobClients.paymentType === 2) {
                                                            return `${totalPrice} for ${hours} ${hourText}`; // Multiply price by hours
                                                        } else {
                                                            return `${totalPrice} for ${hours} ${hourText}`; // Default behavior
                                                        }
                                                    })()}
                                        </strong>
                                    </p>
                                    {/* Add Overtime field for non-recurring jobs */}
                                    {!jobClients.isTutoringJob &&
                                        !jobClients.isRecurringJob &&
                                        jobClients.overtimeRate && (
                                            <p
                                                className="font-normal"
                                                style={{
                                                    textAlign: 'end',
                                                    color: '#585858',
                                                    fontSize: '16px',
                                                    marginTop: '4px', // Add some spacing between Job Total and Overtime
                                                }}
                                            >
                                                Overtime ={' '}
                                                <strong className={styles.jobPriceDoller}>
                                                    ${jobClients.overtimeRate} per hour
                                                </strong>
                                            </p>
                                        )}
                                </div>
                            ) : (
                                ''
                            )}
                        </div>

                        {/* Lower Section: Tag Buttons */}
                        <div className={styles.tagButton}>
                            {/* First Line: Calendar, Clock, Payment Method, Address */}
                            <div
                                style={{
                                    display: 'flex',
                                    flexWrap: 'nowrap', // Prevent tags from wrapping to next line
                                    gap: '8px',
                                    width: '100%',
                                    justifyContent: 'flex-start',
                                    alignItems: 'flex-start',
                                    marginBottom: '8px',
                                }}
                            >
                                {/* Tag 1: Calendar */}
                                <Tag style={{ background: '#2F9ACD', padding: '4px 8px' }}>
                                    <div
                                        className={styles.tagData}
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '4px',
                                        }}
                                    >
                                        <img
                                            alt="Calendar"
                                            src={calendar}
                                            style={{ width: '16px', color: '#FFFFFF' }}
                                        />
                                        {formattedDate}
                                    </div>
                                </Tag>

                                {/* Tag 2: Clock */}
                                {clientType === '0' ? (
                                    <Tag style={{ background: '#8577DB', padding: '4px 8px' }}>
                                        <div
                                            className={styles.tagData}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px',
                                            }}
                                        >
                                            <img
                                                alt="Clock"
                                                src={clock}
                                                style={{ width: '16px', color: '#FFFFFF' }}
                                            />
                                            {jobClients.durationType === null
                                                ? convertTo12HourFormat(
                                                    `${jobClients.jobStartTime}-${jobClients.jobEndTime}`
                                                )
                                                : `${jobClients.duration} week duration`}
                                        </div>
                                    </Tag>
                                ) : (
                                    <Tag style={{ background: '#8577DB', padding: '4px 8px' }}>
                                        <div
                                            className={styles.tagData}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px',
                                            }}
                                        >
                                            <img
                                                alt="Clock"
                                                src={clock}
                                                style={{ width: '16px', color: '#FFFFFF' }}
                                            />
                                            {jobClients.isTutoringJob
                                                ? `${jobClients.duration} week duration`
                                                : jobClients.isRecurringJob
                                                    ? `${jobClients.duration} week duration`
                                                    : convertTo12HourFormat(
                                                        `${jobClients['jobStartTime']}-${jobClients['jobEndTime']}`
                                                    )}
                                        </div>
                                    </Tag>
                                )}

                                {/* Tag 3: Payment Method */}
                                <Tag style={{ background: '#77DBC9', padding: '4px 8px' }}>
                                    <div
                                        className={styles.tagData}
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '4px',
                                        }}
                                    >
                                        <img
                                            alt="Dollar"
                                            src={doller}
                                            style={{ width: '8px', color: '#FFFFFF' }}
                                        />
                                        {jobClients.helperPaymentMethod === 1
                                            ? 'Cash payment'
                                            : 'Bank transfer'}
                                    </div>
                                </Tag>

                                {/* Tag 4: Address */}
                                {clientType !== '0' ? (
                                    <Tag
                                        style={{
                                            background: '#179D52',
                                            padding: '4px 8px',
                                            maxWidth: '238px',
                                            flex: '0 0 auto',
                                        }}
                                    >
                                        <div
                                            className={styles.tagData}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'flex-start',
                                                gap: '4px',
                                            }}
                                        >
                                            <img
                                                alt="Home"
                                                src={home}
                                                style={{
                                                    width: '16px',
                                                    color: '#FFFFFF',
                                                    flexShrink: 0,
                                                }}
                                            />
                                            <div
                                                style={{
                                                    wordBreak: 'break-word',
                                                    maxWidth: '195px', // Constrain text width for wrapping
                                                }}
                                            >
                                                {`${cleanAddress(jobClients.formattedAddress)}, ${jobClients.suburb || ''
                                                    }`}
                                            </div>
                                        </div>
                                    </Tag>
                                ) : null}
                            </div>

                            {/* Second Line: Do It Yourself/Juggle Assist, Expires In, Price Negotiable */}
                            <div
                                style={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    gap: '8px',
                                    width: '100%',
                                    justifyContent: 'flex-start',
                                    alignItems: 'center',
                                    marginBottom: '8px',
                                }}
                            >
                                {/* Tag 5: Managed By */}
                                <Tag style={{ background: '#8bb8ff', padding: '4px 8px' }}>
                                    <div
                                        className={styles.tagData}
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '4px',
                                        }}
                                    >
                                        <MdOutlineHomeWork
                                            style={{ fontSize: '18px', color: '#FFFFFF' }}
                                        />
                                        {jobClients.managedBy === 1
                                            ? 'Do It Yourself'
                                            : 'Juggle Assist'}
                                    </div>
                                </Tag>

                                {/* Tag 6: Expires In */}
                                {clientType !== '0' ? (
                                    <Tag style={{ background: '#ff6359', padding: '4px 8px' }}>
                                        <div
                                            className={styles.tagData}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px',
                                            }}
                                        >
                                            <FaCalendarXmark
                                                style={{ fontSize: '16px', color: '#FFFFFF' }}
                                            />
                                            Expires in {jobClients.expiresInDays} days
                                        </div>
                                    </Tag>
                                ) : null}

                                {/* Tag 7: Price Negotiable */}
                                {jobClients.isRecurringJob || jobClients.isTutoringJob ? (
                                    <Tag
                                        style={{ background: 'darkgoldenrod', padding: '4px 8px' }}
                                    >
                                        <div
                                            className={styles.tagData}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px',
                                            }}
                                        >
                                            <HiOutlineCurrencyDollar
                                                style={{ fontSize: '18px', color: '#FFFFFF' }}
                                            />
                                            <div>
                                                Price is{' '}
                                                {jobClients.isPriceNegotiable
                                                    ? 'Negotiable'
                                                    : 'Not Negotiable'}
                                            </div>
                                        </div>
                                    </Tag>
                                ) : null}
                            </div>
                        </div>
                    </div>
                    <div>
                        {clientType === '0' ? (
                            <div className="mt-3">
                                <div className="flex flex-wrap justify-content-between gap-3">
                                    {jobClients.ownerPublicName && (
                                        <div
                                            className="flex align-items-center text-center gap-1"
                                            style={{ width: '30%' }}
                                        >
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                invited:{' '}
                                            </h4>
                                            <p
                                                className={`${styles.yourJobDescription} p-0 m-0 underline cursor-pointer`}
                                                onClick={() =>
                                                    redirectAfterHome(jobClients.jobOwnerId)
                                                }
                                            >
                                                {jobClients.ownerPublicName} has invited you!
                                            </p>
                                        </div>
                                    )}

                                    {jobClients.price && (
                                        <div
                                            className="flex align-items-center text-center gap-1"
                                            style={{ width: '30%' }}
                                        >
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                Payment:{' '}
                                            </h4>
                                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                                ${jobClients.price} p/h{' '}
                                                {jobClients.isPriceNegotiable && 'Negotiable'}
                                            </p>
                                        </div>
                                    )}

                                    {jobClients.pricePerWeek && (
                                        <div
                                            className="flex align-items-center text-center gap-1"
                                            style={{ width: '30%' }}
                                        >
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                Total Weekly Payment:{' '}
                                            </h4>
                                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                                ${jobClients.pricePerWeek}
                                            </p>
                                        </div>
                                    )}
                                    {jobClients.overtimeRate && (
                                        <div
                                            className="flex align-items-center text-center gap-1"
                                            style={{ width: '30%' }}
                                        >
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                Extra hours rate:{' '}
                                            </h4>
                                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                                ${jobClients.overtimeRate} per hour
                                            </p>
                                        </div>
                                    )}
                                    {jobClients.ownerPhoneNumber && (
                                        <div
                                            className="flex align-items-center text-center gap-1"
                                            style={{ width: '30%' }}
                                        >
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                {' '}
                                                Phone Number:{' '}
                                            </h4>
                                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                                {jobClients.ownerPhoneNumber}
                                            </p>
                                        </div>
                                    )}
                                    {jobClients.gettingHome !== undefined &&
                                        jobClients.gettingHome !== null &&
                                        jobClients.gettingHome !== 0 && (
                                            <div
                                                className="flex align-items-center text-center gap-1"
                                                style={{ width: '30%' }}
                                            >
                                                <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                    Getting Home:{' '}
                                                </h4>
                                                <p
                                                    className={`${styles.yourJobDescription} p-0 m-0`}
                                                >
                                                    {getGettingHomeLabel(jobClients.gettingHome)}
                                                </p>
                                            </div>
                                        )}
                                    {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                        jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                        ? jobDeliveryMethodText && (
                                            <div
                                                className="flex align-items-center text-center gap-1"
                                                style={{ width: '30%' }}
                                            >
                                                <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                    Tutoring Type:{' '}
                                                </h4>
                                                <p
                                                    className={`${styles.yourJobDescription} p-0 m-0`}
                                                >
                                                    {jobDeliveryMethodText}
                                                </p>
                                            </div>
                                        )
                                        : null}

                                    {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                        jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                        ? getSchoolYears(jobClients) && (
                                            <div
                                                className="flex align-items-center text-center gap-1"
                                                style={{ width: '30%' }}
                                            >
                                                <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                    Required School Year(s):{' '}
                                                </h4>
                                                <p
                                                    className={`${styles.yourJobDescription} p-0 m-0`}
                                                >
                                                    {getSchoolYears(jobClients)}
                                                </p>
                                            </div>
                                        )
                                        : null}
                                    {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                        jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                        ? getSchoolSubjects(jobClients) && (
                                            <div
                                                className="flex align-items-center text-center gap-1"
                                                style={{ width: '30%' }}
                                            >
                                                <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                    Required Subject:{' '}
                                                </h4>
                                                <p
                                                    className={`${styles.yourJobDescription} p-0 m-0`}
                                                >
                                                    {getSchoolSubjects(jobClients)}
                                                </p>
                                            </div>
                                        )
                                        : null}
                                </div>
                                {(jobClients.formattedAddress || jobClients.suburb) && (
                                    <div className="flex align-items-center text-center gap-1 mt-2">
                                        <h4 className={`${styles.headerH4} p-0 m-0`}>Location: </h4>
                                        <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                            {jobClients.formattedAddress
                                                ? jobClients.formattedAddress
                                                : jobClients.suburb}
                                        </p>
                                    </div>
                                )}
                                {jobClients.applicantAvailability.length > 0
                                    ? jobClients.applicantAvailability && (
                                        <div className="flex text-center gap-1 mt-2">
                                            <h4 className={`${styles.headerH4} p-0 m-0`}>
                                                Required Days:{' '}
                                            </h4>
                                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                                {Array.from(
                                                    new Set(
                                                        jobClients.applicantAvailability
                                                            .filter((a) => a.isRequired === true)
                                                            .map((a) => daysOfWeek[a.dayOfWeek])
                                                    )
                                                ).join(', ')}
                                            </p>
                                        </div>
                                    )
                                    : null}
                            </div>
                        ) : null}
                        {jobClients.managedBy === 20 ? (
                            <div className="flex flex-wrap gap-1 mt-2">
                                <h4 className={`${styles.headerH4} p-0 m-0`}>Status:</h4>
                                <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                    {jobClients.jobStatus === c.jobStatus.PENDING
                                        ? 'Processing'
                                        : jobClients.jobStatus === c.jobStatus.AWARDED
                                            ? 'award'
                                            : jobClients.jobStatus}
                                </p>
                            </div>
                        ) : null}

                        {filteredApplicantFilters?.length > 0 && (
                            <div>
                                <h4 className="p-0 m-0 mt-2" style={{ color: '#585858' }}>
                                    Candidate Criteria{' '}
                                </h4>
                                <div className="ml-2">
                                    {filteredApplicantFilters.map((filter, index) => {
                                        let displayValue = '';
                                        let displayField =
                                            filter.field.charAt(0).toUpperCase() +
                                            filter.field.slice(1);
                                        if (filter.field === 'distance') {
                                            displayValue = distanceMapping[filter.value];
                                        } else if (filter.field === 'age') {
                                            if (filter.value === 0) {
                                                displayValue = Object.values(ageMapping).join(', ');
                                            } else {
                                                displayValue = (filter.value as number[])
                                                    .map((val) => ageMapping[val])
                                                    .join(', ');
                                            }
                                        } else if (filter.field === 'otherSkills') {
                                            displayValue = (filter.value as number[])
                                                .map((val) => otherSkillsMapping[val])
                                                .join(', ');
                                            displayField = 'Other';
                                        } else if (filter.field === 'tutoringCategory') {
                                            displayValue = (filter.value as number[])
                                                .map((val) => tutoringCategoryMapping[val])
                                                .join(', ');
                                            displayField = 'Experience';
                                        }
                                        return (
                                            <div key={index} className={''}>
                                                <span
                                                    className={''}
                                                    style={{ color: '#585858', fontWeight: '600' }}
                                                >
                                                    {displayField}:&nbsp;
                                                </span>
                                                <span className={''} style={{ color: '#585858' }}>
                                                    {displayValue}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        )}
                        {clientType === '2' && !jobClients['isJobManagerAccount'] && (
                            <p
                                className="p-0 m-0 mb-2 mt-2"
                                style={{
                                    color: '#585858',
                                    fontWeight: 400,
                                    fontSize: '16px',
                                }}
                            >
                                {country == 'au' && (
                                    <small>
                                        * Excludes Superannuation and other statutory entitlements.{' '}
                                    </small>
                                )}
                                {country == 'nz' && (
                                    <small>* Excludes any government statutory entitlements.</small>
                                )}
                            </p>
                        )}
                        <div className="flex flex-wrap gap-1">
                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                {(jobClients.isTutoringJob || jobClients.isRecurringJob) && (
                                    <WeeklySummary />
                                )}
                            </p>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                            <h4 className={`${styles.headerH4} p-0 m-0 `}>Job Description:</h4>
                            <p className={`${styles.yourJobDescription} p-0 m-0`}>
                                {jobClients.specialInstructions}
                            </p>
                        </div>
                        {/* <p className={`${styles.yourJobDescription} p-0 m-0`}>
                            {jobClients.specialInstructions}
                        </p> */}
                    </div>
                    <Divider className="mt-4" />
                    {clientType !== '0' ? (
                        <div className="flex justify-content-center align-items-center">
                            <span
                                className="mt-3 mb-3 cursor-pointer font-bold"
                                style={{
                                    color: '#FFA500',
                                    fontSize: '14px',
                                    textDecoration: 'underLine',
                                }}
                                onClick={(e) => {
                                    e.preventDefault();
                                    setShowFullJob(true);
                                }}
                            >
                                View job details
                            </span>
                        </div>
                    ) : null}
                </div>
                {clientType === '0' && (
                    <ApplyFor
                        job={jobClients}
                        timeSlots={[]}
                        availabilityStatusEnum={undefined}
                        totalInvited={0}
                        totalViewed={0}
                        availableApplicants={[]}
                    />
                )}
                {clientType !== '0' && jobClients.applicantsApplied <= 0 &&(jobClients.applicantsNotAvailable + jobClients.applicantsNotInterested)<=0 ? (
                    <>
                        <h1
                            className="p-0 m-0 mt-3 font-bold"
                            style={{ fontSize: '22px', color: '#585858' }}
                        >
                            Your Job analytics
                        </h1>
                        <div className="flex gap-1 pl-2">
                            <div className="flex align-items-center">
                                <img
                                    src={fileCheck}
                                    alt="file check"
                                    width="12.4px"
                                    height="12.8px"
                                    className="text-gray-500"
                                />
                                <h1
                                    className={`p-1 m-0 font-semibold`}
                                    style={{ fontSize: '16px', color: '#585858' }}
                                >
                                    Invited: {totalInvited}
                                </h1>
                            </div>
                            <div className="flex align-items-center">
                                <FaRegEye
                                    className="text-gray-500"
                                    style={{ width: '12.4px', height: '12.8px' }}
                                />
                                <h1
                                    className={` p-1 m-0 font-semibold`}
                                    style={{ fontSize: '16px', color: '#585858' }}
                                >
                                    Viewed: {totalViewed}
                                </h1>
                            </div>
                        </div>
                    </>
                ) : (
                    <JobAnalytics job={jobClients} />
                )}

                {jobClients && (jobClients.isRecurringJob || jobClients.isTutoringJob) && (
                    <>
                        <WeeklyScheduleds
                            timeSlots={responseTable}
                            availabilityStatusEnum={c.applicantAvailabilityStatus}
                            totalInvited={totalInvited}
                            totalViewed={totalViewed}
                            job={jobClients}
                            availableApplicants={availableApplicants}
                        />
                    </>
                )}
                {shouldShow && (
                    <div
                        className="mt-4"
                        style={{
                            borderRadius: '20px',
                            border: '1px solid #DFDFDF',
                            marginRight: '2%',
                            padding: '20px',
                            width: isMobile ? '100%' : '952px',
                        }}
                    >
                        <div className="flex align-items-center gap-2 vertical-center">
                            {jobClients.managedBy === c.managedBy.SYSTEM &&
                                [1, 256].includes(jobClients.jobType) && (
                                    <span className="font-bold" style={{ fontSize: '22px', color: '#585858', }}>
                                        {jobClients.applicantsApplied === 0 ? 'Shortlist Applicants' : 'Applicants'}
                                    </span>
                                )}

                            {jobClients.managedBy === c.managedBy.USER &&
                                [1, 256].includes(jobClients.jobType) &&
                                jobClients.applicantsApplied > 0 && (
                                    <span className="font-bold mb-1" style={{ fontSize: '22px', color: '#585858'}}>
                                        Applicants
                                    </span>
                                )}

                            {jobClients.applicants.filter(
                                (a) => a.applicationStatus !== c.jobApplicationStatus.APPLIED
                            ).length > 0 &&
                                [1, 256].includes(jobClients.jobType) &&
                                jobClients.applicantsApplied > 0 && (
                                    <p className="p-0 m-0 font-bold" style={{ color: '#179d52', fontSize: '13px' }}>
                                        *{
                                            jobClients.applicants.filter(
                                                (a) =>
                                                    a.applicationStatus !== c.jobApplicationStatus.APPLIED &&
                                                    a.applicationStatus !== c.jobApplicationStatus.NOT_AVAILABLE
                                            ).length
                                        } New offer
                                        {
                                            jobClients.applicants.filter(
                                                (a) => a.applicationStatus !== c.jobApplicationStatus.APPLIED
                                            ).length > 1 ? 's' : ''
                                        }!
                                    </p>
                                )}
                        </div>

                        {renderApplicantsSection(jobClients)}

                        <ConfirmAwardDialog
                            title="Award Job"
                            isOpen={isDialogOpen}
                            onOpenChange={setIsDialogOpen}
                            onConfirm={() => {
                                if (selectedJobtId) {
                                    handleAwardJob(selectedJobtId);
                                    setIsDialogOpen(false);
                                }
                            }}
                            onCancel={() => {
                                setIsDialogOpen(false);
                                setSelectedJobId(null);
                            }}
                            name={
                                jobClients.applicants?.find((a) => a.applicantId === applicantId)?.applicantFirstName || ''
                            }
                            image={
                                jobClients.applicants?.find((a) => a.applicantId === applicantId)?.applicantImageSrc
                            }
                        />
                    </div>
                )}
                {jobClients.managedBy === 1 &&
                    (jobClients.jobType === 1 || jobClients.jobType === 256) && (
                        <div className="gap-3 mt-4"
                            style={{
                                borderRadius: '20px',
                                border: '1px solid #DFDFDF',
                                marginRight: '2%',
                                padding: '20px',
                                width: isMobile ? '100%' : '952px',
                            }}>
                            <div className="flex flex-column align-items-start mb-3">
                                <h4
                                    className={`p-0 m-0 mt-1 font-bold text-wrap`}
                                    style={{ fontSize: '22px', color: '#585858' }}
                                >
                                    Invited Helpers
                                </h4>
                            </div>
                            <div
                                className="flex-grow-1 grid cursor-pointer gap-1 "
                                style={{ width: 'auto' }}
                            >
                                {jobClients.applicants
                                    .filter(
                                        (a) =>
                                            a.applicationStatus !==
                                            c.jobApplicationStatus.APPLIED &&
                                            a.applicationStatus !==
                                            c.jobApplicationStatus.NOT_AVAILABLE
                                    )
                                    .map((v, i) => (
                                        <div
                                            className="h-min col-5 grid-nogutter pt-1 pb-1"
                                            key={i}
                                        >
                                            <div
                                                className="h-min flex gap-2 align-items-center px-2 py-2"
                                                style={{
                                                    border: ' 2px solid #179D52',
                                                    borderRadius: '10px',
                                                }}
                                            >
                                                <div className="flex align-items-center">
                                                    <div className=" relative">
                                                        <img
                                                            src={v.applicantImageSrc}
                                                            alt="img"
                                                            width="81px"
                                                            height="81px"
                                                            style={{
                                                                borderRadius: '50%',
                                                                objectFit: 'contain',
                                                                overflow: 'hidden',
                                                            }}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                redirectAfterHome(v.applicantId);
                                                            }}
                                                        />
                                                        {
                                                            v.completedJobsCount >= 15 && (
                                                                <div
                                                                    className="w-4 flex justify-content-start pl-"
                                                                    style={{ bottom: '6px' }}
                                                                >
                                                                    <div
                                                                        className="text-white font-bold text-center"
                                                                        style={{
                                                                            fontSize: '0.4em',
                                                                            backgroundColor: '#585858',
                                                                            padding: '3px 18px',
                                                                            paddingInline: '15px',
                                                                            borderRadius: '30px',
                                                                            userSelect: 'none',
                                                                            textWrap: 'nowrap',
                                                                            position: 'absolute',
                                                                            bottom: '0px',
                                                                        }}
                                                                    >
                                                                        Super Helper
                                                                    </div>
                                                                </div>
                                                            )
                                                        }

                                                    </div>
                                                </div>

                                                <div className="flex-grow-1 flex flex-column gap-1">
                                                    <p
                                                        className="m-0 p-0"
                                                        style={{
                                                            fontWeight: '700',
                                                            fontSize: '18px',
                                                            color: '#585858',
                                                        }}
                                                    >
                                                        {v.applicantFirstName}
                                                    </p>
                                                    <div className="flex gap-1 align-items-center">
                                                        <SlLocationPin
                                                            color="#37A950"
                                                            fontSize={'12px'}
                                                        />
                                                        <p
                                                            className="m-0 p-0"
                                                            style={{
                                                                fontWeight: '600',
                                                                fontSize: '12px',
                                                                color: '#585858',
                                                            }}
                                                        >
                                                            {v.suburb}
                                                        </p>
                                                    </div>
                                                    <div className="flex gap-1 align-items-center">
                                                        <img
                                                            src={starIcon}
                                                            alt="star"
                                                            width="16.71px"
                                                            height="18px"
                                                        />
                                                        <p
                                                            className="m-0 p-0"
                                                            style={{
                                                                fontWeight: '300',
                                                                fontSize: '12px',
                                                                color: '#585858',
                                                            }}
                                                        >
                                                            {v.completedJobsCount} Jobs completed
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    className="flex gap-2 mr-3"
                                                    style={{
                                                        backgroundColor: '#179D52',
                                                        padding: '10px',
                                                        borderRadius: '10px',
                                                    }}
                                                >
                                                    <p
                                                        className="m-0 p-0"
                                                        style={{
                                                            fontWeight: '700',
                                                            fontSize: '12px',
                                                            color: '#FFFFFF',
                                                        }}
                                                    >
                                                        Selected
                                                    </p>
                                                    <FaCheck color="#FFFFFF" />
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </div>
                    )}
                {clientType !== '0' && (
                    <div style={{ marginRight: '2%', width: isMobile ? '100%' : '952px' }}>
                        <JobFooter
                            expiryDate={jobClients.expiresInDays}
                            managedBy={jobClients.managedBy}
                            onCancelClicked={() => {
                                handleCancelJob(jobClients.id, '0');
                            }}
                            onEditClicked={() => {
                                if (!jobClients || !jobClients.id) {
                                    console.error('Job ID is missing');
                                    return;
                                }
                                const newParam = new URLSearchParams();
                                const action = duplicateJob ? 'Duplicate' : 'Edit';
                                newParam.set('jobaction', action);
                                // newParam.set('jobId',action)
                                if (Number(clientType) === 1) {
                                    navigate({
                                        pathname: `/parent-home/job/${jobClients.id}/job-type`,
                                        search: newParam.toString(),
                                    });
                                } else {
                                    navigate({
                                        pathname: `/business-home/job/${jobClients.id}/job-type`,
                                        search: newParam.toString(),
                                    });
                                }
                            }}
                            onInviteMoreCandidatesClicked={() => setManageRecruitment(true)}
                        />
                    </div>
                )}

                <ViewJobFull
                    visible={showfullJob}
                    onClose={() => {
                        setShowFullJob(false);
                    }}
                    job={jobClients}
                />
                <CustomDialog
                    visible={showPopup}
                    style={{
                        width: '100%',
                        maxWidth: '100%',
                        height: '100%',
                        maxHeight: '100%',
                        backgroundColor: '#ffffff',
                        borderRadius: '0px',
                        overflowY: 'auto',
                    }}
                    onHide={handleCloseProfilePopup}
                    draggable={false}
                >
                    <ProviderProfile
                        candidateId={selectedProviderId}
                        onClose={handleCloseProfilePopup}
                    />
                </CustomDialog>
            </>
        ) : (
            <>
                <ManageRecruitment
                    visible={manageRecruitment}
                    job={jobClients}
                    onClose={() => {
                        setManageRecruitment(false);
                        setUpdate((prev) => !prev);
                    }}
                />
                <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
                <Dialog
                    visible={duplicateJob}
                    onHide={() => {
                        setDuplicateJob(false);
                    }}
                    style={{
                        width: '100vw',
                        height: '100vh',
                        maxHeight: 'none',
                        maxWidth: 'none',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                    content={
                        <div
                            className="flex flex-column py-3 px-4"
                            style={{
                                backgroundColor: '#FFFFFF',
                                border: '1px solid #F0F4F7',
                                borderRadius: '20px',
                                maxWidth: '610px',
                            }}
                        >
                            <h1
                                className="m-0 p-0"
                                style={{
                                    fontWeight: '700',
                                    fontSize: '32px',
                                    color: '#585858',
                                }}
                            >
                                Duplicate Job
                            </h1>
                            <Divider />
                            <p
                                className="m-0 p-0 mt-3"
                                style={{
                                    fontWeight: '500',
                                    fontSize: '16px',
                                    color: '#585858',
                                }}
                            >
                                Use this feature to post a new job based on the current job details,
                                all applicable job settings are copied.
                            </p>
                            <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
                                <button
                                    className="px-4 py-1 cursor-pointer"
                                    style={{
                                        backgroundColor: '#FFFFFF',
                                        border: 'none',
                                        boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.25)',
                                        fontWeight: '500',
                                        fontSize: '18px',
                                        color: '#585858',
                                        textDecoration: 'underline',
                                        borderRadius: '5px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setDuplicateJob(false);
                                    }}
                                >
                                    No
                                </button>
                                <button
                                    className="px-7 py-1 cursor-pointer"
                                    style={{
                                        backgroundColor: '#FFA500',
                                        border: 'none',
                                        boxShadow: '0 4px 4px 0 rgba(0, 0, 0, 0.25)',
                                        fontWeight: '700',
                                        fontSize: '18px',
                                        color: '#FFFFFF',
                                        textDecoration: 'underline',
                                        borderRadius: '5px',
                                    }}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setDuplicateJob(false);
                                        const newParams = new URLSearchParams();
                                        newParams.set('jobId', String(jobClients.id));
                                        if (Number(clientType) === 1) {
                                            navigate({
                                                pathname: '/parent-home/post-job/duplicate',
                                                search: newParams.toString(),
                                            });
                                            return;
                                        }
                                        navigate({
                                            pathname: '/business-home/post-job/duplicate',
                                            search: newParams.toString(),
                                        });
                                    }}
                                >
                                    Yes
                                </button>
                            </div>
                        </div>
                    }
                />
                <div className="relative">
                    {/* Go Back Button */}
                    <div
                        className="flex gap-1 align-items-center w-min cursor-pointer mb-2"
                        style={{
                            textWrap: 'nowrap',
                            position: 'absolute',
                            left: '-10px',
                            top: '3px',
                            borderRadius: '20px',
                            zIndex: '2',
                        }}
                        // onClick={(e) => {
                        //   e.preventDefault();
                        //   const newGoBackParams = new URLSearchParams();
                        //   newGoBackParams.set("jobId", "-1");
                        //   newGoBackParams.set("activeTab", activeTab);
                        //   navigate({ search: newGoBackParams.toString() });
                        // }}
                        onClick={(e) => {
                            window.history.back();
                        }}
                    >
                        <div
                            style={{
                                backgroundColor: '#D9D9D94D',
                                paddingInline: '8px',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                paddingBlock: '8px',
                                borderRadius: '50%',
                            }}
                        >
                            <img src={BackArrow} alt="Arrow Left" width="10px" height="8px" />
                        </div>
                        <p
                            className="m-0 p-0"
                            style={{
                                fontWeight: '500',
                                fontSize: '14px',
                                color: '#585858',
                            }}
                        >
                            Go Back
                        </p>
                    </div>

                    {/* Job Summary Title */}
                    <div className="flex justify-content-center align-items-center relative">
                        <h3
                            className="font-bold"
                            style={{
                                color: '#585858',
                                fontSize: '18px',
                                margin: '0px',
                                textDecoration: 'underline',
                                marginTop: '0px',
                                marginBottom: '5px',
                            }}
                        >
                            Job Details
                        </h3>
                    </div>
                </div>

                <div className="mt-2">
                    {clientType !== '0' ? (
                        <div className={styles.summeryInfo}>
                            <span style={{ color: 'red' }}>*</span>Job post will expire in{' '}
                            <span style={{ color: 'red', fontSize: '15px', fontWeight: '600' }}>
                                {jobClients.expiresInDays}
                            </span>{' '}
                            days
                        </div>
                    ) : null}
                </div>
                <div
                    className="mt-2"
                    style={{
                        width: '100%',
                        borderRadius: '20px',
                    }}
                >
                    {!jobClients.isRecurringJob && (
                        <div>
                            <div
                                style={{
                                    fontSize: '14px',
                                    color: '#585858',
                                    fontWeight: '700',
                                }}
                            >
                                {`${getJobDetails(jobClients).label} - ${jobClients.isTutoringJob
                                    ? 'Tutoring Job'
                                    : jobClients.isRecurringJob
                                        ? 'Recurring Job'
                                        : 'One Off Job'
                                    }`}
                            </div>
                        </div>
                    )}

                    <p className={`${styles.summeryInfo} m-0 p-0`}>
                        {jobClients.isRecurringJob
                            ? 'You are looking to fill a recurring job starting on'
                            : jobClients.isTutoringJob
                                ? 'Starting on'
                                : ''}{' '}
                        {formatShortDate(jobClients?.jobDate)},
                        {clientType === '0' ? (
                            <Tag style={{ background: 'transparent', width: 'max-content' }}>
                                <div className={styles.tagDataMobile}>
                                    <img
                                        alt="Clock"
                                        src={clockStart}
                                        style={{ width: '14px', color: '#FFFFFF' }}
                                    />
                                    {jobClients.durationType === null
                                        ? convertTo12HourFormat(
                                            `${jobClients.jobStartTime}-${jobClients.jobEndTime}`
                                        )
                                        : `${jobClients.duration} week duration`}
                                </div>
                            </Tag>
                        ) : jobClients.isTutoringJob ? (
                            ` for ${jobClients.duration} weeks`
                        ) : jobClients.isRecurringJob ? (
                            ` for ${jobClients.duration} weeks`
                        ) : (
                            convertTo12HourFormat(
                                `${jobClients['jobStartTime']}-${jobClients['jobEndTime']}`
                            )
                        )}
                    </p>
                    <div className="flex flex-column ">
                        <div className="flex flex-column ">
                            {(jobClients.isRecurringJob || jobClients.isTutoringJob) && (
                                <p className={styles.addressTag}>{jobClients.addressLabel}</p>
                            )}
                            {clientType !== '0' ? (
                                <div className={styles.summeryInfo}>{`${utils.cleanAddress(
                                    jobClients.formattedAddress
                                )}`}</div>
                            ) : null}
                        </div>

                        <div className="flex flex-column mt-3">
                            {!jobClients.isRecurringJob && (
                                <p
                                    className="m-0 p-0"
                                    style={{
                                        fontSize: '14px',
                                        color: '#585858',
                                        fontWeight: '700',
                                    }}
                                >
                                    Payment
                                </p>
                            )}
                            {jobClients.isTutoringJob && (
                                <>
                                    <div
                                        className="flex"
                                        style={{ color: '#585858', fontSize: '14px' }}
                                    >
                                        Payment: $&nbsp;
                                        <p className="m-0 p-0">{`${jobClients.price} p/h`}</p>
                                    </div>
                                </>
                            )}
                            {clientType !== '0' && jobClients.price ? (
                                <div className={`${styles.jobPrice}`}>
                                    {/* Tutoring Job */}
                                    {jobClients.isTutoringJob && (
                                        <div
                                            className="flex"
                                            style={{ color: '#585858', fontSize: '14px' }}
                                        >
                                            Total Weekly Payment: ${jobClients.pricePerWeek}
                                        </div>
                                    )}

                                    {/* Recurring Job */}
                                    {/* {jobClients.isRecurringJob && (
      <div className="flex" style={{ color: "#585858", fontSize: "14px" }}>
        Weekly Job Total = 
        <p className="m-0 p-0">${jobClients.pricePerWeek}</p>
      </div>
    )} */}

                                    {/* Odd Job (one-time job) */}
                                    {!jobClients.isTutoringJob && !jobClients.isRecurringJob && (
                                        <>
                                            <div
                                                className="flex"
                                                style={{ color: '#585858', fontSize: '14px' }}
                                            >
                                                Price: $&nbsp;
                                                <p className="m-0 p-0">
                                                    {jobClients.paymentType !== 1
                                                        ? `${jobClients.price} p/h`
                                                        : `${jobClients.price} total`}
                                                </p>
                                            </div>

                                            {jobClients.overtimeRate && (
                                                <div className={`${styles.summeryInfo} flex`}>
                                                    Extra hours rate: ${jobClients.overtimeRate} per
                                                    hour
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                            ) : (
                                ''
                            )}
                            {!jobClients.isRecurringJob && (
                                <div className={`${styles.summeryInfo}`}>
                                    Payment Method:{' '}
                                    {jobClients.helperPaymentMethod === 1
                                        ? 'Cash payment'
                                        : 'Bank transfer'}
                                </div>
                            )}
                            {jobClients.isRecurringJob && (
                                <div className={`${styles.summeryInfo} font-bold`}>
                                    Payment Method
                                    <p className="p-0 m-0 font-normal">
                                        {jobClients.helperPaymentMethod === 1
                                            ? 'Cash payment'
                                            : 'Bank transfer'}
                                    </p>
                                </div>
                            )}

                            {jobClients.isTutoringJob && (
                                <div className={`${styles.summeryInfo}`}>
                                    Tutoring Type:{' '}
                                    {jobClients.jobDeliveryMethod === 1
                                        ? 'In-home Tutoring'
                                        : 'Online Tutoring'}
                                </div>
                            )}

                            {responseTable.length > 0 && jobClients.isRecurringJob && (
                                <div className="mt-3" style={{ overflowX: 'auto' }}>
                                    <table
                                        style={{
                                            width: '100%',
                                            borderCollapse: 'collapse',
                                            fontSize: '12px',
                                        }}
                                    >
                                        <thead>
                                            <tr>
                                                <th
                                                    style={{
                                                        padding: '4px',
                                                        textAlign: 'center',
                                                        borderBottom: '1px solid #ddd',
                                                        color: '#585858',
                                                    }}
                                                ></th>
                                                <th
                                                    style={{
                                                        padding: '4px',
                                                        textAlign: 'center',
                                                        borderBottom: '1px solid #ddd',
                                                        color: '#585858',
                                                    }}
                                                ></th>
                                                <th
                                                    style={{
                                                        padding: '4px',
                                                        textAlign: 'center',
                                                        borderBottom: '1px solid #ddd',
                                                        color: '#585858',
                                                    }}
                                                >
                                                    Hours
                                                </th>
                                                <th
                                                    style={{
                                                        padding: '4px',
                                                        textAlign: 'center',
                                                        borderBottom: '1px solid #ddd',
                                                        color: '#585858',
                                                    }}
                                                >
                                                    Rate
                                                </th>
                                                <th
                                                    style={{
                                                        padding: '4px',
                                                        textAlign: 'center',
                                                        borderBottom: '1px solid #ddd',
                                                        color: '#585858',
                                                    }}
                                                >
                                                    Total
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {responseTable.map((shift, index) => {
                                                const hours = utils.getTimeDifference(
                                                    shift.startTime,
                                                    shift.endTime
                                                );
                                                const rate = calculateHourlyRate(
                                                    shift.price,
                                                    hours
                                                );

                                                return (
                                                    <tr
                                                        key={index}
                                                        style={{ borderBottom: '1px solid #ddd' }}
                                                    >
                                                        <td
                                                            style={{
                                                                padding: '4px',
                                                                color: '#585858',
                                                                fontWeight: '700',
                                                            }}
                                                        >
                                                            {shift.day}
                                                        </td>
                                                        <td
                                                            style={{
                                                                padding: '4px',
                                                                color: '#585858',
                                                            }}
                                                        >
                                                            {formatTime(shift.startTime)} to{' '}
                                                            {formatTime(shift.endTime)}
                                                        </td>
                                                        <td
                                                            style={{
                                                                padding: '4px',
                                                                color: '#585858',
                                                                textAlign: 'center',
                                                            }}
                                                        >
                                                            {hours}
                                                        </td>
                                                        <td
                                                            style={{
                                                                padding: '4px',
                                                                color: '#585858',
                                                                textAlign: 'center',
                                                            }}
                                                        >
                                                            ${rate.toFixed(2)}
                                                        </td>
                                                        <td
                                                            style={{
                                                                padding: '4px',
                                                                color: '#585858',
                                                                textAlign: 'center',
                                                            }}
                                                        >
                                                            ${shift.price.toFixed(2)}
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                            {/* Total row */}
                                            {responseTable.length >= 1 && (
                                                <tr style={{ fontWeight: 'bold' }}>
                                                    <td
                                                        style={{ padding: '4px', color: '#585858' }}
                                                        colSpan={2}
                                                    ></td>
                                                    <td
                                                        style={{
                                                            padding: '4px',
                                                            color: '#585858',
                                                            textAlign: 'center',
                                                        }}
                                                    >
                                                        {responseTable.reduce(
                                                            (sum, shift) =>
                                                                sum +
                                                                utils.getTimeDifference(
                                                                    shift.startTime,
                                                                    shift.endTime
                                                                ),
                                                            0
                                                        )}
                                                    </td>
                                                    <td
                                                        style={{ padding: '4px', color: '#585858' }}
                                                    ></td>
                                                    <td
                                                        style={{
                                                            padding: '4px',
                                                            color: '#585858',
                                                            textAlign: 'center',
                                                        }}
                                                    >
                                                        $
                                                        {responseTable
                                                            .reduce(
                                                                (sum, shift) => sum + shift.price,
                                                                0
                                                            )
                                                            .toFixed(2)}
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                            {/* <div className="flex flex-wrap gap-1">
                <p className={`${styles.yourJobDescription} p-0 m-0`}>{(jobClients.isTutoringJob || jobClients.isRecurringJob) && <WeeklySummary />}</p>
              </div> */}
                        </div>
                    </div>
                    <div>
                        {clientType === '0' ? (
                            <div>
                                <div
                                    className="shadow-2 "
                                    style={{
                                        backgroundColor: '#ffffff',
                                        borderRadius: '20px',
                                        border: '2px solid #179D52',
                                        paddingBlock: '20px',
                                        marginTop: '20px',
                                    }}
                                >
                                    <div className="flex flex-wrap justify-content-between gap-2 flex-column">
                                        {jobClients.ownerPublicName && (
                                            <div className="flex align-items-center text-center gap-1 px-3">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Invited:{' '}
                                                </h4>
                                                <p
                                                    className={`${styles.parentInviteMobile} p-0 m-0 underline cursor-pointer`}
                                                    onClick={() =>
                                                        redirectAfterHome(jobClients.jobOwnerId)
                                                    }
                                                >
                                                    {jobClients.ownerPublicName} has invited you!
                                                </p>
                                                <img
                                                    src={jobClients.ownerImageSrc}
                                                    alt="ownerImageSrc"
                                                    width="33px"
                                                    height="33px"
                                                    style={{
                                                        borderRadius: '50%',
                                                        border: '1px solid #179D52',
                                                    }}
                                                />
                                            </div>
                                        )}
                                        <Divider />
                                        {jobClients.price && (
                                            <div className="flex align-items-center text-center gap-1 px-3">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Payment:{' '}
                                                </h4>
                                                <p className={`${styles.helperinfoMobile} m-0`}>
                                                    ${jobClients.price} p/h{' '}
                                                    {jobClients.isPriceNegotiable && 'Negotiable'}
                                                </p>
                                            </div>
                                        )}

                                        {jobClients.pricePerWeek && (
                                            <div className="flex align-items-center text-center gap-1 px-3">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Total Weekly Payment:{' '}
                                                </h4>
                                                <p className={`${styles.helperinfoMobile}  m-0`}>
                                                    ${jobClients.pricePerWeek}
                                                </p>
                                            </div>
                                        )}

                                        {jobClients.overtimeRate && (
                                            <div className="flex align-items-center text-center gap-1 px-3">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Extra hours rate:{' '}
                                                </h4>
                                                <p className={`${styles.helperinfoMobile} m-0`}>
                                                    ${jobClients.overtimeRate} per hour
                                                </p>
                                            </div>
                                        )}

                                        {jobClients.ownerPhoneNumber && (
                                            <div className="flex align-items-center text-center gap-1 px-3">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Phone Number:{' '}
                                                </h4>
                                                <p className={`${styles.helperinfoMobile}  m-0`}>
                                                    {jobClients.ownerPhoneNumber}
                                                </p>
                                            </div>
                                        )}

                                        {jobClients.gettingHome !== undefined &&
                                            jobClients.gettingHome !== null &&
                                            jobClients.gettingHome !== 0 && (
                                                <div className="flex align-items-center text-center gap-1 px-3">
                                                    <h4
                                                        className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                    >
                                                        Getting Home:{' '}
                                                    </h4>
                                                    <p
                                                        className={`${styles.helperinfoMobile}  m-0`}
                                                    >
                                                        {getGettingHomeLabel(
                                                            jobClients.gettingHome
                                                        )}
                                                    </p>
                                                </div>
                                            )}

                                        {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                            jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                            ? jobDeliveryMethodText && (
                                                <div className="flex align-items-center text-center gap-1 px-3">
                                                    <h4
                                                        className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                    >
                                                        Tutoring Type:{' '}
                                                    </h4>
                                                    <p
                                                        style={{ backgroundColor: '#EDEDED' }}
                                                        className={`${styles.helperinfoMobile} m-0`}
                                                    >
                                                        {jobDeliveryMethodText}
                                                    </p>
                                                </div>
                                            )
                                            : null}

                                        {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                            jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                            ? getSchoolYears(jobClients) && (
                                                <div className="flex align-items-center text-center gap-1 px-3">
                                                    <h4
                                                        className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                    >
                                                        Required School Year(s):{' '}
                                                    </h4>
                                                    <p
                                                        className={`${styles.helperinfoMobile}  m-0`}
                                                    >
                                                        {getSchoolYears(jobClients)}
                                                    </p>
                                                </div>
                                            )
                                            : null}

                                        {jobClients.jobType === c.jobType.PRIMARY_SCHOOL_TUTORING ||
                                            jobClients.jobType === c.jobType.HIGH_SCHOOL_TUTORING
                                            ? getSchoolSubjects(jobClients) && (
                                                <div className="flex align-items-center text-center gap-1 px-3">
                                                    <h4
                                                        className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                    >
                                                        Required Subject:{' '}
                                                    </h4>
                                                    <p
                                                        className={`${styles.helperinfoMobile} m-0`}
                                                    >
                                                        {getSchoolSubjects(jobClients)}
                                                    </p>
                                                </div>
                                            )
                                            : null}
                                    </div>

                                    {(jobClients.formattedAddress || jobClients.suburb) && (
                                        <div className="flex align-items-center text-center gap-1 mt-2 px-3">
                                            <h4 className={`${styles.headerH4MobileSec} p-0 m-0`}>
                                                Location:{' '}
                                            </h4>
                                            <p className={`${styles.helperinfoMobile}  m-0`}>
                                                {jobClients.formattedAddress
                                                    ? jobClients.formattedAddress
                                                    : jobClients.suburb}
                                            </p>
                                        </div>
                                    )}

                                    {jobClients.applicantAvailability.length > 0
                                        ? jobClients.applicantAvailability && (
                                            <div className="flex text-center gap-1 mt-2 px-3 ">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0`}
                                                >
                                                    Required Days:{' '}
                                                </h4>
                                                <div className="flex flex-wrap gap-1">
                                                    {Array.from(
                                                        new Set(
                                                            jobClients.applicantAvailability
                                                                .filter(
                                                                    (a) => a.isRequired === true
                                                                )
                                                                .map(
                                                                    (a) => daysOfWeek[a.dayOfWeek]
                                                                )
                                                        )
                                                    ).map((day, index) => (
                                                        <span
                                                            key={index}
                                                            className={`${styles.helperinfoMobile} m-0`}
                                                        >
                                                            {day}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        )
                                        : null}
                                </div>
                            </div>
                        ) : null}
                    </div>
                </div>
                <div
                    className="py-2 px-3 mt-3"
                    style={{ backgroundColor: '#BBBBBB33', borderRadius: '20px' }}
                >
                    <h4
                        style={{
                            marginTop: '0px',
                            fontSize: '16px',
                            marginBottom: '0px',
                            color: '#585858',
                        }}
                    >
                        Job Description
                    </h4>
                    <p
                        style={{ marginTop: '5px', fontSize: '14px' }}
                        className={styles.yourJobDescriptionMobile}
                    >
                        {isExpanded
                            ? jobClients.specialInstructions
                            : truncateText(jobClients.specialInstructions, wordLimit)}
                    </p>
                    {jobClients.specialInstructions.split(' ').length > wordLimit && (
                        <button
                            className={styles.seeMoreButton}
                            onClick={() => setIsExpanded(!isExpanded)}
                        >
                            {isExpanded ? 'See Less' : 'See More'}
                        </button>
                    )}
                </div>
                {!jobClients.isRecurringJob && !jobClients.isTutoringJob && clientType !== '0' && (
                    <div
                        className="mt-2 pt-0"
                        style={{
                            borderRadius: '20px',
                            width: isMobile ? '100%' : '952px',
                        }}
                    >
                        <div className="flex align-items-center gap-2 vertical-center">
                            {jobClients.managedBy === c.managedBy.SYSTEM &&
                                [1, 256].includes(jobClients.jobType) && (
                                    <>
                                        <span
                                            className="font-bold"
                                            style={{
                                                fontSize: '18px',
                                                color: '#585858',
                                                textDecoration: 'underline',
                                            }}
                                        >
                                            {jobClients.applicantsApplied === 0
                                                ? 'Shortlist Applicants'
                                                : 'Applicants'}
                                        </span>
                                    </>
                                )}

                            {jobClients.managedBy === c.managedBy.USER &&
                                [1, 256].includes(jobClients.jobType) &&
                                jobClients.applicantsApplied > 0 && (
                                    <>
                                        <span
                                            className="font-bold"
                                            style={{
                                                fontSize: '18px',
                                                color: '#585858',
                                                textDecoration: 'underline',
                                            }}
                                        >
                                            Applicants
                                        </span>
                                    </>
                                )}
                            {jobClients.applicants.filter(
                                (a) => a.applicationStatus !== c.jobApplicationStatus.APPLIED
                            ).length > 0 &&
                                [1, 256].includes(jobClients.jobType) &&
                                jobClients.applicantsApplied > 0 && (
                                    <p
                                        className="p-0 m-0 font-bold"
                                        style={{ color: '#179d52', fontSize: '12px' }}
                                    >
                                        *
                                        {
                                            jobClients.applicants.filter(
                                                (a) =>
                                                    a.applicationStatus !==
                                                    c.jobApplicationStatus.APPLIED &&
                                                    a.applicationStatus !==
                                                    c.jobApplicationStatus.NOT_AVAILABLE
                                            ).length
                                        }{' '}
                                        New offer
                                        {jobClients.applicants.filter(
                                            (a) =>
                                                a.applicationStatus !==
                                                c.jobApplicationStatus.APPLIED
                                        ).length > 1
                                            ? 's'
                                            : ''}
                                        !
                                    </p>
                                )}
                        </div>

                        {jobClients.applicants?.length === 0 &&
                            jobClients?.applicantsApplied === 0 &&
                            jobClients.managedBy === 20 ? (
                            <NoJobsCard description="No jobs match the specified criteria" />
                        ) : (
                            (() => {
                                const availableApplicants: Applicants[] = [];
                                jobClients.applicants?.forEach((a) => {
                                    const status = a.applicationStatus as 5 | 9 | 11;
                                    if (
                                        [
                                            c.jobApplicationStatus.APPLIED,
                                            c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM,
                                            c.jobApplicationStatus.SHORTLISTED_BY_CLIENT,
                                            c.jobApplicationStatus.EXCLUDED_BY_CLIENT,
                                        ].includes(status)
                                    ) {
                                        availableApplicants.push(a);
                                    }
                                });

                                return availableApplicants.length === 0 &&
                                    jobClients.managedBy === 20 ? (
                                    <NoJobsCard description="Sit tight Helpers will apply to your job soon!" />
                                ) : (
                                    availableApplicants.map((a, index) => (
                                        <div className="mt-2" key={index}>
                                            <AwardCard
                                                // avgRating={a.applicantRatingsAvg}
                                                // numRatings={a.applicantRatingsCount}
                                                helperImgSrc={a.applicantImageSrc}
                                                location={a.suburb}
                                                transport={(() => {
                                                    switch (a.gettingHome) {
                                                        case 0:
                                                            return 'Unspecified';
                                                        case 1:
                                                            return 'Needs you to provide transport';
                                                        case 2:
                                                            return 'Walking';
                                                        case 3:
                                                            return 'Public Transport';
                                                        case 4:
                                                            return 'Car (Being picked up)';
                                                        case 5:
                                                            return 'Driving';
                                                        case 6:
                                                            return 'Uber / Taxi';
                                                        default:
                                                            return 'Not Available';
                                                    }
                                                })()}
                                                applicationStatus={a.applicationStatus}
                                                publicName={`${a.applicantFirstName || 'name'} ${a.applicantLastInitial || 'Anonymous'
                                                    }`}
                                                isSuperHelper={a.completedJobsCount >= 15}
                                                onViewProfileClicked={() => {
                                                    IframeBridge.sendToParent({
                                                        type: 'navigateToHelperProfile',
                                                        data: {
                                                            id: String(a.applicantId),
                                                        },
                                                    });
                                                    redirectAfterHome(a.applicantId);
                                                }}
                                                onAwardJobClicked={() => {
                                                    setSelectedJobId(jobClients.id);
                                                    setApplicantId(a.applicantId);
                                                    setIsDialogOpen(true);
                                                }}
                                                isOneoffJob={
                                                    !jobClients.isTutoringJob ||
                                                    !jobClients.isRecurringJob
                                                }
                                                // responseRate={a.responseRate}
                                                onContactClicked={() => {
                                                    IframeBridge.sendToParent({
                                                        type: 'navigateChatWithId',
                                                        data: {
                                                            id: String(a.applicantId),
                                                        },
                                                    });
                                                    if (clientType === '1') {
                                                        navigate(
                                                            `/parent-home/inAppChat?userId=${a.applicantId}`
                                                        );
                                                    } else {
                                                        navigate(
                                                            `/business-home/inAppChat?userId=${a.applicantId}`
                                                        );
                                                    }
                                                }}
                                                applicantId={a.applicantId}
                                            />
                                        </div>
                                    ))
                                );
                            })()
                        )}

                        <ConfirmAwardDialog
                            title="Confirm Award"
                            isOpen={isDialogOpen}
                            onOpenChange={setIsDialogOpen}
                            onConfirm={() => {
                                if (selectedJobtId) {
                                    handleAwardJob(selectedJobtId);
                                    setIsDialogOpen(false);
                                }
                            }}
                            onCancel={() => {
                                setIsDialogOpen(false);
                                setSelectedJobId(null);
                            }}
                            name={
                                jobClients.applicants?.find((a) => a.applicantId === applicantId)
                                    ?.applicantFirstName || ''
                            }
                            image={
                                jobClients.applicants?.find((a) => a.applicantId === applicantId)
                                    ?.applicantImageSrc
                            }
                        />
                    </div>
                )}

                {jobClients.managedBy === 1 &&
                    (jobClients.jobType === 1 || jobClients.jobType === 256) && (
                        <div className="gap-3">
                            <div className="">
                                {jobClients.applicants.filter(
                                    (a) => a.applicationStatus !== c.jobApplicationStatus.APPLIED
                                ).length > 0 && (
                                        <>
                                            <h1
                                                className="p-0 m-0 mt-2"
                                                style={{ fontSize: '16px', color: '#585858' }}
                                            >
                                                Your Job analytics
                                            </h1>
                                            <div className="flex gap-1">
                                                <div className="flex align-items-center">
                                                    <img
                                                        src={fileCheck}
                                                        alt="file check"
                                                        width="12.4px"
                                                        height="12.8px"
                                                        className="text-gray-500"
                                                    />
                                                    <h1
                                                        className={`p-1 m-0 font-semibold`}
                                                        style={{ fontSize: '16px', color: '#585858' }}
                                                    >
                                                        Invited: {totalInvited}
                                                    </h1>
                                                </div>
                                                <div className="flex align-items-center">
                                                    <FaRegEye
                                                        className="text-gray-500"
                                                        style={{ width: '12.4px', height: '12.8px' }}
                                                    />
                                                    <h1
                                                        className={` p-1 m-0 font-semibold`}
                                                        style={{ fontSize: '16px', color: '#585858' }}
                                                    >
                                                        Viewed: {totalViewed}
                                                    </h1>
                                                </div>
                                            </div>
                                            <div className="flex flex-column align-items-start">
                                                <h4
                                                    className={`${styles.headerH4MobileSec} p-0 m-0 mt-1 underline`}
                                                >
                                                    Invited Candidates
                                                </h4>
                                            </div>
                                        </>
                                    )}
                            </div>

                            <div className="flex-grow-1 grid grid-nogutter cursor-pointer">
                                {jobClients.applicants
                                    .filter(
                                        (a) =>
                                            a.applicationStatus !==
                                            c.jobApplicationStatus.APPLIED &&
                                            a.applicationStatus !==
                                            c.jobApplicationStatus.NOT_AVAILABLE
                                    )
                                    .map((v, i) => (
                                        <div
                                            className="h-min col-12 grid-nogutter pt-1 pb-1"
                                            key={i}
                                        >
                                            <div
                                                className="h-min flex gap-2 align-items-center px-2 py-2"
                                                style={{
                                                    border: ' 2px solid #179D52',
                                                    borderRadius: '10px',
                                                }}
                                            >
                                                <div className="flex align-items-center">
                                                    <div className=" relative">
                                                        <img
                                                            src={v.applicantImageSrc}
                                                            alt="img"
                                                            width="81px"
                                                            height="81px"
                                                            style={{
                                                                borderRadius: '50%',
                                                                objectFit: 'contain',
                                                                overflow: 'hidden',
                                                            }}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                viewProvider(v.applicantId);
                                                            }}
                                                        />
                                                        <div
                                                            className="w-4 flex justify-content-start pl-"
                                                            style={{ bottom: '6px' }}
                                                        >
                                                            <div
                                                                className="text-yellow-300 font-bold text-center"
                                                                style={{
                                                                    fontSize: '0.4em',
                                                                    backgroundColor: 'white',
                                                                    padding: '3px 20px',
                                                                    paddingInline: '15px',
                                                                    borderRadius: '30px',
                                                                    userSelect: 'none',
                                                                    textWrap: 'nowrap',
                                                                    position: 'absolute',
                                                                    bottom: '0px',
                                                                }}
                                                            >
                                                                View profile
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="flex-grow-1 flex flex-column gap-1">
                                                    <p
                                                        className="m-0 p-0"
                                                        style={{
                                                            fontWeight: '700',
                                                            fontSize: '18px',
                                                            color: '#585858',
                                                        }}
                                                    >
                                                        {v.applicantFirstName}
                                                    </p>
                                                    <div className="flex gap-1 align-items-center">
                                                        <SlLocationPin
                                                            color="#37A950"
                                                            fontSize={'12px'}
                                                        />
                                                        <p
                                                            className="m-0 p-0"
                                                            style={{
                                                                fontWeight: '600',
                                                                fontSize: '12px',
                                                                color: '#585858',
                                                            }}
                                                        >
                                                            {v.suburb}
                                                        </p>
                                                    </div>
                                                    <div className="flex gap-1 align-items-center">
                                                        <img
                                                            src={starIcon}
                                                            alt="star"
                                                            width="16.71px"
                                                            height="18px"
                                                        />
                                                        <p
                                                            className="m-0 p-0"
                                                            style={{
                                                                fontWeight: '300',
                                                                fontSize: '12px',
                                                                color: '#585858',
                                                            }}
                                                        >
                                                            {v.completedJobsCount} Jobs completed
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    className="flex gap-2 mr-3"
                                                    style={{
                                                        backgroundColor: '#179D52',
                                                        padding: '10px',
                                                        borderRadius: '10px',
                                                    }}
                                                >
                                                    <p
                                                        className="m-0 p-0"
                                                        style={{
                                                            fontWeight: '700',
                                                            fontSize: '12px',
                                                            color: '#FFFFFF',
                                                        }}
                                                    >
                                                        Selected
                                                    </p>
                                                    <FaCheck color="#FFFFFF" />
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </div>
                    )}

                {clientType === '0' && (
                    <ApplyFor
                        job={jobClients}
                        timeSlots={[]}
                        availabilityStatusEnum={undefined}
                        totalInvited={0}
                        totalViewed={0}
                        availableApplicants={[]}
                    />
                )}
                {/* {clientType !== "0" && <JobAnalytics job={jobClients} />} */}
                {jobClients && (jobClients.isRecurringJob || jobClients.isTutoringJob) && (
                    <>
                        <WeeklyScheduleds
                            timeSlots={responseTable}
                            availabilityStatusEnum={c.applicantAvailabilityStatus}
                            totalInvited={totalInvited}
                            totalViewed={totalViewed}
                            job={jobClients}
                            availableApplicants={availableApplicants}
                        />
                    </>
                )}

                {clientType !== '0' && (
                    <div style={{ margin: '0px', width: '100%' }}>
                        <JobFooter
                            expiryDate={jobClients.expiresInDays}
                            managedBy={jobClients.managedBy}
                            onCancelClicked={() => {
                                handleCancelJob(jobClients.id, '0');
                            }}
                            onEditClicked={() => {
                                if (!jobClients || !jobClients.id) {
                                    console.error('Job ID is missing');
                                    return;
                                }
                                IframeBridge.sendToParent({
                                    type: 'navigateToPostJob',
                                    data: {
                                        id: jobClients.id,
                                        action: 'Edit',
                                    },
                                });
                                if (!inIframe) {
                                    const newParam = new URLSearchParams();
                                    const action = duplicateJob ? 'Duplicate' : 'Edit';
                                    newParam.set('jobaction', action);
                                    // newParam.set('jobId',action)
                                    if (Number(clientType) === 1) {
                                        navigate({
                                            pathname: `/parent-home/job/${jobClients.id}/job-type`,
                                            search: newParam.toString(),
                                        });
                                    } else {
                                        navigate({
                                            pathname: `/business-home/job/${jobClients.id}/job-type`,
                                            search: newParam.toString(),
                                        });
                                    }
                                }
                            }}
                            onInviteMoreCandidatesClicked={() => setManageRecruitment(true)}
                        />
                    </div>
                )}

                <ViewJobFull
                    visible={showfullJob}
                    onClose={() => {
                        setShowFullJob(false);
                    }}
                    job={jobClients}
                />
            </>
        )
    ) : (
        <Loader />
    );
};

export default JobSummaryUnAwarded;
