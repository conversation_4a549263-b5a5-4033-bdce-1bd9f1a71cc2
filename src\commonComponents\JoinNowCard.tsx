// import { Card } from 'primereact/card';
import React, { ReactNode } from 'react';
import Header from './Header';
import '../components/utils/util.css';
import logoImage from '../assets/images/juggle-st-transparent-card.png'; // Adjust the path to your image

interface CardProps {
    children: ReactNode;
    className?: string;
    altText?: string;
}

const JoinNowCard: React.FC<CardProps> = ({ children, altText = 'jugglestreet' }) => {
    return (
        <>
            <Header />
            <div
                className="flex justify-content-center align-items-center"
                style={{ paddingBlock: '8%',backgroundColor:'#f0f4f7',minBlockSize: '90vh',paddingTop: '50px' }}
            >
                <div
                    className="bg-white"
                    style={{
                        // marginTop: '9vh',
                        width: '607px',
                        // minHeight: '687px',
                        borderRadius: '10px',
                        display: 'flex',
                        zIndex: '0',
                        position: 'relative',
                        flexDirection: 'column',
                        boxShadow: '0 4px 4px 0 rgb(0,0,0,0.25)',
                    }}
                >
                    <div className="text-center">
                        <img
                            loading="lazy"
                            src={logoImage}
                            alt={altText}
                            style={{ width: '239px', height: '129px' }}
                        />
                    </div>
                    {children}
                </div>
            </div>
        </>
    );
};

export default JoinNowCard;
