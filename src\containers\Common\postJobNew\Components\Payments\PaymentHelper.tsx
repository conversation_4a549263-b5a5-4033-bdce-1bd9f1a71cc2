import React, { ChangeEvent, useEffect, useState } from "react";
import { Divider } from "primereact/divider";

import Slimily from "../../../../assets/images/Icons/my_child.png";
import Book from "../../../../assets/images/Icons/book.png";
import <PERSON><PERSON><PERSON> from "../../../../assets/images/Icons/odd_job.png";
import AON from "../../../../assets/images/aon.png";
import CheckGreen from "../../../../assets/images/Icons/check-green.png";
import Lock from "../../../../assets/images/Icons/lock.png";
import LockBlack from "../../../../assets/images/Icons/lock-black.png";
import { useDispatch, useSelector } from "react-redux";
import { CardCvcElement, CardExpiryElement, CardNumberElement, Elements, useElements, useStripe } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { GoBack } from "../Buttons";
import useLoader from "../../../../../hooks/LoaderHook";
import { AppDispatch, RootState } from "../../../../../store";
import Service from "../../../../../services/services";
import c from "../../../../../helper/juggleStreetConstants";
import { refreshAccount } from "../../../../../store/tunks/sessionInfoTunk";
import environment from "../../../../../helper/environment";
import { PayloadTemplate, useJobManager } from "../../provider/JobManagerProvider";

function CustomInput<T>({
  label,
  onChange,
  mask,
  maskWith,
  showAstrict = false,
}: {
  label: string;
  onChange: (value: T) => void;
  mask?: string;
  maskWith?: string;
  showAstrict?: boolean;
}) {
  const [inputValue, setInputValue] = useState<string>("");

  function applyMask(value: string, mask: string): string {
    let maskedValue = "";
    let maskIndex = 0;

    for (let i = 0; i < value.length; i++) {
      if (maskIndex >= mask.length) {
        break;
      }

      if (mask[maskIndex] === maskWith) {
        maskedValue += maskWith;
        maskIndex++;
      }

      if (/\d/.test(mask[maskIndex])) {
        maskedValue += value[i];
        maskIndex++;
      }
    }

    return maskedValue;
  }

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    let newValue = e.target.value;
    if (newValue.trim() === "") {
      setInputValue("");
      onChange("" as unknown as T);
      return;
    }

    if (mask) {
      newValue = applyMask(newValue.replace(/\D/g, ""), mask);
    } else {
      newValue = newValue.replace(/\d/g, "");
    }

    setInputValue(newValue);
    onChange(newValue as unknown as T);
  }

  return (
    <div
      className="flex relative"
      style={{
        borderBottom: "1px solid #F1F1F1",
        position: "relative",
      }}
    >
      <input
        className="flex-grow-1 border-none px-3 py-2 w-full"
        style={{
          color: "#787777",
          fontSize: "14px",
        }}
        type="text"
        name={label.replace(/ /g, "").toLowerCase()}
        id={label.replace(/ /g, "").toLowerCase()}
        value={inputValue}
        onChange={handleChange}
      />
      {!inputValue && (
        <label
          className="absolute mx-3"
          style={{
            color: "#787777",
            fontSize: "14px",
            top: "50%",
            transform: "translateY(-50%)",
            pointerEvents: "none",
          }}
          htmlFor={label.replace(/ /g, "").toLowerCase()}
        >
          {label}{" "}
          {showAstrict && (
            <span
              style={{
                color: "#FF6359",
              }}
            >
              *
            </span>
          )}
        </label>
      )}
    </div>
  );
}

interface PostJobProps {
  currentPayload: ReturnType<typeof useJobManager>["payload"];
  prevClicked: ReturnType<typeof useJobManager>["prev"];
  nextClicked: ReturnType<typeof useJobManager>["next"];
  setPayload: ReturnType<typeof useJobManager>["setpayload"];
}

interface ViewComponentProps extends PostJobProps {
  selectedPlan: number;
  walletBalance: number;
  walletBalanceDecimal: number;
  subscirbeEnabled: boolean;
  changePlan: (update: number) => void;
  toggleView: () => void;
}

interface View {
  render: (props: ViewComponentProps) => React.JSX.Element;
}

const Price: View = {
  render: (props: ViewComponentProps) => (
    <div className="h-min flex justify-content-center w-12 sm:w-10">
      <div
        className="flex flex-column w-11"
        style={{
          marginBottom: "20px",
          maxWidth: "497px",
          width: "80%",
          border: "2px solid #F0F4F7",
          borderRadius: "20px",
        }}
      >
        <div className="flex align-items-center relative">
          <GoBack
            className="absolute"
            onClick={() => {
              props.setPayload(props.currentPayload);
            }}
          />
          <h1
            style={{
              margin: "0",
              marginInline: "auto",
              padding: "10px 20px ",
              fontSize: "22px",
              fontWeight: "600",
              color: "#585858",
            }}
          >
            Pricing
          </h1>
        </div>
        <Divider />
        <div
          className="flex flex-column align-items-center"
          style={{
            padding: "0 10px",
          }}
        >
          <p
            style={{
              margin: "0",
              padding: "10px 20px",
              fontSize: "22px",
              fontWeight: 800,
              color: "#179D52",
            }}
          >
            Complete Family Care
          </p>
          <div className="flex flex-column gap-4 md:flex-row md:gap-0">
            {[
              { text: "Childcare", icon: Slimily },
              { text: "Tutoring", icon: Book },
              { text: "Odd Jobs", icon: OddJob },
            ].map((value, index) => (
              <div
                key={index}
                className="flex justify-content-center align-items-center gap-1"
                style={{
                  width: "120px",
                  textWrap: "nowrap",
                }}
              >
                <img
                  src={value.icon}
                  alt={value.text.trim()}
                  style={{
                    height: "18px",
                    width: "18px",
                    objectFit: "cover",
                  }}
                />
                <p
                  style={{
                    margin: "0",
                    padding: "0",
                    width: "min-content",
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "#585858",
                  }}
                >
                  {value.text}
                </p>
              </div>
            ))}
          </div>
          {[
            {
              element: (
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontSize: "14px",
                    fontWeight: "300",
                    color: "#585858",
                  }}
                >
                  Post unlimited jobs
                </p>
              ),
            },
            {
              element: (
                <p
                  className="m-0 p-0"
                  style={{
                    fontSize: "14px",
                    fontWeight: "300",
                    color: "#585858",
                  }}
                >
                  In-app chat to Helpers invited to jobs
                </p>
              ),
            },
            {
              element: (
                <div className="flex">
                  <img
                    src={AON}
                    alt="aon"
                    style={{
                      height: "17px",
                      width: "42px",
                    }}
                  />
                  <p
                    className="m-0 p-0"
                    style={{
                      fontSize: "14px",
                      fontWeight: "300",
                      color: "#585858",
                    }}
                  >
                    Family Public Liability Insurance Cover
                  </p>
                </div>
              ),
            },
          ].map((value, index) => (
            <div
              className="flex justify-content-between align-items-center mt-3"
              key={index}
              style={{
                width: "80%",
              }}
            >
              {value.element}
              <img
                src={CheckGreen}
                alt="Check"
                style={{
                  width: "14px",
                  height: "13px",
                }}
              />
            </div>
          ))}
          <Divider className="my-4" />
          <h1
            style={{
              margin: "0",
              marginBottom: "10px",
              padding: "0",
              fontSize: "20px",
              fontWeight: "600",
              color: "#585858",
            }}
          >
            Select Your Plan
          </h1>
          <div
            className="flex"
            style={{
              marginTop: "10px",
              marginBottom: "50px",
              width: "264px",
              height: "41px",
              boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
              borderRadius: "10px",
            }}
          >
            {["Pay Annual", "Pay Monthly "].map((value, index) => (
              <div
                key={index}
                className="flex-grow-1 flex justify-content-center align-items-center cursor-pointer"
                style={{
                  margin: "5px 7px",
                  borderRadius: "10px",
                  backgroundColor: props.selectedPlan === index ? "#FFA500" : "#FFFFFF",
                  color: props.selectedPlan === index ? "#ffffff" : "#585858",
                  fontSize: "14px",
                  fontWeight: props.selectedPlan === index ? "700" : "500",
                }}
                onClick={() => props.changePlan(index)}
              >
                {value}
              </div>
            ))}
          </div>
          <Divider />
          <div
            className="flex align-items-center justify-content-between"
            style={{
              marginTop: "40px",
              marginBottom: "20px",
              width: "90%",
            }}
          >
            <span
              className="flex align-items-baseline flex-column md:flex-row"
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#585858",
                marginInline: props.subscirbeEnabled ? "0" : "auto",
              }}
            >
              <div className="flex align-items-baseline">
                <p className="m-0 p-0">$</p>
                <p
                  className="m-0 p-0 mr-1"
                  style={{
                    fontSize: "40px",
                  }}
                >
                  {props.selectedPlan === 0 ? 180 : 90}
                </p>
              </div>
              <p className="m-0 p-0 ">{props.selectedPlan === 0 ? "Per Annum" : "Per Month"}</p>
            </span>
            {props.subscirbeEnabled && (
              <div
                className="flex justify-content-center align-items-center cursor-pointer"
                style={{
                  width: "190px",
                  height: "43px",
                  backgroundColor: "#FFA500",
                  borderRadius: "10px",
                  fontSize: "14px",
                  fontWeight: "700",
                  color: "#ffffff",
                }}
                onClick={() => props.toggleView()}
              >
                Subscribe
              </div>
            )}
          </div>
        </div>
        <Divider />
        <span
          className="flex gap-1 justify-content-center align-items-baseline"
          style={{
            margin: "0",
            padding: "0",
            marginBlock: "30px",
            fontWeight: "600",
            fontSize: "14px",
            color: "#585858",
          }}
        >
          <p className="m-0 p-0">Your Account Balance:</p>
          <p
            className="m-0 p-0"
            style={{
              fontSize: "20px",
            }}
          >
            ${props.walletBalanceDecimal}
          </p>
        </span>
      </div>
    </div>
  ),
};

function Payment(props: ViewComponentProps) {
  const [nameOnCard, setNameOnCard] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [cardNumberDetails, setCardNumberDetails] = useState<boolean>(false);
  const [cardExpiryDetails, setCardExpiryDetails] = useState<boolean>(false);
  const [cardCvcDetails, setCardCvcDetails] = useState<boolean>(false);
  const [valid, setValid] = useState<boolean>(false);
  const [useExistingCard, setUseExistingCard] = useState<boolean>(true);
  const { enableLoader, disableLoader } = useLoader();

  const stripe = useStripe();
  const elements = useElements();
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const dispatch = useDispatch<AppDispatch>();
  const existingCard = sessionInfo.data["paymentInfo"]?.cardLast4Digits ? `XXXX XXXX XXXX ${sessionInfo.data["paymentInfo"].cardLast4Digits}` : null;
  function handleNameChange(value: string) {
    setNameOnCard(value.trim());
  }

  async function onPayClicked(e: React.MouseEvent<HTMLButtonElement>) {
    e.preventDefault();
    enableLoader();
    if (useExistingCard) {
      // Use existing card (no token needed)
      try {
        await Service.payments({
          actionType: c.paymentActionType.ACTIVATE_MEMBERSHIP,
          deviceType: c.deviceType.DESKTOP,
          paymentType:
            props.selectedPlan === 0 ? c.userPaymentType.annualHelperStandardSubscription40 : c.userPaymentType.quarterlyHelperStandardSubscription20,
          // No tokenId; backend uses stored card
        });
        await dispatch(refreshAccount());
        props.toggleView();
      } catch (e) {
        setError(e.message || "An error occurred with the existing card");
      } finally {
        disableLoader();
      }
    } else {
      if (!stripe || !elements) {
        setError("Stripe has not loaded");
        disableLoader();
        return;
      }

      const cardElement = elements.getElement(CardNumberElement);
      if (!cardElement) {
        setError("Card element not found");
        disableLoader();
        return;
      }

      const { token, error } = await stripe.createToken(cardElement, {
        name: nameOnCard,
      });

      if (error) {
        disableLoader();
        setError(error.message || "An error occurred");
      } else if (token) {
        try {
          await Service.payments({
            actionType: c.paymentActionType.ACTIVATE_MEMBERSHIP,
            deviceType: c.deviceType.DESKTOP,
            paymentType:
              props.selectedPlan === 0
                ? c.userPaymentType.annualHelperStandardSubscription40
                : c.userPaymentType.quarterlyHelperStandardSubscription20,
            tokenId: token.id,
          });
          await dispatch(refreshAccount());
          props.toggleView();
        } catch (e) {
          setError(e || "An error occurred");
        } finally {
          disableLoader();
        }
      }
    }
    disableLoader();
  }

  useEffect(() => {
    // Validation only applies to new card input
    setValid(
      useExistingCard || // Valid if using existing card
        (cardNumberDetails && cardExpiryDetails && cardCvcDetails && nameOnCard.trim() !== "")
    );
  }, [cardNumberDetails, cardExpiryDetails, cardCvcDetails, nameOnCard, useExistingCard]);

  useEffect(() => {
    if (existingCard) {
      setUseExistingCard(true); // Default to existing card if available
    } else {
      setUseExistingCard(false); // Default to new card input if no existing card
    }
  }, [existingCard]);
  return (
    <div
      className="flex flex-column select-none"
      style={{
        maxWidth: "891px",
        width: "90%",
        height: "min-content",
        border: "2px solid #F0F4F7",
        borderRadius: "20px",
        padding: "20px 30px 0 30px",
      }}
    >
      <div className="flex w-full align-items-center mb-2">
        <div
          className="cursor-pointer"
          style={{
            fontSize: "16px",
            fontWeight: "500",
            color: "#585858",
          }}
          onClick={() => props.toggleView()}
        >
          {"<"} Go back
        </div>
        <p
          className="m-0 p-0 mx-auto text-sm md:text-xl"
          style={{
            fontWeight: "600",
            color: "#585858",
          }}
        >
          Complete Payment
        </p>
      </div>
      <Divider />
      <div className="mt-4 flex flex-column gap-3 md:flex-row">
        <div
          className="flex flex-column h-min"
          style={{
            border: "1px solid #F5F3F3",
          }}
        >
          <p
            className="m-0 p-2 px-4"
            style={{
              fontSize: "18px",
              fontWeight: "700",
              color: "#585858",
            }}
          >
            Order Summary
          </p>
          <Divider />
          <p
            className="m-0 p-2 pb-0 px-4"
            style={{
              fontSize: "14px",
              fontWeight: "800",
              color: "#179D52",
            }}
          >
            Complete Family Care
          </p>
          <div className="flex justify-content-between align-items-baseline">
            <p
              className="m-0 p-2 pt-0 px-4"
              style={{
                fontSize: "14px",
                fontWeight: "400",
                color: "#585858",
              }}
            >
              {props.selectedPlan === 0 ? "Annual" : "Monthly"} subscription
            </p>
            <p
              className="m-0 p-2 pt-0 px-4"
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#585858",
              }}
            >
              ${props.selectedPlan === 0 ? 180 : 90}
            </p>
          </div>
          <Divider />
          <div className="flex justify-content-between align-items-baseline my-3">
            <p
              className="m-0 p-0 pt-0 px-4"
              style={{
                fontSize: "14px",
                fontWeight: "400",
                color: "#585858",
              }}
            >
              Less Credit Balance
            </p>
            <p
              className="m-0 p-0 px-4"
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#585858",
              }}
            >
              ${props.walletBalanceDecimal}
            </p>
          </div>
          <Divider />
          <div className="flex justify-content-between align-items-baseline my-4">
            <p
              className="m-0 p-0 pt-0 px-4"
              style={{
                fontSize: "14px",
                fontWeight: "700",
                color: "#585858",
              }}
            >
              Amount due inc G.S.T
            </p>
            <p
              className="m-0 p-0 px-4"
              style={{
                fontSize: "22px",
                fontWeight: "600",
                color: "#585858",
              }}
            >
              {(() => {
                const amount = (props.selectedPlan === 0 ? 180 : 90) - props.walletBalanceDecimal;
                if (amount < 0) {
                  return "$0.00";
                }
                return `$${amount.toFixed(2)}`;
              })()}
            </p>
          </div>
          {(props.selectedPlan === 0 ? 180 : 90) - props.walletBalanceDecimal !== 0 && props.walletBalanceDecimal !== 0 && (
            <div className="flex justify-content-between align-items-baseline my-4">
              <p
                className="m-0 p-0 pt-0 px-4"
                style={{
                  fontSize: "14px",
                  fontWeight: "700",
                  color: "#585858",
                }}
              >
                Your Account Balance
              </p>
              <p
                className="m-0 p-0 px-4"
                style={{
                  fontSize: "22px",
                  fontWeight: "600",
                  color: "#585858",
                }}
              >
                {(() => {
                  const amount = (props.selectedPlan === 0 ? 180 : 90) - props.walletBalanceDecimal;
                  return `$${Math.abs(amount).toFixed(2)}`;
                })()}
              </p>
            </div>
          )}
        </div>
        <div className="flex flex-column flex-grow-1">
          <div
            className="flex flex-column"
            style={{
              border: "1px solid #F5F3F3",
            }}
          >
            <p
              className="m-0 p-2 pt-3 pb-4 px-4"
              style={{
                fontSize: "18px",
                fontWeight: "700",
                color: "#585858",
              }}
            >
              Payment details
            </p>
            {existingCard && (
              <div className="px-3 py-2">
                <label className="flex align-items-center gap-2">
                  <input type="radio" checked={useExistingCard} onChange={() => setUseExistingCard(true)} />
                  <span style={{ color: "#585858", fontSize: "14px" }}>Use existing card: {existingCard}</span>
                </label>
                <label className="flex align-items-center gap-2">
                  <input type="radio" checked={!useExistingCard} onChange={() => setUseExistingCard(false)} />
                  <span style={{ color: "#585858", fontSize: "14px" }}>Add new card</span>
                </label>
              </div>
            )}

            {!useExistingCard && (
              <>
                <div className="px-4 flex flex-column gap-4">
                  <CustomInput label="Name on card" onChange={handleNameChange} showAstrict={true} />
                  <div className="flex flex-column mb-1">
                    <div
                      className="flex w-min mb-3"
                      style={{
                        position: "relative",
                        textWrap: "nowrap",
                      }}
                    >
                      <p
                        className="m-0 p-0"
                        style={{
                          fontSize: "12px",
                          fontWeight: "700",
                          color: "#585858",
                        }}
                      >
                        Card Number
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          position: "absolute",
                          top: "0px",
                          right: "-10px",
                          color: "#FF6359",
                          fontSize: "12px",
                        }}
                      >
                        *
                      </p>
                    </div>
                    <CardNumberElement
                      options={{
                        showIcon: true,
                        style: {
                          base: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                            "::placeholder": {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                            },
                          },
                          invalid: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#ff5252",
                          },
                          complete: {
                            fontSize: "14px",
                            fontWeight: "400",
                            color: "#787777",
                          },
                        },
                      }}
                      onChange={(event) => {
                        setCardNumberDetails(event.complete);
                        if (event.error) {
                          setError(event.error.message);
                        } else {
                          setError(null);
                        }
                      }}
                    />
                  </div>
                  <div className="flex">
                    <div className="flex flex-column mb-3 flex-grow-1">
                      <div
                        className="flex w-min mb-3"
                        style={{
                          position: "relative",
                          textWrap: "nowrap",
                        }}
                      >
                        <p
                          className="m-0 p-0"
                          style={{
                            fontSize: "12px",
                            fontWeight: "700",
                            color: "#585858",
                          }}
                        >
                          Card expiration date
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            position: "absolute",
                            top: "0px",
                            right: "-10px",
                            color: "#FF6359",
                            fontSize: "12px",
                          }}
                        >
                          *
                        </p>
                      </div>
                      <CardExpiryElement
                        options={{
                          style: {
                            base: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                              "::placeholder": {
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#787777",
                              },
                            },
                            invalid: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#ff5252",
                            },
                            complete: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                            },
                          },
                        }}
                        onChange={(event) => {
                          setCardExpiryDetails(event.complete);
                          if (event.error) {
                            setError(event.error.message);
                          } else {
                            setError(null);
                          }
                        }}
                      />
                    </div>
                    <div className="flex flex-column mb-3 flex-grow-1">
                      <div
                        className="flex w-min mb-3"
                        style={{
                          position: "relative",
                          textWrap: "nowrap",
                        }}
                      >
                        <p
                          className="m-0 p-0"
                          style={{
                            fontSize: "12px",
                            fontWeight: "700",
                            color: "#585858",
                          }}
                        >
                          CVC
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            position: "absolute",
                            top: "0px",
                            right: "-10px",
                            color: "#FF6359",
                            fontSize: "12px",
                          }}
                        >
                          *
                        </p>
                      </div>
                      <CardCvcElement
                        options={{
                          style: {
                            base: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                              "::placeholder": {
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#787777",
                              },
                            },
                            invalid: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#ff5252",
                            },
                            complete: {
                              fontSize: "14px",
                              fontWeight: "400",
                              color: "#787777",
                            },
                          },
                        }}
                        onChange={(event) => {
                          setCardCvcDetails(event.complete);
                          if (event.error) {
                            setError(event.error.message);
                          } else {
                            setError(null);
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
            <button
              className="mx-3 flex justify-content-center align-items-center mt-3 gap-1"
              style={{
                backgroundColor: (stripe && valid) || useExistingCard ? "#FFA500" : "#ccc",
                cursor: (stripe && valid) || useExistingCard ? "pointer" : "not-allowed",
                borderRadius: "10px",
                border: "none",
                height: "33px",
                fontSize: "14px",
                fontWeight: "700",
                color: "#FFFFFF",
              }}
              disabled={!stripe || !valid}
              onClick={(e) => onPayClicked(e)}
            >
              <img src={Lock} alt="lock" />
              {props.selectedPlan === 0 ? "Pay annual subscription" : "Pay quarterly subscription"}
            </button>
            {error && (
              <p className="m-0 p-0 text-center lg:mt-3 mt-2 mb-3" style={{ color: "#FF6359", fontWeight: "700", fontSize: "12px" }}>
                {error}
              </p>
            )}
          </div>
          <span
            className="flex my-3 gap-2 align-items-center"
            style={{
              backgroundColor: "#F0F4F7",
              borderRadius: "20px",
              border: "1px solid #1F9EAB",
              padding: "5px",
            }}
          >
            <img
              src={LockBlack}
              alt="lock"
              style={{
                height: "16px",
                width: "16px",
              }}
            />
            <p
              className="m-0 p-0"
              style={{
                fontSize: "10px",
                fontWeight: "400",
                color: "#585858",
              }}
            >
              All card information is fully encrypted, secure and protected. Learn more
            </p>
          </span>
        </div>
      </div>
    </div>
  );
}

function PaymentsHelper(props: PostJobProps) {
  const [currentView, setCurrentView] = useState<"price" | "payment">("price");
  const [selectedPlan, setSelectedPlan] = useState(0);

  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);

  function changePlan(update: number) {
    setSelectedPlan(update);
  }

  function toggleView() {
    setCurrentView((prev) => (prev === "payment" ? "price" : "payment"));
  }
  return (
    <div className="w-full h-full flex justify-content-center pt-4 bg-white overflow-hidden overflow-y-auto">
      <Elements stripe={loadStripe(environment.getStripePublishableKey(window.location.hostname))}>
        {currentView === "payment" ? (
          <Payment
            {...props}
            changePlan={changePlan}
            selectedPlan={selectedPlan}
            toggleView={toggleView}
            walletBalance={sessionInfo.loading ? 0 : sessionInfo.data["walletBalanceDecimal"]}
            walletBalanceDecimal={sessionInfo.loading ? 0 : sessionInfo.data["walletBalanceDecimal"]}
            subscirbeEnabled={sessionInfo.loading ? false : sessionInfo.data["paymentInfo"]["paymentType"] === c.userPaymentType.FREE}
          />
        ) : (
          Price.render({
            ...props,
            changePlan,
            selectedPlan,
            toggleView,
            walletBalance: sessionInfo.loading ? 0 : sessionInfo.data["walletBalanceDecimal"],
            walletBalanceDecimal: sessionInfo.loading ? 0 : sessionInfo.data["walletBalanceDecimal"],
            subscirbeEnabled: sessionInfo.loading ? false : sessionInfo.data["paymentInfo"]["paymentType"] === c.userPaymentType.FREE,
          })
        )}
      </Elements>
    </div>
  );
}

export default PaymentsHelper;
