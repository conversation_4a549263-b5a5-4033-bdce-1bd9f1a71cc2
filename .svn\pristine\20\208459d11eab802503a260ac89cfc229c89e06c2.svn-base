import React, { useState } from "react";
import clock from "../../../../assets/images/Icons/clockstart.png";
import clocked from "../../../../assets/images/Icons/clockend.png";
import styles from "../../styles/award-shifts-to.module.css";
import ConfirmAwardDialog from "./ConfirmAwardDialog";
import useIsMobile from "../../../../hooks/useIsMobile";

interface Shift {
  day: string;
  startTime: string;
  endTime: string;
  shiftId: number;
  awardedAplicantImage: string | null;
  imageUrl: string;
  price: number;
  overtimerate: number;
}

interface AwardShiftsToParentProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  shifts: Shift[];
  onShiftSelect?: (shiftId: number) => void;
  onSelectAll: () => void;
  selectedShifts: any | null;
  timeSlots: Shift[];
  job: any;
  onApply?: () => void;
  handleApplyForAuPairJob?: () => void;
  handleApplyForRecurringJob?: () => void;
}

const AwardShiftsToHelper: React.FC<AwardShiftsToParentProps> = ({
  isOpen,
  onClose,
  title,
  shifts,
  onShiftSelect,
  onSelectAll,
  selectedShifts,
  onApply,
  handleApplyForAuPairJob,
  handleApplyForRecurringJob,
}) => {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const { isMobile } = useIsMobile();
  const handleAwardShifts = () => {
    setIsConfirmDialogOpen(true);
  };

  const handleConfirm = () => {
    onApply();
    // handleApplyForAuPairJob()
    handleApplyForRecurringJob();
    setIsConfirmDialogOpen(false);
  };

  const handleCancel = () => {
    setIsConfirmDialogOpen(false);
  };

  const data = shifts.filter((shift) => shift?.["isRequired"] === true);

  function formatTimeTo12Hour(time: string): string {
    if (!time) {
      return "";
    }
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  }

  return !isMobile ? (
    <div className={styles.overlay}>
      <div className={`${styles.dialog} `} style={{ height: "585px" }}>
        <div className={`${styles.header} p-2`}>
          <h2 className={styles.title}>{title}</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>

        <div className="p-3">
          <div className={`${styles.sectionHeader} pl-3 pr-3 mb-2`}>
            <h3 className={`${styles.sectionTitle} p-0 m-0`}>
              Shift date and time
            </h3>
            <h3 className={`${styles.sectionTitle} p-0 m-0`}>price</h3>
            <h3 className={`${styles.sectionTitle} p-0 m-0`}>Select Shift</h3>
          </div>
          <div
            className={`${styles.shiftsList} flex justify-content-between overflow-x-auto`}
            style={{ height: "auto", maxHeight: "350px" }}
          >
            {data.map((shift, index) => (
              <div key={index} className={styles.shiftItem}>
                <div className={styles.shiftInfo}>
                  <span className={styles.dayLabel}>{shift.day}</span>
                  <div className={styles.timeContainer}>
                    <div
                      className="flex justify-content-center align-items-center"
                      style={{
                        border: "1px solid #DFDFDF",
                        borderRadius: "10px",
                        width: "103px",
                        height: "41px",
                      }}
                    >
                      <img
                        src={clock}
                        alt="file check"
                        width="13.5px"
                        height="13.5px"
                        className="text-gray-500"
                      />
                      <span
                        className={`${styles.timeBox} p-2`}
                        style={{ fontSize: "14px" }}
                      >
                        {formatTimeTo12Hour(shift.startTime)}
                      </span>
                    </div>
                    <span
                      className="p-2"
                      style={{ fontSize: "12px", color: "#585858" }}
                    >
                      to
                    </span>
                    <div
                      className="flex justify-content-center align-items-center"
                      style={{
                        border: "1px solid #DFDFDF",
                        borderRadius: "10px",
                        width: "103px",
                        height: "41px",
                      }}
                    >
                      <img
                        src={clocked}
                        alt="file check"
                        width="14.34px"
                        height="13.5px"
                        className="text-gray-500"
                      />
                      <span
                        className={`${styles.timeBox} p-2`}
                        style={{ fontSize: "14px" }}
                      >
                        {formatTimeTo12Hour(shift.endTime)}
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  className="flex align-items-center gap-2"
                  style={{ color: "#585858" }}
                >
                  <p className="p-0 m-0">${shift.price}</p>
                  {/* <p className='p-0 m-0'>(${shift.overtimerate})</p> */}
                </div>
                <div
                  style={{
                    height: "50px",
                    width: "2px",
                    backgroundColor: "#DFDFDF",
                  }}
                ></div>
                <button
                  onClick={() => onShiftSelect(shift.shiftId)}
                  className={`${styles.selectButton} ${selectedShifts.includes(shift.shiftId)
                      ? styles.selected
                      : ""
                    }`}
                  disabled={shift.awardedAplicantImage !== null}
                  style={{
                    cursor:
                      shift.awardedAplicantImage !== null
                        ? "disable"
                        : "pointer",
                  }}
                >
                  {selectedShifts.includes(shift.shiftId) && (
                    <span className={styles.checkmark}>✓</span>
                  )}
                  <span className={styles.circle}></span> &nbsp;&nbsp;
                  <span style={{ fontSize: "14px" }}>
                    {shift.awardedAplicantImage !== null
                      ? "Apply"
                      : "Apply Shift"}
                  </span>
                </button>
              </div>
            ))}
          </div>
          <div className={styles.footer}>
            {selectedShifts.length ===
              shifts.filter((shift) => shift?.["isRequired"]).length ? (
              <span
                className="pl-3 pr-3 flex align-items-center"
                style={{
                  fontWeight: "bold",
                  textDecoration: "underline",
                  cursor: "pointer",
                  color: "#FFA500",
                  fontSize: "14px",
                }}
                onClick={onSelectAll}
              >
                Unselect All
              </span>
            ) : (
              <button
                onClick={onSelectAll}
                className={`${styles.selectAllButton} pl-3 pr-3`}
              >
                Select all shifts
              </button>
            )}
            <button
              className={`${styles.awardButton} ${selectedShifts.length > 0 ? styles.enabledButton : ""
                } cursor-pointer`}
              disabled={selectedShifts.length === 0}
              onClick={handleAwardShifts}
            >
              Apply Shifts
            </button>
          </div>
          <ConfirmAwardDialog
            isOpen={isConfirmDialogOpen}
            title="Confirm Apply"
            message="Are you sure you want to confirm your shift"
            onOpenChange={(open) => setIsConfirmDialogOpen(open)}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            noText="Go back"
            yesText="Yes"
          />
        </div>
      </div>
    </div>
  ) : (
    <div className={styles.overlayMobile}>
      <div className={`${styles.dialogMobile} `}>
        <div className={`${styles.headerMobile}`}>
          <h2 className={styles.titleMobile}>{title}</h2>
          <button className={styles.closeButtonMobile} onClick={onClose}>
            ×
          </button>
        </div>

        <div className="p-3">
          <div className={`${styles.sectionHeader} pl-2 pr-3 mb-2`}>
            <h3 style={{ fontSize: "16px" }} className={`${styles.sectionTitle} p-0 m-0`}>
              Shift date and time
            </h3>
            <h3 style={{ fontSize: "16px" }} className={`${styles.sectionTitle} p-0 m-0`}>price</h3>
            <h3 style={{ fontSize: "16px" }} className={`${styles.sectionTitle} p-0 m-0`}>Select Shift</h3>
          </div>
          <div
            className={`${styles.shiftsListMobile} flex justify-content-between overflow-x-auto`}
            style={{ height: "auto", maxHeight: "320px" }}
          >
            {data.map((shift, index) => (
              <div
                key={index}
                className={`${styles.shiftItemMobile} ${selectedShifts.includes(shift.shiftId) ||
                    shift.awardedAplicantImage
                    ? styles.selectedShiftBorder
                    : ""
                  }`}
              >
                <div className={styles.shiftInfo}>
                  <div>

                  </div>
                  <div className="flex flex-column">
                    <span className={styles.dayLabelMobile}>{shift.day}</span>
                    <div className={styles.timeContainer}>
                      <div className="flex justify-content-center align-items-center">
                        <span
                          className={`${styles.timeBox}`}
                          style={{
                            fontSize: "12px",
                            fontWeight: "700",
                            color: "#179D52",
                            textDecoration: "underline",
                          }}
                        >
                          {formatTimeTo12Hour(shift.startTime)}
                        </span>
                      </div>
                      <span
                        className="px-1"
                        style={{
                          fontSize: "12px",
                          fontWeight: "700",
                          color: "#179D52",
                          textDecoration: "underline",
                        }}
                      >
                        to
                      </span>
                      <div className="flex justify-content-center align-items-center">
                        <span
                          className={`${styles.timeBox} pr-4`}
                          style={{
                            fontSize: "12px",
                            fontWeight: "700",
                            color: "#179D52",
                            textDecoration: "underline",
                          }}
                        >
                          {formatTimeTo12Hour(shift.endTime)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Price Section (Retained as it is) */}
                <div
                  className="flex align-items-center gap-2"
                  style={{ color: "#585858" }}
                >
                  <p className="p-0 m-0">${shift.price}</p>
                  {/* <p className='p-0 m-0'>(${shift.overtimerate})</p> */}
                </div>

                <button
                  onClick={() => onShiftSelect(shift.shiftId)}
                  className={`${selectedShifts.includes(shift.shiftId) ||
                      shift.awardedAplicantImage !== null
                      ? styles.selectedMobile
                      : styles.selectButtonMobile
                    }`}
                  disabled={shift.awardedAplicantImage !== null}
                  style={{
                    backgroundColor:
                      selectedShifts.includes(shift.shiftId) ||
                        shift.awardedAplicantImage
                        ? "#179D52"
                        : "#DFDFDF",
                    color:
                      selectedShifts.includes(shift.shiftId) ||
                        shift.awardedAplicantImage
                        ? "#fff"
                        : "#585858",
                    cursor:
                      shift.awardedAplicantImage !== null
                        ? "not-allowed"
                        : "pointer",
                  }}
                >
                  <span style={{ fontSize: "12px", fontWeight: "700" }}>
                    {shift.awardedAplicantImage !== null
                      ? "Awarded"
                      : selectedShifts.includes(shift.shiftId)
                        ? "Shift Selected >"
                        : "Select Shift >"}
                  </span>
                </button>
              </div>
            ))}
          </div>
          <div className={styles.footerMobile}>
            {selectedShifts.length ===
              shifts.filter((shift) => shift?.["isRequired"]).length ? (
              <span
                className="pl-3 pr-3 flex align-items-center"
                style={{
                  color: "#585858",
                  textDecoration: "underline",
                  fontWeight: "500",
                  fontSize: "12px",
                  backgroundColor: "transparent",
                  border: "1px solid #000000",
                  borderRadius: "20px",
                }}
                onClick={onSelectAll}
              >
                Unselect All
              </span>
            ) : (
              <button
                onClick={onSelectAll}
                className={`pl-3 pr-3`}
                style={{
                  color: "#585858",
                  textDecoration: "underline",
                  fontWeight: "500",
                  fontSize: "12px",
                  backgroundColor: "transparent",
                  border: "1px solid #000000",
                  borderRadius: "20px",
                }}
              >
                Select all shifts
              </button>
            )}
            <button
              className={`${styles.awardButtonMobile} ${selectedShifts.length > 0 ? styles.enabledButtonMobile : ""
                } cursor-pointer`}
              disabled={selectedShifts.length === 0}
              onClick={handleAwardShifts}
            >
              Apply Shifts
            </button>
          </div>
          <ConfirmAwardDialog
            isOpen={isConfirmDialogOpen}
            title="Confirm Apply"
            message="Are you sure you want to confirm your shift"
            onOpenChange={(open) => setIsConfirmDialogOpen(open)}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            noText="Go back"
            yesText="Yes"
          />
        </div>
      </div>
    </div>
  );
};

export default AwardShiftsToHelper;
