.bg-clr {
  /* background-image: url("https://www.jugglestreet.com.au/static/images/home/<USER>");  */
  background-color: #f0f4f7;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  object-fit: contain;
}
.border-red {
  border: 1px solid red !important;
}
.para {
  margin: 15px 0;
  position: relative;
  display: flex;
  flex-direction: column;
}

.input-container {
  position: relative;
  width: 362px;
}
.input-container-business {
  position: relative;
  width: 100%;
}

.label-name {
  position: absolute;
  left: 12px;
  top: 50%;
  height: 26px;
  transform: translateY(-50%);
  transition: 0.2s ease;
  opacity: 0.6;
  background: #f0f4f7;
  padding: 0 4px;
  transform-origin: top left;
  font-size: 16px;
  pointer-events: none;
  border-radius: 10px;
  border: none;
  /* padding: 2px 20px; */
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  color: #585858;
  padding-inline: 5px;
  /* padding-left: 10px;
  padding-right: 10px;  */
}
@media (max-width: 359px) {
  .label-name {
    font-size: 12px; /* Smaller font size for 359px and below */
  }
}
@media (max-width: 340px) {
  .label-name {
    font-size: 12px; /* Smaller font size for smaller screens */
  }
}

.input-placeholder:-webkit-autofill  {
  width: 100%;
  font-size: 1rem;
  color: #585858;
  background-color: #F0F4F7 !important;
}

.input-placeholder:-webkit-autofill {
  background-color: #F0F4F7 !important;
  color: #585858 !important;
}
.input-placeholder {
   transition: background-color 5000s ease-in-out 0s, color 5000s ease-in-out 0s; 
}


.imagelabel h2 {
  color: rgba(23, 157, 82, 1);
  text-align: center;
  font: 700 16px "Poppins", sans-serif;
  margin-top: -43px;
}
.border-custom {
  border: 2px solid #179D52; /* Use the specified color */
}

/* Base styles for small screens */


@media (max-width: 600px) {
  .input-placeholder {
     max-width: 100%;
  }
}


.input-placeholder:focus + .label-name,
.input-placeholder:not(:placeholder-shown) + .label-name {
  opacity: 1;
  transform: scale(0.8) translateY(-180%) translateX(-3px);
}
.emailInputError {
  border-color: red !important;
}
.passwordInputError {
  border-color: red !important;
}
.accountIdentifierError {
  border-color: red !important;
}
.codeInputError {
  border-color: red !important;
}
.confirmPasswordErrorText {
  border-color: red !important;
}
.input-error {
  color: red;
}
.inputErrorLabel {
  font-weight: 500;
  width: auto; /* Dynamic width based on content */
  color: #585858;
  height: 23px;
  font-size: 15px;
  font-family: "Poppins", sans-serif;
}
.error-message {
  color: #ff6359;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
  text-align: center;
}
/* Loader overlay that covers the entire page */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6); /* White shade with transparency */
  backdrop-filter: blur(6px); /* Slightly stronger blur effect */
  z-index: 9999;
  transition: background-color 0.3s ease, backdrop-filter 0.3s ease; /* Smooth transition */
}

/* Loader image styles */
.loader img {
  width: 150px;  /* Increased size of the logo */
  height: 150px;
 
}

/* Apply a transparent effect to the form when loading */
.form-container.transparent-effect {
 
  pointer-events: none; /* Prevent interaction with the form */
  transition: opacity 0.3s ease;
}



@media (max-width: 768px) {
  .inputErrorLabel {
    font-size: 14px; /* Smaller font size for tablets */
  }
}

@media (max-width: 480px) {
  .inputErrorLabel {
    font-size: 12px; /* Even smaller font size for mobile devices */
  }
}


.radiobutton-style[data-should_hover="true"]:hover {
  border: 1px solid #585858 !important;
}
.txt-clr {
  color: #585858;
}

.label-aboutme {
  position: absolute;
  left: 12px;
  top: 1%;
  height: 26px;
  transform: translateY(-50%);
  transition: 0.2s ease;
  opacity: 0.6;
  background: #f0f4f7;
  padding: 0 4px;
  transform-origin: top left;
  font-size: 13px;
  pointer-events: none;
  border-radius: 10px;
  border: none;
  /* padding: 2px 20px; */
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  color: #585858;
  padding-inline: 5px;
  /* padding-left: 10px;
  padding-right: 10px;  */
}
.input-containerhelper {
  position: relative;
}
.box-shadow {
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
}
.box-shadow:hover {
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.1);
}