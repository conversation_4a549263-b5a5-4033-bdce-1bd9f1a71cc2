import React from 'react';
import { ProgressBar } from 'primereact/progressbar';
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import useIsMobile from '../../../hooks/useIsMobile';
import sideArrow from '../../../assets/images/Icons/side_arrow_left.png';
interface ProfileCompletenessHeaderProps {
    title: string;
    profileCompleteness: number;
    loading: boolean;
    onBackClick?: () => void;
}

const ProfileCompletenessHeader: React.FC<ProfileCompletenessHeaderProps> = ({ title, profileCompleteness, loading,onBackClick }) => {
    const {isMobile}=useIsMobile();
    return !isMobile ? (
        <header className={styles.utilheader}>
        <h1 className="p-0 m-0">{title}</h1>
        <ProgressBar
            value={loading ? 100 : profileCompleteness}
            className={`${styles.utilprogressbar} mt-2`}
        />
        <p
            style={{
                fontFamily: 'Poppins',
                fontWeight: 500,
                fontSize: '14px',
                color: '#585858',
            }}
        >
            Your profile is{' '}
            <span
                style={{
                    color: '#179D52',
                    fontSize: '18px',
                    fontWeight: '700',
                }}
            >
                {' '}
                {loading ? 70 : profileCompleteness}% complete.
            </span>
        </p>
    </header>
    ):(
        <header className={styles.utilheader}>
         <div style={{display:"flex", justifyContent:"center"}}>
         <div
                className='w-full flex justify-content-center align-items-center relative'
                style={{
                    height: '60px',
                    backgroundColor: '#179d52',
                }}
            >
                <p
                    className='m-0 p-0'
                    style={{
                        fontSize: '20px',
                        color: '#FFFFFF',
                        fontWeight: '700',
                    }}
                >
                    {title}
                </p>
                <button
                    className='absolute flex justify-content-center items-align-center cursor-pointer'
                    style={{
                        top: '50%',
                        transform: 'translateY(-50%)',
                        left: '30px',
                        background: 'rgba(217, 217, 217, 0.3)',
                        borderRadius: '999px',
                        border: 'none',
                        padding: '5px 8px',
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        onBackClick();
                    }}
                >
                    <img src={sideArrow} alt='sideArrow' width='10px' height='15px' />
                </button>
            </div>
         </div>
         <div className='pt-4 px-3'>
         <ProgressBar
            value={loading ? 100 : profileCompleteness}
            className={`${styles.utilprogressbar} `}
        />
        <p
            style={{
                fontFamily: 'Poppins',
                fontWeight: 500,
                fontSize: '14px',
                color: '#585858',
             
            }}
        >
            Your profile is{' '}
            <span
                style={{
                    color: '#179D52',
                    fontSize: '18px',
                    fontWeight: '700',
                }}
            >
                {' '}
                {loading ? 70 : profileCompleteness}% complete.
            </span>
        </p>
         </div>
    </header>
    )
};

export default ProfileCompletenessHeader;