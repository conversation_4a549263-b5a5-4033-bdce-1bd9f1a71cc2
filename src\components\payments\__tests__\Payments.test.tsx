import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import Payments from '../../../containers/Common/payments/Tabs/Payments';

// Mock the hooks
jest.mock('../../../hooks/usePaymentData', () => ({
  usePaymentData: () => ({
    invoiceApprovalData: [
      {
        id: 1,
        status: 'Awaiting Invoice Approval',
        type: 'Babysitting Service',
        date: '16 January 2024',
        location: '9 Christie Beach, South Brisbane',
        userName: 'Sarah <PERSON>',
        amount: 150,
        description: '3 hours of babysitting'
      }
    ],
    makePaymentData: [
      {
        id: 2,
        status: 'Ready for Payment',
        type: 'Tutoring Session',
        date: '20 January 2024',
        location: '22 Queen Street, Brisbane',
        userName: 'Emily R',
        amount: 175,
        description: 'Math tutoring session'
      }
    ],
    paymentHistoryData: [
      {
        id: 3,
        status: 'Payment Completed',
        type: 'Garden Maintenance',
        date: '10 January 2024',
        location: '12 Garden Street, Brisbane',
        userName: 'Mike T',
        amount: 100,
        description: 'Weekly garden care'
      }
    ],
    isLoading: false,
    error: null,
    refreshData: jest.fn()
  })
}));

// Mock utils and cookies
jest.mock('../../../components/utils/util', () => ({
  getCookie: jest.fn(() => '1') // Mock as parent user
}));

const renderWithRouter = (component: React.ReactElement, initialRoute = '/parent-home/payments/invoice-approval') => {
  window.history.pushState({}, 'Test page', initialRoute);
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Payments Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders payment tabs correctly', () => {
    renderWithRouter(<Payments />);
    
    // Check if tab headers are rendered
    expect(screen.getByText('Invoice Approval')).toBeInTheDocument();
    expect(screen.getByText('Make Payment')).toBeInTheDocument();
    expect(screen.getByText('Payment History')).toBeInTheDocument();
  });

  it('displays payment data in the first tab', () => {
    renderWithRouter(<Payments />);
    
    // Check if payment data is displayed
    expect(screen.getByText('Babysitting Service')).toBeInTheDocument();
    expect(screen.getByText('Sarah M')).toBeInTheDocument();
    expect(screen.getByText('9 Christie Beach, South Brisbane')).toBeInTheDocument();
  });

  it('shows correct tab counts in badges', () => {
    renderWithRouter(<Payments />);
    
    // Check if badges show correct counts
    const badges = screen.getAllByText('1');
    expect(badges).toHaveLength(3); // Each tab should have 1 item
  });

  it('handles tab navigation correctly', () => {
    const mockNavigate = jest.fn();
    jest.doMock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useNavigate: () => mockNavigate
    }));

    renderWithRouter(<Payments />);
    
    // The component should render without errors
    expect(screen.getByText('Invoice Approval')).toBeInTheDocument();
  });

  it('shows loading state when data is loading', () => {
    // Mock loading state
    jest.doMock('../../../hooks/usePaymentData', () => ({
      usePaymentData: () => ({
        invoiceApprovalData: [],
        makePaymentData: [],
        paymentHistoryData: [],
        isLoading: true,
        error: null,
        refreshData: jest.fn()
      })
    }));

    renderWithRouter(<Payments />);
    
    expect(screen.getByText('Loading payment data...')).toBeInTheDocument();
  });

  it('shows error state when there is an error', () => {
    // Mock error state
    jest.doMock('../../../hooks/usePaymentData', () => ({
      usePaymentData: () => ({
        invoiceApprovalData: [],
        makePaymentData: [],
        paymentHistoryData: [],
        isLoading: false,
        error: 'Failed to load payments',
        refreshData: jest.fn()
      })
    }));

    renderWithRouter(<Payments />);
    
    expect(screen.getByText(/Error loading payments/)).toBeInTheDocument();
  });

  it('opens payment details popup when review is clicked', () => {
    renderWithRouter(<Payments />);
    
    // Find and click the review button
    const reviewButtons = screen.getAllByText('Review');
    fireEvent.click(reviewButtons[0]);
    
    // Check if popup is opened
    expect(screen.getByText('Payment Details')).toBeInTheDocument();
    expect(screen.getByText('Service: Babysitting Service')).toBeInTheDocument();
  });

  it('closes payment details popup when close is clicked', () => {
    renderWithRouter(<Payments />);
    
    // Open popup
    const reviewButtons = screen.getAllByText('Review');
    fireEvent.click(reviewButtons[0]);
    
    // Close popup
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);
    
    // Check if popup is closed
    expect(screen.queryByText('Payment Details')).not.toBeInTheDocument();
  });
});
