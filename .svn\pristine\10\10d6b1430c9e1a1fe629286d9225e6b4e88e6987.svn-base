import { Dialog } from "primereact/dialog";
import styles from "./styles/custom-dialog.module.css"; // Importing CSS module
import React, { ReactNode } from "react";
import useIsMobile from "../../hooks/useIsMobile";
import backArrow from "../../assets/images/Icons/side_arrow_left.png";
interface CustomDialogProp {
  profileCompletion: number;
  closeClicked: () => void;
  children: ReactNode;
  onHide: () => void;
  visible: boolean;
  className?: string;
}

const CustomDialog: React.FC<CustomDialogProp> = ({ visible, onHide, children, closeClicked, className }) => {
  const { isMobile } = useIsMobile();
  return (
    <Dialog
      modal
      visible={visible}
      onHide={onHide}
      className={`${styles.customDialog} ${isMobile ? styles.mobileDialog : ""} ${className}`}
      contentStyle={{
        padding: 0,
        ...(isMobile && {
          height: "100vh",
          width: "100vw",
          margin: 0,
          maxHeight: "100vh",
          borderRadius: 0,
        }),
      }}
      style={{
        maxWidth: isMobile ? "100%" : "100%",
        width: isMobile ? "100%" : "auto",
        margin: isMobile ? 0 : "auto",
        height: isMobile ? "100vh" : "auto",
        borderRadius: isMobile ? 0 : "33px",
      }}
      content={
        <div className={`${styles.customDialogContent} ${isMobile ? styles.mobileContent : ""}`}>
          <div
            style={{
              display: "flex",
              height: "50px",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              position: "relative",
              padding: "0 20px",
              width: "100%",
            }}
          ></div>

          {!isMobile ? (
            <i
              className="pi pi-times"
              style={{
                position: "absolute",
                top: "18px",
                right: "26px",
                fontSize: "1.5rem",
                cursor: "pointer",
                zIndex: "99",
                color: "#585858",
              }}
              onClick={onHide} // Close on icon click
            ></i>
          ) : (
            <img
              src={backArrow} // Replace with the correct path
              alt="Close"
              style={{
                position: "fixed",
                top: "0",
                width: "13px", // Adjust size as needed
                height: "20px",
                cursor: "pointer",
                zIndex: "0",
                marginTop: "22px",
                marginLeft: "20px",
              }}
              onClick={onHide} // Close on image click
            />
          )}

          {/* Content Area */}
          {children}
        </div>
      }
    ></Dialog>
  );
};

export default CustomDialog;
