import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Service from '../services/services';
import useLoader from './LoaderHook';
import c from '../helper/juggleStreetConstants';

export interface AdjustedTimesheetEntry {
  id: number;
  status: string;
  type: string;
  date: string;
  location: string;
  userName: string;
  originalImageUrl?: string;
  statusCode: number;
}

interface AdjustedTimesheetApiItem {
  id: number;
  status: number;
  jobType: number;
  jobDate: string;
  formattedAddress: string;
  firstName: string;
  lastName?: string;
  originalImageUrl?: string;
}

const statusMap: { [key: number]: string } = {
  2: "Adjusted Timesheet", // Status 2 for adjusted-timesheets
};

const jobTypeMap: { [key: number]: string } = {
  [c.jobType.UNSPECIFIED]: "Unspecified",
  [c.jobType.BABYSITTING]: "One Of Job",
  [c.jobType.NANNYING]: "Recurring Job",
  [c.jobType.BEFORE_SCHOOL_CARE]: "Before School Care",
  [c.jobType.AFTER_SCHOOL_CARE]: "After School Care",
  [c.jobType.BEFORE_AFTER_SCHOOL_CARE]: "Before & After School Care",
  [c.jobType.AU_PAIR]: "Au Pair",
  [c.jobType.HOME_TUTORING]: "Home Tutoring",
  [c.jobType.PRIMARY_SCHOOL_TUTORING]: "Primary School Tutoring",
  [c.jobType.HIGH_SCHOOL_TUTORING]: "High School Tutoring",
  [c.jobType.ONE_OFF_ODD_JOB]: "Odd Job",
};

export const useAdjustedTimesheetDetails = () => {
  const location = useLocation();
  const [adjustedTimesheetData, setAdjustedTimesheetData] = useState<AdjustedTimesheetEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { enableLoader, disableLoader } = useLoader();

  // Check if current route is adjusted-timesheets
  const isAdjustedTimesheetsRoute = location.pathname.includes('/adjusted-timesheets');

  const fetchAdjustedTimesheetData = async (): Promise<void> => {
    console.log('🔄 Starting adjusted timesheet data fetch from API...');
    setIsLoading(true);
    setError(null);
    enableLoader();

    try {
      await new Promise<void>((resolve, reject) => {
        Service.getTimeSheet(
          (response: AdjustedTimesheetApiItem[]) => {
            console.log("API Response getTimeSheet for adjusted:", response);
            
            if (!Array.isArray(response)) {
              console.warn("Expected array response, got:", typeof response);
              setAdjustedTimesheetData([]);
              resolve();
              return;
            }

            // Filter only status 2 entries (Adjusted Timesheets)
            const filteredData = response.filter((item: AdjustedTimesheetApiItem) => item.status === 2);
            console.log("Filtered adjusted timesheet data (status 2):", filteredData);

            const mappedData: AdjustedTimesheetEntry[] = filteredData.map((item: AdjustedTimesheetApiItem) => ({
              id: item.id,
              status: statusMap[item.status] || "Adjusted Timesheet",
              statusCode: item.status,
              type: jobTypeMap[item.jobType] || "Unknown",
              date: new Date(item.jobDate).toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }),
              location: item.formattedAddress,
              userName: `${item.firstName} ${item.lastName?.charAt(0) || ''}`,
              originalImageUrl: item.originalImageUrl,
            }));

            console.log("Mapped adjusted timesheet data:", mappedData);
            console.log("Adjusted timesheet count:", mappedData.length);
            
            setAdjustedTimesheetData(mappedData);
            console.log('✅ Adjusted timesheet data loaded successfully, count:', mappedData.length);
            resolve();
          },
          (error: any) => {
            console.error('Error fetching adjusted timesheet data from API:', error);
            setError(error?.message || 'Failed to fetch adjusted timesheet data');
            reject(error);
          }
        );
      });
      
    } catch (err) {
      console.error('Fetch adjusted timesheet data failed:', err);
      setError('Failed to fetch adjusted timesheet data');
    } finally {
      console.log('🔄 Finishing adjusted timesheet data fetch...');
      setIsLoading(false);
      disableLoader();
      console.log('✅ Loader disabled for adjusted timesheets');
    }
  };

  useEffect(() => {
    // Only fetch data when on the adjusted-timesheets route
    if (isAdjustedTimesheetsRoute) {
      console.log('🔄 Route changed to adjusted-timesheets, fetching data...');
      fetchAdjustedTimesheetData();
    } else {
      console.log('📍 Not on adjusted-timesheets route, skipping API call');
    }
  }, [location.pathname]); // Trigger when route changes

  const refreshData = () => {
    fetchAdjustedTimesheetData();
  };

  const clearAdjustedTimesheetData = () => {
    setAdjustedTimesheetData([]);
    setError(null);
  };

  return {
    adjustedTimesheetData,
    isLoading,
    error,
    refreshData,
    clearAdjustedTimesheetData
  };
};

export default useAdjustedTimesheetDetails;
