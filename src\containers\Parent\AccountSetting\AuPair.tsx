import React, { useEffect, useState } from 'react'
import styles from '../../Helper/styles/utils-profile-completness-wizard.module.css';
import { AppDispatch, RootState } from '../../../store';
import { useDispatch, useSelector } from 'react-redux';
import useLoader from '../../../hooks/LoaderHook';
import { updateSessionInfo } from '../../../store/tunks/sessionInfoTunk';
import c from '../../../helper/juggleStreetConstants';
import { InputTextarea } from 'primereact/inputtextarea';
import CustomButton from '../../../commonComponents/CustomButton';
import myfamilystyles from "../styles/my-family.module.css";
import { ConfirmationPopupGreen, useConfirmationPopup } from '../../Common/ConfirmationPopup';
import { updateAccountAndSettingsActiveTab } from '../../../store/slices/applicationSlice';
import useIsMobile from '../../../hooks/useIsMobile';

const AuPair = () => {
    const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
    const session = useSelector((state: RootState) => state.sessionInfo.data) as {
        nationality?: string;
    };
    const dispatch = useDispatch<AppDispatch>();
    const [selectedOption, setSelectedOption] = useState<string>('');
    const { disableLoader, enableLoader } = useLoader();
    const [_, setShowConsentOptions] = useState<boolean>(false);
    const [showCountryDropdown, setShowCountryDropdown] = useState<boolean>(false);
    const [selectedCountry, setSelectedCountry] = useState<string>('');
    const [showTextarea, setShowTextarea] = useState<boolean>(false);
    const [aupairchecked, setAuPairCategory] = useState<number>(0);
    const [textareaPlaceholder, setTextareaPlaceholder] = useState<string>('Not interested to work as an Au Pair');
    const [myExperience, setMyExperience] = useState(sessionInfo.data['myExperience4'] || '');
    const { confirmationProps, showConfirmationPopup } = useConfirmationPopup();
    const [isChanged, setIsChanged] = useState<boolean>(false);
    const [citizenship, setCitizenship] = useState<string>("");
    const [nationality, setNationality] = useState<string>("");
    const [countryError, setCountryError] = useState<string>("");
    const {isMobile}=useIsMobile()
    const [citizenshipError, setcitizenshipError] = useState<boolean>(false);
    const [aupairError, setAupairError] = useState(false);
    const [myExperienceError, setmyExperienceError] = useState<boolean>(false);
    const minCharLimit = 100;
    const AuPairBefore = sessionInfo.data['provider']['myExperience4'];

    const notices = [
        {
            label: 'I acknowledge & agree with all of the above',
        },
        {
            label: 'Not interested to work as an Au Pair',
        },
    ];

    const citizenshipOptions = [
        {
            value: 'AU',
            label: 'Australian Citizen',
        },
        {
            value: 'NZ',
            label: 'New Zealand Citizen',
        },
        {
            value: 'OTHER',
            label: 'Other Nationality',
        },
    ];
    useEffect(() => {
        if (session?.nationality) {
            const nationality = session.nationality.toLowerCase();

            if (nationality === "au") {
                setCitizenship("AU");
                setNationality("AU");
            } else if (nationality === "nz") {
                setCitizenship("NZ");
                setNationality("NZ");
            } else {
                setCitizenship("OTHER");
                setSelectedCountry(session.nationality);
                setNationality(session.nationality.toLowerCase());
            }
        } else {
            setCitizenship("OTHER");
            setNationality("OTHER");
        }
    }, [session?.nationality]);
    useEffect(() => {
        const category = session["provider"]["auPairCategory"];
        setAuPairCategory(category);
    }, []);

    const handleCitizenshipChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setCitizenship(value);
        setCountryError("");

        if (value === "AU") {
            setNationality("au");
            setSelectedCountry("");
        } else if (value === "NZ") {
            setNationality("nz");
            setSelectedCountry("");
        } else {
            setNationality("OTHER");
            setSelectedCountry("");
        }
    };
    useEffect(() => {
        if (sessionInfo) {
            setSelectedOption(sessionInfo.data['interestedInAuPairJobs'] ? 'I acknowledge & agree with all of the above' : 'Not interested to work as an Au Pair');
            setShowConsentOptions(sessionInfo.data['interestedInAuPairJobs']);
            setMyExperience(sessionInfo.data['provider']['myExperience4'] || '');
            if (sessionInfo.data['provider']['myExperience4']) {
                setShowTextarea(true);
            }
        }
    }, [sessionInfo]);
    const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSelectedOption(value);
        setIsChanged(true);
        if (value === 'I acknowledge & agree with all of the above') {
            setShowConsentOptions(true);
        } else {
            setShowConsentOptions(false);
            setShowCountryDropdown(false);
            setShowTextarea(false);
        }
    };
    const handleCountryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedCountryValue = event.target.value;
        setSelectedCountry(selectedCountryValue);
        setCountryError("");

        if (selectedCountryValue) {
            setNationality(selectedCountryValue.toLowerCase());
        }
    };
    const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setMyExperience(event.target.value);
        setIsChanged(true);
    };
    const handleHeaderRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value === 'yes' ? 2 : 1;
        setShowTextarea(true);
        setIsChanged(true);
        if (value === 2) {
            setTextareaPlaceholder('Provide details of your previous Au Pair experience');
        } else {
            setTextareaPlaceholder(
                'Provide details of your other childcare experience and explain why you believe you would make a good Au Pair'
            );
        }
        setAuPairCategory(value);
        setAupairError(false);
    };
    useEffect(() => {
        if (sessionInfo) {
            setMyExperience(sessionInfo.loading ? "" : sessionInfo.data["provider"].myExperience4 || "");
            setAuPairCategory(AuPairBefore !== null && AuPairBefore.length > 0 ? 2 : 1);
        }
    }, [sessionInfo]);
    useEffect(() => {
        if (citizenship && myExperience && (citizenship !== "OTHER" || selectedCountry) && aupairchecked !== 0 && myExperience.length >= minCharLimit) {
            setSelectedOption('I acknowledge & agree with all of the above');
        }
    }, [citizenship, myExperience, selectedCountry, aupairchecked]);
    const handleOkGotItClick = () => {
        dispatch(updateAccountAndSettingsActiveTab(17));
    };
    const handleButtonClick = () => {
        let hasError = false;
        if (!citizenship) {
            setcitizenshipError(true);
            hasError = true;
        } else {
            setcitizenshipError(false);
        }
        if (!myExperience) {
            setmyExperienceError(true);
            hasError = true;
        } else {
            setmyExperienceError(false);
        }
        if (citizenship === "OTHER" && !selectedCountry) {
            setCountryError("Please select a country.");
            return;
        }
        if (aupairchecked === 0) {
            setAupairError(true);
            hasError = true;
        } else {
            setAupairError(false);
        }
        if (myExperience.length < minCharLimit) {
            return;
        }
        if (sessionInfo.data['interestedInAuPairJobs'] && (sessionInfo.data['vouches'].length < 2)) {
            showConfirmationPopup(
                "References",
                "A minimum of 2 references are required to be a Juggle Street Au Pair.",
                "Ok Got It",
                null,
                handleOkGotItClick
            );
        }
        if (hasError) return;
        const interestedInAuPairJobs = selectedOption === 'I acknowledge & agree with all of the above';
        const payload = {
            ...session,
            nationality: nationality,
            country: selectedCountry,
            provider: {
                myExperience4: myExperience,
            },
            interestedInAuPairJobs: interestedInAuPairJobs,
        };
        enableLoader();
        dispatch(updateSessionInfo({ payload: payload })).finally(() => {
            disableLoader();
        });
    };
    return (
        <div style={{paddingInline:isMobile && "15px", paddingTop:isMobile && "25px" }} className={styles.utilcontainerhelper}>
            <ConfirmationPopupGreen confirmationProps={confirmationProps} />
            <div className="flex align-items-center justify-content-between mb-2 mt-1 flex-wrap">
                <header className={styles.utilheader}>
                    <h1  style={{fontSize:isMobile && "24px"}} className="p-0 m-0">Au Pair</h1>
                </header>
                <CustomButton
                    label="Save"
                    className={myfamilystyles.customButton}
                    style={{ margin: '0', width: '150px' }}
                    onClick={handleButtonClick}

                />
            </div>
            <div className="txt-clr">
                <h1 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2" style={{ fontSize: '18px', color: '#179d52' }}>
                    Important notice, please read carefully{' '}
                </h1>

                <h1 className="p-0 m-0 font-semibold mt-3" style={{ fontSize: '14px' }}>
                    Au Pairs live-in with a host family 7 days a week and look after the family’s
                    children. In return, Au Pairs receive a small weekly allowance plus lodging and
                    meals.
                </h1>

                <h1 className="p-0 m-0 font-semibold mt-3" style={{ fontSize: '14px' }}>
                    {' '}
                    Availability – you need to be able to move in with a host family and start your
                    Au Pair job within the next 4 weeks.
                </h1>
            </div>
            <div className="txt-clr mt-4">
                {notices.map((notice, index) => (
                    <div key={index} className="flex items-center gap-2 p-1 cursor-pointer">
                        <input
                            type="radio"
                            id={`notice-${index}`}
                            name="notice"
                            value={notice.label}
                            checked={selectedOption === notice.label}
                            onChange={handleRadioChange}
                            className="cursor-pointer"
                        />
                        <label
                            htmlFor={`notice-${index}`}
                            className="txt-clr font-medium cursor-pointer"
                            style={{ fontSize: '16px' }}
                        >
                            {notice.label}
                        </label>
                    </div>
                ))}
            </div>

            {selectedOption === 'I acknowledge & agree with all of the above' && (
                <>
                    <div className="txt-clr mt-4">
                        <h1 className="m-0 p-1 txt-clr font-medium line-height-1 mt-2" style={{ fontSize: '18px', color: citizenshipError ? 'red' : '#179d52' }}>
                            Citizenship and Right to Work
                        </h1>
                        {citizenshipOptions.map((option, index) => (
                            <div key={index}>
                                <input
                                    type="radio"
                                    id={option.value}
                                    name="citizenship"
                                    value={option.value}
                                    checked={citizenship === option.value}
                                    onChange={handleCitizenshipChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor={option.value}
                                    className="cursor-pointer txt-clr font-medium"
                                    style={{ fontSize: "16px" }}
                                >
                                    {option.label}
                                </label>
                            </div>
                        ))}
                        {citizenship === "OTHER" && (
                            <div className="pl-3">
                                <select
                                    value={selectedCountry}
                                    onChange={handleCountryChange}
                                    className="cursor-pointer px-3 mt-2"
                                    style={{
                                        width: "200px",
                                        height: "40px",
                                        borderRadius: "10px",
                                        border: "1px solid #585858",
                                    }}
                                >
                                    <option value="">Select a country</option>
                                    {c.countriesIso.map((country) => (
                                        <option key={country.id} value={country.value}>
                                            {country.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        )}
                    </div>
                    {showCountryDropdown && (
                        <div className="txt-clr mt-2">
                            <select
                                value={selectedCountry}
                                onChange={handleCountryChange}
                                className="cursor-pointer"
                                style={{
                                    width: "200px",
                                    height: "40px",
                                    borderRadius: "10px",
                                    border: "1px solid #585858",
                                    padding: '10px'
                                }}
                            >
                                <option value="">Select a country</option>
                                {c.countriesIso.map((country) => (
                                    <option key={country.id} value={country.value}>
                                        {country.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}
                    {countryError && (
                        <p className="p-0 m-0" style={{ color: "red" }}>
                            {countryError}
                        </p>
                    )}
                    <div className="txt-clr mt-4">
                        <h1 className="p-0 m-0 font-bold" style={{ fontSize: '18px', color: '#179D52' }}>
                            Au Pair Experience
                        </h1>
                        <h1 className="p-0 m-0 font-medium" style={{ fontSize: '18px', color: aupairError ? 'red' : '#179d52' }}>
                            Have you worked as an Au Pair before?
                        </h1>
                        <div className='flex '>
                            <div className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="radio"
                                    id="info-yes"
                                    name="info"
                                    value="yes"
                                    checked={aupairchecked === 2}
                                    // checked={showTextarea && myExperience !== ''}
                                    onChange={handleHeaderRadioChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor="info-yes"
                                    className="txt-clr font-medium cursor-pointer"
                                    style={{ fontSize: '16px' }}
                                >
                                    Yes
                                </label>
                            </div>
                            <div className="flex items-center gap-2 p-1 cursor-pointer">
                                <input
                                    type="radio"
                                    id="info-no"
                                    name="info"
                                    value="no"
                                    checked={aupairchecked === 1}
                                    // checked={!showTextarea}
                                    onChange={handleHeaderRadioChange}
                                    className="cursor-pointer"
                                />
                                <label
                                    htmlFor="info-no"
                                    className="txt-clr font-medium cursor-pointer"
                                    style={{ fontSize: '16px' }}
                                >
                                    No
                                </label>
                            </div>
                        </div>
                        <h1 className="p-0 m-0 font-medium" style={{ fontSize: '18px', color: myExperienceError ? 'red' : '#179d52' }}>
                            Describe your Au Pair experience
                        </h1>
                    </div>

                    {showTextarea && (
                        <div className="mt-4" style={{ height: '130px' }}>
                            <InputTextarea
                                autoResize
                                value={myExperience}
                                required
                                onChange={handleTextareaChange}
                                rows={3}
                                cols={30}
                                className={styles.inputTextareafamily}
                                placeholder={textareaPlaceholder}
                                style={{ width: '100%', height: '130px' }}
                            />
                            <p
                                style={{
                                    fontSize: '14px',
                                    color: myExperience.length < minCharLimit ? 'red' : 'green',
                                    fontWeight: '400',
                                }}
                            >
                                {myExperience.length < minCharLimit &&
                                    `${minCharLimit - myExperience.length} characters remaining`}
                            </p>
                        </div>

                    )}

                </>
            )}
        </div>
    )
}

export default AuPair