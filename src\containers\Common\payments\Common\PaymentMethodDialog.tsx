import React, { useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { RadioButton } from 'primereact/radiobutton';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import AddCardDialog from './AddCardDialog';
import environment from '../../../../helper/environment';
import styles from '../../styles/payment-method-dialog.module.css';
import { Divider } from 'primereact/divider';
import { FaCreditCard, FaUniversity } from 'react-icons/fa';
import { SiVisa, SiMastercard, SiAmericanexpress, SiDiscover } from 'react-icons/si';

const stripePromise = loadStripe(environment.getStripePublishableKey(window.location.hostname));

// Function to get the appropriate card icon
const getCardIcon = (cardName: string) => {
  const name = cardName.toLowerCase();
  const iconProps = { size: 32, className: styles.cardIcon };

  switch (name) {
    case 'visa':
      return <SiVisa {...iconProps} style={{ color: '#1A1F71' }} />;
    case 'mastercard':
      return <SiMastercard {...iconProps} style={{ color: '#EB001B' }} />;
    case 'amex':
    case 'american express':
      return <SiAmericanexpress {...iconProps} style={{ color: '#006FCF' }} />;
    case 'discover':
      return <SiDiscover {...iconProps} style={{ color: '#FF6000' }} />;
    case 'payto':
      return <FaUniversity {...iconProps} style={{ color: '#179D52' }} />;
    default:
      return <FaCreditCard {...iconProps} style={{ color: '#666' }} />;
  }
};

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  owner: string;
  number: string;
  fee: number;
  token?: string;
}

interface PaymentMethodDialogProps {
  visible: boolean;
  onHide: () => void;
  paymentMethods: PaymentMethod[];
  amount: number;
  onPay: (method: PaymentMethod) => void;
  onAddNewCard?: (cardData: PaymentMethod) => void;
}

const PaymentMethodDialog: React.FC<PaymentMethodDialogProps> = ({
  visible,
  onHide,
  paymentMethods,
  amount,
  onPay,
  onAddNewCard
}) => {
  const [selectedMethod, setSelectedMethod] = useState(paymentMethods[0]?.id || '');
  const [showAddCardDialog, setShowAddCardDialog] = useState(false);

  const handlePay = () => {
    const method = paymentMethods.find((m: PaymentMethod) => m.id === selectedMethod);
    if (method) {
      onPay(method);
    }
  };

  const handleAddNewCard = () => {
    setShowAddCardDialog(true);
  };

  const handleCardAdded = (cardData: PaymentMethod) => {
    if (onAddNewCard) {
      onAddNewCard(cardData);
    }
    setShowAddCardDialog(false);
  };

  const renderHeader = () => {
    return (
      <div className={styles.dialogHeader}>
        <button className={styles.backButton} onClick={onHide}>
          <i className="pi pi-arrow-left" /> Back to Payments
        </button>
        <div className={styles.approvalStatus}>
          <div className={styles.tickWrapper}>
            <svg viewBox="0 0 100 100" width="30" height="30">
              {/* Outer static light green circle */}
              <circle className={styles.outerCircle} cx="50" cy="50" r="50" />

              {/* Inner green circle that beats */}
              <circle className={styles.innerCircle} cx="50" cy="50" r="40" />

              {/* Checkmark path with drawing animation */}
              <path
                className={styles.checkmark}
                d="M30 50 L45 65 L70 35"
                stroke="#ffffff"
                strokeWidth="8"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
              />
            </svg>
          </div>
          <span className={styles.approvalText}>Invoice approved</span>
        </div>
      </div>
    );
  };

  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      position="bottom"
      modal
     
      header="Choose Payment method"
      draggable={false}
      resizable={false}
      showHeader={false}
      content={
        <div  className={styles.paymentDialog}>
        {renderHeader()}
      <h2 className={styles.dialogTitle}>Choose Payment method</h2>
      <div className={styles.paymentMethods}>
        {paymentMethods.map(method => (
          <div 
            key={method.id} 
            className={`${styles.paymentOption} ${selectedMethod === method.id ? styles.selectedOption : ''}`}
          >
            <div className={styles.methodDetails}>
              <div className={styles.methodHeader}>
                {getCardIcon(method.name)}
                <div className={styles.methodNameContainer}>
                  <span className={styles.methodName}>{method.name}</span>
                  <span className={styles.methodOwner}>{method.owner}</span>
                  <span className={styles.methodNumber}>•••• •••• •••• {method.number}</span>
                </div>
              </div>
            </div>
            {method.fee > 0 && (
              <span className={styles.feeTag}>{method.fee}% Merchant Fee</span>
            )}
            {method.fee === 0 && (
              <span className={styles.noFeeTag}>No Transaction Fee</span>
            )}
            <RadioButton
              inputId={method.id}
              value={method.id}
              onChange={(e) => setSelectedMethod(e.value)}
              checked={selectedMethod === method.id}
            />
          </div>
        ))}
       
        <div className={styles.addCard} onClick={handleAddNewCard}>
          <i className="pi pi-plus" /> Add new card
        </div>
         <Divider/>
      </div>
      <div className={styles.paymentFooter}>
        <p>
          When you approve this payment, the specified amount will be instantly 
          charged to your selected payment method. By proceeding, you also 
          acknowledge and agree to Juggle Street's 
          <a href="#" className={styles.footerLink}> Platform Fee</a> and 
          <a href="#" className={styles.footerLink}> Terms & Conditions</a>.
        </p>
        <Button
          label={`Pay $${amount.toFixed(2)}`}
          className={styles.payButton}
          onClick={handlePay}
        />
      </div>

      <Elements stripe={stripePromise}>
        <AddCardDialog
          visible={showAddCardDialog}
          onHide={() => setShowAddCardDialog(false)}
          onSuccess={handleCardAdded}
        />
      </Elements>
        </div>
      }
    />
    
  
  );
};

export default PaymentMethodDialog;