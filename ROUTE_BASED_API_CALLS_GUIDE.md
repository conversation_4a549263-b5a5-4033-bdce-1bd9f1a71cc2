# Route-Based API Calls Implementation Guide

## 🎯 **What Was Fixed**

I've updated all three hooks to trigger API calls when you navigate between different timesheet routes, not just on component mount.

### ✅ **1. Route-Based API Triggering**
- **API calls now trigger** when you change routes/tabs
- **Each hook monitors its specific route** and only calls API when active
- **No unnecessary API calls** when not on the relevant route

### ✅ **2. Updated Hooks**
- **`useTimesheetData.ts`** → Triggers on `/awaiting-confirmation` route
- **`useAdjustedTimesheetDetails.ts`** → Triggers on `/adjusted-timesheets` route  
- **`useAwaitingApprovalDetails.ts`** → Triggers on `/awaiting-approval` route

### ✅ **3. Smart Route Detection**
- **Uses `useLocation()` hook** to monitor route changes
- **Conditional API calls** based on current route
- **Console logging** for debugging route changes

## 🔧 **Implementation Details**

### **1. useTimesheetData.ts (Awaiting Confirmation)**

#### **Route Detection:**
```typescript
import { useLocation } from 'react-router-dom';

export const useTimesheetData = () => {
  const location = useLocation();
  
  // Check if current route is awaiting-confirmation
  const isAwaitingConfirmationRoute = location.pathname.includes('/awaiting-confirmation') || 
                                     location.pathname === '/parent-home/timesheet';
```

#### **Route-Based useEffect:**
```typescript
useEffect(() => {
  // Only fetch data when on the awaiting-confirmation route or base timesheet route
  if (isAwaitingConfirmationRoute) {
    console.log('🔄 Route changed to awaiting-confirmation, fetching data...');
    fetchTimesheetData();
  } else {
    console.log('📍 Not on awaiting-confirmation route, skipping API call');
  }
}, [location.pathname]); // Trigger when route changes
```

### **2. useAdjustedTimesheetDetails.ts (Adjusted Timesheets)**

#### **Route Detection:**
```typescript
import { useLocation } from 'react-router-dom';

export const useAdjustedTimesheetDetails = () => {
  const location = useLocation();
  
  // Check if current route is adjusted-timesheets
  const isAdjustedTimesheetsRoute = location.pathname.includes('/adjusted-timesheets');
```

#### **Route-Based useEffect:**
```typescript
useEffect(() => {
  // Only fetch data when on the adjusted-timesheets route
  if (isAdjustedTimesheetsRoute) {
    console.log('🔄 Route changed to adjusted-timesheets, fetching data...');
    fetchAdjustedTimesheetData();
  } else {
    console.log('📍 Not on adjusted-timesheets route, skipping API call');
  }
}, [location.pathname]); // Trigger when route changes
```

### **3. useAwaitingApprovalDetails.ts (Awaiting Approval)**

#### **Route Detection:**
```typescript
import { useLocation } from 'react-router-dom';

export const useAwaitingApprovalDetails = () => {
  const location = useLocation();
  
  // Check if current route is awaiting-approval
  const isAwaitingApprovalRoute = location.pathname.includes('/awaiting-approval');
```

#### **Route-Based useEffect:**
```typescript
useEffect(() => {
  // Only fetch data when on the awaiting-approval route
  if (isAwaitingApprovalRoute) {
    console.log('🔄 Route changed to awaiting-approval, fetching data...');
    fetchAwaitingApprovalData();
  } else {
    console.log('📍 Not on awaiting-approval route, skipping API call');
  }
}, [location.pathname]); // Trigger when route changes
```

## 🔄 **Route-Based Data Flow**

### **When Navigating to `/parent-home/timesheet/awaiting-confirmation`:**
```
1. User navigates to /parent-home/timesheet/awaiting-confirmation
         ↓
2. location.pathname changes
         ↓
3. useTimesheetData() detects route change
         ↓
4. isAwaitingConfirmationRoute = true
         ↓
5. fetchTimesheetData() API call triggered
         ↓
6. Service.getTimeSheet() called
         ↓
7. Data loaded and displayed
```

### **When Navigating to `/parent-home/timesheet/adjusted-timesheets`:**
```
1. User navigates to /parent-home/timesheet/adjusted-timesheets
         ↓
2. location.pathname changes
         ↓
3. useAdjustedTimesheetDetails() detects route change
         ↓
4. isAdjustedTimesheetsRoute = true
         ↓
5. fetchAdjustedTimesheetData() API call triggered
         ↓
6. Service.getTimeSheet() called and filtered for status 2
         ↓
7. Adjusted timesheet data loaded and displayed
```

### **When Navigating to `/parent-home/timesheet/awaiting-approval`:**
```
1. User navigates to /parent-home/timesheet/awaiting-approval
         ↓
2. location.pathname changes
         ↓
3. useAwaitingApprovalDetails() detects route change
         ↓
4. isAwaitingApprovalRoute = true
         ↓
5. fetchAwaitingApprovalData() API call triggered
         ↓
6. Service.getTimeSheet() called and filtered for status 3
         ↓
7. Awaiting approval data loaded and displayed
```

## 📊 **Expected Console Output**

### **When Changing Routes:**
```
// Navigating to awaiting-confirmation
🔄 Route changed to awaiting-confirmation, fetching data...
API Response getTimeSheet: [array of all timesheets]
Mapped timesheet data: [processed data]

// Navigating to adjusted-timesheets  
📍 Not on awaiting-confirmation route, skipping API call
🔄 Route changed to adjusted-timesheets, fetching data...
API Response getTimeSheet for adjusted: [array of all timesheets]
Filtered adjusted timesheet data (status 2): [status 2 entries]

// Navigating to awaiting-approval
📍 Not on adjusted-timesheets route, skipping API call
🔄 Route changed to awaiting-approval, fetching data...
API Response getTimeSheet for awaiting approval: [array of all timesheets]
Filtered awaiting approval data (status 3): [status 3 entries]
```

## 🧪 **Testing**

### **1. Test Route Changes:**
- Start at `/parent-home/timesheet` → Should trigger awaiting-confirmation API
- Navigate to `/parent-home/timesheet/adjusted-timesheets` → Should trigger adjusted API
- Navigate to `/parent-home/timesheet/awaiting-approval` → Should trigger approval API
- Navigate back to `/parent-home/timesheet/awaiting-confirmation` → Should trigger confirmation API again

### **2. Check Console Logs:**
- Should see route change logs for each navigation
- Should see API call logs only for the active route
- Should see "skipping API call" logs for inactive routes

### **3. Verify Data Loading:**
- Each route should show fresh data when navigated to
- Loader should appear during API calls
- Badge counts should update based on fresh data

### **4. Network Tab:**
- Should see API calls in network tab when changing routes
- No duplicate or unnecessary API calls

## 🎯 **Benefits**

### **✅ Fresh Data on Route Change:**
- Each route gets fresh data when navigated to
- No stale data from previous visits
- Real-time updates when switching tabs

### **✅ Efficient API Usage:**
- Only calls API for the active route
- No unnecessary API calls for inactive routes
- Prevents multiple simultaneous API calls

### **✅ Better User Experience:**
- Data loads when user navigates to route
- Loader shows during data fetching
- Consistent behavior across all routes

### **✅ Easy Debugging:**
- Clear console logs for route changes
- Easy to track which API calls are triggered
- Simple to troubleshoot route-specific issues

## 🚀 **Summary**

Your timesheet routes now have proper API call triggering:
- ✅ **API calls trigger on route changes** (not just component mount)
- ✅ **Each route calls its specific API** when navigated to
- ✅ **Smart route detection** prevents unnecessary API calls
- ✅ **Fresh data loading** for each route visit
- ✅ **Console logging** for debugging route changes
- ✅ **Efficient API usage** with conditional calls

Now when you navigate between:
- `/parent-home/timesheet/awaiting-confirmation` → API call triggered
- `/parent-home/timesheet/adjusted-timesheets` → API call triggered  
- `/parent-home/timesheet/awaiting-approval` → API call triggered

The API calls will work properly on route changes! 🎉
