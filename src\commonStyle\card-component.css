/* src/common-components/cardcomponent.css */
.custom-card {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff;
    box-shadow: 0 3px 7px rgba(0, 0, 0, 0.1);
}

.custom-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-container {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.card-header {
    margin-bottom: 16px;
}

.card-body {
    margin-bottom: 16px;
}

.card-error {
    color: red;
    font-size: 14px;
}
