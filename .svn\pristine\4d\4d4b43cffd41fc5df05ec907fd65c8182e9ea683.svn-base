import { useEffect, useState } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import Childcare from './Tabs/Childcare';
import OddJobs from './Tabs/OddJobs';
import Profile from './Tabs/Profile';
import Service from '../../../services/services';
import useLoader from '../../../hooks/LoaderHook';
import { Helper } from '../ProviderProfile/types';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import SideCard from '../ProviderProfile/SideCard';
import styles from '../styles/child-careTab.module.css';
import Aupair from './Tabs/Aupair';

const ViewProfile = () => {
  const session = useSelector((state: RootState) => state.sessionInfo.data);
  const [activeIndex, setActiveIndex] = useState(0);
  const { enableLoader, disableLoader } = useLoader();
  const [helper, setHelper] = useState<Helper | null>(null);
  const id = session["id"];
  const lodHelper = () => {
    enableLoader();
    Service.getHelper(
      id,
      (data: Helper) => {
        setHelper(data);
        disableLoader();
      },
      (error) => {
        disableLoader();
        // alert(error);
      }
    );
  };
  useEffect(() => {
    lodHelper();
  }, []);
  const getHeaderClassName = (index) => {
    return `${styles.tabHeader} ${activeIndex === index ? styles.tabHeaderSelected : ''}`;
  };

  return (
    <div>
      <SideCard helper={helper ?? null} requestId={null} width='100%' boxShadow='none' userId={id} refresh={lodHelper} />
      <br />
      <div className='flex gap-2 p-4'>
        <TabView
          activeIndex={activeIndex}
          onTabChange={(e) => setActiveIndex(e.index)}
          className='ml-5'
        >
          <TabPanel
            header={
              <div
                className={getHeaderClassName(0)}
                onClick={() => setActiveIndex(0)}
                style={{ cursor: 'pointer' }}
              >
                Profile
              </div>
            }
          >
            <Profile helper={helper ?? null} />
          </TabPanel>

          {helper?.interestedInChildcareJobs && (
            <TabPanel
              header={
                <div
                  className={getHeaderClassName(1)}
                  onClick={() => setActiveIndex(1)}
                  style={{ cursor: 'pointer' }}
                >
                  Childcare
                </div>
              }
            >
              <Childcare helper={helper ?? null} />
            </TabPanel>
          )}

          {helper?.interestedInAuPairJobs && (
            <TabPanel
              header={
                <div
                  className={getHeaderClassName(2)}
                  onClick={() => setActiveIndex(2)}
                  style={{ cursor: 'pointer' }}
                >
                  AuPair
                </div>
              }
            >
              <Aupair helper={helper ?? null} />
            </TabPanel>
          )}

          {helper?.interestedInOddJobs && (
            <TabPanel
              header={
                <div
                  className={getHeaderClassName(3)}
                  onClick={() => setActiveIndex(3)}
                  style={{ cursor: 'pointer' }}
                >
                  Odd Jobs
                </div>
              }
            >
              <OddJobs helper={helper ?? null} />
            </TabPanel>
          )}
        </TabView>

      </div>
    </div>
  );
};

export default ViewProfile;
