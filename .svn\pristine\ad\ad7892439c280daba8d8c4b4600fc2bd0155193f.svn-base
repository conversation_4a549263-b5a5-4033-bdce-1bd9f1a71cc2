import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TimesheetDetailsPopup from '../TimesheetDetailsPopup';
import { TimesheetDetails } from '../../../hooks/useTimesheetDetails';

// Mock the hooks and services
jest.mock('../../../hooks/LoaderHook', () => ({
  __esModule: true,
  default: () => ({
    enableLoader: jest.fn(),
    disableLoader: jest.fn(),
  }),
}));

jest.mock('../../../services/services', () => ({
  __esModule: true,
  default: {
    postUpdateHistory: jest.fn(),
  },
}));

// Mock the AwaitingConfirmationCard component
jest.mock('../../containers/Common/payments/Common/AwaitingConfirmationCard', () => {
  return function MockAwaitingConfirmationCard({ onSubmit, onGoBack, profileName }: any) {
    return (
      <div data-testid="awaiting-confirmation-card">
        <div>Profile: {profileName}</div>
        <button onClick={onSubmit} data-testid="submit-btn">Submit</button>
        <button onClick={onGoBack} data-testid="go-back-btn">Go Back</button>
      </div>
    );
  };
});

const mockTimesheetEntry = {
  id: 1,
  status: 'Awaiting Your Approval',
  type: 'One Off Job',
  date: '15 January 2024',
  location: '123 Test Street, Test City',
  userName: 'John D',
  originalImageUrl: 'test-image.jpg',
};

const mockTimesheetDetails: TimesheetDetails = {
  id: 1,
  firstName: 'John',
  lastName: 'D',
  originalImageUrl: 'test-image.jpg',
  jobType: 'One Off Job',
  jobDate: '2024-01-15',
  formattedAddress: '123 Test Street, Test City',
  price: 25,
  overtimeRate: 30,
  jobStartTime: '9:00 AM',
  jobEndTime: '5:00 PM',
  status: 'pending',
  estimatedJobValue: 200,
  estimatedJobHours: 8,
  timesheetId: 1,
  jobId: 1,
  applicantId: 1,
  userId: 1,
};

describe('TimesheetDetailsPopup', () => {
  const mockOnClose = jest.fn();
  const mockOnApprovalSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing when selectedEntry is null', () => {
    const { container } = render(
      <TimesheetDetailsPopup
        selectedEntry={null}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders nothing when timesheetDetails is null', () => {
    const { container } = render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={null}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders popup when both selectedEntry and timesheetDetails are provided', () => {
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    expect(screen.getByTestId('awaiting-confirmation-card')).toBeInTheDocument();
    expect(screen.getByText('Profile: John D')).toBeInTheDocument();
  });

  it('calls onClose when go back button is clicked', () => {
    render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    fireEvent.click(screen.getByTestId('go-back-btn'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calculates hours and total price correctly', () => {
    // This test verifies the utility functions work correctly
    const component = render(
      <TimesheetDetailsPopup
        selectedEntry={mockTimesheetEntry}
        timesheetDetails={mockTimesheetDetails}
        onClose={mockOnClose}
        onApprovalSuccess={mockOnApprovalSuccess}
      />
    );

    // The component should render without errors, indicating calculations work
    expect(screen.getByTestId('awaiting-confirmation-card')).toBeInTheDocument();
  });
});
