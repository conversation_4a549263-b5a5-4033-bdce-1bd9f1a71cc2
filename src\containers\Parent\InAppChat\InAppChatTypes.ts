export interface Message {
  senderId: any;
  subject: string | null;
  body: string;
  deliveryStatus: number;
  sentAt: string;
  sentOn: string;
  id: string;
  retryAttempts: number;
}

export interface Participant {
  participantId: number;
  firstName: string;
  publicName: string;
  lastInitial: string;
  imageUrl: string;
  addedOn: string;
  jugglerTypes: number;
  unreadMessages: number;
  id: string;
}

export interface ChatSession {
  conversationType: number;
  initiatedBy: number;
  startedOn: string;
  lastMessageDate: string;
  messagesCount: number;
  lastMessage: string;
  lastMessageTrimmed: string;
  isEnabled: boolean;
  unreadMessages: number;
  conversationStatus: number;
  conversationStatusText: string;
  participants: Participant[];
  messages: Message[];
  id: string;
}

export interface SendMessageParams {
  conversationId: string;
  message: string;
}

export interface SendChatState {
  messages: Message[];
  newMessage: string;
  isLoading: boolean;
  insuranceNote?: string;
}
export interface SendChatState {
  messages: Message[];
  newMessage: string;
  isLoading: boolean;
  isEnable:boolean;
  participants: Participant[];
}
