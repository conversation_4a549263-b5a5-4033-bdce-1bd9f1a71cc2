.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: var(--Juggle-street-background-colour, #F0F4F7);
    font-family: Arial, sans-serif;
}

.header {
    background: linear-gradient(90deg, #FFA500 0%, #37A950 100%);
    height: 68px;
    width: 100%;
    position: absolute;
    top: 0;
}

.content {
    text-align: center;
    padding: 20px;
    width: 100%;
    max-width: 430px;
}

.checkmarkContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 165px;
    border-radius: 20px;
    border: 1px solid #179D52;
    background-color: #fff;
    margin: 0;
    gap: 5px;
}

.checkmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    animation: draw 0.8s ease forwards, pulse 1.2s ease-in-out 0.8s 2;
    transform-origin: center;
}


.outerCircle {
    animation: outerPulse 1.5s ease-in-out infinite;
    transform-origin: center;
}

@keyframes draw {
    to {
        stroke-dashoffset: 0;
    }
}


@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.15);
    }
}

@keyframes outerPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.title {
    color: #585858;
    font-size: 22px;
    font-weight: 700;
    margin: 0 0 10px 0;
}

.sub {
    color: #179D52;
    font-size: 18px;
    font-weight: 700;
}

.wait {
    color: #585858;
    font-size: 16px;
    font-weight: 300;
    margin-top: 0;
}