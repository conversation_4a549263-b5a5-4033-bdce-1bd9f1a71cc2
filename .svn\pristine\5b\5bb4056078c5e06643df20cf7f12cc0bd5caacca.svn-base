import { InputText } from "primereact/inputtext";
import React, { useEffect, useState } from "react";
import "primeicons/primeicons.css";
import { useJoinNowData } from "../../../model/JoinNowContext";
import { validateAddress, validatePhoneNumber } from "../../utils/validation";
import Auth from "../../../services/authService";
import { Dropdown } from "primereact/dropdown";
import environment from "../../../helper/environment";
import { useNavigateTo } from "../../../helper/navigation";
import "../../utils/util.css";
import "./join-now.css";
import { useDispatch } from "react-redux";
import { AppDispatch, updateRegisterSession } from "../../../store";
import c from "../../../helper/juggleStreetConstants";
import { createNewSessionInfo } from "../../../store/slices/sessionInfoSlice";
import useLoader from "../../../hooks/LoaderHook";

interface SignUpPageProps {
  onPrevious?: () => void;
}
interface FormData {
  phoneNumber: string;
  addressLine1: string;
  suburb: string;
  postCode: string;
  state: string;
}

interface FormErrors {
  phoneNumber: string;
  addressLine1: string;
  global: string;
}
export const Joinnow4: React.FC<SignUpPageProps> = ({ onPrevious }) => {
  const [editAddress, setEditAddress] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const { joinNowData, dispatch } = useJoinNowData();
  const [showMessage, setShowMessage] = useState(false);
  const { enableLoader, disableLoader } = useLoader();
  const sDispatch = useDispatch<AppDispatch>();
  const showDateOfBirth = joinNowData.clientType === 2;

  const navigateTo = useNavigateTo();

  const cities = [
    { name: "nsw" },
    { name: "tas" },
    { name: "qld" },
    { name: "nsw" },
    { name: "sa" },
    { name: "wa" },
  ];

  const [formData, setFormData] = useState<FormData>({
    phoneNumber: joinNowData.phoneNumber || "",
    addressLine1: joinNowData.addressLine1 || "",
    suburb: joinNowData.address?.suburb || "",
    postCode: joinNowData.address?.postCode || "",
    state: joinNowData.address?.state || "",
  });

  // Error state
  const [error, setError] = useState<FormErrors>({
    phoneNumber: "",
    addressLine1: "",
    global: "",
  });

  const handleEditAddress = () => {
    setEditAddress(!editAddress);
  };

  const redirectAfterHome = () => {
    navigateTo("/", { replace: true });
  };

  useEffect(() => {
    const addressValid = formData.addressLine1.trim();
    const phoneValid = formData.phoneNumber.trim() !== "";
    setIsFormValid(addressValid && phoneValid);
  }, [formData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setFormData((prev) => ({ ...prev, [name]: value }));
    setError((prev) => ({ ...prev, [name]: "" }));

    if (name === "addressLine1") {
      dispatch({
        type: "UPDATE_FORM",
        payload: { [name]: value },
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      enableLoader();

      // Validate address
      if (!validateAddress(formData.addressLine1.trim())) {
        setError((prev) => ({
          ...prev,
          addressLine1:
            "Please enter a valid street address (e.g. 348 Northbourne Avenue)",
        }));
        disableLoader();
        return;
      }

      // Validate phone
      if (!validatePhoneNumber(formData.phoneNumber.trim())) {
        setError((prev) => ({
          ...prev,
          phoneNumber: "Please enter a valid phone number",
        }));
        disableLoader();
        return;
      }

      // No validation errors, proceed with registration
      const deviceTypeName = joinNowData.deviceType || "DESKTOP";
      const deviceType =
        c.deviceType[deviceTypeName] || c.deviceType["DESKTOP"];
      const country = environment.getCountry(window.location.hostname);
      const payload = {
        ...joinNowData,
        phoneNumber: formData.phoneNumber,
        address: {
          ...joinNowData.address,
          addressLine1: formData.addressLine1,
          postCode: formData.postCode,
          suburb: formData.suburb,
          state: formData.state,
        },

        deviceType: deviceType,
        country: country,
        dateOfBirth: showDateOfBirth
          ? new Date(joinNowData.dateOfBirth).toISOString()
          : null,
        clientType:
          joinNowData.clientType === 1
            ? 1
            : joinNowData.clientType === 2
              ? 0
              : 2,
        accountType:
          joinNowData.clientType === 1 || joinNowData.clientType === 3 ? 1 : 2,
        clientCategory: joinNowData.clientCategory,

        providerType: joinNowData.clientType === 2 ? 1 : 0,
      };
      // 

      Auth.registerUserV2(
        payload,
        async (_) => {
          Auth.handleAttemptLogins(
            joinNowData.email,
            joinNowData.password,
            async (data) => {
              await sDispatch(createNewSessionInfo(data));
              await updateRegisterSession();
              disableLoader();
              redirectAfterHome();
            },
            (_) => {
              disableLoader();
            }
          );
        },
        (error) => {
          disableLoader();
          setError((prevErrors) => ({
            ...prevErrors,
            global: error.message || "Registration failed. Please try again.",
          }));
        }
      );
    } catch (error) {
      disableLoader();
      setError((prevErrors) => ({
        ...prevErrors,
        global:
          (error as Error).message || "Registration failed. Please try again.",
      }));
    }
  };

  const handlePrevious = (e: React.MouseEvent) => {
    e.preventDefault();
    dispatch({ type: "UPDATE_FORM", payload: formData });
    onPrevious && onPrevious();
  };

  return (
    <form
      className="pr-4 pl-4"
      onSubmit={handleSubmit}
      style={{ marginTop: "-29px", display: "flex", flexDirection: "column" }}
    >
      <div className="flex-column flex justify-content-center align-items-center">
        <p className="h-joinnow p-0 m-0">
          {joinNowData.clientType === 3
            ? "Details for Juggle St jobs"
            : "Account Set Up"}
        </p>
        <br />
        <div className="formgrid grid mt-1">
          <div className="field col-12 md:col-12">
            <label className="mb-3 h-joinnow4-lable">Your mobile number</label>
            <div className="input-container">
              <InputText
                name="phoneNumber"
                maxLength={15}
                placeholder=""
                value={formData.phoneNumber}
                onChange={(e) => {
                  if (
                    /^[\d+]*$/.test(e.target.value) &&
                    e.target.value.length <= 15
                  ) {
                    handleChange(e);
                  }
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderBottom = "1px solid green";
                  setShowMessage(true);
                }}
                onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                // className={error.phoneNumber ? 'border-red' : ''}
                className={`input-placeholder ${error.phoneNumber
                  ? "border-red"
                  : formData.phoneNumber
                    ? "border-green"
                    : ""
                  }`}
              />
              <label htmlFor="phoneNumber" className="label-name ">
                Mobile number
              </label>
            </div>
          </div>
        </div>
        {showMessage && ( // Conditionally render the message
          <div
            className="flex flex-column align-items-center mb-2"
            style={{ marginTop: "-19px" }}
          >
            <p
              className="h-joinnow2-prg col-12 md:col-7 pr-2 pl-2"
              style={{
                textAlign: "justify",
                textAlignLast: "center",
              }}
            >
              {joinNowData.clientType === 3
                ? "Your mobile number and address are only shared with Juggle Street helpers after you award a job."
                : "Your mobile number and home address are only shared with Juggle Street helpers after you award a job."}
            </p>
          </div>
        )}
        <div className="formgrid grid">
          <div className="field col-12 md:col-6" style={{ width: "100%" }}>
            <label className="mb-3 h-joinnow4-lable">Your street address</label>
            <div className="input-container">
              <InputText
                name="addressLine1"
                value={formData.addressLine1}
                onChange={handleChange}
                placeholder=""
                onFocus={(e) => {
                  e.currentTarget.style.borderBottom = "1px solid green";
                  document.getElementById("addressLine1Label")!.innerText =
                    "Street Address";
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderBottom = "";
                  if (formData.addressLine1 === "") {
                    document.getElementById("addressLine1Label")!.innerText =
                      "248 Nourbourne Avenue";
                  }
                }}
                className={`input-placeholder ${error.addressLine1
                  ? "border-red"
                  : formData.addressLine1
                    ? "border-green"
                    : ""
                  }`}
              />
              <label id="addressLine1Label" className="label-name">
                {formData.addressLine1 === ""
                  ? "eg: 248 Nourbourne Avenue"
                  : "Street Address"}
              </label>
            </div>
          </div>
        </div>
        <div className="" style={{ display: "contents" }}>
          {editAddress ? (
            <>
              <div className="field col-12 md:col-3 mt-0">
                <InputText
                  name="suburb"
                  placeholder="Enter Your suburb state pincode"
                  value={formData.suburb}
                  onChange={handleChange}
                  onFocus={(e) =>
                    (e.currentTarget.style.borderBottom = "1px solid green")
                  }
                  onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                />
              </div>
              <div className="field col-12 md:col-3 mt-0">
                <InputText
                  name="postCode"
                  placeholder="Enter Your suburb state pincode"
                  value={formData.postCode}
                  onChange={handleChange}
                  onFocus={(e) =>
                    (e.currentTarget.style.borderBottom = "1px solid green")
                  }
                  onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                />
              </div>
              <div className="field col-12 md:col-3 mt-0">
                <Dropdown
                  name="state"
                  value={formData.state}
                  options={cities}
                  optionLabel="name"
                  optionValue="name"
                  className="w-full"
                  onChange={(e) => {
                    // Update the state by extracting only the state property from the address object
                    setFormData({
                      ...formData,
                      state: e.value, // This should directly set the state name from the selected option
                    });
                  }}
                  onFocus={(e) =>
                    (e.currentTarget.style.borderBottom = "1px solid green")
                  }
                  onBlur={(e) => (e.currentTarget.style.borderBottom = "")}
                />
              </div>
            </>
          ) : (
            <div style={{ marginTop: "-12px" }}>
              <p
                style={{ fontSize: "12px", fontWeight: 500, color: "#585858" }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="17"
                  height="17"
                  viewBox="0 0 24 24"
                  style={{ verticalAlign: "middle", color: "#585858" }} // Change this color as needed
                >
                  <g fill="none">
                    <path
                      fill="currentColor"
                      fillRule="evenodd"
                      d="M12.398 17.804C13.881 17.034 19 14.016 19 9A7 7 0 1 0 5 9c0 5.016 5.119 8.035 6.602 8.804a.86.86 0 0 0 .796 0M12 12a3 3 0 1 0 0-6a3 3 0 0 0 0 6"
                      clipRule="evenodd"
                    />
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeWidth="2"
                      d="M18.062 16.5c.615.456.938.973.938 1.5s-.323 1.044-.938 1.5c-.614.456-1.498.835-2.562 1.098S13.229 21 12 21s-2.436-.139-3.5-.402s-1.948-.642-2.562-1.098C5.323 19.044 5 18.527 5 18s.323-1.044.938-1.5"
                    />
                  </g>
                </svg>
                &nbsp;{formData.suburb}, {formData.state.toUpperCase()}, {formData.postCode}
                <span
                  className="cursor-pointer text-primary"
                  onClick={handleEditAddress}
                ></span>
              </p>
            </div>
          )}
        </div>
      </div>

      <div className="text-center error-message">
        {error.phoneNumber && <span>{error.phoneNumber}</span>}
        {error.addressLine1 && (
          <span className="text-red-600" style={{ color: "red" }}>
            {error.addressLine1}
          </span>
        )}
        {error.global && (
          <div className="text-red-600 mt-2">{error.global}</div>
        )}
      </div>
      <div style={{ flex: 1 }}></div>

      <div
        className="flex"
        style={{
          marginBottom: "7%",
          marginTop: "7%",
          justifyContent: "space-between",
        }}
      >
        <div
          className="flex align-items-center justify-content-center"
          style={{ textWrap: "nowrap" }}
        >
          <i
            className="h-joinnow4-lable pi pi-angle-left pr-2"
            style={{ fontSize: "medium" }}
          ></i>
          <a
            className="h-joinnow4-lable pr-3 cursor-pointer"
            onClick={handlePrevious}
          >
            Go back
          </a>
        </div>
        <button
          className={`flex align-items-center justify-content-center h-joinnow-button ${isFormValid ? "shadow-4" : ""
            }`}
          onClick={handleSubmit}
          style={{
            backgroundColor: isFormValid ? "#FFA500" : "#DFDFDF",
            borderColor: isFormValid ? "#FFA500" : "#DFDFDF",
            color: isFormValid ? "#FFFFFF" : "#c2c7d1",
            cursor: isFormValid ? "pointer" : "not-allowed",
            minWidth: "177px",
            // position: 'absolute',
            // marginLeft: "50%"
          }}
          disabled={!isFormValid}
        >
          Submit
        </button>
      </div>
    </form>
  );
};
