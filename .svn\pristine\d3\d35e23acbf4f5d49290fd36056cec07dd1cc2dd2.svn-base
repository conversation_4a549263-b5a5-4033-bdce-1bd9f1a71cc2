import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
import { Jobs } from "../types";
import { IoClose } from "react-icons/io5";
import { <PERSON>a<PERSON>heck } from "react-icons/fa6";
import locationIcon from "../../../../assets/images/Icons/location.png";
import starIcon from "../../../../assets/images/Icons/star.png";
import calendar from "../../../../assets/images/Icons/Icon (1).png";
import home from "../../../../assets/images/Icons/home-05.png";
import clock from "../../../../assets/images/Icons/Vector.png";
import doller from "../../../../assets/images/Icons/Dollar.png";
import calendarMobile from "../../../../assets/images/Icons/calender.png";
import clockStart from "../../../../assets/images/Icons/clockstart.png";
import dollerMobile from "../../../../assets/images/Icons/family_membership.png";
import homeMobile from "../../../../assets/images/Icons/home.png";
import useIsMobile from "../../../../hooks/useIsMobile";
import { useState } from "react";
import c from "../../../../helper/juggleStreetConstants";
import { useNavigate } from "react-router-dom";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import CustomDialog from "../../../../commonComponents/CustomDialog";
import ProviderProfile from "../../../Parent/ProviderProfile/ProviderProfile";
import { HiOutlineCurrencyDollar } from "react-icons/hi2";
import { IframeBridge } from "../../../../services/IframeBridge";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";

function formatDateToCustomFormat(date: Date): string {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const day = days[date.getDay()];
  const dayOfMonth = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString().slice(-2);
  return `${day}, ${dayOfMonth}/${month}/${year}`;
}

function convertTo12HourFormatWithoutSeconds(time: string): string {
  const [hours, minutes] = time.split(":").map(Number);
  const period = hours >= 12 ? "PM" : "AM";
  const hourIn12HourFormat = hours % 12 === 0 ? 12 : hours % 12;
  const formattedHours = hourIn12HourFormat.toString().padStart(2, "0");
  const formattedMinutes = minutes.toString().padStart(2, "0");
  return `${formattedHours}:${formattedMinutes} ${period}`;
}

const ViewJobFull = ({ job, visible, onClose }: { job: Jobs; visible: boolean; onClose: () => void }) => {
  const { isMobile } = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();
  const client = Number(utils.getCookie(CookiesConstant.clientType));
  const wordLimit = 30;
  const [showPopup, setShowPopup] = useState(false);
  const [selectedapplicantId, setSelectedapplicantId] = useState(null);
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const distanceMapping = {
    0: "Within 2.5km",
    1: "Within 5km",
    2: "Within 10km",
  };
  const ageMapping = {
    1: "16-17",
    2: "18-24",
    3: "25-44",
    4: "45+",
  };
  const otherSkillsMapping = {
    4: "Special Needs",
    9: "Driving Licence",
  };

  const tutoringCategoryMapping = {
    1: "Newbie",
    2: "Apprentice",
    3: "Experienced",
    4: "Professional",
  };

  const filteredApplicantFilters = job?.applicantFilters?.filter(
    (filter) => filter.field === "age" || filter.field === "distance" || filter.field === "otherSkills" || filter.field === "tutoringCategory"
  );
  const truncateText = (text, wordLimit) => {
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };
  const viewProvider = (applicantId) => {
    IframeBridge.sendToParent({
      type: "navigateHelperProfile",
      data: {
        id: String(applicantId),
      },
    });
    if (!inIframe) {
      if (isMobile) {
        const url = `/${client === 1 ? "parent-home" : "business-home"}/provider-profile?id=${applicantId}`;
        navigate(url);
      } else {
        setSelectedapplicantId(applicantId);
        setShowPopup(true);
      }
    }
  };
  //     const redirectAfterHome = (applicantId) => {
  //     const searchParams = new URLSearchParams();
  //     searchParams.set('id', id.toString());
  //     const reqId = isMobile ? searchParams.get('requestId') : requestId;
  //     if (reqId) {
  //         searchParams.set('requestId', requestId.toString());
  //     }
  //     const clientType = utils.getCookie(CookiesConstant.clientType);

  //     // Conditionally navigate based on clientType
  //     if (clientType === '2') {
  //         navigate(`/business-home/provider-profile?${searchParams.toString()}`);
  //     } else if (clientType === '1') {
  //         navigate(`/parent-home/provider-profile?${searchParams.toString()}`);
  //     } else {
  //         console.warn('Unknown clientType, no navigation performed.');
  //     }
  // };
  const handleCloseProfilePopup = () => {
    setShowPopup(false);
    setSelectedapplicantId(null);
  };
  const calculatePriceBasedOnDuration = (startTime: string, endTime: string, hourlyPrice: number) => {
    // Convert times to Date objects for calculation
    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(`2000-01-01 ${endTime}`);

    // If end time is before start time, assume it crosses midnight
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    // Calculate hours difference
    const diffMs = end.getTime() - start.getTime();
    const hours = diffMs / (1000 * 60 * 60);

    // Calculate total price
    const totalPrice = hours * hourlyPrice;

    return {
      hours: hours.toFixed(1), // Return hours with 1 decimal place
      totalPrice: totalPrice.toFixed(2), // Return price with 2 decimal places
    };
  };

  const { hours, totalPrice } = calculatePriceBasedOnDuration(job.jobStartTime, job.jobEndTime, job.price);
  const hoursNum = parseFloat(hours); // Convert string to number
  const hourText = hoursNum <= 1 ? "hour" : "hours"; // Singular/plural logic

  const WeeklySummary = () => {
    const { isMobile } = useIsMobile();
    if (!job?.weeklySchedule?.weeklyScheduleEntries) return null;
    const entries = job.weeklySchedule.weeklyScheduleEntries; // Calculate totalDays (unique days)
    const uniqueDays = new Set(entries.map((entry) => entry.dayOfWeek));
    const totalDays = uniqueDays.size;
    const totalShifts = entries.length;
    let totalHours = 0;
    entries.forEach((entry) => {
      totalHours += utils.getTimeDifference(entry.jobStartTime, entry.jobEndTime);
    });
    return (
      <div key="shift-summary" className="entry-body" style={{ padding: !isMobile ? "10px 0" : "0px" }}>
        <span
          className={"p-0 m-0"}
          style={{
            fontWeight: "700",
            fontSize: !isMobile ? "16px" : "14px",
            color: "#585858",
          }}
        >
          Weekly Summary:{" "}
        </span>
        <span style={{ fontSize: isMobile && "14px" }}>
          {totalHours} Hours over {totalShifts} shifts in {totalDays} Days
        </span>
      </div>
    );
  };
  return !isMobile ? (
    <>
      <Dialog
        className="transition-none"
        visible={visible}
        onHide={onClose}
        modal
        closable={false}
        draggable={false}
        style={{
          // width: "90%",
          maxWidth: "906px",
          height: "95%",
        }}
        content={
          <div className=" h-full relative">
            <div
              className="absolute cursor-pointer flex justify-content-center align-items-center"
              style={{
                top: "-15px",
                right: "5px",
                backgroundColor: "#FFFFFF",
                borderRadius: "50%",
                width: "30px",
                height: "32px",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
              }}
              onClick={(e) => {
                e.preventDefault();
                onClose();
              }}
            >
              <IoClose color="#585858" fontSize="200%" />
            </div>
            <div
              className="w-full h-full overflow-y-auto overflow-x-hidden flex flex-column gap-3"
              style={{
                backgroundColor: "#FFFFFF",
                borderRadius: "20px",
                padding: "35px",
              }}
            >
              <div
                className="flex flex-column gap-2"
                style={{
                  padding: "10px 35px",
                  border: "1px solid #DFDFDF",
                  borderRadius: "20px",
                }}
              >
                <h1
                  className="m-0 p-0"
                  style={{
                    fontWeight: "700",
                    fontSize: "30px",
                    color: "#585858",
                  }}
                >
                  Job Summary
                </h1>
                <div className="flex gap-2 flex-wrap">
                  {[
                    {
                      text: formatDateToCustomFormat(new Date(job.jobDate)),
                      icon: <img src={calendar} alt="calender" width="12px" height="13.5px" />,
                      backgroundColor: "#2F9ACD",
                    },
                    {
                      text: job.isTutoringJob
                        ? `${job.duration} week duration` // Show only the duration for tutoring jobs
                        : job.isRecurringJob
                        ? `${job.duration} week duration` // Show week duration for recurring jobs
                        : `${convertTo12HourFormatWithoutSeconds(job.jobStartTime)} - ${convertTo12HourFormatWithoutSeconds(job.jobEndTime)}`, // Show time slot for one-off jobs
                      icon: <img src={clock} alt="clock" width="14.4px" height="14.4px" />,
                      backgroundColor: "#8577DB",
                    },
                    {
                      text: `${utils.cleanAddress(job.formattedAddress)}`,
                      icon: <img src={home} alt="home" width="15.2px" height="15.2px" />,
                      backgroundColor: "#179D52",
                    },
                    {
                      text: job.isRecurringJob ? `Weekly Price: $${job.pricePerWeek} Per Week` : `Hourly Price: $${job.price} Per Hour`,
                      icon: <img src={doller} alt="doller" width="8px" height="15px" />,
                      backgroundColor: "#77DBC9",
                    },
                    {
                      text: job.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer",
                      icon: <img src={doller} alt="doller" width="8px" height="15px" />,
                      backgroundColor: "#ff8c00",
                    },
                    ...(job.isRecurringJob || job.isTutoringJob
                      ? [
                          {
                            text: job.isPriceNegotiable ? "Price is Negotiable" : "Price is Not Negotiable",
                            icon: <HiOutlineCurrencyDollar size={20} style={{ color: "white" }} />,
                            backgroundColor: "darkgoldenrod",
                          },
                        ]
                      : []),
                  ].map((e, i) => (
                    <div
                      key={i}
                      className="p-2 flex align-items-center gap-2"
                      style={{
                        backgroundColor: e.backgroundColor,
                        borderRadius: "10px",
                        border: "1px solid #DFDFDF",
                      }}
                    >
                      {e.icon}
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "600",
                          fontSize: "14px",
                          color: "#FFFFFF",
                        }}
                      >
                        {e.text}
                      </p>
                    </div>
                  ))}
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  <p
                    className="m-0 p-0 "
                    style={{
                      fontWeight: "300",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    {(job.isTutoringJob || job.isRecurringJob) && <WeeklySummary />}
                  </p>
                </div>
                <div className="flex flex-wrap gap-1 ">
                  <p
                    className="m-0 p-0 "
                    style={{
                      fontWeight: "700",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    Job Description:
                  </p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "300",
                      fontSize: "16px",
                      color: "#585858",
                    }}
                  >
                    {job.specialInstructions}
                  </p>
                </div>
                {job.managedBy === 20 ? (
                  <div className="flex flex-wrap gap-1 mt-2">
                    <p
                      className="m-0 p-0 "
                      style={{
                        fontWeight: "700",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Status:
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "300",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {job.jobStatus === c.jobStatus.PENDING ? "Processing" : job.jobStatus === c.jobStatus.AWARDED ? "award" : job.jobStatus}
                    </p>
                  </div>
                ) : null}
                {filteredApplicantFilters?.length > 0 && (
                  <div>
                    <h4 className="p-0 m-0" style={{ color: "#585858" }}>
                      Candidate Criteria{" "}
                    </h4>
                    <div className="ml-2">
                      {filteredApplicantFilters.map((filter, index) => {
                        let displayValue = "";
                        let displayField = filter.field.charAt(0).toUpperCase() + filter.field.slice(1);
                        if (filter.field === "distance") {
                          displayValue = distanceMapping[filter.value];
                        } else if (filter.field === "age") {
                          if (filter.value === 0) {
                            displayValue = Object.values(ageMapping).join(", ");
                          } else {
                            displayValue = (filter.value as number[]).map((val) => ageMapping[val]).join(", ");
                          }
                        } else if (filter.field === "otherSkills") {
                          displayValue = (filter.value as number[]).map((val) => otherSkillsMapping[val]).join(", ");
                          displayField = "Other";
                        } else if (filter.field === "tutoringCategory") {
                          displayValue = (filter.value as number[]).map((val) => tutoringCategoryMapping[val]).join(", ");
                          displayField = "Experience";
                        }
                        return (
                          <div key={index} className={""}>
                            <span className={""} style={{ color: "#585858", fontWeight: "600" }}>
                              {displayField}:&nbsp;
                            </span>
                            <span className={""} style={{ color: "#585858" }}>
                              {displayValue}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
                <Divider className="my-3" />

                {job.isTutoringJob ? (
                  <div className="flex justify-content-end ml-auto gap-1">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "400",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Price Per Week =
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {`$${job.pricePerWeek}`}
                    </p>
                  </div>
                ) : job.isRecurringJob ? (
                  <div className="flex justify-content-end ml-auto gap-1">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "400",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      Weekly Job Total =
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {`$${job.pricePerWeek}`}
                    </p>
                  </div>
                ) : (
                  <div className="flex flex-column">
                    <div className="flex justify-content-end ml-auto gap-1">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        Job Total =
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "700",
                          fontSize: "16px",
                          color: "#585858",
                        }}
                      >
                        {job.paymentType === 1 ? `$${job.price} for ${hours} ${hourText}` : `$${totalPrice} for ${hours} ${hourText}`}
                      </p>
                    </div>
                    {job.willPayOvertime && job.overtimeRate > 0 && (
                      <div className="flex justify-content-end ml-auto gap-1">
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: "400",
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          Overtime =
                        </p>
                        <p
                          className="m-0 p-0"
                          style={{
                            fontWeight: "700",
                            fontSize: "16px",
                            color: "#585858",
                          }}
                        >
                          {`$${job.overtimeRate} Per Hour`}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
              {job.managedBy === 1 && job.applicants.length > 0 && (
                <>
                  <div className="flex gap-2 mt-2">
                    <h1
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "30px",
                        color: "#585858",
                      }}
                    >
                      Invited Candidates
                    </h1>
                    <h1
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "30px",
                        color: "#179D52",
                      }}
                    >
                      ({job.applicants.length})
                    </h1>
                  </div>
                  <div className="flex-grow-1 grid grid-nogutter cursor-pointer">
                    {job.applicants.map((v, i) => (
                      <div className="h-min col-6 grid-nogutter p-2" key={i}>
                        <div
                          className="h-min flex gap-2 align-items-center px-2 py-3"
                          style={{
                            border: " 2px solid #DFDFDF",
                            borderRadius: "10px",
                          }}
                          onClick={(e) => {
                            e.preventDefault();
                            viewProvider(v.applicantId);
                          }}
                        >
                          <img
                            src={v.applicantImageSrc}
                            alt="img"
                            width="81px"
                            height="81px"
                            style={{
                              borderRadius: "50%",
                              objectFit: "contain",
                              overflow: "hidden",
                            }}
                          />
                          <div className="flex-grow-1 flex flex-column gap-1">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "700",
                                fontSize: "18px",
                                color: "#585858",
                              }}
                            >
                              {v.applicantFirstName}
                            </p>
                            <div className="flex gap-1 align-items-center">
                              <img src={locationIcon} alt="location" width="16.71px" height="16px" />
                              <p
                                className="m-0 p-0"
                                style={{
                                  fontWeight: "600",
                                  fontSize: "14px",
                                  color: "#585858",
                                }}
                              >
                                {v.suburb}
                              </p>
                            </div>
                            <div className="flex gap-1 align-items-center">
                              <img src={starIcon} alt="star" width="16.71px" height="18px" />
                              <p
                                className="m-0 p-0"
                                style={{
                                  fontWeight: "300",
                                  fontSize: "14px",
                                  color: "#585858",
                                }}
                              >
                                {v.completedJobsCount} Jobs completed
                              </p>
                            </div>
                          </div>
                          <div
                            className="flex gap-2 mr-3"
                            style={{
                              backgroundColor: "#45454A",
                              padding: "10px",
                              borderRadius: "10px",
                            }}
                          >
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "700",
                                fontSize: "12px",
                                color: "#FFFFFF",
                              }}
                            >
                              Invited
                            </p>
                            <FaCheck color="#FFFFFF" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        }
      />
      <CustomDialog
        visible={showPopup}
        style={{
          width: "100%",
          maxWidth: "100%",
          height: "100%",
          maxHeight: "100%",
          backgroundColor: "#ffffff",
          borderRadius: "0px",
          overflowY: "auto",
        }}
        onHide={handleCloseProfilePopup}
        draggable={false}
      >
        <ProviderProfile candidateId={selectedapplicantId} onClose={handleCloseProfilePopup} />
      </CustomDialog>
    </>
  ) : (
    <Dialog
      className="transition-none"
      visible={visible}
      onHide={onClose}
      modal
      closable={false}
      draggable={false}
      style={{
        width: "100%",
        maxWidth: "906px",
        height: "95%",
        position: "fixed",
        bottom: "0",
        left: "0",
        maxHeight: "650px",
        overflow: "auto",
        borderTopLeftRadius: "20px",
        borderTopRightRadius: "20px",
      }}
      content={
        <div className=" h-full relative">
          <div
            className="w-full h-full overflow-y-auto overflow-x-hidden flex flex-column gap-3"
            style={{
              backgroundColor: "#FFFFFF",
              borderTopLeftRadius: "20px",
              borderTopRightRadius: "20px",
              padding: "25px",
              paddingTop: "42px",
            }}
          >
            <div
              className="absolute cursor-pointer flex justify-content-center align-items-center"
              style={{
                top: "1px",
                right: "9px",
                backgroundColor: "#FFFFFF",
                borderRadius: "50%",
                width: "25px",
                height: "25px",
                boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                zIndex: "2",
              }}
              onClick={(e) => {
                e.preventDefault();
                onClose();
              }}
            >
              <IoClose color="#585858" fontSize="200%" />
            </div>
            <div className="flex flex-column gap-2">
              <h1
                className="m-0 p-0"
                style={{
                  fontWeight: "700",
                  fontSize: "22px",
                  color: "#585858",
                }}
              >
                Job Summary
              </h1>
              <div className="flex gap-2 flex-wrap">
                {[
                  {
                    text: formatDateToCustomFormat(new Date(job.jobDate)),
                    icon: <img src={calendarMobile} alt="calender" width="12px" height="13.5px" />,
                    backgroundColor: "#2F9ACD",
                  },
                  {
                    text: job.isTutoringJob
                      ? `${job.duration} week duration` // Show only the duration for tutoring jobs
                      : job.isRecurringJob
                      ? `${job.duration} week duration` // Show week duration for recurring jobs
                      : `${convertTo12HourFormatWithoutSeconds(job.jobStartTime)} - ${convertTo12HourFormatWithoutSeconds(job.jobEndTime)}`, // Show time slot for one-off jobs
                    icon: <img src={clockStart} alt="clock" width="13.4px" height="14.4px" />,
                    backgroundColor: "#8577DB",
                  },
                  {
                    text: `${utils.cleanAddress(job.formattedAddress)}`,
                    icon: <img src={homeMobile} alt="home" width="15.2px" height="15.2px" />,
                    backgroundColor: "#179D52",
                  },
                  {
                    text: job.isRecurringJob ? `Weekly Price: $${job.pricePerWeek} Per Week` : `Hourly Price: $${job.price} Per Hour`,
                    icon: <img src={dollerMobile} alt="doller" width="8px" height="15px" />,
                    backgroundColor: "#77DBC9",
                  },
                  {
                    text: job.managedBy === 1 ? "Do It Yourself" : "Juggle Assist",

                    backgroundColor: "#179D52",
                  },
                  {
                    text: job.helperPaymentMethod === 1 ? "Cash payment" : "Bank transfer",

                    backgroundColor: "#ff8c00",
                  },
                  ...(job.isRecurringJob || job.isTutoringJob
                    ? [
                        {
                          text: job.isPriceNegotiable ? "Price is Negotiable" : "Price is Not Negotiable",
                          backgroundColor: "darkgoldenrod",
                        },
                      ]
                    : []),
                ].map((e, i) => (
                  <div
                    key={i}
                    className="p-2 flex align-items-center gap-2"
                    style={{
                      backgroundColor: "transparent",
                      borderRadius: "10px",
                      border: "1px solid #DFDFDF",
                    }}
                  >
                    {e.icon}
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "600",
                        fontSize: "12px",
                        color: "#585858",
                      }}
                    >
                      {e.text}
                    </p>
                  </div>
                ))}
              </div>

              <div>
                <p
                  className="m-0 p-0 mt-2"
                  style={{
                    fontWeight: "700",
                    fontSize: "16px",
                    color: "#179D52",
                  }}
                >
                  Job Description
                </p>
                <p
                  className="m-0 p-0"
                  style={{
                    fontWeight: "500",
                    fontSize: "14px",
                    color: "#585858",
                  }}
                >
                  {isExpanded ? job.specialInstructions : truncateText(job.specialInstructions, wordLimit)}
                </p>
                {job.specialInstructions.split(" ").length > wordLimit && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    style={{
                      fontSize: "14px",
                      fontWeight: "700",
                      color: "#585858",
                      backgroundColor: "transparent",
                      border: "none",
                      textDecoration: "underline",
                      cursor: "pointer", // Added for better UX
                      textAlign: "left",
                    }}
                  >
                    {isExpanded ? "See Less" : "See More"}
                  </button>
                )}
              </div>
              <Divider className="my-3" />

              {job.isTutoringJob ? (
                <div className="flex justify-content-end ml-auto gap-1">
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    Price Per Week =
                  </p>
                  <p
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "14px",
                      color: "#585858",
                    }}
                  >
                    {`$${job.pricePerWeek}`}
                  </p>
                </div>
              ) : job.isRecurringJob ? (
                <>
                  <div className="flex justify-content-end ml-auto gap-1">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      Weekly Job Total =
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {`$${job.pricePerWeek}`}
                    </p>
                  </div>
                  <div className="flex flex-wrap ustify-content-end ml-auto gap-1">
                    <p
                      className="m-0 p-0 "
                      style={{
                        fontWeight: "300",
                        fontSize: "16px",
                        color: "#585858",
                      }}
                    >
                      {(job.isTutoringJob || job.isRecurringJob) && <WeeklySummary />}
                    </p>
                  </div>
                </>
              ) : (
                <div className="flex flex-column">
                  <div className="flex justify-content-end ml-auto gap-1">
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      Job Total =
                    </p>
                    <p
                      className="m-0 p-0"
                      style={{
                        fontWeight: "700",
                        fontSize: "14px",
                        color: "#585858",
                      }}
                    >
                      {job.paymentType === 1 ? `$${job.price} for ${hours} ${hourText}` : `$${totalPrice} for ${hours} ${hourText}`}
                    </p>
                  </div>
                  {job.willPayOvertime && (
                    <div className="flex justify-content-end ml-auto gap-1">
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "400",
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        Overtime =
                      </p>
                      <p
                        className="m-0 p-0"
                        style={{
                          fontWeight: "700",
                          fontSize: "14px",
                          color: "#585858",
                        }}
                      >
                        {`$${job.overtimeRate} Per Hour`}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
            {job.managedBy === 1 && job.applicants.length > 0 && (
              <>
                <div className="flex gap-2 mt-2">
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "22px",
                      color: "#585858",
                    }}
                  >
                    Invited Candidates
                  </h1>
                  <h1
                    className="m-0 p-0"
                    style={{
                      fontWeight: "700",
                      fontSize: "22px",
                      color: "#179D52",
                    }}
                  >
                    ({job.applicants.length})
                  </h1>
                </div>
                <div>
                  {job.applicants.map((v, i) => (
                    <div className="h-min  grid-nogutter p-2" key={i}>
                      <div
                        className="h-min flex gap-2 align-items-center px-2 py-2"
                        style={{
                          border: " 2px solid #179D52",
                          borderRadius: "10px",
                          boxShadow: "0px 4px 4px 0px #00000040",
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          viewProvider(v.applicantId);
                        }}
                      >
                        <img
                          src={v.applicantImageSrc}
                          alt="img"
                          width="71px"
                          height="71px"
                          style={{
                            borderRadius: "50%",
                            objectFit: "contain",
                            overflow: "hidden",
                          }}
                        />
                        <div className="flex-grow-1 flex flex-column gap-1">
                          <p
                            className="m-0 p-0"
                            style={{
                              fontWeight: "700",
                              fontSize: "14px",
                              color: "#585858",
                            }}
                          >
                            {v.applicantFirstName}
                          </p>
                          <div className="flex gap-1 align-items-center">
                            <img src={locationIcon} alt="location" width="10.71px" height="14px" />
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "600",
                                fontSize: "12px",
                                color: "#585858",
                              }}
                            >
                              {v.suburb}
                            </p>
                          </div>
                          <div className="flex gap-1 align-items-center">
                            <img src={starIcon} alt="star" width="14px" height="14px" />
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "300",
                                fontSize: "12px",
                                color: "#585858",
                                textWrap: "nowrap",
                              }}
                            >
                              {v.completedJobsCount} Jobs completed
                            </p>
                          </div>
                        </div>
                        <div
                          className="flex gap-2 mr-0"
                          style={{
                            backgroundColor: "#179D52",
                            padding: "6px",
                            borderRadius: "10px",
                          }}
                        >
                          <p
                            className="m-0 p-0"
                            style={{
                              fontWeight: "700",
                              fontSize: "12px",
                              color: "#FFFFFF",
                            }}
                          >
                            Selected
                          </p>
                          <FaCheck color="#FFFFFF" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      }
    />
  );
};

export default ViewJobFull;
