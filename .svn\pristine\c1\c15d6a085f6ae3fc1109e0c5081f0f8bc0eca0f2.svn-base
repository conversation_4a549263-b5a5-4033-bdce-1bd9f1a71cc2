import React, { useEffect, useMemo, useState } from "react";
import { Applicants, Jobs, ManageJobSectionProps } from "../types";
import styles from "../../../Common/styles/review-and-post.module.css";
import remove from "../../../../assets/images/Icons/remove.png";
import BackArrow from "../../../../assets/images/Icons/back-icon.png";
import { Divider } from "primereact/divider";
import useLoader from "../../../../hooks/LoaderHook";
import Service from "../../../../services/services";
import { useNavigate, useSearchParams } from "react-router-dom";
import AwardCard from "../Common/AwardCard";
import c from "../../../../helper/juggleStreetConstants";
import utils from "../../../../components/utils/util";
import CookiesConstant from "../../../../helper/cookiesConst";
import { Dialog } from "primereact/dialog";
import NoJobsCard from "../Common/NoJobsCard";
import ConfirmAwardDialog from "../Common/ConfirmAwardDialog";
import JobFooter from "../Common/JobFooter";
import { CancelJobPopup, useCancelJobPopup } from "../Common/CancelJobPopup";
import JobAnalytics from "../Common/JobAnalytics";
import WeeklyScheduleds from "../Common/WeeklySheduled";
import ManageRecruitment from "../Common/ManageRecruitment/ManageRecruitment";
import ViewJobFull from "./ViewJob";
import Loader from "../../../../commonComponents/Loader";
import useIsMobile from "../../../../hooks/useIsMobile";
import environment from "../../../../helper/environment";
import { IframeBridge } from "../../../../services/IframeBridge";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { IoClose } from "react-icons/io5";
import { FaCheck, FaRegEye } from "react-icons/fa6";
import { SlLocationPin } from "react-icons/sl";
import starIcon from "../../../../assets/images/Icons/star.png";
import fileCheck from "../../../../assets/images/Icons/file-check-01.png";
const JobAnalyticsUnawarded: React.FC<ManageJobSectionProps> = ({ refresh }) => {
  const { disableLoader, enableLoader } = useLoader();
  const [searchParams] = useSearchParams();
  const [jobClients, setJobClients] = useState<Jobs | null>(null);
  const jobIdString = searchParams.get("jobId");
  const activeTab = searchParams.get("activeTab");
  const navigate = useNavigate();
  const [showfullJob, setShowFullJob] = useState(false);
  const [duplicateJob, setDuplicateJob] = useState<boolean>(false);
  const { cancelJobPopupProps, showCancelJobPopup } = useCancelJobPopup();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedJobtId, setSelectedJobId] = useState<number | null>(null);
  const [applicantId, setApplicantId] = useState<number | null>(null);
  const [manageRecruitment, setManageRecruitment] = useState<boolean>(false);
  const [update, setUpdate] = useState<boolean>(false);
  const sessionInfo = useSelector((state: RootState) => state.sessionInfo);
  const ischateligible = sessionInfo.data?.["paymentInfo"]["paymentType"];
  const [dialogVisible, setDialogVisible] = useState(false);
  const [chatErrorMessage, setChatErrorMessage] = useState<string>('');
  const jobId = jobIdString ? parseInt(jobIdString) : null;
  const clientType = utils.getCookie(CookiesConstant.clientType);
  const totalInvited = jobClients?.applicantsTotal || 0;
  const totalViewed = jobClients?.applicantsViewed || 0;
  const { inIframe } = useSelector((state: RootState) => state.applicationState);
  const { isMobile } = useIsMobile();
  const client = Number(utils.getCookie(CookiesConstant.clientType));

  let formattedDate = "";

  if (jobClients && jobClients.jobDate) {
    const jobDate = new Date(jobClients.jobDate);
    function getDaySuffix(date) {
      const day = date.getDate();
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    }
    formattedDate = `${jobDate.toLocaleString("en-EN", {
      weekday: "short",
    })} ${jobDate.getDate()}${getDaySuffix(jobDate)} of ${jobDate.toLocaleString("en-EN", {
      month: "long",
    })}`;
  }


  const [showPopup, setShowPopup] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState(null);

  const redirectAfterHome = (id: number) => {
    IframeBridge.sendToParent({
      type: "navigateHelperProfile",
      data: {
        id: String(id),
      },
    });
    if (!inIframe) {
      if (isMobile) {
        const navigateParams = new URLSearchParams();
        navigateParams.set("id", id.toString());
        const clientType = utils.getCookie(CookiesConstant.clientType);
        if (clientType === "2") {
          navigate(`/business-home/provider-profile?${navigateParams.toString()}`);
        } else if (clientType === "1") {
          navigate(`/parent-home/provider-profile?${navigateParams.toString()}`);
        } else if (clientType === "0") {
          navigate(`/helper-home/client-profile?${navigateParams.toString()}`);
        } else {
          console.warn("Unknown clientType, no navigation performed.");
        }
      } else {
        setSelectedProviderId(id);
        setShowPopup(true);
      }
    }
  };


  const handleAwardJob = (id: number) => {
    const isOneOffJob = jobClients?.isRecurringJob ? false : true;
    const payload = {
      ...jobClients,
      // awardedApplicantIds: [applicantId],
      awardedApplicantId: isOneOffJob ? applicantId : null,
      awardedApplicantIds: isOneOffJob ? null : [applicantId],
      applicantFilters: null,
      applicationSummaries: null,
      weeklySchedule: null,
      jobInvitations: null,

      returnUpdatedJob: true,
      applicants: jobClients.applicants.map((v) => {
        // Return a new object with applicantAvailability set to null
        return {
          ...v, // Spread the properties of the existing applicant object
          applicantAvailability: null, // Override applicantAvailability to null
          numOfMatches: null,
        };
      }),
    };
    enableLoader();
    Service.jobClientAwardJobs(
      async (response: Jobs) => {
        // setAwardJob(response);

        await refresh();

        const newSearchParams = new URLSearchParams();
        newSearchParams.set("jobId", jobIdString);
        newSearchParams.set("activeTab", "1");
        navigate({ search: newSearchParams.toString() });
        disableLoader();
      },
      (error: any) => {
        disableLoader();
        console.error("Error awarding job:", error);
      },

      id,
      payload
    );
  };
  const handleRemoveJob = (id: number, tabIndex: string) => {
    // Show the confirmation popup
    showCancelJobPopup(
      "Remove Applicant?", // Heading
      `will be removed from your shortlist. Their response will remain in the response table.`, // Message
      "Go Back", // Cancel Text
      "Remove", // Confirm Text
      <img src={remove} alt="confirm" style={{ height: "20px", width: "20px" }} />,
      () => {
        const payload = {
          applicants: [
            {
              id: id,
              applicationStatus: c.jobApplicationStatus.EXCLUDED_BY_CLIENT,
            },
          ],
          id: jobClients.id,
          jobStatus: jobClients.jobStatus.toString(),
        };

        enableLoader();
        Service.jobClientAwardJobs(
          (response: Jobs) => {
            const newSearchParams = new URLSearchParams();
            newSearchParams.set("jobId", "-1");
            newSearchParams.set("activeTab", tabIndex);
            navigate({ search: newSearchParams.toString() });

            disableLoader();
          },
          (error: any) => {
            disableLoader();
            console.error("Error cancelling job:", error);
          },
          jobClients.id,
          payload
        );
      },
      () => {
        // onCancel callback
      }
    );
  };
  const handleCancelJob = (id: number, tabIndex: string) => {
    // Show the confirmation popup
    showCancelJobPopup(
      "Are you sure?", // Heading
      "If you cancel this job all applicants will be notified and the canceled job will be moved to the Job History section of My Jobs. Are you sure you want to continue?", // Message
      "Go Back", // Cancel Text
      "Cancel Job", // Confirm Text
      <img src={remove} alt="confirm" style={{ height: "20px", width: "20px" }} />,

      () => {
        const payload = {
          ...jobClients,
          jobStatus: c.jobStatus.CANCELLED,
        };


        enableLoader();
        Service.jobClientAwardJobs(
          async (response: Jobs) => {
            // setcancelJob(response);
            await refresh();
            const newSearchParams = new URLSearchParams();
            newSearchParams.set("jobId", "-1");
            newSearchParams.set("activeTab", tabIndex);
            navigate({ search: newSearchParams.toString() });
            disableLoader();
          },
          (error: any) => {
            disableLoader();
            console.error("Error cancelling job:", error);
          },
          id,
          payload
        );
      },

      () => {
        // onCancel callback
        // Handle cancellation, maybe log or close popup
      }
    );
  };
  const { responseTable, availableApplicants } = useMemo(() => {
    if (!jobClients) return { result: [], availableApplicants: [] };
    const dows = ["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"];
    const schedules = jobClients.weeklySchedule?.weeklyScheduleEntries || [];
    const applicants = jobClients.applicants || [];
    const result: Array<{
      dayOfWeek: number;
      day: string;
      startTime: string;
      endTime: string;
      shiftId: number;
      price: number;
      awardedAplicantId: number | null;
      applicants: Array<{
        applicantId: number;
        availabilityId: number;
        publicName: string;
        imageSrc: string;
        status: number;
        completedJobsCount: number;
        applicantRatingsCount: number;
        applicantRatingsAvg: number;
        responseRate: number;
      }>;
    }> = [];
    const availableApplicants: Array<{
      applicantId: number;
      publicName: string;
      imageSrc: string;
      isSuperHelper: boolean;
      applicantRatingsCount: number;
      applicantRatingsAvg: number;
      responseRate: number;
      id: number;
      applicationStatus: number;
      gettingHome: number;
      suburb: string;
    }> = [];
    const addedApplicantIds = new Set<number>();

    const inPrevShift = (dow: number, id: number) => {
      const shift = result.find((r) => r.dayOfWeek === dow);
      return shift?.applicants.some((a) => a.availabilityId === id) || false;
    };

    for (const schedule of schedules) {
      const shiftData = {
        dayOfWeek: schedule.dayOfWeek,
        day: dows[schedule.dayOfWeek],
        startTime: schedule.jobStartTime,
        endTime: schedule.jobEndTime,
        shiftId: schedule.id,
        price: schedule.price,
        awardedAplicantId: schedule.applicantId,
        applicants: [] as Array<{
          applicantId: number;
          availabilityId: number;
          publicName: string;
          imageSrc: string;
          status: number;
          completedJobsCount: number;
          applicantRatingsCount: number;
          applicantRatingsAvg: number;
          responseRate: number;
          id: number;
          applicationStatus: number;
        }>,
      };

      for (const applicant of applicants) {
        for (const availability of applicant.applicantAvailability) {
          if (
            availability.dayOfWeek === schedule.dayOfWeek &&
            !shiftData.applicants.some((a) => a.applicantId === applicant.applicantId) &&
            !inPrevShift(availability.dayOfWeek, availability.id)
          ) {
            shiftData.applicants.push({
              applicantId: applicant.applicantId,
              availabilityId: availability.id,
              publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
              imageSrc: applicant.applicantImageSrc,
              status: availability.availabilityStatus,
              completedJobsCount: applicant.completedJobsCount,
              applicantRatingsCount: applicant.applicantRatingsCount,
              applicantRatingsAvg: applicant.applicantRatingsAvg,
              responseRate: applicant.responseRate,
              id: applicant.id,
              applicationStatus: applicant.applicationStatus,
            });

            if (
              schedule.applicantId === null &&
              availability.availabilityStatus === c.applicantAvailabilityStatus.AVAILABLE &&
              !addedApplicantIds.has(applicant.applicantId)
            ) {
              availableApplicants.push({
                applicantId: applicant.applicantId,
                publicName: `${applicant.applicantFirstName} ${applicant.applicantLastInitial}`,
                imageSrc: applicant.applicantImageSrc,
                isSuperHelper: applicant.completedJobsCount >= 15,
                applicantRatingsCount: applicant.applicantRatingsCount,
                applicantRatingsAvg: applicant.applicantRatingsAvg,
                responseRate: applicant.responseRate,
                id: applicant.id,
                applicationStatus: applicant.applicationStatus,
                gettingHome: applicant.gettingHome,
                suburb: applicant.suburb,
              });
              addedApplicantIds.add(applicant.applicantId);
            }
          }
        }
      }

      result.push(shiftData);
    }

    return { responseTable: result, availableApplicants };
  }, [jobClients]);

  const viewProvider = (applicantId) => {
    IframeBridge.sendToParent({
      type: "navigateHelperProfile",
      data: {
        id: String(applicantId),
      },
    });
    if (!inIframe) {
      if (isMobile) {
        const url = `/${client === 1 ? "parent-home" : "business-home"
          }/provider-profile?id=${applicantId}`;
        navigate(url);
      }
    }
  };

  useEffect(() => {
    const fetchData = () => {
      enableLoader();
      if (clientType == "0") {
        Service.jobProviderDetails(
          (response: Jobs) => {
            setJobClients(response);
            refresh();
            disableLoader();
          },
          (error: any) => {
            console.error("Error fetching data:", error);
            disableLoader();
          },
          jobId
        );
      } else {
        Service.jobClientDetails(
          (response: Jobs) => {
            setJobClients(response);
            refresh();
            disableLoader();
          },
          (error: any) => {
            console.error("Error fetching data:", error);
            disableLoader();
          },
          jobId
        );
      }
    };
    fetchData();
  }, [activeTab, update]);
  const getOptionInternal = (value, option) => {
    if (option.optionId === value) return option;

    let result = null;
    if (option.children != null) {
      for (let i = 0; i < option.children.length; i++) {
        result = getOptionInternal(value, option.children[i]);
        if (result !== null) break;
      }
    }

    return result;
  };
  return jobClients ? (
    !isMobile ? (
      <></>
    ) : (
      <>
        <div className="mb-6">
          <ManageRecruitment
            visible={manageRecruitment}
            job={jobClients}
            onClose={() => {
              setManageRecruitment(false);
              setUpdate((prev) => !prev);
            }}
          />
          <CancelJobPopup cancelJobPopupProps={cancelJobPopupProps} />
          <Dialog
            visible={dialogVisible}
            onHide={() => setDialogVisible(false)}
            content={
              <div className={styles.dialogContent}>
                <IoClose
                  onClick={() => setDialogVisible(false)}
                  className={styles.CloseBtn}
                />
                <div>
                  <h1
                    style={{
                      fontSize: '32px',
                      fontWeight: '700',
                      color: '#585858',
                      margin: '0px',
                    }}
                  >
                    Chat
                  </h1>
                </div>
                <Divider />

                <div>
                  <p
                    style={{
                      fontSize: '20px',
                      fontWeight: '500',
                      color: '#585858',
                    }}
                  >
                    {chatErrorMessage}
                  </p>
                  <div style={{ display: 'flex', flexDirection: 'row', gap: '20px' }}>
                    <button
                      className={styles.buttonPost}
                      onClick={() => setDialogVisible(false)}
                    >
                      Ok
                    </button>
                    {/* <button
                                              className={styles.buttonPost}
                                              onClick={() => {
                                                  if (clientType === '1') {
                                                      navigate('/parent-home/job/post/job-type');
                                                  } else if (clientType === '2') {
                                                      navigate('/business-home/job/post/job-type');
                                                  }
                                              }}
                                          >
                                              Post Job
                                          </button> */}
                  </div>
                </div>
              </div>
            }
          />
          <Dialog
            visible={duplicateJob}
            onHide={() => {
              setDuplicateJob(false);
            }}
            style={{
              width: "100vw",
              height: "100vh",
              maxHeight: "none",
              maxWidth: "none",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
            content={
              <div
                className="flex flex-column py-3 px-4"
                style={{
                  backgroundColor: "#FFFFFF",
                  border: "1px solid #F0F4F7",
                  borderRadius: "20px",
                  maxWidth: "610px",
                }}
              >
                <h1
                  className="m-0 p-0"
                  style={{
                    fontWeight: "700",
                    fontSize: "32px",
                    color: "#585858",
                  }}
                >
                  Duplicate Job
                </h1>
                <Divider />
                <p
                  className="m-0 p-0 mt-3"
                  style={{
                    fontWeight: "500",
                    fontSize: "16px",
                    color: "#585858",
                  }}
                >
                  Use this feature to post a new job based on the current job details, all applicable job settings are copied.
                </p>
                <div className="w-full flex justify-content-start align-items-center mt-3 gap-3">
                  <button
                    className="px-4 py-1 cursor-pointer"
                    style={{
                      backgroundColor: "#FFFFFF",
                      border: "none",
                      boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25)",
                      fontWeight: "500",
                      fontSize: "18px",
                      color: "#585858",
                      textDecoration: "underline",
                      borderRadius: "5px",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      setDuplicateJob(false);
                    }}
                  >
                    No
                  </button>
                  <button
                    className="px-7 py-1 cursor-pointer"
                    style={{
                      backgroundColor: "#FFA500",
                      border: "none",
                      boxShadow: "0 4px 4px 0 rgba(0, 0, 0, 0.25)",
                      fontWeight: "700",
                      fontSize: "18px",
                      color: "#FFFFFF",
                      textDecoration: "underline",
                      borderRadius: "5px",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      setDuplicateJob(false);
                      const newParams = new URLSearchParams();
                      newParams.set("jobId", String(jobClients.id));
                      if (Number(clientType) === 1) {
                        navigate({
                          pathname: "/parent-home/post-job/duplicate",
                          search: newParams.toString(),
                        });
                        return;
                      }
                      navigate({
                        pathname: "/business-home/post-job/duplicate",
                        search: newParams.toString(),
                      });
                    }}
                  >
                    Yes
                  </button>
                </div>
              </div>
            }
          />
          <div className="relative">
            {/* Go Back Button */}
            <div
              className="flex gap-1 align-items-center w-min cursor-pointer mb-2"
              style={{
                textWrap: "nowrap",
                position: "absolute",
                left: "-10px",
                top: "3px",
                borderRadius: "20px",
                zIndex: "2",
              }}
              // onClick={(e) => {
              //   e.preventDefault();
              //   const newGoBackParams = new URLSearchParams();
              //   newGoBackParams.set("jobId", "-1");
              //   newGoBackParams.set("activeTab", activeTab);
              //   navigate({ search: newGoBackParams.toString() });
              // }}

              onClick={(e) => {
                window.history.back();
              }}
            >
              <div
                style={{
                  backgroundColor: "#D9D9D94D",
                  paddingInline: "8px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  paddingBlock: "8px",
                  borderRadius: "50%",
                }}
              >
                <img src={BackArrow} alt="Arrow Left" width="10px" height="8px" />
              </div>
              <p
                className="m-0 p-0"
                style={{
                  fontWeight: "500",
                  fontSize: "14px",
                  color: "#585858",
                }}
              >
                Go Back
              </p>
            </div>

            {/* Job Summary Title */}
            <div className="flex justify-content-center align-items-center relative">
              <h3
                className="font-bold"
                style={{
                  color: "#585858",
                  fontSize: "18px",
                  margin: "0px",
                  textDecoration: "underline",
                  marginTop: "0px",
                  marginBottom: "5px",
                }}
              >
                Job Analytics
              </h3>
            </div>
          </div>

          {clientType !== "0" && <JobAnalytics job={jobClients} />}

          {jobClients && (jobClients.isRecurringJob || jobClients.isTutoringJob) && (
            <>
              <WeeklyScheduleds
                timeSlots={responseTable}
                availabilityStatusEnum={c.applicantAvailabilityStatus}
                totalInvited={totalInvited}
                totalViewed={totalViewed}
                job={jobClients}
                availableApplicants={availableApplicants}
              />
            </>
          )}

          {!jobClients.isRecurringJob && !jobClients.isTutoringJob && clientType !== "0" && (
            <div
              className="mt-2 pt-0"
              style={{
                borderRadius: "20px",

                width: isMobile ? "100%" : "952px",
              }}
            >
              <span
                className="font-bold"
                style={{
                  fontSize: "18px",
                  color: "#585858",
                  marginTop: "5px",
                  textDecoration: "underline",
                }}
              >
                {jobClients.applicantsApplied === 0 ? "Shortlist Applicants" : "Applicants"}
              </span>

              {jobClients.applicants?.length === 0 && jobClients?.applicantsApplied === 0 ? (
                <NoJobsCard description="No jobs match the specified criteria" />
              ) : (
                (() => {
                  const availableApplicants: Applicants[] = [];
                  jobClients.applicants?.forEach((a) => {
                    const status = a.applicationStatus as 5 | 9 | 11;
                    if (
                      [
                        c.jobApplicationStatus.APPLIED,
                        c.jobApplicationStatus.SHORTLISTED_BY_SYSTEM,
                        c.jobApplicationStatus.SHORTLISTED_BY_CLIENT,
                        c.jobApplicationStatus.EXCLUDED_BY_CLIENT,
                      ].includes(status)
                    ) {
                      availableApplicants.push(a);
                    }
                  });

                  return availableApplicants.length === 0 ? (
                    <NoJobsCard description="Sit tight Helpers will apply to your job soon!" />
                  ) : (
                    availableApplicants.map((a, index) => (
                      <div className="mt-2" key={index}>
                        <AwardCard
                          // avgRating={a.applicantRatingsAvg}
                          // numRatings={a.applicantRatingsCount}
                          helperImgSrc={a.applicantImageSrc}
                          location={a.suburb}
                          transport={(() => {
                            switch (a.gettingHome) {
                              case 0:
                                return "Unspecified";
                              case 1:
                                return "Needs you to provide transport";
                              case 2:
                                return "Walking";
                              case 3:
                                return "Public Transport";
                              case 4:
                                return "Car (Being picked up)";
                              case 5:
                                return "Driving";
                              case 6:
                                return "Uber / Taxi";
                              default:
                                return "Not Available";
                            }
                          })()}
                          applicationStatus={a.applicationStatus}
                          publicName={`${a.applicantFirstName || "name"} ${a.applicantLastInitial || "Anonymous"}`}
                          isSuperHelper={a.completedJobsCount >= 15}
                          onViewProfileClicked={() => {
                            redirectAfterHome(a.applicantId);
                          }}
                          onAwardJobClicked={() => {
                            setSelectedJobId(jobClients.id);
                            setApplicantId(a.applicantId);
                            setIsDialogOpen(true);
                          }}
                          isOneoffJob={!jobClients.isTutoringJob || !jobClients.isRecurringJob}
                          // responseRate={a.responseRate}
                          onContactClicked={() => {
                            if (ischateligible === 0) {
                              setChatErrorMessage("You need an active membership to chat with Juggle St users.");
                              setDialogVisible(true);
                              return;
                            }
                            IframeBridge.sendToParent({
                              type: "navigateChatWithId",
                              data: {
                                id: String(a.applicantId),
                              },
                            });
                            if (!inIframe) {
                              if (clientType === "1") {
                                navigate(`/parent-home/inAppChat?userId=${a.applicantId}`);
                              } else {
                                navigate(`/business-home/inAppChat?userId=${a.applicantId}`);
                              }
                            }
                          }}
                          applicantId={a.applicantId}
                        />
                      </div>
                    ))
                  );
                })()
              )}

              <ConfirmAwardDialog
                title="Confirm Award"
                isOpen={isDialogOpen}
                onOpenChange={setIsDialogOpen}
                onConfirm={() => {
                  if (selectedJobtId) {
                    handleAwardJob(selectedJobtId);
                    setIsDialogOpen(false);
                  }
                }}
                onCancel={() => {
                  setIsDialogOpen(false);
                  setSelectedJobId(null);
                }}
                name={jobClients.applicants?.find((a) => a.applicantId === applicantId)?.applicantFirstName || ""}
                image={jobClients.applicants?.find((a) => a.applicantId === applicantId)?.applicantImageSrc}
              />
            </div>
          )}
          {jobClients.managedBy === 1 &&
            (jobClients.jobType === 1 || jobClients.jobType === 256) && (
              <div className="gap-3">
                <>
                  <div className="flex flex-column align-items-start">
                    <h4
                      className={`p-0 m-0 mt-2 underline font-bold text-wrap`} style={{ fontSize: "18px", color: "#585858" }}
                    >
                      Invited Candidates
                    </h4>
                  </div>
                </>
                <div className="flex-grow-1 grid grid-nogutter cursor-pointer">
                  {jobClients.applicants
                    .filter(
                      (a) =>
                        a.applicationStatus !== c.jobApplicationStatus.APPLIED &&
                        a.applicationStatus !== c.jobApplicationStatus.NOT_AVAILABLE
                    )
                    .map((v, i) => (
                      <div
                        className="h-min col-12 grid-nogutter pt-1 pb-1"
                        key={i}
                      >
                        <div
                          className="h-min flex gap-2 align-items-center px-2 py-2"
                          style={{
                            border: " 2px solid #179D52",
                            borderRadius: "10px",
                          }}
                        >
                          <div className="flex align-items-center">
                            <div className=" relative">
                              <img
                                src={v.applicantImageSrc}
                                alt="img"
                                width="81px"
                                height="81px"
                                style={{
                                  borderRadius: "50%",
                                  objectFit: "contain",
                                  overflow: "hidden",
                                }}
                                onClick={(e) => {
                                  e.preventDefault();
                                  viewProvider(v.applicantId);
                                }}
                              />
                              <div
                                className="w-4 flex justify-content-start pl-"
                                style={{ bottom: "6px" }}
                              >
                                <div
                                  className="text-yellow-300 font-bold text-center"
                                  style={{
                                    fontSize: "0.4em",
                                    backgroundColor: "white",
                                    padding: "3px 20px",
                                    paddingInline: "15px",
                                    borderRadius: "30px",
                                    userSelect: "none",
                                    textWrap: "nowrap",
                                    position: "absolute",
                                    bottom: "0px",
                                  }}
                                >
                                  View profile
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex-grow-1 flex flex-column gap-1">
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "700",
                                fontSize: "18px",
                                color: "#585858",
                              }}
                            >
                              {v.applicantFirstName}
                            </p>
                            <div className="flex gap-1 align-items-center">
                              <SlLocationPin color="#37A950" fontSize={"12px"} />
                              <p
                                className="m-0 p-0"
                                style={{
                                  fontWeight: "600",
                                  fontSize: "12px",
                                  color: "#585858",
                                }}
                              >
                                {v.suburb}
                              </p>
                            </div>
                            <div className="flex gap-1 align-items-center">
                              <img
                                src={starIcon}
                                alt="star"
                                width="16.71px"
                                height="18px"
                              />
                              <p
                                className="m-0 p-0"
                                style={{
                                  fontWeight: "300",
                                  fontSize: "12px",
                                  color: "#585858",
                                }}
                              >
                                {v.completedJobsCount} Jobs completed
                              </p>
                            </div>
                          </div>
                          <div
                            className="flex gap-2 mr-3"
                            style={{
                              backgroundColor: "#179D52",
                              padding: "10px",
                              borderRadius: "10px",
                            }}
                          >
                            <p
                              className="m-0 p-0"
                              style={{
                                fontWeight: "700",
                                fontSize: "12px",
                                color: "#FFFFFF",
                              }}
                            >
                              Selected
                            </p>
                            <FaCheck color="#FFFFFF" />
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          {clientType !== "0" && (
            <div style={{ margin: "0px", width: "100%" }}>
              <JobFooter
                expiryDate={jobClients.expiresInDays}
                managedBy={jobClients.managedBy}
                onCancelClicked={() => {
                  handleCancelJob(jobClients.id, "0");
                }}
                onEditClicked={() => {
                  if (!jobClients || !jobClients.id) {
                    console.error("Job ID is missing");
                    return;
                  }
                  IframeBridge.sendToParent({
                    type: "navigateToPostJob",
                    data: {
                      id: jobClients.id,
                      action: "Edit",
                    },
                  });

                  if (!inIframe) {
                    const newParam = new URLSearchParams();
                    const action = duplicateJob ? "Duplicate" : "Edit";
                    newParam.set("jobaction", action);
                    // newParam.set('jobId',action)
                    if (Number(clientType) === 1) {
                      navigate({
                        pathname: `/parent-home/job/${jobClients.id}/job-type`,
                        search: newParam.toString(),
                      });
                    } else {
                      navigate({
                        pathname: `/business-home/job/${jobClients.id}/job-type`,
                        search: newParam.toString(),
                      });
                    }
                  }
                }}
                onInviteMoreCandidatesClicked={() => setManageRecruitment(true)}
              />
            </div>
          )}

          <ViewJobFull
            visible={showfullJob}
            onClose={() => {
              setShowFullJob(false);
            }}
            job={jobClients}
          />
        </div>
      </>
    )
  ) : (
    <Loader />
  );
};

export default JobAnalyticsUnawarded;
