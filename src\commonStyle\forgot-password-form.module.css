.ForgetinputField {
    border-radius: 10px;
    background-color: rgba(240, 244, 247, 1);
    width:362px;
    height: 56px;
    
  }

.accountIdentifierErrorLabel {
    font-family: 'Poppins', sans-serif;
}
.instructionText {
    margin-top: -8px;
    color: rgba(88, 88, 88, 1);
    text-align: center;
    line-height: 21px;
    font: 500 14px/21px 'Poppins', '-apple-system', 'Roboto', Helvetica, sans-serif;
  }
  .imageText h2 {
    color: rgba(23, 157, 82, 1);
    text-align: center;
    font: 700 16px 'Poppins', sans-serif;
    margin-top: 7px;
  }
    
  .backToLogin {
    color: rgba(88, 88, 88, 1);
    text-align: center;
    margin: 28px 0 -59px;
    text-decoration: none;
    font: 500 14px 'Poppins', sans-serif;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
  .backToLogin:hover,
  .backToLogin:focus {
    text-decoration: underline;
    cursor: pointer;
    cursor: pointer;
  }
  .forError{
    color: #FF6359;
  }
  .formError {
    margin-top: 15px;       /* Adds space between the error and the "Back to log in" link */
    color: #FF6359;             /* Error text color */
    font-size: 16px;        /* Font size for the error */
    font-weight: bold;      /* Makes the error text bold */
    text-align: center;     /* Center the error message */
}

  
  @media (max-width: 991px) {
    .backToLogin {
      margin-bottom: 10px;
    }
  }
  /* Styles for screens smaller than 600px (e.g., mobile devices) */
@media (max-width: 600px) {
  .ForgetinputField,
  .instructionText,
  .imageTextContainer {
      width: 100%; /* Adjust width to fit the screen */
  }

  .ForgetinputField {
      height: auto; /* Adjust height if necessary */
  }

  .instructionText {
      margin-top: 0; /* Adjust margin for smaller screens */
  }

  .imageTextContainer {
      margin-top: 0; /* Adjust margin for smaller screens */
  }

  .backToLogin {
      margin: 14px 0 0; /* Adjust margin for smaller screens */
  }
}

/* Styles for screens between 601px and 900px (e.g., tablets) */
@media (min-width: 601px) and (max-width: 900px) {
  .ForgetinputField {
      width: 90%; /* Adjust width to fit the screen */
  }

  .instructionText {
      /* width: 90%; Adjust width to fit the screen */
  }

  .imageTextContainer {
      margin-top: -30px; /* Adjust margin for tablets */
  }

  .backToLogin {
      margin: 20px 0 -40px; /* Adjust margin for tablets */
  }
}

/* Styles for screens between 901px and 991px (e.g., large tablets and small desktops) */
@media (min-width: 901px) and (max-width: 991px) {
  .ForgetinputField,
  .instructionText,
  .imageTextContainer {
      /* width: 80%; Adjust width for larger tablets and small desktops */
  }

  .imageTextContainer {
      margin-top: -40px; /* Adjust margin for larger screens */
  }

  .backToLogin {
      margin: 24px 0 -50px; /* Adjust margin for larger screens */
  }
}
.imageText {
  color: rgba(23, 157, 82, 1);
  text-align: center;
  font: 700 16px 'Poppins', sans-serif;
  margin-top: -49px;
}

/* Styles for screens smaller than 600px (e.g., mobile devices) */
@media (max-width: 600px) {
.imageText {
  font-size: 14px; /* Smaller font size for mobile devices */
  margin-top: -20px; /* Adjust margin for better spacing on small screens */
}
}

/* Styles for screens between 601px and 900px (e.g., tablets) */
@media (min-width: 601px) and (max-width: 900px) {
.imageText {
  font-size: 15px; /* Adjust font size for tablets */
  margin-top: -45px; /* Adjust margin for better spacing on tablets */
}
}

/* Styles for screens between 901px and 991px (e.g., large tablets and small desktops) */
@media (min-width: 901px) and (max-width: 991px) {
.imageText {
  font-size: 16px; /* Restore font size for larger tablets and small desktops */
  margin-top: -40px; /* Adjust margin for larger screens */
}
}

/* Styles for screens larger than 992px (e.g., desktops) */
@media (min-width: 992px) {
.imageText {
  font-size: 16px; /* Default font size for larger screens */
  margin-top: -49px; /* Default margin for larger screens */
}
}

