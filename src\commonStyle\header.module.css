.headerContainer {
  background: linear-gradient(to left, #FFA500, #37A950);
  display: flex;
  flex-direction: column;
  border-radius: 0 0 9px 9px;
  width: 100%;
  height: 10vh;
  align-items: center; /* Default centering for tablets and mobile */
  justify-content: center;
  position: relative;
  z-index: 1;
}

.headerContent {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 23px 59px;
  border-radius: 0 0 10px 10px;
}

.headerImage {
  aspect-ratio: 4.81;
  object-fit: contain;
  object-position: center;
  width: 100%;
  max-width: 173px;
}

@media (max-width: 991px) {
  .headerContent {
    padding: 15px 30px;
  }
}

@media (max-width: 768px) {
  .headerContent {
    align-items: center; /* Center content for tablets and below */
    padding: 15px 20px;
  }
  .headerImage {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .headerContent {
    padding: 10px 15px;
  }
  .headerImage {
    max-width: 120px;
  }
}
